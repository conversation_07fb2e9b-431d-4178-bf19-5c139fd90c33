version: "3.8"

services:
  mlflow:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mlflow-server
    ports:
      - "5000:5000"
    volumes:
      # Mount the current directory to persist MLflow data
      - .:/app/mlruns
    environment:
      - MLFLOW_PORT=5000
      - MLFLOW_HOST=0.0.0.0
      - BACKEND_STORE_URI=file:./mlruns
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
