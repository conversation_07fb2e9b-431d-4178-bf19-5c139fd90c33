#!/bin/bash

# Set default values for environment variables if not provided
MLFLOW_PORT=${MLFLOW_PORT:-5000}
MLFLOW_HOST=${MLFLOW_HOST:-0.0.0.0}
BACKEND_STORE_URI=${BACKEND_STORE_URI:-file:./mlruns}

echo "Starting MLflow UI..."
echo "Host: $MLFLOW_HOST"
echo "Port: $MLFLOW_PORT"
echo "Backend Store URI: $BACKEND_STORE_URI"

# Start MLflow UI
exec mlflow ui \
    --host "$MLFLOW_HOST" \
    --port "$MLFLOW_PORT" \
    --backend-store-uri "$BACKEND_STORE_URI"
