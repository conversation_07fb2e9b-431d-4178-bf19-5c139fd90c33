
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: model_registry.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import databricks_pb2 as databricks__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14model_registry.proto\x12\x06mlflow\x1a\x15scalapb/scalapb.proto\x1a\x10\x64\x61tabricks.proto\"\xe9\x02\n\x0fRegisteredModel\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x02 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x03 \x01(\x03\x12\x0f\n\x07user_id\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12-\n\x0flatest_versions\x18\x06 \x03(\x0b\x32\x14.mlflow.ModelVersion\x12(\n\x04tags\x18\x07 \x03(\x0b\x32\x1a.mlflow.RegisteredModelTag\x12-\n\x07\x61liases\x18\x08 \x03(\x0b\x32\x1c.mlflow.RegisteredModelAlias\x12\x19\n\x11\x64\x65ployment_job_id\x18\t \x01(\t\x12\x43\n\x14\x64\x65ployment_job_state\x18\n \x01(\x0e\x32%.mlflow.DeploymentJobConnection.State\"\x82\x04\n\x0cModelVersion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x03 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x04 \x01(\x03\x12\x0f\n\x07user_id\x18\x05 \x01(\t\x12\x15\n\rcurrent_stage\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\x12\x0e\n\x06source\x18\x08 \x01(\t\x12\x0e\n\x06run_id\x18\t \x01(\t\x12*\n\x06status\x18\n \x01(\x0e\x32\x1a.mlflow.ModelVersionStatus\x12\x16\n\x0estatus_message\x18\x0b \x01(\t\x12%\n\x04tags\x18\x0c \x03(\x0b\x32\x17.mlflow.ModelVersionTag\x12\x10\n\x08run_link\x18\r \x01(\t\x12\x0f\n\x07\x61liases\x18\x0e \x03(\t\x12\x10\n\x08model_id\x18\x0f \x01(\t\x12(\n\x0cmodel_params\x18\x10 \x03(\x0b\x32\x12.mlflow.ModelParam\x12*\n\rmodel_metrics\x18\x11 \x03(\x0b\x32\x13.mlflow.ModelMetric\x12\x44\n\x14\x64\x65ployment_job_state\x18\x13 \x01(\x0b\x32&.mlflow.ModelVersionDeploymentJobState\"\xa3\x01\n\x17\x44\x65ploymentJobConnection\"\x87\x01\n\x05State\x12/\n+DEPLOYMENT_JOB_CONNECTION_STATE_UNSPECIFIED\x10\x00\x12\x0e\n\nNOT_SET_UP\x10\x01\x12\r\n\tCONNECTED\x10\x02\x12\r\n\tNOT_FOUND\x10\x03\x12\x1f\n\x1bREQUIRED_PARAMETERS_CHANGED\x10\x04\"\x90\x03\n\x1eModelVersionDeploymentJobState\x12\x0e\n\x06job_id\x18\x01 \x01(\t\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12\x38\n\tjob_state\x18\x03 \x01(\x0e\x32%.mlflow.DeploymentJobConnection.State\x12O\n\trun_state\x18\x04 \x01(\x0e\x32<.mlflow.ModelVersionDeploymentJobState.DeploymentJobRunState\x12\x19\n\x11\x63urrent_task_name\x18\x05 \x01(\t\"\xa7\x01\n\x15\x44\x65ploymentJobRunState\x12(\n$DEPLOYMENT_JOB_RUN_STATE_UNSPECIFIED\x10\x00\x12!\n\x1dNO_VALID_DEPLOYMENT_JOB_FOUND\x10\x01\x12\x0b\n\x07RUNNING\x10\x02\x12\r\n\tSUCCEEDED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\x0b\n\x07PENDING\x10\x05\x12\x0c\n\x08\x41PPROVAL\x10\x06\"\xf1\x01\n\x15\x43reateRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12(\n\x04tags\x18\x02 \x03(\x0b\x32\x1a.mlflow.RegisteredModelTag\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x04 \x01(\t\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa9\x01\n\x15RenameRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x10\n\x08new_name\x18\x02 \x01(\t\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xc7\x01\n\x15UpdateRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x03 \x01(\t\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"d\n\x15\x44\x65leteRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x94\x01\n\x12GetRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xee\x01\n\x16SearchRegisteredModels\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x18\n\x0bmax_results\x18\x02 \x01(\x03:\x03\x31\x30\x30\x12\x10\n\x08order_by\x18\x03 \x03(\t\x12\x12\n\npage_token\x18\x04 \x01(\t\x1aW\n\x08Response\x12\x32\n\x11registered_models\x18\x01 \x03(\x0b\x32\x17.mlflow.RegisteredModel\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x9e\x01\n\x11GetLatestVersions\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06stages\x18\x02 \x03(\t\x1a\x38\n\x08Response\x12,\n\x0emodel_versions\x18\x01 \x03(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x94\x02\n\x12\x43reateModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06source\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12%\n\x04tags\x18\x04 \x03(\x0b\x32\x17.mlflow.ModelVersionTag\x12\x10\n\x08run_link\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x10\n\x08model_id\x18\x07 \x01(\t\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xba\x01\n\x12UpdateModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xec\x01\n\x1bTransitionModelVersionStage\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05stage\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\'\n\x19\x61rchive_existing_versions\x18\x04 \x01(\x08\x42\x04\xf8\x86\x19\x01\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"x\n\x12\x44\x65leteModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa2\x01\n\x0fGetModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xe8\x01\n\x13SearchModelVersions\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x1b\n\x0bmax_results\x18\x02 \x01(\x03:\x06\x32\x30\x30\x30\x30\x30\x12\x10\n\x08order_by\x18\x03 \x03(\t\x12\x12\n\npage_token\x18\x04 \x01(\t\x1aQ\n\x08Response\x12,\n\x0emodel_versions\x18\x01 \x03(\x0b\x32\x14.mlflow.ModelVersion\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x96\x01\n\x1aGetModelVersionDownloadUri\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a \n\x08Response\x12\x14\n\x0c\x61rtifact_uri\x18\x01 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"-\n\x0fModelVersionTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\")\n\nModelParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xb5\x01\n\x0bModelMetric\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\x12\x1a\n\x0c\x64\x61taset_name\x18\x05 \x01(\tB\x04\xf0\x86\x19\x03\x12\x1c\n\x0e\x64\x61taset_digest\x18\x06 \x01(\tB\x04\xf0\x86\x19\x03\x12\x16\n\x08model_id\x18\x07 \x01(\tB\x04\xf0\x86\x19\x03\x12\x14\n\x06run_id\x18\x08 \x01(\tB\x04\xf0\x86\x19\x03\"0\n\x12RegisteredModelTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\x8c\x01\n\x15SetRegisteredModelTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa0\x01\n\x12SetModelVersionTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x04 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"z\n\x18\x44\x65leteRegisteredModelTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8e\x01\n\x15\x44\x65leteModelVersionTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"6\n\x14RegisteredModelAlias\x12\r\n\x05\x61lias\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"\x92\x01\n\x17SetRegisteredModelAlias\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"~\n\x1a\x44\x65leteRegisteredModelAlias\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa7\x01\n\x16GetModelVersionByAlias\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]*R\n\x12ModelVersionStatus\x12\x18\n\x14PENDING_REGISTRATION\x10\x01\x12\x17\n\x13\x46\x41ILED_REGISTRATION\x10\x02\x12\t\n\x05READY\x10\x03\x32\x80\x1e\n\x14ModelRegistryService\x12\xae\x01\n\x15\x63reateRegisteredModel\x12\x1d.mlflow.CreateRegisteredModel\x1a&.mlflow.CreateRegisteredModel.Response\"N\xf2\x86\x19J\n.\n\x04POST\x12 /mlflow/registered-models/create\x1a\x04\x08\x02\x10\x00\x10\x01*\x16\x43reate RegisteredModel\x12\xae\x01\n\x15renameRegisteredModel\x12\x1d.mlflow.RenameRegisteredModel\x1a&.mlflow.RenameRegisteredModel.Response\"N\xf2\x86\x19J\n.\n\x04POST\x12 /mlflow/registered-models/rename\x1a\x04\x08\x02\x10\x00\x10\x01*\x16Rename RegisteredModel\x12\xaf\x01\n\x15updateRegisteredModel\x12\x1d.mlflow.UpdateRegisteredModel\x1a&.mlflow.UpdateRegisteredModel.Response\"O\xf2\x86\x19K\n/\n\x05PATCH\x12 /mlflow/registered-models/update\x1a\x04\x08\x02\x10\x00\x10\x01*\x16Update RegisteredModel\x12\xb0\x01\n\x15\x64\x65leteRegisteredModel\x12\x1d.mlflow.DeleteRegisteredModel\x1a&.mlflow.DeleteRegisteredModel.Response\"P\xf2\x86\x19L\n0\n\x06\x44\x45LETE\x12 /mlflow/registered-models/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\x16\x44\x65lete RegisteredModel\x12\x9e\x01\n\x12getRegisteredModel\x12\x1a.mlflow.GetRegisteredModel\x1a#.mlflow.GetRegisteredModel.Response\"G\xf2\x86\x19\x43\n*\n\x03GET\x12\x1d/mlflow/registered-models/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x13Get RegisteredModel\x12\xb1\x01\n\x16searchRegisteredModels\x12\x1e.mlflow.SearchRegisteredModels\x1a\'.mlflow.SearchRegisteredModels.Response\"N\xf2\x86\x19J\n-\n\x03GET\x12 /mlflow/registered-models/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x17Search RegisteredModels\x12\xef\x01\n\x11getLatestVersions\x12\x19.mlflow.GetLatestVersions\x1a\".mlflow.GetLatestVersions.Response\"\x9a\x01\xf2\x86\x19\x95\x01\n;\n\x04POST\x12-/mlflow/registered-models/get-latest-versions\x1a\x04\x08\x02\x10\x00\n:\n\x03GET\x12-/mlflow/registered-models/get-latest-versions\x1a\x04\x08\x02\x10\x00\x10\x01*\x18Get Latest ModelVersions\x12\x9f\x01\n\x12\x63reateModelVersion\x12\x1a.mlflow.CreateModelVersion\x1a#.mlflow.CreateModelVersion.Response\"H\xf2\x86\x19\x44\n+\n\x04POST\x12\x1d/mlflow/model-versions/create\x1a\x04\x08\x02\x10\x00\x10\x01*\x13\x43reate ModelVersion\x12\xa0\x01\n\x12updateModelVersion\x12\x1a.mlflow.UpdateModelVersion\x1a#.mlflow.UpdateModelVersion.Response\"I\xf2\x86\x19\x45\n,\n\x05PATCH\x12\x1d/mlflow/model-versions/update\x1a\x04\x08\x02\x10\x00\x10\x01*\x13Update ModelVersion\x12\xce\x01\n\x1btransitionModelVersionStage\x12#.mlflow.TransitionModelVersionStage\x1a,.mlflow.TransitionModelVersionStage.Response\"\\\xf2\x86\x19X\n5\n\x04POST\x12\'/mlflow/model-versions/transition-stage\x1a\x04\x08\x02\x10\x00\x10\x01*\x1dTransition ModelVersion Stage\x12\xa1\x01\n\x12\x64\x65leteModelVersion\x12\x1a.mlflow.DeleteModelVersion\x1a#.mlflow.DeleteModelVersion.Response\"J\xf2\x86\x19\x46\n-\n\x06\x44\x45LETE\x12\x1d/mlflow/model-versions/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\x13\x44\x65lete ModelVersion\x12\x8f\x01\n\x0fgetModelVersion\x12\x17.mlflow.GetModelVersion\x1a .mlflow.GetModelVersion.Response\"A\xf2\x86\x19=\n\'\n\x03GET\x12\x1a/mlflow/model-versions/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x10Get ModelVersion\x12\xa6\x01\n\x13searchModelVersions\x12\x1b.mlflow.SearchModelVersions\x1a$.mlflow.SearchModelVersions.Response\"L\xf2\x86\x19\x44\n*\n\x03GET\x12\x1d/mlflow/model-versions/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x14Search ModelVersions\xba\x8c\x19\x00\x12\xd8\x01\n\x1agetModelVersionDownloadUri\x12\".mlflow.GetModelVersionDownloadUri\x1a+.mlflow.GetModelVersionDownloadUri.Response\"i\xf2\x86\x19\x65\n4\n\x03GET\x12\'/mlflow/model-versions/get-download-uri\x1a\x04\x08\x02\x10\x00\x10\x01*+Get Download URI For ModelVersion Artifacts\x12\xb1\x01\n\x15setRegisteredModelTag\x12\x1d.mlflow.SetRegisteredModelTag\x1a&.mlflow.SetRegisteredModelTag.Response\"Q\xf2\x86\x19M\n/\n\x04POST\x12!/mlflow/registered-models/set-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x18Set Registered Model Tag\x12\xa2\x01\n\x12setModelVersionTag\x12\x1a.mlflow.SetModelVersionTag\x1a#.mlflow.SetModelVersionTag.Response\"K\xf2\x86\x19G\n,\n\x04POST\x12\x1e/mlflow/model-versions/set-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x15Set Model Version Tag\x12\xc2\x01\n\x18\x64\x65leteRegisteredModelTag\x12 .mlflow.DeleteRegisteredModelTag\x1a).mlflow.DeleteRegisteredModelTag.Response\"Y\xf2\x86\x19U\n4\n\x06\x44\x45LETE\x12$/mlflow/registered-models/delete-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x1b\x44\x65lete Registered Model Tag\x12\xb3\x01\n\x15\x64\x65leteModelVersionTag\x12\x1d.mlflow.DeleteModelVersionTag\x1a&.mlflow.DeleteModelVersionTag.Response\"S\xf2\x86\x19O\n1\n\x06\x44\x45LETE\x12!/mlflow/model-versions/delete-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x18\x44\x65lete Model Version Tag\x12\xb7\x01\n\x17setRegisteredModelAlias\x12\x1f.mlflow.SetRegisteredModelAlias\x1a(.mlflow.SetRegisteredModelAlias.Response\"Q\xf2\x86\x19M\n-\n\x04POST\x12\x1f/mlflow/registered-models/alias\x1a\x04\x08\x02\x10\x00\x10\x01*\x1aSet Registered Model Alias\x12\xc5\x01\n\x1a\x64\x65leteRegisteredModelAlias\x12\".mlflow.DeleteRegisteredModelAlias\x1a+.mlflow.DeleteRegisteredModelAlias.Response\"V\xf2\x86\x19R\n/\n\x06\x44\x45LETE\x12\x1f/mlflow/registered-models/alias\x1a\x04\x08\x02\x10\x00\x10\x01*\x1d\x44\x65lete Registered Model Alias\x12\xb3\x01\n\x16getModelVersionByAlias\x12\x1e.mlflow.GetModelVersionByAlias\x1a\'.mlflow.GetModelVersionByAlias.Response\"P\xf2\x86\x19L\n,\n\x03GET\x12\x1f/mlflow/registered-models/alias\x1a\x04\x08\x02\x10\x00\x10\x01*\x1aGet Model Version by AliasB!\n\x14org.mlflow.api.proto\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'model_registry_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\n\024org.mlflow.api.proto\220\001\001\240\001\001\342?\002\020\001'
    _globals['_CREATEREGISTEREDMODEL'].fields_by_name['name']._loaded_options = None
    _globals['_CREATEREGISTEREDMODEL'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEREGISTEREDMODEL']._loaded_options = None
    _globals['_CREATEREGISTEREDMODEL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_RENAMEREGISTEREDMODEL'].fields_by_name['name']._loaded_options = None
    _globals['_RENAMEREGISTEREDMODEL'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_RENAMEREGISTEREDMODEL']._loaded_options = None
    _globals['_RENAMEREGISTEREDMODEL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_UPDATEREGISTEREDMODEL'].fields_by_name['name']._loaded_options = None
    _globals['_UPDATEREGISTEREDMODEL'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEREGISTEREDMODEL']._loaded_options = None
    _globals['_UPDATEREGISTEREDMODEL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEREGISTEREDMODEL'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEREGISTEREDMODEL'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODEL']._loaded_options = None
    _globals['_DELETEREGISTEREDMODEL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETREGISTEREDMODEL'].fields_by_name['name']._loaded_options = None
    _globals['_GETREGISTEREDMODEL'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETREGISTEREDMODEL']._loaded_options = None
    _globals['_GETREGISTEREDMODEL']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SEARCHREGISTEREDMODELS']._loaded_options = None
    _globals['_SEARCHREGISTEREDMODELS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETLATESTVERSIONS'].fields_by_name['name']._loaded_options = None
    _globals['_GETLATESTVERSIONS'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETLATESTVERSIONS']._loaded_options = None
    _globals['_GETLATESTVERSIONS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_CREATEMODELVERSION'].fields_by_name['name']._loaded_options = None
    _globals['_CREATEMODELVERSION'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEMODELVERSION'].fields_by_name['source']._loaded_options = None
    _globals['_CREATEMODELVERSION'].fields_by_name['source']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEMODELVERSION']._loaded_options = None
    _globals['_CREATEMODELVERSION']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_UPDATEMODELVERSION'].fields_by_name['name']._loaded_options = None
    _globals['_UPDATEMODELVERSION'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEMODELVERSION'].fields_by_name['version']._loaded_options = None
    _globals['_UPDATEMODELVERSION'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEMODELVERSION']._loaded_options = None
    _globals['_UPDATEMODELVERSION']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['name']._loaded_options = None
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['version']._loaded_options = None
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['stage']._loaded_options = None
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['stage']._serialized_options = b'\370\206\031\001'
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['archive_existing_versions']._loaded_options = None
    _globals['_TRANSITIONMODELVERSIONSTAGE'].fields_by_name['archive_existing_versions']._serialized_options = b'\370\206\031\001'
    _globals['_TRANSITIONMODELVERSIONSTAGE']._loaded_options = None
    _globals['_TRANSITIONMODELVERSIONSTAGE']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEMODELVERSION'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEMODELVERSION'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSION'].fields_by_name['version']._loaded_options = None
    _globals['_DELETEMODELVERSION'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSION']._loaded_options = None
    _globals['_DELETEMODELVERSION']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETMODELVERSION'].fields_by_name['name']._loaded_options = None
    _globals['_GETMODELVERSION'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSION'].fields_by_name['version']._loaded_options = None
    _globals['_GETMODELVERSION'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSION']._loaded_options = None
    _globals['_GETMODELVERSION']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SEARCHMODELVERSIONS']._loaded_options = None
    _globals['_SEARCHMODELVERSIONS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETMODELVERSIONDOWNLOADURI'].fields_by_name['name']._loaded_options = None
    _globals['_GETMODELVERSIONDOWNLOADURI'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONDOWNLOADURI'].fields_by_name['version']._loaded_options = None
    _globals['_GETMODELVERSIONDOWNLOADURI'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONDOWNLOADURI']._loaded_options = None
    _globals['_GETMODELVERSIONDOWNLOADURI']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_MODELMETRIC'].fields_by_name['dataset_name']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['dataset_name']._serialized_options = b'\360\206\031\003'
    _globals['_MODELMETRIC'].fields_by_name['dataset_digest']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['dataset_digest']._serialized_options = b'\360\206\031\003'
    _globals['_MODELMETRIC'].fields_by_name['model_id']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['model_id']._serialized_options = b'\360\206\031\003'
    _globals['_MODELMETRIC'].fields_by_name['run_id']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['run_id']._serialized_options = b'\360\206\031\003'
    _globals['_SETREGISTEREDMODELTAG'].fields_by_name['name']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAG'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELTAG'].fields_by_name['key']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELTAG'].fields_by_name['value']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAG'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELTAG']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SETMODELVERSIONTAG'].fields_by_name['name']._loaded_options = None
    _globals['_SETMODELVERSIONTAG'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAG'].fields_by_name['version']._loaded_options = None
    _globals['_SETMODELVERSIONTAG'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAG'].fields_by_name['key']._loaded_options = None
    _globals['_SETMODELVERSIONTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAG'].fields_by_name['value']._loaded_options = None
    _globals['_SETMODELVERSIONTAG'].fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAG']._loaded_options = None
    _globals['_SETMODELVERSIONTAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEREGISTEREDMODELTAG'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELTAG'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELTAG'].fields_by_name['key']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELTAG']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELTAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEMODELVERSIONTAG'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAG'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONTAG'].fields_by_name['version']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAG'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONTAG'].fields_by_name['key']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAG'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONTAG']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAG']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_SETREGISTEREDMODELALIAS'].fields_by_name['name']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIAS'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELALIAS'].fields_by_name['alias']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIAS'].fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELALIAS'].fields_by_name['version']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIAS'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELALIAS']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIAS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_DELETEREGISTEREDMODELALIAS'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELALIAS'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELALIAS'].fields_by_name['alias']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELALIAS'].fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELALIAS']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELALIAS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_GETMODELVERSIONBYALIAS'].fields_by_name['name']._loaded_options = None
    _globals['_GETMODELVERSIONBYALIAS'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONBYALIAS'].fields_by_name['alias']._loaded_options = None
    _globals['_GETMODELVERSIONBYALIAS'].fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONBYALIAS']._loaded_options = None
    _globals['_GETMODELVERSIONBYALIAS']._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['createRegisteredModel']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['createRegisteredModel']._serialized_options = b'\362\206\031J\n.\n\004POST\022 /mlflow/registered-models/create\032\004\010\002\020\000\020\001*\026Create RegisteredModel'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['renameRegisteredModel']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['renameRegisteredModel']._serialized_options = b'\362\206\031J\n.\n\004POST\022 /mlflow/registered-models/rename\032\004\010\002\020\000\020\001*\026Rename RegisteredModel'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['updateRegisteredModel']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['updateRegisteredModel']._serialized_options = b'\362\206\031K\n/\n\005PATCH\022 /mlflow/registered-models/update\032\004\010\002\020\000\020\001*\026Update RegisteredModel'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteRegisteredModel']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteRegisteredModel']._serialized_options = b'\362\206\031L\n0\n\006DELETE\022 /mlflow/registered-models/delete\032\004\010\002\020\000\020\001*\026Delete RegisteredModel'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getRegisteredModel']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getRegisteredModel']._serialized_options = b'\362\206\031C\n*\n\003GET\022\035/mlflow/registered-models/get\032\004\010\002\020\000\020\001*\023Get RegisteredModel'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['searchRegisteredModels']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['searchRegisteredModels']._serialized_options = b'\362\206\031J\n-\n\003GET\022 /mlflow/registered-models/search\032\004\010\002\020\000\020\001*\027Search RegisteredModels'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getLatestVersions']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getLatestVersions']._serialized_options = b'\362\206\031\225\001\n;\n\004POST\022-/mlflow/registered-models/get-latest-versions\032\004\010\002\020\000\n:\n\003GET\022-/mlflow/registered-models/get-latest-versions\032\004\010\002\020\000\020\001*\030Get Latest ModelVersions'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['createModelVersion']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['createModelVersion']._serialized_options = b'\362\206\031D\n+\n\004POST\022\035/mlflow/model-versions/create\032\004\010\002\020\000\020\001*\023Create ModelVersion'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['updateModelVersion']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['updateModelVersion']._serialized_options = b'\362\206\031E\n,\n\005PATCH\022\035/mlflow/model-versions/update\032\004\010\002\020\000\020\001*\023Update ModelVersion'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['transitionModelVersionStage']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['transitionModelVersionStage']._serialized_options = b'\362\206\031X\n5\n\004POST\022\'/mlflow/model-versions/transition-stage\032\004\010\002\020\000\020\001*\035Transition ModelVersion Stage'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteModelVersion']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteModelVersion']._serialized_options = b'\362\206\031F\n-\n\006DELETE\022\035/mlflow/model-versions/delete\032\004\010\002\020\000\020\001*\023Delete ModelVersion'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getModelVersion']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getModelVersion']._serialized_options = b'\362\206\031=\n\'\n\003GET\022\032/mlflow/model-versions/get\032\004\010\002\020\000\020\001*\020Get ModelVersion'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['searchModelVersions']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['searchModelVersions']._serialized_options = b'\362\206\031D\n*\n\003GET\022\035/mlflow/model-versions/search\032\004\010\002\020\000\020\001*\024Search ModelVersions\272\214\031\000'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getModelVersionDownloadUri']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getModelVersionDownloadUri']._serialized_options = b'\362\206\031e\n4\n\003GET\022\'/mlflow/model-versions/get-download-uri\032\004\010\002\020\000\020\001*+Get Download URI For ModelVersion Artifacts'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['setRegisteredModelTag']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['setRegisteredModelTag']._serialized_options = b'\362\206\031M\n/\n\004POST\022!/mlflow/registered-models/set-tag\032\004\010\002\020\000\020\001*\030Set Registered Model Tag'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['setModelVersionTag']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['setModelVersionTag']._serialized_options = b'\362\206\031G\n,\n\004POST\022\036/mlflow/model-versions/set-tag\032\004\010\002\020\000\020\001*\025Set Model Version Tag'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteRegisteredModelTag']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteRegisteredModelTag']._serialized_options = b'\362\206\031U\n4\n\006DELETE\022$/mlflow/registered-models/delete-tag\032\004\010\002\020\000\020\001*\033Delete Registered Model Tag'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteModelVersionTag']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteModelVersionTag']._serialized_options = b'\362\206\031O\n1\n\006DELETE\022!/mlflow/model-versions/delete-tag\032\004\010\002\020\000\020\001*\030Delete Model Version Tag'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['setRegisteredModelAlias']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['setRegisteredModelAlias']._serialized_options = b'\362\206\031M\n-\n\004POST\022\037/mlflow/registered-models/alias\032\004\010\002\020\000\020\001*\032Set Registered Model Alias'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteRegisteredModelAlias']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['deleteRegisteredModelAlias']._serialized_options = b'\362\206\031R\n/\n\006DELETE\022\037/mlflow/registered-models/alias\032\004\010\002\020\000\020\001*\035Delete Registered Model Alias'
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getModelVersionByAlias']._loaded_options = None
    _globals['_MODELREGISTRYSERVICE'].methods_by_name['getModelVersionByAlias']._serialized_options = b'\362\206\031L\n,\n\003GET\022\037/mlflow/registered-models/alias\032\004\010\002\020\000\020\001*\032Get Model Version by Alias'
    _globals['_MODELVERSIONSTATUS']._serialized_start=5580
    _globals['_MODELVERSIONSTATUS']._serialized_end=5662
    _globals['_REGISTEREDMODEL']._serialized_start=74
    _globals['_REGISTEREDMODEL']._serialized_end=435
    _globals['_MODELVERSION']._serialized_start=438
    _globals['_MODELVERSION']._serialized_end=952
    _globals['_DEPLOYMENTJOBCONNECTION']._serialized_start=955
    _globals['_DEPLOYMENTJOBCONNECTION']._serialized_end=1118
    _globals['_DEPLOYMENTJOBCONNECTION_STATE']._serialized_start=983
    _globals['_DEPLOYMENTJOBCONNECTION_STATE']._serialized_end=1118
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE']._serialized_start=1121
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE']._serialized_end=1521
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE']._serialized_start=1354
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE']._serialized_end=1521
    _globals['_CREATEREGISTEREDMODEL']._serialized_start=1524
    _globals['_CREATEREGISTEREDMODEL']._serialized_end=1765
    _globals['_CREATEREGISTEREDMODEL_RESPONSE']._serialized_start=1659
    _globals['_CREATEREGISTEREDMODEL_RESPONSE']._serialized_end=1720
    _globals['_RENAMEREGISTEREDMODEL']._serialized_start=1768
    _globals['_RENAMEREGISTEREDMODEL']._serialized_end=1937
    _globals['_RENAMEREGISTEREDMODEL_RESPONSE']._serialized_start=1659
    _globals['_RENAMEREGISTEREDMODEL_RESPONSE']._serialized_end=1720
    _globals['_UPDATEREGISTEREDMODEL']._serialized_start=1940
    _globals['_UPDATEREGISTEREDMODEL']._serialized_end=2139
    _globals['_UPDATEREGISTEREDMODEL_RESPONSE']._serialized_start=1659
    _globals['_UPDATEREGISTEREDMODEL_RESPONSE']._serialized_end=1720
    _globals['_DELETEREGISTEREDMODEL']._serialized_start=2141
    _globals['_DELETEREGISTEREDMODEL']._serialized_end=2241
    _globals['_DELETEREGISTEREDMODEL_RESPONSE']._serialized_start=1659
    _globals['_DELETEREGISTEREDMODEL_RESPONSE']._serialized_end=1669
    _globals['_GETREGISTEREDMODEL']._serialized_start=2244
    _globals['_GETREGISTEREDMODEL']._serialized_end=2392
    _globals['_GETREGISTEREDMODEL_RESPONSE']._serialized_start=1659
    _globals['_GETREGISTEREDMODEL_RESPONSE']._serialized_end=1720
    _globals['_SEARCHREGISTEREDMODELS']._serialized_start=2395
    _globals['_SEARCHREGISTEREDMODELS']._serialized_end=2633
    _globals['_SEARCHREGISTEREDMODELS_RESPONSE']._serialized_start=2501
    _globals['_SEARCHREGISTEREDMODELS_RESPONSE']._serialized_end=2588
    _globals['_GETLATESTVERSIONS']._serialized_start=2636
    _globals['_GETLATESTVERSIONS']._serialized_end=2794
    _globals['_GETLATESTVERSIONS_RESPONSE']._serialized_start=2693
    _globals['_GETLATESTVERSIONS_RESPONSE']._serialized_end=2749
    _globals['_CREATEMODELVERSION']._serialized_start=2797
    _globals['_CREATEMODELVERSION']._serialized_end=3073
    _globals['_CREATEMODELVERSION_RESPONSE']._serialized_start=2973
    _globals['_CREATEMODELVERSION_RESPONSE']._serialized_end=3028
    _globals['_UPDATEMODELVERSION']._serialized_start=3076
    _globals['_UPDATEMODELVERSION']._serialized_end=3262
    _globals['_UPDATEMODELVERSION_RESPONSE']._serialized_start=2973
    _globals['_UPDATEMODELVERSION_RESPONSE']._serialized_end=3028
    _globals['_TRANSITIONMODELVERSIONSTAGE']._serialized_start=3265
    _globals['_TRANSITIONMODELVERSIONSTAGE']._serialized_end=3501
    _globals['_TRANSITIONMODELVERSIONSTAGE_RESPONSE']._serialized_start=2973
    _globals['_TRANSITIONMODELVERSIONSTAGE_RESPONSE']._serialized_end=3028
    _globals['_DELETEMODELVERSION']._serialized_start=3503
    _globals['_DELETEMODELVERSION']._serialized_end=3623
    _globals['_DELETEMODELVERSION_RESPONSE']._serialized_start=1659
    _globals['_DELETEMODELVERSION_RESPONSE']._serialized_end=1669
    _globals['_GETMODELVERSION']._serialized_start=3626
    _globals['_GETMODELVERSION']._serialized_end=3788
    _globals['_GETMODELVERSION_RESPONSE']._serialized_start=2973
    _globals['_GETMODELVERSION_RESPONSE']._serialized_end=3028
    _globals['_SEARCHMODELVERSIONS']._serialized_start=3791
    _globals['_SEARCHMODELVERSIONS']._serialized_end=4023
    _globals['_SEARCHMODELVERSIONS_RESPONSE']._serialized_start=3897
    _globals['_SEARCHMODELVERSIONS_RESPONSE']._serialized_end=3978
    _globals['_GETMODELVERSIONDOWNLOADURI']._serialized_start=4026
    _globals['_GETMODELVERSIONDOWNLOADURI']._serialized_end=4176
    _globals['_GETMODELVERSIONDOWNLOADURI_RESPONSE']._serialized_start=4099
    _globals['_GETMODELVERSIONDOWNLOADURI_RESPONSE']._serialized_end=4131
    _globals['_MODELVERSIONTAG']._serialized_start=4178
    _globals['_MODELVERSIONTAG']._serialized_end=4223
    _globals['_MODELPARAM']._serialized_start=4225
    _globals['_MODELPARAM']._serialized_end=4266
    _globals['_MODELMETRIC']._serialized_start=4269
    _globals['_MODELMETRIC']._serialized_end=4450
    _globals['_REGISTEREDMODELTAG']._serialized_start=4452
    _globals['_REGISTEREDMODELTAG']._serialized_end=4500
    _globals['_SETREGISTEREDMODELTAG']._serialized_start=4503
    _globals['_SETREGISTEREDMODELTAG']._serialized_end=4643
    _globals['_SETREGISTEREDMODELTAG_RESPONSE']._serialized_start=1659
    _globals['_SETREGISTEREDMODELTAG_RESPONSE']._serialized_end=1669
    _globals['_SETMODELVERSIONTAG']._serialized_start=4646
    _globals['_SETMODELVERSIONTAG']._serialized_end=4806
    _globals['_SETMODELVERSIONTAG_RESPONSE']._serialized_start=1659
    _globals['_SETMODELVERSIONTAG_RESPONSE']._serialized_end=1669
    _globals['_DELETEREGISTEREDMODELTAG']._serialized_start=4808
    _globals['_DELETEREGISTEREDMODELTAG']._serialized_end=4930
    _globals['_DELETEREGISTEREDMODELTAG_RESPONSE']._serialized_start=1659
    _globals['_DELETEREGISTEREDMODELTAG_RESPONSE']._serialized_end=1669
    _globals['_DELETEMODELVERSIONTAG']._serialized_start=4933
    _globals['_DELETEMODELVERSIONTAG']._serialized_end=5075
    _globals['_DELETEMODELVERSIONTAG_RESPONSE']._serialized_start=1659
    _globals['_DELETEMODELVERSIONTAG_RESPONSE']._serialized_end=1669
    _globals['_REGISTEREDMODELALIAS']._serialized_start=5077
    _globals['_REGISTEREDMODELALIAS']._serialized_end=5131
    _globals['_SETREGISTEREDMODELALIAS']._serialized_start=5134
    _globals['_SETREGISTEREDMODELALIAS']._serialized_end=5280
    _globals['_SETREGISTEREDMODELALIAS_RESPONSE']._serialized_start=1659
    _globals['_SETREGISTEREDMODELALIAS_RESPONSE']._serialized_end=1669
    _globals['_DELETEREGISTEREDMODELALIAS']._serialized_start=5282
    _globals['_DELETEREGISTEREDMODELALIAS']._serialized_end=5408
    _globals['_DELETEREGISTEREDMODELALIAS_RESPONSE']._serialized_start=1659
    _globals['_DELETEREGISTEREDMODELALIAS_RESPONSE']._serialized_end=1669
    _globals['_GETMODELVERSIONBYALIAS']._serialized_start=5411
    _globals['_GETMODELVERSIONBYALIAS']._serialized_end=5578
    _globals['_GETMODELVERSIONBYALIAS_RESPONSE']._serialized_start=2973
    _globals['_GETMODELVERSIONBYALIAS_RESPONSE']._serialized_end=3028
    _globals['_MODELREGISTRYSERVICE']._serialized_start=5665
    _globals['_MODELREGISTRYSERVICE']._serialized_end=9505
  _builder.BuildServices(DESCRIPTOR, 'model_registry_pb2', _globals)
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: model_registry.proto
  """Generated protocol buffer code."""
  from google.protobuf.internal import enum_type_wrapper
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf import service as _service
  from google.protobuf import service_reflection
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import databricks_pb2 as databricks__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14model_registry.proto\x12\x06mlflow\x1a\x15scalapb/scalapb.proto\x1a\x10\x64\x61tabricks.proto\"\xe9\x02\n\x0fRegisteredModel\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x02 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x03 \x01(\x03\x12\x0f\n\x07user_id\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12-\n\x0flatest_versions\x18\x06 \x03(\x0b\x32\x14.mlflow.ModelVersion\x12(\n\x04tags\x18\x07 \x03(\x0b\x32\x1a.mlflow.RegisteredModelTag\x12-\n\x07\x61liases\x18\x08 \x03(\x0b\x32\x1c.mlflow.RegisteredModelAlias\x12\x19\n\x11\x64\x65ployment_job_id\x18\t \x01(\t\x12\x43\n\x14\x64\x65ployment_job_state\x18\n \x01(\x0e\x32%.mlflow.DeploymentJobConnection.State\"\x82\x04\n\x0cModelVersion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x03 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x04 \x01(\x03\x12\x0f\n\x07user_id\x18\x05 \x01(\t\x12\x15\n\rcurrent_stage\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\x12\x0e\n\x06source\x18\x08 \x01(\t\x12\x0e\n\x06run_id\x18\t \x01(\t\x12*\n\x06status\x18\n \x01(\x0e\x32\x1a.mlflow.ModelVersionStatus\x12\x16\n\x0estatus_message\x18\x0b \x01(\t\x12%\n\x04tags\x18\x0c \x03(\x0b\x32\x17.mlflow.ModelVersionTag\x12\x10\n\x08run_link\x18\r \x01(\t\x12\x0f\n\x07\x61liases\x18\x0e \x03(\t\x12\x10\n\x08model_id\x18\x0f \x01(\t\x12(\n\x0cmodel_params\x18\x10 \x03(\x0b\x32\x12.mlflow.ModelParam\x12*\n\rmodel_metrics\x18\x11 \x03(\x0b\x32\x13.mlflow.ModelMetric\x12\x44\n\x14\x64\x65ployment_job_state\x18\x13 \x01(\x0b\x32&.mlflow.ModelVersionDeploymentJobState\"\xa3\x01\n\x17\x44\x65ploymentJobConnection\"\x87\x01\n\x05State\x12/\n+DEPLOYMENT_JOB_CONNECTION_STATE_UNSPECIFIED\x10\x00\x12\x0e\n\nNOT_SET_UP\x10\x01\x12\r\n\tCONNECTED\x10\x02\x12\r\n\tNOT_FOUND\x10\x03\x12\x1f\n\x1bREQUIRED_PARAMETERS_CHANGED\x10\x04\"\x90\x03\n\x1eModelVersionDeploymentJobState\x12\x0e\n\x06job_id\x18\x01 \x01(\t\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12\x38\n\tjob_state\x18\x03 \x01(\x0e\x32%.mlflow.DeploymentJobConnection.State\x12O\n\trun_state\x18\x04 \x01(\x0e\x32<.mlflow.ModelVersionDeploymentJobState.DeploymentJobRunState\x12\x19\n\x11\x63urrent_task_name\x18\x05 \x01(\t\"\xa7\x01\n\x15\x44\x65ploymentJobRunState\x12(\n$DEPLOYMENT_JOB_RUN_STATE_UNSPECIFIED\x10\x00\x12!\n\x1dNO_VALID_DEPLOYMENT_JOB_FOUND\x10\x01\x12\x0b\n\x07RUNNING\x10\x02\x12\r\n\tSUCCEEDED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\x0b\n\x07PENDING\x10\x05\x12\x0c\n\x08\x41PPROVAL\x10\x06\"\xf1\x01\n\x15\x43reateRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12(\n\x04tags\x18\x02 \x03(\x0b\x32\x1a.mlflow.RegisteredModelTag\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x04 \x01(\t\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa9\x01\n\x15RenameRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x10\n\x08new_name\x18\x02 \x01(\t\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xc7\x01\n\x15UpdateRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x03 \x01(\t\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"d\n\x15\x44\x65leteRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x94\x01\n\x12GetRegisteredModel\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x1a=\n\x08Response\x12\x31\n\x10registered_model\x18\x01 \x01(\x0b\x32\x17.mlflow.RegisteredModel:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xee\x01\n\x16SearchRegisteredModels\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x18\n\x0bmax_results\x18\x02 \x01(\x03:\x03\x31\x30\x30\x12\x10\n\x08order_by\x18\x03 \x03(\t\x12\x12\n\npage_token\x18\x04 \x01(\t\x1aW\n\x08Response\x12\x32\n\x11registered_models\x18\x01 \x03(\x0b\x32\x17.mlflow.RegisteredModel\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x9e\x01\n\x11GetLatestVersions\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06stages\x18\x02 \x03(\t\x1a\x38\n\x08Response\x12,\n\x0emodel_versions\x18\x01 \x03(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x94\x02\n\x12\x43reateModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06source\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12%\n\x04tags\x18\x04 \x03(\x0b\x32\x17.mlflow.ModelVersionTag\x12\x10\n\x08run_link\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x10\n\x08model_id\x18\x07 \x01(\t\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xba\x01\n\x12UpdateModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xec\x01\n\x1bTransitionModelVersionStage\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05stage\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\'\n\x19\x61rchive_existing_versions\x18\x04 \x01(\x08\x42\x04\xf8\x86\x19\x01\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"x\n\x12\x44\x65leteModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa2\x01\n\x0fGetModelVersion\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xe8\x01\n\x13SearchModelVersions\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x1b\n\x0bmax_results\x18\x02 \x01(\x03:\x06\x32\x30\x30\x30\x30\x30\x12\x10\n\x08order_by\x18\x03 \x03(\t\x12\x12\n\npage_token\x18\x04 \x01(\t\x1aQ\n\x08Response\x12,\n\x0emodel_versions\x18\x01 \x03(\x0b\x32\x14.mlflow.ModelVersion\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x96\x01\n\x1aGetModelVersionDownloadUri\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a \n\x08Response\x12\x14\n\x0c\x61rtifact_uri\x18\x01 \x01(\t:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"-\n\x0fModelVersionTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\")\n\nModelParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xb5\x01\n\x0bModelMetric\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\x12\x1a\n\x0c\x64\x61taset_name\x18\x05 \x01(\tB\x04\xf0\x86\x19\x03\x12\x1c\n\x0e\x64\x61taset_digest\x18\x06 \x01(\tB\x04\xf0\x86\x19\x03\x12\x16\n\x08model_id\x18\x07 \x01(\tB\x04\xf0\x86\x19\x03\x12\x14\n\x06run_id\x18\x08 \x01(\tB\x04\xf0\x86\x19\x03\"0\n\x12RegisteredModelTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\x8c\x01\n\x15SetRegisteredModelTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa0\x01\n\x12SetModelVersionTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05value\x18\x04 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"z\n\x18\x44\x65leteRegisteredModelTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\x8e\x01\n\x15\x44\x65leteModelVersionTag\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"6\n\x14RegisteredModelAlias\x12\r\n\x05\x61lias\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"\x92\x01\n\x17SetRegisteredModelAlias\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"~\n\x1a\x44\x65leteRegisteredModelAlias\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\n\n\x08Response:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]\"\xa7\x01\n\x16GetModelVersionByAlias\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x1a\x37\n\x08Response\x12+\n\rmodel_version\x18\x01 \x01(\x0b\x32\x14.mlflow.ModelVersion:+\xe2?(\n&com.databricks.rpc.RPC[$this.Response]*R\n\x12ModelVersionStatus\x12\x18\n\x14PENDING_REGISTRATION\x10\x01\x12\x17\n\x13\x46\x41ILED_REGISTRATION\x10\x02\x12\t\n\x05READY\x10\x03\x32\x80\x1e\n\x14ModelRegistryService\x12\xae\x01\n\x15\x63reateRegisteredModel\x12\x1d.mlflow.CreateRegisteredModel\x1a&.mlflow.CreateRegisteredModel.Response\"N\xf2\x86\x19J\n.\n\x04POST\x12 /mlflow/registered-models/create\x1a\x04\x08\x02\x10\x00\x10\x01*\x16\x43reate RegisteredModel\x12\xae\x01\n\x15renameRegisteredModel\x12\x1d.mlflow.RenameRegisteredModel\x1a&.mlflow.RenameRegisteredModel.Response\"N\xf2\x86\x19J\n.\n\x04POST\x12 /mlflow/registered-models/rename\x1a\x04\x08\x02\x10\x00\x10\x01*\x16Rename RegisteredModel\x12\xaf\x01\n\x15updateRegisteredModel\x12\x1d.mlflow.UpdateRegisteredModel\x1a&.mlflow.UpdateRegisteredModel.Response\"O\xf2\x86\x19K\n/\n\x05PATCH\x12 /mlflow/registered-models/update\x1a\x04\x08\x02\x10\x00\x10\x01*\x16Update RegisteredModel\x12\xb0\x01\n\x15\x64\x65leteRegisteredModel\x12\x1d.mlflow.DeleteRegisteredModel\x1a&.mlflow.DeleteRegisteredModel.Response\"P\xf2\x86\x19L\n0\n\x06\x44\x45LETE\x12 /mlflow/registered-models/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\x16\x44\x65lete RegisteredModel\x12\x9e\x01\n\x12getRegisteredModel\x12\x1a.mlflow.GetRegisteredModel\x1a#.mlflow.GetRegisteredModel.Response\"G\xf2\x86\x19\x43\n*\n\x03GET\x12\x1d/mlflow/registered-models/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x13Get RegisteredModel\x12\xb1\x01\n\x16searchRegisteredModels\x12\x1e.mlflow.SearchRegisteredModels\x1a\'.mlflow.SearchRegisteredModels.Response\"N\xf2\x86\x19J\n-\n\x03GET\x12 /mlflow/registered-models/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x17Search RegisteredModels\x12\xef\x01\n\x11getLatestVersions\x12\x19.mlflow.GetLatestVersions\x1a\".mlflow.GetLatestVersions.Response\"\x9a\x01\xf2\x86\x19\x95\x01\n;\n\x04POST\x12-/mlflow/registered-models/get-latest-versions\x1a\x04\x08\x02\x10\x00\n:\n\x03GET\x12-/mlflow/registered-models/get-latest-versions\x1a\x04\x08\x02\x10\x00\x10\x01*\x18Get Latest ModelVersions\x12\x9f\x01\n\x12\x63reateModelVersion\x12\x1a.mlflow.CreateModelVersion\x1a#.mlflow.CreateModelVersion.Response\"H\xf2\x86\x19\x44\n+\n\x04POST\x12\x1d/mlflow/model-versions/create\x1a\x04\x08\x02\x10\x00\x10\x01*\x13\x43reate ModelVersion\x12\xa0\x01\n\x12updateModelVersion\x12\x1a.mlflow.UpdateModelVersion\x1a#.mlflow.UpdateModelVersion.Response\"I\xf2\x86\x19\x45\n,\n\x05PATCH\x12\x1d/mlflow/model-versions/update\x1a\x04\x08\x02\x10\x00\x10\x01*\x13Update ModelVersion\x12\xce\x01\n\x1btransitionModelVersionStage\x12#.mlflow.TransitionModelVersionStage\x1a,.mlflow.TransitionModelVersionStage.Response\"\\\xf2\x86\x19X\n5\n\x04POST\x12\'/mlflow/model-versions/transition-stage\x1a\x04\x08\x02\x10\x00\x10\x01*\x1dTransition ModelVersion Stage\x12\xa1\x01\n\x12\x64\x65leteModelVersion\x12\x1a.mlflow.DeleteModelVersion\x1a#.mlflow.DeleteModelVersion.Response\"J\xf2\x86\x19\x46\n-\n\x06\x44\x45LETE\x12\x1d/mlflow/model-versions/delete\x1a\x04\x08\x02\x10\x00\x10\x01*\x13\x44\x65lete ModelVersion\x12\x8f\x01\n\x0fgetModelVersion\x12\x17.mlflow.GetModelVersion\x1a .mlflow.GetModelVersion.Response\"A\xf2\x86\x19=\n\'\n\x03GET\x12\x1a/mlflow/model-versions/get\x1a\x04\x08\x02\x10\x00\x10\x01*\x10Get ModelVersion\x12\xa6\x01\n\x13searchModelVersions\x12\x1b.mlflow.SearchModelVersions\x1a$.mlflow.SearchModelVersions.Response\"L\xf2\x86\x19\x44\n*\n\x03GET\x12\x1d/mlflow/model-versions/search\x1a\x04\x08\x02\x10\x00\x10\x01*\x14Search ModelVersions\xba\x8c\x19\x00\x12\xd8\x01\n\x1agetModelVersionDownloadUri\x12\".mlflow.GetModelVersionDownloadUri\x1a+.mlflow.GetModelVersionDownloadUri.Response\"i\xf2\x86\x19\x65\n4\n\x03GET\x12\'/mlflow/model-versions/get-download-uri\x1a\x04\x08\x02\x10\x00\x10\x01*+Get Download URI For ModelVersion Artifacts\x12\xb1\x01\n\x15setRegisteredModelTag\x12\x1d.mlflow.SetRegisteredModelTag\x1a&.mlflow.SetRegisteredModelTag.Response\"Q\xf2\x86\x19M\n/\n\x04POST\x12!/mlflow/registered-models/set-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x18Set Registered Model Tag\x12\xa2\x01\n\x12setModelVersionTag\x12\x1a.mlflow.SetModelVersionTag\x1a#.mlflow.SetModelVersionTag.Response\"K\xf2\x86\x19G\n,\n\x04POST\x12\x1e/mlflow/model-versions/set-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x15Set Model Version Tag\x12\xc2\x01\n\x18\x64\x65leteRegisteredModelTag\x12 .mlflow.DeleteRegisteredModelTag\x1a).mlflow.DeleteRegisteredModelTag.Response\"Y\xf2\x86\x19U\n4\n\x06\x44\x45LETE\x12$/mlflow/registered-models/delete-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x1b\x44\x65lete Registered Model Tag\x12\xb3\x01\n\x15\x64\x65leteModelVersionTag\x12\x1d.mlflow.DeleteModelVersionTag\x1a&.mlflow.DeleteModelVersionTag.Response\"S\xf2\x86\x19O\n1\n\x06\x44\x45LETE\x12!/mlflow/model-versions/delete-tag\x1a\x04\x08\x02\x10\x00\x10\x01*\x18\x44\x65lete Model Version Tag\x12\xb7\x01\n\x17setRegisteredModelAlias\x12\x1f.mlflow.SetRegisteredModelAlias\x1a(.mlflow.SetRegisteredModelAlias.Response\"Q\xf2\x86\x19M\n-\n\x04POST\x12\x1f/mlflow/registered-models/alias\x1a\x04\x08\x02\x10\x00\x10\x01*\x1aSet Registered Model Alias\x12\xc5\x01\n\x1a\x64\x65leteRegisteredModelAlias\x12\".mlflow.DeleteRegisteredModelAlias\x1a+.mlflow.DeleteRegisteredModelAlias.Response\"V\xf2\x86\x19R\n/\n\x06\x44\x45LETE\x12\x1f/mlflow/registered-models/alias\x1a\x04\x08\x02\x10\x00\x10\x01*\x1d\x44\x65lete Registered Model Alias\x12\xb3\x01\n\x16getModelVersionByAlias\x12\x1e.mlflow.GetModelVersionByAlias\x1a\'.mlflow.GetModelVersionByAlias.Response\"P\xf2\x86\x19L\n,\n\x03GET\x12\x1f/mlflow/registered-models/alias\x1a\x04\x08\x02\x10\x00\x10\x01*\x1aGet Model Version by AliasB!\n\x14org.mlflow.api.proto\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')

  _MODELVERSIONSTATUS = DESCRIPTOR.enum_types_by_name['ModelVersionStatus']
  ModelVersionStatus = enum_type_wrapper.EnumTypeWrapper(_MODELVERSIONSTATUS)
  PENDING_REGISTRATION = 1
  FAILED_REGISTRATION = 2
  READY = 3


  _REGISTEREDMODEL = DESCRIPTOR.message_types_by_name['RegisteredModel']
  _MODELVERSION = DESCRIPTOR.message_types_by_name['ModelVersion']
  _DEPLOYMENTJOBCONNECTION = DESCRIPTOR.message_types_by_name['DeploymentJobConnection']
  _MODELVERSIONDEPLOYMENTJOBSTATE = DESCRIPTOR.message_types_by_name['ModelVersionDeploymentJobState']
  _CREATEREGISTEREDMODEL = DESCRIPTOR.message_types_by_name['CreateRegisteredModel']
  _CREATEREGISTEREDMODEL_RESPONSE = _CREATEREGISTEREDMODEL.nested_types_by_name['Response']
  _RENAMEREGISTEREDMODEL = DESCRIPTOR.message_types_by_name['RenameRegisteredModel']
  _RENAMEREGISTEREDMODEL_RESPONSE = _RENAMEREGISTEREDMODEL.nested_types_by_name['Response']
  _UPDATEREGISTEREDMODEL = DESCRIPTOR.message_types_by_name['UpdateRegisteredModel']
  _UPDATEREGISTEREDMODEL_RESPONSE = _UPDATEREGISTEREDMODEL.nested_types_by_name['Response']
  _DELETEREGISTEREDMODEL = DESCRIPTOR.message_types_by_name['DeleteRegisteredModel']
  _DELETEREGISTEREDMODEL_RESPONSE = _DELETEREGISTEREDMODEL.nested_types_by_name['Response']
  _GETREGISTEREDMODEL = DESCRIPTOR.message_types_by_name['GetRegisteredModel']
  _GETREGISTEREDMODEL_RESPONSE = _GETREGISTEREDMODEL.nested_types_by_name['Response']
  _SEARCHREGISTEREDMODELS = DESCRIPTOR.message_types_by_name['SearchRegisteredModels']
  _SEARCHREGISTEREDMODELS_RESPONSE = _SEARCHREGISTEREDMODELS.nested_types_by_name['Response']
  _GETLATESTVERSIONS = DESCRIPTOR.message_types_by_name['GetLatestVersions']
  _GETLATESTVERSIONS_RESPONSE = _GETLATESTVERSIONS.nested_types_by_name['Response']
  _CREATEMODELVERSION = DESCRIPTOR.message_types_by_name['CreateModelVersion']
  _CREATEMODELVERSION_RESPONSE = _CREATEMODELVERSION.nested_types_by_name['Response']
  _UPDATEMODELVERSION = DESCRIPTOR.message_types_by_name['UpdateModelVersion']
  _UPDATEMODELVERSION_RESPONSE = _UPDATEMODELVERSION.nested_types_by_name['Response']
  _TRANSITIONMODELVERSIONSTAGE = DESCRIPTOR.message_types_by_name['TransitionModelVersionStage']
  _TRANSITIONMODELVERSIONSTAGE_RESPONSE = _TRANSITIONMODELVERSIONSTAGE.nested_types_by_name['Response']
  _DELETEMODELVERSION = DESCRIPTOR.message_types_by_name['DeleteModelVersion']
  _DELETEMODELVERSION_RESPONSE = _DELETEMODELVERSION.nested_types_by_name['Response']
  _GETMODELVERSION = DESCRIPTOR.message_types_by_name['GetModelVersion']
  _GETMODELVERSION_RESPONSE = _GETMODELVERSION.nested_types_by_name['Response']
  _SEARCHMODELVERSIONS = DESCRIPTOR.message_types_by_name['SearchModelVersions']
  _SEARCHMODELVERSIONS_RESPONSE = _SEARCHMODELVERSIONS.nested_types_by_name['Response']
  _GETMODELVERSIONDOWNLOADURI = DESCRIPTOR.message_types_by_name['GetModelVersionDownloadUri']
  _GETMODELVERSIONDOWNLOADURI_RESPONSE = _GETMODELVERSIONDOWNLOADURI.nested_types_by_name['Response']
  _MODELVERSIONTAG = DESCRIPTOR.message_types_by_name['ModelVersionTag']
  _MODELPARAM = DESCRIPTOR.message_types_by_name['ModelParam']
  _MODELMETRIC = DESCRIPTOR.message_types_by_name['ModelMetric']
  _REGISTEREDMODELTAG = DESCRIPTOR.message_types_by_name['RegisteredModelTag']
  _SETREGISTEREDMODELTAG = DESCRIPTOR.message_types_by_name['SetRegisteredModelTag']
  _SETREGISTEREDMODELTAG_RESPONSE = _SETREGISTEREDMODELTAG.nested_types_by_name['Response']
  _SETMODELVERSIONTAG = DESCRIPTOR.message_types_by_name['SetModelVersionTag']
  _SETMODELVERSIONTAG_RESPONSE = _SETMODELVERSIONTAG.nested_types_by_name['Response']
  _DELETEREGISTEREDMODELTAG = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelTag']
  _DELETEREGISTEREDMODELTAG_RESPONSE = _DELETEREGISTEREDMODELTAG.nested_types_by_name['Response']
  _DELETEMODELVERSIONTAG = DESCRIPTOR.message_types_by_name['DeleteModelVersionTag']
  _DELETEMODELVERSIONTAG_RESPONSE = _DELETEMODELVERSIONTAG.nested_types_by_name['Response']
  _REGISTEREDMODELALIAS = DESCRIPTOR.message_types_by_name['RegisteredModelAlias']
  _SETREGISTEREDMODELALIAS = DESCRIPTOR.message_types_by_name['SetRegisteredModelAlias']
  _SETREGISTEREDMODELALIAS_RESPONSE = _SETREGISTEREDMODELALIAS.nested_types_by_name['Response']
  _DELETEREGISTEREDMODELALIAS = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelAlias']
  _DELETEREGISTEREDMODELALIAS_RESPONSE = _DELETEREGISTEREDMODELALIAS.nested_types_by_name['Response']
  _GETMODELVERSIONBYALIAS = DESCRIPTOR.message_types_by_name['GetModelVersionByAlias']
  _GETMODELVERSIONBYALIAS_RESPONSE = _GETMODELVERSIONBYALIAS.nested_types_by_name['Response']
  _DEPLOYMENTJOBCONNECTION_STATE = _DEPLOYMENTJOBCONNECTION.enum_types_by_name['State']
  _MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE = _MODELVERSIONDEPLOYMENTJOBSTATE.enum_types_by_name['DeploymentJobRunState']
  RegisteredModel = _reflection.GeneratedProtocolMessageType('RegisteredModel', (_message.Message,), {
    'DESCRIPTOR' : _REGISTEREDMODEL,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RegisteredModel)
    })
  _sym_db.RegisterMessage(RegisteredModel)

  ModelVersion = _reflection.GeneratedProtocolMessageType('ModelVersion', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSION,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ModelVersion)
    })
  _sym_db.RegisterMessage(ModelVersion)

  DeploymentJobConnection = _reflection.GeneratedProtocolMessageType('DeploymentJobConnection', (_message.Message,), {
    'DESCRIPTOR' : _DEPLOYMENTJOBCONNECTION,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeploymentJobConnection)
    })
  _sym_db.RegisterMessage(DeploymentJobConnection)

  ModelVersionDeploymentJobState = _reflection.GeneratedProtocolMessageType('ModelVersionDeploymentJobState', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSIONDEPLOYMENTJOBSTATE,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ModelVersionDeploymentJobState)
    })
  _sym_db.RegisterMessage(ModelVersionDeploymentJobState)

  CreateRegisteredModel = _reflection.GeneratedProtocolMessageType('CreateRegisteredModel', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _CREATEREGISTEREDMODEL_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.CreateRegisteredModel.Response)
      })
    ,
    'DESCRIPTOR' : _CREATEREGISTEREDMODEL,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.CreateRegisteredModel)
    })
  _sym_db.RegisterMessage(CreateRegisteredModel)
  _sym_db.RegisterMessage(CreateRegisteredModel.Response)

  RenameRegisteredModel = _reflection.GeneratedProtocolMessageType('RenameRegisteredModel', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _RENAMEREGISTEREDMODEL_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.RenameRegisteredModel.Response)
      })
    ,
    'DESCRIPTOR' : _RENAMEREGISTEREDMODEL,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RenameRegisteredModel)
    })
  _sym_db.RegisterMessage(RenameRegisteredModel)
  _sym_db.RegisterMessage(RenameRegisteredModel.Response)

  UpdateRegisteredModel = _reflection.GeneratedProtocolMessageType('UpdateRegisteredModel', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _UPDATEREGISTEREDMODEL_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.UpdateRegisteredModel.Response)
      })
    ,
    'DESCRIPTOR' : _UPDATEREGISTEREDMODEL,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.UpdateRegisteredModel)
    })
  _sym_db.RegisterMessage(UpdateRegisteredModel)
  _sym_db.RegisterMessage(UpdateRegisteredModel.Response)

  DeleteRegisteredModel = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModel', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEREGISTEREDMODEL_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteRegisteredModel.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEREGISTEREDMODEL,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteRegisteredModel)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModel)
  _sym_db.RegisterMessage(DeleteRegisteredModel.Response)

  GetRegisteredModel = _reflection.GeneratedProtocolMessageType('GetRegisteredModel', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETREGISTEREDMODEL_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetRegisteredModel.Response)
      })
    ,
    'DESCRIPTOR' : _GETREGISTEREDMODEL,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetRegisteredModel)
    })
  _sym_db.RegisterMessage(GetRegisteredModel)
  _sym_db.RegisterMessage(GetRegisteredModel.Response)

  SearchRegisteredModels = _reflection.GeneratedProtocolMessageType('SearchRegisteredModels', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SEARCHREGISTEREDMODELS_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SearchRegisteredModels.Response)
      })
    ,
    'DESCRIPTOR' : _SEARCHREGISTEREDMODELS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SearchRegisteredModels)
    })
  _sym_db.RegisterMessage(SearchRegisteredModels)
  _sym_db.RegisterMessage(SearchRegisteredModels.Response)

  GetLatestVersions = _reflection.GeneratedProtocolMessageType('GetLatestVersions', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETLATESTVERSIONS_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetLatestVersions.Response)
      })
    ,
    'DESCRIPTOR' : _GETLATESTVERSIONS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetLatestVersions)
    })
  _sym_db.RegisterMessage(GetLatestVersions)
  _sym_db.RegisterMessage(GetLatestVersions.Response)

  CreateModelVersion = _reflection.GeneratedProtocolMessageType('CreateModelVersion', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _CREATEMODELVERSION_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.CreateModelVersion.Response)
      })
    ,
    'DESCRIPTOR' : _CREATEMODELVERSION,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.CreateModelVersion)
    })
  _sym_db.RegisterMessage(CreateModelVersion)
  _sym_db.RegisterMessage(CreateModelVersion.Response)

  UpdateModelVersion = _reflection.GeneratedProtocolMessageType('UpdateModelVersion', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _UPDATEMODELVERSION_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.UpdateModelVersion.Response)
      })
    ,
    'DESCRIPTOR' : _UPDATEMODELVERSION,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.UpdateModelVersion)
    })
  _sym_db.RegisterMessage(UpdateModelVersion)
  _sym_db.RegisterMessage(UpdateModelVersion.Response)

  TransitionModelVersionStage = _reflection.GeneratedProtocolMessageType('TransitionModelVersionStage', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _TRANSITIONMODELVERSIONSTAGE_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.TransitionModelVersionStage.Response)
      })
    ,
    'DESCRIPTOR' : _TRANSITIONMODELVERSIONSTAGE,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.TransitionModelVersionStage)
    })
  _sym_db.RegisterMessage(TransitionModelVersionStage)
  _sym_db.RegisterMessage(TransitionModelVersionStage.Response)

  DeleteModelVersion = _reflection.GeneratedProtocolMessageType('DeleteModelVersion', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEMODELVERSION_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteModelVersion.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEMODELVERSION,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteModelVersion)
    })
  _sym_db.RegisterMessage(DeleteModelVersion)
  _sym_db.RegisterMessage(DeleteModelVersion.Response)

  GetModelVersion = _reflection.GeneratedProtocolMessageType('GetModelVersion', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETMODELVERSION_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetModelVersion.Response)
      })
    ,
    'DESCRIPTOR' : _GETMODELVERSION,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetModelVersion)
    })
  _sym_db.RegisterMessage(GetModelVersion)
  _sym_db.RegisterMessage(GetModelVersion.Response)

  SearchModelVersions = _reflection.GeneratedProtocolMessageType('SearchModelVersions', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SEARCHMODELVERSIONS_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SearchModelVersions.Response)
      })
    ,
    'DESCRIPTOR' : _SEARCHMODELVERSIONS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SearchModelVersions)
    })
  _sym_db.RegisterMessage(SearchModelVersions)
  _sym_db.RegisterMessage(SearchModelVersions.Response)

  GetModelVersionDownloadUri = _reflection.GeneratedProtocolMessageType('GetModelVersionDownloadUri', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETMODELVERSIONDOWNLOADURI_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetModelVersionDownloadUri.Response)
      })
    ,
    'DESCRIPTOR' : _GETMODELVERSIONDOWNLOADURI,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetModelVersionDownloadUri)
    })
  _sym_db.RegisterMessage(GetModelVersionDownloadUri)
  _sym_db.RegisterMessage(GetModelVersionDownloadUri.Response)

  ModelVersionTag = _reflection.GeneratedProtocolMessageType('ModelVersionTag', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSIONTAG,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ModelVersionTag)
    })
  _sym_db.RegisterMessage(ModelVersionTag)

  ModelParam = _reflection.GeneratedProtocolMessageType('ModelParam', (_message.Message,), {
    'DESCRIPTOR' : _MODELPARAM,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ModelParam)
    })
  _sym_db.RegisterMessage(ModelParam)

  ModelMetric = _reflection.GeneratedProtocolMessageType('ModelMetric', (_message.Message,), {
    'DESCRIPTOR' : _MODELMETRIC,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ModelMetric)
    })
  _sym_db.RegisterMessage(ModelMetric)

  RegisteredModelTag = _reflection.GeneratedProtocolMessageType('RegisteredModelTag', (_message.Message,), {
    'DESCRIPTOR' : _REGISTEREDMODELTAG,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RegisteredModelTag)
    })
  _sym_db.RegisterMessage(RegisteredModelTag)

  SetRegisteredModelTag = _reflection.GeneratedProtocolMessageType('SetRegisteredModelTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SETREGISTEREDMODELTAG_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SetRegisteredModelTag.Response)
      })
    ,
    'DESCRIPTOR' : _SETREGISTEREDMODELTAG,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SetRegisteredModelTag)
    })
  _sym_db.RegisterMessage(SetRegisteredModelTag)
  _sym_db.RegisterMessage(SetRegisteredModelTag.Response)

  SetModelVersionTag = _reflection.GeneratedProtocolMessageType('SetModelVersionTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SETMODELVERSIONTAG_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SetModelVersionTag.Response)
      })
    ,
    'DESCRIPTOR' : _SETMODELVERSIONTAG,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SetModelVersionTag)
    })
  _sym_db.RegisterMessage(SetModelVersionTag)
  _sym_db.RegisterMessage(SetModelVersionTag.Response)

  DeleteRegisteredModelTag = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEREGISTEREDMODELTAG_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteRegisteredModelTag.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEREGISTEREDMODELTAG,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteRegisteredModelTag)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelTag)
  _sym_db.RegisterMessage(DeleteRegisteredModelTag.Response)

  DeleteModelVersionTag = _reflection.GeneratedProtocolMessageType('DeleteModelVersionTag', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEMODELVERSIONTAG_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteModelVersionTag.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEMODELVERSIONTAG,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteModelVersionTag)
    })
  _sym_db.RegisterMessage(DeleteModelVersionTag)
  _sym_db.RegisterMessage(DeleteModelVersionTag.Response)

  RegisteredModelAlias = _reflection.GeneratedProtocolMessageType('RegisteredModelAlias', (_message.Message,), {
    'DESCRIPTOR' : _REGISTEREDMODELALIAS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.RegisteredModelAlias)
    })
  _sym_db.RegisterMessage(RegisteredModelAlias)

  SetRegisteredModelAlias = _reflection.GeneratedProtocolMessageType('SetRegisteredModelAlias', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _SETREGISTEREDMODELALIAS_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.SetRegisteredModelAlias.Response)
      })
    ,
    'DESCRIPTOR' : _SETREGISTEREDMODELALIAS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.SetRegisteredModelAlias)
    })
  _sym_db.RegisterMessage(SetRegisteredModelAlias)
  _sym_db.RegisterMessage(SetRegisteredModelAlias.Response)

  DeleteRegisteredModelAlias = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelAlias', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _DELETEREGISTEREDMODELALIAS_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.DeleteRegisteredModelAlias.Response)
      })
    ,
    'DESCRIPTOR' : _DELETEREGISTEREDMODELALIAS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.DeleteRegisteredModelAlias)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelAlias)
  _sym_db.RegisterMessage(DeleteRegisteredModelAlias.Response)

  GetModelVersionByAlias = _reflection.GeneratedProtocolMessageType('GetModelVersionByAlias', (_message.Message,), {

    'Response' : _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
      'DESCRIPTOR' : _GETMODELVERSIONBYALIAS_RESPONSE,
      '__module__' : 'model_registry_pb2'
      # @@protoc_insertion_point(class_scope:mlflow.GetModelVersionByAlias.Response)
      })
    ,
    'DESCRIPTOR' : _GETMODELVERSIONBYALIAS,
    '__module__' : 'model_registry_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.GetModelVersionByAlias)
    })
  _sym_db.RegisterMessage(GetModelVersionByAlias)
  _sym_db.RegisterMessage(GetModelVersionByAlias.Response)

  _MODELREGISTRYSERVICE = DESCRIPTOR.services_by_name['ModelRegistryService']
  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n\024org.mlflow.api.proto\220\001\001\240\001\001\342?\002\020\001'
    _CREATEREGISTEREDMODEL.fields_by_name['name']._options = None
    _CREATEREGISTEREDMODEL.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _CREATEREGISTEREDMODEL._options = None
    _CREATEREGISTEREDMODEL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _RENAMEREGISTEREDMODEL.fields_by_name['name']._options = None
    _RENAMEREGISTEREDMODEL.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _RENAMEREGISTEREDMODEL._options = None
    _RENAMEREGISTEREDMODEL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _UPDATEREGISTEREDMODEL.fields_by_name['name']._options = None
    _UPDATEREGISTEREDMODEL.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _UPDATEREGISTEREDMODEL._options = None
    _UPDATEREGISTEREDMODEL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEREGISTEREDMODEL.fields_by_name['name']._options = None
    _DELETEREGISTEREDMODEL.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODEL._options = None
    _DELETEREGISTEREDMODEL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETREGISTEREDMODEL.fields_by_name['name']._options = None
    _GETREGISTEREDMODEL.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETREGISTEREDMODEL._options = None
    _GETREGISTEREDMODEL._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SEARCHREGISTEREDMODELS._options = None
    _SEARCHREGISTEREDMODELS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETLATESTVERSIONS.fields_by_name['name']._options = None
    _GETLATESTVERSIONS.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETLATESTVERSIONS._options = None
    _GETLATESTVERSIONS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _CREATEMODELVERSION.fields_by_name['name']._options = None
    _CREATEMODELVERSION.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _CREATEMODELVERSION.fields_by_name['source']._options = None
    _CREATEMODELVERSION.fields_by_name['source']._serialized_options = b'\370\206\031\001'
    _CREATEMODELVERSION._options = None
    _CREATEMODELVERSION._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _UPDATEMODELVERSION.fields_by_name['name']._options = None
    _UPDATEMODELVERSION.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _UPDATEMODELVERSION.fields_by_name['version']._options = None
    _UPDATEMODELVERSION.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _UPDATEMODELVERSION._options = None
    _UPDATEMODELVERSION._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['name']._options = None
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['version']._options = None
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['stage']._options = None
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['stage']._serialized_options = b'\370\206\031\001'
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['archive_existing_versions']._options = None
    _TRANSITIONMODELVERSIONSTAGE.fields_by_name['archive_existing_versions']._serialized_options = b'\370\206\031\001'
    _TRANSITIONMODELVERSIONSTAGE._options = None
    _TRANSITIONMODELVERSIONSTAGE._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEMODELVERSION.fields_by_name['name']._options = None
    _DELETEMODELVERSION.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSION.fields_by_name['version']._options = None
    _DELETEMODELVERSION.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSION._options = None
    _DELETEMODELVERSION._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETMODELVERSION.fields_by_name['name']._options = None
    _GETMODELVERSION.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSION.fields_by_name['version']._options = None
    _GETMODELVERSION.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSION._options = None
    _GETMODELVERSION._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SEARCHMODELVERSIONS._options = None
    _SEARCHMODELVERSIONS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETMODELVERSIONDOWNLOADURI.fields_by_name['name']._options = None
    _GETMODELVERSIONDOWNLOADURI.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONDOWNLOADURI.fields_by_name['version']._options = None
    _GETMODELVERSIONDOWNLOADURI.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONDOWNLOADURI._options = None
    _GETMODELVERSIONDOWNLOADURI._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _MODELMETRIC.fields_by_name['dataset_name']._options = None
    _MODELMETRIC.fields_by_name['dataset_name']._serialized_options = b'\360\206\031\003'
    _MODELMETRIC.fields_by_name['dataset_digest']._options = None
    _MODELMETRIC.fields_by_name['dataset_digest']._serialized_options = b'\360\206\031\003'
    _MODELMETRIC.fields_by_name['model_id']._options = None
    _MODELMETRIC.fields_by_name['model_id']._serialized_options = b'\360\206\031\003'
    _MODELMETRIC.fields_by_name['run_id']._options = None
    _MODELMETRIC.fields_by_name['run_id']._serialized_options = b'\360\206\031\003'
    _SETREGISTEREDMODELTAG.fields_by_name['name']._options = None
    _SETREGISTEREDMODELTAG.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELTAG.fields_by_name['key']._options = None
    _SETREGISTEREDMODELTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELTAG.fields_by_name['value']._options = None
    _SETREGISTEREDMODELTAG.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELTAG._options = None
    _SETREGISTEREDMODELTAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SETMODELVERSIONTAG.fields_by_name['name']._options = None
    _SETMODELVERSIONTAG.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAG.fields_by_name['version']._options = None
    _SETMODELVERSIONTAG.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAG.fields_by_name['key']._options = None
    _SETMODELVERSIONTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAG.fields_by_name['value']._options = None
    _SETMODELVERSIONTAG.fields_by_name['value']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAG._options = None
    _SETMODELVERSIONTAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEREGISTEREDMODELTAG.fields_by_name['name']._options = None
    _DELETEREGISTEREDMODELTAG.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELTAG.fields_by_name['key']._options = None
    _DELETEREGISTEREDMODELTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELTAG._options = None
    _DELETEREGISTEREDMODELTAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEMODELVERSIONTAG.fields_by_name['name']._options = None
    _DELETEMODELVERSIONTAG.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONTAG.fields_by_name['version']._options = None
    _DELETEMODELVERSIONTAG.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONTAG.fields_by_name['key']._options = None
    _DELETEMODELVERSIONTAG.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONTAG._options = None
    _DELETEMODELVERSIONTAG._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _SETREGISTEREDMODELALIAS.fields_by_name['name']._options = None
    _SETREGISTEREDMODELALIAS.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELALIAS.fields_by_name['alias']._options = None
    _SETREGISTEREDMODELALIAS.fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELALIAS.fields_by_name['version']._options = None
    _SETREGISTEREDMODELALIAS.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELALIAS._options = None
    _SETREGISTEREDMODELALIAS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _DELETEREGISTEREDMODELALIAS.fields_by_name['name']._options = None
    _DELETEREGISTEREDMODELALIAS.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELALIAS.fields_by_name['alias']._options = None
    _DELETEREGISTEREDMODELALIAS.fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELALIAS._options = None
    _DELETEREGISTEREDMODELALIAS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _GETMODELVERSIONBYALIAS.fields_by_name['name']._options = None
    _GETMODELVERSIONBYALIAS.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONBYALIAS.fields_by_name['alias']._options = None
    _GETMODELVERSIONBYALIAS.fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONBYALIAS._options = None
    _GETMODELVERSIONBYALIAS._serialized_options = b'\342?(\n&com.databricks.rpc.RPC[$this.Response]'
    _MODELREGISTRYSERVICE.methods_by_name['createRegisteredModel']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['createRegisteredModel']._serialized_options = b'\362\206\031J\n.\n\004POST\022 /mlflow/registered-models/create\032\004\010\002\020\000\020\001*\026Create RegisteredModel'
    _MODELREGISTRYSERVICE.methods_by_name['renameRegisteredModel']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['renameRegisteredModel']._serialized_options = b'\362\206\031J\n.\n\004POST\022 /mlflow/registered-models/rename\032\004\010\002\020\000\020\001*\026Rename RegisteredModel'
    _MODELREGISTRYSERVICE.methods_by_name['updateRegisteredModel']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['updateRegisteredModel']._serialized_options = b'\362\206\031K\n/\n\005PATCH\022 /mlflow/registered-models/update\032\004\010\002\020\000\020\001*\026Update RegisteredModel'
    _MODELREGISTRYSERVICE.methods_by_name['deleteRegisteredModel']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['deleteRegisteredModel']._serialized_options = b'\362\206\031L\n0\n\006DELETE\022 /mlflow/registered-models/delete\032\004\010\002\020\000\020\001*\026Delete RegisteredModel'
    _MODELREGISTRYSERVICE.methods_by_name['getRegisteredModel']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['getRegisteredModel']._serialized_options = b'\362\206\031C\n*\n\003GET\022\035/mlflow/registered-models/get\032\004\010\002\020\000\020\001*\023Get RegisteredModel'
    _MODELREGISTRYSERVICE.methods_by_name['searchRegisteredModels']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['searchRegisteredModels']._serialized_options = b'\362\206\031J\n-\n\003GET\022 /mlflow/registered-models/search\032\004\010\002\020\000\020\001*\027Search RegisteredModels'
    _MODELREGISTRYSERVICE.methods_by_name['getLatestVersions']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['getLatestVersions']._serialized_options = b'\362\206\031\225\001\n;\n\004POST\022-/mlflow/registered-models/get-latest-versions\032\004\010\002\020\000\n:\n\003GET\022-/mlflow/registered-models/get-latest-versions\032\004\010\002\020\000\020\001*\030Get Latest ModelVersions'
    _MODELREGISTRYSERVICE.methods_by_name['createModelVersion']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['createModelVersion']._serialized_options = b'\362\206\031D\n+\n\004POST\022\035/mlflow/model-versions/create\032\004\010\002\020\000\020\001*\023Create ModelVersion'
    _MODELREGISTRYSERVICE.methods_by_name['updateModelVersion']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['updateModelVersion']._serialized_options = b'\362\206\031E\n,\n\005PATCH\022\035/mlflow/model-versions/update\032\004\010\002\020\000\020\001*\023Update ModelVersion'
    _MODELREGISTRYSERVICE.methods_by_name['transitionModelVersionStage']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['transitionModelVersionStage']._serialized_options = b'\362\206\031X\n5\n\004POST\022\'/mlflow/model-versions/transition-stage\032\004\010\002\020\000\020\001*\035Transition ModelVersion Stage'
    _MODELREGISTRYSERVICE.methods_by_name['deleteModelVersion']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['deleteModelVersion']._serialized_options = b'\362\206\031F\n-\n\006DELETE\022\035/mlflow/model-versions/delete\032\004\010\002\020\000\020\001*\023Delete ModelVersion'
    _MODELREGISTRYSERVICE.methods_by_name['getModelVersion']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['getModelVersion']._serialized_options = b'\362\206\031=\n\'\n\003GET\022\032/mlflow/model-versions/get\032\004\010\002\020\000\020\001*\020Get ModelVersion'
    _MODELREGISTRYSERVICE.methods_by_name['searchModelVersions']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['searchModelVersions']._serialized_options = b'\362\206\031D\n*\n\003GET\022\035/mlflow/model-versions/search\032\004\010\002\020\000\020\001*\024Search ModelVersions\272\214\031\000'
    _MODELREGISTRYSERVICE.methods_by_name['getModelVersionDownloadUri']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['getModelVersionDownloadUri']._serialized_options = b'\362\206\031e\n4\n\003GET\022\'/mlflow/model-versions/get-download-uri\032\004\010\002\020\000\020\001*+Get Download URI For ModelVersion Artifacts'
    _MODELREGISTRYSERVICE.methods_by_name['setRegisteredModelTag']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['setRegisteredModelTag']._serialized_options = b'\362\206\031M\n/\n\004POST\022!/mlflow/registered-models/set-tag\032\004\010\002\020\000\020\001*\030Set Registered Model Tag'
    _MODELREGISTRYSERVICE.methods_by_name['setModelVersionTag']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['setModelVersionTag']._serialized_options = b'\362\206\031G\n,\n\004POST\022\036/mlflow/model-versions/set-tag\032\004\010\002\020\000\020\001*\025Set Model Version Tag'
    _MODELREGISTRYSERVICE.methods_by_name['deleteRegisteredModelTag']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['deleteRegisteredModelTag']._serialized_options = b'\362\206\031U\n4\n\006DELETE\022$/mlflow/registered-models/delete-tag\032\004\010\002\020\000\020\001*\033Delete Registered Model Tag'
    _MODELREGISTRYSERVICE.methods_by_name['deleteModelVersionTag']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['deleteModelVersionTag']._serialized_options = b'\362\206\031O\n1\n\006DELETE\022!/mlflow/model-versions/delete-tag\032\004\010\002\020\000\020\001*\030Delete Model Version Tag'
    _MODELREGISTRYSERVICE.methods_by_name['setRegisteredModelAlias']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['setRegisteredModelAlias']._serialized_options = b'\362\206\031M\n-\n\004POST\022\037/mlflow/registered-models/alias\032\004\010\002\020\000\020\001*\032Set Registered Model Alias'
    _MODELREGISTRYSERVICE.methods_by_name['deleteRegisteredModelAlias']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['deleteRegisteredModelAlias']._serialized_options = b'\362\206\031R\n/\n\006DELETE\022\037/mlflow/registered-models/alias\032\004\010\002\020\000\020\001*\035Delete Registered Model Alias'
    _MODELREGISTRYSERVICE.methods_by_name['getModelVersionByAlias']._options = None
    _MODELREGISTRYSERVICE.methods_by_name['getModelVersionByAlias']._serialized_options = b'\362\206\031L\n,\n\003GET\022\037/mlflow/registered-models/alias\032\004\010\002\020\000\020\001*\032Get Model Version by Alias'
    _MODELVERSIONSTATUS._serialized_start=5580
    _MODELVERSIONSTATUS._serialized_end=5662
    _REGISTEREDMODEL._serialized_start=74
    _REGISTEREDMODEL._serialized_end=435
    _MODELVERSION._serialized_start=438
    _MODELVERSION._serialized_end=952
    _DEPLOYMENTJOBCONNECTION._serialized_start=955
    _DEPLOYMENTJOBCONNECTION._serialized_end=1118
    _DEPLOYMENTJOBCONNECTION_STATE._serialized_start=983
    _DEPLOYMENTJOBCONNECTION_STATE._serialized_end=1118
    _MODELVERSIONDEPLOYMENTJOBSTATE._serialized_start=1121
    _MODELVERSIONDEPLOYMENTJOBSTATE._serialized_end=1521
    _MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE._serialized_start=1354
    _MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE._serialized_end=1521
    _CREATEREGISTEREDMODEL._serialized_start=1524
    _CREATEREGISTEREDMODEL._serialized_end=1765
    _CREATEREGISTEREDMODEL_RESPONSE._serialized_start=1659
    _CREATEREGISTEREDMODEL_RESPONSE._serialized_end=1720
    _RENAMEREGISTEREDMODEL._serialized_start=1768
    _RENAMEREGISTEREDMODEL._serialized_end=1937
    _RENAMEREGISTEREDMODEL_RESPONSE._serialized_start=1659
    _RENAMEREGISTEREDMODEL_RESPONSE._serialized_end=1720
    _UPDATEREGISTEREDMODEL._serialized_start=1940
    _UPDATEREGISTEREDMODEL._serialized_end=2139
    _UPDATEREGISTEREDMODEL_RESPONSE._serialized_start=1659
    _UPDATEREGISTEREDMODEL_RESPONSE._serialized_end=1720
    _DELETEREGISTEREDMODEL._serialized_start=2141
    _DELETEREGISTEREDMODEL._serialized_end=2241
    _DELETEREGISTEREDMODEL_RESPONSE._serialized_start=1659
    _DELETEREGISTEREDMODEL_RESPONSE._serialized_end=1669
    _GETREGISTEREDMODEL._serialized_start=2244
    _GETREGISTEREDMODEL._serialized_end=2392
    _GETREGISTEREDMODEL_RESPONSE._serialized_start=1659
    _GETREGISTEREDMODEL_RESPONSE._serialized_end=1720
    _SEARCHREGISTEREDMODELS._serialized_start=2395
    _SEARCHREGISTEREDMODELS._serialized_end=2633
    _SEARCHREGISTEREDMODELS_RESPONSE._serialized_start=2501
    _SEARCHREGISTEREDMODELS_RESPONSE._serialized_end=2588
    _GETLATESTVERSIONS._serialized_start=2636
    _GETLATESTVERSIONS._serialized_end=2794
    _GETLATESTVERSIONS_RESPONSE._serialized_start=2693
    _GETLATESTVERSIONS_RESPONSE._serialized_end=2749
    _CREATEMODELVERSION._serialized_start=2797
    _CREATEMODELVERSION._serialized_end=3073
    _CREATEMODELVERSION_RESPONSE._serialized_start=2973
    _CREATEMODELVERSION_RESPONSE._serialized_end=3028
    _UPDATEMODELVERSION._serialized_start=3076
    _UPDATEMODELVERSION._serialized_end=3262
    _UPDATEMODELVERSION_RESPONSE._serialized_start=2973
    _UPDATEMODELVERSION_RESPONSE._serialized_end=3028
    _TRANSITIONMODELVERSIONSTAGE._serialized_start=3265
    _TRANSITIONMODELVERSIONSTAGE._serialized_end=3501
    _TRANSITIONMODELVERSIONSTAGE_RESPONSE._serialized_start=2973
    _TRANSITIONMODELVERSIONSTAGE_RESPONSE._serialized_end=3028
    _DELETEMODELVERSION._serialized_start=3503
    _DELETEMODELVERSION._serialized_end=3623
    _DELETEMODELVERSION_RESPONSE._serialized_start=1659
    _DELETEMODELVERSION_RESPONSE._serialized_end=1669
    _GETMODELVERSION._serialized_start=3626
    _GETMODELVERSION._serialized_end=3788
    _GETMODELVERSION_RESPONSE._serialized_start=2973
    _GETMODELVERSION_RESPONSE._serialized_end=3028
    _SEARCHMODELVERSIONS._serialized_start=3791
    _SEARCHMODELVERSIONS._serialized_end=4023
    _SEARCHMODELVERSIONS_RESPONSE._serialized_start=3897
    _SEARCHMODELVERSIONS_RESPONSE._serialized_end=3978
    _GETMODELVERSIONDOWNLOADURI._serialized_start=4026
    _GETMODELVERSIONDOWNLOADURI._serialized_end=4176
    _GETMODELVERSIONDOWNLOADURI_RESPONSE._serialized_start=4099
    _GETMODELVERSIONDOWNLOADURI_RESPONSE._serialized_end=4131
    _MODELVERSIONTAG._serialized_start=4178
    _MODELVERSIONTAG._serialized_end=4223
    _MODELPARAM._serialized_start=4225
    _MODELPARAM._serialized_end=4266
    _MODELMETRIC._serialized_start=4269
    _MODELMETRIC._serialized_end=4450
    _REGISTEREDMODELTAG._serialized_start=4452
    _REGISTEREDMODELTAG._serialized_end=4500
    _SETREGISTEREDMODELTAG._serialized_start=4503
    _SETREGISTEREDMODELTAG._serialized_end=4643
    _SETREGISTEREDMODELTAG_RESPONSE._serialized_start=1659
    _SETREGISTEREDMODELTAG_RESPONSE._serialized_end=1669
    _SETMODELVERSIONTAG._serialized_start=4646
    _SETMODELVERSIONTAG._serialized_end=4806
    _SETMODELVERSIONTAG_RESPONSE._serialized_start=1659
    _SETMODELVERSIONTAG_RESPONSE._serialized_end=1669
    _DELETEREGISTEREDMODELTAG._serialized_start=4808
    _DELETEREGISTEREDMODELTAG._serialized_end=4930
    _DELETEREGISTEREDMODELTAG_RESPONSE._serialized_start=1659
    _DELETEREGISTEREDMODELTAG_RESPONSE._serialized_end=1669
    _DELETEMODELVERSIONTAG._serialized_start=4933
    _DELETEMODELVERSIONTAG._serialized_end=5075
    _DELETEMODELVERSIONTAG_RESPONSE._serialized_start=1659
    _DELETEMODELVERSIONTAG_RESPONSE._serialized_end=1669
    _REGISTEREDMODELALIAS._serialized_start=5077
    _REGISTEREDMODELALIAS._serialized_end=5131
    _SETREGISTEREDMODELALIAS._serialized_start=5134
    _SETREGISTEREDMODELALIAS._serialized_end=5280
    _SETREGISTEREDMODELALIAS_RESPONSE._serialized_start=1659
    _SETREGISTEREDMODELALIAS_RESPONSE._serialized_end=1669
    _DELETEREGISTEREDMODELALIAS._serialized_start=5282
    _DELETEREGISTEREDMODELALIAS._serialized_end=5408
    _DELETEREGISTEREDMODELALIAS_RESPONSE._serialized_start=1659
    _DELETEREGISTEREDMODELALIAS_RESPONSE._serialized_end=1669
    _GETMODELVERSIONBYALIAS._serialized_start=5411
    _GETMODELVERSIONBYALIAS._serialized_end=5578
    _GETMODELVERSIONBYALIAS_RESPONSE._serialized_start=2973
    _GETMODELVERSIONBYALIAS_RESPONSE._serialized_end=3028
    _MODELREGISTRYSERVICE._serialized_start=5665
    _MODELREGISTRYSERVICE._serialized_end=9505
  ModelRegistryService = service_reflection.GeneratedServiceType('ModelRegistryService', (_service.Service,), dict(
    DESCRIPTOR = _MODELREGISTRYSERVICE,
    __module__ = 'model_registry_pb2'
    ))

  ModelRegistryService_Stub = service_reflection.GeneratedServiceStubType('ModelRegistryService_Stub', (ModelRegistryService,), dict(
    DESCRIPTOR = _MODELREGISTRYSERVICE,
    __module__ = 'model_registry_pb2'
    ))


  # @@protoc_insertion_point(module_scope)

