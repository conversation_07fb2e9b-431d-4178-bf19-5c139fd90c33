
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: databricks_uc_registry_messages.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import databricks_pb2 as databricks__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%databricks_uc_registry_messages.proto\x12\x16mlflow.ucmodelregistry\x1a\x15scalapb/scalapb.proto\x1a\x10\x64\x61tabricks.proto\"\xea\x02\n\x0fRegisteredModel\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x02 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x03 \x01(\x03\x12\x0f\n\x07user_id\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12=\n\x07\x61liases\x18\x06 \x03(\x0b\x32,.mlflow.ucmodelregistry.RegisteredModelAlias\x12\x38\n\x04tags\x18\x07 \x03(\x0b\x32*.mlflow.ucmodelregistry.RegisteredModelTag\x12\x19\n\x11\x64\x65ployment_job_id\x18\x08 \x01(\t\x12S\n\x14\x64\x65ployment_job_state\x18\t \x01(\x0e\x32\x35.mlflow.ucmodelregistry.DeploymentJobConnection.State\"6\n\x14RegisteredModelAlias\x12\r\n\x05\x61lias\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"0\n\x12RegisteredModelTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\")\n\nModelParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xb5\x01\n\x0bModelMetric\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\x12\x1a\n\x0c\x64\x61taset_name\x18\x05 \x01(\tB\x04\xf0\x86\x19\x03\x12\x1c\n\x0e\x64\x61taset_digest\x18\x06 \x01(\tB\x04\xf0\x86\x19\x03\x12\x16\n\x08model_id\x18\x07 \x01(\tB\x04\xf0\x86\x19\x03\x12\x14\n\x06run_id\x18\x08 \x01(\tB\x04\xf0\x86\x19\x03\"-\n\x0fModelVersionTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xac\x05\n\x0cModelVersion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x03 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x04 \x01(\x03\x12\x0f\n\x07user_id\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x0e\n\x06source\x18\x07 \x01(\t\x12\x0e\n\x06run_id\x18\x08 \x01(\t\x12\x19\n\x11run_experiment_id\x18\t \x01(\t\x12\x1e\n\x16run_tracking_server_id\x18\n \x01(\t\x12:\n\x06status\x18\x0b \x01(\x0e\x32*.mlflow.ucmodelregistry.ModelVersionStatus\x12\x16\n\x0estatus_message\x18\x0c \x01(\t\x12\x18\n\x10storage_location\x18\r \x01(\t\x12=\n\x07\x61liases\x18\x0e \x03(\x0b\x32,.mlflow.ucmodelregistry.RegisteredModelAlias\x12\x35\n\x04tags\x18\x0f \x03(\x0b\x32\'.mlflow.ucmodelregistry.ModelVersionTag\x12\x10\n\x08model_id\x18\x10 \x01(\t\x12\x38\n\x0cmodel_params\x18\x11 \x03(\x0b\x32\".mlflow.ucmodelregistry.ModelParam\x12:\n\rmodel_metrics\x18\x12 \x03(\x0b\x32#.mlflow.ucmodelregistry.ModelMetric\x12T\n\x14\x64\x65ployment_job_state\x18\x13 \x01(\x0b\x32\x36.mlflow.ucmodelregistry.ModelVersionDeploymentJobState\"\xa3\x01\n\x17\x44\x65ploymentJobConnection\"\x87\x01\n\x05State\x12/\n+DEPLOYMENT_JOB_CONNECTION_STATE_UNSPECIFIED\x10\x00\x12\x0e\n\nNOT_SET_UP\x10\x01\x12\r\n\tCONNECTED\x10\x02\x12\r\n\tNOT_FOUND\x10\x03\x12\x1f\n\x1bREQUIRED_PARAMETERS_CHANGED\x10\x04\"\xb0\x03\n\x1eModelVersionDeploymentJobState\x12\x0e\n\x06job_id\x18\x01 \x01(\t\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12H\n\tjob_state\x18\x03 \x01(\x0e\x32\x35.mlflow.ucmodelregistry.DeploymentJobConnection.State\x12_\n\trun_state\x18\x04 \x01(\x0e\x32L.mlflow.ucmodelregistry.ModelVersionDeploymentJobState.DeploymentJobRunState\x12\x19\n\x11\x63urrent_task_name\x18\x05 \x01(\t\"\xa7\x01\n\x15\x44\x65ploymentJobRunState\x12(\n$DEPLOYMENT_JOB_RUN_STATE_UNSPECIFIED\x10\x00\x12!\n\x1dNO_VALID_DEPLOYMENT_JOB_FOUND\x10\x01\x12\x0b\n\x07RUNNING\x10\x02\x12\r\n\tSUCCEEDED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\x0b\n\x07PENDING\x10\x05\x12\x0c\n\x08\x41PPROVAL\x10\x06\"\xe5\x03\n\x14TemporaryCredentials\x12\x46\n\x14\x61ws_temp_credentials\x18\x02 \x01(\x0b\x32&.mlflow.ucmodelregistry.AwsCredentialsH\x00\x12S\n\x19\x61zure_user_delegation_sas\x18\x03 \x01(\x0b\x32..mlflow.ucmodelregistry.AzureUserDelegationSASH\x00\x12@\n\x0fgcp_oauth_token\x18\x04 \x01(\x0b\x32%.mlflow.ucmodelregistry.GcpOauthTokenH\x00\x12\x44\n\x13r2_temp_credentials\x18\x05 \x01(\x0b\x32%.mlflow.ucmodelregistry.R2CredentialsH\x00\x12\x17\n\x0f\x65xpiration_time\x18\x01 \x01(\x03\x12\x39\n\x0cstorage_mode\x18\x06 \x01(\x0e\x32#.mlflow.ucmodelregistry.StorageMode\x12\x45\n\x12\x65ncryption_details\x18\x07 \x01(\x0b\x32).mlflow.ucmodelregistry.EncryptionDetailsB\r\n\x0b\x63redentials\"Y\n\x0e\x41wsCredentials\x12\x15\n\raccess_key_id\x18\x01 \x01(\t\x12\x19\n\x11secret_access_key\x18\x02 \x01(\t\x12\x15\n\rsession_token\x18\x03 \x01(\t\"+\n\x16\x41zureUserDelegationSAS\x12\x11\n\tsas_token\x18\x01 \x01(\t\"$\n\rGcpOauthToken\x12\x13\n\x0boauth_token\x18\x01 \x01(\t\"X\n\rR2Credentials\x12\x15\n\raccess_key_id\x18\x01 \x01(\t\x12\x19\n\x11secret_access_key\x18\x02 \x01(\t\x12\x15\n\rsession_token\x18\x03 \x01(\t\"~\n\x11\x45ncryptionDetails\x12N\n\x16sse_encryption_details\x18\x01 \x01(\x0b\x32,.mlflow.ucmodelregistry.SseEncryptionDetailsH\x00\x42\x19\n\x17\x65ncryption_details_type\"r\n\x14SseEncryptionDetails\x12\x41\n\talgorithm\x18\x01 \x01(\x0e\x32..mlflow.ucmodelregistry.SseEncryptionAlgorithm\x12\x17\n\x0f\x61ws_kms_key_arn\x18\x02 \x01(\t\"\xd8\x01\n\x1c\x43reateRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x38\n\x04tags\x18\x02 \x03(\x0b\x32*.mlflow.ucmodelregistry.RegisteredModelTag\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x04 \x01(\t::\xe2?7\n5com.databricks.rpc.RPC[CreateRegisteredModelResponse]\"b\n\x1d\x43reateRegisteredModelResponse\x12\x41\n\x10registered_model\x18\x01 \x01(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\"\xb0\x01\n\x1cUpdateRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x10\n\x08new_name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x04 \x01(\t::\xe2?7\n5com.databricks.rpc.RPC[UpdateRegisteredModelResponse]\"b\n\x1dUpdateRegisteredModelResponse\x12\x41\n\x10registered_model\x18\x01 \x01(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\"n\n\x1c\x44\x65leteRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01::\xe2?7\n5com.databricks.rpc.RPC[DeleteRegisteredModelResponse]\"\x1f\n\x1d\x44\x65leteRegisteredModelResponse\"h\n\x19GetRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01:7\xe2?4\n2com.databricks.rpc.RPC[GetRegisteredModelResponse]\"_\n\x1aGetRegisteredModelResponse\x12\x41\n\x10registered_model\x18\x01 \x01(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\"\x8a\x01\n\x1dSearchRegisteredModelsRequest\x12\x18\n\x0bmax_results\x18\x01 \x01(\x03:\x03\x31\x30\x30\x12\x12\n\npage_token\x18\x02 \x01(\t:;\xe2?8\n6com.databricks.rpc.RPC[SearchRegisteredModelsResponse]\"}\n\x1eSearchRegisteredModelsResponse\x12\x42\n\x11registered_models\x18\x01 \x03(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"P\n\nDependency\x12\x34\n\x04type\x18\x01 \x01(\x0e\x32&.mlflow.ucmodelregistry.DependencyType\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xea\x02\n\x19\x43reateModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06source\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x1e\n\x16run_tracking_server_id\x18\x05 \x01(\t\x12\x14\n\x0c\x66\x65\x61ture_deps\x18\x06 \x01(\t\x12\x35\n\x04tags\x18\x07 \x03(\x0b\x32\'.mlflow.ucmodelregistry.ModelVersionTag\x12\x46\n\x1amodel_version_dependencies\x18\x08 \x03(\x0b\x32\".mlflow.ucmodelregistry.Dependency\x12\x10\n\x08model_id\x18\t \x01(\t:7\xe2?4\n2com.databricks.rpc.RPC[CreateModelVersionResponse]\"Y\n\x1a\x43reateModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x94\x01\n\x19UpdateModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t:7\xe2?4\n2com.databricks.rpc.RPC[UpdateModelVersionResponse]\"Y\n\x1aUpdateModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x7f\n\x19\x44\x65leteModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:7\xe2?4\n2com.databricks.rpc.RPC[DeleteModelVersionResponse]\"\x1c\n\x1a\x44\x65leteModelVersionResponse\"y\n\x16GetModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:4\xe2?1\n/com.databricks.rpc.RPC[GetModelVersionResponse]\"V\n\x17GetModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x96\x01\n\x1aSearchModelVersionsRequest\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x1a\n\x0bmax_results\x18\x02 \x01(\x03:\x05\x31\x30\x30\x30\x30\x12\x12\n\npage_token\x18\x03 \x01(\t:8\xe2?5\n3com.databricks.rpc.RPC[SearchModelVersionsResponse]\"t\n\x1bSearchModelVersionsResponse\x12<\n\x0emodel_versions\x18\x01 \x03(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"\xf3\x01\n/GenerateTemporaryModelVersionCredentialsRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x46\n\toperation\x18\x03 \x01(\x0e\x32-.mlflow.ucmodelregistry.ModelVersionOperationB\x04\xf8\x86\x19\x01:M\xe2?J\nHcom.databricks.rpc.RPC[GenerateTemporaryModelVersionCredentialsResponse]\"u\n0GenerateTemporaryModelVersionCredentialsResponse\x12\x41\n\x0b\x63redentials\x18\x01 \x01(\x0b\x32,.mlflow.ucmodelregistry.TemporaryCredentials\"\x8f\x01\n!GetModelVersionDownloadUriRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:?\xe2?<\n:com.databricks.rpc.RPC[GetModelVersionDownloadUriResponse]\":\n\"GetModelVersionDownloadUriResponse\x12\x14\n\x0c\x61rtifact_uri\x18\x01 \x01(\t\"\x83\x01\n\x1b\x46inalizeModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:9\xe2?6\n4com.databricks.rpc.RPC[FinalizeModelVersionResponse]\"[\n\x1c\x46inalizeModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x9e\x01\n\x1eSetRegisteredModelAliasRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01:<\xe2?9\n7com.databricks.rpc.RPC[SetRegisteredModelAliasResponse]\"!\n\x1fSetRegisteredModelAliasResponse\"\x8d\x01\n!DeleteRegisteredModelAliasRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:?\xe2?<\n:com.databricks.rpc.RPC[DeleteRegisteredModelAliasResponse]\"$\n\"DeleteRegisteredModelAliasResponse\"\x90\x01\n\x1cSetRegisteredModelTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\r\n\x05value\x18\x03 \x01(\t::\xe2?7\n5com.databricks.rpc.RPC[SetRegisteredModelTagResponse]\"\x1f\n\x1dSetRegisteredModelTagResponse\"\x87\x01\n\x1f\x44\x65leteRegisteredModelTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:=\xe2?:\n8com.databricks.rpc.RPC[DeleteRegisteredModelTagResponse]\"\"\n DeleteRegisteredModelTagResponse\"\xa1\x01\n\x19SetModelVersionTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\r\n\x05value\x18\x04 \x01(\t:7\xe2?4\n2com.databricks.rpc.RPC[SetModelVersionTagResponse]\"\x1c\n\x1aSetModelVersionTagResponse\"\x98\x01\n\x1c\x44\x65leteModelVersionTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01::\xe2?7\n5com.databricks.rpc.RPC[DeleteModelVersionTagResponse]\"\x1f\n\x1d\x44\x65leteModelVersionTagResponse\"\x85\x01\n\x1dGetModelVersionByAliasRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:;\xe2?8\n6com.databricks.rpc.RPC[GetModelVersionByAliasResponse]\"]\n\x1eGetModelVersionByAliasResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\xa9\x01\n\x06\x45ntity\x12*\n\x03job\x18\x01 \x01(\x0b\x32\x1b.mlflow.ucmodelregistry.JobH\x00\x12\x34\n\x08notebook\x18\x02 \x01(\x0b\x32 .mlflow.ucmodelregistry.NotebookH\x00\x12\x34\n\x08pipeline\x18\x03 \x01(\x0b\x32 .mlflow.ucmodelregistry.PipelineH\x00\x42\x07\n\x05value\";\n\x08Pipeline\x12\x13\n\x0bpipeline_id\x18\x01 \x01(\t\x12\x1a\n\x12pipeline_update_id\x18\x02 \x01(\t\"L\n\x03Job\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08task_key\x18\x02 \x01(\t\x12\x12\n\njob_run_id\x18\x03 \x01(\t\x12\x13\n\x0btask_run_id\x18\x04 \x01(\t\"C\n\x08Notebook\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\ncommand_id\x18\x02 \x01(\t\x12\x17\n\x0fnotebook_run_id\x18\x03 \x01(\t\"p\n\x05Table\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x35\n\ntable_type\x18\x02 \x01(\x0e\x32!.mlflow.ucmodelregistry.TableType\x12\x10\n\x08location\x18\x03 \x01(\t\x12\x10\n\x08table_id\x18\x04 \x01(\t\"D\n\tSecurable\x12.\n\x05table\x18\x01 \x01(\x0b\x32\x1d.mlflow.ucmodelregistry.TableH\x00\x42\x07\n\x05value\"\x84\x01\n\x07Lineage\x12;\n\x10target_securable\x18\x04 \x01(\x0b\x32!.mlflow.ucmodelregistry.Securable\x12<\n\x11source_securables\x18\x05 \x03(\x0b\x32!.mlflow.ucmodelregistry.Securable\"x\n\x11LineageHeaderInfo\x12\x30\n\x08\x65ntities\x18\x01 \x03(\x0b\x32\x1e.mlflow.ucmodelregistry.Entity\x12\x31\n\x08lineages\x18\x02 \x03(\x0b\x32\x1f.mlflow.ucmodelregistry.Lineage\"\x9a\x01\n\x17ModelVersionLineageInfo\x12\x30\n\x08\x65ntities\x18\x01 \x03(\x0b\x32\x1e.mlflow.ucmodelregistry.Entity\x12M\n\tdirection\x18\x02 \x01(\x0e\x32\x34.mlflow.ucmodelregistry.ModelVersionLineageDirectionB\x04\xf8\x86\x19\x01\"\x9a\x02\n\x1e\x45mitModelVersionLineageRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12Y\n\x1amodel_version_lineage_info\x18\x03 \x01(\x0b\x32/.mlflow.ucmodelregistry.ModelVersionLineageInfoB\x04\xf8\x86\x19\x01\x12\x34\n\tsecurable\x18\x04 \x01(\x0b\x32!.mlflow.ucmodelregistry.Securable:<\xe2?9\n7com.databricks.rpc.RPC[EmitModelVersionLineageResponse]\"!\n\x1f\x45mitModelVersionLineageResponse\"\x8c\x01\n5IsDatabricksSdkModelsArtifactRepositoryEnabledRequest:S\xe2?P\nNcom.databricks.rpc.RPC[IsDatabricksSdkModelsArtifactRepositoryEnabledResponse]\"|\n6IsDatabricksSdkModelsArtifactRepositoryEnabledResponse\x12\x42\n4is_databricks_sdk_models_artifact_repository_enabled\x18\x01 \x01(\x08\x42\x04\xf8\x86\x19\x01*c\n\x12ModelVersionStatus\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x18\n\x14PENDING_REGISTRATION\x10\x01\x12\x17\n\x13\x46\x41ILED_REGISTRATION\x10\x02\x12\t\n\x05READY\x10\x03*\x8a\x01\n\x15ModelVersionOperation\x12\'\n#MODEL_VERSION_OPERATION_UNSPECIFIED\x10\x00\x12 \n\x1cMODEL_VERSION_OPERATION_READ\x10\x01\x12&\n\"MODEL_VERSION_OPERATION_READ_WRITE\x10\x02*U\n\x0bStorageMode\x12\x1c\n\x18STORAGE_MODE_UNSPECIFIED\x10\x00\x12\x13\n\x0f\x43USTOMER_HOSTED\x10\x01\x12\x13\n\x0f\x44\x45\x46\x41ULT_STORAGE\x10\x02*c\n\x16SseEncryptionAlgorithm\x12(\n$SSE_ENCRYPTION_ALGORITHM_UNSPECIFIED\x10\x00\x12\x0e\n\nAWS_SSE_S3\x10\x01\x12\x0f\n\x0b\x41WS_SSE_KMS\x10\x02*\xbd\x01\n\x0e\x44\x65pendencyType\x12\x1f\n\x1b\x44\x45PENDENCY_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x44\x41TABRICKS_VECTOR_INDEX\x10\x01\x12\x1d\n\x19\x44\x41TABRICKS_MODEL_ENDPOINT\x10\x02\x12\x1a\n\x16\x44\x41TABRICKS_UC_FUNCTION\x10\x03\x12\x1c\n\x18\x44\x41TABRICKS_UC_CONNECTION\x10\x04\x12\x14\n\x10\x44\x41TABRICKS_TABLE\x10\x05*t\n\tTableType\x12\t\n\x05TABLE\x10\x00\x12\x12\n\x0ePERSISTED_VIEW\x10\x01\x12\r\n\tTEMP_VIEW\x10\x02\x12\x15\n\x11MATERIALIZED_VIEW\x10\x03\x12\x18\n\x14STREAMING_LIVE_TABLE\x10\x04\x12\x08\n\x04PATH\x10\x05*<\n\x1cModelVersionLineageDirection\x12\x0c\n\x08UPSTREAM\x10\x00\x12\x0e\n\nDOWNSTREAM\x10\x01\x42\x32\n(com.databricks.api.proto.ucmodelregistry\xa0\x01\x01\xe2?\x02\x10\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'databricks_uc_registry_messages_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\n(com.databricks.api.proto.ucmodelregistry\240\001\001\342?\002\020\001'
    _globals['_MODELMETRIC'].fields_by_name['dataset_name']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['dataset_name']._serialized_options = b'\360\206\031\003'
    _globals['_MODELMETRIC'].fields_by_name['dataset_digest']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['dataset_digest']._serialized_options = b'\360\206\031\003'
    _globals['_MODELMETRIC'].fields_by_name['model_id']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['model_id']._serialized_options = b'\360\206\031\003'
    _globals['_MODELMETRIC'].fields_by_name['run_id']._loaded_options = None
    _globals['_MODELMETRIC'].fields_by_name['run_id']._serialized_options = b'\360\206\031\003'
    _globals['_CREATEREGISTEREDMODELREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_CREATEREGISTEREDMODELREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEREGISTEREDMODELREQUEST']._loaded_options = None
    _globals['_CREATEREGISTEREDMODELREQUEST']._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[CreateRegisteredModelResponse]'
    _globals['_UPDATEREGISTEREDMODELREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_UPDATEREGISTEREDMODELREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEREGISTEREDMODELREQUEST']._loaded_options = None
    _globals['_UPDATEREGISTEREDMODELREQUEST']._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[UpdateRegisteredModelResponse]'
    _globals['_DELETEREGISTEREDMODELREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELREQUEST']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELREQUEST']._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[DeleteRegisteredModelResponse]'
    _globals['_GETREGISTEREDMODELREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_GETREGISTEREDMODELREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETREGISTEREDMODELREQUEST']._loaded_options = None
    _globals['_GETREGISTEREDMODELREQUEST']._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[GetRegisteredModelResponse]'
    _globals['_SEARCHREGISTEREDMODELSREQUEST']._loaded_options = None
    _globals['_SEARCHREGISTEREDMODELSREQUEST']._serialized_options = b'\342?8\n6com.databricks.rpc.RPC[SearchRegisteredModelsResponse]'
    _globals['_CREATEMODELVERSIONREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_CREATEMODELVERSIONREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEMODELVERSIONREQUEST'].fields_by_name['source']._loaded_options = None
    _globals['_CREATEMODELVERSIONREQUEST'].fields_by_name['source']._serialized_options = b'\370\206\031\001'
    _globals['_CREATEMODELVERSIONREQUEST']._loaded_options = None
    _globals['_CREATEMODELVERSIONREQUEST']._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[CreateModelVersionResponse]'
    _globals['_UPDATEMODELVERSIONREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_UPDATEMODELVERSIONREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEMODELVERSIONREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_UPDATEMODELVERSIONREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_UPDATEMODELVERSIONREQUEST']._loaded_options = None
    _globals['_UPDATEMODELVERSIONREQUEST']._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[UpdateModelVersionResponse]'
    _globals['_DELETEMODELVERSIONREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEMODELVERSIONREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_DELETEMODELVERSIONREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONREQUEST']._loaded_options = None
    _globals['_DELETEMODELVERSIONREQUEST']._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[DeleteModelVersionResponse]'
    _globals['_GETMODELVERSIONREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_GETMODELVERSIONREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_GETMODELVERSIONREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONREQUEST']._loaded_options = None
    _globals['_GETMODELVERSIONREQUEST']._serialized_options = b'\342?1\n/com.databricks.rpc.RPC[GetModelVersionResponse]'
    _globals['_SEARCHMODELVERSIONSREQUEST']._loaded_options = None
    _globals['_SEARCHMODELVERSIONSREQUEST']._serialized_options = b'\342?5\n3com.databricks.rpc.RPC[SearchModelVersionsResponse]'
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST'].fields_by_name['operation']._loaded_options = None
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST'].fields_by_name['operation']._serialized_options = b'\370\206\031\001'
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST']._loaded_options = None
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST']._serialized_options = b'\342?J\nHcom.databricks.rpc.RPC[GenerateTemporaryModelVersionCredentialsResponse]'
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST']._loaded_options = None
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST']._serialized_options = b'\342?<\n:com.databricks.rpc.RPC[GetModelVersionDownloadUriResponse]'
    _globals['_FINALIZEMODELVERSIONREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_FINALIZEMODELVERSIONREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_FINALIZEMODELVERSIONREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_FINALIZEMODELVERSIONREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_FINALIZEMODELVERSIONREQUEST']._loaded_options = None
    _globals['_FINALIZEMODELVERSIONREQUEST']._serialized_options = b'\342?6\n4com.databricks.rpc.RPC[FinalizeModelVersionResponse]'
    _globals['_SETREGISTEREDMODELALIASREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIASREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELALIASREQUEST'].fields_by_name['alias']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIASREQUEST'].fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELALIASREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIASREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELALIASREQUEST']._loaded_options = None
    _globals['_SETREGISTEREDMODELALIASREQUEST']._serialized_options = b'\342?9\n7com.databricks.rpc.RPC[SetRegisteredModelAliasResponse]'
    _globals['_DELETEREGISTEREDMODELALIASREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELALIASREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELALIASREQUEST'].fields_by_name['alias']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELALIASREQUEST'].fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELALIASREQUEST']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELALIASREQUEST']._serialized_options = b'\342?<\n:com.databricks.rpc.RPC[DeleteRegisteredModelAliasResponse]'
    _globals['_SETREGISTEREDMODELTAGREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAGREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELTAGREQUEST'].fields_by_name['key']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAGREQUEST'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_SETREGISTEREDMODELTAGREQUEST']._loaded_options = None
    _globals['_SETREGISTEREDMODELTAGREQUEST']._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[SetRegisteredModelTagResponse]'
    _globals['_DELETEREGISTEREDMODELTAGREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELTAGREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELTAGREQUEST'].fields_by_name['key']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELTAGREQUEST'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEREGISTEREDMODELTAGREQUEST']._loaded_options = None
    _globals['_DELETEREGISTEREDMODELTAGREQUEST']._serialized_options = b'\342?:\n8com.databricks.rpc.RPC[DeleteRegisteredModelTagResponse]'
    _globals['_SETMODELVERSIONTAGREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_SETMODELVERSIONTAGREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAGREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_SETMODELVERSIONTAGREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAGREQUEST'].fields_by_name['key']._loaded_options = None
    _globals['_SETMODELVERSIONTAGREQUEST'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_SETMODELVERSIONTAGREQUEST']._loaded_options = None
    _globals['_SETMODELVERSIONTAGREQUEST']._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[SetModelVersionTagResponse]'
    _globals['_DELETEMODELVERSIONTAGREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAGREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONTAGREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAGREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONTAGREQUEST'].fields_by_name['key']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAGREQUEST'].fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _globals['_DELETEMODELVERSIONTAGREQUEST']._loaded_options = None
    _globals['_DELETEMODELVERSIONTAGREQUEST']._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[DeleteModelVersionTagResponse]'
    _globals['_GETMODELVERSIONBYALIASREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_GETMODELVERSIONBYALIASREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONBYALIASREQUEST'].fields_by_name['alias']._loaded_options = None
    _globals['_GETMODELVERSIONBYALIASREQUEST'].fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _globals['_GETMODELVERSIONBYALIASREQUEST']._loaded_options = None
    _globals['_GETMODELVERSIONBYALIASREQUEST']._serialized_options = b'\342?8\n6com.databricks.rpc.RPC[GetModelVersionByAliasResponse]'
    _globals['_MODELVERSIONLINEAGEINFO'].fields_by_name['direction']._loaded_options = None
    _globals['_MODELVERSIONLINEAGEINFO'].fields_by_name['direction']._serialized_options = b'\370\206\031\001'
    _globals['_EMITMODELVERSIONLINEAGEREQUEST'].fields_by_name['name']._loaded_options = None
    _globals['_EMITMODELVERSIONLINEAGEREQUEST'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_EMITMODELVERSIONLINEAGEREQUEST'].fields_by_name['version']._loaded_options = None
    _globals['_EMITMODELVERSIONLINEAGEREQUEST'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_EMITMODELVERSIONLINEAGEREQUEST'].fields_by_name['model_version_lineage_info']._loaded_options = None
    _globals['_EMITMODELVERSIONLINEAGEREQUEST'].fields_by_name['model_version_lineage_info']._serialized_options = b'\370\206\031\001'
    _globals['_EMITMODELVERSIONLINEAGEREQUEST']._loaded_options = None
    _globals['_EMITMODELVERSIONLINEAGEREQUEST']._serialized_options = b'\342?9\n7com.databricks.rpc.RPC[EmitModelVersionLineageResponse]'
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST']._loaded_options = None
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST']._serialized_options = b'\342?P\nNcom.databricks.rpc.RPC[IsDatabricksSdkModelsArtifactRepositoryEnabledResponse]'
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE'].fields_by_name['is_databricks_sdk_models_artifact_repository_enabled']._loaded_options = None
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE'].fields_by_name['is_databricks_sdk_models_artifact_repository_enabled']._serialized_options = b'\370\206\031\001'
    _globals['_MODELVERSIONSTATUS']._serialized_start=9480
    _globals['_MODELVERSIONSTATUS']._serialized_end=9579
    _globals['_MODELVERSIONOPERATION']._serialized_start=9582
    _globals['_MODELVERSIONOPERATION']._serialized_end=9720
    _globals['_STORAGEMODE']._serialized_start=9722
    _globals['_STORAGEMODE']._serialized_end=9807
    _globals['_SSEENCRYPTIONALGORITHM']._serialized_start=9809
    _globals['_SSEENCRYPTIONALGORITHM']._serialized_end=9908
    _globals['_DEPENDENCYTYPE']._serialized_start=9911
    _globals['_DEPENDENCYTYPE']._serialized_end=10100
    _globals['_TABLETYPE']._serialized_start=10102
    _globals['_TABLETYPE']._serialized_end=10218
    _globals['_MODELVERSIONLINEAGEDIRECTION']._serialized_start=10220
    _globals['_MODELVERSIONLINEAGEDIRECTION']._serialized_end=10280
    _globals['_REGISTEREDMODEL']._serialized_start=107
    _globals['_REGISTEREDMODEL']._serialized_end=469
    _globals['_REGISTEREDMODELALIAS']._serialized_start=471
    _globals['_REGISTEREDMODELALIAS']._serialized_end=525
    _globals['_REGISTEREDMODELTAG']._serialized_start=527
    _globals['_REGISTEREDMODELTAG']._serialized_end=575
    _globals['_MODELPARAM']._serialized_start=577
    _globals['_MODELPARAM']._serialized_end=618
    _globals['_MODELMETRIC']._serialized_start=621
    _globals['_MODELMETRIC']._serialized_end=802
    _globals['_MODELVERSIONTAG']._serialized_start=804
    _globals['_MODELVERSIONTAG']._serialized_end=849
    _globals['_MODELVERSION']._serialized_start=852
    _globals['_MODELVERSION']._serialized_end=1536
    _globals['_DEPLOYMENTJOBCONNECTION']._serialized_start=1539
    _globals['_DEPLOYMENTJOBCONNECTION']._serialized_end=1702
    _globals['_DEPLOYMENTJOBCONNECTION_STATE']._serialized_start=1567
    _globals['_DEPLOYMENTJOBCONNECTION_STATE']._serialized_end=1702
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE']._serialized_start=1705
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE']._serialized_end=2137
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE']._serialized_start=1970
    _globals['_MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE']._serialized_end=2137
    _globals['_TEMPORARYCREDENTIALS']._serialized_start=2140
    _globals['_TEMPORARYCREDENTIALS']._serialized_end=2625
    _globals['_AWSCREDENTIALS']._serialized_start=2627
    _globals['_AWSCREDENTIALS']._serialized_end=2716
    _globals['_AZUREUSERDELEGATIONSAS']._serialized_start=2718
    _globals['_AZUREUSERDELEGATIONSAS']._serialized_end=2761
    _globals['_GCPOAUTHTOKEN']._serialized_start=2763
    _globals['_GCPOAUTHTOKEN']._serialized_end=2799
    _globals['_R2CREDENTIALS']._serialized_start=2801
    _globals['_R2CREDENTIALS']._serialized_end=2889
    _globals['_ENCRYPTIONDETAILS']._serialized_start=2891
    _globals['_ENCRYPTIONDETAILS']._serialized_end=3017
    _globals['_SSEENCRYPTIONDETAILS']._serialized_start=3019
    _globals['_SSEENCRYPTIONDETAILS']._serialized_end=3133
    _globals['_CREATEREGISTEREDMODELREQUEST']._serialized_start=3136
    _globals['_CREATEREGISTEREDMODELREQUEST']._serialized_end=3352
    _globals['_CREATEREGISTEREDMODELRESPONSE']._serialized_start=3354
    _globals['_CREATEREGISTEREDMODELRESPONSE']._serialized_end=3452
    _globals['_UPDATEREGISTEREDMODELREQUEST']._serialized_start=3455
    _globals['_UPDATEREGISTEREDMODELREQUEST']._serialized_end=3631
    _globals['_UPDATEREGISTEREDMODELRESPONSE']._serialized_start=3633
    _globals['_UPDATEREGISTEREDMODELRESPONSE']._serialized_end=3731
    _globals['_DELETEREGISTEREDMODELREQUEST']._serialized_start=3733
    _globals['_DELETEREGISTEREDMODELREQUEST']._serialized_end=3843
    _globals['_DELETEREGISTEREDMODELRESPONSE']._serialized_start=3845
    _globals['_DELETEREGISTEREDMODELRESPONSE']._serialized_end=3876
    _globals['_GETREGISTEREDMODELREQUEST']._serialized_start=3878
    _globals['_GETREGISTEREDMODELREQUEST']._serialized_end=3982
    _globals['_GETREGISTEREDMODELRESPONSE']._serialized_start=3984
    _globals['_GETREGISTEREDMODELRESPONSE']._serialized_end=4079
    _globals['_SEARCHREGISTEREDMODELSREQUEST']._serialized_start=4082
    _globals['_SEARCHREGISTEREDMODELSREQUEST']._serialized_end=4220
    _globals['_SEARCHREGISTEREDMODELSRESPONSE']._serialized_start=4222
    _globals['_SEARCHREGISTEREDMODELSRESPONSE']._serialized_end=4347
    _globals['_DEPENDENCY']._serialized_start=4349
    _globals['_DEPENDENCY']._serialized_end=4429
    _globals['_CREATEMODELVERSIONREQUEST']._serialized_start=4432
    _globals['_CREATEMODELVERSIONREQUEST']._serialized_end=4794
    _globals['_CREATEMODELVERSIONRESPONSE']._serialized_start=4796
    _globals['_CREATEMODELVERSIONRESPONSE']._serialized_end=4885
    _globals['_UPDATEMODELVERSIONREQUEST']._serialized_start=4888
    _globals['_UPDATEMODELVERSIONREQUEST']._serialized_end=5036
    _globals['_UPDATEMODELVERSIONRESPONSE']._serialized_start=5038
    _globals['_UPDATEMODELVERSIONRESPONSE']._serialized_end=5127
    _globals['_DELETEMODELVERSIONREQUEST']._serialized_start=5129
    _globals['_DELETEMODELVERSIONREQUEST']._serialized_end=5256
    _globals['_DELETEMODELVERSIONRESPONSE']._serialized_start=5258
    _globals['_DELETEMODELVERSIONRESPONSE']._serialized_end=5286
    _globals['_GETMODELVERSIONREQUEST']._serialized_start=5288
    _globals['_GETMODELVERSIONREQUEST']._serialized_end=5409
    _globals['_GETMODELVERSIONRESPONSE']._serialized_start=5411
    _globals['_GETMODELVERSIONRESPONSE']._serialized_end=5497
    _globals['_SEARCHMODELVERSIONSREQUEST']._serialized_start=5500
    _globals['_SEARCHMODELVERSIONSREQUEST']._serialized_end=5650
    _globals['_SEARCHMODELVERSIONSRESPONSE']._serialized_start=5652
    _globals['_SEARCHMODELVERSIONSRESPONSE']._serialized_end=5768
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST']._serialized_start=5771
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST']._serialized_end=6014
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSRESPONSE']._serialized_start=6016
    _globals['_GENERATETEMPORARYMODELVERSIONCREDENTIALSRESPONSE']._serialized_end=6133
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST']._serialized_start=6136
    _globals['_GETMODELVERSIONDOWNLOADURIREQUEST']._serialized_end=6279
    _globals['_GETMODELVERSIONDOWNLOADURIRESPONSE']._serialized_start=6281
    _globals['_GETMODELVERSIONDOWNLOADURIRESPONSE']._serialized_end=6339
    _globals['_FINALIZEMODELVERSIONREQUEST']._serialized_start=6342
    _globals['_FINALIZEMODELVERSIONREQUEST']._serialized_end=6473
    _globals['_FINALIZEMODELVERSIONRESPONSE']._serialized_start=6475
    _globals['_FINALIZEMODELVERSIONRESPONSE']._serialized_end=6566
    _globals['_SETREGISTEREDMODELALIASREQUEST']._serialized_start=6569
    _globals['_SETREGISTEREDMODELALIASREQUEST']._serialized_end=6727
    _globals['_SETREGISTEREDMODELALIASRESPONSE']._serialized_start=6729
    _globals['_SETREGISTEREDMODELALIASRESPONSE']._serialized_end=6762
    _globals['_DELETEREGISTEREDMODELALIASREQUEST']._serialized_start=6765
    _globals['_DELETEREGISTEREDMODELALIASREQUEST']._serialized_end=6906
    _globals['_DELETEREGISTEREDMODELALIASRESPONSE']._serialized_start=6908
    _globals['_DELETEREGISTEREDMODELALIASRESPONSE']._serialized_end=6944
    _globals['_SETREGISTEREDMODELTAGREQUEST']._serialized_start=6947
    _globals['_SETREGISTEREDMODELTAGREQUEST']._serialized_end=7091
    _globals['_SETREGISTEREDMODELTAGRESPONSE']._serialized_start=7093
    _globals['_SETREGISTEREDMODELTAGRESPONSE']._serialized_end=7124
    _globals['_DELETEREGISTEREDMODELTAGREQUEST']._serialized_start=7127
    _globals['_DELETEREGISTEREDMODELTAGREQUEST']._serialized_end=7262
    _globals['_DELETEREGISTEREDMODELTAGRESPONSE']._serialized_start=7264
    _globals['_DELETEREGISTEREDMODELTAGRESPONSE']._serialized_end=7298
    _globals['_SETMODELVERSIONTAGREQUEST']._serialized_start=7301
    _globals['_SETMODELVERSIONTAGREQUEST']._serialized_end=7462
    _globals['_SETMODELVERSIONTAGRESPONSE']._serialized_start=7464
    _globals['_SETMODELVERSIONTAGRESPONSE']._serialized_end=7492
    _globals['_DELETEMODELVERSIONTAGREQUEST']._serialized_start=7495
    _globals['_DELETEMODELVERSIONTAGREQUEST']._serialized_end=7647
    _globals['_DELETEMODELVERSIONTAGRESPONSE']._serialized_start=7649
    _globals['_DELETEMODELVERSIONTAGRESPONSE']._serialized_end=7680
    _globals['_GETMODELVERSIONBYALIASREQUEST']._serialized_start=7683
    _globals['_GETMODELVERSIONBYALIASREQUEST']._serialized_end=7816
    _globals['_GETMODELVERSIONBYALIASRESPONSE']._serialized_start=7818
    _globals['_GETMODELVERSIONBYALIASRESPONSE']._serialized_end=7911
    _globals['_ENTITY']._serialized_start=7914
    _globals['_ENTITY']._serialized_end=8083
    _globals['_PIPELINE']._serialized_start=8085
    _globals['_PIPELINE']._serialized_end=8144
    _globals['_JOB']._serialized_start=8146
    _globals['_JOB']._serialized_end=8222
    _globals['_NOTEBOOK']._serialized_start=8224
    _globals['_NOTEBOOK']._serialized_end=8291
    _globals['_TABLE']._serialized_start=8293
    _globals['_TABLE']._serialized_end=8405
    _globals['_SECURABLE']._serialized_start=8407
    _globals['_SECURABLE']._serialized_end=8475
    _globals['_LINEAGE']._serialized_start=8478
    _globals['_LINEAGE']._serialized_end=8610
    _globals['_LINEAGEHEADERINFO']._serialized_start=8612
    _globals['_LINEAGEHEADERINFO']._serialized_end=8732
    _globals['_MODELVERSIONLINEAGEINFO']._serialized_start=8735
    _globals['_MODELVERSIONLINEAGEINFO']._serialized_end=8889
    _globals['_EMITMODELVERSIONLINEAGEREQUEST']._serialized_start=8892
    _globals['_EMITMODELVERSIONLINEAGEREQUEST']._serialized_end=9174
    _globals['_EMITMODELVERSIONLINEAGERESPONSE']._serialized_start=9176
    _globals['_EMITMODELVERSIONLINEAGERESPONSE']._serialized_end=9209
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST']._serialized_start=9212
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST']._serialized_end=9352
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE']._serialized_start=9354
    _globals['_ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE']._serialized_end=9478
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: databricks_uc_registry_messages.proto
  """Generated protocol buffer code."""
  from google.protobuf.internal import enum_type_wrapper
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import databricks_pb2 as databricks__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%databricks_uc_registry_messages.proto\x12\x16mlflow.ucmodelregistry\x1a\x15scalapb/scalapb.proto\x1a\x10\x64\x61tabricks.proto\"\xea\x02\n\x0fRegisteredModel\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x02 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x03 \x01(\x03\x12\x0f\n\x07user_id\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12=\n\x07\x61liases\x18\x06 \x03(\x0b\x32,.mlflow.ucmodelregistry.RegisteredModelAlias\x12\x38\n\x04tags\x18\x07 \x03(\x0b\x32*.mlflow.ucmodelregistry.RegisteredModelTag\x12\x19\n\x11\x64\x65ployment_job_id\x18\x08 \x01(\t\x12S\n\x14\x64\x65ployment_job_state\x18\t \x01(\x0e\x32\x35.mlflow.ucmodelregistry.DeploymentJobConnection.State\"6\n\x14RegisteredModelAlias\x12\r\n\x05\x61lias\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"0\n\x12RegisteredModelTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\")\n\nModelParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xb5\x01\n\x0bModelMetric\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\x12\x0f\n\x04step\x18\x04 \x01(\x03:\x01\x30\x12\x1a\n\x0c\x64\x61taset_name\x18\x05 \x01(\tB\x04\xf0\x86\x19\x03\x12\x1c\n\x0e\x64\x61taset_digest\x18\x06 \x01(\tB\x04\xf0\x86\x19\x03\x12\x16\n\x08model_id\x18\x07 \x01(\tB\x04\xf0\x86\x19\x03\x12\x14\n\x06run_id\x18\x08 \x01(\tB\x04\xf0\x86\x19\x03\"-\n\x0fModelVersionTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xac\x05\n\x0cModelVersion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x03 \x01(\x03\x12\x1e\n\x16last_updated_timestamp\x18\x04 \x01(\x03\x12\x0f\n\x07user_id\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x0e\n\x06source\x18\x07 \x01(\t\x12\x0e\n\x06run_id\x18\x08 \x01(\t\x12\x19\n\x11run_experiment_id\x18\t \x01(\t\x12\x1e\n\x16run_tracking_server_id\x18\n \x01(\t\x12:\n\x06status\x18\x0b \x01(\x0e\x32*.mlflow.ucmodelregistry.ModelVersionStatus\x12\x16\n\x0estatus_message\x18\x0c \x01(\t\x12\x18\n\x10storage_location\x18\r \x01(\t\x12=\n\x07\x61liases\x18\x0e \x03(\x0b\x32,.mlflow.ucmodelregistry.RegisteredModelAlias\x12\x35\n\x04tags\x18\x0f \x03(\x0b\x32\'.mlflow.ucmodelregistry.ModelVersionTag\x12\x10\n\x08model_id\x18\x10 \x01(\t\x12\x38\n\x0cmodel_params\x18\x11 \x03(\x0b\x32\".mlflow.ucmodelregistry.ModelParam\x12:\n\rmodel_metrics\x18\x12 \x03(\x0b\x32#.mlflow.ucmodelregistry.ModelMetric\x12T\n\x14\x64\x65ployment_job_state\x18\x13 \x01(\x0b\x32\x36.mlflow.ucmodelregistry.ModelVersionDeploymentJobState\"\xa3\x01\n\x17\x44\x65ploymentJobConnection\"\x87\x01\n\x05State\x12/\n+DEPLOYMENT_JOB_CONNECTION_STATE_UNSPECIFIED\x10\x00\x12\x0e\n\nNOT_SET_UP\x10\x01\x12\r\n\tCONNECTED\x10\x02\x12\r\n\tNOT_FOUND\x10\x03\x12\x1f\n\x1bREQUIRED_PARAMETERS_CHANGED\x10\x04\"\xb0\x03\n\x1eModelVersionDeploymentJobState\x12\x0e\n\x06job_id\x18\x01 \x01(\t\x12\x0e\n\x06run_id\x18\x02 \x01(\t\x12H\n\tjob_state\x18\x03 \x01(\x0e\x32\x35.mlflow.ucmodelregistry.DeploymentJobConnection.State\x12_\n\trun_state\x18\x04 \x01(\x0e\x32L.mlflow.ucmodelregistry.ModelVersionDeploymentJobState.DeploymentJobRunState\x12\x19\n\x11\x63urrent_task_name\x18\x05 \x01(\t\"\xa7\x01\n\x15\x44\x65ploymentJobRunState\x12(\n$DEPLOYMENT_JOB_RUN_STATE_UNSPECIFIED\x10\x00\x12!\n\x1dNO_VALID_DEPLOYMENT_JOB_FOUND\x10\x01\x12\x0b\n\x07RUNNING\x10\x02\x12\r\n\tSUCCEEDED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x12\x0b\n\x07PENDING\x10\x05\x12\x0c\n\x08\x41PPROVAL\x10\x06\"\xe5\x03\n\x14TemporaryCredentials\x12\x46\n\x14\x61ws_temp_credentials\x18\x02 \x01(\x0b\x32&.mlflow.ucmodelregistry.AwsCredentialsH\x00\x12S\n\x19\x61zure_user_delegation_sas\x18\x03 \x01(\x0b\x32..mlflow.ucmodelregistry.AzureUserDelegationSASH\x00\x12@\n\x0fgcp_oauth_token\x18\x04 \x01(\x0b\x32%.mlflow.ucmodelregistry.GcpOauthTokenH\x00\x12\x44\n\x13r2_temp_credentials\x18\x05 \x01(\x0b\x32%.mlflow.ucmodelregistry.R2CredentialsH\x00\x12\x17\n\x0f\x65xpiration_time\x18\x01 \x01(\x03\x12\x39\n\x0cstorage_mode\x18\x06 \x01(\x0e\x32#.mlflow.ucmodelregistry.StorageMode\x12\x45\n\x12\x65ncryption_details\x18\x07 \x01(\x0b\x32).mlflow.ucmodelregistry.EncryptionDetailsB\r\n\x0b\x63redentials\"Y\n\x0e\x41wsCredentials\x12\x15\n\raccess_key_id\x18\x01 \x01(\t\x12\x19\n\x11secret_access_key\x18\x02 \x01(\t\x12\x15\n\rsession_token\x18\x03 \x01(\t\"+\n\x16\x41zureUserDelegationSAS\x12\x11\n\tsas_token\x18\x01 \x01(\t\"$\n\rGcpOauthToken\x12\x13\n\x0boauth_token\x18\x01 \x01(\t\"X\n\rR2Credentials\x12\x15\n\raccess_key_id\x18\x01 \x01(\t\x12\x19\n\x11secret_access_key\x18\x02 \x01(\t\x12\x15\n\rsession_token\x18\x03 \x01(\t\"~\n\x11\x45ncryptionDetails\x12N\n\x16sse_encryption_details\x18\x01 \x01(\x0b\x32,.mlflow.ucmodelregistry.SseEncryptionDetailsH\x00\x42\x19\n\x17\x65ncryption_details_type\"r\n\x14SseEncryptionDetails\x12\x41\n\talgorithm\x18\x01 \x01(\x0e\x32..mlflow.ucmodelregistry.SseEncryptionAlgorithm\x12\x17\n\x0f\x61ws_kms_key_arn\x18\x02 \x01(\t\"\xd8\x01\n\x1c\x43reateRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x38\n\x04tags\x18\x02 \x03(\x0b\x32*.mlflow.ucmodelregistry.RegisteredModelTag\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x04 \x01(\t::\xe2?7\n5com.databricks.rpc.RPC[CreateRegisteredModelResponse]\"b\n\x1d\x43reateRegisteredModelResponse\x12\x41\n\x10registered_model\x18\x01 \x01(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\"\xb0\x01\n\x1cUpdateRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x10\n\x08new_name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x19\n\x11\x64\x65ployment_job_id\x18\x04 \x01(\t::\xe2?7\n5com.databricks.rpc.RPC[UpdateRegisteredModelResponse]\"b\n\x1dUpdateRegisteredModelResponse\x12\x41\n\x10registered_model\x18\x01 \x01(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\"n\n\x1c\x44\x65leteRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01::\xe2?7\n5com.databricks.rpc.RPC[DeleteRegisteredModelResponse]\"\x1f\n\x1d\x44\x65leteRegisteredModelResponse\"h\n\x19GetRegisteredModelRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01:7\xe2?4\n2com.databricks.rpc.RPC[GetRegisteredModelResponse]\"_\n\x1aGetRegisteredModelResponse\x12\x41\n\x10registered_model\x18\x01 \x01(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\"\x8a\x01\n\x1dSearchRegisteredModelsRequest\x12\x18\n\x0bmax_results\x18\x01 \x01(\x03:\x03\x31\x30\x30\x12\x12\n\npage_token\x18\x02 \x01(\t:;\xe2?8\n6com.databricks.rpc.RPC[SearchRegisteredModelsResponse]\"}\n\x1eSearchRegisteredModelsResponse\x12\x42\n\x11registered_models\x18\x01 \x03(\x0b\x32\'.mlflow.ucmodelregistry.RegisteredModel\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"P\n\nDependency\x12\x34\n\x04type\x18\x01 \x01(\x0e\x32&.mlflow.ucmodelregistry.DependencyType\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xea\x02\n\x19\x43reateModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x14\n\x06source\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x1e\n\x16run_tracking_server_id\x18\x05 \x01(\t\x12\x14\n\x0c\x66\x65\x61ture_deps\x18\x06 \x01(\t\x12\x35\n\x04tags\x18\x07 \x03(\x0b\x32\'.mlflow.ucmodelregistry.ModelVersionTag\x12\x46\n\x1amodel_version_dependencies\x18\x08 \x03(\x0b\x32\".mlflow.ucmodelregistry.Dependency\x12\x10\n\x08model_id\x18\t \x01(\t:7\xe2?4\n2com.databricks.rpc.RPC[CreateModelVersionResponse]\"Y\n\x1a\x43reateModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x94\x01\n\x19UpdateModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t:7\xe2?4\n2com.databricks.rpc.RPC[UpdateModelVersionResponse]\"Y\n\x1aUpdateModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x7f\n\x19\x44\x65leteModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:7\xe2?4\n2com.databricks.rpc.RPC[DeleteModelVersionResponse]\"\x1c\n\x1a\x44\x65leteModelVersionResponse\"y\n\x16GetModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:4\xe2?1\n/com.databricks.rpc.RPC[GetModelVersionResponse]\"V\n\x17GetModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x96\x01\n\x1aSearchModelVersionsRequest\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x1a\n\x0bmax_results\x18\x02 \x01(\x03:\x05\x31\x30\x30\x30\x30\x12\x12\n\npage_token\x18\x03 \x01(\t:8\xe2?5\n3com.databricks.rpc.RPC[SearchModelVersionsResponse]\"t\n\x1bSearchModelVersionsResponse\x12<\n\x0emodel_versions\x18\x01 \x03(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"\xf3\x01\n/GenerateTemporaryModelVersionCredentialsRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x46\n\toperation\x18\x03 \x01(\x0e\x32-.mlflow.ucmodelregistry.ModelVersionOperationB\x04\xf8\x86\x19\x01:M\xe2?J\nHcom.databricks.rpc.RPC[GenerateTemporaryModelVersionCredentialsResponse]\"u\n0GenerateTemporaryModelVersionCredentialsResponse\x12\x41\n\x0b\x63redentials\x18\x01 \x01(\x0b\x32,.mlflow.ucmodelregistry.TemporaryCredentials\"\x8f\x01\n!GetModelVersionDownloadUriRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:?\xe2?<\n:com.databricks.rpc.RPC[GetModelVersionDownloadUriResponse]\":\n\"GetModelVersionDownloadUriResponse\x12\x14\n\x0c\x61rtifact_uri\x18\x01 \x01(\t\"\x83\x01\n\x1b\x46inalizeModelVersionRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:9\xe2?6\n4com.databricks.rpc.RPC[FinalizeModelVersionResponse]\"[\n\x1c\x46inalizeModelVersionResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\x9e\x01\n\x1eSetRegisteredModelAliasRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01:<\xe2?9\n7com.databricks.rpc.RPC[SetRegisteredModelAliasResponse]\"!\n\x1fSetRegisteredModelAliasResponse\"\x8d\x01\n!DeleteRegisteredModelAliasRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:?\xe2?<\n:com.databricks.rpc.RPC[DeleteRegisteredModelAliasResponse]\"$\n\"DeleteRegisteredModelAliasResponse\"\x90\x01\n\x1cSetRegisteredModelTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\r\n\x05value\x18\x03 \x01(\t::\xe2?7\n5com.databricks.rpc.RPC[SetRegisteredModelTagResponse]\"\x1f\n\x1dSetRegisteredModelTagResponse\"\x87\x01\n\x1f\x44\x65leteRegisteredModelTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:=\xe2?:\n8com.databricks.rpc.RPC[DeleteRegisteredModelTagResponse]\"\"\n DeleteRegisteredModelTagResponse\"\xa1\x01\n\x19SetModelVersionTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01\x12\r\n\x05value\x18\x04 \x01(\t:7\xe2?4\n2com.databricks.rpc.RPC[SetModelVersionTagResponse]\"\x1c\n\x1aSetModelVersionTagResponse\"\x98\x01\n\x1c\x44\x65leteModelVersionTagRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12\x11\n\x03key\x18\x03 \x01(\tB\x04\xf8\x86\x19\x01::\xe2?7\n5com.databricks.rpc.RPC[DeleteModelVersionTagResponse]\"\x1f\n\x1d\x44\x65leteModelVersionTagResponse\"\x85\x01\n\x1dGetModelVersionByAliasRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x13\n\x05\x61lias\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01:;\xe2?8\n6com.databricks.rpc.RPC[GetModelVersionByAliasResponse]\"]\n\x1eGetModelVersionByAliasResponse\x12;\n\rmodel_version\x18\x01 \x01(\x0b\x32$.mlflow.ucmodelregistry.ModelVersion\"\xa9\x01\n\x06\x45ntity\x12*\n\x03job\x18\x01 \x01(\x0b\x32\x1b.mlflow.ucmodelregistry.JobH\x00\x12\x34\n\x08notebook\x18\x02 \x01(\x0b\x32 .mlflow.ucmodelregistry.NotebookH\x00\x12\x34\n\x08pipeline\x18\x03 \x01(\x0b\x32 .mlflow.ucmodelregistry.PipelineH\x00\x42\x07\n\x05value\";\n\x08Pipeline\x12\x13\n\x0bpipeline_id\x18\x01 \x01(\t\x12\x1a\n\x12pipeline_update_id\x18\x02 \x01(\t\"L\n\x03Job\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08task_key\x18\x02 \x01(\t\x12\x12\n\njob_run_id\x18\x03 \x01(\t\x12\x13\n\x0btask_run_id\x18\x04 \x01(\t\"C\n\x08Notebook\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\ncommand_id\x18\x02 \x01(\t\x12\x17\n\x0fnotebook_run_id\x18\x03 \x01(\t\"p\n\x05Table\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x35\n\ntable_type\x18\x02 \x01(\x0e\x32!.mlflow.ucmodelregistry.TableType\x12\x10\n\x08location\x18\x03 \x01(\t\x12\x10\n\x08table_id\x18\x04 \x01(\t\"D\n\tSecurable\x12.\n\x05table\x18\x01 \x01(\x0b\x32\x1d.mlflow.ucmodelregistry.TableH\x00\x42\x07\n\x05value\"\x84\x01\n\x07Lineage\x12;\n\x10target_securable\x18\x04 \x01(\x0b\x32!.mlflow.ucmodelregistry.Securable\x12<\n\x11source_securables\x18\x05 \x03(\x0b\x32!.mlflow.ucmodelregistry.Securable\"x\n\x11LineageHeaderInfo\x12\x30\n\x08\x65ntities\x18\x01 \x03(\x0b\x32\x1e.mlflow.ucmodelregistry.Entity\x12\x31\n\x08lineages\x18\x02 \x03(\x0b\x32\x1f.mlflow.ucmodelregistry.Lineage\"\x9a\x01\n\x17ModelVersionLineageInfo\x12\x30\n\x08\x65ntities\x18\x01 \x03(\x0b\x32\x1e.mlflow.ucmodelregistry.Entity\x12M\n\tdirection\x18\x02 \x01(\x0e\x32\x34.mlflow.ucmodelregistry.ModelVersionLineageDirectionB\x04\xf8\x86\x19\x01\"\x9a\x02\n\x1e\x45mitModelVersionLineageRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x12Y\n\x1amodel_version_lineage_info\x18\x03 \x01(\x0b\x32/.mlflow.ucmodelregistry.ModelVersionLineageInfoB\x04\xf8\x86\x19\x01\x12\x34\n\tsecurable\x18\x04 \x01(\x0b\x32!.mlflow.ucmodelregistry.Securable:<\xe2?9\n7com.databricks.rpc.RPC[EmitModelVersionLineageResponse]\"!\n\x1f\x45mitModelVersionLineageResponse\"\x8c\x01\n5IsDatabricksSdkModelsArtifactRepositoryEnabledRequest:S\xe2?P\nNcom.databricks.rpc.RPC[IsDatabricksSdkModelsArtifactRepositoryEnabledResponse]\"|\n6IsDatabricksSdkModelsArtifactRepositoryEnabledResponse\x12\x42\n4is_databricks_sdk_models_artifact_repository_enabled\x18\x01 \x01(\x08\x42\x04\xf8\x86\x19\x01*c\n\x12ModelVersionStatus\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x18\n\x14PENDING_REGISTRATION\x10\x01\x12\x17\n\x13\x46\x41ILED_REGISTRATION\x10\x02\x12\t\n\x05READY\x10\x03*\x8a\x01\n\x15ModelVersionOperation\x12\'\n#MODEL_VERSION_OPERATION_UNSPECIFIED\x10\x00\x12 \n\x1cMODEL_VERSION_OPERATION_READ\x10\x01\x12&\n\"MODEL_VERSION_OPERATION_READ_WRITE\x10\x02*U\n\x0bStorageMode\x12\x1c\n\x18STORAGE_MODE_UNSPECIFIED\x10\x00\x12\x13\n\x0f\x43USTOMER_HOSTED\x10\x01\x12\x13\n\x0f\x44\x45\x46\x41ULT_STORAGE\x10\x02*c\n\x16SseEncryptionAlgorithm\x12(\n$SSE_ENCRYPTION_ALGORITHM_UNSPECIFIED\x10\x00\x12\x0e\n\nAWS_SSE_S3\x10\x01\x12\x0f\n\x0b\x41WS_SSE_KMS\x10\x02*\xbd\x01\n\x0e\x44\x65pendencyType\x12\x1f\n\x1b\x44\x45PENDENCY_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x44\x41TABRICKS_VECTOR_INDEX\x10\x01\x12\x1d\n\x19\x44\x41TABRICKS_MODEL_ENDPOINT\x10\x02\x12\x1a\n\x16\x44\x41TABRICKS_UC_FUNCTION\x10\x03\x12\x1c\n\x18\x44\x41TABRICKS_UC_CONNECTION\x10\x04\x12\x14\n\x10\x44\x41TABRICKS_TABLE\x10\x05*t\n\tTableType\x12\t\n\x05TABLE\x10\x00\x12\x12\n\x0ePERSISTED_VIEW\x10\x01\x12\r\n\tTEMP_VIEW\x10\x02\x12\x15\n\x11MATERIALIZED_VIEW\x10\x03\x12\x18\n\x14STREAMING_LIVE_TABLE\x10\x04\x12\x08\n\x04PATH\x10\x05*<\n\x1cModelVersionLineageDirection\x12\x0c\n\x08UPSTREAM\x10\x00\x12\x0e\n\nDOWNSTREAM\x10\x01\x42\x32\n(com.databricks.api.proto.ucmodelregistry\xa0\x01\x01\xe2?\x02\x10\x01')

  _MODELVERSIONSTATUS = DESCRIPTOR.enum_types_by_name['ModelVersionStatus']
  ModelVersionStatus = enum_type_wrapper.EnumTypeWrapper(_MODELVERSIONSTATUS)
  _MODELVERSIONOPERATION = DESCRIPTOR.enum_types_by_name['ModelVersionOperation']
  ModelVersionOperation = enum_type_wrapper.EnumTypeWrapper(_MODELVERSIONOPERATION)
  _STORAGEMODE = DESCRIPTOR.enum_types_by_name['StorageMode']
  StorageMode = enum_type_wrapper.EnumTypeWrapper(_STORAGEMODE)
  _SSEENCRYPTIONALGORITHM = DESCRIPTOR.enum_types_by_name['SseEncryptionAlgorithm']
  SseEncryptionAlgorithm = enum_type_wrapper.EnumTypeWrapper(_SSEENCRYPTIONALGORITHM)
  _DEPENDENCYTYPE = DESCRIPTOR.enum_types_by_name['DependencyType']
  DependencyType = enum_type_wrapper.EnumTypeWrapper(_DEPENDENCYTYPE)
  _TABLETYPE = DESCRIPTOR.enum_types_by_name['TableType']
  TableType = enum_type_wrapper.EnumTypeWrapper(_TABLETYPE)
  _MODELVERSIONLINEAGEDIRECTION = DESCRIPTOR.enum_types_by_name['ModelVersionLineageDirection']
  ModelVersionLineageDirection = enum_type_wrapper.EnumTypeWrapper(_MODELVERSIONLINEAGEDIRECTION)
  UNSPECIFIED = 0
  PENDING_REGISTRATION = 1
  FAILED_REGISTRATION = 2
  READY = 3
  MODEL_VERSION_OPERATION_UNSPECIFIED = 0
  MODEL_VERSION_OPERATION_READ = 1
  MODEL_VERSION_OPERATION_READ_WRITE = 2
  STORAGE_MODE_UNSPECIFIED = 0
  CUSTOMER_HOSTED = 1
  DEFAULT_STORAGE = 2
  SSE_ENCRYPTION_ALGORITHM_UNSPECIFIED = 0
  AWS_SSE_S3 = 1
  AWS_SSE_KMS = 2
  DEPENDENCY_TYPE_UNSPECIFIED = 0
  DATABRICKS_VECTOR_INDEX = 1
  DATABRICKS_MODEL_ENDPOINT = 2
  DATABRICKS_UC_FUNCTION = 3
  DATABRICKS_UC_CONNECTION = 4
  DATABRICKS_TABLE = 5
  TABLE = 0
  PERSISTED_VIEW = 1
  TEMP_VIEW = 2
  MATERIALIZED_VIEW = 3
  STREAMING_LIVE_TABLE = 4
  PATH = 5
  UPSTREAM = 0
  DOWNSTREAM = 1


  _REGISTEREDMODEL = DESCRIPTOR.message_types_by_name['RegisteredModel']
  _REGISTEREDMODELALIAS = DESCRIPTOR.message_types_by_name['RegisteredModelAlias']
  _REGISTEREDMODELTAG = DESCRIPTOR.message_types_by_name['RegisteredModelTag']
  _MODELPARAM = DESCRIPTOR.message_types_by_name['ModelParam']
  _MODELMETRIC = DESCRIPTOR.message_types_by_name['ModelMetric']
  _MODELVERSIONTAG = DESCRIPTOR.message_types_by_name['ModelVersionTag']
  _MODELVERSION = DESCRIPTOR.message_types_by_name['ModelVersion']
  _DEPLOYMENTJOBCONNECTION = DESCRIPTOR.message_types_by_name['DeploymentJobConnection']
  _MODELVERSIONDEPLOYMENTJOBSTATE = DESCRIPTOR.message_types_by_name['ModelVersionDeploymentJobState']
  _TEMPORARYCREDENTIALS = DESCRIPTOR.message_types_by_name['TemporaryCredentials']
  _AWSCREDENTIALS = DESCRIPTOR.message_types_by_name['AwsCredentials']
  _AZUREUSERDELEGATIONSAS = DESCRIPTOR.message_types_by_name['AzureUserDelegationSAS']
  _GCPOAUTHTOKEN = DESCRIPTOR.message_types_by_name['GcpOauthToken']
  _R2CREDENTIALS = DESCRIPTOR.message_types_by_name['R2Credentials']
  _ENCRYPTIONDETAILS = DESCRIPTOR.message_types_by_name['EncryptionDetails']
  _SSEENCRYPTIONDETAILS = DESCRIPTOR.message_types_by_name['SseEncryptionDetails']
  _CREATEREGISTEREDMODELREQUEST = DESCRIPTOR.message_types_by_name['CreateRegisteredModelRequest']
  _CREATEREGISTEREDMODELRESPONSE = DESCRIPTOR.message_types_by_name['CreateRegisteredModelResponse']
  _UPDATEREGISTEREDMODELREQUEST = DESCRIPTOR.message_types_by_name['UpdateRegisteredModelRequest']
  _UPDATEREGISTEREDMODELRESPONSE = DESCRIPTOR.message_types_by_name['UpdateRegisteredModelResponse']
  _DELETEREGISTEREDMODELREQUEST = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelRequest']
  _DELETEREGISTEREDMODELRESPONSE = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelResponse']
  _GETREGISTEREDMODELREQUEST = DESCRIPTOR.message_types_by_name['GetRegisteredModelRequest']
  _GETREGISTEREDMODELRESPONSE = DESCRIPTOR.message_types_by_name['GetRegisteredModelResponse']
  _SEARCHREGISTEREDMODELSREQUEST = DESCRIPTOR.message_types_by_name['SearchRegisteredModelsRequest']
  _SEARCHREGISTEREDMODELSRESPONSE = DESCRIPTOR.message_types_by_name['SearchRegisteredModelsResponse']
  _DEPENDENCY = DESCRIPTOR.message_types_by_name['Dependency']
  _CREATEMODELVERSIONREQUEST = DESCRIPTOR.message_types_by_name['CreateModelVersionRequest']
  _CREATEMODELVERSIONRESPONSE = DESCRIPTOR.message_types_by_name['CreateModelVersionResponse']
  _UPDATEMODELVERSIONREQUEST = DESCRIPTOR.message_types_by_name['UpdateModelVersionRequest']
  _UPDATEMODELVERSIONRESPONSE = DESCRIPTOR.message_types_by_name['UpdateModelVersionResponse']
  _DELETEMODELVERSIONREQUEST = DESCRIPTOR.message_types_by_name['DeleteModelVersionRequest']
  _DELETEMODELVERSIONRESPONSE = DESCRIPTOR.message_types_by_name['DeleteModelVersionResponse']
  _GETMODELVERSIONREQUEST = DESCRIPTOR.message_types_by_name['GetModelVersionRequest']
  _GETMODELVERSIONRESPONSE = DESCRIPTOR.message_types_by_name['GetModelVersionResponse']
  _SEARCHMODELVERSIONSREQUEST = DESCRIPTOR.message_types_by_name['SearchModelVersionsRequest']
  _SEARCHMODELVERSIONSRESPONSE = DESCRIPTOR.message_types_by_name['SearchModelVersionsResponse']
  _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST = DESCRIPTOR.message_types_by_name['GenerateTemporaryModelVersionCredentialsRequest']
  _GENERATETEMPORARYMODELVERSIONCREDENTIALSRESPONSE = DESCRIPTOR.message_types_by_name['GenerateTemporaryModelVersionCredentialsResponse']
  _GETMODELVERSIONDOWNLOADURIREQUEST = DESCRIPTOR.message_types_by_name['GetModelVersionDownloadUriRequest']
  _GETMODELVERSIONDOWNLOADURIRESPONSE = DESCRIPTOR.message_types_by_name['GetModelVersionDownloadUriResponse']
  _FINALIZEMODELVERSIONREQUEST = DESCRIPTOR.message_types_by_name['FinalizeModelVersionRequest']
  _FINALIZEMODELVERSIONRESPONSE = DESCRIPTOR.message_types_by_name['FinalizeModelVersionResponse']
  _SETREGISTEREDMODELALIASREQUEST = DESCRIPTOR.message_types_by_name['SetRegisteredModelAliasRequest']
  _SETREGISTEREDMODELALIASRESPONSE = DESCRIPTOR.message_types_by_name['SetRegisteredModelAliasResponse']
  _DELETEREGISTEREDMODELALIASREQUEST = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelAliasRequest']
  _DELETEREGISTEREDMODELALIASRESPONSE = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelAliasResponse']
  _SETREGISTEREDMODELTAGREQUEST = DESCRIPTOR.message_types_by_name['SetRegisteredModelTagRequest']
  _SETREGISTEREDMODELTAGRESPONSE = DESCRIPTOR.message_types_by_name['SetRegisteredModelTagResponse']
  _DELETEREGISTEREDMODELTAGREQUEST = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelTagRequest']
  _DELETEREGISTEREDMODELTAGRESPONSE = DESCRIPTOR.message_types_by_name['DeleteRegisteredModelTagResponse']
  _SETMODELVERSIONTAGREQUEST = DESCRIPTOR.message_types_by_name['SetModelVersionTagRequest']
  _SETMODELVERSIONTAGRESPONSE = DESCRIPTOR.message_types_by_name['SetModelVersionTagResponse']
  _DELETEMODELVERSIONTAGREQUEST = DESCRIPTOR.message_types_by_name['DeleteModelVersionTagRequest']
  _DELETEMODELVERSIONTAGRESPONSE = DESCRIPTOR.message_types_by_name['DeleteModelVersionTagResponse']
  _GETMODELVERSIONBYALIASREQUEST = DESCRIPTOR.message_types_by_name['GetModelVersionByAliasRequest']
  _GETMODELVERSIONBYALIASRESPONSE = DESCRIPTOR.message_types_by_name['GetModelVersionByAliasResponse']
  _ENTITY = DESCRIPTOR.message_types_by_name['Entity']
  _PIPELINE = DESCRIPTOR.message_types_by_name['Pipeline']
  _JOB = DESCRIPTOR.message_types_by_name['Job']
  _NOTEBOOK = DESCRIPTOR.message_types_by_name['Notebook']
  _TABLE = DESCRIPTOR.message_types_by_name['Table']
  _SECURABLE = DESCRIPTOR.message_types_by_name['Securable']
  _LINEAGE = DESCRIPTOR.message_types_by_name['Lineage']
  _LINEAGEHEADERINFO = DESCRIPTOR.message_types_by_name['LineageHeaderInfo']
  _MODELVERSIONLINEAGEINFO = DESCRIPTOR.message_types_by_name['ModelVersionLineageInfo']
  _EMITMODELVERSIONLINEAGEREQUEST = DESCRIPTOR.message_types_by_name['EmitModelVersionLineageRequest']
  _EMITMODELVERSIONLINEAGERESPONSE = DESCRIPTOR.message_types_by_name['EmitModelVersionLineageResponse']
  _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST = DESCRIPTOR.message_types_by_name['IsDatabricksSdkModelsArtifactRepositoryEnabledRequest']
  _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE = DESCRIPTOR.message_types_by_name['IsDatabricksSdkModelsArtifactRepositoryEnabledResponse']
  _DEPLOYMENTJOBCONNECTION_STATE = _DEPLOYMENTJOBCONNECTION.enum_types_by_name['State']
  _MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE = _MODELVERSIONDEPLOYMENTJOBSTATE.enum_types_by_name['DeploymentJobRunState']
  RegisteredModel = _reflection.GeneratedProtocolMessageType('RegisteredModel', (_message.Message,), {
    'DESCRIPTOR' : _REGISTEREDMODEL,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.RegisteredModel)
    })
  _sym_db.RegisterMessage(RegisteredModel)

  RegisteredModelAlias = _reflection.GeneratedProtocolMessageType('RegisteredModelAlias', (_message.Message,), {
    'DESCRIPTOR' : _REGISTEREDMODELALIAS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.RegisteredModelAlias)
    })
  _sym_db.RegisterMessage(RegisteredModelAlias)

  RegisteredModelTag = _reflection.GeneratedProtocolMessageType('RegisteredModelTag', (_message.Message,), {
    'DESCRIPTOR' : _REGISTEREDMODELTAG,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.RegisteredModelTag)
    })
  _sym_db.RegisterMessage(RegisteredModelTag)

  ModelParam = _reflection.GeneratedProtocolMessageType('ModelParam', (_message.Message,), {
    'DESCRIPTOR' : _MODELPARAM,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.ModelParam)
    })
  _sym_db.RegisterMessage(ModelParam)

  ModelMetric = _reflection.GeneratedProtocolMessageType('ModelMetric', (_message.Message,), {
    'DESCRIPTOR' : _MODELMETRIC,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.ModelMetric)
    })
  _sym_db.RegisterMessage(ModelMetric)

  ModelVersionTag = _reflection.GeneratedProtocolMessageType('ModelVersionTag', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSIONTAG,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.ModelVersionTag)
    })
  _sym_db.RegisterMessage(ModelVersionTag)

  ModelVersion = _reflection.GeneratedProtocolMessageType('ModelVersion', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSION,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.ModelVersion)
    })
  _sym_db.RegisterMessage(ModelVersion)

  DeploymentJobConnection = _reflection.GeneratedProtocolMessageType('DeploymentJobConnection', (_message.Message,), {
    'DESCRIPTOR' : _DEPLOYMENTJOBCONNECTION,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeploymentJobConnection)
    })
  _sym_db.RegisterMessage(DeploymentJobConnection)

  ModelVersionDeploymentJobState = _reflection.GeneratedProtocolMessageType('ModelVersionDeploymentJobState', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSIONDEPLOYMENTJOBSTATE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.ModelVersionDeploymentJobState)
    })
  _sym_db.RegisterMessage(ModelVersionDeploymentJobState)

  TemporaryCredentials = _reflection.GeneratedProtocolMessageType('TemporaryCredentials', (_message.Message,), {
    'DESCRIPTOR' : _TEMPORARYCREDENTIALS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.TemporaryCredentials)
    })
  _sym_db.RegisterMessage(TemporaryCredentials)

  AwsCredentials = _reflection.GeneratedProtocolMessageType('AwsCredentials', (_message.Message,), {
    'DESCRIPTOR' : _AWSCREDENTIALS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.AwsCredentials)
    })
  _sym_db.RegisterMessage(AwsCredentials)

  AzureUserDelegationSAS = _reflection.GeneratedProtocolMessageType('AzureUserDelegationSAS', (_message.Message,), {
    'DESCRIPTOR' : _AZUREUSERDELEGATIONSAS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.AzureUserDelegationSAS)
    })
  _sym_db.RegisterMessage(AzureUserDelegationSAS)

  GcpOauthToken = _reflection.GeneratedProtocolMessageType('GcpOauthToken', (_message.Message,), {
    'DESCRIPTOR' : _GCPOAUTHTOKEN,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GcpOauthToken)
    })
  _sym_db.RegisterMessage(GcpOauthToken)

  R2Credentials = _reflection.GeneratedProtocolMessageType('R2Credentials', (_message.Message,), {
    'DESCRIPTOR' : _R2CREDENTIALS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.R2Credentials)
    })
  _sym_db.RegisterMessage(R2Credentials)

  EncryptionDetails = _reflection.GeneratedProtocolMessageType('EncryptionDetails', (_message.Message,), {
    'DESCRIPTOR' : _ENCRYPTIONDETAILS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.EncryptionDetails)
    })
  _sym_db.RegisterMessage(EncryptionDetails)

  SseEncryptionDetails = _reflection.GeneratedProtocolMessageType('SseEncryptionDetails', (_message.Message,), {
    'DESCRIPTOR' : _SSEENCRYPTIONDETAILS,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SseEncryptionDetails)
    })
  _sym_db.RegisterMessage(SseEncryptionDetails)

  CreateRegisteredModelRequest = _reflection.GeneratedProtocolMessageType('CreateRegisteredModelRequest', (_message.Message,), {
    'DESCRIPTOR' : _CREATEREGISTEREDMODELREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.CreateRegisteredModelRequest)
    })
  _sym_db.RegisterMessage(CreateRegisteredModelRequest)

  CreateRegisteredModelResponse = _reflection.GeneratedProtocolMessageType('CreateRegisteredModelResponse', (_message.Message,), {
    'DESCRIPTOR' : _CREATEREGISTEREDMODELRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.CreateRegisteredModelResponse)
    })
  _sym_db.RegisterMessage(CreateRegisteredModelResponse)

  UpdateRegisteredModelRequest = _reflection.GeneratedProtocolMessageType('UpdateRegisteredModelRequest', (_message.Message,), {
    'DESCRIPTOR' : _UPDATEREGISTEREDMODELREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.UpdateRegisteredModelRequest)
    })
  _sym_db.RegisterMessage(UpdateRegisteredModelRequest)

  UpdateRegisteredModelResponse = _reflection.GeneratedProtocolMessageType('UpdateRegisteredModelResponse', (_message.Message,), {
    'DESCRIPTOR' : _UPDATEREGISTEREDMODELRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.UpdateRegisteredModelResponse)
    })
  _sym_db.RegisterMessage(UpdateRegisteredModelResponse)

  DeleteRegisteredModelRequest = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEREGISTEREDMODELREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteRegisteredModelRequest)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelRequest)

  DeleteRegisteredModelResponse = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelResponse', (_message.Message,), {
    'DESCRIPTOR' : _DELETEREGISTEREDMODELRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteRegisteredModelResponse)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelResponse)

  GetRegisteredModelRequest = _reflection.GeneratedProtocolMessageType('GetRegisteredModelRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETREGISTEREDMODELREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetRegisteredModelRequest)
    })
  _sym_db.RegisterMessage(GetRegisteredModelRequest)

  GetRegisteredModelResponse = _reflection.GeneratedProtocolMessageType('GetRegisteredModelResponse', (_message.Message,), {
    'DESCRIPTOR' : _GETREGISTEREDMODELRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetRegisteredModelResponse)
    })
  _sym_db.RegisterMessage(GetRegisteredModelResponse)

  SearchRegisteredModelsRequest = _reflection.GeneratedProtocolMessageType('SearchRegisteredModelsRequest', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHREGISTEREDMODELSREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SearchRegisteredModelsRequest)
    })
  _sym_db.RegisterMessage(SearchRegisteredModelsRequest)

  SearchRegisteredModelsResponse = _reflection.GeneratedProtocolMessageType('SearchRegisteredModelsResponse', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHREGISTEREDMODELSRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SearchRegisteredModelsResponse)
    })
  _sym_db.RegisterMessage(SearchRegisteredModelsResponse)

  Dependency = _reflection.GeneratedProtocolMessageType('Dependency', (_message.Message,), {
    'DESCRIPTOR' : _DEPENDENCY,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Dependency)
    })
  _sym_db.RegisterMessage(Dependency)

  CreateModelVersionRequest = _reflection.GeneratedProtocolMessageType('CreateModelVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _CREATEMODELVERSIONREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.CreateModelVersionRequest)
    })
  _sym_db.RegisterMessage(CreateModelVersionRequest)

  CreateModelVersionResponse = _reflection.GeneratedProtocolMessageType('CreateModelVersionResponse', (_message.Message,), {
    'DESCRIPTOR' : _CREATEMODELVERSIONRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.CreateModelVersionResponse)
    })
  _sym_db.RegisterMessage(CreateModelVersionResponse)

  UpdateModelVersionRequest = _reflection.GeneratedProtocolMessageType('UpdateModelVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _UPDATEMODELVERSIONREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.UpdateModelVersionRequest)
    })
  _sym_db.RegisterMessage(UpdateModelVersionRequest)

  UpdateModelVersionResponse = _reflection.GeneratedProtocolMessageType('UpdateModelVersionResponse', (_message.Message,), {
    'DESCRIPTOR' : _UPDATEMODELVERSIONRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.UpdateModelVersionResponse)
    })
  _sym_db.RegisterMessage(UpdateModelVersionResponse)

  DeleteModelVersionRequest = _reflection.GeneratedProtocolMessageType('DeleteModelVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEMODELVERSIONREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteModelVersionRequest)
    })
  _sym_db.RegisterMessage(DeleteModelVersionRequest)

  DeleteModelVersionResponse = _reflection.GeneratedProtocolMessageType('DeleteModelVersionResponse', (_message.Message,), {
    'DESCRIPTOR' : _DELETEMODELVERSIONRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteModelVersionResponse)
    })
  _sym_db.RegisterMessage(DeleteModelVersionResponse)

  GetModelVersionRequest = _reflection.GeneratedProtocolMessageType('GetModelVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELVERSIONREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetModelVersionRequest)
    })
  _sym_db.RegisterMessage(GetModelVersionRequest)

  GetModelVersionResponse = _reflection.GeneratedProtocolMessageType('GetModelVersionResponse', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELVERSIONRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetModelVersionResponse)
    })
  _sym_db.RegisterMessage(GetModelVersionResponse)

  SearchModelVersionsRequest = _reflection.GeneratedProtocolMessageType('SearchModelVersionsRequest', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHMODELVERSIONSREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SearchModelVersionsRequest)
    })
  _sym_db.RegisterMessage(SearchModelVersionsRequest)

  SearchModelVersionsResponse = _reflection.GeneratedProtocolMessageType('SearchModelVersionsResponse', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHMODELVERSIONSRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SearchModelVersionsResponse)
    })
  _sym_db.RegisterMessage(SearchModelVersionsResponse)

  GenerateTemporaryModelVersionCredentialsRequest = _reflection.GeneratedProtocolMessageType('GenerateTemporaryModelVersionCredentialsRequest', (_message.Message,), {
    'DESCRIPTOR' : _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GenerateTemporaryModelVersionCredentialsRequest)
    })
  _sym_db.RegisterMessage(GenerateTemporaryModelVersionCredentialsRequest)

  GenerateTemporaryModelVersionCredentialsResponse = _reflection.GeneratedProtocolMessageType('GenerateTemporaryModelVersionCredentialsResponse', (_message.Message,), {
    'DESCRIPTOR' : _GENERATETEMPORARYMODELVERSIONCREDENTIALSRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GenerateTemporaryModelVersionCredentialsResponse)
    })
  _sym_db.RegisterMessage(GenerateTemporaryModelVersionCredentialsResponse)

  GetModelVersionDownloadUriRequest = _reflection.GeneratedProtocolMessageType('GetModelVersionDownloadUriRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELVERSIONDOWNLOADURIREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetModelVersionDownloadUriRequest)
    })
  _sym_db.RegisterMessage(GetModelVersionDownloadUriRequest)

  GetModelVersionDownloadUriResponse = _reflection.GeneratedProtocolMessageType('GetModelVersionDownloadUriResponse', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELVERSIONDOWNLOADURIRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetModelVersionDownloadUriResponse)
    })
  _sym_db.RegisterMessage(GetModelVersionDownloadUriResponse)

  FinalizeModelVersionRequest = _reflection.GeneratedProtocolMessageType('FinalizeModelVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _FINALIZEMODELVERSIONREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.FinalizeModelVersionRequest)
    })
  _sym_db.RegisterMessage(FinalizeModelVersionRequest)

  FinalizeModelVersionResponse = _reflection.GeneratedProtocolMessageType('FinalizeModelVersionResponse', (_message.Message,), {
    'DESCRIPTOR' : _FINALIZEMODELVERSIONRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.FinalizeModelVersionResponse)
    })
  _sym_db.RegisterMessage(FinalizeModelVersionResponse)

  SetRegisteredModelAliasRequest = _reflection.GeneratedProtocolMessageType('SetRegisteredModelAliasRequest', (_message.Message,), {
    'DESCRIPTOR' : _SETREGISTEREDMODELALIASREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SetRegisteredModelAliasRequest)
    })
  _sym_db.RegisterMessage(SetRegisteredModelAliasRequest)

  SetRegisteredModelAliasResponse = _reflection.GeneratedProtocolMessageType('SetRegisteredModelAliasResponse', (_message.Message,), {
    'DESCRIPTOR' : _SETREGISTEREDMODELALIASRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SetRegisteredModelAliasResponse)
    })
  _sym_db.RegisterMessage(SetRegisteredModelAliasResponse)

  DeleteRegisteredModelAliasRequest = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelAliasRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEREGISTEREDMODELALIASREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteRegisteredModelAliasRequest)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelAliasRequest)

  DeleteRegisteredModelAliasResponse = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelAliasResponse', (_message.Message,), {
    'DESCRIPTOR' : _DELETEREGISTEREDMODELALIASRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteRegisteredModelAliasResponse)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelAliasResponse)

  SetRegisteredModelTagRequest = _reflection.GeneratedProtocolMessageType('SetRegisteredModelTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _SETREGISTEREDMODELTAGREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SetRegisteredModelTagRequest)
    })
  _sym_db.RegisterMessage(SetRegisteredModelTagRequest)

  SetRegisteredModelTagResponse = _reflection.GeneratedProtocolMessageType('SetRegisteredModelTagResponse', (_message.Message,), {
    'DESCRIPTOR' : _SETREGISTEREDMODELTAGRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SetRegisteredModelTagResponse)
    })
  _sym_db.RegisterMessage(SetRegisteredModelTagResponse)

  DeleteRegisteredModelTagRequest = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEREGISTEREDMODELTAGREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteRegisteredModelTagRequest)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelTagRequest)

  DeleteRegisteredModelTagResponse = _reflection.GeneratedProtocolMessageType('DeleteRegisteredModelTagResponse', (_message.Message,), {
    'DESCRIPTOR' : _DELETEREGISTEREDMODELTAGRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteRegisteredModelTagResponse)
    })
  _sym_db.RegisterMessage(DeleteRegisteredModelTagResponse)

  SetModelVersionTagRequest = _reflection.GeneratedProtocolMessageType('SetModelVersionTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _SETMODELVERSIONTAGREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SetModelVersionTagRequest)
    })
  _sym_db.RegisterMessage(SetModelVersionTagRequest)

  SetModelVersionTagResponse = _reflection.GeneratedProtocolMessageType('SetModelVersionTagResponse', (_message.Message,), {
    'DESCRIPTOR' : _SETMODELVERSIONTAGRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.SetModelVersionTagResponse)
    })
  _sym_db.RegisterMessage(SetModelVersionTagResponse)

  DeleteModelVersionTagRequest = _reflection.GeneratedProtocolMessageType('DeleteModelVersionTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEMODELVERSIONTAGREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteModelVersionTagRequest)
    })
  _sym_db.RegisterMessage(DeleteModelVersionTagRequest)

  DeleteModelVersionTagResponse = _reflection.GeneratedProtocolMessageType('DeleteModelVersionTagResponse', (_message.Message,), {
    'DESCRIPTOR' : _DELETEMODELVERSIONTAGRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.DeleteModelVersionTagResponse)
    })
  _sym_db.RegisterMessage(DeleteModelVersionTagResponse)

  GetModelVersionByAliasRequest = _reflection.GeneratedProtocolMessageType('GetModelVersionByAliasRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELVERSIONBYALIASREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetModelVersionByAliasRequest)
    })
  _sym_db.RegisterMessage(GetModelVersionByAliasRequest)

  GetModelVersionByAliasResponse = _reflection.GeneratedProtocolMessageType('GetModelVersionByAliasResponse', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELVERSIONBYALIASRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.GetModelVersionByAliasResponse)
    })
  _sym_db.RegisterMessage(GetModelVersionByAliasResponse)

  Entity = _reflection.GeneratedProtocolMessageType('Entity', (_message.Message,), {
    'DESCRIPTOR' : _ENTITY,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Entity)
    })
  _sym_db.RegisterMessage(Entity)

  Pipeline = _reflection.GeneratedProtocolMessageType('Pipeline', (_message.Message,), {
    'DESCRIPTOR' : _PIPELINE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Pipeline)
    })
  _sym_db.RegisterMessage(Pipeline)

  Job = _reflection.GeneratedProtocolMessageType('Job', (_message.Message,), {
    'DESCRIPTOR' : _JOB,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Job)
    })
  _sym_db.RegisterMessage(Job)

  Notebook = _reflection.GeneratedProtocolMessageType('Notebook', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOK,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Notebook)
    })
  _sym_db.RegisterMessage(Notebook)

  Table = _reflection.GeneratedProtocolMessageType('Table', (_message.Message,), {
    'DESCRIPTOR' : _TABLE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Table)
    })
  _sym_db.RegisterMessage(Table)

  Securable = _reflection.GeneratedProtocolMessageType('Securable', (_message.Message,), {
    'DESCRIPTOR' : _SECURABLE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Securable)
    })
  _sym_db.RegisterMessage(Securable)

  Lineage = _reflection.GeneratedProtocolMessageType('Lineage', (_message.Message,), {
    'DESCRIPTOR' : _LINEAGE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.Lineage)
    })
  _sym_db.RegisterMessage(Lineage)

  LineageHeaderInfo = _reflection.GeneratedProtocolMessageType('LineageHeaderInfo', (_message.Message,), {
    'DESCRIPTOR' : _LINEAGEHEADERINFO,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.LineageHeaderInfo)
    })
  _sym_db.RegisterMessage(LineageHeaderInfo)

  ModelVersionLineageInfo = _reflection.GeneratedProtocolMessageType('ModelVersionLineageInfo', (_message.Message,), {
    'DESCRIPTOR' : _MODELVERSIONLINEAGEINFO,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.ModelVersionLineageInfo)
    })
  _sym_db.RegisterMessage(ModelVersionLineageInfo)

  EmitModelVersionLineageRequest = _reflection.GeneratedProtocolMessageType('EmitModelVersionLineageRequest', (_message.Message,), {
    'DESCRIPTOR' : _EMITMODELVERSIONLINEAGEREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.EmitModelVersionLineageRequest)
    })
  _sym_db.RegisterMessage(EmitModelVersionLineageRequest)

  EmitModelVersionLineageResponse = _reflection.GeneratedProtocolMessageType('EmitModelVersionLineageResponse', (_message.Message,), {
    'DESCRIPTOR' : _EMITMODELVERSIONLINEAGERESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.EmitModelVersionLineageResponse)
    })
  _sym_db.RegisterMessage(EmitModelVersionLineageResponse)

  IsDatabricksSdkModelsArtifactRepositoryEnabledRequest = _reflection.GeneratedProtocolMessageType('IsDatabricksSdkModelsArtifactRepositoryEnabledRequest', (_message.Message,), {
    'DESCRIPTOR' : _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.IsDatabricksSdkModelsArtifactRepositoryEnabledRequest)
    })
  _sym_db.RegisterMessage(IsDatabricksSdkModelsArtifactRepositoryEnabledRequest)

  IsDatabricksSdkModelsArtifactRepositoryEnabledResponse = _reflection.GeneratedProtocolMessageType('IsDatabricksSdkModelsArtifactRepositoryEnabledResponse', (_message.Message,), {
    'DESCRIPTOR' : _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE,
    '__module__' : 'databricks_uc_registry_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.ucmodelregistry.IsDatabricksSdkModelsArtifactRepositoryEnabledResponse)
    })
  _sym_db.RegisterMessage(IsDatabricksSdkModelsArtifactRepositoryEnabledResponse)

  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n(com.databricks.api.proto.ucmodelregistry\240\001\001\342?\002\020\001'
    _MODELMETRIC.fields_by_name['dataset_name']._options = None
    _MODELMETRIC.fields_by_name['dataset_name']._serialized_options = b'\360\206\031\003'
    _MODELMETRIC.fields_by_name['dataset_digest']._options = None
    _MODELMETRIC.fields_by_name['dataset_digest']._serialized_options = b'\360\206\031\003'
    _MODELMETRIC.fields_by_name['model_id']._options = None
    _MODELMETRIC.fields_by_name['model_id']._serialized_options = b'\360\206\031\003'
    _MODELMETRIC.fields_by_name['run_id']._options = None
    _MODELMETRIC.fields_by_name['run_id']._serialized_options = b'\360\206\031\003'
    _CREATEREGISTEREDMODELREQUEST.fields_by_name['name']._options = None
    _CREATEREGISTEREDMODELREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _CREATEREGISTEREDMODELREQUEST._options = None
    _CREATEREGISTEREDMODELREQUEST._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[CreateRegisteredModelResponse]'
    _UPDATEREGISTEREDMODELREQUEST.fields_by_name['name']._options = None
    _UPDATEREGISTEREDMODELREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _UPDATEREGISTEREDMODELREQUEST._options = None
    _UPDATEREGISTEREDMODELREQUEST._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[UpdateRegisteredModelResponse]'
    _DELETEREGISTEREDMODELREQUEST.fields_by_name['name']._options = None
    _DELETEREGISTEREDMODELREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELREQUEST._options = None
    _DELETEREGISTEREDMODELREQUEST._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[DeleteRegisteredModelResponse]'
    _GETREGISTEREDMODELREQUEST.fields_by_name['name']._options = None
    _GETREGISTEREDMODELREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETREGISTEREDMODELREQUEST._options = None
    _GETREGISTEREDMODELREQUEST._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[GetRegisteredModelResponse]'
    _SEARCHREGISTEREDMODELSREQUEST._options = None
    _SEARCHREGISTEREDMODELSREQUEST._serialized_options = b'\342?8\n6com.databricks.rpc.RPC[SearchRegisteredModelsResponse]'
    _CREATEMODELVERSIONREQUEST.fields_by_name['name']._options = None
    _CREATEMODELVERSIONREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _CREATEMODELVERSIONREQUEST.fields_by_name['source']._options = None
    _CREATEMODELVERSIONREQUEST.fields_by_name['source']._serialized_options = b'\370\206\031\001'
    _CREATEMODELVERSIONREQUEST._options = None
    _CREATEMODELVERSIONREQUEST._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[CreateModelVersionResponse]'
    _UPDATEMODELVERSIONREQUEST.fields_by_name['name']._options = None
    _UPDATEMODELVERSIONREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _UPDATEMODELVERSIONREQUEST.fields_by_name['version']._options = None
    _UPDATEMODELVERSIONREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _UPDATEMODELVERSIONREQUEST._options = None
    _UPDATEMODELVERSIONREQUEST._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[UpdateModelVersionResponse]'
    _DELETEMODELVERSIONREQUEST.fields_by_name['name']._options = None
    _DELETEMODELVERSIONREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONREQUEST.fields_by_name['version']._options = None
    _DELETEMODELVERSIONREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONREQUEST._options = None
    _DELETEMODELVERSIONREQUEST._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[DeleteModelVersionResponse]'
    _GETMODELVERSIONREQUEST.fields_by_name['name']._options = None
    _GETMODELVERSIONREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONREQUEST.fields_by_name['version']._options = None
    _GETMODELVERSIONREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONREQUEST._options = None
    _GETMODELVERSIONREQUEST._serialized_options = b'\342?1\n/com.databricks.rpc.RPC[GetModelVersionResponse]'
    _SEARCHMODELVERSIONSREQUEST._options = None
    _SEARCHMODELVERSIONSREQUEST._serialized_options = b'\342?5\n3com.databricks.rpc.RPC[SearchModelVersionsResponse]'
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST.fields_by_name['name']._options = None
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST.fields_by_name['version']._options = None
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST.fields_by_name['operation']._options = None
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST.fields_by_name['operation']._serialized_options = b'\370\206\031\001'
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST._options = None
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST._serialized_options = b'\342?J\nHcom.databricks.rpc.RPC[GenerateTemporaryModelVersionCredentialsResponse]'
    _GETMODELVERSIONDOWNLOADURIREQUEST.fields_by_name['name']._options = None
    _GETMODELVERSIONDOWNLOADURIREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONDOWNLOADURIREQUEST.fields_by_name['version']._options = None
    _GETMODELVERSIONDOWNLOADURIREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONDOWNLOADURIREQUEST._options = None
    _GETMODELVERSIONDOWNLOADURIREQUEST._serialized_options = b'\342?<\n:com.databricks.rpc.RPC[GetModelVersionDownloadUriResponse]'
    _FINALIZEMODELVERSIONREQUEST.fields_by_name['name']._options = None
    _FINALIZEMODELVERSIONREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _FINALIZEMODELVERSIONREQUEST.fields_by_name['version']._options = None
    _FINALIZEMODELVERSIONREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _FINALIZEMODELVERSIONREQUEST._options = None
    _FINALIZEMODELVERSIONREQUEST._serialized_options = b'\342?6\n4com.databricks.rpc.RPC[FinalizeModelVersionResponse]'
    _SETREGISTEREDMODELALIASREQUEST.fields_by_name['name']._options = None
    _SETREGISTEREDMODELALIASREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELALIASREQUEST.fields_by_name['alias']._options = None
    _SETREGISTEREDMODELALIASREQUEST.fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELALIASREQUEST.fields_by_name['version']._options = None
    _SETREGISTEREDMODELALIASREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELALIASREQUEST._options = None
    _SETREGISTEREDMODELALIASREQUEST._serialized_options = b'\342?9\n7com.databricks.rpc.RPC[SetRegisteredModelAliasResponse]'
    _DELETEREGISTEREDMODELALIASREQUEST.fields_by_name['name']._options = None
    _DELETEREGISTEREDMODELALIASREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELALIASREQUEST.fields_by_name['alias']._options = None
    _DELETEREGISTEREDMODELALIASREQUEST.fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELALIASREQUEST._options = None
    _DELETEREGISTEREDMODELALIASREQUEST._serialized_options = b'\342?<\n:com.databricks.rpc.RPC[DeleteRegisteredModelAliasResponse]'
    _SETREGISTEREDMODELTAGREQUEST.fields_by_name['name']._options = None
    _SETREGISTEREDMODELTAGREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELTAGREQUEST.fields_by_name['key']._options = None
    _SETREGISTEREDMODELTAGREQUEST.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _SETREGISTEREDMODELTAGREQUEST._options = None
    _SETREGISTEREDMODELTAGREQUEST._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[SetRegisteredModelTagResponse]'
    _DELETEREGISTEREDMODELTAGREQUEST.fields_by_name['name']._options = None
    _DELETEREGISTEREDMODELTAGREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELTAGREQUEST.fields_by_name['key']._options = None
    _DELETEREGISTEREDMODELTAGREQUEST.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _DELETEREGISTEREDMODELTAGREQUEST._options = None
    _DELETEREGISTEREDMODELTAGREQUEST._serialized_options = b'\342?:\n8com.databricks.rpc.RPC[DeleteRegisteredModelTagResponse]'
    _SETMODELVERSIONTAGREQUEST.fields_by_name['name']._options = None
    _SETMODELVERSIONTAGREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAGREQUEST.fields_by_name['version']._options = None
    _SETMODELVERSIONTAGREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAGREQUEST.fields_by_name['key']._options = None
    _SETMODELVERSIONTAGREQUEST.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _SETMODELVERSIONTAGREQUEST._options = None
    _SETMODELVERSIONTAGREQUEST._serialized_options = b'\342?4\n2com.databricks.rpc.RPC[SetModelVersionTagResponse]'
    _DELETEMODELVERSIONTAGREQUEST.fields_by_name['name']._options = None
    _DELETEMODELVERSIONTAGREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONTAGREQUEST.fields_by_name['version']._options = None
    _DELETEMODELVERSIONTAGREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONTAGREQUEST.fields_by_name['key']._options = None
    _DELETEMODELVERSIONTAGREQUEST.fields_by_name['key']._serialized_options = b'\370\206\031\001'
    _DELETEMODELVERSIONTAGREQUEST._options = None
    _DELETEMODELVERSIONTAGREQUEST._serialized_options = b'\342?7\n5com.databricks.rpc.RPC[DeleteModelVersionTagResponse]'
    _GETMODELVERSIONBYALIASREQUEST.fields_by_name['name']._options = None
    _GETMODELVERSIONBYALIASREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONBYALIASREQUEST.fields_by_name['alias']._options = None
    _GETMODELVERSIONBYALIASREQUEST.fields_by_name['alias']._serialized_options = b'\370\206\031\001'
    _GETMODELVERSIONBYALIASREQUEST._options = None
    _GETMODELVERSIONBYALIASREQUEST._serialized_options = b'\342?8\n6com.databricks.rpc.RPC[GetModelVersionByAliasResponse]'
    _MODELVERSIONLINEAGEINFO.fields_by_name['direction']._options = None
    _MODELVERSIONLINEAGEINFO.fields_by_name['direction']._serialized_options = b'\370\206\031\001'
    _EMITMODELVERSIONLINEAGEREQUEST.fields_by_name['name']._options = None
    _EMITMODELVERSIONLINEAGEREQUEST.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _EMITMODELVERSIONLINEAGEREQUEST.fields_by_name['version']._options = None
    _EMITMODELVERSIONLINEAGEREQUEST.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _EMITMODELVERSIONLINEAGEREQUEST.fields_by_name['model_version_lineage_info']._options = None
    _EMITMODELVERSIONLINEAGEREQUEST.fields_by_name['model_version_lineage_info']._serialized_options = b'\370\206\031\001'
    _EMITMODELVERSIONLINEAGEREQUEST._options = None
    _EMITMODELVERSIONLINEAGEREQUEST._serialized_options = b'\342?9\n7com.databricks.rpc.RPC[EmitModelVersionLineageResponse]'
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST._options = None
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST._serialized_options = b'\342?P\nNcom.databricks.rpc.RPC[IsDatabricksSdkModelsArtifactRepositoryEnabledResponse]'
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE.fields_by_name['is_databricks_sdk_models_artifact_repository_enabled']._options = None
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE.fields_by_name['is_databricks_sdk_models_artifact_repository_enabled']._serialized_options = b'\370\206\031\001'
    _MODELVERSIONSTATUS._serialized_start=9480
    _MODELVERSIONSTATUS._serialized_end=9579
    _MODELVERSIONOPERATION._serialized_start=9582
    _MODELVERSIONOPERATION._serialized_end=9720
    _STORAGEMODE._serialized_start=9722
    _STORAGEMODE._serialized_end=9807
    _SSEENCRYPTIONALGORITHM._serialized_start=9809
    _SSEENCRYPTIONALGORITHM._serialized_end=9908
    _DEPENDENCYTYPE._serialized_start=9911
    _DEPENDENCYTYPE._serialized_end=10100
    _TABLETYPE._serialized_start=10102
    _TABLETYPE._serialized_end=10218
    _MODELVERSIONLINEAGEDIRECTION._serialized_start=10220
    _MODELVERSIONLINEAGEDIRECTION._serialized_end=10280
    _REGISTEREDMODEL._serialized_start=107
    _REGISTEREDMODEL._serialized_end=469
    _REGISTEREDMODELALIAS._serialized_start=471
    _REGISTEREDMODELALIAS._serialized_end=525
    _REGISTEREDMODELTAG._serialized_start=527
    _REGISTEREDMODELTAG._serialized_end=575
    _MODELPARAM._serialized_start=577
    _MODELPARAM._serialized_end=618
    _MODELMETRIC._serialized_start=621
    _MODELMETRIC._serialized_end=802
    _MODELVERSIONTAG._serialized_start=804
    _MODELVERSIONTAG._serialized_end=849
    _MODELVERSION._serialized_start=852
    _MODELVERSION._serialized_end=1536
    _DEPLOYMENTJOBCONNECTION._serialized_start=1539
    _DEPLOYMENTJOBCONNECTION._serialized_end=1702
    _DEPLOYMENTJOBCONNECTION_STATE._serialized_start=1567
    _DEPLOYMENTJOBCONNECTION_STATE._serialized_end=1702
    _MODELVERSIONDEPLOYMENTJOBSTATE._serialized_start=1705
    _MODELVERSIONDEPLOYMENTJOBSTATE._serialized_end=2137
    _MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE._serialized_start=1970
    _MODELVERSIONDEPLOYMENTJOBSTATE_DEPLOYMENTJOBRUNSTATE._serialized_end=2137
    _TEMPORARYCREDENTIALS._serialized_start=2140
    _TEMPORARYCREDENTIALS._serialized_end=2625
    _AWSCREDENTIALS._serialized_start=2627
    _AWSCREDENTIALS._serialized_end=2716
    _AZUREUSERDELEGATIONSAS._serialized_start=2718
    _AZUREUSERDELEGATIONSAS._serialized_end=2761
    _GCPOAUTHTOKEN._serialized_start=2763
    _GCPOAUTHTOKEN._serialized_end=2799
    _R2CREDENTIALS._serialized_start=2801
    _R2CREDENTIALS._serialized_end=2889
    _ENCRYPTIONDETAILS._serialized_start=2891
    _ENCRYPTIONDETAILS._serialized_end=3017
    _SSEENCRYPTIONDETAILS._serialized_start=3019
    _SSEENCRYPTIONDETAILS._serialized_end=3133
    _CREATEREGISTEREDMODELREQUEST._serialized_start=3136
    _CREATEREGISTEREDMODELREQUEST._serialized_end=3352
    _CREATEREGISTEREDMODELRESPONSE._serialized_start=3354
    _CREATEREGISTEREDMODELRESPONSE._serialized_end=3452
    _UPDATEREGISTEREDMODELREQUEST._serialized_start=3455
    _UPDATEREGISTEREDMODELREQUEST._serialized_end=3631
    _UPDATEREGISTEREDMODELRESPONSE._serialized_start=3633
    _UPDATEREGISTEREDMODELRESPONSE._serialized_end=3731
    _DELETEREGISTEREDMODELREQUEST._serialized_start=3733
    _DELETEREGISTEREDMODELREQUEST._serialized_end=3843
    _DELETEREGISTEREDMODELRESPONSE._serialized_start=3845
    _DELETEREGISTEREDMODELRESPONSE._serialized_end=3876
    _GETREGISTEREDMODELREQUEST._serialized_start=3878
    _GETREGISTEREDMODELREQUEST._serialized_end=3982
    _GETREGISTEREDMODELRESPONSE._serialized_start=3984
    _GETREGISTEREDMODELRESPONSE._serialized_end=4079
    _SEARCHREGISTEREDMODELSREQUEST._serialized_start=4082
    _SEARCHREGISTEREDMODELSREQUEST._serialized_end=4220
    _SEARCHREGISTEREDMODELSRESPONSE._serialized_start=4222
    _SEARCHREGISTEREDMODELSRESPONSE._serialized_end=4347
    _DEPENDENCY._serialized_start=4349
    _DEPENDENCY._serialized_end=4429
    _CREATEMODELVERSIONREQUEST._serialized_start=4432
    _CREATEMODELVERSIONREQUEST._serialized_end=4794
    _CREATEMODELVERSIONRESPONSE._serialized_start=4796
    _CREATEMODELVERSIONRESPONSE._serialized_end=4885
    _UPDATEMODELVERSIONREQUEST._serialized_start=4888
    _UPDATEMODELVERSIONREQUEST._serialized_end=5036
    _UPDATEMODELVERSIONRESPONSE._serialized_start=5038
    _UPDATEMODELVERSIONRESPONSE._serialized_end=5127
    _DELETEMODELVERSIONREQUEST._serialized_start=5129
    _DELETEMODELVERSIONREQUEST._serialized_end=5256
    _DELETEMODELVERSIONRESPONSE._serialized_start=5258
    _DELETEMODELVERSIONRESPONSE._serialized_end=5286
    _GETMODELVERSIONREQUEST._serialized_start=5288
    _GETMODELVERSIONREQUEST._serialized_end=5409
    _GETMODELVERSIONRESPONSE._serialized_start=5411
    _GETMODELVERSIONRESPONSE._serialized_end=5497
    _SEARCHMODELVERSIONSREQUEST._serialized_start=5500
    _SEARCHMODELVERSIONSREQUEST._serialized_end=5650
    _SEARCHMODELVERSIONSRESPONSE._serialized_start=5652
    _SEARCHMODELVERSIONSRESPONSE._serialized_end=5768
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST._serialized_start=5771
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSREQUEST._serialized_end=6014
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSRESPONSE._serialized_start=6016
    _GENERATETEMPORARYMODELVERSIONCREDENTIALSRESPONSE._serialized_end=6133
    _GETMODELVERSIONDOWNLOADURIREQUEST._serialized_start=6136
    _GETMODELVERSIONDOWNLOADURIREQUEST._serialized_end=6279
    _GETMODELVERSIONDOWNLOADURIRESPONSE._serialized_start=6281
    _GETMODELVERSIONDOWNLOADURIRESPONSE._serialized_end=6339
    _FINALIZEMODELVERSIONREQUEST._serialized_start=6342
    _FINALIZEMODELVERSIONREQUEST._serialized_end=6473
    _FINALIZEMODELVERSIONRESPONSE._serialized_start=6475
    _FINALIZEMODELVERSIONRESPONSE._serialized_end=6566
    _SETREGISTEREDMODELALIASREQUEST._serialized_start=6569
    _SETREGISTEREDMODELALIASREQUEST._serialized_end=6727
    _SETREGISTEREDMODELALIASRESPONSE._serialized_start=6729
    _SETREGISTEREDMODELALIASRESPONSE._serialized_end=6762
    _DELETEREGISTEREDMODELALIASREQUEST._serialized_start=6765
    _DELETEREGISTEREDMODELALIASREQUEST._serialized_end=6906
    _DELETEREGISTEREDMODELALIASRESPONSE._serialized_start=6908
    _DELETEREGISTEREDMODELALIASRESPONSE._serialized_end=6944
    _SETREGISTEREDMODELTAGREQUEST._serialized_start=6947
    _SETREGISTEREDMODELTAGREQUEST._serialized_end=7091
    _SETREGISTEREDMODELTAGRESPONSE._serialized_start=7093
    _SETREGISTEREDMODELTAGRESPONSE._serialized_end=7124
    _DELETEREGISTEREDMODELTAGREQUEST._serialized_start=7127
    _DELETEREGISTEREDMODELTAGREQUEST._serialized_end=7262
    _DELETEREGISTEREDMODELTAGRESPONSE._serialized_start=7264
    _DELETEREGISTEREDMODELTAGRESPONSE._serialized_end=7298
    _SETMODELVERSIONTAGREQUEST._serialized_start=7301
    _SETMODELVERSIONTAGREQUEST._serialized_end=7462
    _SETMODELVERSIONTAGRESPONSE._serialized_start=7464
    _SETMODELVERSIONTAGRESPONSE._serialized_end=7492
    _DELETEMODELVERSIONTAGREQUEST._serialized_start=7495
    _DELETEMODELVERSIONTAGREQUEST._serialized_end=7647
    _DELETEMODELVERSIONTAGRESPONSE._serialized_start=7649
    _DELETEMODELVERSIONTAGRESPONSE._serialized_end=7680
    _GETMODELVERSIONBYALIASREQUEST._serialized_start=7683
    _GETMODELVERSIONBYALIASREQUEST._serialized_end=7816
    _GETMODELVERSIONBYALIASRESPONSE._serialized_start=7818
    _GETMODELVERSIONBYALIASRESPONSE._serialized_end=7911
    _ENTITY._serialized_start=7914
    _ENTITY._serialized_end=8083
    _PIPELINE._serialized_start=8085
    _PIPELINE._serialized_end=8144
    _JOB._serialized_start=8146
    _JOB._serialized_end=8222
    _NOTEBOOK._serialized_start=8224
    _NOTEBOOK._serialized_end=8291
    _TABLE._serialized_start=8293
    _TABLE._serialized_end=8405
    _SECURABLE._serialized_start=8407
    _SECURABLE._serialized_end=8475
    _LINEAGE._serialized_start=8478
    _LINEAGE._serialized_end=8610
    _LINEAGEHEADERINFO._serialized_start=8612
    _LINEAGEHEADERINFO._serialized_end=8732
    _MODELVERSIONLINEAGEINFO._serialized_start=8735
    _MODELVERSIONLINEAGEINFO._serialized_end=8889
    _EMITMODELVERSIONLINEAGEREQUEST._serialized_start=8892
    _EMITMODELVERSIONLINEAGEREQUEST._serialized_end=9174
    _EMITMODELVERSIONLINEAGERESPONSE._serialized_start=9176
    _EMITMODELVERSIONLINEAGERESPONSE._serialized_end=9209
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST._serialized_start=9212
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDREQUEST._serialized_end=9352
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE._serialized_start=9354
    _ISDATABRICKSSDKMODELSARTIFACTREPOSITORYENABLEDRESPONSE._serialized_end=9478
  # @@protoc_insertion_point(module_scope)

