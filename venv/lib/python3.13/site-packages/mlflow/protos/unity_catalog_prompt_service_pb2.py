
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: unity_catalog_prompt_service.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from . import databricks_pb2 as databricks__pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import unity_catalog_prompt_messages_pb2 as unity_catalog_prompt_messages_pb2
  from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"unity_catalog_prompt_service.proto\x12\x13mlflow.unitycatalog\x1a\x10\x64\x61tabricks.proto\x1a\x15scalapb/scalapb.proto\x1a#unity_catalog_prompt_messages.proto\x1a\x1bgoogle/protobuf/empty.proto2\xfe \n\x19UnityCatalogPromptService\x12\xa9\x01\n\x0c\x43reatePrompt\x12(.mlflow.unitycatalog.CreatePromptRequest\x1a\x1b.mlflow.unitycatalog.Prompt\"R\xf2\x86\x19N\n+\n\x04POST\x12\x1d/mlflow/unity-catalog/prompts\x1a\x04\x08\x02\x10\x00\x10\x03*\x1d(Unity Catalog) Create Prompt\x12\xb1\x01\n\x0cUpdatePrompt\x12(.mlflow.unitycatalog.UpdatePromptRequest\x1a\x1b.mlflow.unitycatalog.Prompt\"Z\xf2\x86\x19V\n3\n\x05PATCH\x12$/mlflow/unity-catalog/prompts/{name}\x1a\x04\x08\x02\x10\x00\x10\x03*\x1d(Unity Catalog) Update Prompt\x12\xad\x01\n\x0c\x44\x65letePrompt\x12(.mlflow.unitycatalog.DeletePromptRequest\x1a\x16.google.protobuf.Empty\"[\xf2\x86\x19W\n4\n\x06\x44\x45LETE\x12$/mlflow/unity-catalog/prompts/{name}\x1a\x04\x08\x02\x10\x00\x10\x03*\x1d(Unity Catalog) Delete Prompt\x12\xa6\x01\n\tGetPrompt\x12%.mlflow.unitycatalog.GetPromptRequest\x1a\x1b.mlflow.unitycatalog.Prompt\"U\xf2\x86\x19Q\n1\n\x03GET\x12$/mlflow/unity-catalog/prompts/{name}\x1a\x04\x08\x02\x10\x00\x10\x03*\x1a(Unity Catalog) Get Prompt\x12\xc2\x01\n\rSearchPrompts\x12).mlflow.unitycatalog.SearchPromptsRequest\x1a*.mlflow.unitycatalog.SearchPromptsResponse\"Z\xf2\x86\x19V\n2\n\x04POST\x12$/mlflow/unity-catalog/prompts/search\x1a\x04\x08\x02\x10\x00\x10\x03*\x1e(Unity Catalog) Search Prompts\x12\xd6\x01\n\x13\x43reatePromptVersion\x12/.mlflow.unitycatalog.CreatePromptVersionRequest\x1a\".mlflow.unitycatalog.PromptVersion\"j\xf2\x86\x19\x66\n;\n\x04POST\x12-/mlflow/unity-catalog/prompts/{name}/versions\x1a\x04\x08\x02\x10\x00\x10\x03*%(Unity Catalog) Create Prompt Version\x12\xe1\x01\n\x13UpdatePromptVersion\x12/.mlflow.unitycatalog.UpdatePromptVersionRequest\x1a\".mlflow.unitycatalog.PromptVersion\"u\xf2\x86\x19q\nF\n\x05PATCH\x12\x37/mlflow/unity-catalog/prompts/{name}/versions/{version}\x1a\x04\x08\x02\x10\x00\x10\x03*%(Unity Catalog) Update Prompt Version\x12\xd6\x01\n\x13\x44\x65letePromptVersion\x12/.mlflow.unitycatalog.DeletePromptVersionRequest\x1a\x16.google.protobuf.Empty\"v\xf2\x86\x19r\nG\n\x06\x44\x45LETE\x12\x37/mlflow/unity-catalog/prompts/{name}/versions/{version}\x1a\x04\x08\x02\x10\x00\x10\x03*%(Unity Catalog) Delete Prompt Version\x12\xd6\x01\n\x10GetPromptVersion\x12,.mlflow.unitycatalog.GetPromptVersionRequest\x1a\".mlflow.unitycatalog.PromptVersion\"p\xf2\x86\x19l\nD\n\x03GET\x12\x37/mlflow/unity-catalog/prompts/{name}/versions/{version}\x1a\x04\x08\x02\x10\x00\x10\x03*\"(Unity Catalog) Get Prompt Version\x12\xfa\x01\n\x14SearchPromptVersions\x12\x30.mlflow.unitycatalog.SearchPromptVersionsRequest\x1a\x31.mlflow.unitycatalog.SearchPromptVersionsResponse\"}\xf2\x86\x19y\nB\n\x04POST\x12\x34/mlflow/unity-catalog/prompts/{name}/versions/search\x1a\x04\x08\x02\x10\x00\x10\x03\x18\xe8\x07\x18\xec\x07\x18\xba\x17\x18\x01*&(Unity Catalog) Search Prompt Versions\x12\xc2\x01\n\x0eSetPromptAlias\x12*.mlflow.unitycatalog.SetPromptAliasRequest\x1a\x16.google.protobuf.Empty\"l\xf2\x86\x19h\nB\n\x04POST\x12\x34/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\x1a\x04\x08\x02\x10\x00\x10\x03* (Unity Catalog) Set Prompt Alias\x12\xcd\x01\n\x11\x44\x65letePromptAlias\x12-.mlflow.unitycatalog.DeletePromptAliasRequest\x1a\x16.google.protobuf.Empty\"q\xf2\x86\x19m\nD\n\x06\x44\x45LETE\x12\x34/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\x1a\x04\x08\x02\x10\x00\x10\x03*#(Unity Catalog) Delete Prompt Alias\x12\xf5\x01\n\x17GetPromptVersionByAlias\x12\x33.mlflow.unitycatalog.GetPromptVersionByAliasRequest\x1a\".mlflow.unitycatalog.PromptVersion\"\x80\x01\xf2\x86\x19|\nK\n\x03GET\x12>/mlflow/unity-catalog/prompts/{name}/versions/by-alias/{alias}\x1a\x04\x08\x02\x10\x00\x10\x03*+(Unity Catalog) Get Prompt Version By Alias\x12\xb1\x01\n\x0cSetPromptTag\x12(.mlflow.unitycatalog.SetPromptTagRequest\x1a\x16.google.protobuf.Empty\"_\xf2\x86\x19[\n7\n\x04POST\x12)/mlflow/unity-catalog/prompts/{name}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*\x1e(Unity Catalog) Set Prompt Tag\x12\xc2\x01\n\x0f\x44\x65letePromptTag\x12+.mlflow.unitycatalog.DeletePromptTagRequest\x1a\x16.google.protobuf.Empty\"j\xf2\x86\x19\x66\n?\n\x06\x44\x45LETE\x12//mlflow/unity-catalog/prompts/{name}/tags/{key}\x1a\x04\x08\x02\x10\x00\x10\x03*!(Unity Catalog) Delete Prompt Tag\x12\xda\x01\n\x13SetPromptVersionTag\x12/.mlflow.unitycatalog.SetPromptVersionTagRequest\x1a\x16.google.protobuf.Empty\"z\xf2\x86\x19v\nJ\n\x04POST\x12</mlflow/unity-catalog/prompts/{name}/versions/{version}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*&(Unity Catalog) Set Prompt Version Tag\x12\xed\x01\n\x16\x44\x65letePromptVersionTag\x12\x32.mlflow.unitycatalog.DeletePromptVersionTagRequest\x1a\x16.google.protobuf.Empty\"\x86\x01\xf2\x86\x19\x81\x01\nR\n\x06\x44\x45LETE\x12\x42/mlflow/unity-catalog/prompts/{name}/versions/{version}/tags/{key}\x1a\x04\x08\x02\x10\x00\x10\x03*)(Unity Catalog) Delete Prompt Version Tag\x12\xe9\x01\n\x1aLinkPromptVersionsToModels\x12\x36.mlflow.unitycatalog.LinkPromptVersionsToModelsRequest\x1a\x16.google.protobuf.Empty\"{\xf2\x86\x19w\nC\n\x04POST\x12\x35/mlflow/unity-catalog/prompt-versions/links-to-models\x1a\x04\x08\x02\x10\x00\x10\x03*.(Unity Catalog) Link Prompt Versions to Models\x12\xd3\x01\n\x13LinkPromptsToTraces\x12/.mlflow.unitycatalog.LinkPromptsToTracesRequest\x1a\x16.google.protobuf.Empty\"s\xf2\x86\x19o\nC\n\x04POST\x12\x35/mlflow/unity-catalog/prompt-versions/links-to-traces\x1a\x04\x08\x02\x10\x00\x10\x03*&(Unity Catalog) Link Prompts to Traces\x12\xe1\x01\n\x18LinkPromptVersionsToRuns\x12\x34.mlflow.unitycatalog.LinkPromptVersionsToRunsRequest\x1a\x16.google.protobuf.Empty\"w\xf2\x86\x19s\nA\n\x04POST\x12\x33/mlflow/unity-catalog/prompt-versions/links-to-runs\x1a\x04\x08\x02\x10\x00\x10\x03*,(Unity Catalog) Link Prompt Versions to RunsB4\n\'com.databricks.api.proto.managedcatalog\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'unity_catalog_prompt_service_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\n\'com.databricks.api.proto.managedcatalog\220\001\001\240\001\001\342?\002\020\001'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['CreatePrompt']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['CreatePrompt']._serialized_options = b'\362\206\031N\n+\n\004POST\022\035/mlflow/unity-catalog/prompts\032\004\010\002\020\000\020\003*\035(Unity Catalog) Create Prompt'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['UpdatePrompt']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['UpdatePrompt']._serialized_options = b'\362\206\031V\n3\n\005PATCH\022$/mlflow/unity-catalog/prompts/{name}\032\004\010\002\020\000\020\003*\035(Unity Catalog) Update Prompt'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePrompt']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePrompt']._serialized_options = b'\362\206\031W\n4\n\006DELETE\022$/mlflow/unity-catalog/prompts/{name}\032\004\010\002\020\000\020\003*\035(Unity Catalog) Delete Prompt'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['GetPrompt']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['GetPrompt']._serialized_options = b'\362\206\031Q\n1\n\003GET\022$/mlflow/unity-catalog/prompts/{name}\032\004\010\002\020\000\020\003*\032(Unity Catalog) Get Prompt'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SearchPrompts']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SearchPrompts']._serialized_options = b'\362\206\031V\n2\n\004POST\022$/mlflow/unity-catalog/prompts/search\032\004\010\002\020\000\020\003*\036(Unity Catalog) Search Prompts'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['CreatePromptVersion']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['CreatePromptVersion']._serialized_options = b'\362\206\031f\n;\n\004POST\022-/mlflow/unity-catalog/prompts/{name}/versions\032\004\010\002\020\000\020\003*%(Unity Catalog) Create Prompt Version'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['UpdatePromptVersion']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['UpdatePromptVersion']._serialized_options = b'\362\206\031q\nF\n\005PATCH\0227/mlflow/unity-catalog/prompts/{name}/versions/{version}\032\004\010\002\020\000\020\003*%(Unity Catalog) Update Prompt Version'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptVersion']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptVersion']._serialized_options = b'\362\206\031r\nG\n\006DELETE\0227/mlflow/unity-catalog/prompts/{name}/versions/{version}\032\004\010\002\020\000\020\003*%(Unity Catalog) Delete Prompt Version'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['GetPromptVersion']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['GetPromptVersion']._serialized_options = b'\362\206\031l\nD\n\003GET\0227/mlflow/unity-catalog/prompts/{name}/versions/{version}\032\004\010\002\020\000\020\003*\"(Unity Catalog) Get Prompt Version'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SearchPromptVersions']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SearchPromptVersions']._serialized_options = b'\362\206\031y\nB\n\004POST\0224/mlflow/unity-catalog/prompts/{name}/versions/search\032\004\010\002\020\000\020\003\030\350\007\030\354\007\030\272\027\030\001*&(Unity Catalog) Search Prompt Versions'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SetPromptAlias']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SetPromptAlias']._serialized_options = b'\362\206\031h\nB\n\004POST\0224/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\032\004\010\002\020\000\020\003* (Unity Catalog) Set Prompt Alias'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptAlias']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptAlias']._serialized_options = b'\362\206\031m\nD\n\006DELETE\0224/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\032\004\010\002\020\000\020\003*#(Unity Catalog) Delete Prompt Alias'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['GetPromptVersionByAlias']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['GetPromptVersionByAlias']._serialized_options = b'\362\206\031|\nK\n\003GET\022>/mlflow/unity-catalog/prompts/{name}/versions/by-alias/{alias}\032\004\010\002\020\000\020\003*+(Unity Catalog) Get Prompt Version By Alias'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SetPromptTag']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SetPromptTag']._serialized_options = b'\362\206\031[\n7\n\004POST\022)/mlflow/unity-catalog/prompts/{name}/tags\032\004\010\002\020\000\020\003*\036(Unity Catalog) Set Prompt Tag'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptTag']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptTag']._serialized_options = b'\362\206\031f\n?\n\006DELETE\022//mlflow/unity-catalog/prompts/{name}/tags/{key}\032\004\010\002\020\000\020\003*!(Unity Catalog) Delete Prompt Tag'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SetPromptVersionTag']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['SetPromptVersionTag']._serialized_options = b'\362\206\031v\nJ\n\004POST\022</mlflow/unity-catalog/prompts/{name}/versions/{version}/tags\032\004\010\002\020\000\020\003*&(Unity Catalog) Set Prompt Version Tag'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptVersionTag']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['DeletePromptVersionTag']._serialized_options = b'\362\206\031\201\001\nR\n\006DELETE\022B/mlflow/unity-catalog/prompts/{name}/versions/{version}/tags/{key}\032\004\010\002\020\000\020\003*)(Unity Catalog) Delete Prompt Version Tag'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['LinkPromptVersionsToModels']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['LinkPromptVersionsToModels']._serialized_options = b'\362\206\031w\nC\n\004POST\0225/mlflow/unity-catalog/prompt-versions/links-to-models\032\004\010\002\020\000\020\003*.(Unity Catalog) Link Prompt Versions to Models'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['LinkPromptsToTraces']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['LinkPromptsToTraces']._serialized_options = b'\362\206\031o\nC\n\004POST\0225/mlflow/unity-catalog/prompt-versions/links-to-traces\032\004\010\002\020\000\020\003*&(Unity Catalog) Link Prompts to Traces'
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['LinkPromptVersionsToRuns']._loaded_options = None
    _globals['_UNITYCATALOGPROMPTSERVICE'].methods_by_name['LinkPromptVersionsToRuns']._serialized_options = b'\362\206\031s\nA\n\004POST\0223/mlflow/unity-catalog/prompt-versions/links-to-runs\032\004\010\002\020\000\020\003*,(Unity Catalog) Link Prompt Versions to Runs'
    _globals['_UNITYCATALOGPROMPTSERVICE']._serialized_start=167
    _globals['_UNITYCATALOGPROMPTSERVICE']._serialized_end=4389
  _builder.BuildServices(DESCRIPTOR, 'unity_catalog_prompt_service_pb2', _globals)
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: unity_catalog_prompt_service.proto
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf import service as _service
  from google.protobuf import service_reflection
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from . import databricks_pb2 as databricks__pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2
  from . import unity_catalog_prompt_messages_pb2 as unity_catalog_prompt_messages_pb2
  from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"unity_catalog_prompt_service.proto\x12\x13mlflow.unitycatalog\x1a\x10\x64\x61tabricks.proto\x1a\x15scalapb/scalapb.proto\x1a#unity_catalog_prompt_messages.proto\x1a\x1bgoogle/protobuf/empty.proto2\xfe \n\x19UnityCatalogPromptService\x12\xa9\x01\n\x0c\x43reatePrompt\x12(.mlflow.unitycatalog.CreatePromptRequest\x1a\x1b.mlflow.unitycatalog.Prompt\"R\xf2\x86\x19N\n+\n\x04POST\x12\x1d/mlflow/unity-catalog/prompts\x1a\x04\x08\x02\x10\x00\x10\x03*\x1d(Unity Catalog) Create Prompt\x12\xb1\x01\n\x0cUpdatePrompt\x12(.mlflow.unitycatalog.UpdatePromptRequest\x1a\x1b.mlflow.unitycatalog.Prompt\"Z\xf2\x86\x19V\n3\n\x05PATCH\x12$/mlflow/unity-catalog/prompts/{name}\x1a\x04\x08\x02\x10\x00\x10\x03*\x1d(Unity Catalog) Update Prompt\x12\xad\x01\n\x0c\x44\x65letePrompt\x12(.mlflow.unitycatalog.DeletePromptRequest\x1a\x16.google.protobuf.Empty\"[\xf2\x86\x19W\n4\n\x06\x44\x45LETE\x12$/mlflow/unity-catalog/prompts/{name}\x1a\x04\x08\x02\x10\x00\x10\x03*\x1d(Unity Catalog) Delete Prompt\x12\xa6\x01\n\tGetPrompt\x12%.mlflow.unitycatalog.GetPromptRequest\x1a\x1b.mlflow.unitycatalog.Prompt\"U\xf2\x86\x19Q\n1\n\x03GET\x12$/mlflow/unity-catalog/prompts/{name}\x1a\x04\x08\x02\x10\x00\x10\x03*\x1a(Unity Catalog) Get Prompt\x12\xc2\x01\n\rSearchPrompts\x12).mlflow.unitycatalog.SearchPromptsRequest\x1a*.mlflow.unitycatalog.SearchPromptsResponse\"Z\xf2\x86\x19V\n2\n\x04POST\x12$/mlflow/unity-catalog/prompts/search\x1a\x04\x08\x02\x10\x00\x10\x03*\x1e(Unity Catalog) Search Prompts\x12\xd6\x01\n\x13\x43reatePromptVersion\x12/.mlflow.unitycatalog.CreatePromptVersionRequest\x1a\".mlflow.unitycatalog.PromptVersion\"j\xf2\x86\x19\x66\n;\n\x04POST\x12-/mlflow/unity-catalog/prompts/{name}/versions\x1a\x04\x08\x02\x10\x00\x10\x03*%(Unity Catalog) Create Prompt Version\x12\xe1\x01\n\x13UpdatePromptVersion\x12/.mlflow.unitycatalog.UpdatePromptVersionRequest\x1a\".mlflow.unitycatalog.PromptVersion\"u\xf2\x86\x19q\nF\n\x05PATCH\x12\x37/mlflow/unity-catalog/prompts/{name}/versions/{version}\x1a\x04\x08\x02\x10\x00\x10\x03*%(Unity Catalog) Update Prompt Version\x12\xd6\x01\n\x13\x44\x65letePromptVersion\x12/.mlflow.unitycatalog.DeletePromptVersionRequest\x1a\x16.google.protobuf.Empty\"v\xf2\x86\x19r\nG\n\x06\x44\x45LETE\x12\x37/mlflow/unity-catalog/prompts/{name}/versions/{version}\x1a\x04\x08\x02\x10\x00\x10\x03*%(Unity Catalog) Delete Prompt Version\x12\xd6\x01\n\x10GetPromptVersion\x12,.mlflow.unitycatalog.GetPromptVersionRequest\x1a\".mlflow.unitycatalog.PromptVersion\"p\xf2\x86\x19l\nD\n\x03GET\x12\x37/mlflow/unity-catalog/prompts/{name}/versions/{version}\x1a\x04\x08\x02\x10\x00\x10\x03*\"(Unity Catalog) Get Prompt Version\x12\xfa\x01\n\x14SearchPromptVersions\x12\x30.mlflow.unitycatalog.SearchPromptVersionsRequest\x1a\x31.mlflow.unitycatalog.SearchPromptVersionsResponse\"}\xf2\x86\x19y\nB\n\x04POST\x12\x34/mlflow/unity-catalog/prompts/{name}/versions/search\x1a\x04\x08\x02\x10\x00\x10\x03\x18\xe8\x07\x18\xec\x07\x18\xba\x17\x18\x01*&(Unity Catalog) Search Prompt Versions\x12\xc2\x01\n\x0eSetPromptAlias\x12*.mlflow.unitycatalog.SetPromptAliasRequest\x1a\x16.google.protobuf.Empty\"l\xf2\x86\x19h\nB\n\x04POST\x12\x34/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\x1a\x04\x08\x02\x10\x00\x10\x03* (Unity Catalog) Set Prompt Alias\x12\xcd\x01\n\x11\x44\x65letePromptAlias\x12-.mlflow.unitycatalog.DeletePromptAliasRequest\x1a\x16.google.protobuf.Empty\"q\xf2\x86\x19m\nD\n\x06\x44\x45LETE\x12\x34/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\x1a\x04\x08\x02\x10\x00\x10\x03*#(Unity Catalog) Delete Prompt Alias\x12\xf5\x01\n\x17GetPromptVersionByAlias\x12\x33.mlflow.unitycatalog.GetPromptVersionByAliasRequest\x1a\".mlflow.unitycatalog.PromptVersion\"\x80\x01\xf2\x86\x19|\nK\n\x03GET\x12>/mlflow/unity-catalog/prompts/{name}/versions/by-alias/{alias}\x1a\x04\x08\x02\x10\x00\x10\x03*+(Unity Catalog) Get Prompt Version By Alias\x12\xb1\x01\n\x0cSetPromptTag\x12(.mlflow.unitycatalog.SetPromptTagRequest\x1a\x16.google.protobuf.Empty\"_\xf2\x86\x19[\n7\n\x04POST\x12)/mlflow/unity-catalog/prompts/{name}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*\x1e(Unity Catalog) Set Prompt Tag\x12\xc2\x01\n\x0f\x44\x65letePromptTag\x12+.mlflow.unitycatalog.DeletePromptTagRequest\x1a\x16.google.protobuf.Empty\"j\xf2\x86\x19\x66\n?\n\x06\x44\x45LETE\x12//mlflow/unity-catalog/prompts/{name}/tags/{key}\x1a\x04\x08\x02\x10\x00\x10\x03*!(Unity Catalog) Delete Prompt Tag\x12\xda\x01\n\x13SetPromptVersionTag\x12/.mlflow.unitycatalog.SetPromptVersionTagRequest\x1a\x16.google.protobuf.Empty\"z\xf2\x86\x19v\nJ\n\x04POST\x12</mlflow/unity-catalog/prompts/{name}/versions/{version}/tags\x1a\x04\x08\x02\x10\x00\x10\x03*&(Unity Catalog) Set Prompt Version Tag\x12\xed\x01\n\x16\x44\x65letePromptVersionTag\x12\x32.mlflow.unitycatalog.DeletePromptVersionTagRequest\x1a\x16.google.protobuf.Empty\"\x86\x01\xf2\x86\x19\x81\x01\nR\n\x06\x44\x45LETE\x12\x42/mlflow/unity-catalog/prompts/{name}/versions/{version}/tags/{key}\x1a\x04\x08\x02\x10\x00\x10\x03*)(Unity Catalog) Delete Prompt Version Tag\x12\xe9\x01\n\x1aLinkPromptVersionsToModels\x12\x36.mlflow.unitycatalog.LinkPromptVersionsToModelsRequest\x1a\x16.google.protobuf.Empty\"{\xf2\x86\x19w\nC\n\x04POST\x12\x35/mlflow/unity-catalog/prompt-versions/links-to-models\x1a\x04\x08\x02\x10\x00\x10\x03*.(Unity Catalog) Link Prompt Versions to Models\x12\xd3\x01\n\x13LinkPromptsToTraces\x12/.mlflow.unitycatalog.LinkPromptsToTracesRequest\x1a\x16.google.protobuf.Empty\"s\xf2\x86\x19o\nC\n\x04POST\x12\x35/mlflow/unity-catalog/prompt-versions/links-to-traces\x1a\x04\x08\x02\x10\x00\x10\x03*&(Unity Catalog) Link Prompts to Traces\x12\xe1\x01\n\x18LinkPromptVersionsToRuns\x12\x34.mlflow.unitycatalog.LinkPromptVersionsToRunsRequest\x1a\x16.google.protobuf.Empty\"w\xf2\x86\x19s\nA\n\x04POST\x12\x33/mlflow/unity-catalog/prompt-versions/links-to-runs\x1a\x04\x08\x02\x10\x00\x10\x03*,(Unity Catalog) Link Prompt Versions to RunsB4\n\'com.databricks.api.proto.managedcatalog\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')



  _UNITYCATALOGPROMPTSERVICE = DESCRIPTOR.services_by_name['UnityCatalogPromptService']
  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n\'com.databricks.api.proto.managedcatalog\220\001\001\240\001\001\342?\002\020\001'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['CreatePrompt']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['CreatePrompt']._serialized_options = b'\362\206\031N\n+\n\004POST\022\035/mlflow/unity-catalog/prompts\032\004\010\002\020\000\020\003*\035(Unity Catalog) Create Prompt'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['UpdatePrompt']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['UpdatePrompt']._serialized_options = b'\362\206\031V\n3\n\005PATCH\022$/mlflow/unity-catalog/prompts/{name}\032\004\010\002\020\000\020\003*\035(Unity Catalog) Update Prompt'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePrompt']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePrompt']._serialized_options = b'\362\206\031W\n4\n\006DELETE\022$/mlflow/unity-catalog/prompts/{name}\032\004\010\002\020\000\020\003*\035(Unity Catalog) Delete Prompt'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['GetPrompt']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['GetPrompt']._serialized_options = b'\362\206\031Q\n1\n\003GET\022$/mlflow/unity-catalog/prompts/{name}\032\004\010\002\020\000\020\003*\032(Unity Catalog) Get Prompt'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SearchPrompts']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SearchPrompts']._serialized_options = b'\362\206\031V\n2\n\004POST\022$/mlflow/unity-catalog/prompts/search\032\004\010\002\020\000\020\003*\036(Unity Catalog) Search Prompts'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['CreatePromptVersion']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['CreatePromptVersion']._serialized_options = b'\362\206\031f\n;\n\004POST\022-/mlflow/unity-catalog/prompts/{name}/versions\032\004\010\002\020\000\020\003*%(Unity Catalog) Create Prompt Version'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['UpdatePromptVersion']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['UpdatePromptVersion']._serialized_options = b'\362\206\031q\nF\n\005PATCH\0227/mlflow/unity-catalog/prompts/{name}/versions/{version}\032\004\010\002\020\000\020\003*%(Unity Catalog) Update Prompt Version'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptVersion']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptVersion']._serialized_options = b'\362\206\031r\nG\n\006DELETE\0227/mlflow/unity-catalog/prompts/{name}/versions/{version}\032\004\010\002\020\000\020\003*%(Unity Catalog) Delete Prompt Version'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['GetPromptVersion']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['GetPromptVersion']._serialized_options = b'\362\206\031l\nD\n\003GET\0227/mlflow/unity-catalog/prompts/{name}/versions/{version}\032\004\010\002\020\000\020\003*\"(Unity Catalog) Get Prompt Version'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SearchPromptVersions']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SearchPromptVersions']._serialized_options = b'\362\206\031y\nB\n\004POST\0224/mlflow/unity-catalog/prompts/{name}/versions/search\032\004\010\002\020\000\020\003\030\350\007\030\354\007\030\272\027\030\001*&(Unity Catalog) Search Prompt Versions'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SetPromptAlias']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SetPromptAlias']._serialized_options = b'\362\206\031h\nB\n\004POST\0224/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\032\004\010\002\020\000\020\003* (Unity Catalog) Set Prompt Alias'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptAlias']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptAlias']._serialized_options = b'\362\206\031m\nD\n\006DELETE\0224/mlflow/unity-catalog/prompts/{name}/aliases/{alias}\032\004\010\002\020\000\020\003*#(Unity Catalog) Delete Prompt Alias'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['GetPromptVersionByAlias']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['GetPromptVersionByAlias']._serialized_options = b'\362\206\031|\nK\n\003GET\022>/mlflow/unity-catalog/prompts/{name}/versions/by-alias/{alias}\032\004\010\002\020\000\020\003*+(Unity Catalog) Get Prompt Version By Alias'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SetPromptTag']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SetPromptTag']._serialized_options = b'\362\206\031[\n7\n\004POST\022)/mlflow/unity-catalog/prompts/{name}/tags\032\004\010\002\020\000\020\003*\036(Unity Catalog) Set Prompt Tag'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptTag']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptTag']._serialized_options = b'\362\206\031f\n?\n\006DELETE\022//mlflow/unity-catalog/prompts/{name}/tags/{key}\032\004\010\002\020\000\020\003*!(Unity Catalog) Delete Prompt Tag'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SetPromptVersionTag']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['SetPromptVersionTag']._serialized_options = b'\362\206\031v\nJ\n\004POST\022</mlflow/unity-catalog/prompts/{name}/versions/{version}/tags\032\004\010\002\020\000\020\003*&(Unity Catalog) Set Prompt Version Tag'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptVersionTag']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['DeletePromptVersionTag']._serialized_options = b'\362\206\031\201\001\nR\n\006DELETE\022B/mlflow/unity-catalog/prompts/{name}/versions/{version}/tags/{key}\032\004\010\002\020\000\020\003*)(Unity Catalog) Delete Prompt Version Tag'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['LinkPromptVersionsToModels']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['LinkPromptVersionsToModels']._serialized_options = b'\362\206\031w\nC\n\004POST\0225/mlflow/unity-catalog/prompt-versions/links-to-models\032\004\010\002\020\000\020\003*.(Unity Catalog) Link Prompt Versions to Models'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['LinkPromptsToTraces']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['LinkPromptsToTraces']._serialized_options = b'\362\206\031o\nC\n\004POST\0225/mlflow/unity-catalog/prompt-versions/links-to-traces\032\004\010\002\020\000\020\003*&(Unity Catalog) Link Prompts to Traces'
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['LinkPromptVersionsToRuns']._options = None
    _UNITYCATALOGPROMPTSERVICE.methods_by_name['LinkPromptVersionsToRuns']._serialized_options = b'\362\206\031s\nA\n\004POST\0223/mlflow/unity-catalog/prompt-versions/links-to-runs\032\004\010\002\020\000\020\003*,(Unity Catalog) Link Prompt Versions to Runs'
    _UNITYCATALOGPROMPTSERVICE._serialized_start=167
    _UNITYCATALOGPROMPTSERVICE._serialized_end=4389
  UnityCatalogPromptService = service_reflection.GeneratedServiceType('UnityCatalogPromptService', (_service.Service,), dict(
    DESCRIPTOR = _UNITYCATALOGPROMPTSERVICE,
    __module__ = 'unity_catalog_prompt_service_pb2'
    ))

  UnityCatalogPromptService_Stub = service_reflection.GeneratedServiceStubType('UnityCatalogPromptService_Stub', (UnityCatalogPromptService,), dict(
    DESCRIPTOR = _UNITYCATALOGPROMPTSERVICE,
    __module__ = 'unity_catalog_prompt_service_pb2'
    ))


  # @@protoc_insertion_point(module_scope)

