
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: databricks_managed_catalog_service.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from . import databricks_pb2 as databricks__pb2
  from . import databricks_managed_catalog_messages_pb2 as databricks_managed_catalog_messages_pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(databricks_managed_catalog_service.proto\x12\x15mlflow.managedcatalog\x1a\x10\x64\x61tabricks.proto\x1a)databricks_managed_catalog_messages.proto\x1a\x15scalapb/scalapb.proto2\xb2\x01\n\x1d\x44\x61tabricksUnityCatalogService\x12\x90\x01\n\x08getTable\x12\x1f.mlflow.managedcatalog.GetTable\x1a\'.mlflow.managedcatalog.GetTableResponse\":\xf2\x86\x19\x36\n2\n\x03GET\x12%/unity-catalog/tables/{full_name_arg}\x1a\x04\x08\x02\x10\x00\x10\x03\x42\x34\n\'com.databricks.api.proto.managedcatalog\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'databricks_managed_catalog_service_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\n\'com.databricks.api.proto.managedcatalog\220\001\001\240\001\001\342?\002\020\001'
    _globals['_DATABRICKSUNITYCATALOGSERVICE'].methods_by_name['getTable']._loaded_options = None
    _globals['_DATABRICKSUNITYCATALOGSERVICE'].methods_by_name['getTable']._serialized_options = b'\362\206\0316\n2\n\003GET\022%/unity-catalog/tables/{full_name_arg}\032\004\010\002\020\000\020\003'
    _globals['_DATABRICKSUNITYCATALOGSERVICE']._serialized_start=152
    _globals['_DATABRICKSUNITYCATALOGSERVICE']._serialized_end=330
  _builder.BuildServices(DESCRIPTOR, 'databricks_managed_catalog_service_pb2', _globals)
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: databricks_managed_catalog_service.proto
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf import service as _service
  from google.protobuf import service_reflection
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from . import databricks_pb2 as databricks__pb2
  from . import databricks_managed_catalog_messages_pb2 as databricks_managed_catalog_messages_pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(databricks_managed_catalog_service.proto\x12\x15mlflow.managedcatalog\x1a\x10\x64\x61tabricks.proto\x1a)databricks_managed_catalog_messages.proto\x1a\x15scalapb/scalapb.proto2\xb2\x01\n\x1d\x44\x61tabricksUnityCatalogService\x12\x90\x01\n\x08getTable\x12\x1f.mlflow.managedcatalog.GetTable\x1a\'.mlflow.managedcatalog.GetTableResponse\":\xf2\x86\x19\x36\n2\n\x03GET\x12%/unity-catalog/tables/{full_name_arg}\x1a\x04\x08\x02\x10\x00\x10\x03\x42\x34\n\'com.databricks.api.proto.managedcatalog\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')



  _DATABRICKSUNITYCATALOGSERVICE = DESCRIPTOR.services_by_name['DatabricksUnityCatalogService']
  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n\'com.databricks.api.proto.managedcatalog\220\001\001\240\001\001\342?\002\020\001'
    _DATABRICKSUNITYCATALOGSERVICE.methods_by_name['getTable']._options = None
    _DATABRICKSUNITYCATALOGSERVICE.methods_by_name['getTable']._serialized_options = b'\362\206\0316\n2\n\003GET\022%/unity-catalog/tables/{full_name_arg}\032\004\010\002\020\000\020\003'
    _DATABRICKSUNITYCATALOGSERVICE._serialized_start=152
    _DATABRICKSUNITYCATALOGSERVICE._serialized_end=330
  DatabricksUnityCatalogService = service_reflection.GeneratedServiceType('DatabricksUnityCatalogService', (_service.Service,), dict(
    DESCRIPTOR = _DATABRICKSUNITYCATALOGSERVICE,
    __module__ = 'databricks_managed_catalog_service_pb2'
    ))

  DatabricksUnityCatalogService_Stub = service_reflection.GeneratedServiceStubType('DatabricksUnityCatalogService_Stub', (DatabricksUnityCatalogService,), dict(
    DESCRIPTOR = _DATABRICKSUNITYCATALOGSERVICE,
    __module__ = 'databricks_managed_catalog_service_pb2'
    ))


  # @@protoc_insertion_point(module_scope)

