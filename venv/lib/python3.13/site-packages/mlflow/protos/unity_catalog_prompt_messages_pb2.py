
import google.protobuf
from packaging.version import Version
if Version(google.protobuf.__version__).major >= 5:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: unity_catalog_prompt_messages.proto
  # Protobuf Python Version: 5.26.0
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import symbol_database as _symbol_database
  from google.protobuf.internal import builder as _builder
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
  from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
  from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
  from . import databricks_pb2 as databricks__pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#unity_catalog_prompt_messages.proto\x12\x13mlflow.unitycatalog\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x10\x64\x61tabricks.proto\x1a\x15scalapb/scalapb.proto\"\x95\x02\n\x06Prompt\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x36\n\x12\x63reation_timestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12:\n\x16last_updated_timestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x31\n\x07\x61liases\x18\x06 \x03(\x0b\x32 .mlflow.unitycatalog.PromptAlias\x12,\n\x04tags\x18\x07 \x03(\x0b\x32\x1e.mlflow.unitycatalog.PromptTagJ\x04\x08\x05\x10\x06R\rexperiment_id\"\xb1\x02\n\rPromptVersion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x36\n\x12\x63reation_timestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12:\n\x16last_updated_timestamp\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\x10\n\x08template\x18\x06 \x01(\t\x12\x31\n\x07\x61liases\x18\x07 \x03(\x0b\x32 .mlflow.unitycatalog.PromptAlias\x12\x33\n\x04tags\x18\x08 \x03(\x0b\x32%.mlflow.unitycatalog.PromptVersionTag\"\'\n\tPromptTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\".\n\x10PromptVersionTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"-\n\x0bPromptAlias\x12\r\n\x05\x61lias\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"?\n\x12UnityCatalogSchema\x12\x14\n\x0c\x63\x61talog_name\x18\x04 \x01(\t\x12\x13\n\x0bschema_name\x18\x05 \x01(\t\"P\n\x13\x43reatePromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12+\n\x06prompt\x18\x02 \x01(\x0b\x32\x1b.mlflow.unitycatalog.Prompt\"P\n\x13UpdatePromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12+\n\x06prompt\x18\x02 \x01(\x0b\x32\x1b.mlflow.unitycatalog.Prompt\"#\n\x13\x44\x65letePromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x16\n\x14\x44\x65letePromptResponse\" \n\x10GetPromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xb3\x01\n\x14SearchPromptsRequest\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x41\n\x0e\x63\x61talog_schema\x18\x03 \x01(\x0b\x32\'.mlflow.unitycatalog.UnityCatalogSchemaH\x00\x12\x13\n\x0bmax_results\x18\x04 \x01(\x03\x12\x12\n\npage_token\x18\x05 \x01(\tB\n\n\x08locationJ\x04\x08\x02\x10\x03R\rexperiment_id\"^\n\x15SearchPromptsResponse\x12,\n\x07prompts\x18\x01 \x03(\x0b\x32\x1b.mlflow.unitycatalog.Prompt\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"u\n\x1a\x43reatePromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12:\n\x0eprompt_version\x18\x03 \x01(\x0b\x32\".mlflow.unitycatalog.PromptVersionJ\x04\x08\x02\x10\x03R\x07version\"w\n\x1aUpdatePromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12:\n\x0eprompt_version\x18\x03 \x01(\x0b\x32\".mlflow.unitycatalog.PromptVersion\";\n\x1a\x44\x65letePromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"8\n\x17GetPromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"T\n\x1bSearchPromptVersionsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bmax_results\x18\x02 \x01(\x03\x12\x12\n\npage_token\x18\x03 \x01(\t\"t\n\x1cSearchPromptVersionsResponse\x12;\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32\".mlflow.unitycatalog.PromptVersion\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"E\n\x15SetPromptAliasRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\"7\n\x18\x44\x65letePromptAliasRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\"=\n\x1eGetPromptVersionByAliasRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\"?\n\x13SetPromptTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"3\n\x16\x44\x65letePromptTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\"W\n\x1aSetPromptVersionTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\t\x12\r\n\x05value\x18\x04 \x01(\t\"K\n\x1d\x44\x65letePromptVersionTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\t\"\xbe\x01\n!LinkPromptVersionsToModelsRequest\x12\x44\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32+.mlflow.unitycatalog.PromptVersionLinkEntry\x12\x11\n\tmodel_ids\x18\x02 \x03(\t:@\xe2?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]\"\xb7\x01\n\x1aLinkPromptsToTracesRequest\x12\x44\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32+.mlflow.unitycatalog.PromptVersionLinkEntry\x12\x11\n\ttrace_ids\x18\x02 \x03(\t:@\xe2?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]\"\xba\x01\n\x1fLinkPromptVersionsToRunsRequest\x12\x44\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32+.mlflow.unitycatalog.PromptVersionLinkEntry\x12\x0f\n\x07run_ids\x18\x02 \x03(\t:@\xe2?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]\"C\n\x16PromptVersionLinkEntry\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x42\x34\n\'com.databricks.api.proto.managedcatalog\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')

  _globals = globals()
  _builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
  _builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'unity_catalog_prompt_messages_pb2', _globals)
  if not _descriptor._USE_C_DESCRIPTORS:
    _globals['DESCRIPTOR']._loaded_options = None
    _globals['DESCRIPTOR']._serialized_options = b'\n\'com.databricks.api.proto.managedcatalog\220\001\001\240\001\001\342?\002\020\001'
    _globals['_LINKPROMPTVERSIONSTOMODELSREQUEST']._loaded_options = None
    _globals['_LINKPROMPTVERSIONSTOMODELSREQUEST']._serialized_options = b'\342?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]'
    _globals['_LINKPROMPTSTOTRACESREQUEST']._loaded_options = None
    _globals['_LINKPROMPTSTOTRACESREQUEST']._serialized_options = b'\342?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]'
    _globals['_LINKPROMPTVERSIONSTORUNSREQUEST']._loaded_options = None
    _globals['_LINKPROMPTVERSIONSTORUNSREQUEST']._serialized_options = b'\342?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]'
    _globals['_PROMPTVERSIONLINKENTRY'].fields_by_name['name']._loaded_options = None
    _globals['_PROMPTVERSIONLINKENTRY'].fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _globals['_PROMPTVERSIONLINKENTRY'].fields_by_name['version']._loaded_options = None
    _globals['_PROMPTVERSIONLINKENTRY'].fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _globals['_PROMPT']._serialized_start=194
    _globals['_PROMPT']._serialized_end=471
    _globals['_PROMPTVERSION']._serialized_start=474
    _globals['_PROMPTVERSION']._serialized_end=779
    _globals['_PROMPTTAG']._serialized_start=781
    _globals['_PROMPTTAG']._serialized_end=820
    _globals['_PROMPTVERSIONTAG']._serialized_start=822
    _globals['_PROMPTVERSIONTAG']._serialized_end=868
    _globals['_PROMPTALIAS']._serialized_start=870
    _globals['_PROMPTALIAS']._serialized_end=915
    _globals['_UNITYCATALOGSCHEMA']._serialized_start=917
    _globals['_UNITYCATALOGSCHEMA']._serialized_end=980
    _globals['_CREATEPROMPTREQUEST']._serialized_start=982
    _globals['_CREATEPROMPTREQUEST']._serialized_end=1062
    _globals['_UPDATEPROMPTREQUEST']._serialized_start=1064
    _globals['_UPDATEPROMPTREQUEST']._serialized_end=1144
    _globals['_DELETEPROMPTREQUEST']._serialized_start=1146
    _globals['_DELETEPROMPTREQUEST']._serialized_end=1181
    _globals['_DELETEPROMPTRESPONSE']._serialized_start=1183
    _globals['_DELETEPROMPTRESPONSE']._serialized_end=1205
    _globals['_GETPROMPTREQUEST']._serialized_start=1207
    _globals['_GETPROMPTREQUEST']._serialized_end=1239
    _globals['_SEARCHPROMPTSREQUEST']._serialized_start=1242
    _globals['_SEARCHPROMPTSREQUEST']._serialized_end=1421
    _globals['_SEARCHPROMPTSRESPONSE']._serialized_start=1423
    _globals['_SEARCHPROMPTSRESPONSE']._serialized_end=1517
    _globals['_CREATEPROMPTVERSIONREQUEST']._serialized_start=1519
    _globals['_CREATEPROMPTVERSIONREQUEST']._serialized_end=1636
    _globals['_UPDATEPROMPTVERSIONREQUEST']._serialized_start=1638
    _globals['_UPDATEPROMPTVERSIONREQUEST']._serialized_end=1757
    _globals['_DELETEPROMPTVERSIONREQUEST']._serialized_start=1759
    _globals['_DELETEPROMPTVERSIONREQUEST']._serialized_end=1818
    _globals['_GETPROMPTVERSIONREQUEST']._serialized_start=1820
    _globals['_GETPROMPTVERSIONREQUEST']._serialized_end=1876
    _globals['_SEARCHPROMPTVERSIONSREQUEST']._serialized_start=1878
    _globals['_SEARCHPROMPTVERSIONSREQUEST']._serialized_end=1962
    _globals['_SEARCHPROMPTVERSIONSRESPONSE']._serialized_start=1964
    _globals['_SEARCHPROMPTVERSIONSRESPONSE']._serialized_end=2080
    _globals['_SETPROMPTALIASREQUEST']._serialized_start=2082
    _globals['_SETPROMPTALIASREQUEST']._serialized_end=2151
    _globals['_DELETEPROMPTALIASREQUEST']._serialized_start=2153
    _globals['_DELETEPROMPTALIASREQUEST']._serialized_end=2208
    _globals['_GETPROMPTVERSIONBYALIASREQUEST']._serialized_start=2210
    _globals['_GETPROMPTVERSIONBYALIASREQUEST']._serialized_end=2271
    _globals['_SETPROMPTTAGREQUEST']._serialized_start=2273
    _globals['_SETPROMPTTAGREQUEST']._serialized_end=2336
    _globals['_DELETEPROMPTTAGREQUEST']._serialized_start=2338
    _globals['_DELETEPROMPTTAGREQUEST']._serialized_end=2389
    _globals['_SETPROMPTVERSIONTAGREQUEST']._serialized_start=2391
    _globals['_SETPROMPTVERSIONTAGREQUEST']._serialized_end=2478
    _globals['_DELETEPROMPTVERSIONTAGREQUEST']._serialized_start=2480
    _globals['_DELETEPROMPTVERSIONTAGREQUEST']._serialized_end=2555
    _globals['_LINKPROMPTVERSIONSTOMODELSREQUEST']._serialized_start=2558
    _globals['_LINKPROMPTVERSIONSTOMODELSREQUEST']._serialized_end=2748
    _globals['_LINKPROMPTSTOTRACESREQUEST']._serialized_start=2751
    _globals['_LINKPROMPTSTOTRACESREQUEST']._serialized_end=2934
    _globals['_LINKPROMPTVERSIONSTORUNSREQUEST']._serialized_start=2937
    _globals['_LINKPROMPTVERSIONSTORUNSREQUEST']._serialized_end=3123
    _globals['_PROMPTVERSIONLINKENTRY']._serialized_start=3125
    _globals['_PROMPTVERSIONLINKENTRY']._serialized_end=3192
  # @@protoc_insertion_point(module_scope)

else:
  # -*- coding: utf-8 -*-
  # Generated by the protocol buffer compiler.  DO NOT EDIT!
  # source: unity_catalog_prompt_messages.proto
  """Generated protocol buffer code."""
  from google.protobuf import descriptor as _descriptor
  from google.protobuf import descriptor_pool as _descriptor_pool
  from google.protobuf import message as _message
  from google.protobuf import reflection as _reflection
  from google.protobuf import symbol_database as _symbol_database
  # @@protoc_insertion_point(imports)

  _sym_db = _symbol_database.Default()


  from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
  from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
  from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
  from . import databricks_pb2 as databricks__pb2
  from .scalapb import scalapb_pb2 as scalapb_dot_scalapb__pb2


  DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#unity_catalog_prompt_messages.proto\x12\x13mlflow.unitycatalog\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x10\x64\x61tabricks.proto\x1a\x15scalapb/scalapb.proto\"\x95\x02\n\x06Prompt\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x36\n\x12\x63reation_timestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12:\n\x16last_updated_timestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x31\n\x07\x61liases\x18\x06 \x03(\x0b\x32 .mlflow.unitycatalog.PromptAlias\x12,\n\x04tags\x18\x07 \x03(\x0b\x32\x1e.mlflow.unitycatalog.PromptTagJ\x04\x08\x05\x10\x06R\rexperiment_id\"\xb1\x02\n\rPromptVersion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x36\n\x12\x63reation_timestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12:\n\x16last_updated_timestamp\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\x10\n\x08template\x18\x06 \x01(\t\x12\x31\n\x07\x61liases\x18\x07 \x03(\x0b\x32 .mlflow.unitycatalog.PromptAlias\x12\x33\n\x04tags\x18\x08 \x03(\x0b\x32%.mlflow.unitycatalog.PromptVersionTag\"\'\n\tPromptTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\".\n\x10PromptVersionTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"-\n\x0bPromptAlias\x12\r\n\x05\x61lias\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"?\n\x12UnityCatalogSchema\x12\x14\n\x0c\x63\x61talog_name\x18\x04 \x01(\t\x12\x13\n\x0bschema_name\x18\x05 \x01(\t\"P\n\x13\x43reatePromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12+\n\x06prompt\x18\x02 \x01(\x0b\x32\x1b.mlflow.unitycatalog.Prompt\"P\n\x13UpdatePromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12+\n\x06prompt\x18\x02 \x01(\x0b\x32\x1b.mlflow.unitycatalog.Prompt\"#\n\x13\x44\x65letePromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x16\n\x14\x44\x65letePromptResponse\" \n\x10GetPromptRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xb3\x01\n\x14SearchPromptsRequest\x12\x0e\n\x06\x66ilter\x18\x01 \x01(\t\x12\x41\n\x0e\x63\x61talog_schema\x18\x03 \x01(\x0b\x32\'.mlflow.unitycatalog.UnityCatalogSchemaH\x00\x12\x13\n\x0bmax_results\x18\x04 \x01(\x03\x12\x12\n\npage_token\x18\x05 \x01(\tB\n\n\x08locationJ\x04\x08\x02\x10\x03R\rexperiment_id\"^\n\x15SearchPromptsResponse\x12,\n\x07prompts\x18\x01 \x03(\x0b\x32\x1b.mlflow.unitycatalog.Prompt\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"u\n\x1a\x43reatePromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12:\n\x0eprompt_version\x18\x03 \x01(\x0b\x32\".mlflow.unitycatalog.PromptVersionJ\x04\x08\x02\x10\x03R\x07version\"w\n\x1aUpdatePromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12:\n\x0eprompt_version\x18\x03 \x01(\x0b\x32\".mlflow.unitycatalog.PromptVersion\";\n\x1a\x44\x65letePromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"8\n\x17GetPromptVersionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"T\n\x1bSearchPromptVersionsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bmax_results\x18\x02 \x01(\x03\x12\x12\n\npage_token\x18\x03 \x01(\t\"t\n\x1cSearchPromptVersionsResponse\x12;\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32\".mlflow.unitycatalog.PromptVersion\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"E\n\x15SetPromptAliasRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\"7\n\x18\x44\x65letePromptAliasRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\"=\n\x1eGetPromptVersionByAliasRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\"?\n\x13SetPromptTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"3\n\x16\x44\x65letePromptTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\"W\n\x1aSetPromptVersionTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\t\x12\r\n\x05value\x18\x04 \x01(\t\"K\n\x1d\x44\x65letePromptVersionTagRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\t\"\xbe\x01\n!LinkPromptVersionsToModelsRequest\x12\x44\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32+.mlflow.unitycatalog.PromptVersionLinkEntry\x12\x11\n\tmodel_ids\x18\x02 \x03(\t:@\xe2?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]\"\xb7\x01\n\x1aLinkPromptsToTracesRequest\x12\x44\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32+.mlflow.unitycatalog.PromptVersionLinkEntry\x12\x11\n\ttrace_ids\x18\x02 \x03(\t:@\xe2?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]\"\xba\x01\n\x1fLinkPromptVersionsToRunsRequest\x12\x44\n\x0fprompt_versions\x18\x01 \x03(\x0b\x32+.mlflow.unitycatalog.PromptVersionLinkEntry\x12\x0f\n\x07run_ids\x18\x02 \x03(\t:@\xe2?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]\"C\n\x16PromptVersionLinkEntry\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\xf8\x86\x19\x01\x12\x15\n\x07version\x18\x02 \x01(\tB\x04\xf8\x86\x19\x01\x42\x34\n\'com.databricks.api.proto.managedcatalog\x90\x01\x01\xa0\x01\x01\xe2?\x02\x10\x01')



  _PROMPT = DESCRIPTOR.message_types_by_name['Prompt']
  _PROMPTVERSION = DESCRIPTOR.message_types_by_name['PromptVersion']
  _PROMPTTAG = DESCRIPTOR.message_types_by_name['PromptTag']
  _PROMPTVERSIONTAG = DESCRIPTOR.message_types_by_name['PromptVersionTag']
  _PROMPTALIAS = DESCRIPTOR.message_types_by_name['PromptAlias']
  _UNITYCATALOGSCHEMA = DESCRIPTOR.message_types_by_name['UnityCatalogSchema']
  _CREATEPROMPTREQUEST = DESCRIPTOR.message_types_by_name['CreatePromptRequest']
  _UPDATEPROMPTREQUEST = DESCRIPTOR.message_types_by_name['UpdatePromptRequest']
  _DELETEPROMPTREQUEST = DESCRIPTOR.message_types_by_name['DeletePromptRequest']
  _DELETEPROMPTRESPONSE = DESCRIPTOR.message_types_by_name['DeletePromptResponse']
  _GETPROMPTREQUEST = DESCRIPTOR.message_types_by_name['GetPromptRequest']
  _SEARCHPROMPTSREQUEST = DESCRIPTOR.message_types_by_name['SearchPromptsRequest']
  _SEARCHPROMPTSRESPONSE = DESCRIPTOR.message_types_by_name['SearchPromptsResponse']
  _CREATEPROMPTVERSIONREQUEST = DESCRIPTOR.message_types_by_name['CreatePromptVersionRequest']
  _UPDATEPROMPTVERSIONREQUEST = DESCRIPTOR.message_types_by_name['UpdatePromptVersionRequest']
  _DELETEPROMPTVERSIONREQUEST = DESCRIPTOR.message_types_by_name['DeletePromptVersionRequest']
  _GETPROMPTVERSIONREQUEST = DESCRIPTOR.message_types_by_name['GetPromptVersionRequest']
  _SEARCHPROMPTVERSIONSREQUEST = DESCRIPTOR.message_types_by_name['SearchPromptVersionsRequest']
  _SEARCHPROMPTVERSIONSRESPONSE = DESCRIPTOR.message_types_by_name['SearchPromptVersionsResponse']
  _SETPROMPTALIASREQUEST = DESCRIPTOR.message_types_by_name['SetPromptAliasRequest']
  _DELETEPROMPTALIASREQUEST = DESCRIPTOR.message_types_by_name['DeletePromptAliasRequest']
  _GETPROMPTVERSIONBYALIASREQUEST = DESCRIPTOR.message_types_by_name['GetPromptVersionByAliasRequest']
  _SETPROMPTTAGREQUEST = DESCRIPTOR.message_types_by_name['SetPromptTagRequest']
  _DELETEPROMPTTAGREQUEST = DESCRIPTOR.message_types_by_name['DeletePromptTagRequest']
  _SETPROMPTVERSIONTAGREQUEST = DESCRIPTOR.message_types_by_name['SetPromptVersionTagRequest']
  _DELETEPROMPTVERSIONTAGREQUEST = DESCRIPTOR.message_types_by_name['DeletePromptVersionTagRequest']
  _LINKPROMPTVERSIONSTOMODELSREQUEST = DESCRIPTOR.message_types_by_name['LinkPromptVersionsToModelsRequest']
  _LINKPROMPTSTOTRACESREQUEST = DESCRIPTOR.message_types_by_name['LinkPromptsToTracesRequest']
  _LINKPROMPTVERSIONSTORUNSREQUEST = DESCRIPTOR.message_types_by_name['LinkPromptVersionsToRunsRequest']
  _PROMPTVERSIONLINKENTRY = DESCRIPTOR.message_types_by_name['PromptVersionLinkEntry']
  Prompt = _reflection.GeneratedProtocolMessageType('Prompt', (_message.Message,), {
    'DESCRIPTOR' : _PROMPT,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.Prompt)
    })
  _sym_db.RegisterMessage(Prompt)

  PromptVersion = _reflection.GeneratedProtocolMessageType('PromptVersion', (_message.Message,), {
    'DESCRIPTOR' : _PROMPTVERSION,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.PromptVersion)
    })
  _sym_db.RegisterMessage(PromptVersion)

  PromptTag = _reflection.GeneratedProtocolMessageType('PromptTag', (_message.Message,), {
    'DESCRIPTOR' : _PROMPTTAG,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.PromptTag)
    })
  _sym_db.RegisterMessage(PromptTag)

  PromptVersionTag = _reflection.GeneratedProtocolMessageType('PromptVersionTag', (_message.Message,), {
    'DESCRIPTOR' : _PROMPTVERSIONTAG,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.PromptVersionTag)
    })
  _sym_db.RegisterMessage(PromptVersionTag)

  PromptAlias = _reflection.GeneratedProtocolMessageType('PromptAlias', (_message.Message,), {
    'DESCRIPTOR' : _PROMPTALIAS,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.PromptAlias)
    })
  _sym_db.RegisterMessage(PromptAlias)

  UnityCatalogSchema = _reflection.GeneratedProtocolMessageType('UnityCatalogSchema', (_message.Message,), {
    'DESCRIPTOR' : _UNITYCATALOGSCHEMA,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.UnityCatalogSchema)
    })
  _sym_db.RegisterMessage(UnityCatalogSchema)

  CreatePromptRequest = _reflection.GeneratedProtocolMessageType('CreatePromptRequest', (_message.Message,), {
    'DESCRIPTOR' : _CREATEPROMPTREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.CreatePromptRequest)
    })
  _sym_db.RegisterMessage(CreatePromptRequest)

  UpdatePromptRequest = _reflection.GeneratedProtocolMessageType('UpdatePromptRequest', (_message.Message,), {
    'DESCRIPTOR' : _UPDATEPROMPTREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.UpdatePromptRequest)
    })
  _sym_db.RegisterMessage(UpdatePromptRequest)

  DeletePromptRequest = _reflection.GeneratedProtocolMessageType('DeletePromptRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEPROMPTREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.DeletePromptRequest)
    })
  _sym_db.RegisterMessage(DeletePromptRequest)

  DeletePromptResponse = _reflection.GeneratedProtocolMessageType('DeletePromptResponse', (_message.Message,), {
    'DESCRIPTOR' : _DELETEPROMPTRESPONSE,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.DeletePromptResponse)
    })
  _sym_db.RegisterMessage(DeletePromptResponse)

  GetPromptRequest = _reflection.GeneratedProtocolMessageType('GetPromptRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETPROMPTREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.GetPromptRequest)
    })
  _sym_db.RegisterMessage(GetPromptRequest)

  SearchPromptsRequest = _reflection.GeneratedProtocolMessageType('SearchPromptsRequest', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHPROMPTSREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SearchPromptsRequest)
    })
  _sym_db.RegisterMessage(SearchPromptsRequest)

  SearchPromptsResponse = _reflection.GeneratedProtocolMessageType('SearchPromptsResponse', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHPROMPTSRESPONSE,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SearchPromptsResponse)
    })
  _sym_db.RegisterMessage(SearchPromptsResponse)

  CreatePromptVersionRequest = _reflection.GeneratedProtocolMessageType('CreatePromptVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _CREATEPROMPTVERSIONREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.CreatePromptVersionRequest)
    })
  _sym_db.RegisterMessage(CreatePromptVersionRequest)

  UpdatePromptVersionRequest = _reflection.GeneratedProtocolMessageType('UpdatePromptVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _UPDATEPROMPTVERSIONREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.UpdatePromptVersionRequest)
    })
  _sym_db.RegisterMessage(UpdatePromptVersionRequest)

  DeletePromptVersionRequest = _reflection.GeneratedProtocolMessageType('DeletePromptVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEPROMPTVERSIONREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.DeletePromptVersionRequest)
    })
  _sym_db.RegisterMessage(DeletePromptVersionRequest)

  GetPromptVersionRequest = _reflection.GeneratedProtocolMessageType('GetPromptVersionRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETPROMPTVERSIONREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.GetPromptVersionRequest)
    })
  _sym_db.RegisterMessage(GetPromptVersionRequest)

  SearchPromptVersionsRequest = _reflection.GeneratedProtocolMessageType('SearchPromptVersionsRequest', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHPROMPTVERSIONSREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SearchPromptVersionsRequest)
    })
  _sym_db.RegisterMessage(SearchPromptVersionsRequest)

  SearchPromptVersionsResponse = _reflection.GeneratedProtocolMessageType('SearchPromptVersionsResponse', (_message.Message,), {
    'DESCRIPTOR' : _SEARCHPROMPTVERSIONSRESPONSE,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SearchPromptVersionsResponse)
    })
  _sym_db.RegisterMessage(SearchPromptVersionsResponse)

  SetPromptAliasRequest = _reflection.GeneratedProtocolMessageType('SetPromptAliasRequest', (_message.Message,), {
    'DESCRIPTOR' : _SETPROMPTALIASREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SetPromptAliasRequest)
    })
  _sym_db.RegisterMessage(SetPromptAliasRequest)

  DeletePromptAliasRequest = _reflection.GeneratedProtocolMessageType('DeletePromptAliasRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEPROMPTALIASREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.DeletePromptAliasRequest)
    })
  _sym_db.RegisterMessage(DeletePromptAliasRequest)

  GetPromptVersionByAliasRequest = _reflection.GeneratedProtocolMessageType('GetPromptVersionByAliasRequest', (_message.Message,), {
    'DESCRIPTOR' : _GETPROMPTVERSIONBYALIASREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.GetPromptVersionByAliasRequest)
    })
  _sym_db.RegisterMessage(GetPromptVersionByAliasRequest)

  SetPromptTagRequest = _reflection.GeneratedProtocolMessageType('SetPromptTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _SETPROMPTTAGREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SetPromptTagRequest)
    })
  _sym_db.RegisterMessage(SetPromptTagRequest)

  DeletePromptTagRequest = _reflection.GeneratedProtocolMessageType('DeletePromptTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEPROMPTTAGREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.DeletePromptTagRequest)
    })
  _sym_db.RegisterMessage(DeletePromptTagRequest)

  SetPromptVersionTagRequest = _reflection.GeneratedProtocolMessageType('SetPromptVersionTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _SETPROMPTVERSIONTAGREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.SetPromptVersionTagRequest)
    })
  _sym_db.RegisterMessage(SetPromptVersionTagRequest)

  DeletePromptVersionTagRequest = _reflection.GeneratedProtocolMessageType('DeletePromptVersionTagRequest', (_message.Message,), {
    'DESCRIPTOR' : _DELETEPROMPTVERSIONTAGREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.DeletePromptVersionTagRequest)
    })
  _sym_db.RegisterMessage(DeletePromptVersionTagRequest)

  LinkPromptVersionsToModelsRequest = _reflection.GeneratedProtocolMessageType('LinkPromptVersionsToModelsRequest', (_message.Message,), {
    'DESCRIPTOR' : _LINKPROMPTVERSIONSTOMODELSREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.LinkPromptVersionsToModelsRequest)
    })
  _sym_db.RegisterMessage(LinkPromptVersionsToModelsRequest)

  LinkPromptsToTracesRequest = _reflection.GeneratedProtocolMessageType('LinkPromptsToTracesRequest', (_message.Message,), {
    'DESCRIPTOR' : _LINKPROMPTSTOTRACESREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.LinkPromptsToTracesRequest)
    })
  _sym_db.RegisterMessage(LinkPromptsToTracesRequest)

  LinkPromptVersionsToRunsRequest = _reflection.GeneratedProtocolMessageType('LinkPromptVersionsToRunsRequest', (_message.Message,), {
    'DESCRIPTOR' : _LINKPROMPTVERSIONSTORUNSREQUEST,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.LinkPromptVersionsToRunsRequest)
    })
  _sym_db.RegisterMessage(LinkPromptVersionsToRunsRequest)

  PromptVersionLinkEntry = _reflection.GeneratedProtocolMessageType('PromptVersionLinkEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROMPTVERSIONLINKENTRY,
    '__module__' : 'unity_catalog_prompt_messages_pb2'
    # @@protoc_insertion_point(class_scope:mlflow.unitycatalog.PromptVersionLinkEntry)
    })
  _sym_db.RegisterMessage(PromptVersionLinkEntry)

  if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n\'com.databricks.api.proto.managedcatalog\220\001\001\240\001\001\342?\002\020\001'
    _LINKPROMPTVERSIONSTOMODELSREQUEST._options = None
    _LINKPROMPTVERSIONSTOMODELSREQUEST._serialized_options = b'\342?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]'
    _LINKPROMPTSTOTRACESREQUEST._options = None
    _LINKPROMPTSTOTRACESREQUEST._serialized_options = b'\342?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]'
    _LINKPROMPTVERSIONSTORUNSREQUEST._options = None
    _LINKPROMPTVERSIONSTORUNSREQUEST._serialized_options = b'\342?=\n;UcModelRegistryRoutableRPC[com.google.protobuf.empty.Empty]'
    _PROMPTVERSIONLINKENTRY.fields_by_name['name']._options = None
    _PROMPTVERSIONLINKENTRY.fields_by_name['name']._serialized_options = b'\370\206\031\001'
    _PROMPTVERSIONLINKENTRY.fields_by_name['version']._options = None
    _PROMPTVERSIONLINKENTRY.fields_by_name['version']._serialized_options = b'\370\206\031\001'
    _PROMPT._serialized_start=194
    _PROMPT._serialized_end=471
    _PROMPTVERSION._serialized_start=474
    _PROMPTVERSION._serialized_end=779
    _PROMPTTAG._serialized_start=781
    _PROMPTTAG._serialized_end=820
    _PROMPTVERSIONTAG._serialized_start=822
    _PROMPTVERSIONTAG._serialized_end=868
    _PROMPTALIAS._serialized_start=870
    _PROMPTALIAS._serialized_end=915
    _UNITYCATALOGSCHEMA._serialized_start=917
    _UNITYCATALOGSCHEMA._serialized_end=980
    _CREATEPROMPTREQUEST._serialized_start=982
    _CREATEPROMPTREQUEST._serialized_end=1062
    _UPDATEPROMPTREQUEST._serialized_start=1064
    _UPDATEPROMPTREQUEST._serialized_end=1144
    _DELETEPROMPTREQUEST._serialized_start=1146
    _DELETEPROMPTREQUEST._serialized_end=1181
    _DELETEPROMPTRESPONSE._serialized_start=1183
    _DELETEPROMPTRESPONSE._serialized_end=1205
    _GETPROMPTREQUEST._serialized_start=1207
    _GETPROMPTREQUEST._serialized_end=1239
    _SEARCHPROMPTSREQUEST._serialized_start=1242
    _SEARCHPROMPTSREQUEST._serialized_end=1421
    _SEARCHPROMPTSRESPONSE._serialized_start=1423
    _SEARCHPROMPTSRESPONSE._serialized_end=1517
    _CREATEPROMPTVERSIONREQUEST._serialized_start=1519
    _CREATEPROMPTVERSIONREQUEST._serialized_end=1636
    _UPDATEPROMPTVERSIONREQUEST._serialized_start=1638
    _UPDATEPROMPTVERSIONREQUEST._serialized_end=1757
    _DELETEPROMPTVERSIONREQUEST._serialized_start=1759
    _DELETEPROMPTVERSIONREQUEST._serialized_end=1818
    _GETPROMPTVERSIONREQUEST._serialized_start=1820
    _GETPROMPTVERSIONREQUEST._serialized_end=1876
    _SEARCHPROMPTVERSIONSREQUEST._serialized_start=1878
    _SEARCHPROMPTVERSIONSREQUEST._serialized_end=1962
    _SEARCHPROMPTVERSIONSRESPONSE._serialized_start=1964
    _SEARCHPROMPTVERSIONSRESPONSE._serialized_end=2080
    _SETPROMPTALIASREQUEST._serialized_start=2082
    _SETPROMPTALIASREQUEST._serialized_end=2151
    _DELETEPROMPTALIASREQUEST._serialized_start=2153
    _DELETEPROMPTALIASREQUEST._serialized_end=2208
    _GETPROMPTVERSIONBYALIASREQUEST._serialized_start=2210
    _GETPROMPTVERSIONBYALIASREQUEST._serialized_end=2271
    _SETPROMPTTAGREQUEST._serialized_start=2273
    _SETPROMPTTAGREQUEST._serialized_end=2336
    _DELETEPROMPTTAGREQUEST._serialized_start=2338
    _DELETEPROMPTTAGREQUEST._serialized_end=2389
    _SETPROMPTVERSIONTAGREQUEST._serialized_start=2391
    _SETPROMPTVERSIONTAGREQUEST._serialized_end=2478
    _DELETEPROMPTVERSIONTAGREQUEST._serialized_start=2480
    _DELETEPROMPTVERSIONTAGREQUEST._serialized_end=2555
    _LINKPROMPTVERSIONSTOMODELSREQUEST._serialized_start=2558
    _LINKPROMPTVERSIONSTOMODELSREQUEST._serialized_end=2748
    _LINKPROMPTSTOTRACESREQUEST._serialized_start=2751
    _LINKPROMPTSTOTRACESREQUEST._serialized_end=2934
    _LINKPROMPTVERSIONSTORUNSREQUEST._serialized_start=2937
    _LINKPROMPTVERSIONSTORUNSREQUEST._serialized_end=3123
    _PROMPTVERSIONLINKENTRY._serialized_start=3125
    _PROMPTVERSIONLINKENTRY._serialized_end=3192
  # @@protoc_insertion_point(module_scope)

