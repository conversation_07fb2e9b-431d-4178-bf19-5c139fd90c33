"use strict";(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[2],{12002:(e,t,n)=>{let r;n.r(t),n.d(t,{CodeSnippetRenderMode:()=>em,INFERENCE_TABLE_RESPONSE_COLUMN_KEY:()=>ed,INFERENCE_TABLE_TRACE_COLUMN_KEY:()=>eu,MLFLOW_TRACE_SCHEMA_VERSION_KEY:()=>ec,ModelIconType:()=>eg,ModelSpanType:()=>ep,ModelTraceChildToParentFrameMessage:()=>r7,ModelTraceContextProvider:()=>oi,ModelTraceExplorer:()=>rz,ModelTraceExplorerFrameRenderer:()=>ot,ModelTraceExplorerOSSNotebookRenderer:()=>r1,ModelTraceParentToChildFrameMessage:()=>r8,ModelTraceSpanType:()=>eh,getIsMlflowTraceUIEnabled:()=>eW,getModelTraceSpanEndTime:()=>ej,getModelTraceSpanId:()=>eH,getModelTraceSpanParentId:()=>eU,getModelTraceSpanStartTime:()=>eB,getTraceArtifact:()=>rX,isModelTrace:()=>ez,isV3ModelTraceSpan:()=>eF,useModelTraceContext:()=>or});var o=n(71218),i=n(25656),a=n.n(i),l=n(65848),s=n.n(l),c=n(80838),d=n(21340),u=n.n(d);let p=s().createContext({tags:{},contextChain:[],contextList:[]});var g=n(49232),h=n(10322);let m=(0,l.createContext)({esComponent:h.Es.UiInfra,errorBoundaryId:"__default"});function f(){return(0,l.useContext)(m)}function v(e,t){let n=(0,l.useRef)([]),{esComponent:r,errorBoundaryId:o}=f();return(0,l.useCallback)((i,a)=>{let l=Object.keys(a),s=Object.keys(i).filter(e=>l.includes(e)&&!n.current.includes(e));s.length>0&&(n.current.push(...s),g.iT.sev2(s.some(e=>"esComponent"===e)?h.Es.UiObservability:r,t,`Error boundary: ${o}, record event context chain: ${e.join(",")}, duplicate tags: ${s.join(",")}. Please follow go/recordeventduplicatetags to resolve.
Call stack: ${Error().stack}`,!0))},[e,t,r,o])}let E=Symbol("NOT_INITIALIZED");var y=n(71907),A=n(8161);let b="rit_comp_id";var _=n(19073);let T=function(e){return e.INITIAL_LOAD="INITIAL_LOAD",e.NAVIGATION="NAVIGATION",e.INTERACTION="INTERACTION",e.MEASUREMENT="MEASUREMENT",e.PANEL_BOUNDARY="PANEL_BOUNDARY",e}({});var S=n(36827);function x(e){switch(e.entityType){case"page":return{type:S.Vj.PAGE,sub_type:void 0,entity_id:e.entityId};case"component":return{type:S.Vj.COMPONENT,sub_type:function(e){switch(e){case"accordion":return S.jj.ACCORDION;case"alert":return S.jj.ALERT;case"banner":return S.jj.BANNER;case"button":return S.jj.BUTTON;case"card":return S.jj.CARD;case"checkbox":return S.jj.CHECKBOX;case"context_menu_checkbox_item":return S.jj.CONTEXT_MENU_CHECKBOX_ITEM;case"context_menu_item":return S.jj.CONTEXT_MENU_ITEM;case"context_menu_radio_group":return S.jj.CONTEXT_MENU_RADIO_GROUP;case"dialog_combobox":return S.jj.DIALOG_COMBOBOX;case"div":return S.jj.DIV;case"drawer_content":return S.jj.DRAWER_CONTENT;case"dropdown_menu_checkbox_item":return S.jj.DROPDOWN_MENU_CHECKBOX_ITEM;case"dropdown_menu_item":return S.jj.DROPDOWN_MENU_ITEM;case"dropdown_menu_radio_group":return S.jj.DROPDOWN_MENU_RADIO_GROUP;case"file_input":return S.jj.FILE_INPUT;case"form":return S.jj.FORM;case"input":return S.jj.INPUT;case"legacy_select":return S.jj.LEGACY_SELECT;case"link":return S.jj.LINK;case"modal":return S.jj.MODAL;case"notification":return S.jj.NOTIFICATION;case"pagination":return S.jj.PAGINATION;case"pill_control":return S.jj.PILL_CONTROL;case"popover":return S.jj.POPOVER;case"preview_card":return S.jj.PREVIEW_CARD;case"radio":return S.jj.RADIO;case"radio_group":return S.jj.RADIO_GROUP;case"react_select":return S.jj.REACT_SELECT;case"segmented_control_group":return S.jj.SEGMENTED_CONTROL_GROUP;case"simple_select":return S.jj.SIMPLE_SELECT;case"switch":return S.jj.SWITCH;case"table_header":return S.jj.TABLE_HEADER;case"tabs":return S.jj.TABS;case"tag":return S.jj.TAG;case"text_area":return S.jj.TEXT_AREA;case"toggle_button":return S.jj.TOGGLE_BUTTON;case"tooltip":return S.jj.TOOLTIP;case"typeahead_combobox":return S.jj.TYPEAHEAD_COMBOBOX;case"typography_link":return S.jj.TYPOGRAPHY_LINK}}(e.entitySubType),entity_id:e.entityId};case"interaction":return{type:S.Vj.INTERACTION,sub_type:function(e){switch(e){case T.INITIAL_LOAD:return S.jj.INITIAL_LOAD;case T.NAVIGATION:return S.jj.NAVIGATION;case T.INTERACTION:return S.jj.INTERACTION;case T.MEASUREMENT:return S.jj.MEASUREMENT;case T.PANEL_BOUNDARY:return S.jj.PANEL_BOUNDARY}}(e.entitySubType),entity_id:e.entityId}}}function I(e){return void 0!==e.valueBoolean?{value_boolean:e.valueBoolean}:void 0!==e.valueString?{value_string:e.valueString}:void 0!==e.valueNumber?{value_number:e.valueNumber}:void 0!==e.valueFiles?{value_multi_file_metadata:{file_metadata_list:e.valueFiles.map(e=>({mime_type:e.type,byte_size:e.size}))}}:{}}let R=new Set(["clusterLogDestination","availabilityZone","availability","clusterLogRegion","virtualClusterSize","performanceMode","manualModeEnableAutoscaling","autoterminationEnabled","autoModeSparkVersion","manualModeSparkVersion","runtimeEngine","nodeTypeId","driverNodeTypeId","instancePoolId","driverInstancePoolId","numWorkers","minWorkers","maxWorkers","autoterminationMinutes","dataSecurityMode","useMlRuntime","singleNode","useDocker","ebsVolumeType","ebsVolumeCount","ebsVolumeSize","localSsdCount"]),N={SimpleClusterCreateForm:R,SimpleClusterEditForm:R};function w(e){switch(typeof e){case"boolean":return{value_boolean:e};case"number":return{value_number:e};case"string":return{value_string:e};default:return}}function C(e){var t,n,r,o,i,a,l,s;let c={...e.customContext,interactionSubject:e.eventPayload.interactionSubject,rootInteractionId:e.eventPayload.rootInteractionId,interactionId:e.eventPayload.interactionId,eventType:e.eventType,eventClientSource:e.eventClientSource,entityType:e.eventEntity.entityType,entitySubType:e.eventEntity.entitySubType,entityId:e.eventEntity.entityId};switch(e.eventType){case"page_view":{let{productEntityChain:t,urlParams:n,...r}=e.eventPayload,o=null==t?void 0:t.map(e=>`${e.type}:${e.id}`).join(",");return{...c,pageId:e.eventEntity.entityId,productEntityChain:o,...r,...n}}case"page_clipboard_cut":return{...c,referrerComponentType:null===(t=e.eventPayload.referrerEntity)||void 0===t?void 0:t.entitySubType,referrerComponentId:null===(n=e.eventPayload.referrerEntity)||void 0===n?void 0:n.entityId};case"page_clipboard_copy":return{...c,referrerComponentType:null===(r=e.eventPayload.referrerEntity)||void 0===r?void 0:r.entitySubType,referrerComponentId:null===(o=e.eventPayload.referrerEntity)||void 0===o?void 0:o.entityId};case"page_clipboard_paste":return{...c,referrerComponentType:null===(i=e.eventPayload.referrerEntity)||void 0===i?void 0:i.entitySubType,referrerComponentId:null===(a=e.eventPayload.referrerEntity)||void 0===a?void 0:a.entityId};case"component_click":case"component_view":case"component_zoom":return{...c,componentType:e.eventEntity.entitySubType,componentId:e.eventEntity.entityId};case"component_value_change":return{...c,componentType:e.eventEntity.entitySubType,componentId:e.eventEntity.entityId,valueChangeEventValueBoolean:e.eventPayload.valueBoolean,valueChangeEventValueString:e.eventPayload.valueString,valueChangeEventValueNumber:e.eventPayload.valueNumber};case"form_submit":return{...c,referrerComponentType:null===(l=e.eventPayload.submissionTriggerEntity)||void 0===l?void 0:l.entitySubType,referrerComponentId:null===(s=e.eventPayload.submissionTriggerEntity)||void 0===s?void 0:s.entityId};case"interaction_phase":return{...c,interactionPhaseName:e.eventPayload.interactionPhaseName,interactionPhaseStatus:e.eventPayload.interactionPhaseStatus,interactionPhaseType:e.eventPayload.interactionPhaseType}}}function O(e){return{type:function(e){switch(e){case"page_view":return S.AX.PAGE_VIEW;case"page_clipboard_cut":return S.AX.CLIPBOARD_CUT;case"page_clipboard_copy":return S.AX.CLIPBOARD_COPY;case"page_clipboard_paste":return S.AX.CLIPBOARD_PASTE;case"component_click":return S.AX.COMPONENT_CLICK;case"component_value_change":return S.AX.COMPONENT_VALUE_CHANGE;case"component_view":return S.AX.COMPONENT_VIEW;case"component_zoom":return S.AX.COMPONENT_ZOOM;case"interaction_phase":return S.AX.INTERACTION_PHASE;case"form_submit":return S.AX.FORM_SUBMIT}}(e.eventType),client_source:function(e){switch(e){case"adhoc":case"dropzone":return S.Ye.ADHOC;case"design_system":return S.Ye.DESIGN_SYSTEM;case"legacy_router":return S.Ye.LEGACY_ROUTER;case"react_interaction_tracing":return S.Ye.REACT_INTERACTION_TRACING;case"unified_router":return S.Ye.UNIFIED_ROUTER;case"error_logging":return S.Ye.ERROR_LOGGING}}(e.eventClientSource),entity:x(e.eventEntity),payload:(()=>{var t,n,r,o,i,a,l,s,c,d,u,p,g,h,m,f,v,E,y,A,b;let _={interaction_subject:e.eventPayload.interactionSubject,interaction_id:e.eventPayload.interactionId,root_interaction_id:e.eventPayload.rootInteractionId,parent_id:null===(t=e.eventPayload.interactionId)||void 0===t?void 0:t.slice(0,16)};switch(e.eventType){case"page_view":let{utmSourceUrlParam:T,utmMediumUrlParam:R,utmCampaignUrlParam:C,utmAdGroupUrlParam:O,utmContentUrlParam:M,utmOfferUrlParam:Y,utmAdUrlParam:D,sisuIntentUrlParam:L,itmDataUrlParam:P,scidUrlParam:k,sisuDbxSourceUrlParam:F,sisuRlAidUrlParam:H,sisuGclidUrlParam:U,sisuMsclkidUrlParam:B,sisuFblidUrlParam:j,sisuLiFatIdUrlParam:W,sisuTwclidUrlParam:G,sisuRdtCidUrlParam:z,fullscreenWidgetUrlParam:$,...K}=null!==(n=e.eventPayload.urlParams)&&void 0!==n?n:{},V={utm_source:null===(r=e.eventPayload.urlParams)||void 0===r?void 0:r.utmSourceUrlParam,utm_medium:null===(o=e.eventPayload.urlParams)||void 0===o?void 0:o.utmMediumUrlParam,utm_campaign:null===(i=e.eventPayload.urlParams)||void 0===i?void 0:i.utmCampaignUrlParam,utm_ad_group:null===(a=e.eventPayload.urlParams)||void 0===a?void 0:a.utmAdGroupUrlParam,utm_content:null===(l=e.eventPayload.urlParams)||void 0===l?void 0:l.utmContentUrlParam,utm_offer:null===(s=e.eventPayload.urlParams)||void 0===s?void 0:s.utmOfferUrlParam,utm_ad_url:null===(c=e.eventPayload.urlParams)||void 0===c?void 0:c.utmAdUrlParam,sisu_intent:null===(d=e.eventPayload.urlParams)||void 0===d?void 0:d.sisuIntentUrlParam,itm_data:null===(u=e.eventPayload.urlParams)||void 0===u?void 0:u.itmDataUrlParam,scid:null===(p=e.eventPayload.urlParams)||void 0===p?void 0:p.scidUrlParam,sisu_dbx_source:null===(g=e.eventPayload.urlParams)||void 0===g?void 0:g.sisuDbxSourceUrlParam,sisu_rl_aid:null===(h=e.eventPayload.urlParams)||void 0===h?void 0:h.sisuRlAidUrlParam,sisu_gclid:null===(m=e.eventPayload.urlParams)||void 0===m?void 0:m.sisuGclidUrlParam,sisu_msclkid:null===(f=e.eventPayload.urlParams)||void 0===f?void 0:f.sisuMsclkidUrlParam,sisu_fbclid:null===(v=e.eventPayload.urlParams)||void 0===v?void 0:v.sisuFblidUrlParam,sisu_li_fat_id:null===(E=e.eventPayload.urlParams)||void 0===E?void 0:E.sisuLiFatIdUrlParam,sisu_twclid:null===(y=e.eventPayload.urlParams)||void 0===y?void 0:y.sisuTwclidUrlParam,sisu_rdt_cid:null===(A=e.eventPayload.urlParams)||void 0===A?void 0:A.sisuRdtCidUrlParam,fullscreen_widget:null===(b=e.eventPayload.urlParams)||void 0===b?void 0:b.fullscreenWidgetUrlParam,...K};return{..._,page_view_payload:{referrer_component_id:e.eventPayload.referrerComponentId,browser_screen_height:e.eventPayload.browserScreenHeight,browser_screen_width:e.eventPayload.browserScreenWidth,url_params:Object.values(V).some(e=>void 0!==e)?V:void 0}};case"page_clipboard_cut":return{..._,clipboard_cut_payload:{referrer_entity:e.eventPayload.referrerEntity?x(e.eventPayload.referrerEntity):void 0}};case"page_clipboard_copy":return{..._,clipboard_copy_payload:{referrer_entity:e.eventPayload.referrerEntity?x(e.eventPayload.referrerEntity):void 0}};case"page_clipboard_paste":return{..._,clipboard_paste_payload:{referrer_entity:e.eventPayload.referrerEntity?x(e.eventPayload.referrerEntity):void 0}};case"component_click":case"component_zoom":return _;case"component_value_change":let X=I(e.eventPayload);return{..._,component_value_change_payload:{...X,value_payload:X}};case"component_view":{let t=I(e.eventPayload);return{..._,component_view_payload:{value_payload:t}}}case"form_submit":return{..._,form_submit_payload:{submission_trigger_entity:e.eventPayload.submissionTriggerEntity?x(e.eventPayload.submissionTriggerEntity):void 0,form_property_values:function(e,t,n){if(void 0===t&&void 0===n||void 0===e||!N[e])return;let r={};return N[e].forEach(e=>{let o=null==t?void 0:t[e],i=null==n?void 0:n[e];(void 0!==o||void 0!==i)&&(r[e]={initial_value:w(o),final_value:w(i)})}),r}(e.eventEntity.entityId,e.eventPayload.initialPropertyValues,e.eventPayload.finalPropertyValues)}};case"interaction_phase":let q=function(e){switch(e){case"start":return S.jq.START;case"in_progress":return S.jq.IN_PROGRESS;case"complete":return S.jq.COMPLETE}}(e.eventPayload.interactionPhaseType);return{..._,interaction_phase_payload:{name:e.eventPayload.interactionPhaseName,type:q,status:function(e){switch(e){case"success":return S.Qf.SUCCESS;case"failure":return S.Qf.FAILURE;case"cancelled":return S.Qf.CANCELLED;case"interrupted":return S.Qf.INTERRUPTED;case"timed_out":return S.Qf.TIMED_OUT}}(e.eventPayload.interactionPhaseStatus),duration_millis:q===S.jq.COMPLETE&&e.eventPayload.duration?Math.round(10*e.eventPayload.duration)/10:void 0,exception:e.eventPayload.exception,exception_metadata:e.eventPayload.exceptionMetadata,causes:e.eventPayload.causes}}}})(),custom_context:e.customContextProto}}function M(e,t,n){Y(e,t,{...n,eventClientSource:"adhoc"})}function Y(e,t,n){if((0,_.W)("databricks.fe.observability.recordProtoBeforeEvent",!0)){t&&t({observability_log:O(n)}),e("uiAnalyticsEvent",C(n));return}e("uiAnalyticsEvent",C(n)),t&&t({observability_log:O(n)})}let D=new WeakMap,L=null;function P(e){if("nativeEvent"in e)return e.nativeEvent;return e}let k={onClick:"component_click",onSubmit:"form_submit",onValueChange:"component_value_change",onView:"component_view"},F=e=>{let{recordEvent:t,recordProto:n,startInteraction:r}=e;return(0,l.useCallback)(e=>{var o,i,a,l,s;let{eventType:c,componentType:d,componentId:u,componentSubType:p,value:m,shouldStartInteraction:f,event:v,mode:E,referrerComponent:y,isInteractionSubject:_,formPropertyValues:T}=e,S=null,x=k[c],I=function(e,t,n){return{interactionName:`${b}:${e}:component:${t}:${n}`,componentType:t,componentId:n}}(x,d,u),R=null;if(null!=v&&v.defaultPrevented&&"form_submit"!==x||"skip"===E)return;if(f){let e=!0;if("form_submit"===x){let t=v?D.get(P(v))||null:void 0;e=!t;let n=(t=null!==(a=t)&&void 0!==a?a:L?D.get(L):null)?function(e){if(!e.startsWith(`${b}:`))return null;return{interactionName:e,componentType:e.split(":")[3],componentId:e.split(":")[4]}}(t.name):null;t&&n&&("button"===n.componentType||"dropdown_menu_item"===n.componentType)&&(R=t,y||(y={id:n.componentId,type:n.componentType}))}R||(R=r(I.interactionName,void 0,void 0,void 0,{isInteractionSubject:!1})),e&&R&&v&&function(e,t){if(!t){g.iT.sev2(h.Es.UiInfra,"Experimental: set-interaction-for-event-with-null",Error("cannot set null interaction for event"));return}let n=P(e);if(D.has(n)){var r;g.iT.sev2(h.Es.UiObservability,"Experimental: event-has-interaction-associated-already",{eventType:n.type,existing:null===(r=D.get(n))||void 0===r?void 0:r.name,new:null==t?void 0:t.name,stack:Error().stack})}L=n,D.set(n,t),queueMicrotask(()=>{n===L&&(L=null)})}(v,R)}if("associate_event_only"===E)return;switch(x){case"component_click":S={eventType:x,eventClientSource:"design_system",eventEntity:{entityType:"component",entitySubType:d,entityId:u},eventPayload:{interactionSubject:null==_||_,interactionId:null===(o=R)||void 0===o?void 0:o.id,rootInteractionId:null===(i=R)||void 0===i?void 0:i.rootInteractionId}};break;case"form_submit":S={eventType:x,eventClientSource:"design_system",eventEntity:{entityType:"component",entitySubType:d,entityId:u},eventPayload:{interactionSubject:!0,interactionId:null===(l=R)||void 0===l?void 0:l.id,rootInteractionId:null===(s=R)||void 0===s?void 0:s.rootInteractionId,submissionTriggerEntity:y?{entityType:"component",entitySubType:y.type,entityId:y.id}:void 0,initialPropertyValues:null==T?void 0:T.initial,finalPropertyValues:null==T?void 0:T.final}};break;case"component_value_change":S={eventType:x,eventClientSource:"design_system",eventEntity:{entityType:"component",entitySubType:d,entityId:u},eventPayload:{interactionSubject:!0,valueBoolean:"boolean"==typeof m?m:void 0,valueString:"string"==typeof m?m:void 0,valueNumber:"number"==typeof m?m:void 0}};break;case"component_view":if(S={eventType:x,eventClientSource:"design_system",eventEntity:{entityType:"component",entitySubType:d,entityId:u},eventPayload:{interactionSubject:!0,valueBoolean:"boolean"==typeof m?m:void 0,valueString:"string"==typeof m?m:void 0,valueNumber:"number"==typeof m?m:void 0}},[A.v_.Alert,A.v_.Banner,A.v_.Notification].includes(d)&&p===A.NI.Error){let e=`Experimental: DuBois-${d} High Erroring Rate`;t(g.CW,{eventId:e},`Experimental: DuBois-${d} component being viewed at a high rate on ${u}`)}}S?Y(t,n,S):g.iT.sev2(h.Es.UiObservability,"InternalDesignSystemEventProvider/UnknownEventType",{eventType:c,componentType:d,componentId:u})},[t,n,r])},H=Symbol();function U(e,t,n){let r;let o=function(){if(void 0===r&&(r=e()),!r)return t(...arguments);return n(...arguments)};return o[H]=function(){r=void 0},o}var B=n(97402),j=n(37067);function W(e,t,n,r){if(n&&e(null!=n?n:{},t),n&&r){let e=Object.keys(n),t=Object.keys(r);u()(0===e.filter(e=>t.includes(e)).length,`Cannot specify a tag in both the tags and as override. Tags: ${e.join(",")}, overrides: ${t.join(",")}`)}return n||r?{...t,...n,...r}:t}let G={};function z(){let{tags:e,contextChain:t}=(0,l.useContext)(p),n=v(t,"DuplicateRecordEventInvocationTag"),r=(0,l.useCallback)((t,r)=>W(n,e,t,r),[n,e]);return{contextTags:e,mergeTags:r}}function $(){let e=function(){let{mergeTags:e}=z();return(0,l.useCallback)(t=>{let{metric:n,tags:r,overrides:o,eventData:i}=t,a=e(r,o);(0,j.y)(n,a,i)},[e])}();return(0,l.useCallback)((t,n,r)=>e({metric:t,tags:n,eventData:r}),[e])}let K=function(e){return e.GLOBAL="global",e.WEBAPP_APP_ROOT="webapp_app_root",e.WEBAPP_LOGIN_ROOT="webapp_login_root",e.WEBAPP_REACT_ROOT="webapp_react_root",e.PLATFORM_ROUTER_PAGE_VIEW="platform_router_page_view",e.PLATFORM_ROUTER_PAGE_ID="platform_router_page_id",e.SCHEMA_BROWSER_OPTIONS="schema_browser_options",e.SCHEMA_BROWSER_LOCATION_ID="schema_browser_location_id",e.SCHEMA_BROWSER_PANEL_ID="schema_browser_panel_id",e.SCHEMA_BROWSER_TREE_ID="schema_browser_tree_id",e.SEARCH_PAGE="search_page",e.NAVIGATIONAL_SEARCH_BAR="navigational_search_bar",e.INGESTION_CONNECTIONS_MODAL_ID="ingestion_connections_modal_id",e.INGESTION_CREATE_TABLE_VOLUME="ingestion_create_table_volume",e.INGESTION_CREATE_OR_MODIFY_TABLE_PAGE_ID="ingestion_create_or_modify_table_page_id",e.INGESTION_CREATE_OR_MODIFY_TABLE_ID="ingestion_create_or_modify_table_id",e.INGESTION_CREATE_TABLE_EXTERNAL_LOCATIONS_PAGE_ID="ingestion_create_table_external_locations_page_id",e.INGESTION_CREATE_TABLE_EXTERNAL_LOCATIONS_ID="ingestion_create_table_external_locations_id",e.INGESTION_NEW_PIPELINE_PAGE_ID="ingestion_new_pipeline_page_id",e.INGESTION_PREVIEW_TABLE_ID="ingestion_preview_table_id",e.INGESTION_FILE_UPLOADER_ID="ingestion_file_uploader_id",e.INGESTION_VOLUME_UPLOAD_ID="ingestion_volume_upload_id",e.INGESTION_ADD_DATA_PAGE_ID="ingestion_add_data_page_id",e.INGESTION_QUERY_SOURCE_ALERTS_ID="ingestion_query_source_alerts_id",e.INGESTION_SETUP_WIZARD_ID="ingestion_setup_wizard_id",e.DATA_EXPLORER_UI="data_explorer_ui",e.SQL_EDITOR_TAB_ID="sql_editor_tab_id",e.SQL_EDITOR_TABS_COUNT_ID="sql_editor_tabs_count_id",e.SISU_APP_ROOT="sisu_app_root",e.ANALYTICS_CONTAINER="analytics_container",e.PRODUCT_ENTITY_CONTAINER="product_entity_container",e.PANEL_BOUNDARY="panel_boundary",e.ACCOUNTS_CONSOLE_LOGIN_ROOT="accounts_console_login_root",e.BILLING_UPGRADE_MODAL="billing_upgrade_modal",e.FEATURE_PREVIEW_PAGE_ID="feature_preview_page_id",e.WEBAPP_DARK_MODE_PROMPT_MODAL="webapp_dark_mode_prompt_modal",e.ASSET_LIST_ITEM="homepage_asset_list_item",e.BROWSE_ASSET_SELECTOR="browse_asset_selector",e.BROWSE_ASSET_SELECTOR_SECTION="browse_asset_selector_section",e.BROWSE_ASSET_SELECTOR_SUBSECTION="browse_asset_selector_subsection",e.BROWSE_FAVORITES_TABLE="browse_favorite_table",e.BROWSE_POPULAR_TABLE="browse_popular_table",e.ACCOUNTS_CONSOLE_CREATE_WORKSPACE_MODAL="accounts_console_create_workspace_modal",e.DATA_SHARING="data_sharing",e.AI_PLAYGROUND="ai_playground",e.UI_DEPLOYABLE="ui_deployable",e.WAREHOUSE_UI_MONITORING_TAB="warehouse_ui_monitoring_tab",e.ASSISTANT_QUICKFIX="assistant_quickfix",e.PIPELINE_CONTEXT="pipeline_context",e.MLFLOW_CONTEXT="mlflow_context",e.ALERT_UTILS="alert_utils",e}({}),V={DUMMY_TEST_CONTEXT:["context.server_context.client_ip"],[K.ANALYTICS_CONTAINER]:["context.analytics_context.analytics_container_context","context.client_context.context_error_boundary_id","context.client_context.es_component"],[K.PLATFORM_ROUTER_PAGE_ID]:["context.client_context.page_id","context.client_context.page_info_context.record_event_context_page_id"],[K.PLATFORM_ROUTER_PAGE_VIEW]:["context.client_context.page_view_id","context.client_context.page_info_context.record_event_context_page_view_id"],[K.PRODUCT_ENTITY_CONTAINER]:["context.analytics_context.product_entity_context"]},X=void 0,q=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=new Set;return Object.keys(e).forEach(r=>{let o=[...t,r].join(".");"object"==typeof e[r]&&null!==e[r]?q(e[r],[...t,r]).forEach(e=>n.add(e)):n.add(o)}),n},J=(e,t)=>{var n,r;return null!==(n=null===(r=V[e])||void 0===r?void 0:r.some(e=>t.startsWith(e)))&&void 0!==n&&n};function Q(e,t,n,r,o){let i=q(e),l=new Set,s=new Set,c=t.map(t=>{let n=t.recordProtoContextFunc(e);return{id:t.id,result:n,paths:q(n)}});c.forEach(e=>{let{paths:t,id:n}=e;t.forEach(e=>{J(n,e)&&i.has(e)&&s.add(e)})});let d=a().merge(e,...c.map(e=>e.result));return c.forEach(e=>{let{id:t,paths:n}=e;n.forEach(e=>{i.has(e)?J(t,e)||l.add(e):i.add(e)})}),s.size>0&&(0,_.W)("databricks.fe.observability.protoContextualAlerts",!1)&&g.iT.sev2(h.Es.UiObservability,"experimental-AllowlistedRecordProtoPropertyViolation",`Error boundary: ${r}, record event context chain: ${o.join(",")}, allowlisted properties modified by main object: ${[...s].join(",")}. Please follow go/recordprotoallowlistviolation to resolve.
Call stack: ${Error().stack}`,!0),l.delete("context.client_context.page_info_context.active_record_event_context_id"),l.size>0&&(0,_.W)("databricks.fe.observability.protoContextualAlerts",!1)&&g.iT.sev2(h.Es.UiObservability,"experimental-DuplicateRecordProtoProperty",`Error boundary: ${r}, record event context chain: ${o.join(",")}, duplicate proto properties: ${[...l].join(",")}. Please follow go/recordprotoduplicateproperties to resolve.
Call stack: ${Error().stack}`,!0),d}function Z(){let{esComponent:e,errorBoundaryId:t}=f(),{contextList:n,contextChain:r}=(0,l.useContext)(p);return(0,l.useCallback)(o=>(0,B.F)(n.length>0?Q({entry:o},n,e,t,r):{entry:o}),[r,t,e,n])}function ee(){let{esComponent:e,errorBoundaryId:t}=f(),{contextList:n,contextChain:r}=(0,l.useContext)(p),o=(0,l.useRef)(X);return(0,l.useLayoutEffect)(()=>{o.current=n},[n]),(0,l.useCallback)(n=>{(0,B.F)(o.current?Q({entry:n},o.current,e,t,r):{entry:n})},[r,t,e])}let et=s().createContext({getCurrentInteraction:()=>null,startInteraction:()=>null}),en=U(()=>null!=r?r:(0,_.W)("databricks.fe.observability.enableInteractionPhaseUseRecordProtoFn",!0),()=>B.w,Z),er=U(()=>null!=r?r:(0,_.W)("databricks.fe.observability.enableInteractionPhaseUseRecordProtoFn",!0),()=>B.w,ee);var eo=n(98358);function ei(e){let{children:t}=e,n=function(){let{tags:e,contextChain:t}=(0,l.useContext)(p),n=v(t,"DuplicateRecordEventInvocationTag"),r=(0,l.useRef)(G);(0,l.useLayoutEffect)(()=>{r.current=e},[e]);let o=(0,l.useCallback)((e,t)=>(u()(r.current!==G,"mergeTagsStable called before layout effects ran. mergeTagsStable should only be called in response to user actions or in useEffect."),W(n,r.current,e,t)),[n]);return(0,l.useCallback)((e,t,n)=>{let r=o(t);(0,j.y)(e,r,n)},[o])}(),r=ee(),{startInteraction:o}=function(){let{esComponent:e,errorBoundaryId:t}=f(),{mergeTags:n}=function(){let{tags:e,contextChain:t}=(0,l.useContext)(p),n=v(t,"DuplicateRecordEventInvocationTag"),r=(0,l.useRef)(G);return(0,l.useLayoutEffect)(()=>{r.current=e},[e]),{mergeTags:(0,l.useCallback)((e,t)=>(u()(r.current!==G,"mergeTagsStable called before layout effects ran. mergeTagsStable should only be called in response to user actions or in useEffect."),W(n,r.current,e,t)),[n])}}(),r=(0,l.useContext)(et),o=r.startInteraction,i=er(),a=(0,l.useCallback)((r,a,l,s,c)=>o(r,a,l,n({errorBoundaryId:t},{...s,esComponent:e}),c?{...c,useRecordProtoFn:i}:{useRecordProtoFn:i}),[n,e,o,t,i]);return(0,l.useMemo)(()=>({...r,startInteraction:a}),[r,a])}(),i=F({recordEvent:n,recordProto:r,startInteraction:o});return(0,eo.Y)(A.zI,{callback:i,children:t})}function ea(e){let{tags:t,id:n,context:r,children:o}=e;return(0,eo.Y)(es,{tags:t,id:n,context:r,override:!1,children:o})}let el=()=>({});function es(e){let{tags:t,id:n,context:r=el,children:o,override:i}=e,{contextChain:a,tags:s,contextList:c}=(0,l.useContext)(p),d=(0,l.useMemo)(()=>[...a,n],[n,a]),g=function(e){let t=(0,l.useRef)(E);if(t.current===E){let n=e();return t.current=n,n}return t.current}(()=>y.n.generateUuidV4()),h=(0,l.useRef)(0),m=(0,l.useMemo)(()=>{h.current+=1;let e=`${g}:${h.current}`;return{tags:{...s,...t,activeRecordEventContextId:e},contextChain:d,contextList:[...c,{recordProtoContextFunc:t=>{var n,o;let i=r(t);return{...i,context:{...i.context,client_context:{...null===(n=i.context)||void 0===n?void 0:n.client_context,page_info_context:{...null===(o=i.context)||void 0===o||null===(o=o.client_context)||void 0===o?void 0:o.page_info_context,active_record_event_context_id:e}}}}},id:n}]}},[s,t,d,c,r,n,g,h]),f=v(d,"DuplicateRecordEventContextTag"),{activeRecordEventContextId:A,...b}=s;return(0,l.useEffect)(()=>{i||f(t,b)},[i,f,b,t]),u()(i||!a.includes(n),`Record event context ${n} cannot be redefined in the current chain ${d.join(",")}`),(0,eo.Y)(p.Provider,{value:m,children:(0,eo.Y)(ei,{children:o})})}let ec="mlflow.trace_schema.version",ed="response",eu="trace",ep=function(e){return e.LLM="LLM",e.CHAIN="CHAIN",e.AGENT="AGENT",e.TOOL="TOOL",e.FUNCTION="FUNCTION",e.CHAT_MODEL="CHAT_MODEL",e.RETRIEVER="RETRIEVER",e.PARSER="PARSER",e.EMBEDDING="EMBEDDING",e.RERANKER="RERANKER",e.UNKNOWN="UNKNOWN",e}({}),eg=function(e){return e.MODELS="models",e.DOCUMENT="document",e.CONNECT="connect",e.SEARCH="search",e.SORT="sort",e.UNKNOWN="unknown",e.FUNCTION="function",e.CODE="code",e.NUMBERS="numbers",e.WRENCH="wrench",e.AGENT="agent",e.CHAIN="chain",e}({}),eh=function(e){return e.LLM="LLM",e.CHAIN="CHAIN",e.AGENT="AGENT",e.TOOL="TOOL",e.CHAT_MODEL="CHAT_MODEL",e.RETRIEVER="RETRIEVER",e.PARSER="PARSER",e.EMBEDDING="EMBEDDING",e.RERANKER="RERANKER",e.UNKNOWN="UNKNOWN",e}({}),em=function(e){return e.JSON="json",e.TEXT="text",e.MARKDOWN="markdown",e}({});var ef=n(92546),ev=n(26769),eE=n(80982),ey=n(86571),eA=n(72152),eb=n(90374),e_=n(49716),eT=n(79670),eS=n(72299),ex=n(40631),eI=n(49393),eR=n(53127);let eN=e=>{let t,{type:n=eg.CONNECT,isInTooltip:r=!1,hasException:i=!1,isRootSpan:a=!1}=e,{theme:l}=(0,c.wn)();a?t="ai":i&&(t="danger");let s={[eg.MODELS]:(0,eo.Y)(ef.A,{color:t}),[eg.DOCUMENT]:(0,eo.Y)(ev.A,{color:t}),[eg.CONNECT]:(0,eo.Y)(eE.A,{color:t}),[eg.CODE]:(0,eo.Y)(ey.A,{color:t}),[eg.FUNCTION]:(0,eo.Y)(eA.A,{color:t}),[eg.NUMBERS]:(0,eo.Y)(eb.A,{color:t}),[eg.SEARCH]:(0,eo.Y)(e_.A,{color:t}),[eg.SORT]:(0,eo.Y)(eT.A,{color:t}),[eg.UNKNOWN]:(0,eo.Y)(eS.A,{color:t}),[eg.WRENCH]:(0,eo.Y)(ex.A,{color:t}),[eg.AGENT]:(0,eo.Y)(eI.A,{color:t}),[eg.CHAIN]:(0,eo.Y)(eR.A,{color:t})},d=l.colors.actionDefaultIconDefault,u=l.colors.actionPrimaryIcon,p=l.colors.backgroundSecondary;switch(n){case eg.SEARCH:d=l.colors.textValidationSuccess,u=l.colors.green500,p=l.isDarkMode?l.colors.green800:l.colors.green100;break;case eg.MODELS:d=l.isDarkMode?l.colors.blue500:l.colors.turquoise,u=l.isDarkMode?l.colors.turquoise:l.colors.blue500,p=l.isDarkMode?l.colors.blue800:l.colors.blue100;break;case eg.WRENCH:d=l.isDarkMode?l.colors.red500:l.colors.red700,u=l.isDarkMode?l.colors.red700:l.colors.red500,p=l.isDarkMode?l.colors.red800:l.colors.red100}return(0,eo.Y)("div",{css:(0,o.AH)({width:l.general.iconSize,height:l.general.iconSize,display:"flex",alignItems:"center",justifyContent:"center",borderRadius:l.legacyBorders.borderRadiusMd,background:r?l.colors.tooltipBackgroundTooltip:p,color:r?u:d,svg:{width:l.general.iconFontSize,height:l.general.iconFontSize}},"",""),children:s[n]})};function ew(e){switch(e){case ep.LLM:return eg.MODELS;case ep.CHAIN:return eg.CHAIN;case ep.AGENT:return eg.AGENT;case ep.TOOL:return eg.WRENCH;case ep.CHAT_MODEL:return eg.MODELS;case ep.RETRIEVER:return eg.SEARCH;case ep.PARSER:return eg.CODE;case ep.EMBEDDING:return eg.NUMBERS;case ep.RERANKER:return eg.SORT;case ep.FUNCTION:return eg.FUNCTION;case ep.UNKNOWN:return eg.UNKNOWN;default:return eg.FUNCTION}}function eC(e){try{return JSON.parse(e)}catch(t){return e}}let eO=(e,t)=>{let n=e.events;if(!n)return[];let r=[];return n.forEach((n,o)=>{let i=n.attributes;if(!i)return;Object.keys(i).forEach(a=>{let l=a.toLowerCase().includes(t),s=eX(n.name,o,a);l&&r.push({span:e,section:"events",key:s,isKeyMatch:!0,matchIndex:0});let c=JSON.stringify(i[a]).toLowerCase().split(t).length-1;for(let t=0;t<c;t++)r.push({span:e,section:"events",key:s,isKeyMatch:!1,matchIndex:t})})}),r},eM=(e,t)=>{if(!t)return[];let n=[],r={inputs:null==e?void 0:e.inputs,outputs:null==e?void 0:e.outputs,attributes:null==e?void 0:e.attributes,events:null==e?void 0:e.events};return(0,i.map)(r,(r,o)=>{if("events"===o){n.push(...eO(e,t));return}e$(r).forEach(r=>{r.key.toLowerCase().includes(t)&&n.push({span:e,section:o,key:r.key,isKeyMatch:!0,matchIndex:0});let i=r.value.toLowerCase().split(t).length-1;for(let t=0;t<i;t++)n.push({span:e,section:o,key:r.key,isKeyMatch:!1,matchIndex:t})})}),n},eY=(e,t,n)=>{var r,o;if(Array.isArray(e)&&e.every(e6))return e.map(tl);let i=null!==(r=tn(t))&&void 0!==r?r:[],a=null!==(o=tn(n))&&void 0!==o?o:[];if(0===i.length||0===a.length)return;return i.concat(a)},eD=(e,t)=>{var n;if(Array.isArray(e)&&e.every(e0))return e;if(Array.isArray(null==t?void 0:t.tools)&&null!=t&&null!==(n=t.tools)&&void 0!==n&&n.every(e0))return t.tools},eL=(e,t,n,r)=>{var o,a,l,s,c,d,u;let p=eC(null===(o=e.attributes)||void 0===o?void 0:o["mlflow.spanType"]),g=eC(null===(a=e.attributes)||void 0===a?void 0:a["mlflow.spanInputs"]),h=eC(null===(l=e.attributes)||void 0===l?void 0:l["mlflow.spanOutputs"]),m=ek(null!==(s=eU(e))&&void 0!==s?s:""),f=ek(eH(e)),v=eY(eC(null===(c=e.attributes)||void 0===c?void 0:c["mlflow.chat.messages"]),g,h),E=eD(eC(null===(d=e.attributes)||void 0===d?void 0:d["mlflow.chat.tools"]),g),y=(0,i.mapValues)((0,i.omitBy)(e.attributes,(e,t)=>t.startsWith("mlflow.")),e=>eC(e)),A=e.events,b=(Number(eB(e))-t)/1e3,_=(Number(null!==(u=ej(e))&&void 0!==u?u:n)-t)/1e3;return{title:e.name,icon:(0,eo.Y)(eN,{type:ew(p),hasException:eq(e)>0,isRootSpan:!m}),type:p,key:f,start:b,end:_,children:r,inputs:g,outputs:h,attributes:y,events:A,chatMessages:v,chatTools:E,parentId:m}},eP=e=>{let t=atob(e),n=t.length,r="";for(let e=0;e<n;e++)r+=t.charCodeAt(e).toString(16).padStart(2,"0");return r},ek=e=>{if(e.startsWith("0x"))return e.slice(2);try{return eP(e)}catch(t){return e}};function eF(e){return"start_time_unix_nano"in e}function eH(e){var t;return eF(e)?e.span_id:null===(t=e.context)||void 0===t?void 0:t.span_id}function eU(e){return eF(e)?e.parent_span_id:e.parent_id}function eB(e){return eF(e)?Number(e.start_time_unix_nano):Number(e.start_time)}function ej(e){return eF(e)?Number(e.end_time_unix_nano):Number(e.end_time)}function eW(){var e;return null!==(e=(0,_.W)("mlflow_tracing",null))&&void 0!==e?e:(0,_.W)("databricks.fe.mlflow.enableTracingUI",!1)}function eG(e){if(!e)return;if((0,i.isArray)(e)){var t;return null===(t=e.find(e=>e.key===ec))||void 0===t?void 0:t.value}return e[ec]}let ez=e=>{let t=null==e?void 0:e.info,n=null==e?void 0:e.data;if(!t||!n||!(null!=n&&n.spans))return!1;if((null==t?void 0:t.request_metadata)&&eG(t.request_metadata)||(null==t?void 0:t.tags)&&eG(t.tags))return!0;return!1},e$=e=>{if((0,i.isNil)(e))return[];if(Array.isArray(e)||(0,i.isString)(e)||(0,i.isNumber)(e)||(0,i.isBoolean)(e))return[{key:"",value:JSON.stringify(e,null,2)}];return Object.entries(e).map(e=>{let[t,n]=e;return{key:t,value:JSON.stringify(n,null,2)}})},eK=e=>{let{searchFilter:t,data:n,activeMatchBackgroundColor:r,inactiveMatchBackgroundColor:a,containsActiveMatch:l,activeMatch:s,scrollToActiveMatch:c}=e,d=RegExp(`(${(0,i.escapeRegExp)(t.trim())})`,"gi"),u=n.split(d),p=[],g=0;for(let e=0;e<u.length;e++)if(u[e].toLowerCase().includes(t.toLowerCase().trim())){let t=l&&s.matchIndex===g,n=t?r:a,i=(0,eo.Y)("span",{ref:t?c:null,css:(0,o.AH)({backgroundColor:n,scrollMarginTop:50},"",""),children:u[e]},e);g++,p.push(i)}else p.push(u[e]);return p},eV=e=>e.type===ep.RETRIEVER&&Array.isArray(e.outputs)&&e.outputs.length>0&&(0,i.every)(e.outputs,e=>(0,i.has)(e,"page_content")),eX=(e,t,n)=>`${e}-${t}-${n}`,eq=e=>{var t;return(null!==(t=e.events)&&void 0!==t?t:[]).filter(e=>"exception"===e.name).length},eJ=e=>{var t;let n;switch(e.type){case"user":case"human":n="user";break;case"assistant":case"ai":n="assistant";break;case"system":n="system";break;case"tool":n="tool";break;case"function":n="function";break;default:return null}let r={content:e.content,role:n},o=e.tool_calls,a=null===(t=e.additional_kwargs)||void 0===t?void 0:t.tool_calls;return!(0,i.isNil)(o)&&Array.isArray(o)&&o.length>0&&o.every(e9)?r.tool_calls=(0,i.compact)(o.map(eQ)):!(0,i.isNil)(a)&&Array.isArray(a)&&a.length>0&&a.every(e1)&&(r.tool_calls=a.map(ti)),(0,i.isNil)(e.tool_call_id)||(r.tool_call_id=e.tool_call_id),r},eQ=e=>({id:e.id,function:{arguments:JSON.stringify(e.args,null,2),name:e.name}}),eZ=e=>{if((0,i.isNil)(e)||!(0,i.isNil)(e.type)&&!(0,i.isString)(e.type)||!(0,i.isNil)(e.description)&&!(0,i.isString)(e.description)||!(0,i.isNil)(e.enum)&&!Array.isArray(e.enum))return!1;return!0},e0=e=>{var t;if((0,i.isNil)(e)||"function"!==e.type||!(0,i.has)(e,"function.name"))return!1;let n=null===(t=e.function)||void 0===t?void 0:t.parameters;if(!(0,i.isNil)(n)){let e=n.required;if(!(0,i.isNil)(e)&&(!Array.isArray(e)||!e.every(i.isString)))return!1;let t=n.properties;if(!(0,i.isNil)(t)&&!Object.values(t).every(eZ))return!1}return!0},e1=e=>{var t,n;return e&&(0,i.isString)(e.id)&&(0,i.isString)(null===(t=e.function)||void 0===t?void 0:t.arguments)&&(0,i.isString)(null===(n=e.function)||void 0===n?void 0:n.name)},e2=e=>{switch(e.type){case"text":return(0,i.isString)(e.text);case"image_url":let{image_url:t}=e;if((0,i.isNil)(t))return!1;return(0,i.isString)(t.url)&&((0,i.isNil)(t.detail)||["auto","low","high"].includes(t.detail));case"input_audio":let{input_audio:n}=e;if((0,i.isNil)(n))return!1;return(0,i.isString)(n.data)&&((0,i.isNil)(n.format)||["wav","mp3"].includes(n.format));default:return!1}},e3=e=>{if((0,i.isNil)(e)||(0,i.isString)(e))return!0;if((0,i.isArray)(e))return e.every(e=>e2(e));return!1},e5=e=>{if(!e6(e))return!1;return(0,i.isNil)(e.content)||(0,i.isString)(e.content)},e6=e=>{if(!e||e.tool_calls&&(!Array.isArray(e.tool_calls)||!e.tool_calls.every(e1))||!e3(e.content))return!1;return"user"===e.role||"assistant"===e.role||"system"===e.role||"tool"===e.role},e4=e=>e&&Array.isArray(e.messages)&&e.messages.length>0&&e.messages.every(e6),e7=e=>e&&Array.isArray(e.choices)&&e.choices.length>0&&e.choices.every(e=>(0,i.has)(e,"message")&&e5(e.message)),e8=e=>{if(!e||!(0,i.isNil)(e.content)&&!(0,i.isString)(e.content))return!1;return["human","user","assistant","ai","system","tool","function"].includes(e.type)},e9=e=>e&&(0,i.isString)(e.name)&&(0,i.has)(e,"args")&&(0,i.isString)(e.id),te=e=>e&&e8(e.message),tt=e=>e&&e5(e.message),tn=e=>{if(Array.isArray(e)&&e.every(e6))return e.map(tl);let t=tr(e);if(t)return t;let n=ts(e);if(n)return n;let r=to(e);if(r)return r;let o=tc(e);if(o)return o;let i=td(e);if(i)return i;return null},tr=e=>{if(Array.isArray(e)&&1===e.length&&Array.isArray(e[0])&&e[0].length>0&&e[0].every(e8)){let t=e[0].map(eJ);if(t.some(e=>null===e))return null;return t}if(Array.isArray(null==e?void 0:e.messages)&&e.messages.length>0&&e.messages.every(e8)){let t=e.messages.map(eJ);if(t.some(e=>null===e))return null;return t}return null},to=e=>{if(!Array.isArray(null==e?void 0:e.generations)||!(e.generations.length>0)||!e.generations[0].every(te))return null;let t=e.generations[0].map(e=>eJ(e.message));if(t.some(e=>null===e))return null;return t},ti=e=>{var t;let n=null===(t=e.function)||void 0===t?void 0:t.arguments;try{n=JSON.stringify(JSON.parse(n),null,2)}catch(e){}return{id:e.id,function:{arguments:n,name:e.function.name}}},ta=e=>{if((0,i.isNil)(e)||(0,i.isString)(e))return e;return e.map(e=>{switch(e.type){case"text":return e.text;case"image_url":return"[image]";case"input_audio":return"[audio]"}}).join("\n")},tl=e=>{var t;return{...e,content:ta(e.content),tool_calls:null===(t=e.tool_calls)||void 0===t?void 0:t.map(ti)}},ts=e=>{if(!e4(e))return null;return e.messages.map(tl)},tc=e=>{if(!e7(e))return null;return e.choices.map(e=>{var t;return{...e.message,tool_calls:null===(t=e.message.tool_calls)||void 0===t?void 0:t.map(ti)}})},td=e=>{if(!tt(e))return null;return[e.message]};var tu=n(57488);function tp(e){let{ref:t,debounceTimeMs:n}=e,r=(0,l.useRef)({width:-1,height:-1}),o=(0,l.useRef)(),[i,a]=(0,l.useState)(null),s=(0,l.useCallback)(e=>{if(0===e.length)return;let t=e[0].contentRect;if(-1===r.current.width){r.current={width:t.width,height:t.height},a(r.current);return}if(t.width!==r.current.width||t.height!==r.current.height){if(r.current.width=t.width,r.current.height=t.height,!n){a({...r.current});return}clearTimeout(o.current),o.current=setTimeout(()=>{a({...r.current})},n)}},[n]),c=(0,l.useRef)();return c.current||(c.current=new ResizeObserver(s)),(0,l.useEffect)(()=>{let e="function"==typeof t?t():t.current;if(e){let t=c.current;return t.observe(e),()=>t.unobserve(e)}}),i}var tg={name:"1h0bf8r",styles:"body, :host{user-select:none;}"},th={name:"1endq0h",styles:"display:flex;flex:1;overflow-y:auto;flex-direction:row"},tm={name:"kjqqaz",styles:"width:0;position:relative"};let tf=(0,l.forwardRef)((e,t)=>{var n;let{initialRatio:r,paneWidth:a,setPaneWidth:s,leftChild:d,leftMinWidth:u,rightChild:p,rightMinWidth:g}=e,[h,m]=(0,l.useState)(!1),f=(0,l.useRef)(null),v=null===(n=tp({ref:f}))||void 0===n?void 0:n.width,E=(null!=v?v:1/0)-g,y=(0,l.useRef)(r),{theme:A}=(0,c.wn)(),b=(0,l.useCallback)(e=>{v&&(y.current=e/v)},[v]);return(0,l.useImperativeHandle)(t,()=>({updateRatio:b})),(0,l.useEffect)(()=>{v&&s((0,i.clamp)(v*y.current,u,E))},[v,E,u,g,s]),(0,eo.FD)("div",{ref:f,css:th,children:[h&&(0,eo.Y)(o.mL,{styles:tg}),(0,eo.Y)(tu.ResizableBox,{axis:"x",width:a,css:(0,o.AH)({display:"flex",flex:`0 0 ${a}px`},"",""),handle:(0,eo.Y)("div",{css:tm,children:(0,eo.Y)("div",{css:(0,o.AH)({position:"relative",width:A.spacing.sm,marginLeft:-A.spacing.xs,minHeight:"100%",cursor:"ew-resize",backgroundColor:"rgba(0,0,0,0)",zIndex:1,":hover":{backgroundColor:"rgba(0,0,0,0.1)"}},"","")})}),onResize:(e,t)=>{let{size:n}=t,r=(0,i.clamp)(n.width,u,E);s(r),v&&(y.current=r/v)},onResizeStart:()=>m(!0),onResizeStop:()=>m(!1),minConstraints:[u,1/0],maxConstraints:[E,1/0],children:d}),p]})});var tv=n(27714),tE=n(48681),ty=n(3078),tA=n(27443),tb=n(98022),t_=n(80061),tT={name:"2wv1be",styles:"width:100%;box-sizing:border-box"};let tS=e=>{let{searchFilter:t,setSearchFilter:n,matchData:r,handleNextSearchMatch:i,handlePreviousSearchMatch:a}=e,[s,d]=(0,l.useState)(t),u=(0,tv.YQ)(n,350),{theme:p}=(0,c.wn)();return(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",justifyContent:"space-between",gap:p.spacing.sm},"",""),children:[(0,eo.Y)(tE.p,{componentId:"shared.model-trace-explorer.search-input",allowClear:!0,placeholder:"Search",value:s,onClear:()=>{n(""),d("")},onChange:e=>{d(e.target.value),u(e.target.value.toLowerCase())},prefix:(0,eo.Y)(e_.A,{}),css:tT}),r.match&&(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",marginLeft:p.spacing.xs,marginRight:p.spacing.sm,alignItems:"center"},"",""),children:[(0,eo.FD)(ty.o.Text,{css:(0,o.AH)({whiteSpace:"nowrap",marginRight:p.spacing.sm},"",""),children:[r.currentMatchIndex+1," / ",r.totalMatches]}),(0,eo.Y)(tA.$n,{"data-testid":"prev-search-match",icon:(0,eo.Y)(tb.A,{}),onClick:a,componentId:"shared.model-trace-explorer.prev-search-match"}),(0,eo.Y)(tA.$n,{"data-testid":"next-search-match",icon:(0,eo.Y)(t_.A,{}),onClick:i,componentId:"shared.model-trace-explorer.next-search-match"})]})]})};var tx=n(30109),tI=n(29183),tR={name:"15currb",styles:"display:flex;height:100%"},tN={name:"82a6rk",styles:"flex:1"};let tw=1/0,tC={HIGH:2,NORMAL:1,LOW:-1},tO=(e,t)=>{let n=[],r=e=>{if(!e)return;if(n.push(e),t.has(e.key)){var o;null===(o=e.children)||void 0===o||o.forEach(r)}};return e.forEach(r),n},tM=e=>{let t=[],n=e=>{var r;if(!e)return;t.push(e),null===(r=e.children)||void 0===r||r.forEach(n)};return e.forEach(n),t},tY=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0,n={},r=(e,o)=>{var i;if(!e||o>t)return;n[e.key]=e,null===(i=e.children)||void 0===i||i.forEach(e=>r(e,o+1))};return e.forEach(r,0),n},tD=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,n]=(0,l.useState)(()=>{if(e.rootNodes)return new Set((0,i.values)(tY(e.rootNodes,e.initialExpandDepth)).map(e=>e.key));return new Set});return{expandedKeys:t,setExpandedKeys:n}},tL=e=>{if(0===e)return"0s";if(e>=60*1e6)return`${(e/1e6/60).toFixed(2)}m`;if(e>=1e5)return`${(e/1e6).toFixed(2)}s`;return`${(e/1e3).toFixed(2)}ms`},tP=(e,t)=>{var n;if(e.key===t)return 0;return(null!==(n=e.children)&&void 0!==n?n:[]).findIndex(e=>tP(e,t)>-1)},tk=e=>{var t;if(!e.children||(null===(t=e.children)||void 0===t?void 0:t.length)===0)return 0;return Math.max(...e.children.map(tk))+1},tF=(e,t)=>{let n=new Set,r=e;for(;r&&r.parentId;)n.add(r.parentId),r=t[r.parentId];return n},tH=e=>{let t={};return e&&(0,i.compact)(tM([e]).map(e=>e.type)).forEach(e=>{t[e]=!0}),{showParents:!0,showExceptions:!0,spanTypeDisplayState:t}},tU=e=>{switch(e.section){case"inputs":case"outputs":default:return"content";case"attributes":return"attributes";case"events":return"events"}},tB=e=>{var t;let{treeNode:n,selectedNode:r,setSelectedNode:o,setActiveTab:a,setExpandedKeys:s}=e,[c,d]=(0,l.useState)(""),[u,p]=(0,l.useState)(()=>tH(n)),[g,h]=(0,l.useState)(0),{filteredTreeNodes:m,matches:f}=(0,l.useMemo)(()=>{if((0,i.isNil)(n))return{filteredTreeNodes:[],matches:[]};return function e(t,n,r){var o,i;let a=n.toLowerCase().trim(),l=Object.values(r.spanTypeDisplayState).every(e=>e);if(""===a&&l)return{filteredTreeNodes:[t],matches:[]};let s=null!==(o=t.children)&&void 0!==o?o:[],c=[],d=[];s.forEach(t=>{let{filteredTreeNodes:n,matches:o}=e(t,a,r);c.push(...n),d.push(...o)});let u=(null!==(i=t.title)&&void 0!==i?i:"").toLowerCase(),p=eM(t,a),g=p.length>0||u.includes(a),h=!t.type||r.spanTypeDisplayState[t.type],m=c.length>0,f=eq(t)>0;if(g&&h||r.showParents&&m||r.showExceptions&&f)return{filteredTreeNodes:[{...t,children:c}],matches:p.concat(d)};return{filteredTreeNodes:c,matches:d}}(n,c,u)},[null==n?void 0:n.key,c,u]),v=(0,l.useMemo)(()=>tY(m),[m]),E=(0,l.useCallback)(e=>{if(e>=f.length||e<0)return;h(e);let t=f[e];o(t.span),a(tU(t));let n=tF(t.span,v);s(e=>new Set([...e,...n]))},[f,o,a,v,s]),y=(0,l.useCallback)(()=>{E(g+1)},[g,E]),A=(0,l.useCallback)(()=>{E(g-1)},[g,E]);return(0,l.useLayoutEffect)(()=>{if(0===m.length)return;if(0===f.length){var e;if(!((null!==(e=null==r?void 0:r.key)&&void 0!==e?e:"")in v)){let e=m[0];o(e),a(null!=e&&e.chatMessages?"chat":"content")}return}h(0),o(f[0].span),a(tU(f[0]))},[m,f,o]),{matchData:{match:null!==(t=f[g])&&void 0!==t?t:null,totalMatches:f.length,currentMatchIndex:g},searchFilter:c.toLowerCase().trim(),setSearchFilter:d,spanFilterState:u,setSpanFilterState:p,filteredTreeNodes:m,handleNextSearchMatch:y,handlePreviousSearchMatch:A}};var tj=n(70821),tW=n(99347),tG=n(14671),tz=n(28219),t$=n(36173);let tK="yellow500",tV="yellow200",tX=e=>{let{searchFilter:t,data:n,activeMatch:r,containsActiveMatch:i}=e,{theme:a}=(0,c.wn)(),s=(0,l.useCallback)(e=>{null==e||e.scrollIntoView({block:"nearest",inline:"nearest"})},[]),d=(0,l.useMemo)(()=>{if(!t)return[];return eK({data:n,searchFilter:t,activeMatchBackgroundColor:a.colors[tK],inactiveMatchBackgroundColor:a.colors[tV],containsActiveMatch:i,activeMatch:r,scrollToActiveMatch:s})},[t,n,a,i,r,s]);return(0,eo.Y)("pre",{css:(0,o.AH)({whiteSpace:"pre-wrap",backgroundColor:a.colors.backgroundSecondary,padding:a.spacing.sm,fontSize:a.typography.fontSizeSm},"",""),children:d})};var tq=n(25650),tJ=n(29417),tQ=n(66795),tZ=n(67559),t0=n(83308),t1=n(83487),t2=n(83214),t3=n(8153),t5=n(66164),t6=n(22919),t4=n(30136),t7=n(70549),t8=n(21733),t9=n(60700);let ne={name:"1fz341y",styles:"margin-right:16px;text-align:right;position:absolute;right:0;padding-top:2px;padding-right:2px"};function nt(e){let{children:t,style:n}=e;return(0,eo.Y)("div",{css:ne,style:n,children:t})}let nn=s().createContext({});function nr(e){let{children:t,theme:n={}}=e;return(0,eo.Y)(nn.Provider,{value:n,children:t})}function no(e){let{theme:t="light",actions:n,children:r,style:o}=e;return(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(nt,{style:o,children:(0,eo.Y)(nr,{theme:"duotoneDark"===t?{color:"rgba(255, 255, 255, 0.84)",hoverColor:"#8ccbffcc",backgroundColor:"rgba(140, 203, 255, 0)",hoverBackgroundColor:"rgba(140, 203, 255, 0.08)"}:void 0,children:n})}),r]})}function ni(e){e.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:BASE64|PARSE_URL|UPPER|LOWER|INITCAP|FORMAT_NUMBER|FORMAT_STRING|TRIM|UNBASE64|CONCAT|MAP|MAP_KEYS|MAP_VALUES|MAP_ENTRIES|MAP_FILTER|MAP_FROM_ARRAYS|MAP_FROM_ENTRIES|MAP_ZIP_WITH|ARRAY|AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:COPY|DESCRIBE HISTORY|DESCRIBE DETAIL|STORAGE CREDENTIALS?|URL|CACHE|UNCACHE|VACUUM|MSCK|CATALOGS?|CREDENTIALS?|FILES?|RECIPIENTS?|PROVIDERS?|TBLPROPERTIES|SHARES|VIEWS|EXTERNAL|REPAIR|REFRESH|COMMENT|LIST|PARQUET|LOCATIONS?|OPTIONS|JAR|ARCHIVE|ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR|AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/i,punctuation:/[;[\]()`,.]/}}ni.displayName="databricks-runtime-sql",ni.aliases=["sql","dbr-sql","dbsql","databricks-sql"],t2.A.registerLanguage("sql",ni),t2.A.registerLanguage("java",t6.A),t2.A.registerLanguage("python",t8.A),t2.A.registerLanguage("go",t3.A),t2.A.registerLanguage("javascript",t4.A),t2.A.registerLanguage("yaml",t9.A),t2.A.registerLanguage("json",t7.A),t2.A.registerLanguage("hcl",t5.A);let na={light:{'code[class*="language-"]':{textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",color:"rgb(77, 77, 76)",background:"#fafafa",fontFamily:"'Menlo', 'Monaco', 'Consolas', 'Ubuntu Mono', 'Source Code Pro', monospace",fontSize:"12px",lineHeight:"1.5em",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none"},'pre[class*="language-"]':{textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",color:"rgb(77, 77, 76)",background:"#fafafa",fontFamily:"'Menlo', 'Monaco', 'Consolas', 'Ubuntu Mono', 'Source Code Pro', monospace",fontSize:"12px",lineHeight:"1.5em",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",overflow:"auto",position:"relative",margin:"0.5em 0",padding:"1.25em 1em"},'code[class*="language-"]::-moz-selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"]::-moz-selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"] ::-moz-selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"] ::-moz-selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"]::selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"]::selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"] ::selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"] ::selection':{background:"#cceae7",color:"#263238"},':not(pre) > code[class*="language-"]':{whiteSpace:"normal",borderRadius:"0.2em",padding:"0.1em"},".language-css > code":{color:"#f5871f"},".language-sass > code":{color:"#f5871f"},".language-scss > code":{color:"#f5871f"},'[class*="language-"] .namespace':{Opacity:"0.7"},atrule:{color:"#7c4dff"},"attr-name":{color:"#39adb5"},"attr-value":{color:"#f6a434"},attribute:{color:"#f6a434"},boolean:{color:"#7c4dff"},builtin:{color:"#39adb5"},cdata:{color:"#39adb5"},char:{color:"#39adb5"},class:{color:"#39adb5"},"class-name":{color:"#6182b8"},comment:{color:"#8e908c"},constant:{color:"#7c4dff"},deleted:{color:"#e53935"},doctype:{color:"#aabfc9"},entity:{color:"#e53935"},function:{color:"#4271ae"},hexcode:{color:"#f5871f"},id:{color:"#7c4dff",fontWeight:"bold"},important:{color:"#7c4dff",fontWeight:"bold"},inserted:{color:"#39adb5"},keyword:{color:"#8959a8"},number:{color:"#f5871f"},operator:{color:"#3e999f"},prolog:{color:"#aabfc9"},property:{color:"#39adb5"},"pseudo-class":{color:"#f6a434"},"pseudo-element":{color:"#f6a434"},punctuation:{color:"rgb(77, 77, 76)"},regex:{color:"#6182b8"},selector:{color:"#e53935"},string:{color:"#3ba85f"},symbol:{color:"#7c4dff"},tag:{color:"#e53935"},unit:{color:"#f5871f"},url:{color:"#e53935"},variable:{color:"#c72d4c"}},duotoneDark:{'code[class*="language-"]':{fontFamily:"'Menlo', 'Monaco', 'Consolas', 'Ubuntu Mono', 'Andale Mono WT', 'Andale Mono', 'Lucida Console', 'Lucida Sans Typewriter', 'DejaVu Sans Mono', 'Bitstream Vera Sans Mono', 'Liberation Mono', 'Nimbus Mono L', 'Courier New', 'Courier', 'Source Code Pro', monospace",fontSize:"14px",lineHeight:"1.375",direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",background:"#2a2734",color:"#5DFAFC"},'pre[class*="language-"]':{fontFamily:"'Menlo', 'Monaco', 'Consolas', 'Ubuntu Mono', 'Andale Mono WT', 'Andale Mono', 'Lucida Console', 'Lucida Sans Typewriter', 'DejaVu Sans Mono', 'Bitstream Vera Sans Mono', 'Liberation Mono', 'Nimbus Mono L', 'Courier New', 'Courier', 'Source Code Pro', monospace",fontSize:"14px",lineHeight:"1.375",direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",background:"#2a2734",color:"#5DFAFC",padding:"1em",margin:".5em 0",overflow:"auto"},'pre > code[class*="language-"]':{fontSize:"1em"},'pre[class*="language-"]::-moz-selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"] ::-moz-selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"]::-moz-selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"] ::-moz-selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"]::selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"] ::selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"]::selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"] ::selection':{textShadow:"none",background:"#6a51e6"},':not(pre) > code[class*="language-"]':{padding:".1em",borderRadius:".3em"},comment:{color:"#6c6783"},prolog:{color:"#6c6783"},doctype:{color:"#6c6783"},cdata:{color:"#6c6783"},punctuation:{color:"#6c6783"},namespace:{Opacity:".7"},tag:{color:"#3AACE2"},operator:{color:"#3AACE2"},number:{color:"#3AACE2"},property:{color:"#5DFAFC"},function:{color:"#5DFAFC"},"tag-id":{color:"#eeebff"},selector:{color:"#eeebff"},"atrule-id":{color:"#eeebff"},"code.language-javascript":{color:"#c4b9fe"},"attr-name":{color:"#c4b9fe"},"code.language-css":{color:"#ffffff"},"code.language-scss":{color:"#ffffff"},boolean:{color:"#ffffff"},string:{color:"#ffffff"},entity:{color:"#ffffff",cursor:"help"},url:{color:"#ffffff"},".language-css .token.string":{color:"#ffffff"},".language-scss .token.string":{color:"#ffffff"},".style .token.string":{color:"#ffffff"},"attr-value":{color:"#ffffff"},keyword:{color:"#ffffff"},control:{color:"#ffffff"},directive:{color:"#ffffff"},unit:{color:"#ffffff"},statement:{color:"#ffffff"},regex:{color:"#ffffff"},atrule:{color:"#ffffff"},placeholder:{color:"#ffffff"},variable:{color:"#ffffff"},deleted:{textDecoration:"line-through"},inserted:{borderBottom:"1px dotted #eeebff",textDecoration:"none"},italic:{fontStyle:"italic"},important:{fontWeight:"bold",color:"#c4b9fe"},bold:{fontWeight:"bold"},"pre > code.highlight":{Outline:".4em solid #8a75f5",OutlineOffset:".4em"},".line-numbers.line-numbers .line-numbers-rows":{borderRightColor:"#2c2937"},".line-numbers .line-numbers-rows > span:before":{color:"#3c3949"},".line-highlight.line-highlight":{background:"linear-gradient(to right, rgba(224, 145, 66, 0.2) 70%, rgba(224, 145, 66, 0))"}}};function nl(e){let{theme:t="light",language:n,actions:r,style:o,children:a,...l}=e,s={border:"none",borderRadius:0,margin:0,padding:"24px",...o};return(0,eo.Y)(no,{theme:t,actions:r,children:(0,eo.Y)(t2.A,{language:n,style:na[t],customStyle:s,codeTagProps:{style:(0,i.pick)(o,"backgroundColor")},...l,children:a})})}var ns=n(80333);function nc(e){return e&&`${e} !important`}function nd(e){let{tooltipMessage:t,...n}=e,{color:r,hoverColor:i,hoverBackgroundColor:a,backgroundColor:s,borderColor:c}=(0,l.useContext)(nn),d=(0,o.AH)({zIndex:1,color:nc(r),borderColor:nc(c),backgroundColor:nc(s),":hover":{color:nc(i),backgroundColor:nc(a)}},"","");return(0,eo.Y)(ns.p,{title:t,children:(0,eo.Y)(tA.$n,{componentId:"codegen_web-shared_src_snippet_actions_snippetactionbutton.tsx_33",...n,css:d})})}var nu=n(48091),np=n(23240),ng=n(23859);function nh(e){let{copyText:t,onClick:n,...r}=e,{actionIcon:o,tooltipMessage:i,copy:a}=function(e,t,n){let r=(0,tG.tz)(),o=r.formatMessage({id:"hBAhI8",defaultMessage:"Copy"}),i=r.formatMessage({id:"g4hHzO",defaultMessage:"Copied"}),a=(0,nu.useClipboard)(),s=(0,l.useRef)(),[c,d]=(0,l.useState)(!1);return(0,l.useEffect)(()=>()=>{window.clearTimeout(s.current)},[]),{actionIcon:c?(0,eo.Y)(np.A,{}):(0,eo.Y)(ng.A,{}),tooltipMessage:c?i:o,copy:()=>{a.copy(e),window.clearTimeout(s.current),d(!0),s.current=window.setTimeout(()=>{d(!1)},3e3)},copied:c,ariaLabel:o}}(t);return(0,eo.Y)(nd,{tooltipMessage:i,icon:o,onClick:e=>{a(),null==n||n(e)},...r})}let nm=e=>{let t=(0,l.useMemo)(()=>nS({extensions:e.components}),[e.components]);return(0,eo.Y)(tq.$,{components:t,remarkPlugins:nA,children:e.children})},nf=e=>{let{codeBlock:t,codeInline:n,...r}=e,o=s().useMemo(()=>{var e;let t=/language-(\w+)/.exec(null!==(e=r.className)&&void 0!==e?e:"");return t&&t[1]?t[1]:void 0},[r.className]);if(r.inline)return s().createElement(n,r);return s().createElement(t,{...r,language:o})},nv=e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Text,{code:!0,children:t})};var nE={name:"bjn8wh",styles:"position:relative"};let ny=s().memo(e=>{let{children:t,language:n}=e,{theme:r}=(0,c.wn)(),o=String(t).replace(/\n$/,"");return(0,eo.Y)("div",{css:nE,children:(0,eo.Y)(nl,{actions:(0,eo.Y)(nh,{componentId:"genai.util.markdown-copy-code-block",copyText:o}),theme:r.isDarkMode?"duotoneDark":"light",children:o,language:n&&nx(n)?n:"text",style:{padding:"8px 0",borderRadius:8,width:"100%",boxSizing:"border-box",maxHeight:640,display:"flex",flexDirection:"column-reverse"},showLineNumbers:!0})})}),nA=[tJ.A];var nb={name:"1qmr6ab",styles:"overflow:auto"},n_={name:"qneu98",styles:"height:auto;min-height:initial;display:inline-flex;min-width:100%"},nT={name:"qhxz92",styles:"max-width:100%"};let nS=e=>({a:e=>{let{href:t,children:n}=e;return(0,eo.Y)(ty.o.Link,{componentId:"codegen_webapp_js_genai_util_markdown.tsx_71",href:t,disabled:null==t?void 0:t.startsWith("."),children:n,openInNewTab:!(t&&t.startsWith("#"))})},code:t=>{var n,r,o,i;return(0,eo.Y)(nf,{...t,codeBlock:null!==(n=null===(r=e.extensions)||void 0===r?void 0:r.codeBlock)&&void 0!==n?n:ny,codeInline:null!==(o=null===(i=e.extensions)||void 0===i?void 0:i.codeInline)&&void 0!==o?o:nv})},p:e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Paragraph,{children:t})},h1:e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Title,{level:1,children:t})},h2:e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Title,{level:2,children:t})},h3:e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Title,{level:3,children:t})},h4:e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Title,{level:4,children:t})},h5:e=>{let{children:t}=e;return(0,eo.Y)(ty.o.Title,{level:5,children:t})},table:e=>{let{children:t}=e;return(0,eo.Y)("div",{css:nb,children:(0,eo.Y)(tQ.X,{scrollable:!0,css:n_,children:t})})},tr:e=>{let{children:t,isHeader:n}=e;return(0,eo.Y)(tZ.H,{children:t,isHeader:n})},th:e=>{let{children:t}=e;return(0,eo.Y)(t0.A,{componentId:"codegen_webapp_js_genai_util_markdown.tsx_90",children:t})},td:e=>{let{children:t}=e;return(0,eo.Y)(t1.n,{children:t,multiline:!0})},thead:e=>{let{children:t}=e;return(0,eo.Y)(eo.FK,{children:t})},tbody:e=>{let{children:t}=e;return(0,eo.Y)(eo.FK,{children:t})},img:e=>{let{src:t,alt:n}=e;return(0,eo.Y)("img",{src:t,alt:n,css:nT})}}),nx=e=>{switch(e){case"go":case"hcl":case"java":case"javascript":case"json":case"python":case"sql":case"text":case"yaml":return!0;default:return!1}};function nI(e){let{data:t,searchFilter:n,activeMatch:r,containsActiveMatch:a,renderMode:s}=e,d=!!n&&!(0,i.isNil)(r)&&t.toLowerCase().includes(n),{theme:u}=(0,c.wn)(),[p,g]=(0,l.useState)("json"===s),[h,m]=(0,l.useState)(d),f=(0,l.useRef)(null),v=(0,l.useMemo)(()=>{if("json"===s)return t;let e=JSON.parse(t);if((0,i.isString)(e))return e;return t},[t,s]),E=p||v.split("\n").length>4||v.length>300,y=(0,l.useMemo)(()=>{if(E&&!h){let e=v.split("\n").slice(0,4).join("\n");return e.length>300?e.slice(0,300):e}return v},[v,E,h]);(0,l.useEffect)(()=>{f.current&&g(f.current.scrollWidth>f.current.clientWidth)},[t]);let A=(0,l.useCallback)(e=>(0,eo.Y)("pre",{...e,ref:f}),[]);if(d)return(0,eo.Y)(tX,{data:t,searchFilter:n,activeMatch:r,containsActiveMatch:!r.isKeyMatch&&a});return(0,eo.FD)(eo.FK,{children:["markdown"===s?(0,eo.Y)("div",{css:(0,o.AH)({padding:u.spacing.md,backgroundColor:u.colors.backgroundSecondary,marginBottom:-u.spacing.md},"",""),children:(0,eo.Y)(nm,{children:y})}):(0,eo.Y)(nl,{PreTag:A,showLineNumbers:!0,language:s,lineProps:{style:{wordBreak:"break-word",whiteSpace:"pre-wrap"}},wrapLines:h,theme:u.isDarkMode?"duotoneDark":"light",style:{backgroundColor:u.colors.backgroundSecondary,padding:u.spacing.sm,paddingBottom:E?0:u.spacing.sm,overflow:h?"auto":"hidden",fontSize:u.typography.fontSizeSm,lineHeight:u.typography.lineHeightBase},children:y}),E&&(0,eo.Y)("div",{css:(0,o.AH)({backgroundColor:u.colors.backgroundSecondary},"",""),children:(0,eo.Y)(tA.$n,{css:(0,o.AH)({width:"100%",padding:u.spacing.sm},"",""),componentId:h?"shared.model-trace-explorer.snippet-see-less":"shared.model-trace-explorer.snippet-see-more",icon:h?(0,eo.Y)(tb.A,{}):(0,eo.Y)(t_.A,{}),type:"tertiary",onClick:()=>m(!h),children:h?(0,eo.Y)(tG.sA,{id:"AgjhWE",defaultMessage:"See less"}):(0,eo.Y)(tG.sA,{id:"HYQHVk",defaultMessage:"See more"})})})]})}let nR=e=>{let{title:t,searchFilter:n,isActiveMatch:r}=e,{theme:i}=(0,c.wn)(),a=(0,l.useCallback)(e=>{null==e||e.scrollIntoView({block:"nearest",inline:"nearest"})},[]),s=t.toLowerCase();if(!s.includes(n))return(0,eo.Y)("div",{children:t});let d=s.indexOf(n),u=d+n.length,p=r?i.colors[tK]:i.colors[tV];return(0,eo.FD)("div",{children:[t.slice(0,d),(0,eo.Y)("span",{ref:r?a:null,css:(0,o.AH)({backgroundColor:p,scrollMarginTop:50},"",""),children:t.slice(d,u)}),t.slice(u)]})};function nN(e,t){if(t)return t;if(e)return em.MARKDOWN;return em.JSON}function nw(e){switch(e){case em.JSON:return"JSON";case em.TEXT:return"Text";case em.MARKDOWN:return"Markdown"}}var nC={name:"fhxb3m",styles:"display:flex;flex-direction:row;align-items:center"},nO={name:"1c55ilo",styles:"height:min-content"};function nM(e){var t;let{title:n,tokens:r,data:a,searchFilter:s,activeMatch:d,containsActiveMatch:u,initialRenderMode:p}=e,g=(0,l.useMemo)(()=>JSON.parse(a),[a]),h=(0,i.isString)(g),{theme:m}=(0,c.wn)(),[f,v]=(0,l.useState)(nN(h,p)),E=u&&null!==(t=null==d?void 0:d.isKeyMatch)&&void 0!==t&&t;return(0,l.useEffect)(()=>{v(nN(h,p))},[h,p]),(0,eo.Y)("div",{css:(0,o.AH)({position:"relative",marginBottom:m.spacing.md},"",""),children:(0,eo.FD)("div",{css:(0,o.AH)({borderRadius:m.legacyBorders.borderRadiusMd,overflow:"hidden"},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center",borderBottom:`1px solid ${m.colors.border}`,backgroundColor:m.colors.backgroundSecondary,padding:m.spacing.xs},"",""),children:[(0,eo.Y)(ty.o.Title,{css:(0,o.AH)({marginLeft:m.spacing.md-m.spacing.xs,maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},"",""),level:4,color:"secondary",withoutMargins:!0,children:(0,eo.Y)(nR,{title:n,searchFilter:s,isActiveMatch:E})}),(0,eo.FD)("div",{css:nC,children:[h&&!s&&(0,eo.FD)(tz.Root,{children:[(0,eo.Y)(tz.Trigger,{asChild:!0,children:(0,eo.Y)(t$.v,{componentId:"shared.model-trace-explorer.snippet-render-mode-tag",css:nO,children:(0,eo.FD)("div",{css:(0,o.AH)({paddingLeft:m.spacing.xs,marginRight:m.spacing.xs,cursor:"pointer"},"",""),children:[(0,eo.Y)(ty.o.Text,{size:"sm",color:"secondary",children:nw(f)}),(0,eo.Y)(t_.A,{})]})})}),(0,eo.FD)(tz.Content,{children:[(0,eo.Y)(tz.RadioGroup,{componentId:"shared.model-trace-explorer.snippet-render-mode-radio",value:f,onValueChange:e=>v(e),children:Object.values(em).map(e=>(0,eo.FD)(tz.RadioItem,{value:e,children:[(0,eo.Y)(tz.ItemIndicator,{}),nw(e)]},e))}),(0,eo.Y)(tz.Arrow,{})]})]}),(0,eo.Y)(nh,{componentId:"shared.model-trace-explorer.copy-snippet",copyText:a},"copy-snippet")]})]}),(0,eo.Y)(nI,{data:a,searchFilter:s,activeMatch:d,containsActiveMatch:u,renderMode:f})]})})}function nY(e){let{activeSpan:t,searchFilter:n,activeMatch:r}=e,{theme:a}=(0,c.wn)(),{attributes:l}=t,s=(0,i.keys)(l).length>0,d=!(0,i.isNil)(r)&&r.span.key===t.key;if(!s||(0,i.isNil)(l))return(0,eo.Y)("div",{css:(0,o.AH)({marginTop:a.spacing.md},"",""),children:(0,eo.Y)(tj.S,{description:(0,eo.Y)(tG.sA,{id:"B5K1Ix",defaultMessage:"No attributes found"})})});return(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:a.spacing.sm,padding:a.spacing.md},"",""),children:Object.entries(l).map(e=>{let[t,o]=e;return(0,eo.Y)(nM,{title:t,data:JSON.stringify(o,null,2),searchFilter:n,activeMatch:r,containsActiveMatch:d&&"attributes"===r.section&&r.key===t},t)})})}var nD=n(17061);function nL(e){let{paramName:t,paramProperties:n,isRequired:r}=e,{theme:i}=(0,c.wn)(),{type:a,description:l,enum:s}=n,d=a||l||s,u=d?{borderTopLeftRadius:i.borders.borderRadiusMd,borderTopRightRadius:i.borders.borderRadiusMd,borderBottom:`1px solid ${i.colors.border}`}:{borderRadius:i.borders.borderRadiusMd};return(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",width:"100%",borderRadius:i.borders.borderRadiusMd,border:`1px solid ${i.colors.border}`},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",alignItems:"center",backgroundColor:i.colors.backgroundSecondary,padding:`${i.spacing.sm}px ${i.spacing.md}px`,gap:i.spacing.sm,...u},"",""),children:[(0,eo.Y)(ty.o.Title,{withoutMargins:!0,style:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden"},children:t}),r&&(0,eo.Y)(ty.o.Text,{withoutMargins:!0,color:"error",children:(0,eo.Y)(tG.sA,{id:"JmNdE4",defaultMessage:"required"})})]}),d&&(0,eo.FD)("div",{css:(0,o.AH)({display:"grid",gridTemplateColumns:"auto 1fr",gridTemplateRows:"auto",gap:i.spacing.md,padding:`${i.spacing.sm}px ${i.spacing.md}px`},"",""),children:[a&&(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(ty.o.Text,{withoutMargins:!0,bold:!0,children:(0,eo.Y)(tG.sA,{id:"CbRDlp",defaultMessage:"Type"})}),(0,eo.Y)(ty.o.Text,{withoutMargins:!0,code:!0,children:a})]}),l&&(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(ty.o.Text,{withoutMargins:!0,bold:!0,children:(0,eo.Y)(tG.sA,{id:"SLjN1/",defaultMessage:"Description"})}),(0,eo.Y)(ty.o.Text,{withoutMargins:!0,children:l})]}),s&&(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(ty.o.Text,{withoutMargins:!0,bold:!0,children:(0,eo.Y)(tG.sA,{id:"/WK1x1",defaultMessage:"Enum Values"})}),(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",gap:i.spacing.sm,flexWrap:"wrap"},"",""),children:s.map(e=>(0,eo.Y)(ty.o.Text,{withoutMargins:!0,code:!0,children:e},e))})]})]})]})}function nP(e){var t,n,r;let{tool:i}=e,{theme:a}=(0,c.wn)(),[s,d]=(0,l.useState)(!1),u=i.function.description,p=null===(t=i.function.parameters)||void 0===t?void 0:t.properties,g=null!==(n=null===(r=i.function.parameters)||void 0===r?void 0:r.required)&&void 0!==n?n:[],h=u||p,m=h?{":hover":{backgroundColor:a.colors.actionIconBackgroundHover,cursor:"pointer"}}:{};return(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",width:"100%",borderRadius:a.borders.borderRadiusMd,border:`1px solid ${a.colors.border}`,marginBottom:a.spacing.md},"",""),"data-testid":"model-trace-explorer-chat-tool",children:[(0,eo.FD)("div",{role:"button",css:(0,o.AH)({display:"flex",flexDirection:"row",gap:a.spacing.sm,alignItems:"center",borderBottom:h&&s?`1px solid ${a.colors.border}`:"none",padding:a.spacing.sm,...m},"",""),onClick:()=>d(!s),"data-testid":"model-trace-explorer-chat-tool-toggle",children:[h&&(s?(0,eo.Y)(t_.A,{}):(0,eo.Y)(nD.A,{})),(0,eo.Y)(ty.o.Text,{bold:!0,withoutMargins:!0,style:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden"},children:i.function.name})]}),h&&s&&(0,eo.FD)("div",{css:(0,o.AH)({padding:a.spacing.md},"",""),children:[u&&(0,eo.Y)(ty.o.Paragraph,{style:{whiteSpace:"pre-wrap"},children:i.function.description}),p&&(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:a.spacing.md},"",""),children:Object.keys(p).map(e=>(0,eo.Y)(nL,{paramName:e,paramProperties:p[e],isRequired:g.includes(e)},e))})]})]})}var nk=n(22073),nF=n(99145),nH=n(39234);let nU=e=>{let t=e.isDarkMode?e.colors.tagPurple:e.colors.tagIndigo,n=e.isDarkMode?e.colors.white:e.colors.tagIndigo,r=e.colors.tagPurple,o=e.isDarkMode?"48%":"12%";return{iconColor:t,textColor:n,backgroundColor:`color-mix(in srgb, ${r} ${o}, transparent)`}};var nB={name:"12gbhjf",styles:"display:inline-flex;flex-shrink:1;overflow:hidden"};function nj(e){let{toolCall:t}=e,{theme:n}=(0,c.wn)(),{iconColor:r,textColor:i,backgroundColor:a}=(0,l.useMemo)(()=>nU(n),[n]);return(0,eo.FD)("div",{children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",gap:n.spacing.xs,alignItems:"center",marginBottom:n.spacing.sm},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"inline-flex",flexDirection:"row",gap:n.spacing.sm,backgroundColor:a,alignItems:"center",padding:`2px ${n.spacing.xs}px`,width:"min-content",borderRadius:n.borders.borderRadiusSm},"",""),children:[(0,eo.Y)(eA.A,{css:(0,o.AH)({color:r},"","")}),(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({color:`${i} !important`,whiteSpace:"nowrap"},"",""),children:t.function.name})]}),(0,eo.Y)(nH.m,{componentId:"test",content:t.id,children:(0,eo.Y)("div",{css:nB,children:(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({padding:`2px ${n.spacing.xs}px`,backgroundColor:n.colors.codeBackground,"> code":{background:"none !important"},borderRadius:n.borders.borderRadiusSm,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"},"",""),code:!0,color:"secondary",children:t.id})})})]}),(0,eo.Y)(nl,{language:"json",theme:n.isDarkMode?"duotoneDark":"light",style:{padding:n.spacing.sm,marginBottom:n.spacing.md,fontSize:n.typography.fontSizeSm,lineHeight:n.typography.lineHeightBase},showLineNumbers:!0,children:t.function.arguments})]},t.id)}let nW=e=>{switch(e){case"system":return(0,eo.Y)(nk.A,{});case"user":return(0,eo.Y)(nF.A,{});case"tool":case"function":return(0,eo.Y)(ex.A,{});default:return(0,eo.Y)(ef.A,{})}},nG=e=>{switch(e){case"system":return(0,eo.Y)(tG.sA,{id:"+gqcru",defaultMessage:"System"});case"user":return(0,eo.Y)(tG.sA,{id:"t+aG6y",defaultMessage:"User"});case"assistant":return(0,eo.Y)(tG.sA,{id:"Hvznjh",defaultMessage:"Assistant"});case"tool":return(0,eo.Y)(tG.sA,{id:"dPn3az",defaultMessage:"Tool"});case"function":return(0,eo.Y)(tG.sA,{id:"5ZUAC2",defaultMessage:"Function"});default:return e}};var nz={name:"12gbhjf",styles:"display:inline-flex;flex-shrink:1;overflow:hidden"},n$={name:"12wal98",styles:"text-overflow:ellipsis;overflow:hidden;white-space:nowrap"};function nK(e){var t;let{message:n,className:r}=e,{theme:a}=(0,c.wn)(),[s,d]=(0,l.useState)(!1),u=null!==(t=n.content)&&void 0!==t?t:"",p=u.length>300,g=(0,l.useMemo)(()=>p?{":hover":{backgroundColor:a.colors.actionIconBackgroundHover,cursor:"pointer"}}:{},[a,p]),h=p&&!s?`${u.slice(0,300)}...`:u;return(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",width:"100%",borderRadius:a.borders.borderRadiusMd,border:`1px solid ${a.colors.border}`},"",""),className:r,children:[(0,eo.FD)("div",{role:"button",css:(0,o.AH)({display:"flex",flexDirection:"row",alignItems:"center",backgroundColor:a.colors.backgroundSecondary,borderTopLeftRadius:a.borders.borderRadiusMd,borderTopRightRadius:a.borders.borderRadiusMd,borderBottom:`1px solid ${a.colors.border}`,padding:`${a.spacing.sm}px ${a.spacing.md}px`,gap:a.spacing.sm,...g},"",""),onClick:()=>d(!s),children:[p&&(s?(0,eo.Y)(t_.A,{}):(0,eo.Y)(nD.A,{})),nW(n.role),(0,eo.Y)(ty.o.Text,{bold:!0,children:nG(n.role)}),n.tool_call_id&&(0,eo.Y)(nH.m,{componentId:"test",content:n.tool_call_id,children:(0,eo.Y)("div",{css:nz,children:(0,eo.Y)(ty.o.Text,{css:n$,code:!0,children:n.tool_call_id})})})]}),(0,eo.FD)("div",{css:(0,o.AH)({padding:a.spacing.md,marginBottom:-a.typography.fontSizeBase},"",""),children:[!(0,i.isNil)(n.tool_calls)&&n.tool_calls.map(e=>(0,eo.Y)(nj,{toolCall:e},e.id)),u&&(0,eo.Y)(nm,{children:h})]}),p&&(0,eo.Y)(tA.$n,{componentId:s?"shared.model-trace-explorer.chat-message-see-less":"shared.model-trace-explorer.chat-message-see-more",icon:s?(0,eo.Y)(tb.A,{}):(0,eo.Y)(t_.A,{}),type:"tertiary",onClick:()=>d(!s),css:(0,o.AH)({display:"flex",width:"100%",padding:a.spacing.md,borderTopLeftRadius:0,borderTopRightRadius:0},"",""),children:s?(0,eo.Y)(tG.sA,{id:"gTEFCD",defaultMessage:"See less"}):(0,eo.Y)(tG.sA,{id:"6fP+8o",defaultMessage:"See more"})})]})}function nV(e){let{messages:t}=e,{theme:n}=(0,c.wn)();if((0,i.isNil)(t))return null;return(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:n.spacing.md},"",""),children:t.map((e,t)=>(0,eo.Y)(nK,{message:e},t))})}var nX={name:"1d3w5wq",styles:"width:100%"};let nq=e=>{let{sectionKey:t,title:n,children:r,withBorder:i=!1}=e,[a,s]=(0,l.useState)(!0),{theme:d}=(0,c.wn)();return(0,eo.FD)("div",{css:(0,o.AH)({marginBottom:d.spacing.md},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({alignItems:"center",display:"flex",flexDirection:"row",gap:d.spacing.xs,marginBottom:d.spacing.xs},"",""),children:[(0,eo.Y)(tA.$n,{size:"small",componentId:`shared.model-trace-explorer.expand-${t}`,type:"tertiary",icon:a?(0,eo.Y)(t_.A,{}):(0,eo.Y)(nD.A,{}),onClick:()=>s(!a)}),(0,eo.Y)(ty.o.Title,{withoutMargins:!0,level:4,css:nX,children:n})]}),a&&(0,eo.Y)("div",{css:(0,o.AH)({border:i?`1px solid ${d.colors.border}`:"",borderRadius:i?d.legacyBorders.borderRadiusMd:"",padding:i?d.spacing.md:0,paddingTop:i?d.spacing.md:d.spacing.sm,paddingBottom:0},"",""),children:r})]})};function nJ(e){let{chatMessages:t,chatTools:n}=e,{theme:r}=(0,c.wn)();return(0,eo.FD)("div",{css:(0,o.AH)({overflowY:"auto",padding:r.spacing.md},"",""),"data-testid":"model-trace-explorer-chat-tab",children:[n&&(0,eo.Y)(nq,{title:(0,eo.Y)(tG.sA,{id:"Cjq57+",defaultMessage:"Tools"}),sectionKey:"messages",children:n.map(e=>(0,eo.Y)(nP,{tool:e},e.function.name))}),(0,eo.Y)(nq,{title:(0,eo.Y)(tG.sA,{id:"AAdQxU",defaultMessage:"Messages"}),sectionKey:"messages",children:(0,eo.Y)(nV,{messages:t})})]})}var nQ={name:"1s4awny",styles:"display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:100%"},nZ={name:"uqztsn",styles:"display:flex;flex-direction:row;justify-content:space-between;width:100%"};function n0(e){let{activeSpan:t,className:n,searchFilter:r,activeMatch:o}=e,a=(0,l.useMemo)(()=>e$(null==t?void 0:t.inputs),[t]),s=(0,l.useMemo)(()=>e$(null==t?void 0:t.outputs),[t]);if((0,i.isNil)(t))return null;let c=a.length>0,d=s.length>0,u=!(0,i.isNil)(o)&&o.span.key===t.key;return(0,eo.FD)("div",{"data-testid":"model-trace-explorer-default-span-view",children:[c&&(0,eo.Y)(nq,{withBorder:!0,sectionKey:"input",title:(0,eo.Y)("div",{css:nQ,children:(0,eo.Y)(tG.sA,{id:"/TX1Az",defaultMessage:"Inputs"})}),children:a.map((e,t)=>{let{key:n,value:i}=e;return(0,eo.Y)(nM,{title:n,data:i,searchFilter:r,activeMatch:o,containsActiveMatch:u&&"inputs"===o.section&&o.key===n},n||t)})}),d&&(0,eo.Y)(nq,{withBorder:!0,sectionKey:"output",title:(0,eo.Y)("div",{css:nZ,children:(0,eo.Y)(tG.sA,{id:"ZstVNI",defaultMessage:"Outputs"})}),children:s.map(e=>{let{key:t,value:n}=e;return(0,eo.Y)(nM,{title:t,data:n,searchFilter:r,activeMatch:o,containsActiveMatch:u&&"outputs"===o.section&&o.key===t},t)})})]})}let n1=e=>e?{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}:{whiteSpace:"nowrap"},n2=e=>{let{itemKey:t,itemValue:n,charLimit:r=18,maxWidth:i=150,className:a}=e,{shouldTruncateKey:l,shouldTruncateValue:s}=function(e,t,n){let r=e.length+t.length,o=e.length>t.length,i=o?t.length:e.length;if(r<=n)return{shouldTruncateKey:!1,shouldTruncateValue:!1};if(i>n/2)return{shouldTruncateKey:!0,shouldTruncateValue:!0};return{shouldTruncateKey:o,shouldTruncateValue:!o}}(t,n,r);return(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.key-value-tag.hover-tooltip",content:`${t}: ${n}`,children:(0,eo.Y)(t$.v,{componentId:"shared.model-trace-explorer.key-value-tag",className:a,children:(0,eo.FD)("span",{css:(0,o.AH)({maxWidth:i,display:"inline-flex"},"",""),children:[(0,eo.Y)(ty.o.Text,{bold:!0,css:n1(l),size:"sm",children:t}),":\xa0",(0,eo.Y)(ty.o.Text,{css:n1(s),size:"sm",children:n})]})})})};var n3={name:"1fttcpj",styles:"display:flex;flex-direction:column"};function n5(e){let{text:t,metadataTags:n,setExpanded:r,logDocumentClick:i}=e,{theme:a}=(0,c.wn)();return(0,eo.FD)("div",{css:n3,children:[(0,eo.Y)("div",{role:"button",onClick:()=>{r(!1),i("collapse")},css:(0,o.AH)({display:"flex",flexDirection:"row",alignItems:"center",cursor:"pointer",padding:a.spacing.md,height:a.typography.lineHeightBase,boxSizing:"content-box","&:hover":{backgroundColor:a.colors.backgroundSecondary}},"",""),children:(0,eo.Y)(ev.A,{})}),(0,eo.Y)("div",{css:(0,o.AH)({padding:a.spacing.md,paddingBottom:0},"",""),children:(0,eo.Y)(nm,{children:t})}),(0,eo.Y)("div",{css:(0,o.AH)({padding:a.spacing.md,paddingTop:0},"",""),children:n.map(e=>{let{key:t,value:n}=e;return(0,eo.Y)(n2,{itemKey:t,itemValue:n},t)})}),(0,eo.Y)(tA.$n,{css:(0,o.AH)({width:"100%",padding:a.spacing.sm},"",""),componentId:"shared.model-trace-explorer.retriever-document-collapse",icon:(0,eo.Y)(tb.A,{}),type:"tertiary",onClick:()=>r(!1),children:(0,eo.Y)(tG.sA,{id:"AgjhWE",defaultMessage:"See less"})})]})}var n6={name:"ho1qnd",styles:"display:flex;flex-direction:row"},n4={name:"1r5gb7q",styles:"display:inline-block"},n7={name:"1bmnxg7",styles:"white-space:nowrap"};function n8(e){let{text:t,metadataTags:n,setExpanded:r,logDocumentClick:i}=e,{theme:a}=(0,c.wn)();return(0,eo.FD)("div",{role:"button",onClick:()=>{r(!0),i("expand")},css:(0,o.AH)({display:"flex",flexDirection:"row",padding:a.spacing.md,gap:a.spacing.sm,alignItems:"center",justifyContent:"space-between",cursor:"pointer","&:hover":{backgroundColor:a.colors.backgroundSecondary}},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",gap:a.spacing.sm,alignItems:"center",minWidth:0,flexShrink:1},"",""),children:[(0,eo.Y)(ev.A,{}),(0,eo.Y)(ty.o.Text,{ellipsis:!0,size:"md",children:t})]}),(0,eo.FD)("div",{css:n6,children:[n.length>0?(0,eo.Y)(n2,{itemKey:n[0].key,itemValue:n[0].value}):null,n.length>1?(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.tag-count.hover-tooltip",content:n.slice(1).map(e=>{let{key:t,value:n}=e;return(0,eo.Y)("span",{css:n4,children:`${t}: ${n}`},t)}),children:(0,eo.FD)(t$.v,{componentId:"shared.model-trace-explorer.tag-count",css:n7,children:["+",n.length-1]})}):null]})]})}function n9(e){let{text:t,metadata:n}=e,[r,o]=(0,l.useState)(!1),i=e$(n),a=$(),s=Z(),c=(0,l.useCallback)(e=>{M(a,s,{eventType:"component_click",eventEntity:{entityType:"component",entitySubType:"div",entityId:`shared.model-trace-explorer.retriever-document-${e}`},eventPayload:{interactionSubject:!0}})},[a,s]);return r?(0,eo.Y)(n5,{text:t,metadataTags:i,setExpanded:o,logDocumentClick:c}):(0,eo.Y)(n8,{text:t,metadataTags:i,setExpanded:o,logDocumentClick:c})}var re=n(17021),rt=n(17784),rn=n(57477),rr={name:"s5xdrg",styles:"display:flex;align-items:center"},ro={name:"s5xdrg",styles:"display:flex;align-items:center"};function ri(e){let{shouldRenderMarkdown:t,setShouldRenderMarkdown:n}=e,{theme:r}=(0,c.wn)();return(0,eo.FD)(re.d,{"data-testid":"model-trace-explorer-render-mode-toggle",name:"render-mode",size:"small",componentId:`shared.model-trace-explorer.toggle-markdown-rendering-${!t}`,value:t,onChange:e=>{n(e.target.value)},children:[(0,eo.Y)(re.E,{"data-testid":"model-trace-explorer-render-raw-input-button",value:!1,children:(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.raw-input-rendering-tooltip",content:(0,eo.Y)(tG.sA,{id:"NMxAot",defaultMessage:"Raw input"}),children:(0,eo.Y)("div",{css:rr,children:(0,eo.Y)(rt.A,{css:(0,o.AH)({fontSize:r.typography.fontSizeLg},"","")})})})}),(0,eo.Y)(re.E,{"data-testid":"model-trace-explorer-render-default-button",value:!0,children:(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.default-rendering-tooltip",content:(0,eo.Y)(tG.sA,{id:"xwe7WH",defaultMessage:"Default rendering"}),children:(0,eo.Y)("div",{css:ro,children:(0,eo.Y)(rn.A,{css:(0,o.AH)({fontSize:r.typography.fontSizeLg},"","")})})})})]})}var ra={name:"1s4awny",styles:"display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:100%"};function rl(e){let{activeSpan:t,className:n,searchFilter:r,activeMatch:a}=e,{theme:s}=(0,c.wn)(),[d,u]=(0,l.useState)(!0),p=(0,l.useMemo)(()=>e$(t.inputs),[t]),g=t.outputs,h=p.length>0,m=!(0,i.isNil)(a)&&a.span.key===t.key,f=m&&"outputs"===a.section;return(0,eo.FD)("div",{className:n,"data-testid":"model-trace-explorer-retriever-span-view",children:[h&&(0,eo.Y)(nq,{sectionKey:"input",title:(0,eo.Y)(tG.sA,{id:"/TX1Az",defaultMessage:"Inputs"}),children:p.map((e,t)=>{let{key:n,value:o}=e;return(0,eo.Y)(nM,{title:n,data:o,searchFilter:r,activeMatch:a,containsActiveMatch:m&&"inputs"===a.section&&a.key===n},n||t)})}),(0,eo.Y)(nq,{sectionKey:"output",title:(0,eo.FD)("div",{css:ra,children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",gap:s.spacing.sm},"",""),children:[(0,eo.Y)(tG.sA,{id:"ThYYz6",defaultMessage:"Documents"}),(0,eo.Y)(t$.v,{componentId:"shared.model-trace-explorer.document-count",children:g.length})]}),!f&&(0,eo.Y)(ri,{shouldRenderMarkdown:d,setShouldRenderMarkdown:u})]}),children:d&&!f?(0,eo.Y)("div",{css:(0,o.AH)({border:`1px solid ${s.colors.border}`,borderRadius:s.legacyBorders.borderRadiusMd},"",""),children:g.map((e,t)=>(0,eo.Y)("div",{css:(0,o.AH)({borderBottom:t!==g.length-1?`1px solid ${s.colors.border}`:""},"",""),children:(0,eo.Y)(n9,{text:e.page_content,metadata:e.metadata},t)},t))}):(0,eo.Y)("div",{css:(0,o.AH)({border:`1px solid ${s.colors.border}`,borderRadius:s.legacyBorders.borderRadiusMd,padding:s.spacing.md,paddingBottom:0},"",""),children:(0,eo.Y)(nM,{title:"",data:JSON.stringify(g,null,2),searchFilter:r,activeMatch:a,containsActiveMatch:m&&"outputs"===a.section})})})]})}function rs(e){let{activeSpan:t,className:n,searchFilter:r,activeMatch:a}=e,{theme:l}=(0,c.wn)();if(!(0,i.isNil)(t)&&eV(t))return(0,eo.Y)("div",{css:(0,o.AH)({overflowY:"auto",padding:l.spacing.md},"",""),className:n,"data-testid":"model-trace-explorer-content-tab",children:(0,eo.Y)(rl,{activeSpan:t,className:n,searchFilter:r,activeMatch:a})});return(0,eo.Y)("div",{css:(0,o.AH)({overflowY:"auto",padding:l.spacing.md},"",""),className:n,"data-testid":"model-trace-explorer-content-tab",children:(0,eo.Y)(n0,{activeSpan:t,className:n,searchFilter:r,activeMatch:a})})}var rc=n(84260);function rd(e){let{activeSpan:t,searchFilter:n,activeMatch:r}=e,{theme:a}=(0,c.wn)(),{events:l}=t,s=!(0,i.isNil)(r)&&r.span.key===t.key;if(!Array.isArray(l)||0===l.length)return(0,eo.Y)("div",{css:(0,o.AH)({marginTop:a.spacing.md},"",""),children:(0,eo.Y)(tj.S,{description:(0,eo.Y)(tG.sA,{id:"xnr5KT",defaultMessage:"No events found"})})});return(0,eo.Y)("div",{css:(0,o.AH)({padding:a.spacing.md},"",""),children:l.map((e,t)=>{let i=e.attributes,l="exception"===e.name?(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(rc.A,{css:(0,o.AH)({marginRight:a.spacing.sm},"",""),color:"danger"}),(0,eo.Y)(ty.o.Text,{color:"error",bold:!0,children:"Exception"})]}):e.name;if(!i)return null;return(0,eo.Y)(nq,{sectionKey:e.name,title:l,withBorder:!0,children:Object.keys(i).map(o=>{let a=eX(e.name,t,o);return(0,eo.Y)(nM,{title:o,data:JSON.stringify(i[o],null,2),searchFilter:n,activeMatch:r,containsActiveMatch:s&&"events"===r.section&&r.key===a,initialRenderMode:em.TEXT},a)})},`${e.name}-${t}`)})})}function ru(e){let{count:t}=e,{theme:n}=(0,c.wn)();return(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",alignItems:"center",justifyContent:"center",borderRadius:n.typography.fontSizeBase,height:n.typography.fontSizeBase,backgroundColor:n.colors.actionDangerPrimaryBackgroundDefault,padding:n.spacing.xs,marginLeft:n.spacing.xs,boxSizing:"border-box"},"",""),children:(0,eo.Y)("span",{css:(0,o.AH)({color:n.colors.actionPrimaryTextDefault,fontSize:11},"",""),children:t})})}let rp=s().memo(function(e){let{activeSpan:t,searchFilter:n,activeMatch:r,activeTab:a,setActiveTab:l}=e,{theme:s}=(0,c.wn)(),d={flex:1,marginTop:-s.spacing.md,overflowY:"auto"};if((0,i.isNil)(t))return(0,eo.Y)(tj.S,{description:"Please select a span to view more information"});let u=eq(t);return(0,eo.FD)(tW.Root,{componentId:"shared.model-trace-explorer.right-pane-tabs",css:(0,o.AH)({display:"flex",flexDirection:"column",flex:1,borderLeft:`1px solid ${s.colors.border}`,minWidth:300},"",""),value:a,onValueChange:e=>l(e),children:[(0,eo.FD)(tW.List,{css:(0,o.AH)({padding:`0px ${s.spacing.md}px`,boxSizing:"border-box"},"",""),children:[t.chatMessages&&(0,eo.Y)(tW.Trigger,{value:"chat",children:(0,eo.Y)(tG.sA,{id:"BkDhmT",defaultMessage:"Chat"})}),(0,eo.Y)(tW.Trigger,{value:"content",children:(0,eo.Y)(tG.sA,{id:"KW2dOC",defaultMessage:"Inputs / Outputs"})}),(0,eo.Y)(tW.Trigger,{value:"attributes",children:"Attributes"}),(0,eo.FD)(tW.Trigger,{value:"events",children:["Events ",u>0&&(0,eo.Y)(ru,{count:u})]})]}),t.chatMessages&&(0,eo.Y)(tW.Content,{css:d,value:"chat",children:(0,eo.Y)(nJ,{chatMessages:t.chatMessages,chatTools:t.chatTools})}),(0,eo.Y)(tW.Content,{css:d,value:"content",children:(0,eo.Y)(rs,{activeSpan:t,searchFilter:n,activeMatch:r})}),(0,eo.Y)(tW.Content,{css:d,value:"attributes",children:(0,eo.Y)(nY,{activeSpan:t,searchFilter:n,activeMatch:r})}),(0,eo.Y)(tW.Content,{css:d,value:"events",children:(0,eo.Y)(rd,{activeSpan:t,searchFilter:n,activeMatch:r})})]})});var rg=n(54198),rh=n(67630),rm=n(95381),rf=n(37370),rv=n(16389),rE=n(46249);let ry=e=>{let{spanFilterState:t,setSpanFilterState:n}=e,{theme:r}=(0,c.wn)();return(0,eo.FD)(rm.Root,{componentId:"shared.model-trace-explorer.timeline-tree-filter-popover",children:[(0,eo.Y)(rm.Trigger,{asChild:!0,children:(0,eo.Y)(tA.$n,{componentId:"shared.model-trace-explorer.timeline-tree-filter-button",icon:(0,eo.Y)(rf.A,{}),size:"small",children:(0,eo.Y)(tG.sA,{id:"Dm4Ez0",defaultMessage:"Filter"})})}),(0,eo.Y)(rm.Content,{align:"start",children:(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:r.spacing.sm,paddingBottom:r.spacing.xs},"",""),children:[(0,eo.Y)(ty.o.Text,{bold:!0,children:(0,eo.Y)(tG.sA,{id:"oy2LTl",defaultMessage:"Filter"})}),(0,eo.Y)(ty.o.Text,{color:"secondary",children:(0,eo.Y)(tG.sA,{id:"rgSaFx",defaultMessage:"Span type"})}),Object.entries(t.spanTypeDisplayState).map(e=>{let[i,a]=e,l=(0,eo.Y)(eN,{type:ew(i)});return(0,eo.FD)(rv.Sc,{componentId:`shared.model-trace-explorer.toggle-span-filter_${i}-${!a}`,style:{width:"100%"},isChecked:a,onChange:()=>n({...t,spanTypeDisplayState:{...t.spanTypeDisplayState,[i]:!a}}),children:[l,(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({marginLeft:r.spacing.xs},"",""),children:function(e){switch(e){case ep.LLM:return"LLM";case ep.CHAIN:return"Chain";case ep.AGENT:return"Agent";case ep.TOOL:return"Tool";case ep.CHAT_MODEL:return"Chat model";case ep.RETRIEVER:return"Retriever";case ep.PARSER:return"Parser";case ep.EMBEDDING:return"Embedding";case ep.RERANKER:return"Reranker";case ep.FUNCTION:return"Function";case ep.UNKNOWN:return"Unknown";default:return e}}(i)})]},i)}),(0,eo.Y)(ty.o.Text,{color:"secondary",children:(0,eo.Y)(tG.sA,{id:"SrV58C",defaultMessage:"Settings"})}),(0,eo.FD)(rv.Sc,{componentId:`shared.model-trace-explorer.toggle-show-parents_${!t.showParents}`,style:{width:"100%"},isChecked:t.showParents,onChange:()=>n({...t,showParents:!t.showParents}),children:[(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({marginRight:r.spacing.xs},"",""),children:(0,eo.Y)(tG.sA,{id:"nYSJgH",defaultMessage:"Show all parent spans"})}),(0,eo.Y)(rE.I,{componentId:"shared.model-trace-explorer.show-parents-tooltip",content:(0,eo.Y)(tG.sA,{id:"H9MulE",defaultMessage:"Always show parents of matched spans, regardless of filter conditions"})})]}),(0,eo.FD)(rv.Sc,{componentId:`shared.model-trace-explorer.toggle-show-parents_${!t.showExceptions}`,style:{width:"100%"},isChecked:t.showExceptions,onChange:()=>n({...t,showExceptions:!t.showExceptions}),children:[(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({marginRight:r.spacing.xs},"",""),children:(0,eo.Y)(tG.sA,{id:"/vhBCt",defaultMessage:"Show exceptions"})}),(0,eo.Y)(rE.I,{componentId:"shared.model-trace-explorer.show-exceptions-tooltip",content:(0,eo.Y)(tG.sA,{id:"60b0vW",defaultMessage:"Always show spans with exceptions, regardless of filter conditions"})})]})]})})]})};var rA={name:"s079uh",styles:"margin-top:2px"};let rb=e=>{let{showTimelineInfo:t,setShowTimelineInfo:n,spanFilterState:r,setSpanFilterState:i}=e,{theme:a}=(0,c.wn)();return(0,eo.FD)("div",{css:(0,o.AH)({padding:`${a.spacing.xs}px ${a.spacing.sm}px`,paddingBottom:3,borderBottom:`1px solid ${a.colors.border}`,boxSizing:"border-box",paddingLeft:a.spacing.sm,alignItems:"center",display:"flex",justifyContent:"space-between"},"",""),children:[(0,eo.Y)(ty.o.Text,{bold:!0,children:(0,eo.Y)(tG.sA,{id:"w48V74",defaultMessage:"Trace breakdown"})}),(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",gap:a.spacing.sm},"",""),children:[(0,eo.Y)(ry,{spanFilterState:r,setSpanFilterState:i}),(0,eo.FD)(re.d,{name:"size-story",value:t,onChange:e=>{n(e.target.value)},size:"small",componentId:"shared.model-trace-explorer.toggle-show-timeline",children:[(0,eo.Y)(re.E,{"data-testid":"hide-timeline-info-button",value:!1,children:(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.hide-timeline-info-tooltip",content:(0,eo.Y)(tG.sA,{id:"E9fsRD",defaultMessage:"Show span tree"}),children:(0,eo.Y)(rg.A,{css:rA})})}),(0,eo.Y)(re.E,{"data-testid":"show-timeline-info-button",value:!0,children:(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.show-timeline-info-tooltip",content:(0,eo.Y)(tG.sA,{id:"r5t1Je",defaultMessage:"Show execution timeline"}),children:(0,eo.Y)(rh.A,{})})})]})]})]})},r_=e=>{let{active:t}=e,{theme:n}=(0,c.wn)(),r=t?n.colors.blue500:n.colors.border;return(0,eo.Y)("div",{css:(0,o.AH)({position:"absolute",left:"100%",bottom:0,width:9,height:n.spacing.md,boxSizing:"border-box",borderTopRightRadius:n.borders.borderRadiusMd,borderTop:`1px solid ${r}`,borderRight:`1px solid ${r}`,zIndex:tC.LOW},"","")})},rT=e=>{let{active:t}=e,{theme:n}=(0,c.wn)(),r=t?n.colors.blue500:n.colors.border;return(0,eo.Y)("div",{css:(0,o.AH)({position:"absolute",left:"50%",top:0,width:8,height:n.spacing.md,boxSizing:"border-box",borderBottomLeftRadius:n.borders.borderRadiusMd,borderBottom:`1px solid ${r}`,borderLeft:`1px solid ${r}`,zIndex:t?tC.NORMAL:tC.LOW},"","")})},rS=e=>{let{active:t}=e,{theme:n}=(0,c.wn)(),r=t?n.colors.blue500:n.colors.border;return(0,eo.Y)("div",{css:(0,o.AH)({position:"absolute",width:8,left:"50%",height:32,borderLeft:`1px solid ${r}`,boxSizing:"border-box"},"","")})},rx=e=>{let{isActiveSpan:t,isInActiveChain:n,linesToRender:r,hasChildren:i,isExpanded:a}=e;if(0===r.length)return(0,eo.Y)("div",{css:(0,o.AH)({width:0,height:32,boxSizing:"border-box",position:"relative"},"",""),children:i&&(0,eo.Y)(r_,{active:n&&!t})});return(0,eo.Y)(eo.FK,{children:r.map((e,l)=>{let{shouldRender:s,isActive:c}=e;return(0,eo.FD)("div",{css:(0,o.AH)({width:16,height:32,boxSizing:"border-box",position:"relative"},"",""),children:[s&&(0,eo.Y)(rS,{active:c}),l===r.length-1&&(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(rT,{active:n}),i&&a&&(0,eo.Y)(r_,{active:n&&!t})]})]},l)})})};var rI={name:"1fttcpj",styles:"display:flex;flex-direction:column"};let rR=e=>{var t;let{span:n,children:r}=e,{theme:i}=(0,c.wn)(),a=ew(null!==(t=n.type)&&void 0!==t?t:ep.UNKNOWN),l=i.isDarkMode?i.colors.grey800:i.colors.grey100,s=i.isDarkMode?i.colors.grey500:i.colors.grey350;return(0,eo.Y)(nH.m,{componentId:"shared.model-trace-explorer.timeline-tree-node-tooltip",hideWhenDetached:!1,content:(0,eo.FD)("div",{css:rI,children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",gap:i.spacing.xs,alignItems:"center",overflow:"hidden",wordBreak:"break-all"},"",""),children:[(0,eo.Y)(eN,{type:a,isInTooltip:!0}),(0,eo.Y)("span",{css:(0,o.AH)({color:l},"",""),children:n.title}),(0,eo.Y)("span",{css:(0,o.AH)({marginLeft:i.spacing.xs,color:s,fontSize:i.typography.fontSizeSm},"",""),children:tL(n.end-n.start)})]}),(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",color:l},"",""),children:[(0,eo.Y)(tG.sA,{id:"YV6xFB",defaultMessage:"Start:"})," ",tL(n.start)," | ",(0,eo.Y)(tG.sA,{id:"q90jVU",defaultMessage:"End:"})," ",tL(n.end)]})]}),side:"right",maxWidth:700,children:r})};var rN={name:"wrqjbz",styles:"display:flex;flex-direction:row;align-items:center;overflow:hidden"},rw={name:"adzntw",styles:"overflow:hidden;white-space:nowrap;text-overflow:ellipsis;flex:1"};let rC=e=>{var t,n;let{node:r,selectedKey:i,expandedKeys:a,setExpandedKeys:l,traceStartTime:s,traceEndTime:d,onSelect:u,linesToRender:p}=e,g=a.has(r.key),{theme:h}=(0,c.wn)(),m=(null!==(t=r.children)&&void 0!==t?t:[]).length>0,f=i===r.key,v=tP(r,String(i)),E=eq(r)>0,y=f?h.colors.actionDefaultBackgroundHover:"transparent";return(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(rR,{span:r,children:(0,eo.Y)("div",{"data-testid":`timeline-tree-node-${r.key}`,css:(0,o.AH)({display:"flex",flexDirection:"column",width:"100%",cursor:"pointer",boxSizing:"border-box",backgroundColor:y,":hover":{backgroundColor:h.colors.actionDefaultBackgroundHover},":active":{backgroundColor:h.colors.actionDefaultBackgroundPress}},"",""),onClick:()=>{null==u||u(r)},children:(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",alignItems:"center",padding:`0px ${h.spacing.sm}px`,justifyContent:"space-between",overflow:"hidden"},"",""),children:(0,eo.FD)("div",{css:rN,children:[m?(0,eo.Y)(tA.$n,{size:"small","data-testid":`toggle-span-expanded-${r.key}`,css:(0,o.AH)({flexShrink:0,marginRight:h.spacing.xs},"",""),icon:g?(0,eo.Y)(t_.A,{}):(0,eo.Y)(nD.A,{}),onClick:e=>{e.stopPropagation();let t=new Set(a);g?t.delete(r.key):t.add(r.key),l(t)},componentId:"shared.model-trace-explorer.toggle-span"}):(0,eo.Y)("div",{css:(0,o.AH)({width:24,marginRight:h.spacing.xs},"","")}),(0,eo.Y)(rx,{isActiveSpan:f,isInActiveChain:v>-1,linesToRender:p,hasChildren:m,isExpanded:g}),(0,eo.FD)("span",{css:(0,o.AH)({flexShrink:0,position:"relative",marginRight:h.spacing.xs,borderRadius:h.borders.borderRadiusSm,border:`1px solid ${v>-1?h.colors.blue500:h.colors.backgroundSecondary}`},"",""),children:[E&&(0,eo.Y)("div",{css:(0,o.AH)({position:"absolute",top:-h.spacing.xs,right:-h.spacing.xs,height:h.spacing.sm,width:h.spacing.sm,borderRadius:h.borders.borderRadiusSm,backgroundColor:h.colors.actionDangerPrimaryBackgroundDefault,zIndex:tC.HIGH},"","")}),r.icon]}),(0,eo.Y)(ty.o.Text,{color:E?"error":"primary",css:rw,children:r.title})]})})})}),g&&(null===(n=r.children)||void 0===n?void 0:n.map((e,t)=>{var n,o;return(0,eo.Y)(rC,{node:e,expandedKeys:a,setExpandedKeys:l,selectedKey:i,traceStartTime:s,traceEndTime:d,onSelect:u,linesToRender:p.concat({shouldRender:t<(null!==(n=null===(o=r.children)||void 0===o?void 0:o.length)&&void 0!==n?n:0)-1,isActive:t<v})},e.key)}))]})};var rO={name:"1j2qjqs",styles:"flex:1;overflow:hidden;white-space:nowrap;text-overflow:ellipsis"};let rM=e=>{var t;let{node:n,selectedKey:r,leftOffset:i,width:a,onSelect:s,expandedKeys:d,setExpandedKeys:u}=e,{theme:p}=(0,c.wn)(),g=(0,l.useRef)(null),h=(0,l.useRef)(null),m=r===n.key?p.colors.actionDefaultBackgroundHover:"transparent",f=(null!==(t=n.children)&&void 0!==t?t:[]).length>0,v=d.has(n.key);return(0,l.useLayoutEffect)(()=>{if(!g.current||!h.current)return;Math.max(g.current.offsetWidth,h.current.offsetWidth)<a-p.spacing.sm?(g.current.style.display="inline",h.current.style.display="none"):(g.current.style.display="none",h.current.style.display="inline")},[p.spacing.sm,a]),(0,eo.Y)(rR,{span:n,children:(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",cursor:"pointer",boxSizing:"border-box",paddingLeft:p.spacing.xs,paddingRight:p.spacing.sm,paddingTop:p.spacing.xs,paddingBottom:p.spacing.xs,backgroundColor:m,alignItems:"center",":hover":{backgroundColor:p.colors.actionDefaultBackgroundHover},":active":{backgroundColor:p.colors.actionDefaultBackgroundPress}},"",""),onClick:()=>null==s?void 0:s(n),children:[f?(0,eo.Y)(tA.$n,{size:"small","data-testid":`toggle-timeline-span-expanded-${n.key}`,css:(0,o.AH)({flexShrink:0,marginRight:p.spacing.xs},"",""),icon:v?(0,eo.Y)(t_.A,{}):(0,eo.Y)(nD.A,{}),onClick:e=>{e.stopPropagation();let t=new Set(d);v?t.delete(n.key):t.add(n.key),u(t)},componentId:"shared.model-trace-explorer.toggle-timeline-span"}):(0,eo.Y)("div",{css:(0,o.AH)({width:24,marginRight:p.spacing.xs},"","")}),(0,eo.Y)("div",{css:(0,o.AH)({width:i,flexShrink:0},"","")}),(0,eo.Y)("div",{css:(0,o.AH)({position:"relative",width:a,height:p.typography.lineHeightBase,backgroundColor:p.colors.blue600,borderRadius:p.borders.borderRadiusSm,flexShrink:0},"",""),children:(0,eo.Y)(ty.o.Text,{children:(0,eo.Y)("span",{ref:g,css:(0,o.AH)({marginLeft:p.spacing.xs,color:p.colors.white,display:"none"},"",""),children:n.title})})}),(0,eo.FD)("div",{css:rO,children:[(0,eo.Y)(ty.o.Text,{children:(0,eo.Y)("span",{ref:h,css:(0,o.AH)({marginLeft:p.spacing.xs,color:p.colors.textPrimary},"",""),children:n.title})}),(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({marginLeft:p.spacing.xs},"",""),color:"secondary",children:tL(n.end-n.start)})]})]},n.key)})};var rY={name:"1c2ejnx",styles:"display:flex;flex-direction:column;position:relative;box-sizing:border-box"},rD={name:"bjn8wh",styles:"position:relative"},rL={name:"xyyuvf",styles:"position:absolute;height:100%;width:100%"};let rP=e=>{var t,n;let{nodes:r,selectedKey:i,onSelect:a,traceStartTime:s,traceEndTime:d,expandedKeys:u,setExpandedKeys:p}=e,{theme:g}=(0,c.wn)(),h=(0,l.useRef)(null),m=null!==(t=null===(n=tp({ref:h}))||void 0===n?void 0:n.width)&&void 0!==t?t:0,f=(0,l.useMemo)(()=>(function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:60,o=t-e;if(o<=0||n<=0)return[];let i=Math.floor(n/r);if(i<1)return[];let a=o/i,l=Math.pow(10,Math.floor(Math.log10(a))),s=a/l,c=(s<=1?1:s<=2?2:s<=5?5:10)*l,d=t+2*c,u=Math.ceil(e/c)*c,p=[];for(let e=u;e<=d;e+=c)p.push(Number(e.toFixed(10)));return p})(s,d,m),[d,s,m]),v=Math.min(...f),E=(f[f.length-1]+f[f.length-2])/2,y=f.slice(0,-1),A=e=>(e-v)/(E-v)*m;return(0,eo.FD)("div",{css:rY,children:[(0,eo.Y)("div",{ref:h,css:(0,o.AH)({display:"flex",width:"100%",flexDirection:"row",height:g.typography.lineHeightBase,paddingLeft:32,paddingRight:g.spacing.lg,boxSizing:"border-box",position:"sticky",top:0,backgroundColor:g.colors.backgroundPrimary,zIndex:tC.HIGH},"",""),children:(0,eo.Y)("div",{"data-testid":"time-marker-area",css:rD,children:y.map(e=>(0,eo.Y)(ty.o.Text,{css:(0,o.AH)({position:"absolute",transform:"translateX(-50%)",left:A(e),whiteSpace:"nowrap"},"",""),children:tL(e)},e))})}),(0,eo.Y)("div",{css:(0,o.AH)({flex:1,pointerEvents:"none",zIndex:-1},"",""),children:(0,eo.Y)("div",{css:rL,children:y.map(e=>(0,eo.Y)("div",{css:(0,o.AH)({position:"absolute",left:A(e)+32,borderRight:`1px solid ${g.colors.border}`,height:"100%"},"","")},e))})}),r.map(e=>{let t=A(e.start),n=A(e.end)-t;return(0,eo.Y)(rM,{selectedKey:i,onSelect:a,node:e,leftOffset:t,width:n,expandedKeys:u,setExpandedKeys:p},e.key)})]})};var rk={name:"1v8q7xf",styles:"flex:1;overflow:auto;min-height:100%;box-sizing:border-box;display:flex;flex-direction:column"},rF={name:"1jyk3dc",styles:"overflow:hidden;display:flex;flex-direction:column;flex:1"},rH={name:"13ku56z",styles:"display:flex;flex-direction:column;height:100%"},rU={name:"9nf0yq",styles:"flex:1;overflow-y:auto;display:flex"};let rB=e=>{let{rootNodes:t,selectedNode:n,setSelectedNode:r,traceStartTime:i,traceEndTime:a,expandedKeys:s,setExpandedKeys:d,spanFilterState:u,setSpanFilterState:p,className:g}=e,{theme:h}=(0,c.wn)(),m=$(),f=Z(),v=(0,l.useCallback)(e=>{M(m,f,{eventType:"component_click",eventEntity:{entityType:"component",entitySubType:"div",entityId:`shared.model-trace-explorer.${e.type}-span-click`},eventPayload:{interactionSubject:!0}})},[m,f]),E=(0,l.useCallback)(e=>{null==r||r(e),v(e)},[v,r]),[y,A]=(0,l.useState)(!1),b=(0,l.useMemo)(()=>tO(t,s),[t,s]),_=(0,l.useMemo)(()=>{var e;return(0,eo.Y)("div",{css:rk,children:y?(0,eo.Y)(rP,{nodes:b,selectedKey:null!==(e=null==n?void 0:n.key)&&void 0!==e?e:"",onSelect:E,traceStartTime:i,traceEndTime:a,expandedKeys:s,setExpandedKeys:d}):t.map(e=>{var t;return(0,eo.Y)(rC,{node:e,expandedKeys:s,setExpandedKeys:d,selectedKey:null!==(t=null==n?void 0:n.key)&&void 0!==t?t:"",traceStartTime:i,traceEndTime:a,onSelect:E,linesToRender:[]},e.key)})})},[y,b,null==n?void 0:n.key,E,i,a,t,s,d]);return(0,eo.Y)("div",{css:(0,o.AH)({height:"100%",borderRadius:h.legacyBorders.borderRadiusMd,overflow:"hidden",display:"flex"},"",""),className:g,children:(0,eo.Y)("div",{css:rF,children:(0,eo.FD)("div",{css:rH,children:[(0,eo.Y)(rb,{showTimelineInfo:y,setShowTimelineInfo:A,spanFilterState:u,setSpanFilterState:p}),t.length>0?(0,eo.Y)("div",{css:rU,children:_}):(0,eo.Y)("div",{css:(0,o.AH)({flex:1,display:"flex",justifyContent:"center",padding:h.spacing.md,paddingTop:h.spacing.lg},"",""),children:(0,eo.Y)(tG.sA,{id:"4pXg9j",defaultMessage:"No results found. Try using a different search term."})})]})})})},rj=()=>{if(window.innerWidth<=768)return{ratio:.5,showGantt:!1};if(window.innerWidth<=1280)return{ratio:.33,showGantt:!1};if(window.innerWidth<=1440)return{ratio:.4,showGantt:!0};return{ratio:.33,showGantt:!0}};var rW={name:"13ku56z",styles:"display:flex;flex-direction:column;height:100%"},rG={name:"82a6rk",styles:"flex:1"};let rz=e=>{var t,n,r,a;let{modelTrace:s,className:d,selectedSpanId:u,onSelectSpan:p}=e,{theme:g}=(0,c.wn)(),{ratio:h}=rj(),m=(0,l.useRef)(null),f=(0,l.useMemo)(()=>(function(e){var t,n;let r=null!==(t=null===(n=e.trace_data)||void 0===n?void 0:n.spans)&&void 0!==t?t:e.data.spans,o={},a={};r.forEach(e=>{let t=eH(e);o[t]=e,a[t]=[]}),r.forEach(e=>{let t=eH(e),n=eU(e);if(n){if(!a[n])throw Error("Tree structure is malformed!");a[n].push(t)}});let l=r.find(e=>!eU(e));if((0,i.isNil)(l))return null;return function e(t){var n,r,i;let s=o[t],c=Number(eB(l)),d=Number(ej(l)),u=a[t].map(e);if(eF(s)||"parent_id"in s)return eL(s,c,d,u);let p=null!==(n=s.span_type)&&void 0!==n?n:ep.UNKNOWN;return{title:s.name,icon:(0,eo.Y)(eN,{type:ew(p)}),type:p,key:s.context.span_id,start:Number(s.start_time)-c,end:Number(null!==(r=s.end_time)&&void 0!==r?r:d)-c,children:u,inputs:s.inputs,outputs:s.outputs,attributes:s.attributes,events:s.events,parentId:null!==(i=s.parent_id)&&void 0!==i?i:s.parent_span_id}}(eH(l))})(s),[s]),[v,E]=(0,l.useState)(500),y=function e(t,n){var r;if((0,i.isNil)(n)||(0,i.isNil)(t))return;if(t.key===n)return t;for(let o of null!==(r=t.children)&&void 0!==r?r:[]){let t=e(o,n);if(t)return t}}(f,u),[A,b]=(0,l.useState)(null!==(t=null!=y?y:f)&&void 0!==t?t:void 0),[_,T]=(0,l.useState)(null!=A&&A.chatMessages?"chat":"content"),{expandedKeys:S,setExpandedKeys:x}=tD({rootNodes:f?[f]:[],initialExpandDepth:tw}),{matchData:I,searchFilter:R,setSearchFilter:N,spanFilterState:w,setSpanFilterState:C,filteredTreeNodes:O,handleNextSearchMatch:M,handlePreviousSearchMatch:Y}=tB({treeNode:f,selectedNode:A,setSelectedNode:b,setActiveTab:T,setExpandedKeys:x});(0,l.useLayoutEffect)(()=>{x(new Set((0,i.values)(tY(O,tw)).map(e=>e.key)))},[O,x]);let D=(0,l.useMemo)(()=>Math.max(275,16*Math.max(...O.map(tk))+7*g.spacing.lg),[O,g.spacing.lg]);return(0,eo.Y)(ea,{id:K.MLFLOW_CONTEXT,tags:{tagTracesRequestId:null!==(n=s.info.request_id)&&void 0!==n?n:""},children:(0,eo.FD)("div",{css:rW,className:d,children:[(0,eo.Y)("div",{css:(0,o.AH)({padding:g.spacing.xs,borderBottom:`1px solid ${g.colors.border}`,borderTop:`1px solid ${g.colors.border}`},"",""),children:(0,eo.Y)(tS,{searchFilter:R,setSearchFilter:N,matchData:I,handleNextSearchMatch:M,handlePreviousSearchMatch:Y})}),(0,eo.Y)(tf,{ref:m,initialRatio:h,paneWidth:v,setPaneWidth:E,leftChild:(0,eo.Y)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",flex:1,minWidth:D},"",""),children:(0,eo.Y)(rB,{rootNodes:O,selectedNode:A,traceStartTime:null!==(r=null==f?void 0:f.start)&&void 0!==r?r:0,traceEndTime:null!==(a=null==f?void 0:f.end)&&void 0!==a?a:0,setSelectedNode:e=>{b(e),T(null!=e&&e.chatMessages?"chat":"content"),(0,i.isString)(null==e?void 0:e.key)&&(null==p||p(null==e?void 0:e.key))},css:rG,expandedKeys:S,setExpandedKeys:x,spanFilterState:w,setSpanFilterState:C})}),leftMinWidth:D,rightChild:(0,eo.Y)(rp,{activeSpan:A,searchFilter:R,activeMatch:I.match,activeTab:_,setActiveTab:T}),rightMinWidth:300})]})})};rz.Skeleton=e=>{let{label:t}=e,{theme:n}=(0,c.wn)();return(0,eo.FD)("div",{css:tR,children:[(0,eo.FD)("div",{css:tN,children:[(0,eo.Y)("div",{css:(0,o.AH)({padding:n.spacing.sm,borderBottom:`1px solid ${n.colors.border}`},"",""),children:(0,eo.Y)(tx.o,{label:t})}),(0,eo.FD)("div",{css:(0,o.AH)({borderRadius:n.legacyBorders.borderRadiusMd,overflow:"hidden",display:"flex"},"",""),children:[(0,eo.Y)("div",{css:(0,o.AH)({flex:1,padding:n.spacing.sm,borderRight:`1px solid ${n.colors.border}`},"",""),children:(0,eo.Y)(tI.Q,{lines:5})}),(0,eo.Y)("div",{css:(0,o.AH)({flex:2,padding:n.spacing.sm},"",""),children:(0,eo.Y)(tI.Q,{lines:5})})]})]}),(0,eo.Y)("div",{css:(0,o.AH)({padding:n.spacing.md,overflowY:"auto",flex:1},"",""),children:(0,eo.Y)(tI.Q,{lines:12})})]})};var r$=n(89481),rK=n(89815);function rV(e,t,n,r,o,i,a){try{var l=e[i](a),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,o)}function rX(e){return rq.apply(this,arguments)}function rq(){var e;return e=function*(e){try{let t=yield fetch(`/ajax-api/2.0/mlflow/get-trace-artifact?request_id=${e}`),n=yield t.text(),r=JSON.parse(n);if(r.spans)return{info:{request_id:e},data:r};if(r.error_code)return r.message;return"Unknown error occurred"}catch(e){if(e instanceof Error)return e.message;if("string"==typeof e)return e;return"Unknown error occurred"}},(rq=function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){rV(i,r,o,a,l,"next",e)}function l(e){rV(i,r,o,a,l,"throw",e)}a(void 0)})}).apply(this,arguments)}function rJ(e,t,n,r,o,i,a){try{var l=e[i](a),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,o)}let rQ=(e,t)=>{let n=new URLSearchParams;return n.append("selectedTraceId",e),n.append("compareRunsMode","TRACES"),`/#/experiments/${t}?${n.toString()}`};var rZ={name:"1elrbzk",styles:"width:calc(100% - 2px)"},r0={name:"inxa61",styles:"flex:1;overflow:hidden"};let r1=()=>{let e=(0,l.useMemo)(()=>new URLSearchParams(window.location.search).getAll("trace_id"),[]),t=(0,l.useMemo)(()=>new URLSearchParams(window.location.search).getAll("experiment_id"),[]),[n,r]=(0,l.useState)(e.length>0?0:null),[i,a]=(0,l.useState)(null),[s,d]=(0,l.useState)(!0),[u,p]=(0,l.useState)(null),{theme:g}=(0,c.wn)();if((0,l.useEffect)(()=>{(function(){var t,r=(t=function*(){if(null===n)return;d(!0);let t=e[n],r=yield rX(t);"string"==typeof r?p(r):a(r),d(!1)},function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(e){rJ(i,r,o,a,l,"next",e)}function l(e){rJ(i,r,o,a,l,"throw",e)}a(void 0)})});return function(){return r.apply(this,arguments)}})()()},[n,e]),0===e.length||null===n)return null;if(s)return(0,eo.Y)("div",{css:rZ,children:(0,eo.Y)(ty.o.Text,{color:"secondary",children:(0,eo.Y)(tG.sA,{id:"JJFZ1V",defaultMessage:"Fetching trace data..."})})});if(!i)return(0,eo.Y)("div",{css:(0,o.AH)({paddingTop:g.spacing.md,width:"calc(100% - 2px)"},"",""),children:(0,eo.Y)(tj.S,{image:(0,eo.Y)(r$.A,{}),description:(0,eo.FD)(eo.FK,{children:[(0,eo.Y)(ty.o.Paragraph,{children:(0,eo.Y)(tG.sA,{id:"UD4Z+e",defaultMessage:"An error occurred while attempting to fetch trace data (ID: {traceId}). Please ensure that the MLflow tracking server is running, and that the trace data exists. Error details:",values:{traceId:e[n]}})}),u&&(0,eo.Y)(ty.o.Paragraph,{children:u})]}),title:(0,eo.Y)(tG.sA,{id:"md81Lo",defaultMessage:"Error"})})});return(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",border:`1px solid ${g.colors.border}`,height:"calc(100% - 2px)"},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",alignItems:"center",justifyContent:"space-between",padding:`${g.spacing.xs}px ${g.spacing.md}px`},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",alignItems:"center",gap:g.spacing.sm},"",""),children:[(0,eo.Y)(ty.o.Title,{level:4,withoutMargins:!0,children:"MLflow Trace UI"}),(0,eo.Y)(rE.I,{componentId:"mlflow.notebook.trace-ui-info",iconTitle:"More information",maxWidth:400,content:(0,eo.Y)(tG.sA,{id:"LED2z5",defaultMessage:"To disable or enable this display, call {disableFunction} or {enableFunction} and re-run the cell",values:{disableFunction:(0,eo.Y)("code",{children:"mlflow.tracing.disable_notebook_display()"}),enableFunction:(0,eo.Y)("code",{children:"mlflow.tracing.enable_notebook_display()"})}})}),(0,eo.Y)(ty.o.Link,{componentId:"mlflow.notebook.trace-ui-learn-more-link",href:"https://mlflow.org/docs/latest/llms/tracing/index.html?ref=jupyter-notebook-widget",openInNewTab:!0,title:"Learn More",children:(0,eo.Y)(tG.sA,{id:"uIY1qq",defaultMessage:"Learn More"})})]}),(0,eo.Y)(ty.o.Link,{componentId:"mlflow.notebook.trace-ui-see-in-mlflow-link",href:rQ(e[n],t[n]),openInNewTab:!0,title:"View in MLflow UI",children:(0,eo.Y)(tG.sA,{id:"4NyhOm",defaultMessage:"View in MLflow UI"})})]}),e.length>1&&(0,eo.Y)(rK.dK,{componentId:"mlflow.notebook.pagination",currentPageIndex:n+1,dangerouslySetAntdProps:{showQuickJumper:!0},numTotal:e.length,onChange:e=>r(e-1),pageSize:1,style:{marginBottom:g.spacing.sm,paddingLeft:g.spacing.sm,paddingRight:g.spacing.sm}}),(0,eo.Y)("div",{css:r0,children:(0,eo.Y)(rz,{modelTrace:i})})]})};var r2=n(88416);let r3=function(e){return e[e.FORM_FEEDBACK=0]="FORM_FEEDBACK",e[e.FORM_ERROR_REPORT=1]="FORM_ERROR_REPORT",e[e.METADATA=2]="METADATA",e}({}),r5=(0,n(26827).fH)("Feedback::OpenDrawer"),r6=e=>{let t=e||r5,[n,r]=(0,l.useState)(t.isAvailable());return(0,l.useEffect)(()=>{let e;if(n)return;t.isAvailable()&&r(!0);let o=500,i=()=>{t.isAvailable()?r(!0):((o*=2)>5e3&&(o=5e3),e=setTimeout(i,o))};return e=setTimeout(i,o),()=>clearTimeout(e)},[n,t]),n};function r4(e){let{"aria-describedby":t,debugRpc:n,mode:r=r3.FORM_FEEDBACK,...o}=e;if(!r6(n))return null;return(0,eo.Y)(tA.$n,{componentId:"codegen_web-shared_src_fastfeedback_components_feedbacklink.tsx_54","aria-describedby":t,type:"link",icon:(0,eo.Y)(r2.A,{}),onClick:()=>{(n||r5).call({...o,mode:r})},children:r===r3.FORM_ERROR_REPORT?(0,eo.Y)(tG.sA,{id:"URIk/E",defaultMessage:"Report error"}):(0,eo.Y)(tG.sA,{id:"W96Xb9",defaultMessage:"Send feedback"})})}let r7=function(e){return e.Ready="READY",e.LogError="LOG_ERROR",e.LogEvent="LOG_EVENT",e}({}),r8=function(e){return e.UpdateTrace="UPDATE_TRACE",e}({}),r9=JSON.parse('{"2":{"path":"/static/lib/ml-model-trace-renderer/2/index.html","commit":"b5595f5c6263c1c8e3614d85eb1d233d28789bb9"},"3":{"path":"/static/lib/ml-model-trace-renderer/3/index.html","commit":"b5595f5c6263c1c8e3614d85eb1d233d28789bb9"},"current":{"path":"/static/lib/ml-model-trace-renderer/index.html"},"oss":{"path":"/static/lib/ml-model-trace-renderer/oss/index.html","commit":"b5595f5c6263c1c8e3614d85eb1d233d28789bb9"}}'),oe=e=>{var t,n,r;let o=eG(null==e||null===(t=e.info)||void 0===t?void 0:t.request_metadata);if(o)return o;return null!==(n=eG(null==e||null===(r=e.info)||void 0===r?void 0:r.tags))&&void 0!==n?n:"2"},ot=e=>{var t,n;let{modelTrace:r,height:i=700,useLatestVersion:a=!1}=e,d=(0,l.useRef)(null),[u,p]=s().useState(!0),[m,v]=s().useState(!0),E=null!==(t=null===(n=r9[a?"current":oe(r)])||void 0===n?void 0:n.path)&&void 0!==t?t:r9["2"].path,y=$(),A=Z(),{startInteraction:b}=function(){let{esComponent:e,errorBoundaryId:t}=f(),{mergeTags:n}=z(),r=(0,l.useContext)(et),o=en();return(0,l.useMemo)(()=>({...r,startInteraction:(i,a,l,s)=>{let c=n({errorBoundaryId:t},{...s,esComponent:e});return r.startInteraction(i,a,l,c,{useRecordProtoFn:o})}}),[e,r,n,t,o])}(),{theme:_}=(0,c.wn)(),T=F({recordEvent:y,recordProto:A,startInteraction:b});return(0,l.useEffect)(()=>{let e=e=>{var t;let n=null===(t=d.current)||void 0===t?void 0:t.contentWindow;if(!n||e.source!==n)return;switch(e.data.type){case r7.Ready:p(!1);break;case r7.LogError:{let{error:t}=e.data||{};g.iT.sev2(h.Es.MlExperiments,"ModelTraceExplorerFrameRenderer/Error",t);break}case r7.LogEvent:{let t=e.data.payload.eventDefaultPrevented;T({...e.data.payload,skip:t})}}};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}},[T]),(0,l.useEffect)(()=>{var e;let t=null===(e=d.current)||void 0===e?void 0:e.contentWindow;if(!t||u)return;t.postMessage({type:r8.UpdateTrace,traceData:r})},[r,u]),(0,eo.FD)("div",{css:(0,o.AH)({height:m?i:36,borderTop:`1px solid ${_.colors.border}`},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",height:36,justifyContent:"space-between",padding:_.spacing.sm},"",""),children:[(0,eo.FD)("div",{css:(0,o.AH)({display:"flex",alignItems:"center",gap:_.spacing.sm},"",""),children:[(0,eo.Y)(tA.$n,{componentId:`mlflow.notebook.trace-ui-${m?"collapse":"expand"}`,icon:m?(0,eo.Y)(t_.A,{}):(0,eo.Y)(nD.A,{}),size:"small",onClick:()=>v(!m)}),(0,eo.Y)(ty.o.Title,{level:4,withoutMargins:!0,children:"MLflow Trace UI"}),(0,eo.Y)(ty.o.Link,{componentId:"mlflow.notebook.trace-ui-learn-more-link",href:"https://mlflow.org/docs/latest/llms/tracing/index.html",openInNewTab:!0,title:"Learn More",children:"Learn More"})]}),(0,eo.Y)(r4,{parentUi:h.Es.MlFlowOSS,origin:"mlflow-notebook-trace-ui"})]}),u&&(0,eo.Y)("div",{css:(0,o.AH)({position:"absolute",width:"100%",height:i},"",""),children:(0,eo.Y)(rz.Skeleton,{label:"Frame Skeleton"})}),(0,eo.Y)("iframe",{title:"Model Trace Explorer",src:E,ref:d,css:(0,o.AH)({border:"none",width:"100%",height:"calc(100% - 36px)",display:m?"block":"none"},"","")})]})},on=(0,l.createContext)({onSelectRow:()=>{}}),or=()=>(0,l.useContext)(on),oo=e=>{if((0,i.isNil)(e))return null;if(ez(e))return e;try{var t;let n=null===(t=e.spans)||void 0===t?void 0:t.map(e=>({...e,attributes:JSON.parse(e.attributes)}));if((0,i.isNil)(n))return null;return{data:{spans:n},info:{}}}catch(e){return null}},oi=e=>{let{children:t}=e,[n,r]=(0,l.useState)(null),a=oo(n),{theme:s}=(0,c.wn)();return(0,eo.FD)(on.Provider,{value:{onSelectRow:r},children:[t,!(0,i.isNil)(a)&&(0,eo.Y)("div",{css:(0,o.AH)({borderTop:`1px solid ${s.colors.border}`},"",""),children:(0,eo.Y)(ot,{modelTrace:a})})]})}}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/2.50231fdd.chunk.js.map