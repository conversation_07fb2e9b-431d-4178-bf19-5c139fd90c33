"use strict";(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[799],{21799:(e,r,t)=>{t.r(r),t.d(r,{default:()=>v});var n=t(25656),o=t(65848);function a(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function l(e,r,t,n){Object.defineProperty(e,r,{get:t,set:n,enumerable:!0,configurable:!0})}l({},"ErrorBoundary",()=>d),l({},"ErrorBoundaryContext",()=>s);let s=(0,o.createContext)(null),i={didCatch:!1,error:null};class d extends o.Component{static getDerivedStateFromError(e){return{didCatch:!0,error:e}}componentDidCatch(e,r){var t,n;null===(t=(n=this.props).onError)||void 0===t||t.call(n,e,r)}componentDidUpdate(e,r){let{didCatch:t}=this.state,{resetKeys:n}=this.props;if(t&&null!==r.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==r.length||e.some((e,t)=>!Object.is(e,r[t]))}(e.resetKeys,n)){var o,a;null===(o=(a=this.props).onReset)||void 0===o||o.call(a,{next:n,prev:e.resetKeys,reason:"keys"}),this.setState(i)}}render(){let{children:e,fallbackRender:r,FallbackComponent:t,fallback:n}=this.props,{didCatch:a,error:l}=this.state,i=e;if(a){let e={error:l,resetErrorBoundary:this.resetErrorBoundary};if((0,o.isValidElement)(n))i=n;else if("function"==typeof r)i=r(e);else if(t)i=(0,o.createElement)(t,e);else throw Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop")}return(0,o.createElement)(s.Provider,{value:{didCatch:a,error:l,resetErrorBoundary:this.resetErrorBoundary}},i)}constructor(...e){var r;super(...e),r=this,a(this,"state",i),a(this,"resetErrorBoundary",function(){for(var e,t,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];let{error:l}=r.state;null!==l&&(null===(e=(t=r.props).onReset)||void 0===e||e.call(t,{args:o,reason:"imperative-api"}),r.setState(i))})}}function u(){let e=(0,o.useContext)(s);!function(e){if(null==e||"boolean"!=typeof e.didCatch||"function"!=typeof e.resetErrorBoundary)throw Error("ErrorBoundaryContext not found")}(e);let[r,t]=(0,o.useState)({error:null,hasError:!1}),n=(0,o.useMemo)(()=>({resetBoundary:()=>{null==e||e.resetErrorBoundary(),t({error:null,hasError:!1})},showBoundary:e=>t({error:e,hasError:!0})}),[null==e?void 0:e.resetErrorBoundary]);if(r.hasError)throw r.error;return n}function c(e,r){let t=t=>(0,o.createElement)(d,r,(0,o.createElement)(e,t)),n=e.displayName||e.name||"Unknown";return t.displayName=`withErrorBoundary(${n})`,t}l({},"useErrorBoundary",()=>u),l({},"withErrorBoundary",()=>c);var p=t(70821),h=t(89481),E=t(8161),f=t(98358);let y=e=>{let{error:r}=e;return(0,f.Y)(p.S,{description:r instanceof Error?r.message:"An unexpected error has occurred. Please wait a bit and refresh the page or contact support.",title:"Error",image:(0,f.Y)(h.A,{})})},v=e=>{let{children:r}=e;return(0,o.useEffect)(()=>{let e=e=>{let r=e instanceof PromiseRejectionEvent,t=r?e.reason:e.error;if(!t||null!=t&&t._isErrorHandled)return;(0,n.isObjectLike)(t)&&(t._isErrorHandled=!0),window.parent.postMessage({type:"LOG_ERROR",error:t,isPromiseRejection:r})};return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]),(0,f.Y)(E.zI,{callback:e=>{var r;window.parent.postMessage({type:"LOG_EVENT",payload:{eventType:e.eventType,componentType:e.componentType,componentId:e.componentId,value:e.value,shouldStartInteraction:e.shouldStartInteraction,eventDefaultPrevented:null===(r=e.event)||void 0===r?void 0:r.defaultPrevented}})},children:(0,f.Y)(d,{onError:e=>{window.parent.postMessage({type:"LOG_ERROR",error:e})},fallbackRender:y,children:r})})}}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/799.26151587.chunk.js.map