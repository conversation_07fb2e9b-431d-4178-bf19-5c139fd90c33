"use strict";(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[35],{88466:(e,o,r)=>{r.d(o,{U:()=>a});var t=r(63434),n=r(98358),l={name:"g8zzui",styles:"cursor:progress"};function a(e){let{children:o,label:r}=e;if(!r)return(0,n.Y)(n.FK,{children:o});return(0,n.FD)("div",{css:l,children:[(0,n.Y)("span",{css:t.Q,children:r}),(0,n.Y)("div",{"aria-hidden":!0,children:o})]})}},27443:(e,o,r)=>{r.d(o,{$n:()=>C,CL:()=>w,oc:()=>x});var t=r(71218),n=r(63649),l=r(65848),a=r(76495),i=r(46792),c=r(8161),d=r(39659),s=r(80838),u=r(60973),g=r(8467),p=r(80842),h=r(69391),f=r(94167),m=r(58829),v=r(98358);let b=new WeakMap,w=e=>{let{theme:o,...r}=e,t=b.get(o)||new Map;b.has(o)||b.set(o,t);let n=JSON.stringify(r),l=t.get(n);if(l)return l;let a=x(e);return t.set(n,a),a};function y(e){return`${e.general.iconfontCssPrefix}-btn-end-icon`}let x=e=>{let{theme:o,classNamePrefix:r,loading:n,withIcon:l,onlyIcon:i,isAnchor:c,enableAnimation:s,size:u,type:g,useFocusPseudoClass:p,forceIconStyles:f,danger:m,useNewShadows:v,useNewBorderRadii:b}=e,w=`.${o.general.iconfontCssPrefix}`,x=`.${y(o)}`,C=`.${r}-btn-loading-icon`,k=`.${r}-btn-icon-only`,D=`.${r}-btn-primary`,R=`.${r}-btn-link`,S=`.${r}-btn-dangerous`,$={background:o.colors.actionTertiaryBackgroundDefault,color:o.colors.actionTertiaryTextDefault,...v&&{boxShadow:"none"},"&:hover":{background:o.colors.actionTertiaryBackgroundHover,color:o.colors.actionTertiaryTextHover},"&:active":{background:o.colors.actionTertiaryBackgroundPress,color:o.colors.actionTertiaryTextPress}},Y={fontSize:o.general.iconFontSize,lineHeight:0,..."small"===u&&{lineHeight:o.typography.lineHeightSm,height:16,...(i||f)&&{fontSize:16}}},B={color:o.colors.textSecondary},H=`span > ${x} > ${w}`,A={lineHeight:o.typography.lineHeightBase,boxShadow:v?o.shadows.xs:"none",height:o.general.heightSm,display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",...b&&{borderRadius:o.borders.borderRadiusSm},...!i&&!f&&{"&&":{padding:"4px 12px",..."small"===u&&{padding:"0 8px"}}},...(i||f)&&{width:o.general.heightSm},..."small"===u&&{height:24,lineHeight:o.typography.lineHeightBase,...(i||f)&&{width:24,paddingTop:0,paddingBottom:0,verticalAlign:"middle"}},"&:focus-visible":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:(o.isDarkMode,o.colors.actionDefaultBorderFocus)},...(0,a.o8)(o,n),[`&${D}`]:{...(0,a.p_)(o)},[`&${R}`]:{...(0,a.pe)(o),..."link"===g&&{padding:"unset",height:"auto",border:"none",boxShadow:"none","&[disabled],&:hover":{background:"none"}}},[`&${S}${D}`]:{...(0,a.ap)(o)},[`&${S}`]:{...(0,a.om)(o)},"&[disabled]":{...(0,a.wy)(o,v)},[`&${R}:disabled`]:{...(0,a.lp)(o,v)},[`&${S}:disabled`]:{...(0,a.xc)(o,v)},[`&${D}:disabled`]:{...(0,a.Pp)(o,v)},[`&[disabled], &${S}:disabled`]:{...v&&{boxShadow:"none"},...(i||f)&&{backgroundColor:"transparent","&:hover":{backgroundColor:"transparent"},"&:active":{backgroundColor:"transparent"}}},[C]:{display:"none"},...n&&{"::before":{opacity:0},cursor:"default",[`${C}`]:{...i?{verticalAlign:"middle"}:{position:"absolute"},svg:{animationDuration:"1s !important"}},[`& > ${C} .anticon`]:{paddingRight:0},[`> :not(${C})`]:{opacity:0,visibility:"hidden",...l&&{paddingLeft:o.general.iconFontSize+o.spacing.xs}}},[`> ${w} + span, > span + ${w}`]:{marginRight:0,marginLeft:o.spacing.xs},[`> ${w}`]:Y,[`> ${H}`]:{...Y,marginLeft:"small"===u?o.spacing.xs:o.spacing.sm},...!g&&!m&&{[`&:enabled:not(:hover):not(:active) > ${w}`]:B},...!g&&!m&&{[`&:enabled:not(:hover):not(:active) > ${H}`]:B},[`&[${r}-click-animating-without-extra-node='true']::after`]:{display:"none"},[`&${k}`]:{border:"none",...v&&{boxShadow:"none"},[`&:enabled:not(${R})`]:{...$,color:o.colors.textSecondary,"&:hover > .anticon":{color:$["&:hover"].color,...m&&{color:o.colors.actionDangerDefaultTextHover}},"&:active > .anticon":{color:$["&:active"].color,...m&&{color:o.colors.actionDangerDefaultTextPress}},...n&&{"&&, &:hover, &:active":{backgroundColor:"transparent"}}},[`&:enabled:not(${R}) > .anticon`]:{color:o.colors.textSecondary,...m&&{color:o.colors.actionDangerDefaultTextDefault}},...c&&{lineHeight:`${o.general.heightSm}px`,...(0,a.pe)(o),"&:disabled":{color:o.colors.actionDisabledText}},...n&&{"&&, &:hover, &:active":{backgroundColor:"transparent"}},"&[disabled]:hover":{backgroundColor:"transparent"}},"&:focus":{...p&&{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:(o.isDarkMode,o.colors.actionDefaultBorderFocus)},[`${C}`]:{...i&&{left:0}}},...f&&{padding:"0 6px",lineHeight:o.typography.lineHeightSm,color:o.colors.textSecondary,...n&&{"&&, &:hover, &:active":{backgroundColor:"transparent",borderColor:o.colors.actionDefaultBorderDefault},"&[disabled], &[disabled]:hover, &[disabled]:active":{backgroundColor:"transparent",borderColor:"transparent"}},"& > span":{verticalAlign:-1,height:o.general.heightSm/2,width:o.general.heightSm/2},[`& > ${C} .anticon`]:{height:o.general.heightSm/2,width:o.general.heightSm/2,padding:0}},...(0,d.Cx)(s)},T={..."tertiary"===g&&{[`&:enabled:not(${k})`]:$,[`&${R}[disabled]`]:{...(0,a.lp)(o,v)}}},I=(0,h.dg)(A),P=(0,h.dg)(T);return(0,t.AH)(I,P,"","")},C=(()=>{let e=(0,l.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,children:a,size:h,type:b,loading:x,loadingDescription:C,endIcon:k,onClick:D,dangerouslySetForceIconStyles:R,dangerouslyUseFocusPseudoClass:S,dangerouslyAppendWrapperCss:$,componentId:Y,analyticsEvents:B,shouldStartInteraction:H,...A}=e,T=(0,m.W)("databricks.fe.observability.defaultButtonComponentView",!0),I=(0,i.xW)(),{theme:P,classNamePrefix:z}=(0,s.wn)(),{useNewShadows:V,useNewBorderRadii:M}=(0,g.p)(),L=(0,l.useMemo)(()=>null!=B?B:T?[c.s7.OnClick,c.s7.OnView]:[c.s7.OnClick],[B,T]),O=(0,c.ei)({componentType:c.v_.Button,componentId:Y,analyticsEvents:L,shouldStartInteraction:H,isInteractionSubject:!("submit"===A.htmlType&&I.componentId)}),F=y(P),_=`${z}-btn-loading-icon`,{elementRef:N}=(0,p.z)({onView:O.onView});(0,l.useImperativeHandle)(o,()=>N.current);let E=null!=x?x:"submit"===A.htmlType&&I.isSubmitting;(0,l.useEffect)(()=>{N.current&&(E?(N.current.setAttribute("loading","true"),N.current.classList.add(`${z}-btn-loading`)):(N.current.setAttribute("loading","false"),N.current.classList.remove(`${z}-btn-loading`)))},[E,z,N]);let W=!!((A.icon||k)&&!a),G=(0,l.useCallback)(e=>{var o;if(E)return;O.onClick(e),"submit"===A.htmlType&&null!==(o=I.formRef)&&void 0!==o&&o.current&&(e.preventDefault(),I.formRef.current.requestSubmit()),null==D||D(e)},[E,A.htmlType,I.formRef,O,D]),K=(0,v.Y)(u.y,{className:_,animationDuration:8,inheritColor:!0,label:"loading","aria-label":"loading",loadingDescription:null!=C?C:Y,css:(0,t.AH)({color:"inherit !important",pointerEvents:"none",...!W&&!R&&{".anticon":{verticalAlign:"-0.2em"}},'[aria-hidden="true"]':{display:"inline"}},"","")});return(0,v.Y)(d.wC,{children:(0,v.Y)(n.A,{...(0,f.VG)(),...A,css:w({theme:P,classNamePrefix:z,loading:!!E,withIcon:!!A.icon,onlyIcon:W,isAnchor:!!(A.href&&!b),danger:!!A.danger,enableAnimation:P.options.enableAnimation,size:h||"middle",type:b,forceIconStyles:!!R,useFocusPseudoClass:!!S,useNewShadows:V,useNewBorderRadii:M}),href:A.disabled?void 0:A.href,...r,onClick:G,icon:E?K:A.icon,ref:N,type:"tertiary"===b?"link":b,...O.dataComponentProps,children:a&&(0,v.FD)("span",{style:{visibility:E?"hidden":"visible",display:"inline-flex",alignItems:"center",...$},children:[a,k&&(0,v.Y)("span",{className:F,style:{display:"inline-flex",alignItems:"center"},children:k})]})})})});return e.__ANT_BUTTON=!0,e})()},76495:(e,o,r)=>{function t(e){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDefaultBorderDefault,color:e.colors.actionDefaultTextDefault,lineHeight:e.typography.lineHeightBase,textDecoration:"none","&:hover":{backgroundColor:e.colors.actionDefaultBackgroundHover,borderColor:e.colors.actionDefaultBorderHover,color:e.colors.actionDefaultTextHover},"&:active":{backgroundColor:o?e.colors.actionDefaultBackgroundDefault:e.colors.actionDefaultBackgroundPress,borderColor:e.colors.actionDefaultBorderPress,color:e.colors.actionDefaultTextPress}}}function n(e){return{backgroundColor:e.colors.actionPrimaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault,textShadow:"none","&:hover":{backgroundColor:e.colors.actionPrimaryBackgroundHover,borderColor:"transparent",color:e.colors.actionPrimaryTextHover},"&:active":{backgroundColor:e.colors.actionPrimaryBackgroundPress,borderColor:"transparent",color:e.colors.actionPrimaryTextPress}}}function l(e){return{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:e.colors.actionTertiaryBackgroundDefault,color:e.colors.actionTertiaryTextDefault,"&:hover":{backgroundColor:e.colors.actionTertiaryBackgroundHover,borderColor:"transparent",color:e.colors.actionTertiaryTextHover},"&:active":{backgroundColor:e.colors.actionTertiaryBackgroundPress,borderColor:"transparent",color:e.colors.actionTertiaryTextPress},"&[disabled]:hover":{background:"none",color:e.colors.actionDisabledText}}}function a(e){return{backgroundColor:e.colors.actionDangerPrimaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault,"&:hover":{backgroundColor:e.colors.actionDangerPrimaryBackgroundHover,borderColor:"transparent",color:e.colors.actionPrimaryTextHover},"&:active":{backgroundColor:e.colors.actionDangerPrimaryBackgroundPress,borderColor:"transparent",color:e.colors.actionPrimaryTextPress},"&:focus-visible":{outlineColor:e.colors.actionDangerPrimaryBackgroundDefault}}}function i(e){return{backgroundColor:e.colors.actionDangerDefaultBackgroundDefault,borderColor:e.colors.actionDangerDefaultBorderDefault,color:e.colors.actionDangerDefaultTextDefault,"&:hover":{backgroundColor:e.colors.actionDangerDefaultBackgroundHover,borderColor:e.colors.actionDangerDefaultBorderHover,color:e.colors.actionDangerDefaultTextHover},"&:active":{backgroundColor:e.colors.actionDangerDefaultBackgroundPress,borderColor:e.colors.actionDangerDefaultBorderPress,color:e.colors.actionDangerDefaultTextPress},"&:focus-visible":{outlineColor:e.colors.actionDangerPrimaryBackgroundDefault}}}function c(e,o){return{...{backgroundColor:"transparent",borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:"transparent",borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText},"&:active":{backgroundColor:"transparent",borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText}}}function d(e,o){return{...{backgroundColor:e.colors.actionDisabledBorder,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionDisabledBorder,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault},"&:active":{backgroundColor:e.colors.actionDisabledBorder,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault}}}function s(e,o){return d(e,o)}function u(e,o){return{...{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionDisabledText,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionDisabledText},"&:active":{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionDisabledText}}}function g(e,o){return{...{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText},"&:active":{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText}}}function p(e,o){return{...{backgroundColor:e.colors.actionDisabledBorder,color:e.colors.actionPrimaryTextDefault,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionDisabledBorder,color:e.colors.actionPrimaryTextDefault},"&:active":{backgroundColor:e.colors.actionDisabledBorder,color:e.colors.actionPrimaryTextDefault}}}r.d(o,{J5:()=>g,LM:()=>p,Pp:()=>d,ap:()=>a,lp:()=>u,o8:()=>t,om:()=>i,p_:()=>n,pe:()=>l,wy:()=>c,xc:()=>s})},16389:(e,o,r)=>{r.d(o,{B6:()=>C,Sc:()=>x,xl:()=>b});var t=r(71218),n=r(36292),l=r(88608),a=r.n(l),i=r(65848),c=r(8161),d=r(39659),s=r(80838),u=r(8467),g=r(80842),p=r(69391),h=r(94167),f=r(58829),m=r(98358);function v(e,o){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],t=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0,l=arguments.length>5?arguments[5]:void 0,a=`.${e}-input`,i=`.${e}-inner`,c=`.${e}-indeterminate`,s=`.${e}-checked`,u=`.${e}-disabled`,g=`.${e}-wrapper-disabled`,p=`.${e}-group`,h=`.${e}-wrapper`,f=`${a} + ${i}`,m=`${a}:hover + ${i}`,v=`${a}:active + ${i}`,b=`.${e.replace("-checkbox","")}`;return{[`.${e}`]:{top:"unset",lineHeight:o.typography.lineHeightBase,alignSelf:"flex-start",display:"flex",alignItems:"center",height:o.typography.lineHeightBase},[`&${h}, ${h}`]:{alignItems:"center",lineHeight:o.typography.lineHeightBase},[i]:{borderColor:o.colors.actionDefaultBorderDefault,...l&&{borderRadius:o.borders.borderRadiusSm}},[`&> span:not(.${e})`]:{display:"inline-flex",alignItems:"center"},[`&${p}`]:{display:"flex",flexDirection:"column",rowGap:o.spacing.sm,columnGap:0,...n&&{[`& + ${b}-form-message`]:{marginTop:o.spacing.sm}}},...n&&{[`${b}-hint + &${p}`]:{marginTop:o.spacing.sm}},...r&&{[`&${p}`]:{display:"flex",flexDirection:"row",columnGap:o.spacing.sm,rowGap:0,[`& > ${p}-item`]:{marginRight:0}}},[`${a}:focus-visible + ${i}`]:{outlineWidth:"2px",outlineColor:o.colors.actionDefaultBorderFocus,outlineOffset:"4px",outlineStyle:"solid"},[m]:{backgroundColor:o.colors.actionDefaultBackgroundHover,borderColor:o.colors.actionPrimaryBackgroundHover},[v]:{backgroundColor:o.colors.actionDefaultBackgroundPress,borderColor:o.colors.actionPrimaryBackgroundPress},[s]:{...t&&{[i]:{boxShadow:o.shadows.xs}},"&::after":{border:"none"},[f]:{backgroundColor:o.colors.actionPrimaryBackgroundDefault,borderColor:"transparent"},[m]:{backgroundColor:o.colors.actionPrimaryBackgroundHover,borderColor:"transparent"},[v]:{backgroundColor:o.colors.actionPrimaryBackgroundPress,borderColor:"transparent"}},[c]:{[i]:{...t&&{boxShadow:o.shadows.xs},backgroundColor:o.colors.actionPrimaryBackgroundDefault,borderColor:o.colors.actionPrimaryBackgroundDefault,"&:after":{backgroundColor:o.colors.white,height:"3px",width:"8px",borderRadius:"4px"}},[m]:{backgroundColor:o.colors.actionPrimaryBackgroundHover,borderColor:"transparent"},[v]:{backgroundColor:o.colors.actionPrimaryBackgroundPress}},[`&${g}`]:{[u]:{[`&${s}`]:{[i]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder,"&:after":{borderColor:o.colors.actionDisabledText}},[m]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder}},[`&${c}`]:{[i]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder,"&:after":{borderColor:o.colors.actionDisabledText,backgroundColor:o.colors.actionDisabledText}},[m]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder}},[i]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder,"&:after":{borderColor:"transparent"}},[m]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder},"& + span":{color:o.colors.actionDisabledText}}},...(0,d.Cx)(o.options.enableAnimation)}}let b=e=>{let{clsPrefix:o,theme:r,wrapperStyle:n={},useNewFormUISpacing:l}=e,a=l?`, && + .${o}-hint + .${o}-form-message`:"",i={height:r.typography.lineHeightBase,lineHeight:r.typography.lineHeightBase,[`&& + .${o}-hint, && + .${o}-form-message${a}`]:{paddingLeft:r.spacing.lg,marginTop:0},...n};return(0,t.AH)(i,"","")},w=(0,i.forwardRef)(function(e,o){let{isChecked:r,onChange:l,children:w,isDisabled:y=!1,style:x,wrapperStyle:C,dangerouslySetAntdProps:k,className:D,componentId:R,analyticsEvents:S,...$}=e,Y=(0,f.W)("databricks.fe.observability.defaultComponentView.checkbox",!1),{theme:B,classNamePrefix:H,getPrefixedClassName:A}=(0,s.wn)(),{useNewShadows:T,useNewFormUISpacing:I,useNewBorderRadii:P}=(0,u.p)(),z=A("checkbox"),V=(0,i.useMemo)(()=>null!=S?S:Y?[c.s7.OnValueChange,c.s7.OnView]:[c.s7.OnValueChange],[S,Y]),M=(0,c.ei)({componentType:c.v_.Checkbox,componentId:R,analyticsEvents:V,valueHasNoPii:!0}),{elementRef:L}=(0,g.z)({onView:M.onView,value:null!=r?r:$.defaultChecked});return(0,m.Y)(d.wC,{children:(0,m.Y)("div",{...(0,h.VG)(),className:a()(D,`${z}-container`),css:b({clsPrefix:H,theme:B,wrapperStyle:C,useNewFormUISpacing:I}),ref:L,children:(0,m.Y)(n.A,{checked:null===r?void 0:r,ref:o,onChange:e=>{M.onValueChange(e.target.checked),null==l||l(e.target.checked,e)},disabled:y,indeterminate:null===r,css:(0,t.AH)((0,p.dg)(v(z,B,!1,T,I,P)),"",""),style:x,"aria-checked":null===r?"mixed":r,...$,...k,...M.dataComponentProps,children:(0,m.Y)(d.dg,{children:w})})})})}),y=(0,i.forwardRef)(function(e,o){let{children:r,layout:t="vertical",...l}=e,{theme:a,getPrefixedClassName:i}=(0,s.wn)(),c=i("checkbox"),{useNewShadows:g,useNewFormUISpacing:p,useNewBorderRadii:f}=(0,u.p)();return(0,m.Y)(d.wC,{children:(0,m.Y)(n.A.Group,{...(0,h.VG)(),ref:o,...l,css:v(c,a,"horizontal"===t,g,p,f),children:(0,m.Y)(d.dg,{children:r})})})}),x=Object.assign(w,{Group:y}),C=y},28219:(e,o,r)=>{r.r(o),r.d(o,{Arrow:()=>M,CheckboxItem:()=>P,Content:()=>S,Group:()=>N,HintColumn:()=>E,HintRow:()=>W,IconWrapper:()=>G,Item:()=>B,ItemIndicator:()=>V,Label:()=>H,RadioGroup:()=>z,RadioItem:()=>L,Root:()=>R,Separator:()=>A,Sub:()=>_,SubContent:()=>$,SubTrigger:()=>T,Trigger:()=>Y,TriggerItem:()=>I,dropdownContentStyles:()=>q,dropdownItemStyles:()=>j,dropdownSeparatorStyles:()=>J});var t=r(71218),n=r(81434),l=r(35012),a=r(65848),i=r.n(a),c=r(19362),d=r(46792),s=r(8161),u=r(80838),g=r(63908),p=r(17061),h=r(23240),f=r(49817),m=r(60054),v=r(71009),b=r(8467),w=r(69391),y=r(94167),x=r(58829),C=r(98358);let k=(0,a.createContext)({isOpen:!1,setIsOpen:e=>{}}),D=()=>i().useContext(k),R=e=>{let{children:o,itemHtmlType:r,...t}=e,[n,c]=i().useState(!!(t.defaultOpen||t.open)),d=(0,a.useRef)(void 0!==t.open||void 0!==t.onOpenChange).current;(0,a.useEffect)(()=>{d&&c(!!t.open)},[d,t.open]);let s=e=>{d||c(e),t.onOpenChange&&t.onOpenChange(e)};return(0,C.Y)(l.bL,{...t,...!d&&{open:n,onOpenChange:s},children:(0,C.Y)(k.Provider,{value:{isOpen:d?t.open:n,setIsOpen:d?t.onOpenChange:s,itemHtmlType:r},children:(0,C.Y)(v.C,{children:o})})})},S=(0,a.forwardRef)(function(e,o){let{children:r,minWidth:t=220,matchTriggerWidth:n,forceCloseOnEscape:a,onEscapeKeyDown:i,onKeyDown:d,...s}=e,{getPopupContainer:p}=(0,g.G)(),{theme:h}=(0,u.wn)(),{useNewShadows:m}=(0,b.p)(),{setIsOpen:v}=D(),{isInsideModal:w}=(0,f.k3)();return(0,C.Y)(l.ZL,{container:p&&p(),children:(0,C.Y)(l.UC,{...(0,y.VG)(),ref:o,loop:!0,css:[U(h,m),{minWidth:t},n?{width:"var(--radix-dropdown-menu-trigger-width)"}:{},"",""],sideOffset:4,align:"start",onKeyDown:e=>{"Escape"===e.key&&((w||a)&&(e.stopPropagation(),null==v||v(!1)),null==i||i(e.nativeEvent)),("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&(0,c.K)(e),null==d||d(e)},...s,onWheel:e=>{var o;e.stopPropagation(),null==s||null===(o=s.onWheel)||void 0===o||o.call(s,e)},onTouchMove:e=>{var o;e.stopPropagation(),null==s||null===(o=s.onTouchMove)||void 0===o||o.call(s,e)},children:r})})}),$=(0,a.forwardRef)(function(e,o){let{children:r,minWidth:t=220,onKeyDown:n,...d}=e,{getPopupContainer:s}=(0,g.G)(),{theme:p}=(0,u.wn)(),{useNewShadows:h}=(0,b.p)(),[f,m]=i().useState(!0),[v,w]=i().useState(null),{isOpen:x}=F(),k=(0,a.useRef)(null);(0,a.useImperativeHandle)(o,()=>k.current);let D=(0,a.useCallback)(()=>{if(k.current){let e=parseFloat(getComputedStyle(k.current).getPropertyValue("--radix-dropdown-menu-content-available-width")),o=k.current.offsetWidth,r=k.current.getAttribute("data-side");"left"===r||"right"===r?w(r):w(null),e<o?m(!1):m(!0)}},[]);(0,a.useEffect)(()=>(window.addEventListener("resize",D),D(),()=>{window.removeEventListener("resize",D)}),[D]),(0,a.useEffect)(()=>{x&&setTimeout(()=>{D()},25)},[x,D]);let R="calc(var(--radix-dropdown-menu-content-available-width) + var(--radix-dropdown-menu-trigger-width) * -1)";"left"===v&&(R="calc(var(--radix-dropdown-menu-trigger-width) - var(--radix-dropdown-menu-content-available-width))");let S=`
    transform-origin: var(--radix-dropdown-menu-content-transform-origin) !important;
    transform: translateX(${R}) !important;
`;return(0,C.Y)(l.ZL,{container:s&&s(),children:(0,C.Y)(l.G5,{...(0,y.VG)(),ref:k,loop:!0,css:[U(p,h),{minWidth:t},f?"":S,"",""],sideOffset:-2,alignOffset:-5,onKeyDown:e=>{("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&(e.stopPropagation(),(0,c.K)(e)),null==n||n(e)},...d,children:r})})}),Y=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(l.l9,{...(0,y.VG)(),ref:o,...t,children:r})}),B=(0,a.forwardRef)(function(e,o){let{children:r,disabledReason:t,danger:i,onClick:c,componentId:u,analyticsEvents:g,...p}=e,h=(0,x.W)("databricks.fe.observability.defaultComponentView.dropdownMenu",!1),f=(0,d.xW)(),{itemHtmlType:b}=D(),w=(0,a.useRef)(null);(0,a.useImperativeHandle)(o,()=>w.current);let y=(0,a.useMemo)(()=>null!=g?g:h?[s.s7.OnClick,s.s7.OnView]:[s.s7.OnClick],[g,h]),k=(0,s.ei)({componentType:s.v_.DropdownMenuItem,componentId:u,analyticsEvents:y,isInteractionSubject:!("submit"===b&&f.componentId)}),{elementRef:R}=(0,v.Y)({onView:p.asChild?()=>{}:k.onView}),S=(0,n.SV)([w,R]);return(0,C.Y)(l.q7,{css:e=>[j,i&&X(e)],ref:S,onClick:e=>{if(p.disabled)e.preventDefault();else{var o;p.asChild||k.onClick(e),"submit"===b&&null!==(o=f.formRef)&&void 0!==o&&o.current&&(e.preventDefault(),f.formRef.current.requestSubmit()),null==c||c(e)}},onKeyDown:e=>{var o;("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&e.preventDefault(),null===(o=p.onKeyDown)||void 0===o||o.call(p,e)},...p,...k.dataComponentProps,children:(0,m.m)(r,p,t,w)})}),H=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(l.JU,{ref:o,css:[j,e=>({color:e.colors.textSecondary,"&:hover":{cursor:"default"}}),"",""],...t,children:r})}),A=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(l.wv,{ref:o,css:J,...t,children:r})}),T=(0,a.forwardRef)(function(e,o){let{children:r,disabledReason:t,...n}=e,i=(0,a.useRef)(null);return(0,a.useImperativeHandle)(o,()=>i.current),(0,C.FD)(l.ZP,{ref:i,css:[j,e=>({'&[data-state="open"]':{backgroundColor:e.colors.actionTertiaryBackgroundHover}}),"",""],onKeyDown:e=>{var o;("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&e.preventDefault(),null===(o=n.onKeyDown)||void 0===o||o.call(n,e)},...n,children:[(0,m.m)(r,n,t,i),(0,C.Y)(E,{css:e=>({margin:K.subMenuIconMargin(e),display:"flex",alignSelf:"stretch",alignItems:"center"}),children:(0,C.Y)(p.A,{css:e=>({fontSize:K.subMenuIconSize(e)})})})]})}),I=T,P=(0,a.forwardRef)(function(e,o){var r;let{children:t,disabledReason:i,componentId:c,analyticsEvents:d,onCheckedChange:u,...g}=e,p=(0,x.W)("databricks.fe.observability.defaultComponentView.dropdownMenu",!1),h=(0,a.useRef)(null);(0,a.useImperativeHandle)(o,()=>h.current);let f=(0,a.useMemo)(()=>null!=d?d:p?[s.s7.OnValueChange,s.s7.OnView]:[s.s7.OnValueChange],[d,p]),b=(0,s.ei)({componentType:s.v_.DropdownMenuCheckboxItem,componentId:c,analyticsEvents:f,valueHasNoPii:!0}),{elementRef:w}=(0,v.Y)({onView:b.onView,value:null!==(r=g.checked)&&void 0!==r?r:g.defaultChecked}),y=(0,n.SV)([h,w]),k=(0,a.useCallback)(e=>{b.onValueChange(e),null==u||u(e)},[b,u]);return(0,C.Y)(l.H_,{ref:y,css:e=>[j,Z(e)],onCheckedChange:k,onKeyDown:e=>{var o;("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&e.preventDefault(),null===(o=g.onKeyDown)||void 0===o||o.call(g,e)},...g,...b.dataComponentProps,children:(0,m.m)(t,g,i,h)})}),z=(0,a.forwardRef)(function(e,o){var r;let{children:t,componentId:i,analyticsEvents:c,onValueChange:d,valueHasNoPii:u,...g}=e,p=(0,x.W)("databricks.fe.observability.defaultComponentView.dropdownMenu",!1),h=(0,a.useRef)(null);(0,a.useImperativeHandle)(o,()=>h.current);let f=(0,a.useMemo)(()=>null!=c?c:p?[s.s7.OnValueChange,s.s7.OnView]:[s.s7.OnValueChange],[c,p]),m=(0,s.ei)({componentType:s.v_.DropdownMenuRadioGroup,componentId:i,analyticsEvents:f,valueHasNoPii:u}),{elementRef:b}=(0,v.Y)({onView:m.onView,value:null!==(r=g.value)&&void 0!==r?r:g.defaultValue}),w=(0,n.SV)([h,b]),y=(0,a.useCallback)(e=>{m.onValueChange(e),null==d||d(e)},[m,d]);return(0,C.Y)(l.z6,{ref:w,onValueChange:y,...g,...m.dataComponentProps,children:t})}),V=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(l.VF,{ref:o,css:e=>({marginLeft:-(K.checkboxIconWidth(e)+K.checkboxPaddingRight(e)),position:"absolute",fontSize:e.general.iconFontSize}),...t,children:null!=r?r:(0,C.Y)(h.A,{css:e=>({color:e.colors.textSecondary})})})}),M=(0,a.forwardRef)(function(e,o){let{children:r,...n}=e,{theme:a}=(0,u.wn)();return(0,C.Y)(l.i3,{css:(0,t.AH)({fill:a.colors.backgroundPrimary,stroke:a.colors.borderDecorative,strokeDashoffset:-K.arrowBottomLength(),strokeDasharray:K.arrowBottomLength()+2*K.arrowSide(),strokeWidth:K.arrowStrokeWidth(),position:"relative",top:-1},"",""),ref:o,width:12,height:6,...n,children:r})}),L=(0,a.forwardRef)(function(e,o){let{children:r,disabledReason:t,...n}=e,i=(0,a.useRef)(null);return(0,a.useImperativeHandle)(o,()=>i.current),(0,C.Y)(l.hN,{ref:i,css:e=>[j,Z(e)],...n,children:(0,m.m)(r,n,t,i)})}),O=(0,a.createContext)({isOpen:!1}),F=()=>i().useContext(O),_=e=>{var o;let{children:r,onOpenChange:t,...n}=e,[a,c]=i().useState(null!==(o=n.defaultOpen)&&void 0!==o&&o);return(0,C.Y)(l.Pb,{onOpenChange:e=>{null==t||t(e),c(e)},...n,children:(0,C.Y)(O.Provider,{value:{isOpen:a},children:r})})},N=l.YJ,E=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)("div",{ref:o,css:[Q,"margin-left:auto;",""],...t,children:r})}),W=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)("div",{ref:o,css:[Q,"min-width:100%;",""],...t,children:r})}),G=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)("div",{ref:o,css:e=>({fontSize:16,color:e.colors.textSecondary,paddingRight:e.spacing.sm}),...t,children:r})}),K={itemPaddingVertical:e=>.5*e.spacing.xs+.5*e.spacing.sm,itemPaddingHorizontal:e=>e.spacing.sm,checkboxIconWidth:e=>e.general.iconFontSize,checkboxPaddingLeft:e=>e.spacing.sm+e.spacing.xs,checkboxPaddingRight:e=>e.spacing.sm,subMenuIconMargin(e){let o=this.itemPaddingVertical(e)/2,r=-this.itemPaddingVertical(e)+1.5*e.spacing.sm;return`${-o}px ${-r}px ${-o}px auto`},subMenuIconSize:e=>e.spacing.lg,arrowBottomLength:()=>30,arrowHeight:()=>10,arrowSide(){return 2*(this.arrowHeight()**2*2)**.5},arrowStrokeWidth:()=>2},q=(e,o)=>({backgroundColor:e.colors.backgroundPrimary,color:e.colors.textPrimary,lineHeight:e.typography.lineHeightBase,border:`1px solid ${e.colors.borderDecorative}`,borderRadius:e.borders.borderRadiusSm,padding:`${e.spacing.xs}px 0`,boxShadow:o?e.shadows.lg:e.general.shadowLow,userSelect:"none",overflowY:"auto",maxHeight:"var(--radix-dropdown-menu-content-available-height)",...(0,w.WO)(e,o),zIndex:1e4,a:(0,w.dg)({color:e.colors.textPrimary,"&:hover, &:focus":{color:e.colors.textPrimary,textDecoration:"none"}})}),U=(e,o)=>({...q(e,o)}),j=e=>({padding:`${K.itemPaddingVertical(e)}px ${K.itemPaddingHorizontal(e)}px`,display:"flex",flexWrap:"wrap",alignItems:"center",outline:"unset","&:hover":{cursor:"pointer"},"&:focus":{backgroundColor:e.colors.actionTertiaryBackgroundHover,"&:not(:hover)":{outline:`2px auto ${e.colors.actionDefaultBorderFocus}`,outlineOffset:"-1px"}},"&[data-disabled]":{pointerEvents:"none",color:`${e.colors.actionDisabledText} !important`}}),X=e=>({color:e.colors.textValidationDanger,"&:hover, &:focus":{backgroundColor:e.colors.actionDangerDefaultBackgroundHover}}),Z=e=>({position:"relative",paddingLeft:K.checkboxIconWidth(e)+K.checkboxPaddingLeft(e)+K.checkboxPaddingRight(e)}),Q=e=>({color:e.colors.textSecondary,fontSize:e.typography.fontSizeSm,"[data-disabled] &":{color:e.colors.actionDisabledText}}),J=e=>({height:1,margin:`${e.spacing.xs}px ${e.spacing.sm}px`,backgroundColor:e.colors.borderDecorative})},19362:(e,o,r)=>{r.d(o,{K:()=>a});let t=e=>e.querySelectorAll('[role^="menuitem"]'),n=e=>{let o=e.currentTarget.closest('[role="menu"]');if(!o)return;let r=t(o),n=document.activeElement,l="ArrowUp"===e.key||"Tab"===e.key&&e.shiftKey,a=Array.from(r).findIndex(e=>e===n),i=l?a-1:a+1;(i<0||i>=r.length)&&(i=l?r.length-1:0);let c=r[i];if(c){if(c.hasAttribute("data-disabled")){let o=c.querySelector("[data-disabled-tooltip]");null==o||o.setAttribute("tabindex","0"),o&&(e.preventDefault(),o.focus())}else c.focus(),c.setAttribute("data-highlighted","true")}},l=e=>{let o=document.activeElement,r=o.closest('[role^="menuitem"]'),n=o.closest('[role="menu"]');if(!n)return;let l=t(n),a=Array.from(l).findIndex(e=>e===r),i="ArrowUp"===e.key||"Tab"===e.key&&e.shiftKey,c=i?a-1:a+1;(c<0||c>=l.length)&&(c=i?l.length-1:0);let d=l[c];if(d){if(o.removeAttribute("tabindex"),o.blur(),d.hasAttribute("data-disabled")){let o=d.querySelector("[data-disabled-tooltip]");null==o||o.setAttribute("tabindex","0"),o&&(e.preventDefault(),o.focus())}else d.focus()}},a=e=>{var o,r,t,a;let i=(null===(o=document.activeElement)||void 0===o?void 0:o.getAttribute("role"))==="menuitem"||(null===(r=document.activeElement)||void 0===r?void 0:r.getAttribute("role"))==="menuitemcheckbox"||(null===(t=document.activeElement)||void 0===t?void 0:t.getAttribute("role"))==="menuitemradio",c=null===(a=document.activeElement)||void 0===a?void 0:a.hasAttribute("data-disabled-tooltip");i||!c?n(e):l(e)}},63908:(e,o,r)=>{r.d(o,{G:()=>l});var t=r(65848),n=r(39659);function l(){return(0,t.useContext)(n.dC)}},67630:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",d:"M7 3.25H1v1.5h6zM15 11.25H1v1.5h14zM1 8.75h10v-1.5H1z"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="BarsAscendingVerticalIcon";let c=i},53127:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"m6.144 12.331.972-.972 1.06 1.06-.971.973a3.625 3.625 0 1 1-5.127-5.127l2.121-2.121A3.625 3.625 0 0 1 10.32 8H8.766a2.125 2.125 0 0 0-3.507-.795l-2.121 2.12a2.125 2.125 0 0 0 3.005 3.006"}),(0,l.Y)("path",{fill:"currentColor",d:"m9.856 3.669-.972.972-1.06-1.06.971-.973a3.625 3.625 0 1 1 5.127 5.127l-2.121 2.121A3.625 3.625 0 0 1 5.68 8h1.552a2.125 2.125 0 0 0 3.507.795l2.121-2.12a2.125 2.125 0 0 0-3.005-3.006"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ChainIcon";let c=i},23240:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"m15.06 3.56-9.53 9.531L1 8.561 2.06 7.5l3.47 3.47L14 2.5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="CheckIcon";let c=i},80061:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 8.917 10.947 6 12 7.042 8 11 4 7.042 5.053 6z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ChevronDownIcon";let c=i},16306:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.083 8 10 10.947 8.958 12 5 8l3.958-4L10 5.053z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ChevronLeftIcon";let c=i},17061:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8.917 8 6 5.053 7.042 4 11 8l-3.958 4L6 10.947z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ChevronRightIcon";let c=i},98022:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 7.083 5.053 10 4 8.958 8 5l4 3.958L10.947 10z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ChevronUpIcon";let c=i},4700:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.97 8.03 2 3.06 3.06 2l4.97 4.97L13 2l1.06 1.06-4.969 4.97 4.97 4.97L13 14.06 8.03 9.092l-4.97 4.97L2 13z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="CloseIcon";let c=i},26033:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.064 8 4 4.936 4.936 4 8 7.064 11.063 4l.937.936L8.937 8 12 11.063l-.937.937L8 8.937 4.936 12 4 11.063z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="CloseSmallIcon";let c=i},86571:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 17 16",...e,children:(0,l.Y)("path",{fill:"currentColor",d:"M4.03 12.06 5.091 11l-2.97-2.97 2.97-2.97L4.031 4 0 8.03zM12.091 4l4.03 4.03-4.03 4.03-1.06-1.06L14 8.03l-2.97-2.97z"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="CodeIcon";let c=i},80982:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.78 3.97 5.03 1.22a.75.75 0 0 0-1.06 0L1.22 3.97a.75.75 0 0 0 0 1.06l2.75 2.75a.75.75 0 0 0 1.06 0l2.75-2.75a.75.75 0 0 0 0-1.06m-1.59.53L4.5 6.19 2.81 4.5 4.5 2.81zM15 11.75a3.25 3.25 0 1 0-6.5 0 3.25 3.25 0 0 0 6.5 0M11.75 10a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5",clipRule:"evenodd"}),(0,l.Y)("path",{fill:"currentColor",d:"M14.25 1H9v1.5h4.5V7H15V1.75a.75.75 0 0 0-.75-.75M1 9v5.25c0 .414.336.75.75.75H7v-1.5H2.5V9z"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ConnectIcon";let c=i},23859:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75H5v3.25c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-8.5a.75.75 0 0 0-.75-.75H11V1.75a.75.75 0 0 0-.75-.75zM9.5 5V2.5h-7v7H5V5.75A.75.75 0 0 1 5.75 5zm-3 8.5v-7h7v7z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="CopyIcon";let c=i},26769:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M2 1.75A.75.75 0 0 1 2.75 1h6a.75.75 0 0 1 .53.22l4.5 4.5c.141.14.22.331.22.53V10h-1.5V7H8.75A.75.75 0 0 1 8 6.25V2.5H3.5V16H2zm7.5 1.81 1.94 1.94H9.5z",clipRule:"evenodd"}),(0,l.Y)("path",{fill:"currentColor",d:"M5 11.5V13h9v-1.5zM14 16H5v-1.5h9z"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="FileDocumentIcon";let c=i},37370:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1.75A.75.75 0 0 1 1.75 1h12.5a.75.75 0 0 1 .75.75V4a.75.75 0 0 1-.22.53L10 9.31v4.94a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1-.75-.75V9.31L1.22 4.53A.75.75 0 0 1 1 4zm1.5.75v1.19l4.78 4.78c.141.14.22.331.22.53v4.5h1V9a.75.75 0 0 1 .22-.53l4.78-4.78V2.5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="FilterIcon";let c=i},72152:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("g",{clipPath:"url(#FunctionIcon_svg__a)",children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M9.93 2.988c-.774-.904-2.252-.492-2.448.682L7.094 6h2.005a2.75 2.75 0 0 1 2.585 1.81l.073.202 2.234-2.063 1.018 1.102-2.696 2.489.413 1.137c.18.494.65.823 1.175.823H15V13h-1.1a2.75 2.75 0 0 1-2.585-1.81l-.198-.547-2.61 2.408-1.017-1.102 3.07-2.834-.287-.792A1.25 1.25 0 0 0 9.099 7.5H6.844l-.846 5.076c-.405 2.43-3.464 3.283-5.067 1.412l1.139-.976c.774.904 2.252.492 2.448-.682l.805-4.83H3V6h2.573l.43-2.576C6.407.994 9.465.14 11.07 2.012z",clipRule:"evenodd"})}),(0,l.Y)("defs",{children:(0,l.Y)("clipPath",{children:(0,l.Y)("path",{fill:"#fff",d:"M16 0H0v16h16z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="FunctionIcon";let c=i},22073:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.FD)("g",{fill:"currentColor",fillRule:"evenodd",clipPath:"url(#GearIcon_svg__a)",clipRule:"evenodd",children:[(0,l.Y)("path",{d:"M7.984 5a3 3 0 1 0 0 6 3 3 0 0 0 0-6m-1.5 3a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0"}),(0,l.Y)("path",{d:"M7.966 0q-.51 0-1.005.063a.75.75 0 0 0-.62.51l-.639 1.946q-.315.13-.61.294L3.172 2.1a.75.75 0 0 0-.784.165c-.481.468-.903.996-1.255 1.572a.75.75 0 0 0 .013.802l1.123 1.713a6 6 0 0 0-.15.66L.363 8.07a.75.75 0 0 0-.36.716c.067.682.22 1.34.447 1.962a.75.75 0 0 0 .635.489l2.042.19q.195.276.422.529l-.27 2.032a.75.75 0 0 0 .336.728 8 8 0 0 0 1.812.874.75.75 0 0 0 .778-.192l1.422-1.478a6 6 0 0 0 .677 0l1.422 1.478a.75.75 0 0 0 .778.192 8 8 0 0 0 1.812-.874.75.75 0 0 0 .335-.728l-.269-2.032a6 6 0 0 0 .422-.529l2.043-.19a.75.75 0 0 0 .634-.49c.228-.621.38-1.279.447-1.961a.75.75 0 0 0-.36-.716l-1.756-1.056a6 6 0 0 0-.15-.661l1.123-1.713a.75.75 0 0 0 .013-.802 8 8 0 0 0-1.255-1.572.75.75 0 0 0-.784-.165l-1.92.713q-.295-.163-.61-.294L9.589.573a.75.75 0 0 0-.619-.51A8 8 0 0 0 7.965 0m-.95 3.328.597-1.819a7 7 0 0 1 .705 0l.597 1.819a.75.75 0 0 0 .472.476q.519.177.97.468a.75.75 0 0 0 .668.073l1.795-.668q.234.264.44.552l-1.05 1.6a.75.75 0 0 0-.078.667q.181.501.24 1.05a.75.75 0 0 0 .359.567l1.642.988q-.06.351-.156.687l-1.909.178a.75.75 0 0 0-.569.353q-.287.463-.672.843a.75.75 0 0 0-.219.633l.252 1.901a7 7 0 0 1-.635.306l-1.33-1.381a.75.75 0 0 0-.63-.225 4.5 4.5 0 0 1-1.08 0 .75.75 0 0 0-.63.225l-1.33 1.381a7 7 0 0 1-.634-.306l.252-1.9a.75.75 0 0 0-.219-.634 4.5 4.5 0 0 1-.672-.843.75.75 0 0 0-.569-.353l-1.909-.178a7 7 0 0 1-.156-.687L3.2 8.113a.75.75 0 0 0 .36-.567q.056-.549.239-1.05a.75.75 0 0 0-.078-.666L2.67 4.229q.206-.288.44-.552l1.795.668a.75.75 0 0 0 .667-.073c.3-.193.626-.351.97-.468a.75.75 0 0 0 .472-.476"})]}),(0,l.Y)("defs",{children:(0,l.Y)("clipPath",{children:(0,l.Y)("path",{fill:"#fff",d:"M0 0h16v16H0z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="GearIcon";let c=i},55444:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M7.25 10.5v-3h1.5v3zM8 5a.75.75 0 1 1 0 1.5A.75.75 0 0 1 8 5"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12m0-1.5a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="InfoIcon";let c=i},54198:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M12 8.75H7v-1.5h5zM7 5.5h5V4H7zM12 12H7v-1.5h5zM4.75 5.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5M5.5 8A.75.75 0 1 1 4 8a.75.75 0 0 1 1.5 0M4.75 12a.75.75 0 1 0 0-********* 0 0 0 0 1.5"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1.75A.75.75 0 0 1 1.75 1h12.5a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75zm1.5.75v11h11v-11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ListBorderIcon";let c=i},5844:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 24 24",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M23.212 12a.79.79 0 0 1-.789-.788 9.6 9.6 0 0 0-.757-3.751 9.66 9.66 0 0 0-5.129-5.129 9.6 9.6 0 0 0-3.749-.755.788.788 0 0 1 0-1.577c1.513 0 2.983.296 4.365.882a11.1 11.1 0 0 1 3.562 2.403 11.157 11.157 0 0 1 3.283 7.927.785.785 0 0 1-.786.788",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="LoadingIcon";let c=i},84437:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M7.25 9v4h1.5V9z"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M12 6V4a4 4 0 0 0-8 0v2H2.75a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h10.5a.75.75 0 0 0 .75-.75v-8.5a.75.75 0 0 0-.75-.75zm.5 1.5v7h-9v-7zM5.5 4v2h5V4a2.5 2.5 0 0 0-5 0",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="LockIcon";let c=i},57477:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M6.42 5.415A.75.75 0 0 0 5 5.75V11h1.5V8.927l.83 1.658a.75.75 0 0 0 1.34 0l.83-1.658V11H11V5.75a.75.75 0 0 0-1.42-.335L8 8.573z"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75V1.75a.75.75 0 0 0-.75-.75zm.75 12.5v-11h11v11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="MIcon";let c=i},69397:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M11.5 8.75h-7v-1.5h7z"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75V1.75a.75.75 0 0 0-.75-.75zm.75 12.5v-11h11v11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="MinusSquareIcon";let c=i},92546:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("g",{clipPath:"url(#ModelsIcon_svg__a)",children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 4.75a2.75 2.75 0 0 1 5.145-1.353l4.372-.95a2.75 2.75 0 1 1 3.835 2.823l.282 2.257a2.75 2.75 0 1 1-2.517 4.46l-2.62 1.145.003.118a2.75 2.75 0 1 1-4.415-2.19L3.013 7.489A2.75 2.75 0 0 1 0 4.75M2.75 3.5a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5m2.715 1.688q.027-.164.033-.333l4.266-.928a2.75 2.75 0 0 0 2.102 1.546l.282 2.257c-.377.165-.71.412-.976.719zM4.828 6.55a2.8 2.8 0 0 1-.413.388l1.072 3.573q.13-.012.263-.012c.945 0 1.778.476 2.273 1.202l2.5-1.093a2.8 2.8 0 0 1 .012-.797zM12 10.25a1.25 1.25 0 1 1 2.5 0 1.25 1.25 0 0 1-2.5 0M5.75 12a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5M11 2.75a1.25 1.25 0 1 1 2.5 0 1.25 1.25 0 0 1-2.5 0",clipRule:"evenodd"})}),(0,l.Y)("defs",{children:(0,l.Y)("clipPath",{children:(0,l.Y)("path",{fill:"#fff",d:"M0 0h16v16H0z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="ModelsIcon";let c=i},90374:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",d:"M7.889 1A2.39 2.39 0 0 0 5.5 3.389H7c0-.491.398-.889.889-.889h.371a.74.74 0 0 1 .292 1.42l-1.43.613A2.68 2.68 0 0 0 5.5 6.992V8h5V6.5H7.108c.12-.26.331-.472.604-.588l1.43-.613A2.24 2.24 0 0 0 8.26 1zM2.75 6a1.5 1.5 0 0 1-1.5 1.5H1V9h.25c.546 0 1.059-.146 1.5-.401V11.5H1V13h5v-1.5H4.25V6zM10 12.85A2.15 2.15 0 0 0 12.15 15h.725a2.125 2.125 0 0 0 1.617-3.504 2.138 2.138 0 0 0-1.656-3.521l-.713.008A2.15 2.15 0 0 0 10 10.133v.284h1.5v-.284a.65.65 0 0 1 .642-.65l.712-.009a.638.638 0 1 1 .008 1.276H12v1.5h.875a.625.625 0 1 1 0 1.25h-.725a.65.65 0 0 1-.65-.65v-.267H10z"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="NumbersIcon";let c=i},6672:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.25 7.25V1h1.5v6.25H15v1.5H8.75V15h-1.5V8.75H1v-1.5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="PlusIcon";let c=i},40813:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M7.25 7.25V4.5h1.5v2.75h2.75v1.5H8.75v2.75h-1.5V8.75H4.5v-1.5z"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1.75A.75.75 0 0 1 1.75 1h12.5a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75zm1.5.75v11h11v-11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="PlusSquareIcon";let c=i},72299:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M7.25 10.75a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0M10.079 7.111A2.25 2.25 0 1 0 5.75 6.25h1.5A.75.75 0 1 1 8 7a.75.75 0 0 0-.75.75V9h1.5v-.629a2.25 2.25 0 0 0 1.329-1.26"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="QuestionMarkIcon";let c=i},49716:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("g",{clipPath:"url(#SearchIcon_svg__a)",children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 1a7 7 0 1 0 4.39 12.453l2.55 2.55 1.06-1.06-2.55-2.55A7 7 0 0 0 8 1M2.5 8a5.5 5.5 0 1 1 11 0 5.5 5.5 0 0 1-11 0",clipRule:"evenodd"})}),(0,l.Y)("defs",{children:(0,l.Y)("clipPath",{children:(0,l.Y)("path",{fill:"#fff",d:"M0 0h16v16H0z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="SearchIcon";let c=i},16494:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",d:"m11.5.94 4.03 4.03-1.06 1.06-2.22-2.22V10h-1.5V3.81L8.53 6.03 7.47 4.97zM1 4.5h4V6H1zM1 12.5h10V14H1zM8 8.5H1V10h7z"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="SortAscendingIcon";let c=i},62270:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 3.5h10V2H1zm0 8h4V10H1zm7-4H1V6h7zm3.5 7.56 4.03-4.03-1.06-1.06-2.22 2.22V6h-1.5v6.19L8.53 9.97l-1.06 1.06z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="SortDescendingIcon";let c=i},79670:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",d:"M11.5.94 7.47 4.97l1.06 1.06 2.22-2.22v8.38L8.53 9.97l-1.06 1.06 4.03 4.03 4.03-4.03-1.06-1.06-2.22 2.22V3.81l2.22 2.22 1.06-1.06zM6 3.5H1V5h5zM6 11.5H1V13h5zM1 7.5h5V9H1z"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="SortUnsortedIcon";let c=i},88416:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M6 7a.75.75 0 1 1-1.5 0A.75.75 0 0 1 6 7M8 7.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5M10.75 7.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6 1a6 6 0 1 0 0 12v2.25a.75.75 0 0 0 1.28.53L10.061 13A6 6 0 0 0 10 1zM1.5 7A4.5 4.5 0 0 1 6 2.5h4a4.5 4.5 0 1 1 0 9h-.25a.75.75 0 0 0-.53.22L7.5 13.44v-1.19a.75.75 0 0 0-.75-.75H6A4.5 4.5 0 0 1 1.5 7",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="SpeechBubbleIcon";let c=i},17784:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75V1.75a.75.75 0 0 0-.75-.75zm.75 12.5v-11h11v11zM5 6h2.25v5.5h1.5V6H11V4.5H5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="TextBoxIcon";let c=i},99145:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 1a3.25 3.25 0 1 0 0 6.5A3.25 3.25 0 0 0 8 1M6.25 4.25a1.75 1.75 0 1 1 3.5 0 1.75 1.75 0 0 1-3.5 0M8 9a8.74 8.74 0 0 0-6.836 3.287.75.75 0 0 0-.164.469v1.494c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-1.494a.75.75 0 0 0-.164-.469A8.74 8.74 0 0 0 8 9m-5.5 4.5v-.474A7.23 7.23 0 0 1 8 10.5c2.2 0 4.17.978 5.5 2.526v.474z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="UserIcon";let c=i},49393:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M8 1c.664 0 1.282.2 1.797.542l-.014.072-.062.357-.357.062c-.402.07-.765.245-1.06.493a1.75 1.75 0 1 0 0 3.447c.295.25.658.424 1.06.494l.357.062.062.357.014.072A3.25 3.25 0 1 1 8 1"}),(0,l.Y)("path",{fill:"currentColor",d:"M9.59 4.983A.75.75 0 0 1 9.62 3.51l.877-.152a.75.75 0 0 0 .61-.61l.153-.878a.75.75 0 0 1 1.478 0l.152.877a.75.75 0 0 0 .61.61l.878.153a.75.75 0 0 1 0 1.478l-.877.152a.75.75 0 0 0-.61.61l-.153.878a.75.75 0 0 1-1.478 0l-.152-.877a.75.75 0 0 0-.61-.61l-.878-.153z"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.164 12.287A8.74 8.74 0 0 1 8 9a8.74 8.74 0 0 1 6.836 3.287.75.75 0 0 1 .164.469v1.494a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75v-1.494a.75.75 0 0 1 .164-.469m1.336.74v.473h11v-.474A7.23 7.23 0 0 0 8 10.5c-2.2 0-4.17.978-5.5 2.526",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="UserSparkleIcon";let c=i},40631:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M14.367 3.29a.75.75 0 0 1 .547.443 5.001 5.001 0 0 1-6.072 6.736l-3.187 3.186a2.341 2.341 0 0 1-3.31-3.31L5.53 7.158a5.001 5.001 0 0 1 6.736-*********** 0 0 1 .237 1.22L10.5 4.312V5.5h1.19l2.003-2.004a.75.75 0 0 1 .674-.206m-.56 2.214L12.53 6.78A.75.75 0 0 1 12 7H9.75A.75.75 0 0 1 9 6.25V4a.75.75 0 0 1 .22-.53l1.275-1.276a3.501 3.501 0 0 0-3.407 4.865.75.75 0 0 1-.16.823l-3.523 3.523a.84.84 0 1 0 1.19 1.19L8.118 9.07a.75.75 0 0 1 .823-.16 3.5 3.5 0 0 0 4.865-3.407",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="WrenchIcon";let c=i},51211:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m1.97-4.97L8 9.06l-1.97 1.97-1.06-1.06L6.94 8 4.97 6.03l1.06-1.06L8 6.94l1.97-1.97 1.06 1.06L9.06 8l1.97 1.97z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="XCircleFillIcon";let c=i},84260:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),l=r(98358);function a(e){return(0,l.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,l.Y)("path",{fill:"currentColor",d:"M6.94 8 4.97 6.03l1.06-1.06L8 6.94l1.97-1.97 1.06 1.06L9.06 8l1.97 1.97-1.06 1.06L8 9.06l-1.97 1.97-1.06-1.06z"}),(0,l.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,l.Y)(n.I,{ref:o,...e,component:a}));i.displayName="XCircleIcon";let c=i},48681:(e,o,r)=>{r.d(o,{p:()=>R,n:()=>x});var t=r(71218),n=r(22933),l=r(39659),a=r(80838),i=r(8467),c=r(94167),d=r(98358);let s=(e,o,r,n)=>{let l=`.${e}-input`,a=`.${e}-btn`;return(0,t.AH)({display:"inline-flex !important",width:"auto",[`& > ${l}`]:{flexGrow:1,...n&&{borderTopRightRadius:"0px !important",borderBottomRightRadius:"0px !important"},"&:disabled":{border:"none",background:o.colors.actionDisabledBackground,"&:hover":{borderRight:`1px solid ${o.colors.actionDisabledBorder} !important`}},"&[data-validation]":{marginRight:0}},...r&&{[`& > ${a}`]:{boxShadow:"none !important"}},...n&&{[`& > ${a}`]:{borderTopLeftRadius:"0px !important",borderBottomLeftRadius:"0px !important"}},[`& > ${a} > span`]:{verticalAlign:"middle"},[`& > ${a}:disabled, & > ${a}:disabled:hover`]:{borderLeft:`1px solid ${o.colors.actionDisabledBorder} !important`,backgroundColor:`${o.colors.actionDisabledBackground} !important`,color:`${o.colors.actionDisabledText} !important`}},"","")};var u=r(65848),g=r.n(u),p=r(46792),h=r(8161),f=r(84437),m=r(69391),v=r(58829),b=r(29121);let w=e=>{let o=(0,u.useRef)(!0),r=(0,u.useCallback)(()=>{o.current=!0},[]);return{callbackOnceUntilReset:(0,u.useCallback)(()=>{o.current&&(e(),o.current=!1)},[e]),reset:r}},y=e=>{let{handlers:o,stopOnDefaultPrevented:r}=e;return(0,u.useCallback)(e=>{for(let t of o){if(r&&e.defaultPrevented)return;null==t||t(e)}},[o,r])},x=(e,o,r,t)=>{let{validationState:n,type:a,hasValue:i,useNewShadows:c,useNewFormUISpacing:d,useNewBorderRadii:s,locked:u}=r,{useFocusWithin:g=!1}=t,p=`.${e}-input`,h=`.${e}-input-affix-wrapper`,f=`.${e}-input-affix-wrapper-disabled`,v=`.${e}-input-affix-wrapper-focused`,b=`.${e}-input-clear-icon`,w=`.${e}-input-prefix`,y=`.${e}-input-suffix`,x=(0,m.yu)(o,n),C=g?"focus-within":"focus";return{"&&":{lineHeight:o.typography.lineHeightBase,minHeight:o.general.heightSm,borderColor:o.colors.actionDefaultBorderDefault,...n&&{borderColor:x},"&:hover":{borderColor:n?x:o.colors.actionPrimaryBackgroundHover},[`&:${C}`]:{outlineColor:n?x:o.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",...!c&&{boxShadow:"none"},borderColor:"transparent"},"&:focus-visible":{outlineColor:n?x:o.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",...!c&&{boxShadow:"none"},borderColor:"transparent"},...d&&{[`& + .${e}-form-message`]:{marginTop:o.spacing.sm}}},[`&${p}, ${p}`]:{backgroundColor:"transparent",...s&&{borderRadius:o.borders.borderRadiusSm},"&:disabled":{backgroundColor:o.colors.actionDisabledBackground,color:o.colors.actionDisabledText,borderColor:o.colors.actionDisabledBorder},"&::placeholder":{color:o.colors.textPlaceholder}},[`&${h}`]:{backgroundColor:"transparent",lineHeight:o.typography.lineHeightBase,paddingTop:5,paddingBottom:5,minHeight:o.general.heightSm,"::before":{lineHeight:o.typography.lineHeightBase},"&:hover":{borderColor:n?x:o.colors.actionPrimaryBackgroundHover},[`input.${e}-input`]:{borderRadius:0}},[`&${f}`]:{backgroundColor:o.colors.actionDisabledBackground},[`&${v}`]:{boxShadow:"none",[`&&, &:${C}`]:{outlineColor:n?x:o.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",boxShadow:"none",borderColor:"transparent"}},[b]:{fontSize:o.typography.fontSizeSm},[w]:{marginRight:o.spacing.sm,color:o.colors.textSecondary},[y]:{marginLeft:o.spacing.sm,color:o.colors.textSecondary,...!i&&"number"===a&&{display:"none"}},...u?{[`&${h}`]:{backgroundColor:o.colors.backgroundSecondary},[`input.${e}-input`]:{backgroundColor:`${o.colors.backgroundSecondary} !important`,color:`${o.colors.textPrimary} !important`}}:{},...(0,l.Cx)(o.options.enableAnimation)}},C=(e,o,r)=>{let{validationState:n,type:l,hasValue:a,useNewShadows:i,useNewBorderRadii:c,locked:d}=r,s=x(e,o,{validationState:n,type:l,hasValue:a,useNewShadows:i,useNewBorderRadii:c,locked:d},{});return(0,t.AH)((0,m.dg)(s),"","")},k=(0,u.forwardRef)(function(e,o){let{validationState:r,autoComplete:t="off",dangerouslySetAntdProps:s,dangerouslyAppendEmotionCSS:m,onChange:x,onClear:k,onFocus:D,onPressEnter:R,onCompositionStart:S,onCompositionEnd:$,componentId:Y,shouldPreventFormSubmission:B,analyticsEvents:H,readOnly:A,locked:T,...I}=e,P=(0,v.W)("databricks.fe.observability.defaultComponentView.input",!1),z=(0,p.xW)(),{classNamePrefix:V,theme:M}=(0,a.wn)(),[L,O]=g().useState(void 0!==I.value&&null!==I.value&&""!==I.value),{useNewShadows:F,useNewBorderRadii:_}=(0,i.p)(),N=(0,u.useMemo)(()=>null!=H?H:P?[h.s7.OnValueChange,h.s7.OnView]:[h.s7.OnValueChange],[H,P]),E=(0,h.ei)({componentType:h.v_.Input,componentId:Y,analyticsEvents:N,valueHasNoPii:!1}),W=(0,u.useRef)(!0);(0,u.useEffect)(()=>{W.current&&(E.onView(),W.current=!1)},[E]);let{callbackOnceUntilReset:G,reset:K}=w(E.onValueChange),q=(0,u.useCallback)(e=>{G(),!e.target.value&&e.nativeEvent instanceof InputEvent==!1&&k?(null==k||k(),O(!1)):(null==x||x(e),O(!!e.target.value))},[x,k,G]),U=(0,u.useCallback)(e=>{K(),null==D||D(e)},[D,K]),j=(0,u.useCallback)(e=>{var o;if(!(null!==(o=z.formRef)&&void 0!==o&&o.current))return;e.preventDefault(),z.formRef.current.requestSubmit()},[z.formRef]),X=(0,b.s)({callback:j,allowBasicEnter:!B,allowPlatformEnter:!B}),Z=y((0,u.useMemo)(()=>({handlers:[X.onKeyDown,R],stopOnDefaultPrevented:!1}),[X.onKeyDown,R])),Q=y((0,u.useMemo)(()=>({handlers:[X.onCompositionStart,S]}),[X.onCompositionStart,S])),J=y((0,u.useMemo)(()=>({handlers:[X.onCompositionEnd,$]}),[X.onCompositionEnd,$]));return(0,d.Y)(l.wC,{children:(0,d.Y)(n.A,{...(0,c.VG)(),autoComplete:t,"data-validation":r,ref:o,css:[C(V,M,{validationState:r,type:I.type,hasValue:L,useNewShadows:F,useNewBorderRadii:_,locked:T}),m,"",""],onChange:q,onFocus:U,onPressEnter:Z,onCompositionStart:Q,onCompositionEnd:J,...I,readOnly:T||A,suffix:T?(0,d.Y)(f.A,{}):I.suffix,...s,...E.dataComponentProps})})}),D=(0,u.forwardRef)(function(e,o){let{validationState:r,autoComplete:t="off",dangerouslySetAntdProps:s,dangerouslyAppendEmotionCSS:u,...g}=e,{classNamePrefix:p,theme:h}=(0,a.wn)(),{useNewShadows:f,useNewBorderRadii:m}=(0,i.p)();return(0,d.Y)(l.wC,{children:(0,d.Y)(n.A.Password,{...(0,c.VG)(),visibilityToggle:!1,ref:o,autoComplete:t,css:[C(p,h,{validationState:r,useNewShadows:f,useNewBorderRadii:m}),u,"",""],...g,...s})})}),R=Object.assign(k,{TextArea:(0,u.forwardRef)(function(e,o){let{validationState:r,autoComplete:t="off",dangerouslySetAntdProps:s,dangerouslyAppendEmotionCSS:g,componentId:f,analyticsEvents:m,onChange:x,onFocus:k,onKeyDown:D,onCompositionStart:R,onCompositionEnd:S,allowFormSubmitOnEnter:$=!1,...Y}=e,B=(0,v.W)("databricks.fe.observability.defaultComponentView.textArea",!1),H=(0,p.xW)(),{classNamePrefix:A,theme:T}=(0,a.wn)(),{useNewShadows:I,useNewBorderRadii:P}=(0,i.p)(),z=(0,u.useMemo)(()=>null!=m?m:B?[h.s7.OnValueChange,h.s7.OnView]:[h.s7.OnValueChange],[m,B]),V=(0,h.ei)({componentType:h.v_.TextArea,componentId:f,analyticsEvents:z,valueHasNoPii:!1}),M=(0,u.useRef)(!0);(0,u.useEffect)(()=>{M.current&&(V.onView(),M.current=!1)},[V]);let{callbackOnceUntilReset:L,reset:O}=w(V.onValueChange),F=(0,u.useCallback)(e=>{L(),null==x||x(e)},[x,L]),_=(0,u.useCallback)(e=>{O(),null==k||k(e)},[k,O]),N=(0,u.useCallback)(e=>{var o;if(!(null!==(o=H.formRef)&&void 0!==o&&o.current))return;e.preventDefault(),H.formRef.current.requestSubmit()},[H.formRef]),E=(0,b.s)({callback:N,allowBasicEnter:$,allowPlatformEnter:!0}),W=y((0,u.useMemo)(()=>({handlers:[E.onKeyDown,D],stopOnDefaultPrevented:!0}),[D,E.onKeyDown])),G=y((0,u.useMemo)(()=>({handlers:[E.onCompositionStart,R]}),[E.onCompositionStart,R])),K=y((0,u.useMemo)(()=>({handlers:[E.onCompositionEnd,S]}),[E.onCompositionEnd,S]));return(0,d.Y)(l.wC,{children:(0,d.Y)(n.A.TextArea,{...(0,c.VG)(),ref:o,autoComplete:t,css:[C(A,T,{validationState:r,useNewShadows:I,useNewBorderRadii:P}),g,"",""],onChange:F,onFocus:_,onKeyDown:W,onCompositionStart:G,onCompositionEnd:K,"data-component-type":h.v_.TextArea,"data-component-id":f,...Y,...s})})}),Password:D,Group:e=>{let{dangerouslySetAntdProps:o,dangerouslyAppendEmotionCSS:r,compact:t=!0,...u}=e,{classNamePrefix:g,theme:p}=(0,a.wn)(),{useNewShadows:h,useNewBorderRadii:f}=(0,i.p)();return(0,d.Y)(l.wC,{children:(0,d.Y)(n.A.Group,{...(0,c.VG)(),css:[s(g,p,h,f),r,"",""],compact:t,...u,...o})})}})},13177:(e,o,r)=>{r.d(o,{AG:()=>R,VB:()=>C,_v:()=>S,c4:()=>D,r0:()=>k});var t=r(71218),n=r(48959),l=r(25656),a=r.n(l),i=r(65848),c=r(39659),d=r(80838),s=r(4700),u=r(51211),g=r(5844),p=r(80061),h=r(23240),f=r(14687),m=r(8467),v=r(69391),b=r(94167),w=r(98358);let y=(e,o)=>(0,v.dg)({fontSize:null!=o?o:e.general.iconFontSize});function x(e,o){let{children:r,validationState:l,loading:x,loadingDescription:C="Select",mode:D,options:R,notFoundContent:S,optionFilterProp:$,dangerouslySetAntdProps:Y,virtual:B,dropdownClassName:H,id:A,onDropdownVisibleChange:T,maxHeight:I,...P}=e,{theme:z,getPrefixedClassName:V}=(0,d.wn)(),{useNewShadows:M,useNewFormUISpacing:L}=(0,m.p)(),O=V("select"),[F,_]=(0,i.useState)(!1),[N,E]=(0,i.useState)(""),W=null!=I?I:8.5*z.general.heightSm;return(0,i.useEffect)(()=>{E(A||a().uniqueId("dubois-select-"))},[A]),(0,i.useEffect)(()=>{var e;null===(e=document.getElementById(N))||void 0===e||e.setAttribute("aria-expanded","false")},[N]),(0,w.Y)(t.Z2,{children:e=>{let{css:a}=e;return(0,w.FD)(c.wC,{children:[x&&(0,w.Y)(f.G,{description:C}),(0,w.Y)(n.A,{onDropdownVisibleChange:e=>{null==T||T(e),_(e)},...F?{}:{"aria-owns":void 0,"aria-controls":void 0,"aria-activedescendant":void 0},id:N,css:function(e){let{clsPrefix:o,theme:r,validationState:n,useNewFormUISpacing:l}=e,a=`.${o}-focused`,i=`.${o}-open`,d=`.${o}-single`,s=`.${o}-selector`,u=`.${o}-disabled`,g=`.${o}-multiple`,p=`.${o}-selection-item`,h=`.${o}-selection-overflow`,f=`.${o}-selection-overflow-item`,m=`.${o}-selection-overflow-item-suffix`,w=`.${o}-arrow`,y=`.${o}-arrow-loading`,x=`.${o}-selection-placeholder`,C=`.${o}-selection-item-remove`,k=`.${o}-selection-search`,D=`.${o}-show-search`,R=`.${o}-clear`,S=`.${o}-allow-clear`,$=`.${o}-selection-search-input`,Y=`.${o.replace("-select","")}-form-message`,B=(0,v.yu)(r,n),H={...(0,b.De)(r),...l&&{[`& + ${Y}`]:{marginTop:r.spacing.sm}},"&:hover":{[s]:{borderColor:r.colors.actionDefaultBorderHover}},[s]:{paddingLeft:12,paddingRight:0,color:r.colors.textPrimary,backgroundColor:"transparent",height:r.general.heightSm,"::after":{lineHeight:r.typography.lineHeightBase},"::before":{lineHeight:r.typography.lineHeightBase}},[d]:{[`&${s}`]:{height:r.general.heightSm}},[p]:{color:r.colors.textPrimary,paddingRight:32,lineHeight:r.typography.lineHeightBase,paddingTop:5,paddingBottom:5},[k]:{right:24,left:8,marginInlineStart:4,[$]:{color:r.colors.actionDefaultTextDefault,height:24}},[`&${d}`]:{[$]:{height:r.general.heightSm}},[`&${D}${i}${d}`]:{[p]:{color:r.colors.actionDisabledText}},[R]:{right:24,backgroundColor:"transparent"},[`&${a}`]:{[s]:{outlineColor:r.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",borderColor:"transparent",boxShadow:"none"}},[`&${u}`]:{[s]:{backgroundColor:r.colors.actionDisabledBackground,color:r.colors.actionDisabledText,border:"transparent"},[p]:{color:r.colors.actionDisabledText},[w]:{color:r.colors.actionDisabledText}},[w]:{height:r.general.iconFontSize,width:r.general.iconFontSize,top:(r.general.heightSm-r.general.iconFontSize)/2,marginTop:0,color:r.colors.textSecondary,fontSize:r.general.iconFontSize,".anticon":{pointerEvents:"none"},[`&${y}`]:{top:(r.general.heightSm-r.general.iconFontSize)/2,display:"flex",alignItems:"center",justifyContent:"center",fontSize:r.general.iconFontSize}},[x]:{color:r.colors.textPlaceholder,right:"auto",left:"auto",width:"100%",paddingRight:32,lineHeight:r.typography.lineHeightBase,alignSelf:"center"},[`&${g}`]:{[s]:{paddingTop:3,paddingBottom:3,paddingLeft:8,paddingRight:30,minHeight:r.general.heightSm,height:"auto","&::after":{margin:0}},[p]:{backgroundColor:r.colors.tagDefault,color:r.colors.textPrimary,border:"none",height:20,lineHeight:r.typography.lineHeightBase,fontSize:r.typography.fontSizeBase,marginInlineEnd:4,marginTop:2,marginBottom:2,paddingRight:0,paddingTop:0,paddingBottom:0},[h]:{minHeight:24},[f]:{alignSelf:"auto",height:24,lineHeight:r.typography.lineHeightBase},[k]:{marginTop:0,left:0,right:0},[`&${u}`]:{[p]:{paddingRight:2}},[w]:{top:(r.general.heightSm-r.general.iconFontSize)/2},[`&${S}`]:{[R]:{top:(r.general.heightSm-r.general.iconFontSize+4)/2}},[x]:{paddingLeft:4,color:r.colors.textPlaceholder},[`&:not(${a})`]:{[m]:{height:0}}},[`&${g}${u}`]:{[p]:{color:r.colors.actionDisabledText}},[`&${S}`]:{[p]:{paddingRight:0},[s]:{paddingRight:52},[R]:{top:(r.general.heightSm-r.general.iconFontSize+4)/2,opacity:100,width:r.general.iconFontSize,height:r.general.iconFontSize,marginTop:0}},[C]:{color:r.colors.textPrimary,borderTopRightRadius:r.legacyBorders.borderRadiusMd,borderBottomRightRadius:r.legacyBorders.borderRadiusMd,height:r.general.iconFontSize,width:r.general.iconFontSize,lineHeight:r.typography.lineHeightBase,paddingInlineEnd:0,marginInlineEnd:0,"& > .anticon":{height:r.general.iconFontSize-4,fontSize:r.general.iconFontSize-4},"&:hover":{color:r.colors.actionTertiaryTextHover,backgroundColor:r.colors.tagHover},"&:active":{color:r.colors.actionTertiaryTextPress,backgroundColor:r.colors.tagPress}},...n&&{[`& > ${s}`]:{borderColor:B,"&:hover":{borderColor:B}},[`&${a} > ${s}`]:{outlineColor:B,outlineOffset:-2}},...(0,c.Cx)(r.options.enableAnimation)},A=(0,v.dg)(H);return(0,t.AH)(A,"","")}({clsPrefix:O,theme:z,validationState:l,useNewFormUISpacing:L}),removeIcon:(0,w.Y)(s.A,{"aria-hidden":"false",css:y(z)}),clearIcon:(0,w.Y)(u.A,{"aria-hidden":"false",css:y(z,12),"aria-label":"close-circle"}),ref:o,suffixIcon:x&&"tags"===D?(0,w.Y)(g.A,{spin:!0,"aria-label":"loading","aria-hidden":"false",css:y(z,12)}):(0,w.Y)(p.A,{css:y(z)}),menuItemSelectedIcon:(0,w.Y)(h.A,{css:y(z)}),showArrow:!0,dropdownMatchSelectWidth:!0,notFoundContent:null!=S?S:(0,w.Y)("div",{css:(0,t.AH)({color:z.colors.textSecondary,textAlign:"center"},"",""),children:"No results found"}),dropdownClassName:a([function(e,o,r){let n=`.${e}-item-option`,l=`.${e}-item-option-active`,a=`.${e}-item-option-selected`,i=`.${e}-item-option-state`,d={borderColor:o.colors.borderDecorative,borderWidth:1,borderStyle:"solid",zIndex:o.options.zIndexBase+50,boxShadow:o.general.shadowLow,...(0,b.De)(o),[n]:{height:o.general.heightSm},[l]:{backgroundColor:o.colors.actionTertiaryBackgroundHover,height:o.general.heightSm,"&:hover":{backgroundColor:o.colors.actionTertiaryBackgroundHover}},[a]:{backgroundColor:o.colors.actionTertiaryBackgroundHover,fontWeight:"normal","&:hover":{backgroundColor:o.colors.actionTertiaryBackgroundHover}},[i]:{color:o.colors.textSecondary,"& > span":{verticalAlign:"middle"}},[`.${e}-loading-options`]:{pointerEvents:"none",margin:"0 auto",height:o.general.heightSm,display:"block"},...(0,c.Cx)(o.options.enableAnimation),...(0,v.WO)(o,r)},s=(0,v.dg)(d);return(0,t.AH)(s,"","")}(O,z,M),H]),listHeight:W,maxTagPlaceholder:e=>`+ ${e.length} more`,mode:D,options:R,loading:x,filterOption:!0,virtual:null!=B?B:r&&Array.isArray(r)&&8!==r.length||R&&8!==R.length||!r&&!R,optionFilterProp:null!=$?$:"children",...P,...Y,children:x&&"tags"!==D?(0,w.FD)(w.FK,{children:[r,(0,w.Y)(k,{disabled:!0,value:"select-loading-options",className:`${O}-loading-options`,children:(0,w.Y)(g.A,{"aria-hidden":"false",spin:!0,css:(0,t.AH)({fontSize:20,color:z.colors.textSecondary,lineHeight:"20px"},"",""),"aria-label":"loading"})})]}):r})]})}})}let C=(0,i.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,...t}=e;return(0,w.Y)(n.A.Option,{...t,ref:o,...r})});C.isSelectOption=!0;let k=C,D=(()=>{let e=(0,i.forwardRef)(function(e,o){return(0,w.Y)(n.A.OptGroup,{...e,isSelectOptGroup:!0,ref:o})});return e.isSelectOptGroup=!0,e})(),R=D,S=(()=>{let e=(0,i.forwardRef)(x);return e.Option=C,e.OptGroup=D,e})()},80333:(e,o,r)=>{r.d(o,{p:()=>h});var t=r(71218),n=r(59859),l=r(25656),a=r(65848),i=r.n(a),c=r(39659),d=r(80838),s=r(8467),u=r(69391),g=r(60499),p=r(98358);let h=e=>{let{children:o,title:r,placement:h="top",dataTestId:f,dangerouslySetAntdProps:m,silenceScreenReader:v=!1,useAsLabel:b=!1,...w}=e,{theme:y}=(0,d.wn)(),{useNewShadows:x}=(0,s.p)(),C=(0,a.useRef)(null),k=(0,g.Y)("dubois-tooltip-component-"),D=null!=m&&m.id?null==m?void 0:m.id:k;if(!r)return(0,p.Y)(i().Fragment,{children:o});let R=v?{}:{"aria-live":"polite","aria-relevant":"additions"};f&&(R["data-testid"]=f);let S=r&&i().isValidElement(r)?i().cloneElement(r,R):(0,p.Y)("span",{...R,children:r}),$={"aria-hidden":!1},Y=e=>{if(!C.current||e.currentTarget.hasAttribute("aria-describedby")||e.currentTarget.hasAttribute("aria-labelledby"))return;D&&(e.currentTarget.setAttribute("aria-live","polite"),b?e.currentTarget.setAttribute("aria-labelledby",D):e.currentTarget.setAttribute("aria-describedby",D))},B=e=>{if(!C||!e.currentTarget.hasAttribute("aria-describedby")&&!e.currentTarget.hasAttribute("aria-labelledby"))return;b?e.currentTarget.removeAttribute("aria-labelledby"):e.currentTarget.removeAttribute("aria-describedby"),e.currentTarget.removeAttribute("aria-live")},H={onMouseEnter:e=>{Y(e)},onMouseLeave:e=>{B(e)},onFocus:e=>{Y(e)},onBlur:e=>{B(e)}},A=i().isValidElement(o)?i().cloneElement(o,{...$,...H,...o.props}):(0,l.isNil)(o)?o:(0,p.Y)("span",{...$,...H,children:o}),{overlayInnerStyle:T,overlayStyle:I,...P}=m||{};return(0,p.Y)(c.wC,{children:(0,p.Y)(n.A,{id:D,ref:C,title:S,placement:h,trigger:["hover","focus"],overlayInnerStyle:{backgroundColor:"#2F3941",lineHeight:"22px",padding:"4px 8px",boxShadow:y.general.shadowLow,...T,...(0,u.WO)(y,x)},overlayStyle:{zIndex:y.options.zIndexBase+70,...I},css:(0,t.AH)({...(0,c.Cx)(y.options.enableAnimation)},"",""),...P,...w,children:A})})}},14687:(e,o,r)=>{r.d(o,{G:()=>i,B:()=>a});var t=r(65848);let n=Symbol("NOT_INITIALIZED"),l=0,a=(0,t.createContext)(null),i=e=>{let{description:o="Generic UI loading state"}=e,r=function(e){let o=(0,t.useRef)(n);if(o.current===n){let r=e();return o.current=r,r}return o.current}(()=>l++),i=(0,t.useContext)(a);return(0,t.useLayoutEffect)(()=>(i&&i.startLoading(r,o),()=>{i&&i.endLoading(r)}),[r,o,i]),null}},49817:(e,o,r)=>{r.d(o,{ZF:()=>B,aF:()=>R,k3:()=>y});var t=r(71218),n=r(87685),l=r(65848),a=r(27443),i=r(8161),c=r(47088),d=r(39659),s=r(80838),u=r(4700),g=r(89481),p=r(69391),h=r(8467),f=r(94167),m=r(58829),v=r(80842),b=r(98358);let w=(0,l.createContext)({isInsideModal:!0}),y=()=>(0,l.useContext)(w),x={normal:640,wide:880},C=e=>{let{theme:o,clsPrefix:r,hasFooter:n=!0,maxedOutHeight:l,useNewShadows:a,useNewBorderRadii:i}=e,c=`.${r}-modal-close`,s=`.${r}-modal-close-x`,u=`.${r}-modal-title`,g=`.${r}-modal-content`,h=`.${r}-modal-body`,m=`.${r}-modal-header`,v=`.${r}-modal-footer`,b=`.${r}-btn`,w=`.${r}-dropdown-button`,y=o.spacing.lg,x=o.general.heightSm,C="90vh",k=n?52:0,D=`calc(${C} - 64px - ${k}px - ${y}px)`;return(0,t.AH)({"&&":{...(0,f.De)(o)},[m]:{background:"transparent",paddingTop:o.spacing.md,paddingLeft:o.spacing.lg,paddingRight:o.spacing.md,paddingBottom:o.spacing.md},[v]:{height:k,paddingTop:o.spacing.lg-8,paddingLeft:y,paddingRight:y,marginTop:"auto",[`${b} + ${b}`]:{marginLeft:o.spacing.sm},[`${w} > ${b}:nth-of-type(2)`]:{marginLeft:-1}},[s]:{fontSize:o.general.iconSize,height:x,width:x,lineHeight:"normal",display:"flex",alignItems:"center",justifyContent:"center",color:o.colors.textSecondary},[c]:{height:x,width:x,margin:"16px 16px 0 0",borderRadius:i?o.borders.borderRadiusSm:o.legacyBorders.borderRadiusMd,backgroundColor:o.colors.actionDefaultBackgroundDefault,borderColor:o.colors.actionDefaultBackgroundDefault,color:o.colors.actionDefaultTextDefault,"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover,borderColor:o.colors.actionDefaultBackgroundHover,color:o.colors.actionDefaultTextHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress,borderColor:o.colors.actionDefaultBackgroundPress,color:o.colors.actionDefaultTextPress},"&:focus-visible":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:o.colors.actionDefaultBorderFocus}},[u]:{fontSize:o.typography.fontSizeXl,lineHeight:o.typography.lineHeightXl,fontWeight:o.typography.typographyBoldFontWeight,paddingRight:y,minHeight:32,display:"flex",alignItems:"center",overflowWrap:"anywhere"},[g]:{backgroundColor:o.colors.backgroundPrimary,maxHeight:C,height:l?C:"",overflow:"hidden",paddingBottom:y,display:"flex",flexDirection:"column",boxShadow:a?o.shadows.xl:o.general.shadowHigh,...i&&{borderRadius:o.borders.borderRadiusLg},...(0,p.WO)(o,a)},[h]:{overflowY:"auto",maxHeight:D,paddingLeft:y,paddingRight:y,paddingTop:8,paddingBottom:8,...(0,p.Ud)(o)},...(0,d.Cx)(o.options.enableAnimation)},"","")};function k(e){return e?`${e}.footer.cancel`:"codegen_design-system_src_design-system_modal_modal.tsx_260"}function D(e){let{componentId:o,onOk:r,onCancel:t,confirmLoading:n,okText:l,cancelText:i,okButtonProps:c,cancelButtonProps:d,autoFocusButton:s,shouldStartInteraction:u}=e;return(0,b.FD)(b.FK,{children:[i&&(0,b.Y)(a.$n,{componentId:k(o),onClick:e=>{null==t||t(e)},autoFocus:"cancel"===s,dangerouslyUseFocusPseudoClass:!0,shouldStartInteraction:u,...d,children:i}),l&&(0,b.Y)(a.$n,{componentId:o?`${o}.footer.ok`:"codegen_design-system_src_design-system_modal_modal.tsx_271",loading:n,onClick:e=>{null==r||r(e)},type:"primary",autoFocus:"ok"===s,dangerouslyUseFocusPseudoClass:!0,shouldStartInteraction:u,...c,children:l})]})}function R(e){return(0,b.Y)(c.vR.Provider,{value:c.pz,children:(0,b.Y)(S,{...e})})}function S(e){let{componentId:o,analyticsEvents:r=[i.s7.OnView],okButtonProps:a,cancelButtonProps:c,dangerouslySetAntdProps:g,children:p,title:y,footer:R,size:S="normal",verticalSizing:$="dynamic",autoFocusButton:Y,truncateTitle:B,shouldStartInteraction:H,...A}=e,{theme:T,classNamePrefix:I}=(0,s.wn)(),{useNewShadows:P,useNewBorderRadii:z}=(0,h.p)(),V=(0,l.useMemo)(()=>r,[r]),M=(0,i.ei)({componentType:i.v_.Modal,componentId:o,analyticsEvents:V,shouldStartInteraction:H}),{elementRef:L}=(0,v.z)({onView:M.onView}),O=(null==g?void 0:g.closable)===!1,F=(0,l.useRef)(!1);(0,l.useEffect)(()=>{O&&!F.current&&!0===A.visible&&(F.current=!0,M.onView())},[A.visible,O,M]);let _=(0,i.ei)({componentType:i.v_.Button,componentId:k(o),analyticsEvents:[i.s7.OnClick],shouldStartInteraction:H}),N=(0,m.W)("databricks.fe.observability.enableModalDataComponentProps",!1)?(0,i.PS)(p,M.dataComponentProps):p;return(0,b.Y)(d.wC,{children:(0,b.Y)(n.A,{...(0,f.VG)(),css:C({theme:T,clsPrefix:I,hasFooter:null!==R,maxedOutHeight:"maxed_out"===$,useNewShadows:P,useNewBorderRadii:z}),title:(0,b.Y)(d.dg,{children:B?(0,b.Y)("div",{css:(0,t.AH)({textOverflow:"ellipsis",marginRight:T.spacing.md,overflow:"hidden",whiteSpace:"nowrap"},"",""),title:"string"==typeof y?y:void 0,children:y}):y}),footer:null===R?null:(0,b.Y)(d.dg,{children:void 0===R?(0,b.Y)(D,{componentId:o,onOk:A.onOk,onCancel:A.onCancel,confirmLoading:A.confirmLoading,okText:A.okText,cancelText:A.cancelText,okButtonProps:a,cancelButtonProps:c,autoFocusButton:Y,shouldStartInteraction:H}):R}),width:S?x[S]:void 0,closeIcon:(0,b.Y)(u.A,{ref:L}),centered:!0,zIndex:T.options.zIndexBase,maskStyle:{backgroundColor:T.colors.overlayOverlay},...A,onCancel:e=>{var o;_.onClick(e),null===(o=A.onCancel)||void 0===o||o.call(A,e)},...g,children:(0,b.Y)(d.dg,{children:(0,b.Y)(w.Provider,{value:{isInsideModal:!0},children:N})})})})}var $={name:"b9hrb",styles:"position:relative;display:inline-flex;align-items:center"},Y={name:"1o6wc9k",styles:"padding-left:6px"};function B(e){let{theme:o}=(0,s.wn)(),{title:r,onCancel:n,onOk:l,cancelText:i,okText:c,okButtonProps:d,cancelButtonProps:u,...p}=e,h=(0,b.FD)("div",{css:$,children:[(0,b.Y)(g.A,{css:(0,t.AH)({color:o.colors.textValidationDanger,left:2,height:18,width:18,fontSize:18},"","")}),(0,b.Y)("div",{css:Y,children:r})]});return(0,b.Y)(R,{shouldStartInteraction:e.shouldStartInteraction,title:h,footer:[(0,b.Y)(a.$n,{componentId:e.componentId?`${e.componentId}.danger.footer.cancel`:"codegen_design-system_src_design-system_modal_modal.tsx_386",onClick:n,shouldStartInteraction:e.shouldStartInteraction,...u,children:i||"Cancel"},"cancel"),(0,b.Y)(a.$n,{componentId:e.componentId?`${e.componentId}.danger.footer.ok`:"codegen_design-system_src_design-system_modal_modal.tsx_395",type:"primary",danger:!0,onClick:l,loading:e.confirmLoading,shouldStartInteraction:e.shouldStartInteraction,...d,children:c||"Delete"},"discard")],onOk:l,onCancel:n,...p})}},89815:(e,o,r)=>{r.d(o,{dK:()=>x,mB:()=>y,vI:()=>k});var t=r(71218),n=r(81434),l=r(62260),a=r(65848),i=r(27443),c=r(8161),d=r(39659),s=r(80838),u=r(16306),g=r(17061),p=r(13177),h=r(8467),f=r(80842),m=r(69391),v=r(94167),b=r(58829),w=r(98358);function y(e,o,r){let n=`.${e}-pagination`,l=`.${e}-pagination-item`,a=`.${e}-pagination-item-link`,i=`.${e}-pagination-item-active`,c=`.${e}-pagination-item-ellipsis`,d=`.${e}-pagination-next`,s=`.${e}-pagination-prev`,u=`.${e}-pagination-jump-next`,g=`.${e}-pagination-jump-prev`,p=`.${e}-pagination-options-size-changer`,h=`.${e}-pagination-options`,f=`.${e}-pagination-disabled`,v=`.${e}-select-selector`,b={"span[role=img]":{color:o.colors.textSecondary,"> *":{color:"inherit"}},[l]:{backgroundColor:"none",border:"none",color:o.colors.textSecondary,"&:focus-visible":{outline:"auto"},"> a":{color:o.colors.textSecondary,textDecoration:"none","&:hover":{color:o.colors.actionDefaultTextHover},"&:active":{color:o.colors.actionDefaultTextPress}},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress}},[i]:{backgroundColor:o.colors.actionDefaultBackgroundPress,color:o.colors.actionDefaultTextPress,border:"none","> a":{color:o.colors.actionDefaultTextPress},"&:focus-visible":{outline:"auto"},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundPress,color:o.colors.actionDefaultTextPress}},[a]:{border:"none",color:o.colors.textSecondary,"&[disabled]":{display:"none"},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress},"&:focus-visible":{outline:"auto"}},[c]:{color:"inherit"},[`${d}, ${s}, ${u}, ${g}`]:{color:o.colors.textSecondary,"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress},"&:focus-visible":{outline:"auto"},[`&${f}`]:{pointerEvents:"none"}},[`&${n}.mini, ${n}.mini`]:{[`${l}, ${d}, ${s}, ${u}, ${g}`]:{height:"32px",minWidth:"32px",width:"auto",lineHeight:"32px"},[p]:{marginLeft:4},[`input,  ${h}`]:{height:"32px"},...r&&{[`${v}`]:{boxShadow:o.shadows.xs}}}},w=(0,m.dg)(b);return(0,t.AH)(w,"","")}let x=function(e){let{currentPageIndex:o,pageSize:r=10,numTotal:t,onChange:i,style:u,hideOnSinglePage:g,dangerouslySetAntdProps:p,componentId:m,analyticsEvents:x}=e,C=(0,b.W)("databricks.fe.observability.defaultComponentView.pagination",!1),{classNamePrefix:k,theme:D}=(0,s.wn)(),{useNewShadows:R}=(0,h.p)(),{pageSizeSelectAriaLabel:S,pageQuickJumperAriaLabel:$,...Y}=null!=p?p:{},B=(0,a.useRef)(null),H=(0,a.useMemo)(()=>null!=x?x:C?[c.s7.OnValueChange,c.s7.OnView]:[c.s7.OnValueChange],[x,C]),A=(0,c.ei)({componentType:c.v_.Pagination,componentId:m,analyticsEvents:H,valueHasNoPii:!0}),T=(0,a.useCallback)((e,o)=>{A.onValueChange(e),i(e,o)},[A,i]),{elementRef:I}=(0,f.z)({onView:A.onView}),P=(0,n.SV)([B,I]);return(0,a.useEffect)(()=>{if(B&&B.current){let e=B.current.querySelector(`.${k}-select-selection-search-input`);e&&e.setAttribute("aria-label",null!=S?S:"Select page size");let o=B.current.querySelector(`.${k}-pagination-options-quick-jumper > input`);o&&o.setAttribute("aria-label",null!=$?$:"Go to page")}},[$,S,k]),(0,w.Y)(d.wC,{children:(0,w.Y)("div",{ref:P,children:(0,w.Y)(l.A,{...(0,v.VG)(),css:y(k,D,R),current:o,pageSize:r,responsive:!1,total:t,onChange:T,showSizeChanger:!1,showQuickJumper:!1,size:"small",style:u,hideOnSinglePage:g,...Y,...A.dataComponentProps})})})};var C={name:"1u1zie3",styles:"width:120px"};let k=function(e){let{onNextPage:o,onPreviousPage:r,hasNextPage:n,hasPreviousPage:l,nextPageText:d="Next",previousPageText:h="Previous",pageSizeSelect:{options:f,default:m,getOptionText:v,onChange:b,ariaLabel:y="Select page size"}={},componentId:x="design_system.cursor_pagination",analyticsEvents:k=[c.s7.OnValueChange],valueHasNoPii:D}=e,{theme:R,classNamePrefix:S}=(0,s.wn)(),[$,Y]=(0,a.useState)(m),B=(0,a.useMemo)(()=>k,[k]),H=`${x}.page_size`,A=(0,c.ei)({componentType:c.v_.LegacySelect,componentId:H,analyticsEvents:B,valueHasNoPii:D}),T=e=>`${e} / page`;return(0,w.FD)("div",{css:(0,t.AH)({display:"flex",flexDirection:"row",gap:R.spacing.sm,[`.${S}-select-selector::after`]:{content:"none"}},"",""),...A.dataComponentProps,children:[(0,w.Y)(i.$n,{componentId:`${x}.previous_page`,icon:(0,w.Y)(u.A,{}),disabled:!l,onClick:r,type:"tertiary",children:h}),(0,w.Y)(i.$n,{componentId:`${x}.next_page`,endIcon:(0,w.Y)(g.A,{}),disabled:!n,onClick:o,type:"tertiary",children:d}),f&&(0,w.Y)(p._v,{"aria-label":y,value:String($),css:C,onChange:e=>{let o=Number(e);null==b||b(o),Y(o),A.onValueChange(e)},children:f.map(e=>(0,w.Y)(p._v.Option,{value:String(e),children:(v||T)(e)},e))})]})}},95381:(e,o,r)=>{r.r(o),r.d(o,{Anchor:()=>p,Arrow:()=>b,Close:()=>v,Content:()=>f,Root:()=>h,Trigger:()=>m});var t=r(71218),n=r(88002),l=r(65848),a=r(8161),i=r(80838),c=r(63908),d=r(8467),s=r(69391),u=r(94167),g=r(98358);let p=n.Mz,h=e=>{let{children:o,onOpenChange:r,componentId:t,analyticsEvents:i=[a.s7.OnView],...c}=e,d=(0,l.useRef)(!0),s=(0,l.useMemo)(()=>i,[i]),u=(0,a.ei)({componentType:a.v_.Popover,componentId:null!=t?t:"design_system.popover",analyticsEvents:s});(0,l.useEffect)(()=>{c.open&&d.current&&(u.onView(),d.current=!1)},[u,c.open]);let p=(0,l.useCallback)(e=>{e&&d.current&&(u.onView(),d.current=!1),null==r||r(e)},[u,r]);return(0,g.Y)(n.bL,{...c,onOpenChange:p,children:(0,g.Y)(a.z8.Provider,{value:{dataComponentProps:u.dataComponentProps},children:o})})},f=(0,l.forwardRef)(function(e,o){let{children:r,minWidth:t=220,maxWidth:l,...s}=e,{getPopupContainer:p}=(0,c.G)(),{theme:h}=(0,i.wn)(),{useNewShadows:f}=(0,d.p)(),m=(0,a.WF)(a.v_.Popover);return(0,g.Y)(n.ZL,{container:p&&p(),children:(0,g.Y)(n.UC,{...(0,u.VG)(),ref:o,css:[x(h,f),{minWidth:t,maxWidth:l},"",""],sideOffset:4,"aria-label":"Popover content",...s,...m,children:r})})}),m=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,g.Y)(n.l9,{...(0,u.VG)(),ref:o,...t,children:r})}),v=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,g.Y)(n.bm,{ref:o,...t,children:r})}),b=(0,l.forwardRef)(function(e,o){let{children:r,...l}=e,{theme:a}=(0,i.wn)();return(0,g.Y)(n.i3,{css:(0,t.AH)({fill:a.colors.backgroundPrimary,stroke:a.colors.borderDecorative,strokeDashoffset:-w.arrowBottomLength(),strokeDasharray:w.arrowBottomLength()+2*w.arrowSide(),strokeWidth:w.arrowStrokeWidth(),position:"relative",top:-1},"",""),ref:o,width:12,height:6,...l,children:r})}),w={arrowBottomLength:()=>30,arrowHeight:()=>10,arrowSide(){return 2*(this.arrowHeight()**2*2)**.5},arrowStrokeWidth:()=>2},y=(e,o)=>({backgroundColor:e.colors.backgroundPrimary,color:e.colors.textPrimary,lineHeight:e.typography.lineHeightBase,border:`1px solid ${e.colors.borderDecorative}`,borderRadius:e.borders.borderRadiusSm,padding:`${e.spacing.sm}px`,boxShadow:o?e.shadows.lg:e.general.shadowLow,zIndex:e.options.zIndexBase+30,...(0,s.WO)(e,o),a:(0,s.dg)({color:e.colors.actionTertiaryTextDefault,"&:hover, &:focus":{color:e.colors.actionTertiaryTextHover}}),"&:focus-visible":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:e.colors.actionDefaultBorderFocus}}),x=(e,o)=>({...y(e,o)})},17021:(e,o,r)=>{r.d(o,{E:()=>b,d:()=>w});var t=r(71218),n=r(81434),l=r(26976),a=r(65848),i=r.n(a),c=r(8161),d=r(39659),s=r(80838),u=r(8467),g=r(80842),p=r(69391),h=r(94167),f=r(58829),m=r(98358);let v=(0,a.createContext)({size:"middle",spaced:!1,dontTruncate:!1}),b=(0,a.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,...n}=e,{classNamePrefix:c,theme:g}=(0,s.wn)(),{size:h,spaced:b,dontTruncate:w}=(0,a.useContext)(v),y=(0,f.W)("databricks.fe.designsystem.truncateSegmentedControlText",!1)&&!w,{useNewShadows:x}=(0,u.p)(),C=(0,a.useRef)(null);(0,a.useImperativeHandle)(o,()=>C.current);let k=(0,a.useCallback)(()=>{let e="";return i().Children.map(n.children,o=>{"string"==typeof o&&(e+=o)}),e},[n.children]);return(0,a.useEffect)(()=>{if(C.current){let e=C.current.input.closest("label");e&&e.setAttribute("title",k())}},[C,k]),(0,m.Y)(d.wC,{children:(0,m.Y)(l.Ay.Button,{css:function(e,o,r){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],l=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0,i=`.${e}-radio-button-wrapper-checked`,c=`.${e}-radio-button-wrapper`,s=`.${e}-radio-button-wrapper-disabled`,u=`.${e}-radio-button`,g={backgroundColor:o.colors.actionDefaultBackgroundDefault,borderColor:o.colors.actionDefaultBorderDefault,color:o.colors.actionDefaultTextDefault,...a&&{boxShadow:o.shadows.xs},"::before":{display:n?"none":"block",backgroundColor:o.colors.actionDefaultBorderDefault},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover,borderColor:o.colors.actionDefaultBorderHover,color:o.colors.actionDefaultTextHover,"::before":{backgroundColor:o.colors.actionDefaultBorderHover},[`& + ${c}::before`]:{backgroundColor:o.colors.actionDefaultBorderPress}},"&:active":{backgroundColor:o.colors.actionTertiaryBackgroundPress,borderColor:o.colors.actionDefaultBorderPress,color:o.colors.actionTertiaryTextPress},[`&${i}`]:{backgroundColor:o.colors.actionTertiaryBackgroundPress,borderColor:o.colors.actionDefaultBorderPress,color:o.colors.actionTertiaryTextPress,...!a&&{boxShadow:"none"},"::before":{backgroundColor:o.colors.actionDefaultBorderPress},[`& + ${c}::before`]:{backgroundColor:o.colors.actionDefaultBorderPress}},[`&${i}:focus-within`]:{"::before":{width:0}},[`&${c}`]:{padding:"middle"===r?"0 16px":"0 8px",display:"inline-flex",verticalAlign:"middle",...l&&{flexShrink:1,textOverflow:"ellipsis",whiteSpace:"nowrap",minWidth:"small"===r?58:68},"&:first-of-type":{borderTopLeftRadius:o.borders.borderRadiusSm,borderBottomLeftRadius:o.borders.borderRadiusSm},"&:last-of-type":{borderTopRightRadius:o.borders.borderRadiusSm,borderBottomRightRadius:o.borders.borderRadiusSm},...n?{borderWidth:1,borderRadius:o.borders.borderRadiusSm}:{},"&:focus-within":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"-2px",outlineColor:o.colors.actionDefaultBorderFocus},...l&&{"span:last-of-type":{textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"}}},[`&${c}, ${u}`]:{height:"middle"===r?o.general.heightSm:24,lineHeight:o.typography.lineHeightBase,alignItems:"center"},[`&${s}, &${s} + ${s}`]:{color:o.colors.actionDisabledText,backgroundColor:"transparent",borderColor:o.colors.actionDisabledBorder,"&:hover":{color:o.colors.actionDisabledText,borderColor:o.colors.actionDisabledBorder,backgroundColor:"transparent"},"&:active":{color:o.colors.actionDisabledText,borderColor:o.colors.actionDisabledBorder,backgroundColor:"transparent"},"::before":{backgroundColor:o.colors.actionDisabledBorder},[`&${i}`]:{borderColor:o.colors.actionDefaultBorderPress,"::before":{backgroundColor:o.colors.actionDefaultBorderPress}},[`&${i} + ${c}`]:{"::before":{backgroundColor:o.colors.actionDefaultBorderPress}}},...(0,d.Cx)(o.options.enableAnimation)},h=(0,p.dg)(g);return(0,t.AH)(h,"","")}(c,g,h,b,y,x),...n,...r,ref:C})})}),w=(0,a.forwardRef)(function(e,o){var r;let{dangerouslySetAntdProps:i,size:u="middle",spaced:b=!1,onChange:w,componentId:y,analyticsEvents:x,valueHasNoPii:C,dontTruncate:k,...D}=e,R=(0,f.W)("databricks.fe.observability.defaultComponentView.segmentedControlGroup",!1),{classNamePrefix:S}=(0,s.wn)(),$=(0,f.W)("databricks.fe.designsystem.truncateSegmentedControlText",!1)&&!k,Y=(0,a.useMemo)(()=>null!=x?x:R?[c.s7.OnValueChange,c.s7.OnView]:[c.s7.OnValueChange],[x,R]),B=(0,c.ei)({componentType:c.v_.SegmentedControlGroup,componentId:y,analyticsEvents:Y,valueHasNoPii:C}),{elementRef:H}=(0,g.z)({onView:B.onView,value:null!==(r=D.value)&&void 0!==r?r:D.defaultValue}),A=(0,n.SV)([o,H]),T=(0,a.useCallback)(e=>{B.onValueChange(e.target.value),null==w||w(e)},[B,w]);return(0,m.Y)(d.wC,{children:(0,m.Y)(v.Provider,{value:{size:u,spaced:b,dontTruncate:k},children:(0,m.Y)(l.Ay.Group,{...(0,h.VG)(),...D,css:function(e){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2?arguments[2]:void 0,n=`.${e}-radio-group`,l=`.${e}-radio-group-small`,a=`.${e}-radio-button-wrapper`,i={...r&&{display:"flex",maxWidth:"100%"},[`&${n}`]:o?{display:"flex",gap:8,flexWrap:"wrap"}:{},[`&${l} ${a}`]:{padding:"0 12px"}},c=(0,p.dg)(i);return(0,t.AH)(c,"","")}(S,b,$),onChange:T,...i,ref:A,...B.dataComponentProps})})})})},29183:(e,o,r)=>{r.d(o,{B:()=>m,Q:()=>f});var t=r(65848),n=r(97835),l=r(80838),a=r(14687),i=r(66795),c=r(83487),d=r(67559),s=r(32501),u=r(63434),g=r(94167),p=r(98358);let h={container:{name:"6kz1wu",styles:"display:flex;flex-direction:column;align-items:flex-start"},cell:{name:"1t820zr",styles:"width:100%;height:8px;border-radius:4px;background:var(--table-skeleton-color);margin-top:var(--table-skeleton-row-vertical-margin);margin-bottom:var(--table-skeleton-row-vertical-margin)"}},f=e=>{let{lines:o=1,seed:r="",frameRate:a=60,style:c,label:d,...s}=e,{theme:f}=(0,l.wn)(),{size:m}=(0,t.useContext)(i.G),v=(0,n.PT)(r);return(0,p.FD)("div",{...s,...(0,g.VG)(),"aria-busy":!0,css:h.container,role:"status",style:{...c,"--table-skeleton-color":f.isDarkMode?"rgba(255, 255, 255, 0.1)":"rgba(31, 38, 45, 0.1)","--table-skeleton-row-vertical-margin":"small"===m?"4px":"6px"},children:[[...Array(o)].map((e,o)=>(0,p.Y)("div",{css:[h.cell,(0,n.q5)(f,a),{width:`calc(100% - ${v[o%v.length]}px)`},"",""]},o)),(0,p.Y)("span",{css:u.Q,children:d})]})},m=e=>{let{table:o,actionColumnIds:r=[],numRows:t=3,loading:n=!0,loadingDescription:i="Table skeleton rows"}=e,{theme:u}=(0,l.wn)();return(0,p.FD)(p.FK,{children:[n&&(0,p.Y)(a.G,{description:i}),[...Array(t).keys()].map(e=>(0,p.Y)(d.H,{children:o.getFlatHeaders().map(o=>{var t,n;let l=o.column.columnDef.meta;return r.includes(o.id)?(0,p.Y)(s.f,{children:(0,p.Y)(f,{style:{width:u.general.iconSize}})},`cell-${o.id}-${e}`):(0,p.Y)(c.n,{style:null!==(t=null==l?void 0:l.styles)&&void 0!==t?t:(null==l?void 0:l.width)!==void 0?{maxWidth:l.width}:{},children:(0,p.Y)(f,{seed:`skeleton-${o.id}-${e}`,lines:null!==(n=null==l?void 0:l.numSkeletonLines)&&void 0!==n?n:void 0})},`cell-${o.id}-${e}`)})},e))]})}},30109:(e,o,r)=>{r.d(o,{o:()=>u});var t=r(97835),n=r(80838),l=r(14687),a=r(63434),i=r(94167),c=r(98358);let d={name:"116rc6i",styles:"cursor:progress;width:100%;height:28px;display:flex;justify-content:flex-start;align-items:center"},s={name:"9fmdbb",styles:"border-radius:var(--border-radius);height:12px;width:100%"},u=e=>{let{label:o,frameRate:r=60,style:u,loading:g=!0,loadingDescription:p="TitleSkeleton",...h}=e,{theme:f}=(0,n.wn)();return(0,c.FD)("div",{...(0,i.VG)(),css:d,style:{...u,"--border-radius":`${f.general.borderRadiusBase}px`},...h,children:[g&&(0,c.Y)(l.G,{description:p}),(0,c.Y)("span",{css:a.Q,children:o}),(0,c.Y)("div",{"aria-hidden":!0,css:[s,(0,t.q5)(f,r),"",""]})]})}},97835:(e,o,r)=>{r.d(o,{PT:()=>n,q5:()=>a});var t=r(71218);function n(e){return function(e,o){for(let r=e.length-1;r>0;r--){let t=Math.floor((Math.sin((o+String(r)).split("").map(e=>e.charCodeAt(0)).reduce((e,o)=>e+o,0))/2+.5)*(r+1));[e[r],e[t]]=[e[t],e[r]]}return e}([48,24,0],e)}let l=(0,t.i7)({"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),a=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60,r=e.isDarkMode?"rgba(255, 255, 255, 0.1)":"rgba(31, 38, 45, 0.1)",n=e.isDarkMode?"rgba(99, 99, 99, 0.24)":"rgba(129, 129, 129, 0.24)";return(0,t.AH)({animationDuration:"1.4s",background:`linear-gradient(90deg, ${r} 25%, ${n} 37%, ${r} 63%)`,backgroundSize:"400% 100%",animationName:l,animationTimingFunction:`steps(${o}, end)`,animationIterationCount:14,"@media only percy":{animation:"none"}},"","")}},60973:(e,o,r)=>{r.d(o,{y:()=>p});var t=r(71218),n=r(88466),l=r(39659),a=r(80838),i=r(5844),c=r(14687),d=r(69391),s=r(98358);let u=(0,t.i7)({"0%":{transform:"rotate(0deg) translate3d(0, 0, 0)"},"100%":{transform:"rotate(360deg) translate3d(0, 0, 0)"}}),g=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,l=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a={animation:`${u} ${n}s steps(${o}, end) infinite`,...l?{color:"inherit"}:{color:e.colors.textSecondary},animationDelay:`${r}s`,"@media only percy":{animation:"none"}};return(0,t.AH)((0,d.dg)(a),"","")},p=e=>{let{frameRate:o,size:r="default",delay:t,className:d,label:u,animationDuration:p,inheritColor:h,loading:f=!0,loadingDescription:m="Spinner",...v}=e,{classNamePrefix:b,theme:w}=(0,a.wn)(),y="small"===r?"-sm":"large"===r?"-lg":"",x=y?`${b}-spin${y}`:"",C=`${d||""} ${b}-spin ${x} ${b}-spin-spinning ${l.Nb}`.trim(),k=`${b}-spin-dot ${l.Nb}`.trim();return(0,s.FD)("div",{...v,className:C,children:[f&&(0,s.Y)(c.G,{description:m}),(0,s.Y)(n.U,{label:u,children:(0,s.Y)(i.A,{"aria-hidden":"false",css:g(w,o,t,p,h),className:k})})]})}},66795:(e,o,r)=>{r.d(o,{G:()=>g,X:()=>p});var t=r(71218),n=r(88608),l=r.n(n),a=r(65848),i=r(4987),c=r(47088),d=r(80838),s=r(94167),u=r(98358);let g=(0,a.createContext)({size:"default",grid:!1}),p=(0,a.forwardRef)(function(e,o){let{children:r,size:n="default",someRowsSelected:p,style:h,pagination:f,empty:m,className:v,scrollable:b=!1,grid:w=!1,...y}=e,{theme:x}=(0,d.wn)(),C=(0,a.useRef)(null);return(0,a.useImperativeHandle)(o,()=>C.current),(0,u.Y)(c.vR.Provider,{value:c.pz,children:(0,u.Y)(g.Provider,{value:(0,a.useMemo)(()=>({size:n,someRowsSelected:p,grid:w}),[n,p,w]),children:(0,u.FD)("div",{...(0,s.VG)(),...y,style:{...h,"--table-header-active-color":x.colors.actionDefaultTextPress,colorScheme:x.isDarkMode?"dark":void 0,"--table-header-background-color":x.colors.backgroundPrimary,"--table-header-focus-color":x.colors.actionDefaultTextHover,"--table-header-sort-icon-color":x.colors.textSecondary,"--table-header-text-color":x.colors.actionDefaultTextDefault,"--table-row-hover":x.colors.tableRowHover,"--table-separator-color":x.colors.borderDecorative,"--table-resize-handle-color":x.colors.borderDecorative,"--table-spacing-md":`${x.spacing.md}px`,"--table-spacing-sm":`${x.spacing.sm}px`,"--table-spacing-xs":`${x.spacing.xs}px`},css:[i.Ay.tableWrapper,(0,t.AH)({minHeight:!m&&f?150:100},"",""),"",""],className:l()({"table-isScrollable":b,"table-isGrid":w},v),children:[(0,u.FD)("div",{role:"table",ref:C,css:i.Ay.table,tabIndex:b?0:-1,children:[r,m&&(0,u.Y)("div",{css:(0,t.AH)({padding:x.spacing.lg},"",""),children:m})]}),!m&&f&&(0,u.Y)("div",{css:i.Ay.paginationContainer,children:f})]})})})})},83487:(e,o,r)=>{r.d(o,{n:()=>u});var t=r(88608),n=r.n(t),l=r(65848),a=r(66795),i=r(4987),c=r(3078),d=r(98358),s={name:"1hipw91",styles:"&:has(> button){overflow:visible;}"};let u=(0,l.forwardRef)(function(e,o){let{children:r,className:t,ellipsis:u=!1,multiline:g=!1,align:p="left",style:h,wrapContent:f=!0,...m}=e,{size:v,grid:b}=(0,l.useContext)(a.G),w="md";"small"===v&&(w="sm");let y=!0===f?(0,d.Y)(c.o.Text,{ellipsis:!g,size:w,title:!g&&"string"==typeof r&&r||void 0,css:s,children:r}):r;return(0,d.Y)("div",{...m,role:"cell",style:{textAlign:p,...h},ref:o,css:b?void 0:i.PV.cell,className:n()(b&&i.lH.cell,t),children:y})})},83308:(e,o,r)=>{r.d(o,{A:()=>D});var t=r(81434),n=r(88608),l=r.n(n),a=r(65848),i=r(66795),c=r(67559),d=r(4987),s=r(27443),u=r(8161),g=r(80838),p=r(16494),h=r(62270),f=r(79670),m=r(69397),v=r(40813),b=r(95381),w=r(3078),y=r(58829),x=r(80842),C=r(98358);let k=(0,a.forwardRef)(function(e,o){let{style:r,resizeHandler:t,increaseWidthHandler:n,decreaseWidthHandler:l,children:i,...u}=e,{isHeader:p}=(0,a.useContext)(c.g);if(!p)throw Error("`TableHeaderResizeHandle` must be used within a `TableRow` with `isHeader` set to true.");let[h,f]=(0,a.useState)(!1),y=(0,a.useRef)(null),x=(0,a.useRef)(null),k=(0,a.useRef)(!0),D=(0,a.useRef)(!1),{theme:R}=(0,g.wn)(),S=(0,a.useCallback)(e=>{if(!n||!l){null==t||t(e);return}if(h&&!k.current)return;k.current=!1,y.current={x:e.clientX,y:e.clientY},x.current=e,D.current=!1;let o=e=>{y.current&&Math.abs(e.clientX-y.current.x)>2&&x.current&&(D.current=!0,null==t||t(x.current),document.removeEventListener("pointermove",o))},r=()=>{y.current=null,document.removeEventListener("pointermove",o),document.removeEventListener("pointerup",r)};document.addEventListener("pointermove",o),document.addEventListener("pointerup",r)},[h,t,n,l]),$=(0,a.useCallback)(e=>{if(D.current){e.preventDefault(),e.stopPropagation(),D.current=!1;return}},[]),Y=(0,C.Y)("div",{...u,ref:o,onPointerDown:S,onClick:$,css:d.Ay.resizeHandleContainer,style:r,role:"button","aria-label":"Resize Column",children:(0,C.Y)("div",{css:d.Ay.resizeHandle})});return n&&l?(0,C.FD)(b.Root,{componentId:"codegen_design-system_src_design-system_tableui_tableheader.tsx_114",onOpenChange:f,children:[(0,C.Y)(b.Trigger,{asChild:!0,children:Y}),(0,C.FD)(b.Content,{side:"top",align:"center",sideOffset:0,minWidth:135,style:{padding:`${R.spacing.sm} ${R.spacing.md} ${R.spacing.md} ${R.spacing.sm}`},children:[(0,C.FD)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,C.Y)(w.o.Title,{style:{marginBottom:0,marginTop:0},children:"Resize Column"}),(0,C.FD)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center"},children:[(0,C.Y)(s.$n,{onClick:()=>{l()},size:"small",componentId:"design_system.adjustable_width_header.decrease_width_button",icon:(0,C.Y)(m.A,{}),style:{backgroundColor:R.colors.actionTertiaryBackgroundHover}}),(0,C.Y)(s.$n,{onClick:()=>{n()},size:"small",componentId:"design_system.adjustable_width_header.increase_width_button",icon:(0,C.Y)(v.A,{})})]})]}),(0,C.Y)(b.Arrow,{})]})]}):Y}),D=(0,a.forwardRef)(function(e,o){let r,{children:n,ellipsis:s=!1,multiline:g=!1,sortable:m,sortDirection:v,onToggleSort:b,style:D,className:R,isResizing:S=!1,align:$="left",wrapContent:Y=!0,column:B,header:H,setColumnSizing:A,componentId:T,analyticsEvents:I,"aria-label":P,...z}=e,V=(0,y.W)("databricks.fe.observability.defaultComponentView.tableHeader",!1),M=(null==B?void 0:B.getCanResize())||z.resizable||!1,L=(null==H?void 0:H.getResizeHandler())||z.resizeHandler,O=B&&H&&A,{size:F,grid:_}=(0,a.useContext)(i.G),{isHeader:N}=(0,a.useContext)(c.g),[E,W]=(0,a.useState)(v),G=(0,a.useMemo)(()=>null!=I?I:V?[u.s7.OnValueChange,u.s7.OnView]:[u.s7.OnValueChange],[I,V]),K=(0,u.ei)({componentType:u.v_.TableHeader,componentId:T,analyticsEvents:G,valueHasNoPii:!0}),{elementRef:q}=(0,x.z)({onView:K.onView,value:E}),U=(0,t.SV)([o,q]);if(!N)throw Error("`TableHeader` a must be used within a `TableRow` with `isHeader` set to true.");let j=(0,C.Y)(C.FK,{});m&&("asc"===v?(j=(0,C.Y)(p.A,{}),r="ascending"):"desc"===v?(j=(0,C.Y)(h.A,{}),r="descending"):"none"===v&&(j=(0,C.Y)(f.A,{}),r="none")),(0,a.useEffect)(()=>{v!==E&&(W(v),K.onValueChange(v))},[v,E,K]);let X="right"===$,Z="md";"small"===F&&(Z="sm");let Q=Y?(0,C.Y)(w.o.Text,{className:"table-header-text",ellipsis:!g,size:Z,title:!g&&"string"==typeof n&&n||void 0,bold:!0,children:n}):n,J=(0,a.useCallback)(e=>()=>{B&&A&&A(o=>({...o,[B.id]:e}))},[B,A]),ee=(0,a.useCallback)(()=>{B&&A&&J(B.getSize()+10)()},[B,A,J]),eo=(0,a.useCallback)(()=>{B&&A&&J(B.getSize()-10)()},[B,A,J]),er=M&&L?(0,C.Y)(k,{style:{height:"default"===F?"20px":"16px"},resizeHandler:L,increaseWidthHandler:O?ee:void 0,decreaseWidthHandler:O?eo:void 0}):null,et=m&&!S;return(0,C.FD)("div",{...z,ref:U,css:_?void 0:[d.PV.cell,d.PV.header],className:l()(_&&d.lH.cell,_&&d.lH.header,{"table-header-isGrid":_},R),role:"columnheader","aria-sort":m&&r||void 0,style:{justifyContent:$,textAlign:$,...D},"aria-label":et?void 0:P,...K.dataComponentProps,children:[et?(0,C.FD)("div",{css:[d.Ay.headerButtonTarget,"",""],role:"button",tabIndex:0,onClick:b,onKeyDown:e=>{if(m&&("Enter"===e.key||" "===e.key))return e.preventDefault(),null==b?void 0:b(e)},"aria-label":et?P:void 0,children:[X?(0,C.Y)("span",{className:"table-header-icon-container",css:[d.Ay.sortHeaderIconOnLeft,"",""],children:j}):null,Q,X?null:(0,C.Y)("span",{className:"table-header-icon-container",css:[d.Ay.sortHeaderIconOnRight,"",""],children:j})]}):Q,er]})})},67559:(e,o,r)=>{r.d(o,{H:()=>u,g:()=>s});var t=r(88608),n=r.n(t),l=r(65848),a=r(66795),i=r(4987),c=r(80838),d=r(98358);let s=(0,l.createContext)({isHeader:!1}),u=(0,l.forwardRef)(function(e,o){let r,{children:t,className:u,style:g,isHeader:p=!1,verticalAlignment:h,...f}=e,{size:m,grid:v}=(0,l.useContext)(a.G),{theme:b}=(0,c.wn)();return r=p&&"default"===m?b.spacing.sm:"default"===m?6:b.spacing.xs,(0,d.Y)(s.Provider,{value:(0,l.useMemo)(()=>({isHeader:p}),[p]),children:(0,d.Y)("div",{...f,ref:o,role:"row",style:{...g,"--table-row-vertical-padding":`${r}px`},css:v?void 0:i.PV.row,className:n()(u,v&&i.lH.row,{"table-isHeader":p,"table-row-isGrid":v}),children:t})})})},32501:(e,o,r)=>{r.d(o,{R:()=>s,f:()=>d});var t=r(65848),n=r(66795),l=r(67559),a=r(80838),i=r(98358);let c={container:{name:"gk361n",styles:"width:32px;padding-top:var(--vertical-padding);padding-bottom:var(--vertical-padding);display:flex;align-items:start;justify-content:center"}},d=(0,t.forwardRef)(function(e,o){let{children:r,style:d,className:s,...u}=e,{size:g}=(0,t.useContext)(n.G),{isHeader:p}=(0,t.useContext)(l.g),{theme:h}=(0,a.wn)();return(0,i.Y)("div",{...u,ref:o,role:p?"columnheader":"cell",style:{...d,"--vertical-padding":"default"===g?`${h.spacing.xs}px`:0},css:c.container,className:s,children:r})}),s=d},4987:(e,o,r)=>{r.d(o,{Ay:()=>c,PV:()=>i,lH:()=>a});var t=r(71218),n=r(25656);function l(){return(0,n.times)(20,()=>(0,n.random)(35).toString(36)).join("")}let a={cell:`js--ds-table-cell-${l()}`,header:`js--ds-table-header-${l()}`,row:`js--ds-table-row-${l()}`},i={cell:{name:"fdi8dv",styles:"display:inline-grid;position:relative;flex:1;box-sizing:border-box;padding-left:var(--table-spacing-sm);padding-right:var(--table-spacing-sm);word-break:break-word;overflow:hidden;& .anticon{vertical-align:text-bottom;}"},header:{name:"ik7qgz",styles:'font-weight:bold;align-items:flex-end;display:flex;overflow:hidden;&[aria-sort]{cursor:pointer;user-select:none;}.table-header-text{color:var(--table-header-text-color);}.table-header-icon-container{color:var(--table-header-sort-icon-color);display:none;}&[aria-sort]:hover{.table-header-icon-container, .table-header-text{color:var(--table-header-focus-color);}}&[aria-sort]:active{.table-header-icon-container, .table-header-text{color:var(--table-header-active-color);}}&:hover, &[aria-sort="ascending"], &[aria-sort="descending"]{.table-header-icon-container{display:inline;}}'},row:{name:"ndcf6g",styles:'display:flex;&.table-isHeader{> *{background-color:var(--table-header-background-color);}.table-isScrollable &{position:sticky;top:0;z-index:1;}}.table-row-select-cell input[type="checkbox"] ~ *{opacity:var(--row-checkbox-opacity, 0);}&:not(.table-row-isGrid)&:hover{&:not(.table-isHeader){background-color:var(--table-row-hover);}.table-row-select-cell input[type="checkbox"] ~ *{opacity:1;}}.table-row-select-cell input[type="checkbox"]:focus ~ *{opacity:1;}> *{padding-top:var(--table-row-vertical-padding);padding-bottom:var(--table-row-vertical-padding);border-bottom:1px solid;border-color:var(--table-separator-color);}&.table-row-isGrid > *{border-right:1px solid;border-color:var(--table-separator-color);}&.table-row-isGrid > :first-of-type{border-left:1px solid;border-color:var(--table-separator-color);}&.table-row-isGrid.table-isHeader:first-of-type > *{border-top:1px solid;border-color:var(--table-separator-color);}'}},c={tableWrapper:(0,t.AH)({"&.table-isScrollable":{overflow:"auto"},display:"flex",flexDirection:"column",height:"100%",[`.${a.cell}`]:i.cell,[`.${a.header}`]:i.header,[`.${a.row}`]:i.row},"",""),table:{name:"oxmfz7",styles:".table-isScrollable &{flex:1;overflow:auto;}"},headerButtonTarget:{name:"sezlox",styles:"align-items:flex-end;display:flex;overflow:hidden;width:100%;justify-content:inherit;&:focus{.table-header-text{color:var(--table-header-focus-color);}.table-header-icon-container{color:var(--table-header-focus-color);display:inline;}}&:active{.table-header-icon-container, .table-header-text{color:var(--table-header-active-color);}}"},sortHeaderIconOnRight:{name:"1hdiaor",styles:"margin-left:var(--table-spacing-xs)"},sortHeaderIconOnLeft:{name:"d4plmt",styles:"margin-right:var(--table-spacing-xs)"},checkboxCell:{name:"4cdr0s",styles:"display:flex;align-items:center;flex:0;padding-left:var(--table-spacing-sm);padding-top:0;padding-bottom:0;min-width:var(--table-spacing-md);max-width:var(--table-spacing-md);box-sizing:content-box"},resizeHandleContainer:(0,t.AH)({position:"absolute",right:-3,top:"var(--table-spacing-sm)",bottom:"var(--table-spacing-sm)",width:"var(--table-spacing-sm)",display:"flex",justifyContent:"center",cursor:"col-resize",userSelect:"none",touchAction:"none",zIndex:1},"",""),resizeHandle:{name:"55zery",styles:"width:1px;background:var(--table-resize-handle-color)"},paginationContainer:{name:"ehlmid",styles:"display:flex;justify-content:flex-end;padding-top:var(--table-spacing-sm);padding-bottom:var(--table-spacing-sm)"}}},99347:(e,o,r)=>{r.r(o),r.d(o,{Content:()=>D,List:()=>C,Root:()=>x,Trigger:()=>k});var t=r(81434),n=r(41270),l=r(78545),a=r(25656),i=r(65848),c=r.n(i),d=r(8161),s=r(80842),u=r(27443),g=r(6672),p=r(26033),h=r(80838),f=r(69391),m=r(94363),v=r(58829),b=r(98358);let w=c().createContext({activeValue:void 0,dataComponentProps:{"data-component-id":"design_system.tabs.default_component_id","data-component-type":d.v_.Tabs}}),y=c().createContext({viewportRef:{current:null}}),x=c().forwardRef((e,o)=>{let{value:r,defaultValue:n,onValueChange:a,componentId:u,analyticsEvents:g,valueHasNoPii:p,...h}=e,f=(0,v.W)("databricks.fe.observability.defaultComponentView.tabs",!1),m=void 0!==r,[y,x]=c().useState(n),C=(0,i.useMemo)(()=>null!=g?g:f?[d.s7.OnValueChange,d.s7.OnView]:[d.s7.OnValueChange],[g,f]),k=(0,d.ei)({componentType:d.v_.Tabs,componentId:u,analyticsEvents:C,valueHasNoPii:p,shouldStartInteraction:!0}),{elementRef:D}=(0,s.z)({onView:k.onView,value:null!=r?r:n}),R=(0,t.SV)([o,D]);return(0,b.Y)(w.Provider,{value:{activeValue:m?r:y,dataComponentProps:k.dataComponentProps},children:(0,b.Y)(l.bL,{value:r,defaultValue:n,onValueChange:e=>{k.onValueChange(e),a&&a(e),m||x(e)},...h,ref:R})})}),C=c().forwardRef((e,o)=>{let{addButtonProps:r,scrollAreaViewportCss:t,tabListCss:a,children:i,dangerouslyAppendEmotionCSS:d,shadowScrollStylesBackgroundColor:s,scrollbarHeight:p,getScrollAreaViewportRef:h,...f}=e,m=c().useRef(null),{dataComponentProps:v}=c().useContext(w),x=R(s,p);return c().useEffect(()=>{h&&h(m.current)},[h]),(0,b.Y)(y.Provider,{value:{viewportRef:m},children:(0,b.FD)("div",{css:[x.container,d,"",""],children:[(0,b.FD)(n.bL,{type:"hover",css:[x.root,"",""],children:[(0,b.Y)(n.LM,{css:[x.viewport,t,"",""],ref:m,children:(0,b.Y)(l.B8,{css:[x.list,a,"",""],...f,ref:o,...v,children:i})}),(0,b.Y)(n.Ze,{orientation:"horizontal",css:x.scrollbar,children:(0,b.Y)(n.zi,{css:x.thumb})})]}),r&&(0,b.Y)("div",{css:[x.addButtonContainer,r.dangerouslyAppendEmotionCSS,"",""],children:(0,b.Y)(u.$n,{icon:(0,b.Y)(g.A,{}),size:"small","aria-label":"Add tab",css:x.addButton,onClick:r.onClick,componentId:`${v["data-component-id"]}.add_tab`,className:r.className})})]})})}),k=c().forwardRef((e,o)=>{let{onClose:r,value:n,disabled:i,children:s,...u}=e,g=c().useRef(null),h=(0,t.SV)([o,g]),{activeValue:f,dataComponentProps:m}=c().useContext(w),v=m["data-component-id"],{viewportRef:x}=c().useContext(y),C=void 0!==r&&!i,k=S(C),D=(0,d.ei)({componentType:d.v_.Button,componentId:`${v}.close_tab`,analyticsEvents:[d.s7.OnClick]}),R=c().useCallback(()=>{if(g.current&&x.current&&f===n){let e=x.current.getBoundingClientRect(),o=g.current.getBoundingClientRect();o.left<e.left?x.current.scrollLeft-=e.left-o.left:o.right>e.right&&(x.current.scrollLeft+=o.right-e.right)}},[x,f,n]),$=c().useMemo(()=>(0,a.debounce)(R,10),[R]);return c().useEffect(()=>{R()},[R]),c().useEffect(()=>{if(!x.current||!g.current)return;let e=new ResizeObserver($);return e.observe(x.current),e.observe(g.current),()=>{e.disconnect(),$.cancel()}},[$,x]),(0,b.FD)(l.l9,{css:k.trigger,value:n,disabled:i,onKeyDown:e=>{C&&"Delete"===e.key&&(D.onClick(e),e.stopPropagation(),e.preventDefault(),r(n))},onMouseDown:e=>{C&&1===e.button&&(D.onClick(e),e.stopPropagation(),e.preventDefault(),r(n))},...u,ref:h,children:[s,C&&(0,b.Y)(p.A,{onMouseDown:e=>{i||0!==e.button||!1!==e.ctrlKey||(D.onClick(e),e.stopPropagation(),e.preventDefault(),r(n))},css:k.closeSmallIcon,"aria-hidden":"false","aria-label":"Press delete to close the tab"})]})}),D=c().forwardRef((e,o)=>{let{...r}=e,{theme:t}=(0,h.wn)(),n=$(t);return(0,b.Y)(l.UC,{css:n,...r,ref:o})}),R=(e,o)=>{let{theme:r}=(0,h.wn)();return{container:(0,m.c)(r),root:{overflow:"hidden"},viewport:{...(0,f.Ud)(r,{orientation:"horizontal",backgroundColor:e})},list:{display:"flex",alignItems:"center"},scrollbar:{display:"flex",flexDirection:"column",userSelect:"none",touchAction:"none",height:null!=o?o:3},thumb:{flex:1,background:r.isDarkMode?"rgba(255, 255, 255, 0.2)":"rgba(17, 23, 28, 0.2)","&:hover":{background:r.isDarkMode?"rgba(255, 255, 255, 0.3)":"rgba(17, 23, 28, 0.3)"},borderRadius:r.borders.borderRadiusSm,position:"relative"},addButtonContainer:{flex:1},addButton:{margin:"2px 0 6px 0"}}},S=e=>{let{theme:o}=(0,h.wn)();return{trigger:{...(0,m.s)(o),alignItems:"center",justifyContent:e?"space-between":"center",minWidth:e?o.spacing.lg+o.spacing.md:o.spacing.lg,color:o.colors.textSecondary,lineHeight:o.typography.lineHeightBase,whiteSpace:"nowrap",border:"none",padding:`${o.spacing.xs}px 0 ${o.spacing.sm}px 0`,"& > .anticon:last-of-type":{visibility:"hidden"},"&:hover":{cursor:"pointer",color:o.colors.actionDefaultTextHover,"& > .anticon:last-of-type":{visibility:"visible"}},"&:active":{color:o.colors.actionDefaultTextPress},outlineStyle:"none",outlineColor:o.colors.actionDefaultBorderFocus,"&:focus-visible":{outlineStyle:"auto"},'&[data-state="active"]':{color:o.colors.textPrimary,boxShadow:`inset 0 -4px 0 ${o.colors.actionPrimaryBackgroundDefault}`,"& > .anticon:last-of-type":{visibility:"visible"}},"&[data-disabled]":{color:o.colors.actionDisabledText,"&:hover":{cursor:"not-allowed"}}},closeSmallIcon:{marginLeft:o.spacing.xs,color:o.colors.textSecondary,"&:hover":{color:o.colors.actionDefaultTextHover},"&:active":{color:o.colors.actionDefaultTextPress}}}},$=e=>({color:e.colors.textPrimary,'&[data-state="inactive"]':{display:"none"}})},36173:(e,o,r)=>{r.d(o,{v:()=>h});var t=r(71218),n=r(81434),l=r(65848),a=r(8161),i=r(80838),c=r(4700),d=r(80842),s=r(94167),u=r(58829),g=r(98358);let p={default:"tagDefault",brown:"tagBrown",coral:"tagCoral",charcoal:"grey600",indigo:"tagIndigo",lemon:"tagLemon",lime:"tagLime",pink:"tagPink",purple:"tagPurple",teal:"tagTeal",turquoise:"tagTurquoise"},h=(0,l.forwardRef)((e,o)=>{let{theme:r}=(0,i.wn)(),{color:h,children:f,closable:m,onClose:v,role:b="status",closeButtonProps:w,analyticsEvents:y,componentId:x,icon:C,onClick:k,...D}=e,R=(0,u.W)("databricks.fe.observability.defaultComponentView.tag",!1),S=!!e.onClick,$=(0,l.useMemo)(()=>null!=y?y:R?[a.s7.OnClick,a.s7.OnView]:[a.s7.OnClick],[y,R]),Y=(0,a.ei)({componentType:a.v_.Tag,componentId:x,analyticsEvents:$}),{elementRef:B}=(0,d.z)({onView:Y.onView}),H=(0,n.SV)([B,o]),A=x?`${x}.close`:void 0,T=(0,a.ei)({componentType:a.v_.Button,componentId:A,analyticsEvents:[a.s7.OnClick]}),I=(0,l.useCallback)(e=>{k&&(Y.onClick(e),k(e))},[Y,k]),P=(0,l.useCallback)(e=>{T.onClick(e),e.stopPropagation(),v&&v()},[T,v]),z=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],t=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=e.colors.tagText,l=e.colors[p[o]],a="",i=e.colors.actionDefaultBorderFocus,c=o.charAt(0).toUpperCase()+o.slice(1);n=e.DU_BOIS_INTERNAL_ONLY.colors[`tagText${c}`],l=e.DU_BOIS_INTERNAL_ONLY.colors[`tagBackground${c}`],a=e.DU_BOIS_INTERNAL_ONLY.colors[`tagIcon${c}`],"charcoal"===o&&(i=e.colors.white);let d=e.colors.tagIconHover,s=e.colors.tagIconPress;return{wrapper:{backgroundColor:l,display:"inline-flex",alignItems:"center",marginRight:e.spacing.sm,borderRadius:e.borders.borderRadiusSm},tag:{border:"none",color:n,padding:"",backgroundColor:"transparent",borderRadius:e.borders.borderRadiusSm,marginRight:e.spacing.sm,display:"inline-block",cursor:r?"pointer":"default",...t&&{borderTopRightRadius:0,borderBottomRightRadius:0},...r&&{"&:hover":{"& > div":{backgroundColor:e.colors.actionDefaultBackgroundHover}},"&:active":{"& > div":{backgroundColor:e.colors.actionDefaultBackgroundPress}}}},content:{display:"flex",alignItems:"center",minWidth:0,height:e.typography.lineHeightBase},close:{height:e.typography.lineHeightBase,width:e.typography.lineHeightBase,lineHeight:`${e.general.iconFontSize}px`,padding:0,color:n,fontSize:e.general.iconFontSize,borderTopRightRadius:e.borders.borderRadiusSm,borderBottomRightRadius:e.borders.borderRadiusSm,border:"none",background:"none",cursor:"pointer",marginLeft:e.spacing.xs,display:"flex",alignItems:"center",justifyContent:"center",margin:0,"&:hover":{backgroundColor:e.colors.actionDefaultBackgroundHover,color:d},"&:active":{backgroundColor:e.colors.actionDefaultBackgroundPress,color:s},"&:focus-visible":{outlineStyle:"solid",outlineWidth:1,outlineOffset:-2,outlineColor:i},".anticon":{verticalAlign:0,fontSize:12}},text:{padding:0,fontSize:e.typography.fontSizeBase,fontWeight:e.typography.typographyRegularFontWeight,lineHeight:e.typography.lineHeightSm,"& .anticon":{verticalAlign:"text-top"},whiteSpace:"nowrap"},icon:{color:a,paddingLeft:e.spacing.xs,height:e.typography.lineHeightBase,display:"inline-flex",alignItems:"center",borderTopLeftRadius:e.borders.borderRadiusSm,borderBottomLeftRadius:e.borders.borderRadiusSm,"& > span":{fontSize:12},"& + div":{borderTopLeftRadius:0,borderBottomLeftRadius:0,...t&&{borderTopRightRadius:0,borderBottomRightRadius:0}}},childrenWrapper:{paddingLeft:e.spacing.xs,paddingRight:e.spacing.xs,height:e.typography.lineHeightBase,display:"inline-flex",alignItems:"center",borderRadius:e.borders.borderRadiusSm,minWidth:0}}}(r,h,S,m);return(0,g.FD)("div",{ref:H,role:b,onClick:I,css:[z.wrapper,"",""],...D,...(0,s.VG)(),...Y.dataComponentProps,tabIndex:S?0:-1,children:[(0,g.FD)("div",{css:[z.tag,z.content,z.text,"margin-right:0;",""],...Y.dataComponentProps,children:[C&&(0,g.Y)("div",{css:[z.icon,"",""],children:C}),(0,g.Y)("div",{css:[z.childrenWrapper,"",""],children:f})]}),m&&(0,g.Y)("button",{css:z.close,tabIndex:0,onClick:P,onMouseDown:e=>{e.stopPropagation()},...w,...T.dataComponentProps,children:(0,g.Y)(c.A,{css:(0,t.AH)({fontSize:r.general.iconFontSize-4},"","")})})]})})},46249:(e,o,r)=>{r.d(o,{I:()=>c});var t=r(71218),n=r(39234),l=r(80838),a=r(55444),i=r(98358);let c=e=>{let{content:o,iconTitle:r="More information",...c}=e,{theme:d}=(0,l.wn)();return(0,i.Y)(n.m,{content:o,...c,children:(0,i.Y)(a.A,{tabIndex:0,"aria-hidden":"false","aria-label":r,alt:r,css:(0,t.AH)({color:d.colors.textSecondary},"","")})})}},39234:(e,o,r)=>{r.d(o,{m:()=>p});var t=r(71218),n=r(80692),l=r(65848),a=r(8161),i=r(80838),c=r(63908),d=r(8467);let s=()=>"undefined"!=typeof jest;var u=r(98358);let g=e=>{let{maxWidth:o}=e,{theme:r,classNamePrefix:n}=(0,i.wn)(),{useNewShadows:l}=(0,d.p)(),a=`.${n}-typography`,{isDarkMode:c}=r,s=(0,t.i7)({from:{opacity:0,transform:"translateY(2px)"},to:{opacity:1,transform:"translateY(0)"}}),u=(0,t.i7)({from:{opacity:0,transform:"translateX(-2px)"},to:{opacity:1,transform:"translateX(0)"}}),g=(0,t.i7)({from:{opacity:0,transform:"translateY(-2px)"},to:{opacity:1,transform:"translateY(0)"}}),p=(0,t.i7)({from:{opacity:0,transform:"translateX(2px)"},to:{opacity:1,transform:"translateX(0)"}}),h=c?r.colors.blue600:r.colors.blue500,f=c?r.colors.blue800:r.colors.blue300,m=c?r.colors.blue700:r.colors.blue400;return{content:{backgroundColor:r.colors.tooltipBackgroundTooltip,color:r.colors.actionPrimaryTextDefault,borderRadius:r.borders.borderRadiusSm,fontSize:r.typography.fontSizeMd,padding:`${r.spacing.xs}px ${r.spacing.sm}px`,lineHeight:r.typography.lineHeightLg,fontWeight:r.typography.typographyRegularFontWeight,boxShadow:l?r.shadows.lg:r.general.shadowHigh,maxWidth:o,wordWrap:"break-word",whiteSpace:"normal",zIndex:r.options.zIndexBase+70,willChange:"transform, opacity","&[data-state='delayed-open'][data-side='top']":{animation:`${g} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},"&[data-state='delayed-open'][data-side='right']":{animation:`${p} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},"&[data-state='delayed-open'][data-side='bottom']":{animation:`${s} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},"&[data-state='delayed-open'][data-side='left']":{animation:`${u} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},[`& a${a}`]:{"&, :focus":{color:h,".anticon":{color:h}},":active":{color:f,".anticon":{color:f}},":hover":{color:m,".anticon":{color:m}}}},arrow:{fill:r.colors.tooltipBackgroundTooltip,zIndex:r.options.zIndexBase+70,visibility:"visible"}}},p=e=>{let{children:o,content:r,defaultOpen:t=!1,delayDuration:d=350,side:p="top",sideOffset:h=4,align:f="center",maxWidth:m=250,componentId:v,analyticsEvents:b=[a.s7.OnView],zIndex:w,...y}=e,{theme:x}=(0,i.wn)(),{getPopupContainer:C}=(0,c.G)(),k=g({maxWidth:m}),D=(0,l.useMemo)(()=>b,[b]),R=(0,a.ei)({componentType:a.v_.Tooltip,componentId:v,analyticsEvents:D}),S=(0,l.useRef)(!0),$=(0,l.useCallback)(e=>{e&&S.current&&(R.onView(),S.current=!1)},[R,S]),Y=s();return(0,u.FD)(n.bL,{defaultOpen:t,delayDuration:Y?10:d,onOpenChange:$,children:[(0,u.Y)(n.l9,{asChild:!0,children:o}),r?(0,u.Y)(n.ZL,{container:C&&C(),children:(0,u.FD)(n.UC,{side:p,align:f,sideOffset:x.spacing.sm,arrowPadding:x.spacing.md,css:[k.content,w?{zIndex:w}:void 0,"",""],...y,...R.dataComponentProps,children:[r,(0,u.Y)(n.i3,{css:k.arrow})]})}):null]})}},60054:(e,o,r)=>{r.d(o,{m:()=>d});var t=r(65848),n=r(28219),l=r(55444),a=r(80333),i=r(98358);let c=e=>({display:"inline-flex",paddingLeft:e.spacing.xs,color:e.colors.textSecondary,pointerEvents:"all"}),d=(e,o,r,n)=>{let d=t.Children.count(e),u=(0,i.Y)(a.p,{title:r,placement:"right",dangerouslySetAntdProps:{getPopupContainer:()=>n.current||document.body},children:(0,i.Y)("span",{"data-disabled-tooltip":!0,css:e=>c(e),onClick:e=>{o.disabled&&e.stopPropagation()},children:(0,i.Y)(l.A,{role:"presentation",alt:"Disabled state reason","aria-hidden":"false"})})});if(1===d)return s(e,!!o.disabled,r,u,0,d);return t.Children.map(e,(e,t)=>s(e,!!o.disabled,r,u,t,d))},s=(e,o,r,t,l,a)=>{let c=(0,i.Y)(n.HintColumn,{}).type,d=!!(e&&"string"!=typeof e&&"number"!=typeof e&&"boolean"!=typeof e&&"type"in e&&(null==e?void 0:e.type)===c);if(o&&r&&e&&d)return(0,i.FD)(i.FK,{children:[t,e]});if(l===a-1&&o&&r)return(0,i.FD)(i.FK,{children:[e,t]});return e}},94363:(e,o,r)=>{r.d(o,{c:()=>t,s:()=>n});let t=e=>({display:"flex",borderBottom:`1px solid ${e.colors.border}`,marginBottom:e.spacing.md,height:e.general.heightSm,boxSizing:"border-box"}),n=e=>({display:"flex",fontWeight:e.typography.typographyBoldFontWeight,fontSize:e.typography.fontSizeMd,backgroundColor:"transparent",marginRight:e.spacing.md})},8467:(e,o,r)=>{r.d(o,{p:()=>n});var t=r(58829);let n=()=>({useNewShadows:(0,t.W)("databricks.fe.designsystem.useNewShadows",!1),useNewFormUISpacing:(0,t.W)("databricks.fe.designsystem.useNewFormUISpacing",!1),useNewBorderRadii:(0,t.W)("databricks.fe.designsystem.useNewBorderRadii",!1),useNewLargeAlertSizing:(0,t.W)("databricks.fe.designsystem.useNewLargeAlertSizing",!1)})},29121:(e,o,r)=>{r.d(o,{s:()=>n});var t=r(65848);let n=e=>{let{callback:o,allowBasicEnter:r,allowPlatformEnter:n}=e,l=(0,t.useMemo)(()=>navigator.userAgent.includes("Mac"),[]),a=(0,t.useRef)(!1),i=(0,t.useCallback)(()=>{a.current=!0},[]),c=(0,t.useCallback)(()=>{a.current=!1},[]);return{onKeyDown:(0,t.useCallback)(e=>{if("Enter"!==e.key||a.current||!r&&!n)return;let t=r&&!e.metaKey&&!e.ctrlKey&&!e.shiftKey&&!e.altKey,i=n&&(l?e.metaKey:e.ctrlKey);(t||i)&&o(e)},[r,n,o,l]),onCompositionEnd:c,onCompositionStart:i}}},71009:(e,o,r)=>{r.d(o,{C:()=>i,Y:()=>c});var t=r(65848),n=r(80842),l=r(98358);let a=(0,t.createContext)({isViewed:!1,emitOnFirstView:()=>{}}),i=e=>{let{children:o}=e,r=(0,t.useRef)(!1);return(0,l.Y)(a.Provider,{value:{isViewed:r.current,emitOnFirstView:()=>{r.current=!0}},children:o})},c=e=>{let{onView:o,value:r}=e,{isViewed:l,emitOnFirstView:i}=(0,t.useContext)(a),c=(0,t.useCallback)(()=>{l||(o(r),i())},[o,l,r,i]);return(0,n.z)({onView:c})}},46792:(e,o,r)=>{r.d(o,{xW:()=>a});var t=r(65848),n=r.n(t);let l=n().createContext({componentId:void 0,isSubmitting:!1,formRef:void 0}),a=()=>n().useContext(l)}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/35.1623fe90.chunk.js.map