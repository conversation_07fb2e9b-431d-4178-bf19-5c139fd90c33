{"version": 3, "file": "static/css/7202.41c9bc32.chunk.css", "mappings": "AAAA,oBACE,mBACF,CAEA,6CACE,iBACF,CAEA,gCACE,aACF,CAEA,qBAKE,WAAY,CAJZ,eAAgB,CAChB,eAAgB,CAChB,sBAAuB,CACvB,kBAEF,CAEA,+BAGE,kBAAmB,CAFnB,YAAa,CACb,eAEF,CAEA,wFACE,gCAAqC,CAErC,iBAAkB,CADlB,UAEF,CAEA,2DACE,gCACF", "sources": ["model-registry/components/ModelListView.css"], "sourcesContent": [".pagination-section {\n  padding-bottom: 30px;\n}\n\n.pagination-dropdown .ant-dropdown-menu-item {\n  text-align: center;\n}\n\n.ant-alert-info .ant-alert-icon {\n  color: #00b379;\n}\n\n.table-tag-container {\n  max-width: 100px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  height: 100%;\n}\n\n.ModelListView-filter-dropdown {\n  margin-top: 0;\n  min-width: 800px;\n  margin-bottom: 24px;\n}\n\n.search-input-tooltip .du-bois-light-popover-inner .du-bois-light-popover-inner-content {\n  background-color: rgba(0, 0, 0, 0.75);\n  color: white;\n  border-radius: 4px;\n}\n\n.search-input-tooltip .du-bois-light-popover-arrow-content {\n  background-color: rgba(0, 0, 0, 0.75);\n}\n"], "names": [], "sourceRoot": ""}