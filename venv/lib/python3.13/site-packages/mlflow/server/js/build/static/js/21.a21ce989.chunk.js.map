{"version": 3, "file": "static/js/21.a21ce989.chunk.js", "mappings": "ykBA6BA,MAAM,QAAEA,GAAYC,EAAAA,IAEpB,SAASC,EAAaC,GACpB,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KAClB,OACEC,EAAAA,EAAAA,GAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHC,UAAW,SACXC,MAAON,EAAMO,OAAOC,eACrB,OACGT,GAGV,CAEA,SAASU,EAAaV,GACpB,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KAClB,OACEC,EAAAA,EAAAA,GAAA,SACEQ,UAAU,sBACVP,KAAGC,EAAAA,EAAAA,IAAE,CACH,uBAAwB,CACtBO,gBAAiBX,EAAMO,OAAOK,MAC9BC,QAAS,GAEX,oBAAqB,CACnBF,gBAAiBX,EAAMO,OAAOO,8BAEjC,OACGf,GAGV,CAEA,SAASgB,EAAehB,GACtB,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KAClB,OACEC,EAAAA,EAAAA,GAAA,UACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHC,UAAW,OACXW,QAAS,OACTC,WAAY,SACZC,OAAQ,OACRP,gBAAiBX,EAAMO,OAAOK,MAC9BO,YAAa,EACbC,OAAQ,WACT,OACGrB,GAGV,CAyBO,MAAMsB,UAAqCC,EAAAA,UAGhDC,WAAAA,GAAA,SAAAC,WAAA,KACAC,MAAQ,CACNC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,2BAA2B,EAC3BC,cAAc,EACdC,cAAc,EACdC,cAAc,GACd,KAEFC,MAAQ,CACNC,UAAUlC,EAAAA,EAAAA,GAAA,KAAGQ,UAAU,yBACvB2B,WAAWnC,EAAAA,EAAAA,GAAA,KAAGQ,UAAU,0BACxB4B,UAAUpC,EAAAA,EAAAA,GAAA,KAAGQ,UAAU,sBACvB6B,WAAWrC,EAAAA,EAAAA,GAAA,KAAGQ,UAAU,uBACxB8B,WAAWtC,EAAAA,EAAAA,GAAA,KAAGQ,UAAU,yCACxB,KAEF+B,cAAiBC,IACfC,KAAKC,UAAUnB,IAAU,CACvB,CAACiB,IAAUjB,EAAMiB,MAChB,CACH,CAEFG,MAAAA,GACE,MAAM,kBACJC,EAAiB,iBACjBC,EAAgB,UAChBC,EAAS,mBACTC,EAAkB,kBAClBC,EAAiB,SACjBC,EAAQ,SACRC,EAAQ,gBACRC,EAAe,WACfC,EAAU,YACVC,GACEZ,KAAK5C,MACHyD,GACJtD,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,mCAEfC,OAAQ,CAAEC,YAAalB,KAAK5C,MAAMoD,SAASW,UAGzCC,EAAc,EAClB7D,EAAAA,EAAAA,GAAC8D,EAAAA,GAAI,CAACC,GAAIC,EAAAA,GAAoBC,mBAAmBC,UAC/ClE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yBAInBzD,EAAAA,EAAAA,GAAC8D,EAAAA,GAAI,CAACC,GAAIC,EAAAA,GAAoBG,kBAAkBrB,GAAWoB,SAAEpB,KAG/D,OACEsB,EAAAA,EAAAA,IAAA,OACE5D,UAAU,2BAEVP,KAAGC,EAAAA,EAAAA,IAAE,IACAmE,EAAOC,4BACPD,EAAOE,QAAQtB,EAASW,SAC5B,IAACM,SAAA,EAEFlE,EAAAA,EAAAA,GAACwE,EAAAA,EAAU,CAAClB,MAAOA,EAAOO,YAAaA,KACvC7D,EAAAA,EAAAA,GAAA,OAAKQ,UAAU,6BAA4B0D,UACzCE,EAAAA,EAAAA,IAAC7D,EAAY,CAAA2D,SAAA,CACVzB,KAAKgC,oBACLhC,KAAKiC,yBACLjC,KAAKkC,oBACJ,eACA,gBACA3E,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAIlBhB,KAAKmC,eACLnC,KAAKkC,oBACJ,eACA,gBACA3E,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,YAGjB,GAEAzD,EAAAA,EAAAA,GAAC6E,EAAAA,IAAM,CACLrE,UAAU,gBAEVsE,MAAO,CAAEC,WAAY,QACrBC,SAAUA,IAAMvC,KAAKF,cAAc,gCAErCvC,EAAAA,EAAAA,GAAA,OAAKQ,UAAU,uCAAsC0D,UACnDlE,EAAAA,EAAAA,GAAA,QAAAkE,UACElE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gCAOtBhB,KAAKwC,0BACJ,eACAjF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,YAIlBhB,KAAKyC,aACJ,eACAlF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAGjBb,EACAC,GAEDJ,KAAKwC,0BACJ,gBACAjF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,aAIlBhB,KAAKyC,aACJ,gBACAlF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,YAGjBV,EACAC,GAEDP,KAAKkC,oBACJ,eACA,gBACA3E,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,aAIlBhB,KAAK0C,sBAGVf,EAAAA,EAAAA,IAACzE,EAAAA,IAAU,CAAAuE,SAAA,EACTlE,EAAAA,EAAAA,GAACN,EAAO,CACN0F,KACEpF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,8BAGlBS,UAGDlE,EAAAA,EAAAA,GAACqF,EAAAA,GAA4B,CAACnC,SAAUA,KAFpC,8BAINlD,EAAAA,EAAAA,GAACN,EAAO,CACN0F,KACEpF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAGlBS,UAGDlE,EAAAA,EAAAA,GAACsF,EAAAA,EAAiB,CAACpC,SAAUA,EAAUC,gBAAiBA,KAFpD,iBAINnD,EAAAA,EAAAA,GAACN,EAAO,CACN0F,KACEpF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,aAGlBS,UAGDlE,EAAAA,EAAAA,GAACuF,EAAAA,EAAa,CAACrC,SAAUA,EAAUD,SAAUA,EAAUG,WAAYA,EAAYC,YAAaA,KAFxF,aAINrD,EAAAA,EAAAA,GAACN,EAAO,CACN0F,KACEpF,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAGlBS,UAGDlE,EAAAA,EAAAA,GAACwF,EAAAA,EAAiB,CAACtC,SAAUA,EAAUC,gBAAiBA,KAFpD,qBAOd,CAEAsB,iBAAAA,GACE,MAAM,SAAExB,EAAQ,cAAEwC,GAAkBhD,KAAK5C,MACzC,OACEG,EAAAA,EAAAA,GAAA,SAAAkE,UACEE,EAAAA,EAAAA,IAAA,MAAI5D,UAAU,YAAW0D,SAAA,EACvBlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,2BAA0B0D,UAClDlE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAIlBR,EAAS0C,KAAI,CAACC,EAAGC,KAAG,IAAAC,EAAAC,EAAA,OACnB/F,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,SAASlF,UAAU,2BAA0B0D,SAEpDuB,EAAcI,IACb7F,EAAAA,EAAAA,GAAC8D,EAAAA,GAAI,CAACC,GAAIiC,EAAAA,EAAOC,gBAA8B,QAAfH,EAACF,EAAEM,oBAAY,IAAAJ,EAAAA,EAAI,IAAc,QAAXC,EAAEH,EAAEO,eAAO,IAAAJ,EAAAA,EAAI,IAAI7B,SAAE0B,EAAEO,UAE7EP,EAAEO,SALuDP,EAAEO,QAO1D,QAKf,CAEAzB,sBAAAA,GACE,MAAM,SAAEzB,EAAQ,cAAEwC,EAAa,eAAEW,EAAc,SAAEC,EAAQ,UAAEvD,GAAcL,KAAK5C,MAC9E,OACEuE,EAAAA,EAAAA,IAAA,SAAO5D,UAAU,mBAAkB0D,SAAA,EACjCE,EAAAA,EAAAA,IAAA,MAAI5D,UAAU,YAAW0D,SAAA,EACvBlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,2BAA0B0D,UAClDlE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qBAKlB6C,OAAOC,KAAKH,GAAgBT,KAAKa,IAChC,MAAMC,EAAML,EAAeI,GAC3B,OACExG,EAAAA,EAAAA,GAAA,MAAIQ,UAAU,0BAAyB0D,UACrClE,EAAAA,EAAAA,GAAC8D,EAAAA,GAAI,CAACC,GAAIC,EAAAA,GAAoB0C,yBAAyB5D,EAAW0D,GAActC,SAAEsC,KADvCC,EAExC,QAIXrC,EAAAA,EAAAA,IAAA,MAAI5D,UAAU,YAAW0D,SAAA,EACvBlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,2BAA0B0D,UAClDlE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAKlB4C,EAASV,KAAI,CAACgB,EAASC,KAEpB5G,EAAAA,EAAAA,GAAA,MAAIQ,UAAU,0BAAyB0D,UACrClE,EAAAA,EAAAA,GAAA,OAAKQ,UAAU,yCAAwC0D,SAAEyC,KADd1D,EAAS2D,GAAGT,eAM/D/B,EAAAA,EAAAA,IAAA,MAAI5D,UAAU,YAAW0D,SAAA,EACvBlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,2BAA0B0D,UAClDlE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAKlBR,EAAS0C,KAAI,CAACc,EAAKZ,KAElB,MAAMgB,EACJJ,EAAII,WAAapB,EAAcI,GAAOiB,EAAAA,EAAMC,gBAAgBN,EAAII,UAAWpE,KAAK5C,MAAMmH,MAAQ,YAChG,OACEhH,EAAAA,EAAAA,GAAA,MAAIQ,UAAU,0BAAyB0D,SACpC2C,GAD0CJ,EAAIN,QAE5C,SAMjB,CAKAxB,mBAAAA,CACEsC,EACAC,EACAC,GAIC,IAHDC,IAAU9F,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,KAAAA,UAAA,GACVgG,EAAgBhG,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,GAAAA,UAAA,GAAG,KACnBiG,EAAoBjG,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,GAAAA,UAAA,GAAG,KAEvB,MAAM,SAAE2B,GAAaR,KAAK5C,MAEpB2H,EAAW/E,KAAKlB,MAAM0F,IACtB,SAAE7E,EAAQ,UAAEC,GAAcI,KAAKR,MACrC,OACEjC,EAAAA,EAAAA,GAAA,SAAAkE,UACElE,EAAAA,EAAAA,GAAA,MAAAkE,UACElE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,WAAWlF,UAAU,kCAAkCiH,QAASxE,EAASW,OAAS,EAAEM,UAC5FE,EAAAA,EAAAA,IAAA,OAAK5D,UAAU,0BAAyB0D,SAAA,EACtCE,EAAAA,EAAAA,IAACvD,EAAc,CAAC6G,QAASA,IAAMjF,KAAKF,cAAc0E,GAAe/C,SAAA,CAC9DsD,EAAWpF,EAAWC,GACvBrC,EAAAA,EAAAA,GAAA,QAAMQ,UAAU,SAAQ0D,SAAEiD,OAE3BG,EACAC,GACDvH,EAAAA,EAAAA,GAAC6E,EAAAA,IAAM,CACL8C,gBAAc,EACdnH,UAAU,gBAEVsE,MAAOsC,EAAa,CAAErC,WAAY,QAAW,CAAC,EAC9CC,SAAUA,IAAMvC,KAAKF,cAAc2E,MAErClH,EAAAA,EAAAA,GAAA,OAAKQ,UAAU,oBAAmB0D,UAChClE,EAAAA,EAAAA,GAAA,QAAAkE,UACElE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,+BAWjC,CAEAmB,YAAAA,GACE,OACE5E,EAAAA,EAAAA,GAAA,SAAOQ,UAAU,mBAAkB0D,SAChCzB,KAAKmF,eACJnF,KAAK5C,MAAMuD,YACXpD,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,eAGjBhB,KAAKlB,MAAMI,aACXc,KAAKlB,MAAMG,eAInB,CAEAuD,yBAAAA,CAA0BgC,EAAoBE,GAC5C,MAAM,SAAElE,GAAaR,KAAK5C,OACpB,aAAEiC,GAAiBW,KAAKlB,MAExBiG,EAAW/E,KAAKlB,MAAM0F,IACtB,UAAE9E,EAAS,SAAED,GAAaO,KAAKR,MACrC,OACEjC,EAAAA,EAAAA,GAAA,SAAAkE,UACElE,EAAAA,EAAAA,GAAA,MAAIQ,UAAW,IAAGsB,EAAe,GAAK,cAAeoC,UACnDlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,WAAWlF,UAAU,oCAAoCiH,QAASxE,EAASW,OAAS,EAAEM,UAC9FE,EAAAA,EAAAA,IAAA,UAAQ5D,UAAU,yBAAyBkH,QAASA,IAAMjF,KAAKF,cAAc0E,GAAe/C,SAAA,CACzFsD,EAAWrF,EAAYD,GACxBlC,EAAAA,EAAAA,GAAA,UAAQ8E,MAAO,CAAE7D,YAAa,GAAIiD,SAAEiD,YAMhD,CAEAjC,YAAAA,CAAa+B,EAAoBE,EAAkBU,EAAkBC,GACnE,MAAM,aAAEhG,EAAY,0BAAED,EAAyB,aAAED,GAAiBa,KAAKlB,MAEjEiG,EAAW/E,KAAKlB,MAAM0F,GACtBc,EAAoBjG,GAAgB0F,EACpCQ,GAAmBnG,IAA8BoG,IAAAA,QAAUJ,GAC3DK,EAAiBrG,IAA8BoG,IAAAA,QAAUH,GAOzDK,EAAmBC,GAAeA,EAClCC,GACJrI,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uBAEfC,OAAQ,CAAEyD,YAAaA,KAG3B,OACE/C,EAAAA,EAAAA,IAAA,SAAO5D,UAAU,2CAA0C0D,SAAA,CACxDzB,KAAKmF,eACJC,EACAQ,EACAN,GAAqBC,EACrBpG,GApBuB0G,CAACC,EAAUC,KACtCpE,EAAAA,EAAAA,IAAAqE,EAAAA,GAAA,CAAAvE,SAAA,CACGiD,EAAY,KAAGoB,EAAI,QAoBlBJ,GAED1F,KAAKmF,eACJE,EACAO,EACAN,GAAqBG,EACrBtG,GAvBsB8G,CAACH,EAAUC,IAAcD,GAyB/CJ,KAIR,CAEAhD,aAAAA,GACE,MAAM,SAAElC,EAAQ,YAAEI,GAAgBZ,KAAK5C,OACjC,aAAEmC,EAAY,aAAED,GAAiBU,KAAKlB,OACtC,UAAEe,GAAcG,KAAKR,MAmB3B,OACEjC,EAAAA,EAAAA,GAAA,SAAOQ,UAAU,mBAAkB0D,SAChCzB,KAAKmF,eACJvE,GACArD,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,YAGjBzB,EACAD,GA3BmB4G,CAACJ,EAAUC,KAEhCpE,EAAAA,EAAAA,IAACN,EAAAA,GAAI,CACHC,GAAIiC,EAAAA,EAAO4C,mBACT3F,EAAS0C,KAAKkD,GAASA,EAAK1C,UAAS2C,QAAO,CAACC,EAAMlD,SAAsBwB,IAAdmB,EAAK3C,KAChE0C,EAGA,CAACtF,EAAS,GAAGiD,eAEf8C,OAAO,SACP1F,MAAM,aAAYY,SAAA,CAEjBqE,EACAjG,MAeDwE,EAAAA,EAAMmC,eAId,CAGArB,cAAAA,CACEsB,EACAC,GAKC,IAJDC,IAAI9H,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,KAAAA,UAAA,GACJ+H,EAAM/H,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,IAAAA,UAAA,GACNgI,EAAShI,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,GAAAA,UAAA,GAAG,CAACiH,EAAUC,IAAcD,EACrCgB,EAASjI,UAAAsC,OAAA,QAAAyD,IAAA/F,UAAA,GAAAA,UAAA,GAAI8G,GAAgBoB,MAAMpB,GAAS,IAAIA,KAAWA,EAG3D,MAAM7B,EAAOkD,EAAAA,EAAeC,QAAQR,GAC9BV,EAAO,CAAC,EASd,GAPAjC,EAAKoD,SAASC,GAAOpB,EAAKoB,GAAK,KAC/BV,EAAKS,SAAQ,CAACE,EAAcjD,KAE1BL,EAAKoD,SAASC,GAAMpB,EAAKoB,GAAGE,UAAKzC,KAEjCwC,EAAQF,SAAS/D,GAAY4C,EAAK5C,EAAE2C,KAAK3B,GAAKhB,EAAEwC,OAAO,IAErDH,IAAAA,QAAU1B,IAAS0B,IAAAA,QAAUiB,GAC/B,OACElJ,EAAAA,EAAAA,GAAA,MAAIQ,UAAW,cAAa4I,EAAO,GAAK,cAAelF,UACrDlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,0BAAyB0D,UACjDlE,EAAAA,EAAAA,GAACJ,EAAY,CAAAsE,UACXlE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,wBAGfC,OAAQ,CAAEyF,UAAWA,WAQZlB,IAAAA,MAAQ1B,GAAOgC,IAASiB,MAAMjB,KAEjDhC,EAAKwD,MAAK,CAACC,EAAGC,IAAMC,SAASF,EAAG,IAAME,SAASD,EAAG,MAElD1D,EAAKwD,OAEP,IAAII,GAAY,EAChB,MAAMC,EAAa7D,EAAKZ,KAAKiE,IAE3B,MAAMS,EAAc7B,EAAKoB,GAAGhG,OAAS,GAAKqE,IAAAA,KAAOO,EAAKoB,IAAIhG,OAAS,EAEnE,OADAuG,GAAaE,GAAeF,GAE1B/F,EAAAA,EAAAA,IAAA,MAAY5D,UAAW,cAAc6I,IAAWgB,IAAiBjB,EAAO,aAAe,IAAKlF,SAAA,EAC1FlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,0BAAyB0D,SAEhDoF,EAAUM,EAAGpB,EAAKoB,MAGpBpB,EAAKoB,GAAGjE,KAAI,CAACyC,EAAYxB,KACxB5G,EAAAA,EAAAA,GAAA,MACEQ,UAAW,6BAA4B6J,EAAc,iBAAmB,IAAKnG,UAG7ElE,EAAAA,EAAAA,GAAA,QAAMQ,UAAU,yCAAwC0D,cAC3CmD,IAAVe,EAAsB,IAAMmB,EAAUnB,MAHpC3F,KAAK5C,MAAMoD,SAAS2D,GAAGT,aATzByD,EAgBJ,IAGT,OAAIO,GAAad,GAEbrJ,EAAAA,EAAAA,GAAA,MAAIQ,UAAW,cAAa4I,EAAO,GAAK,cAAelF,UACrDlE,EAAAA,EAAAA,GAAA,MAAI0F,MAAM,MAAMlF,UAAU,0BAAyB0D,UACjDlE,EAAAA,EAAAA,GAACJ,EAAY,CAAAsE,UACXlE,EAAAA,EAAAA,GAACuD,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,4BAGfC,OAAQ,CAAEyF,UAAWA,WAO1BiB,CACT,EAGF,MAAME,EAAuCC,IAC3C,MAAMC,EAAiB,CAAC,EAUxB,OATAD,EAAQZ,SAAQ,CAACc,EAAaC,KAC5B,MAAMC,EAAOF,EAAOE,KAAOF,EAAOE,KAAO,GACnCC,EAAOH,EAAOG,KAAOH,EAAOG,KAAO,GAEzCJ,EAAeE,GAAS,CACtBnC,IAAKmC,EACLtC,MAAgB,KAATuC,GAAwB,KAATC,EAAc,GAAGD,MAASC,IAAS,GAAGD,IAAOC,IACpE,IAEIJ,CAAc,EAGjBK,EAAsCN,IAC1C,MAAMO,EAAgB,CAAC,EAUvB,OATAP,EAAQZ,SAASc,IACf,MAAME,EAAOF,EAAOE,KAAOF,EAAOE,KAAO,IACnCC,EAAOH,EAAOG,KAAOH,EAAOG,KAAO,IAEzCE,EAAcH,GAAQ,CACpBpC,IAAKoC,EACLvC,MAAOwC,EACR,IAEIE,CAAa,EAkEhBzG,EAAS,CACbE,QAAUwG,IAAY,CACpB,iBAAkB,CAEhBC,SANuB,KAMZD,EAAU,MAGzBzG,yBAA0B,CACxB,eAAgB,CACd2G,QAAS,OACTC,UAAW,QAEb,oCAAqC,CACnCC,WAAY,SACZC,aAAc,WACdC,YAAa,QACbC,UAAW,eAEb,yBAA0B,CACxBC,OAAQ,GACR5K,QAAS,GAEX,eAAgB,CACdG,QAAS,QACT0K,MAAO,OACPH,YAAa,SAEf,gBAAiB,CACfvK,QAAS,QAEX,yBAA0B,CACxB0K,MAAO,OACP1K,QAAS,QACTE,OAAQ,OACRyK,UAAW,IACXC,UAAW,QAEb,gCAAiC,CAC/BD,UAAW,KAEb,2BAA4B,CAC1B3K,QAAS,OACT6K,WAAY,GACZC,cAAe,IAEjB,gCAAiC,CAC/BzL,UAAW,OACXW,QAAS,QACT0K,MAAO,OACPD,OAAQ,OACRvK,OAAQ,QAEV,mBAAoB,CAClBb,UAAW,OACXW,QAAS,OACTC,WAAY,SACZC,OAAQ,OACRP,gBAAiB,QACjBQ,YAAa,GAEf,gBAAiB,CACf4K,SAAU,QACVb,SAAU,SAEZ,qBAAsB,CACpB/J,YAAa,GAEf,sBAAuB,CACrB6K,aAAc,IAEhB,iBAAkB,CAChBC,UAAW,GAEb,UAAW,CACT9K,YAAa,EACb+K,SAAU,MAKHC,GAA2BC,EAAAA,EAAAA,KA/IhBC,CAAC5K,EAAY6K,KACnC,MAAMnJ,EAAW,GACXwC,EAAgB,GAChBpC,EAAc,GACdD,EAAa,GACbiD,EAAW,GACXlD,EAAkB,GAClBD,EAAW,IACX,UAAEJ,EAAS,eAAEsD,GAAmBgG,EAChCvJ,EAAmB,GACnBD,EAAoB,GACpBI,EAAoB,GACpBD,EAAqB,GAC3B,IAAK,MAAMyD,KAAgBJ,EACzB,GAAIA,GAAkBI,KAAgBJ,EAAgB,CACpD,MAAMD,EAAUC,EAAeI,GACzB6F,GAAUC,EAAAA,EAAAA,IAAWnG,EAAS5E,GAChC8K,GACFpJ,EAAS6G,KAAKuC,GACd5G,EAAcqE,MAAK,GACnBzG,EAAYyG,KAAKxD,OAAO5C,QAAO6I,EAAAA,EAAAA,IAAiBpG,EAAS5E,KACzD6B,EAAW0G,KAAKxD,OAAO5C,QAAO8I,EAAAA,EAAAA,IAAUrG,EAAS5E,KACjD8E,EAASyD,KAAKhD,EAAAA,EAAM2F,WAAWJ,IAE/BlJ,EAAgB2G,KAAKhD,EAAAA,EAAM4F,kBAAkBL,EAASlG,IACtDjD,EAAS4G,KAAK3D,KAEVA,EACFlD,EAAS6G,KAAK,CAAE3D,YAEhBlD,EAAS6G,KAAK,CAAE3D,QAAS,SAE3BV,EAAcqE,MAAK,GACnBzG,EAAYyG,KAAK,IACjB1G,EAAW0G,KAAK,IAChBzD,EAASyD,KAAK,gBAEhB,MAAM6C,GAASC,EAAAA,EAAAA,IAAuBrL,EAAOuB,EAAW0D,GACxD5D,EAAkBkH,KAAKxD,OAAO5C,OAAO4G,EAAqCqC,EAAeE,UACzFhK,EAAiBiH,KAAKxD,OAAO5C,OAAOmH,EAAoC8B,EAAeE,UACvF9J,EAAmB+G,KAAKxD,OAAO5C,OAAO4G,EAAqCqC,EAAeG,WAC1F9J,EAAkB8G,KAAKxD,OAAO5C,OAAOmH,EAAoC8B,EAAeG,UAC1F,CAGF,MAAO,CACL7J,WACAwC,gBACApC,cACAD,aACAiD,WACAlD,kBACAD,WACAJ,YACAD,mBACAD,oBACAI,oBACAD,qBACD,GAqFqCmJ,EAAyBa,EAAAA,EAAAA,IAAW5L,I,gDC9wBrE,MAAM6L,UAAqC5L,EAAAA,UAGhDC,WAAAA,GAAA,SAAAC,WAAA,KACA2L,0BAA2BC,EAAAA,EAAAA,MAAU,KACrCC,kBAAmBD,EAAAA,EAAAA,MAAU,KAC7BE,cAAeF,EAAAA,EAAAA,MAAU,KACzBG,yBAA0BH,EAAAA,EAAAA,MAAU,KAEpC3L,MAAQ,CACN+L,WAAY,CAEV7K,KAAKwK,yBACLxK,KAAK2K,aACL3K,KAAK0K,iBACL1K,KAAK4K,yBAEPE,gCAAiC,CAAC9K,KAAK2K,aAAc3K,KAAK4K,yBAC1D,CAEFG,kBAAAA,GACE/K,KAAKC,UAAU+K,IAAc,CAC3BH,WAAYrF,IAAAA,QAAUwF,EAAUH,WAAY7K,KAAK2K,iBAErD,CAEAM,iBAAAA,GACEjL,KAAK5C,MAAM8N,sBAAsBlL,KAAK5C,MAAMiD,UAAWL,KAAKwK,0BAC5D,IAAK,MAAMzG,KAAgB/D,KAAK5C,MAAMuG,eACpC,GAAI,CAAC,EAAEwH,eAAeC,KAAKpL,KAAK5C,MAAMuG,eAAgBI,GAAe,CACnE,MAAMsH,EAAQrL,KAAK5C,MAAMuG,eAAeI,GACpCsH,EACFrL,KAAK5C,MAAMkO,UAAUD,EAAOrL,KAAK2K,cAAcY,OAAM,KAInDvL,KAAK+K,oBAAoB,IAG3B/K,KAAK+K,qBAEP,MAAM,UAAE1K,GAAcL,KAAK5C,MAC3B4C,KAAK5C,MAAMoO,mBAAmBnL,EAAW0D,EAAc/D,KAAK0K,kBAC5D1K,KAAK5C,MACFqO,2BAA2BpL,EAAW0D,GACtC2H,MAAMC,GACL3L,KAAK5C,MAAMwO,iBAAiBvL,EAAW0D,EAAc4H,EAAQhG,MAAO3F,KAAK4K,2BAE1EW,OAAM,KAILvL,KAAKC,UAAU+K,IAAc,CAC3BH,WAAYrF,IAAAA,QAAUwF,EAAUH,WAAY7K,KAAK4K,4BAChD,GAET,CAEJ,CAEA1K,MAAAA,GACE,OACE3C,EAAAA,EAAAA,GAACsO,EAAAA,EAAa,CAAApK,UACZlE,EAAAA,EAAAA,GAACuO,EAAAA,GAAmB,CAClBjB,WAAY7K,KAAKlB,MAAM+L,WACvBkB,2BAA4B/L,KAAKlB,MAAMgM,gCAAgCrJ,UAEvElE,EAAAA,EAAAA,GAACiM,EAAwB,CAACnJ,UAAWL,KAAK5C,MAAMiD,UAAWsD,eAAgB3D,KAAK5C,MAAMuG,oBAI9F,EAGF,MAUMqI,EAAqB,CACzBV,UAAS,KACTJ,sBAAqB,KACrBM,mBAAkB,KAClBC,2BAA0B,KAC1BG,iBACF,MAEMK,GAAqCC,EAAAA,EAAAA,IACzCzC,EAAAA,EAAAA,KAnBsBC,CAAC5K,EAAY6K,KACnC,MAAM,SAAEwC,GAAaxC,EACfyC,EAAeC,IAAAA,MAASF,EAASG,QAKvC,MAAO,CAAEjM,UAHSkM,mBAAmBC,KAAKC,MAAML,EAAa,WAGzCzI,eADG6I,KAAKC,MAAML,EAAmB,MACjB,GAYXJ,EAAzBvC,CAA6Cc,IAGlCmC,GAA2BC,EAAAA,EAAAA,GACtCC,EAAAA,EAAWC,eAAeC,eAC1Bb,GAGF,O", "sources": ["model-registry/components/CompareModelVersionsView.tsx", "model-registry/components/CompareModelVersionsPage.tsx"], "sourcesContent": ["/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport { connect } from 'react-redux';\nimport { Link } from '../../common/utils/RoutingUtils';\nimport _ from 'lodash';\nimport { FormattedMessage, injectIntl, IntlShape } from 'react-intl';\nimport { Switch, LegacyTabs, useDesignSystemTheme } from '@databricks/design-system';\n\nimport { getParams, getRunInfo } from '../../experiment-tracking/reducers/Reducers';\nimport '../../experiment-tracking/components/CompareRunView.css';\nimport { CompareRunScatter } from '../../experiment-tracking/components/CompareRunScatter';\nimport { CompareRunBox } from '../../experiment-tracking/components/CompareRunBox';\nimport CompareRunContour from '../../experiment-tracking/components/CompareRunContour';\nimport Routes from '../../experiment-tracking/routes';\nimport { getLatestMetrics } from '../../experiment-tracking/reducers/MetricReducer';\nimport CompareRunUtil from '../../experiment-tracking/components/CompareRunUtil';\nimport Utils from '../../common/utils/Utils';\nimport ParallelCoordinatesPlotPanel from '../../experiment-tracking/components/ParallelCoordinatesPlotPanel';\nimport { ModelRegistryRoutes } from '../routes';\nimport { getModelVersionSchemas } from '../reducers';\nimport { PageHeader } from '../../shared/building_blocks/PageHeader';\nimport type { RunInfoEntity } from '../../experiment-tracking/types';\n\nconst { TabPane } = LegacyTabs;\n\nfunction CenteredText(props: any) {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <div\n      css={{\n        textAlign: 'center',\n        color: theme.colors.textSecondary,\n      }}\n      {...props}\n    />\n  );\n}\n\nfunction CompareTable(props: any) {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <table\n      className=\"compare-table table\"\n      css={{\n        'th.main-table-header': {\n          backgroundColor: theme.colors.white,\n          padding: 0,\n        },\n        'td.highlight-data': {\n          backgroundColor: theme.colors.backgroundValidationWarning,\n        },\n      }}\n      {...props}\n    />\n  );\n}\n\nfunction CollapseButton(props: any) {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <button\n      css={{\n        textAlign: 'left',\n        display: 'flex',\n        alignItems: 'center',\n        border: 'none',\n        backgroundColor: theme.colors.white,\n        paddingLeft: 0,\n        cursor: 'pointer',\n      }}\n      {...props}\n    />\n  );\n}\n\ntype CompareModelVersionsViewImplProps = {\n  runInfos: RunInfoEntity[];\n  runInfosValid: boolean[];\n  runUuids: string[];\n  metricLists: any[][];\n  paramLists: any[][];\n  runNames: string[];\n  runDisplayNames: string[];\n  modelName: string;\n  versionsToRuns: any;\n  // @ts-expect-error TS(2314): Generic type 'Array<T>' requires 1 type argument(s... Remove this comment to see the full error message\n  inputsListByName: Array[];\n  // @ts-expect-error TS(2314): Generic type 'Array<T>' requires 1 type argument(s... Remove this comment to see the full error message\n  inputsListByIndex: Array[];\n  // @ts-expect-error TS(2314): Generic type 'Array<T>' requires 1 type argument(s... Remove this comment to see the full error message\n  outputsListByName: Array[];\n  // @ts-expect-error TS(2314): Generic type 'Array<T>' requires 1 type argument(s... Remove this comment to see the full error message\n  outputsListByIndex: Array[];\n  intl: IntlShape;\n};\n\ntype CompareModelVersionsViewImplState = any;\n\nexport class CompareModelVersionsViewImpl extends Component<\n  CompareModelVersionsViewImplProps,\n  CompareModelVersionsViewImplState\n> {\n  state = {\n    inputActive: true,\n    outputActive: true,\n    paramsToggle: true,\n    paramsActive: true,\n    schemaToggle: true,\n    compareByColumnNameToggle: false,\n    schemaActive: true,\n    metricToggle: true,\n    metricActive: true,\n  };\n\n  icons = {\n    plusIcon: <i className=\"far fa-plus-square-o\" />,\n    minusIcon: <i className=\"far fa-minus-square-o\" />,\n    downIcon: <i className=\"fas fa-caret-down\" />,\n    rightIcon: <i className=\"fas fa-caret-right\" />,\n    chartIcon: <i className=\"fas fa-line-chart padding-left-text\" />,\n  };\n\n  onToggleClick = (active: any) => {\n    this.setState((state: any) => ({\n      [active]: !state[active],\n    }));\n  };\n\n  render() {\n    const {\n      inputsListByIndex,\n      inputsListByName,\n      modelName,\n      outputsListByIndex,\n      outputsListByName,\n      runInfos,\n      runUuids,\n      runDisplayNames,\n      paramLists,\n      metricLists,\n    } = this.props;\n    const title = (\n      <FormattedMessage\n        defaultMessage=\"Comparing {numVersions} Versions\"\n        description=\"Text for main title for the model comparison page\"\n        values={{ numVersions: this.props.runInfos.length }}\n      />\n    );\n    const breadcrumbs = [\n      <Link to={ModelRegistryRoutes.modelListPageRoute}>\n        <FormattedMessage\n          defaultMessage=\"Registered Models\"\n          description=\"Text for registered model link in the title for model comparison page\"\n        />\n      </Link>,\n      <Link to={ModelRegistryRoutes.getModelPageRoute(modelName)}>{modelName}</Link>,\n    ];\n\n    return (\n      <div\n        className=\"CompareModelVersionsView\"\n        // @ts-expect-error TS(2322): Type '{ '.compare-table': { minWidth: number; }; '... Remove this comment to see the full error message\n        css={{\n          ...styles.compareModelVersionsView,\n          ...styles.wrapper(runInfos.length),\n        }}\n      >\n        <PageHeader title={title} breadcrumbs={breadcrumbs} />\n        <div className=\"responsive-table-container\">\n          <CompareTable>\n            {this.renderTableHeader()}\n            {this.renderModelVersionInfo()}\n            {this.renderSectionHeader(\n              'paramsActive',\n              'paramsToggle',\n              <FormattedMessage\n                defaultMessage=\"Parameters\"\n                description=\"Table title text for parameters table in the model comparison page\"\n              />,\n            )}\n            {this.renderParams()}\n            {this.renderSectionHeader(\n              'schemaActive',\n              'schemaToggle',\n              <FormattedMessage\n                defaultMessage=\"Schema\"\n                description=\"Table title text for schema table in the model comparison page\"\n              />,\n              false,\n              // @ts-expect-error TS(2345): Argument of type 'Element' is not assignable to pa... Remove this comment to see the full error message\n              <Switch\n                className=\"toggle-switch\"\n                // @ts-expect-error TS(2322): Type '{ className: string; style: { marginLeft: st... Remove this comment to see the full error message\n                style={{ marginLeft: 'auto' }}\n                onChange={() => this.onToggleClick('compareByColumnNameToggle')}\n              />,\n              <div className=\"padding-left-text padding-right-text\">\n                <span>\n                  <FormattedMessage\n                    defaultMessage=\"Ignore column ordering\"\n                    description=\"Toggle text that determines whether to ignore column order in the\n                      model comparison page\"\n                  />\n                </span>\n              </div>,\n            )}\n            {this.renderSchemaSectionHeader(\n              'inputActive',\n              <FormattedMessage\n                defaultMessage=\"Inputs\"\n                description=\"Table subtitle for schema inputs in the model comparison page\"\n              />,\n            )}\n            {this.renderSchema(\n              'inputActive',\n              <FormattedMessage\n                defaultMessage=\"Inputs\"\n                description=\"Table section name for schema inputs in the model comparison page\"\n              />,\n              inputsListByIndex,\n              inputsListByName,\n            )}\n            {this.renderSchemaSectionHeader(\n              'outputActive',\n              <FormattedMessage\n                defaultMessage=\"Outputs\"\n                description=\"Table subtitle for schema outputs in the model comparison page\"\n              />,\n            )}\n            {this.renderSchema(\n              'outputActive',\n              <FormattedMessage\n                defaultMessage=\"Outputs\"\n                description=\"Table section name for schema outputs in the model comparison page\"\n              />,\n              outputsListByIndex,\n              outputsListByName,\n            )}\n            {this.renderSectionHeader(\n              'metricActive',\n              'metricToggle',\n              <FormattedMessage\n                defaultMessage=\"Metrics\"\n                description=\"Table title text for metrics table in the model comparison page\"\n              />,\n            )}\n            {this.renderMetrics()}\n          </CompareTable>\n        </div>\n        <LegacyTabs>\n          <TabPane\n            tab={\n              <FormattedMessage\n                defaultMessage=\"Parallel Coordinates Plot\"\n                description=\"Tab text for parallel coordinates plot on the model comparison page\"\n              />\n            }\n            key=\"parallel-coordinates-plot\"\n          >\n            <ParallelCoordinatesPlotPanel runUuids={runUuids} />\n          </TabPane>\n          <TabPane\n            tab={\n              <FormattedMessage\n                defaultMessage=\"Scatter Plot\"\n                description=\"Tab text for scatter plot on the model comparison page\"\n              />\n            }\n            key=\"scatter-plot\"\n          >\n            <CompareRunScatter runUuids={runUuids} runDisplayNames={runDisplayNames} />\n          </TabPane>\n          <TabPane\n            tab={\n              <FormattedMessage\n                defaultMessage=\"Box Plot\"\n                description=\"Tab pane title for box plot on the compare runs page\"\n              />\n            }\n            key=\"box-plot\"\n          >\n            <CompareRunBox runUuids={runUuids} runInfos={runInfos} paramLists={paramLists} metricLists={metricLists} />\n          </TabPane>\n          <TabPane\n            tab={\n              <FormattedMessage\n                defaultMessage=\"Contour Plot\"\n                description=\"Tab text for contour plot on the model comparison page\"\n              />\n            }\n            key=\"contour-plot\"\n          >\n            <CompareRunContour runUuids={runUuids} runDisplayNames={runDisplayNames} />\n          </TabPane>\n        </LegacyTabs>\n      </div>\n    );\n  }\n\n  renderTableHeader() {\n    const { runInfos, runInfosValid } = this.props;\n    return (\n      <thead>\n        <tr className=\"table-row\">\n          <th scope=\"row\" className=\"row-header block-content\">\n            <FormattedMessage\n              defaultMessage=\"Run ID:\"\n              description=\"Text for run ID header in the main table in the model comparison page\"\n            />\n          </th>\n          {runInfos.map((r, idx) => (\n            <th scope=\"column\" className=\"data-value block-content\" key={r.runUuid}>\n              {/* Do not show links for invalid run IDs */}\n              {runInfosValid[idx] ? (\n                <Link to={Routes.getRunPageRoute(r.experimentId ?? '0', r.runUuid ?? '')}>{r.runUuid}</Link>\n              ) : (\n                r.runUuid\n              )}\n            </th>\n          ))}\n        </tr>\n      </thead>\n    );\n  }\n\n  renderModelVersionInfo() {\n    const { runInfos, runInfosValid, versionsToRuns, runNames, modelName } = this.props;\n    return (\n      <tbody className=\"scrollable-table\">\n        <tr className=\"table-row\">\n          <th scope=\"row\" className=\"data-value block-content\">\n            <FormattedMessage\n              defaultMessage=\"Model Version:\"\n              description=\"Text for model version row header in the main table in the model\n                comparison page\"\n            />\n          </th>\n          {Object.keys(versionsToRuns).map((modelVersion) => {\n            const run = versionsToRuns[modelVersion];\n            return (\n              <td className=\"meta-info block-content\" key={run}>\n                <Link to={ModelRegistryRoutes.getModelVersionPageRoute(modelName, modelVersion)}>{modelVersion}</Link>\n              </td>\n            );\n          })}\n        </tr>\n        <tr className=\"table-row\">\n          <th scope=\"row\" className=\"data-value block-content\">\n            <FormattedMessage\n              defaultMessage=\"Run Name:\"\n              description=\"Text for run name row header in the main table in the model comparison\n                page\"\n            />\n          </th>\n          {runNames.map((runName, i) => {\n            return (\n              <td className=\"meta-info block-content\" key={runInfos[i].runUuid}>\n                <div className=\"truncate-text single-line cell-content\">{runName}</div>\n              </td>\n            );\n          })}\n        </tr>\n        <tr className=\"table-row\">\n          <th scope=\"row\" className=\"data-value block-content\">\n            <FormattedMessage\n              defaultMessage=\"Start Time:\"\n              description=\"Text for start time row header in the main table in the model comparison\n                page\"\n            />\n          </th>\n          {runInfos.map((run, idx) => {\n            /* Do not attempt to get timestamps for invalid run IDs */\n            const startTime =\n              run.startTime && runInfosValid[idx] ? Utils.formatTimestamp(run.startTime, this.props.intl) : '(unknown)';\n            return (\n              <td className=\"meta-info block-content\" key={run.runUuid}>\n                {startTime}\n              </td>\n            );\n          })}\n        </tr>\n      </tbody>\n    );\n  }\n\n  /* additional Switch and Text are antd Switch component and the text followed by the toggle switch\n  this is currently used in schema section where we have an additional switch toggle for\n  ignore column ordering, same logic can be applied if future section needs additional toggle */\n  renderSectionHeader(\n    activeSection: any,\n    toggleSection: any,\n    sectionName: any,\n    leftToggle = true,\n    additionalSwitch = null,\n    additionalSwitchText = null,\n  ) {\n    const { runInfos } = this.props;\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    const isActive = this.state[activeSection];\n    const { downIcon, rightIcon } = this.icons;\n    return (\n      <tbody>\n        <tr>\n          <th scope=\"rowgroup\" className=\"block-content main-table-header\" colSpan={runInfos.length + 1}>\n            <div className=\"switch-button-container\">\n              <CollapseButton onClick={() => this.onToggleClick(activeSection)}>\n                {isActive ? downIcon : rightIcon}\n                <span className=\"header\">{sectionName}</span>\n              </CollapseButton>\n              {additionalSwitch}\n              {additionalSwitchText}\n              <Switch\n                defaultChecked\n                className=\"toggle-switch\"\n                // @ts-expect-error TS(2322): Type '{ defaultChecked: true; className: string; s... Remove this comment to see the full error message\n                style={leftToggle ? { marginLeft: 'auto' } : {}}\n                onChange={() => this.onToggleClick(toggleSection)}\n              />\n              <div className=\"padding-left-text\">\n                <span>\n                  <FormattedMessage\n                    defaultMessage=\"Show diff only\"\n                    description=\"Toggle text that determines whether to show diff only in the model\n                      comparison page\"\n                  />\n                </span>\n              </div>\n            </div>\n          </th>\n        </tr>\n      </tbody>\n    );\n  }\n\n  renderParams() {\n    return (\n      <tbody className=\"scrollable-table\">\n        {this.renderDataRows(\n          this.props.paramLists,\n          <FormattedMessage\n            defaultMessage=\"Parameters\"\n            description=\"Field name text for parameters table in the model comparison page\"\n          />,\n          this.state.paramsActive,\n          this.state.paramsToggle,\n        )}\n      </tbody>\n    );\n  }\n\n  renderSchemaSectionHeader(activeSection: any, sectionName: any) {\n    const { runInfos } = this.props;\n    const { schemaActive } = this.state;\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    const isActive = this.state[activeSection];\n    const { minusIcon, plusIcon } = this.icons;\n    return (\n      <tbody>\n        <tr className={`${schemaActive ? '' : 'hidden-row'}`}>\n          <th scope=\"rowgroup\" className=\"schema-table-header block-content\" colSpan={runInfos.length + 1}>\n            <button className=\"schema-collapse-button\" onClick={() => this.onToggleClick(activeSection)}>\n              {isActive ? minusIcon : plusIcon}\n              <strong style={{ paddingLeft: 4 }}>{sectionName}</strong>\n            </button>\n          </th>\n        </tr>\n      </tbody>\n    );\n  }\n\n  renderSchema(activeSection: any, sectionName: any, listByIndex: any, listByName: any) {\n    const { schemaActive, compareByColumnNameToggle, schemaToggle } = this.state;\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    const isActive = this.state[activeSection];\n    const showSchemaSection = schemaActive && isActive;\n    const showListByIndex = !compareByColumnNameToggle && !_.isEmpty(listByIndex);\n    const showListByName = compareByColumnNameToggle && !_.isEmpty(listByName);\n    const listByIndexHeaderMap = (key: any, data: any) => (\n      <>\n        {sectionName} [{key}]\n      </>\n    );\n    const listByNameHeaderMap = (key: any, data: any) => key;\n    const schemaFormatter = (value: any) => value;\n    const schemaFieldName = (\n      <FormattedMessage\n        defaultMessage=\"Schema {sectionName}\"\n        description=\"Field name text for schema table in the model comparison page\"\n        values={{ sectionName: sectionName }}\n      />\n    );\n    return (\n      <tbody className=\"scrollable-table schema-scrollable-table\">\n        {this.renderDataRows(\n          listByIndex,\n          schemaFieldName,\n          showSchemaSection && showListByIndex,\n          schemaToggle,\n          listByIndexHeaderMap,\n          schemaFormatter,\n        )}\n        {this.renderDataRows(\n          listByName,\n          schemaFieldName,\n          showSchemaSection && showListByName,\n          schemaToggle,\n          listByNameHeaderMap,\n          schemaFormatter,\n        )}\n      </tbody>\n    );\n  }\n\n  renderMetrics() {\n    const { runInfos, metricLists } = this.props;\n    const { metricActive, metricToggle } = this.state;\n    const { chartIcon } = this.icons;\n    const metricsHeaderMap = (key: any, data: any) => {\n      return (\n        <Link\n          to={Routes.getMetricPageRoute(\n            runInfos.map((info) => info.runUuid).filter((uuid, idx) => data[idx] !== undefined),\n            key,\n            // TODO: Refactor so that the breadcrumb\n            // on the linked page is for model registry\n            [runInfos[0].experimentId],\n          )}\n          target=\"_blank\"\n          title=\"Plot chart\"\n        >\n          {key}\n          {chartIcon}\n        </Link>\n      );\n    };\n    return (\n      <tbody className=\"scrollable-table\">\n        {this.renderDataRows(\n          metricLists,\n          <FormattedMessage\n            defaultMessage=\"Metrics\"\n            description=\"Field name text for metrics table in the model comparison page\"\n          />,\n          metricActive,\n          metricToggle,\n          metricsHeaderMap,\n          Utils.formatMetric,\n        )}\n      </tbody>\n    );\n  }\n\n  // eslint-disable-next-line no-unused-vars\n  renderDataRows(\n    list: any,\n    fieldName: any,\n    show = true,\n    toggle = false,\n    headerMap = (key: any, data: any) => key,\n    formatter = (value: any) => (isNaN(value) ? `\"${value}\"` : value),\n  ) {\n    // @ts-expect-error TS(2554): Expected 2 arguments, but got 1.\n    const keys = CompareRunUtil.getKeys(list);\n    const data = {};\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    keys.forEach((k) => (data[k] = []));\n    list.forEach((records: any, i: any) => {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      keys.forEach((k) => data[k].push(undefined));\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      records.forEach((r: any) => (data[r.key][i] = r.value));\n    });\n    if (_.isEmpty(keys) || _.isEmpty(list)) {\n      return (\n        <tr className={`table-row ${show ? '' : 'hidden-row'}`}>\n          <th scope=\"row\" className=\"rowHeader block-content\">\n            <CenteredText>\n              <FormattedMessage\n                defaultMessage=\"{fieldName} are empty\"\n                description=\"Default text in data table where items are empty in the model\n                  comparison page\"\n                values={{ fieldName: fieldName }}\n              />\n            </CenteredText>\n          </th>\n        </tr>\n      );\n    }\n    // @ts-expect-error TS(2345): Argument of type 'string' is not assignable to par... Remove this comment to see the full error message\n    const isAllNumeric = _.every(keys, (key) => !isNaN(key));\n    if (isAllNumeric) {\n      keys.sort((a, b) => parseInt(a, 10) - parseInt(b, 10));\n    } else {\n      keys.sort();\n    }\n    let identical = true;\n    const resultRows = keys.map((k) => {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      const isDifferent = data[k].length > 1 && _.uniq(data[k]).length > 1;\n      identical = !isDifferent && identical;\n      return (\n        <tr key={k} className={`table-row ${(toggle && !isDifferent) || !show ? 'hidden-row' : ''}`}>\n          <th scope=\"row\" className=\"rowHeader block-content\">\n            {/* @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message */}\n            {headerMap(k, data[k])}\n          </th>\n          {/* @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message */}\n          {data[k].map((value: any, i: any) => (\n            <td\n              className={`data-value block-content ${isDifferent ? 'highlight-data' : ''}`}\n              key={this.props.runInfos[i].runUuid}\n            >\n              <span className=\"truncate-text single-line cell-content\">\n                {value === undefined ? '-' : formatter(value)}\n              </span>\n            </td>\n          ))}\n        </tr>\n      );\n    });\n    if (identical && toggle) {\n      return (\n        <tr className={`table-row ${show ? '' : 'hidden-row'}`}>\n          <th scope=\"row\" className=\"rowHeader block-content\">\n            <CenteredText>\n              <FormattedMessage\n                defaultMessage=\"{fieldName} are identical\"\n                // eslint-disable-next-line max-len\n                description=\"Default text in data table where items are identical in the model comparison page\"\n                values={{ fieldName: fieldName }}\n              />\n            </CenteredText>\n          </th>\n        </tr>\n      );\n    }\n    return resultRows;\n  }\n}\n\nconst getModelVersionSchemaColumnsByIndex = (columns: any) => {\n  const columnsByIndex = {};\n  columns.forEach((column: any, index: any) => {\n    const name = column.name ? column.name : '';\n    const type = column.type ? column.type : '';\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    columnsByIndex[index] = {\n      key: index,\n      value: name !== '' && type !== '' ? `${name}: ${type}` : `${name}${type}`,\n    };\n  });\n  return columnsByIndex;\n};\n\nconst getModelVersionSchemaColumnsByName = (columns: any) => {\n  const columnsByName = {};\n  columns.forEach((column: any) => {\n    const name = column.name ? column.name : '-';\n    const type = column.type ? column.type : '-';\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    columnsByName[name] = {\n      key: name,\n      value: type,\n    };\n  });\n  return columnsByName;\n};\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const runInfos = [];\n  const runInfosValid = [];\n  const metricLists = [];\n  const paramLists = [];\n  const runNames = [];\n  const runDisplayNames = [];\n  const runUuids = [];\n  const { modelName, versionsToRuns } = ownProps;\n  const inputsListByName = [];\n  const inputsListByIndex = [];\n  const outputsListByName = [];\n  const outputsListByIndex = [];\n  for (const modelVersion in versionsToRuns) {\n    if (versionsToRuns && modelVersion in versionsToRuns) {\n      const runUuid = versionsToRuns[modelVersion];\n      const runInfo = getRunInfo(runUuid, state);\n      if (runInfo) {\n        runInfos.push(runInfo);\n        runInfosValid.push(true);\n        metricLists.push(Object.values(getLatestMetrics(runUuid, state)));\n        paramLists.push(Object.values(getParams(runUuid, state)));\n        runNames.push(Utils.getRunName(runInfo));\n        // the following are used to render plots - we only include valid run IDs here\n        runDisplayNames.push(Utils.getRunDisplayName(runInfo, runUuid));\n        runUuids.push(runUuid);\n      } else {\n        if (runUuid) {\n          runInfos.push({ runUuid });\n        } else {\n          runInfos.push({ runUuid: 'None' });\n        }\n        runInfosValid.push(false);\n        metricLists.push([]);\n        paramLists.push([]);\n        runNames.push('Invalid Run');\n      }\n      const schema = getModelVersionSchemas(state, modelName, modelVersion);\n      inputsListByIndex.push(Object.values(getModelVersionSchemaColumnsByIndex((schema as any).inputs)));\n      inputsListByName.push(Object.values(getModelVersionSchemaColumnsByName((schema as any).inputs)));\n      outputsListByIndex.push(Object.values(getModelVersionSchemaColumnsByIndex((schema as any).outputs)));\n      outputsListByName.push(Object.values(getModelVersionSchemaColumnsByName((schema as any).outputs)));\n    }\n  }\n\n  return {\n    runInfos,\n    runInfosValid,\n    metricLists,\n    paramLists,\n    runNames,\n    runDisplayNames,\n    runUuids,\n    modelName,\n    inputsListByName,\n    inputsListByIndex,\n    outputsListByName,\n    outputsListByIndex,\n  };\n};\n\nconst DEFAULT_COLUMN_WIDTH = 200;\n\nconst styles = {\n  wrapper: (numRuns: any) => ({\n    '.compare-table': {\n      // 1 extra unit for header column\n      minWidth: (numRuns + 1) * DEFAULT_COLUMN_WIDTH,\n    },\n  }),\n  compareModelVersionsView: {\n    'button:focus': {\n      outline: 'none',\n      boxShadow: 'none',\n    },\n    'td.block-content th.block-content': {\n      whiteSpace: 'nowrap',\n      textOverflow: 'ellipsis',\n      tableLayout: 'fixed',\n      boxSizing: 'content-box',\n    },\n    'th.schema-table-header': {\n      height: 28,\n      padding: 0,\n    },\n    'tr.table-row': {\n      display: 'table',\n      width: '100%',\n      tableLayout: 'fixed',\n    },\n    'tr.hidden-row': {\n      display: 'none',\n    },\n    'tbody.scrollable-table': {\n      width: '100%',\n      display: 'block',\n      border: 'none',\n      maxHeight: 400,\n      overflowY: 'auto',\n    },\n    'tbody.schema-scrollable-table': {\n      maxHeight: 200,\n    },\n    '.switch-button-container': {\n      display: 'flex',\n      paddingTop: 16,\n      paddingBottom: 16,\n    },\n    'button.schema-collapse-button': {\n      textAlign: 'left',\n      display: 'block',\n      width: '100%',\n      height: '100%',\n      border: 'none',\n    },\n    '.collapse-button': {\n      textAlign: 'left',\n      display: 'flex',\n      alignItems: 'center',\n      border: 'none',\n      backgroundColor: 'white',\n      paddingLeft: 0,\n    },\n    '.cell-content': {\n      maxWidth: '200px',\n      minWidth: '100px',\n    },\n    '.padding-left-text': {\n      paddingLeft: 8,\n    },\n    '.padding-right-text': {\n      paddingRight: 16,\n    },\n    '.toggle-switch': {\n      marginTop: 2,\n    },\n    '.header': {\n      paddingLeft: 8,\n      fontSize: 16,\n    },\n  },\n};\n\nexport const CompareModelVersionsView = connect(mapStateToProps)(injectIntl(CompareModelVersionsViewImpl));\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport qs from 'qs';\nimport { connect } from 'react-redux';\nimport { getRunApi } from '../../experiment-tracking/actions';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { getRegisteredModelApi, getModelVersionApi, getModelVersionArtifactApi, parseMlModelFile } from '../actions';\nimport RequestStateWrapper from '../../common/components/RequestStateWrapper';\nimport { CompareModelVersionsView } from './CompareModelVersionsView';\nimport _ from 'lodash';\nimport { PageContainer } from '../../common/components/PageContainer';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\n\ntype CompareModelVersionsPageImplProps = {\n  modelName: string;\n  versionsToRuns: any;\n  getRunApi: (...args: any[]) => any;\n  getRegisteredModelApi: (...args: any[]) => any;\n  getModelVersionApi: (...args: any[]) => any;\n  getModelVersionArtifactApi: (...args: any[]) => any;\n  parseMlModelFile: (...args: any[]) => any;\n};\n\ntype CompareModelVersionsPageImplState = any;\n\n// TODO: Write integration tests for this component\nexport class CompareModelVersionsPageImpl extends Component<\n  CompareModelVersionsPageImplProps,\n  CompareModelVersionsPageImplState\n> {\n  registeredModelRequestId = getUUID();\n  versionRequestId = getUUID();\n  runRequestId = getUUID();\n  getMlModelFileRequestId = getUUID();\n\n  state = {\n    requestIds: [\n      // requests that must be fulfilled before rendering\n      this.registeredModelRequestId,\n      this.runRequestId,\n      this.versionRequestId,\n      this.getMlModelFileRequestId,\n    ],\n    requestIdsWith404ErrorsToIgnore: [this.runRequestId, this.getMlModelFileRequestId],\n  };\n\n  removeRunRequestId() {\n    this.setState((prevState: any) => ({\n      requestIds: _.without(prevState.requestIds, this.runRequestId),\n    }));\n  }\n\n  componentDidMount() {\n    this.props.getRegisteredModelApi(this.props.modelName, this.registeredModelRequestId);\n    for (const modelVersion in this.props.versionsToRuns) {\n      if ({}.hasOwnProperty.call(this.props.versionsToRuns, modelVersion)) {\n        const runID = this.props.versionsToRuns[modelVersion];\n        if (runID) {\n          this.props.getRunApi(runID, this.runRequestId).catch(() => {\n            // Failure of this call should not block the page. Here we remove\n            // `runRequestId` from `requestIds` to unblock RequestStateWrapper\n            // from rendering its content\n            this.removeRunRequestId();\n          });\n        } else {\n          this.removeRunRequestId();\n        }\n        const { modelName } = this.props;\n        this.props.getModelVersionApi(modelName, modelVersion, this.versionRequestId);\n        this.props\n          .getModelVersionArtifactApi(modelName, modelVersion)\n          .then((content: any) =>\n            this.props.parseMlModelFile(modelName, modelVersion, content.value, this.getMlModelFileRequestId),\n          )\n          .catch(() => {\n            // Failure of this call chain should not block the page. Here we remove\n            // `getMlModelFileRequestId` from `requestIds` to unblock RequestStateWrapper\n            // from rendering its content\n            this.setState((prevState: any) => ({\n              requestIds: _.without(prevState.requestIds, this.getMlModelFileRequestId),\n            }));\n          });\n      }\n    }\n  }\n\n  render() {\n    return (\n      <PageContainer>\n        <RequestStateWrapper\n          requestIds={this.state.requestIds}\n          requestIdsWith404sToIgnore={this.state.requestIdsWith404ErrorsToIgnore}\n        >\n          <CompareModelVersionsView modelName={this.props.modelName} versionsToRuns={this.props.versionsToRuns} />\n        </RequestStateWrapper>\n      </PageContainer>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: WithRouterNextProps) => {\n  const { location } = ownProps;\n  const searchValues = qs.parse(location.search);\n  // @ts-expect-error TS(2345): Argument of type 'string | string[] | ParsedQs | P... Remove this comment to see the full error message\n  const modelName = decodeURIComponent(JSON.parse(searchValues['?name']));\n  // @ts-expect-error TS(2345): Argument of type 'string | string[] | ParsedQs | P... Remove this comment to see the full error message\n  const versionsToRuns = JSON.parse(searchValues['runs']);\n  return { modelName, versionsToRuns };\n};\n\nconst mapDispatchToProps = {\n  getRunApi,\n  getRegisteredModelApi,\n  getModelVersionApi,\n  getModelVersionArtifactApi,\n  parseMlModelFile,\n};\n\nconst CompareModelVersionsPageWithRouter = withRouterNext(\n  connect(mapStateToProps, mapDispatchToProps)(CompareModelVersionsPageImpl),\n);\n\nexport const CompareModelVersionsPage = withErrorBoundary(\n  ErrorUtils.mlflowServices.MODEL_REGISTRY,\n  CompareModelVersionsPageWithRouter,\n);\n\nexport default CompareModelVersionsPage;\n"], "names": ["TabPane", "LegacyTabs", "CenteredText", "props", "theme", "useDesignSystemTheme", "_jsx", "css", "_css", "textAlign", "color", "colors", "textSecondary", "CompareTable", "className", "backgroundColor", "white", "padding", "backgroundValidationWarning", "CollapseButton", "display", "alignItems", "border", "paddingLeft", "cursor", "CompareModelVersionsViewImpl", "Component", "constructor", "arguments", "state", "inputActive", "outputActive", "paramsToggle", "paramsActive", "schemaToggle", "compareByColumnNameToggle", "schemaActive", "metricToggle", "metricActive", "icons", "plusIcon", "minusIcon", "downIcon", "rightIcon", "chartIcon", "onToggleClick", "active", "this", "setState", "render", "inputsListByIndex", "inputsListByName", "modelName", "outputsListByIndex", "outputsListByName", "runInfos", "runUuids", "runDisplayNames", "paramLists", "metricLists", "title", "FormattedMessage", "id", "defaultMessage", "values", "numVersions", "length", "breadcrumbs", "Link", "to", "ModelRegistryRoutes", "modelListPageRoute", "children", "getModelPageRoute", "_jsxs", "styles", "compareModelVersionsView", "wrapper", "<PERSON><PERSON><PERSON><PERSON>", "renderTableHeader", "renderModelVersionInfo", "renderSectionHeader", "renderParams", "Switch", "style", "marginLeft", "onChange", "renderSchemaSectionHeader", "renderSchema", "renderMetrics", "tab", "ParallelCoordinatesPlotPanel", "CompareRunScatter", "CompareRunBox", "CompareRunContour", "runInfosValid", "scope", "map", "r", "idx", "_r$experimentId", "_r$runUuid", "Routes", "getRunPageRoute", "experimentId", "runUuid", "versionsToRuns", "runNames", "Object", "keys", "modelVersion", "run", "getModelVersionPageRoute", "runName", "i", "startTime", "Utils", "formatTimestamp", "intl", "activeSection", "toggleSection", "sectionName", "leftToggle", "undefined", "additionalSwitch", "additionalSwitchText", "isActive", "colSpan", "onClick", "defaultChecked", "renderDataRows", "listByIndex", "listByName", "showSchemaSection", "showListByIndex", "_", "showListByName", "schema<PERSON><PERSON><PERSON><PERSON>", "value", "schemaFieldName", "listByIndexHeaderMap", "key", "data", "_Fragment", "listByNameHeaderMap", "metricsHeaderMap", "getMetricPageRoute", "info", "filter", "uuid", "target", "formatMetric", "list", "fieldName", "show", "toggle", "headerMap", "formatter", "isNaN", "CompareRunUtil", "get<PERSON><PERSON><PERSON>", "for<PERSON>ach", "k", "records", "push", "sort", "a", "b", "parseInt", "identical", "resultRows", "isDifferent", "getModelVersionSchemaColumnsByIndex", "columns", "columnsByIndex", "column", "index", "name", "type", "getModelVersionSchemaColumnsByName", "columnsByName", "numRuns", "min<PERSON><PERSON><PERSON>", "outline", "boxShadow", "whiteSpace", "textOverflow", "tableLayout", "boxSizing", "height", "width", "maxHeight", "overflowY", "paddingTop", "paddingBottom", "max<PERSON><PERSON><PERSON>", "paddingRight", "marginTop", "fontSize", "CompareModelVersionsView", "connect", "mapStateToProps", "ownProps", "runInfo", "getRunInfo", "getLatestMetrics", "getParams", "getRunName", "getRunDisplayName", "schema", "getModelVersionSchemas", "inputs", "outputs", "injectIntl", "CompareModelVersionsPageImpl", "registeredModelRequestId", "getUUID", "versionRequestId", "runRequestId", "getMlModelFileRequestId", "requestIds", "requestIdsWith404ErrorsToIgnore", "removeRunRequestId", "prevState", "componentDidMount", "getRegisteredModelApi", "hasOwnProperty", "call", "runID", "getRunApi", "catch", "getModelVersionApi", "getModelVersionArtifactApi", "then", "content", "parseMlModelFile", "<PERSON><PERSON><PERSON><PERSON>", "RequestStateWrapper", "requestIdsWith404sToIgnore", "mapDispatchToProps", "CompareModelVersionsPageWithRouter", "withRouterNext", "location", "searchValues", "qs", "search", "decodeURIComponent", "JSON", "parse", "CompareModelVersionsPage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY"], "sourceRoot": ""}