{"version": 3, "file": "static/js/7473.86c94070.chunk.js", "mappings": "kHAgDAA,EAAOC,QA5BS,SAASC,EAAWC,EAAQC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GAOzD,IAAKP,EAAW,CACd,IAAIQ,EACJ,QAAeC,IAAXR,EACFO,EAAQ,IAAIE,MACV,qIAGG,CACL,IAAIC,EAAO,CAACT,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACvBK,EAAW,GACfJ,EAAQ,IAAIE,MACVT,EAAOY,QAAQ,OAAO,WAAa,OAAOF,EAAKC,IAAa,MAExDE,KAAO,qBACf,CAGA,MADAN,EAAMO,YAAc,EACdP,CACR,CACF,C,6FCzCM,SAAUQ,EACdC,GAEA,IAAMC,GAAUC,EAAAA,EAAAA,aAAWC,EAAAA,EAAAA,MACrBC,EAASJ,GAAYC,EAAQG,OAQnC,OAPAC,SACEC,EAAAA,EAAAA,MACAF,EAAA,uKAGAE,EAAAA,EAAAA,MAAAF,EAAA,IAEKA,CACT,C,wEClBA,SAASG,EAAQC,GAAmV,OAAtOD,EAArD,oBAAXE,QAAoD,kBAApBA,OAAOC,SAAmC,SAAiBF,GAAO,cAAcA,CAAK,EAAsB,SAAiBA,GAAO,OAAOA,GAAyB,oBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAAK,EAAYD,EAAQC,EAAM,C,eCQlX,SAASK,EAAYC,EAAQC,GAMlC,IALA,IAGIC,EAHAC,EAAa,eACbC,EAAO,EACPC,EAASJ,EAAW,GAGhBC,EAAQC,EAAWG,KAAKN,EAAOO,QAAUL,EAAMM,MAAQP,GAC7DG,GAAQ,EACRC,EAASJ,EAAW,GAAKC,EAAMM,MAAQN,EAAM,GAAGO,QAGlD,MAAO,CACLL,KAAMA,EACNC,OAAQA,EAEZ,CClBO,SAASK,EAAcC,GAC5B,OAAOC,EAAoBD,EAASX,OAAQD,EAAYY,EAASX,OAAQW,EAASE,OACpF,CAKO,SAASD,EAAoBZ,EAAQc,GAC1C,IAAIC,EAAwBf,EAAOgB,eAAeX,OAAS,EACvDE,EAAOU,EAAWF,GAAyBf,EAAOO,KAClDW,EAAYJ,EAAeV,KAAO,EAClCe,EAAanB,EAAOgB,eAAeZ,KAAO,EAC1CgB,EAAUN,EAAeV,KAAOe,EAChCE,EAAuC,IAAxBP,EAAeV,KAAaW,EAAwB,EACnEO,EAAYR,EAAeT,OAASgB,EACpCE,EAAc,GAAGC,OAAOxB,EAAOjB,KAAM,KAAKyC,OAAOJ,EAAS,KAAKI,OAAOF,EAAW,MACjFG,EAAQlB,EAAKmB,MAAM,gBACnBC,EAAeF,EAAMP,GAEzB,GAAIS,EAAalB,OAAS,IAAK,CAK7B,IAJA,IAAImB,EAAeC,KAAKC,MAAMR,EAAY,IACtCS,EAAmBT,EAAY,GAC/BU,EAAW,GAENC,EAAI,EAAGA,EAAIN,EAAalB,OAAQwB,GAAK,GAC5CD,EAASE,KAAKP,EAAaQ,MAAMF,EAAGA,EAAI,KAG1C,OAAOV,EAAca,EAAmB,CAAC,CAAC,GAAGZ,OAAOJ,GAAUY,EAAS,KAAKR,OAAOQ,EAASG,MAAM,EAAGP,EAAe,GAAGS,KAAI,SAAUC,GACnI,MAAO,CAAC,GAAIA,EACd,IAAI,CAAC,CAAC,IAAKrB,EAAWc,EAAmB,GAAK,KAAM,CAAC,GAAIC,EAASJ,EAAe,MACnF,CAEA,OAAOL,EAAca,EAAmB,CACxC,CAAC,GAAGZ,OAAOJ,EAAU,GAAIK,EAAMP,EAAY,IAAK,CAAC,GAAGM,OAAOJ,GAAUO,GAAe,CAAC,GAAIV,EAAWK,EAAY,GAAK,KAAM,CAAC,GAAGE,OAAOJ,EAAU,GAAIK,EAAMP,EAAY,KACxK,CAEA,SAASkB,EAAmBX,GAC1B,IAAIc,EAAgBd,EAAMe,QAAO,SAAUC,GACjCA,EAAK,GAEb,YAAgB/D,IADL+D,EAAK,EAElB,IACIC,EAASb,KAAKc,IAAIC,MAAMf,KAAMU,EAAcF,KAAI,SAAUQ,GAE5D,OADaA,EAAM,GACLpC,MAChB,KACA,OAAO8B,EAAcF,KAAI,SAAUS,GACjC,IAUkBC,EAVdC,EAASF,EAAM,GACf1C,EAAO0C,EAAM,GACjB,OASK7B,EATUyB,GAQGK,EARKC,GASGvC,QAAUsC,GATF3C,EAAO,MAAQA,EAAO,KAC1D,IAAG6C,KAAK,KACV,CAEA,SAAShC,EAAWiC,GAClB,OAAOC,MAAMD,EAAM,GAAGD,KAAK,IAC7B,CC7DA,SAASxD,EAAQC,GAAmV,OAAtOD,EAArD,oBAAXE,QAAoD,kBAApBA,OAAOC,SAAmC,SAAiBF,GAAO,cAAcA,CAAK,EAAsB,SAAiBA,GAAO,OAAOA,GAAyB,oBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAAK,EAAYD,EAAQC,EAAM,CAIzX,SAAS0D,EAAkBC,EAAQC,GAAS,IAAK,IAAIrB,EAAI,EAAGA,EAAIqB,EAAM7C,OAAQwB,IAAK,CAAE,IAAIsB,EAAaD,EAAMrB,GAAIsB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMC,OAAOC,eAAeP,EAAQE,EAAWM,IAAKN,EAAa,CAAE,CAQ5T,SAASO,EAA2BC,EAAMC,GAAQ,OAAIA,GAA2B,WAAlBvE,EAAQuE,IAAsC,oBAATA,EAA8CC,EAAuBF,GAAtCC,CAA6C,CAEhL,SAASC,EAAuBF,GAAQ,QAAa,IAATA,EAAmB,MAAM,IAAIG,eAAe,6DAAgE,OAAOH,CAAM,CAErK,SAASI,EAAiBC,GAAS,IAAIC,EAAwB,oBAARC,IAAqB,IAAIA,SAAQ5F,EAA8nB,OAAnnByF,EAAmB,SAA0BC,GAAS,GAAc,OAAVA,IAMlIG,EANuKH,GAMjG,IAAzDI,SAASC,SAAST,KAAKO,GAAIG,QAAQ,kBAN+H,OAAON,EAMjN,IAA2BG,EAN6L,GAAqB,oBAAVH,EAAwB,MAAM,IAAIO,UAAU,sDAAyD,GAAsB,qBAAXN,EAAwB,CAAE,GAAIA,EAAOO,IAAIR,GAAQ,OAAOC,EAAOQ,IAAIT,GAAQC,EAAOS,IAAIV,EAAOW,EAAU,CAAE,SAASA,IAAY,OAAOC,EAAWZ,EAAOa,UAAWC,EAAgBC,MAAMtF,YAAc,CAAkJ,OAAhJkF,EAAQjF,UAAY6D,OAAOyB,OAAOhB,EAAMtE,UAAW,CAAED,YAAa,CAAEwF,MAAON,EAASvB,YAAY,EAAOE,UAAU,EAAMD,cAAc,KAAkB6B,EAAgBP,EAASX,EAAQ,EAAUD,EAAiBC,EAAQ,CAEtvB,SAASY,EAAWO,EAAQ3G,EAAMwF,GAAqV,OAAzSY,EAA/BQ,IAA4CC,QAAQC,UAAiC,SAAoBH,EAAQ3G,EAAMwF,GAAS,IAAIjG,EAAI,CAAC,MAAOA,EAAE+D,KAAKU,MAAMzE,EAAGS,GAAO,IAAsD+G,EAAW,IAA/CnB,SAASoB,KAAKhD,MAAM2C,EAAQpH,IAA6F,OAAnDiG,GAAOkB,EAAgBK,EAAUvB,EAAMtE,WAAmB6F,CAAU,EAAYX,EAAWpC,MAAM,KAAMqC,UAAY,CAEja,SAASO,IAA8B,GAAuB,qBAAZC,UAA4BA,QAAQC,UAAW,OAAO,EAAO,GAAID,QAAQC,UAAUG,KAAM,OAAO,EAAO,GAAqB,oBAAVC,MAAsB,OAAO,EAAM,IAAiF,OAA3EC,KAAKjG,UAAU2E,SAAST,KAAKyB,QAAQC,UAAUK,KAAM,IAAI,WAAa,MAAY,CAAM,CAAE,MAAOxH,GAAK,OAAO,CAAO,CAAE,CAInU,SAAS+G,EAAgBU,EAAGC,GAA+G,OAA1GX,EAAkB3B,OAAOuC,gBAAkB,SAAyBF,EAAGC,GAAsB,OAAjBD,EAAEG,UAAYF,EAAUD,CAAG,EAAUV,EAAgBU,EAAGC,EAAI,CAEzK,SAASf,EAAgBc,GAAwJ,OAAnJd,EAAkBvB,OAAOuC,eAAiBvC,OAAOyC,eAAiB,SAAyBJ,GAAK,OAAOA,EAAEG,WAAaxC,OAAOyC,eAAeJ,EAAI,EAAUd,EAAgBc,EAAI,CAerM,IAAIK,EAA4B,SAAUC,IAjCjD,SAAmBC,EAAUC,GAAc,GAA0B,oBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAI7B,UAAU,sDAAyD4B,EAASzG,UAAY6D,OAAOyB,OAAOoB,GAAcA,EAAW1G,UAAW,CAAED,YAAa,CAAEwF,MAAOkB,EAAU7C,UAAU,EAAMD,cAAc,KAAe+C,GAAYlB,EAAgBiB,EAAUC,EAAa,CAkC9XC,CAAUJ,EAAcC,GAExB,IAtCoBI,EAAaC,EAAYC,EAsCzCC,EAlCN,SAAsBC,GAAW,IAAIC,EAA4BvB,IAA6B,OAAO,WAAkC,IAAsCwB,EAAlCC,EAAQ/B,EAAgB4B,GAAkB,GAAIC,EAA2B,CAAE,IAAIG,EAAYhC,EAAgBC,MAAMtF,YAAamH,EAASvB,QAAQC,UAAUuB,EAAOhC,UAAWiC,EAAY,MAASF,EAASC,EAAMrE,MAAMuC,KAAMF,WAAc,OAAOnB,EAA2BqB,KAAM6B,EAAS,CAAG,CAkCzZG,CAAad,GAmD1B,SAASA,EAAae,EAASC,EAAOrH,EAAQsH,EAAWC,EAAMC,EAAeC,GAC5E,IAAIC,EAAaC,EAAUC,EAAaC,EAEpCC,GAhGR,SAAyBnC,EAAUe,GAAe,KAAMf,aAAoBe,GAAgB,MAAM,IAAI/B,UAAU,oCAAwC,CAkGpJoD,CAAgB5C,KAAMkB,GAEtByB,EAAQjB,EAAO7C,KAAKmB,KAAMiC,GAE1B,IAMMY,EANFC,EAAS9E,MAAM+E,QAAQb,GAA0B,IAAjBA,EAAM5G,OAAe4G,OAAQ3I,EAAY2I,EAAQ,CAACA,QAAS3I,EAG3FyJ,EAAUnI,GAETmI,GAAWF,IAGdE,EAA6C,QAAlCH,EAAeC,EAAO,GAAGG,WAAkC,IAAjBJ,OAA0B,EAASA,EAAahI,QAGvG,IAgBIqI,EAhBAC,EAAahB,GAEZgB,GAAcL,IACjBK,EAAaL,EAAOM,QAAO,SAAUC,EAAMC,GAKzC,OAJIA,EAAKL,KACPI,EAAKtG,KAAKuG,EAAKL,IAAIvH,OAGd2H,CACT,GAAG,KAGDF,GAAoC,IAAtBA,EAAW7H,SAC3B6H,OAAa5J,GAKX4I,GAAatH,EACfqI,EAAaf,EAAUjF,KAAI,SAAUqG,GACnC,OAAO3I,EAAYC,EAAQ0I,EAC7B,IACST,IACTI,EAAaJ,EAAOM,QAAO,SAAUC,EAAMC,GAKzC,OAJIA,EAAKL,KACPI,EAAKtG,KAAKnC,EAAY0I,EAAKL,IAAIpI,OAAQyI,EAAKL,IAAIvH,QAG3C2H,CACT,GAAG,KAGL,IH7IiCnD,EG6I7BsD,EAAclB,EAElB,GAAmB,MAAfkB,GAAwC,MAAjBnB,EAAuB,CAChD,IAAIoB,EAAqBpB,EAAcC,WH/IlB,UAAlBhI,EAD4B4F,EGkJduD,IHjJ0B,OAAVvD,IGkJ/BsD,EAAcC,EAElB,CAuDA,OArDAjF,OAAOkF,iBAAiB5E,EAAuB6D,GAAQ,CACrD/I,KAAM,CACJsG,MAAO,gBAET+B,QAAS,CACP/B,MAAO+B,EAIP5D,YAAY,EACZE,UAAU,GAEZoF,UAAW,CAGTzD,MAAsC,QAA9BqC,EAAcW,SAAwC,IAAhBX,EAAyBA,OAAchJ,EAIrF8E,WAA0B,MAAd6E,GAEdd,KAAM,CAGJlC,MAAgB,OAATkC,QAA0B,IAATA,EAAkBA,OAAO7I,EAIjD8E,WAAoB,MAAR+D,GAEdF,MAAO,CACLhC,MAAkB,OAAX4C,QAA8B,IAAXA,EAAoBA,OAASvJ,GAEzDsB,OAAQ,CACNqF,MAAgC,QAAxBsC,EAAWQ,SAAkC,IAAbR,EAAsBA,OAAWjJ,GAE3E4I,UAAW,CACTjC,MAAsC,QAA9BuC,EAAcU,SAAwC,IAAhBV,EAAyBA,OAAclJ,GAEvF8I,cAAe,CACbnC,MAAOmC,GAETC,WAAY,CAGVpC,MAAwC,QAAhCwC,EAAec,SAA0C,IAAjBd,EAA0BA,OAAenJ,EAIzF8E,WAA2B,MAAfmF,KAIM,OAAlBnB,QAA4C,IAAlBA,GAA4BA,EAAcuB,OACtEpF,OAAOC,eAAeK,EAAuB6D,GAAQ,QAAS,CAC5DzC,MAAOmC,EAAcuB,MACrBrF,UAAU,EACVD,cAAc,IAETK,EAA2BgE,KAIhCnJ,MAAMqK,kBACRrK,MAAMqK,kBAAkB/E,EAAuB6D,GAAQzB,GAEvD1C,OAAOC,eAAeK,EAAuB6D,GAAQ,QAAS,CAC5DzC,MAAO1G,QAAQoK,MACfrF,UAAU,EACVD,cAAc,IAIXqE,EACT,CAgBA,OAjPoBpB,EAmOPL,GAnOoBM,EAmON,CAAC,CAC1B9C,IAAK,WACLwB,MAAO,WACL,OAkBC,SAAoB5G,GACzB,IAAIwK,EAASxK,EAAM2I,QAEnB,GAAI3I,EAAM4I,MACR,IAAK,IAAI6B,EAAM,EAAGC,EAAgB1K,EAAM4I,MAAO6B,EAAMC,EAAc1I,OAAQyI,IAAO,CAChF,IAAIT,EAAOU,EAAcD,GAErBT,EAAKL,MACPa,GAAU,OAASvI,EAAc+H,EAAKL,KAE1C,MACK,GAAI3J,EAAMuB,QAAUvB,EAAMqK,UAC/B,IAAK,IAAIM,EAAM,EAAGC,EAAoB5K,EAAMqK,UAAWM,EAAMC,EAAkB5I,OAAQ2I,IAAO,CAC5F,IAAIzI,EAAW0I,EAAkBD,GACjCH,GAAU,OAASrI,EAAoBnC,EAAMuB,OAAQW,EACvD,CAGF,OAAOsI,CACT,CArCaK,CAAWnE,KACpB,GAGC,CACDtB,IAAK0F,EAAAA,GACL1E,IAAK,WACH,MAAO,QACT,MA9O0EzB,EAAkBsD,EAAY5G,UAAW6G,GAAiBC,GAAaxD,EAAkBsD,EAAaE,GAiP3KP,CACT,CA/MuC,CA+MvBlC,EAAiBxF,QClP1B,SAAS6K,EAAYxJ,EAAQC,EAAUwJ,GAC5C,OAAO,IAAIpD,EAAa,iBAAiB7E,OAAOiI,QAAc/K,EAAWsB,EAAQ,CAACC,GACpF,CCLO,IAAIyJ,EAAO/F,OAAOgG,OAAO,CAE9BC,KAAM,OAENC,SAAU,WACVC,qBAAsB,sBACtBC,oBAAqB,qBACrBC,cAAe,eACfC,MAAO,QACPC,SAAU,WAEVC,gBAAiB,iBACjBC,gBAAiB,iBACjBC,oBAAqB,qBAErBC,SAAU,WACVC,IAAK,WACLC,MAAO,aACPC,OAAQ,cACRC,QAAS,eACTC,KAAM,YACNC,KAAM,YACNC,KAAM,YACNC,OAAQ,cACRC,aAAc,cAEdC,UAAW,YAEXC,WAAY,YACZC,UAAW,WACXC,cAAe,cAEfC,kBAAmB,mBACnBC,0BAA2B,0BAE3BC,uBAAwB,uBACxBC,uBAAwB,uBACxBC,iBAAkB,kBAClBC,uBAAwB,uBACxBC,0BAA2B,0BAC3BC,sBAAuB,sBACvBC,qBAAsB,qBACtBC,sBAAuB,sBACvBC,6BAA8B,4BAE9BC,qBAAsB,sBAEtBC,iBAAkB,kBAElBC,sBAAuB,sBACvBC,sBAAuB,sBACvBC,yBAA0B,yBAC1BC,qBAAsB,qBACtBC,oBAAqB,oBACrBC,4BAA6B,6B,WCrDpBC,EAAY5I,OAAOgG,OAAO,CACnC6C,IAAK,QACLC,IAAK,QACLC,KAAM,IACNC,OAAQ,IACRC,IAAK,IACLC,QAAS,IACTC,QAAS,IACTC,OAAQ,MACRC,MAAO,IACPC,OAAQ,IACRC,GAAI,IACJC,UAAW,IACXC,UAAW,IACXC,QAAS,IACTC,KAAM,IACNC,QAAS,IACT3D,KAAM,OACNW,IAAK,MACLC,MAAO,QACPC,OAAQ,SACR+C,aAAc,cACdC,QAAS,Y,WCvBAC,EAAoB/J,OAAOgG,OAAO,CAE3CgE,MAAO,QACPC,SAAU,WACVC,aAAc,eACd5D,MAAO,QACPI,oBAAqB,sBACrBF,gBAAiB,kBACjBC,gBAAiB,kBACjBL,oBAAqB,sBAErB+D,OAAQ,SACRC,OAAQ,SACRjD,OAAQ,SACRU,iBAAkB,mBAClBwC,oBAAqB,sBACrBC,UAAW,YACXC,MAAO,QACPtD,KAAM,OACNuD,WAAY,aACZC,aAAc,eACdC,uBAAwB,2B,WCXfC,EAAqB,WAgB9B,SAASA,EAAMtO,GACb,IAAIuO,EAAmB,IAAIC,EAAAA,GAAMjC,EAAUC,IAAK,EAAG,EAAG,EAAG,EAAG,MAC5DrH,KAAKnF,OAASA,EACdmF,KAAKsJ,UAAYF,EACjBpJ,KAAKuJ,MAAQH,EACbpJ,KAAK/E,KAAO,EACZ+E,KAAKwJ,UAAY,CACnB,CAMA,IAAIC,EAASN,EAAMxO,UA4BnB,OA1BA8O,EAAOC,QAAU,WAGf,OAFA1J,KAAKsJ,UAAYtJ,KAAKuJ,MACVvJ,KAAKuJ,MAAQvJ,KAAK2J,WAEhC,EAOAF,EAAOE,UAAY,WACjB,IAAIJ,EAAQvJ,KAAKuJ,MAEjB,GAAIA,EAAMK,OAASxC,EAAUE,IAC3B,EAAG,CACD,IAAIuC,EAGJN,EAAuC,QAA9BM,EAAcN,EAAMO,YAAkC,IAAhBD,EAAyBA,EAAcN,EAAMO,KAAOC,EAAU/J,KAAMuJ,EACrH,OAASA,EAAMK,OAASxC,EAAUkB,SAGpC,OAAOiB,CACT,EAEOJ,CACT,CA1DgC,GAmEhC,SAASa,EAAcC,GACrB,OACEC,MAAMD,GAAQ7C,EAAUE,IACxB2C,EAAO,IAASE,KAAKC,UAAUC,OAAOC,aAAaL,IACnD,OAAQ5N,QAAQ,KAAO4N,EAAK3K,SAAS,IAAIiL,eAAevN,OAAO,GAAI,IAEvE,CAUA,SAAS+M,EAAUS,EAAOC,GAMxB,IALA,IAAI5P,EAAS2P,EAAM3P,OACfO,EAAOP,EAAOO,KACdsP,EAAatP,EAAKE,OAClBiI,EAAMkH,EAAKE,IAERpH,EAAMmH,GAAY,CACvB,IAAIT,EAAO7O,EAAKwP,WAAWrH,GACvBsH,EAAQL,EAAMvP,KAEd6P,EAAO,EAAIvH,EAAMiH,EAAMhB,UAG3B,OAAQS,GACN,KAAK,MAEL,KAAK,EAEL,KAAK,GAEL,KAAK,KAED1G,EACF,SAEF,KAAK,KAEDA,IACAiH,EAAMvP,KACRuP,EAAMhB,UAAYjG,EAClB,SAEF,KAAK,GAE8B,KAA7BnI,EAAKwP,WAAWrH,EAAM,GACxBA,GAAO,IAELA,IAGFiH,EAAMvP,KACRuP,EAAMhB,UAAYjG,EAClB,SAEF,KAAK,GAEH,OAAO,IAAI8F,EAAAA,GAAMjC,EAAUG,KAAMhE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAE9D,KAAK,GAEH,OAAOM,EAAYlQ,EAAQ0I,EAAKsH,EAAOC,EAAML,GAE/C,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUI,OAAQjE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEhE,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUK,IAAKlE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAE7D,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUM,QAASnE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEjE,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUO,QAASpE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEjE,KAAK,GAEH,GAAiC,KAA7BrP,EAAKwP,WAAWrH,EAAM,IAA0C,KAA7BnI,EAAKwP,WAAWrH,EAAM,GAC3D,OAAO,IAAI8F,EAAAA,GAAMjC,EAAUQ,OAAQrE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAGhE,MAEF,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUS,MAAOtE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAE/D,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUU,OAAQvE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEhE,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUW,GAAIxE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAE5D,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUY,UAAWzE,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEnE,KAAK,GAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUa,UAAW1E,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEnE,KAAK,IAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUc,QAAS3E,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEjE,KAAK,IAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUe,KAAM5E,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAE9D,KAAK,IAEH,OAAO,IAAIpB,EAAAA,GAAMjC,EAAUgB,QAAS7E,EAAKA,EAAM,EAAGsH,EAAOC,EAAML,GAEjE,KAAK,GAEH,OAAiC,KAA7BrP,EAAKwP,WAAWrH,EAAM,IAA0C,KAA7BnI,EAAKwP,WAAWrH,EAAM,GACpDyH,EAAgBnQ,EAAQ0I,EAAKsH,EAAOC,EAAML,EAAMD,GAGlDS,EAAWpQ,EAAQ0I,EAAKsH,EAAOC,EAAML,GAE9C,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEH,OAAOS,EAAWrQ,EAAQ0I,EAAK0G,EAAMY,EAAOC,EAAML,GAEpD,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEL,KAAK,IAEH,OAAOU,EAAStQ,EAAQ0I,EAAKsH,EAAOC,EAAML,GAG9C,MAAMpG,EAAYxJ,EAAQ0I,EAAK6H,EAA2BnB,GAC5D,CAEA,IAAIhP,EAAOuP,EAAMvP,KACboQ,EAAM,EAAI9H,EAAMiH,EAAMhB,UAC1B,OAAO,IAAIH,EAAAA,GAAMjC,EAAUE,IAAKoD,EAAYA,EAAYzP,EAAMoQ,EAAKZ,EACrE,CAMA,SAASW,EAA2BnB,GAClC,OAAIA,EAAO,IAAmB,IAATA,GAA4B,KAATA,GAA4B,KAATA,EAClD,wCAAwC5N,OAAO2N,EAAcC,GAAO,KAGhE,KAATA,EAEK,kFAGF,yCAAyC5N,OAAO2N,EAAcC,GAAO,IAC9E,CAQA,SAASc,EAAYlQ,EAAQa,EAAOT,EAAMoQ,EAAKZ,GAC7C,IACIR,EADA7O,EAAOP,EAAOO,KAEdN,EAAWY,EAEf,GACEuO,EAAO7O,EAAKwP,aAAa9P,UACjBoP,MAAMD,KAChBA,EAAO,IAAmB,IAATA,IAEjB,OAAO,IAAIZ,EAAAA,GAAMjC,EAAUkB,QAAS5M,EAAOZ,EAAUG,EAAMoQ,EAAKZ,EAAMrP,EAAK4B,MAAMtB,EAAQ,EAAGZ,GAC9F,CAUA,SAASoQ,EAAWrQ,EAAQa,EAAO4P,EAAWrQ,EAAMoQ,EAAKZ,GACvD,IAAIrP,EAAOP,EAAOO,KACd6O,EAAOqB,EACPxQ,EAAWY,EACX6P,GAAU,EAOd,GALa,KAATtB,IAEFA,EAAO7O,EAAKwP,aAAa9P,IAGd,KAATmP,GAIF,IAFAA,EAAO7O,EAAKwP,aAAa9P,KAEb,IAAMmP,GAAQ,GACxB,MAAM5F,EAAYxJ,EAAQC,EAAU,6CAA6CuB,OAAO2N,EAAcC,GAAO,WAG/GnP,EAAW0Q,EAAW3Q,EAAQC,EAAUmP,GACxCA,EAAO7O,EAAKwP,WAAW9P,GA0BzB,GAvBa,KAATmP,IAEFsB,GAAU,EACVtB,EAAO7O,EAAKwP,aAAa9P,GACzBA,EAAW0Q,EAAW3Q,EAAQC,EAAUmP,GACxCA,EAAO7O,EAAKwP,WAAW9P,IAGZ,KAATmP,GAAwB,MAATA,IAEjBsB,GAAU,EAGG,MAFbtB,EAAO7O,EAAKwP,aAAa9P,KAEG,KAATmP,IAEjBA,EAAO7O,EAAKwP,aAAa9P,IAG3BA,EAAW0Q,EAAW3Q,EAAQC,EAAUmP,GACxCA,EAAO7O,EAAKwP,WAAW9P,IAIZ,KAATmP,GAsON,SAAqBA,GACnB,OAAgB,KAATA,GAAeA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,GAC1E,CAxOqBwB,CAAYxB,GAC7B,MAAM5F,EAAYxJ,EAAQC,EAAU,2CAA2CuB,OAAO2N,EAAcC,GAAO,MAG7G,OAAO,IAAIZ,EAAAA,GAAMkC,EAAUnE,EAAU/B,MAAQ+B,EAAUhC,IAAK1J,EAAOZ,EAAUG,EAAMoQ,EAAKZ,EAAMrP,EAAK4B,MAAMtB,EAAOZ,GAClH,CAMA,SAAS0Q,EAAW3Q,EAAQa,EAAO4P,GACjC,IAAIlQ,EAAOP,EAAOO,KACdN,EAAWY,EACXuO,EAAOqB,EAEX,GAAIrB,GAAQ,IAAMA,GAAQ,GAAI,CAE5B,GACEA,EAAO7O,EAAKwP,aAAa9P,SAClBmP,GAAQ,IAAMA,GAAQ,IAG/B,OAAOnP,CACT,CAEA,MAAMuJ,EAAYxJ,EAAQC,EAAU,2CAA2CuB,OAAO2N,EAAcC,GAAO,KAC7G,CAQA,SAASgB,EAAWpQ,EAAQa,EAAOT,EAAMoQ,EAAKZ,GAO5C,IANA,IAsJmBzR,EAAGC,EAAGC,EAAGC,EAtJxBiC,EAAOP,EAAOO,KACdN,EAAWY,EAAQ,EACnBgQ,EAAa5Q,EACbmP,EAAO,EACP/J,EAAQ,GAELpF,EAAWM,EAAKE,SAAW4O,MAAMD,EAAO7O,EAAKwP,WAAW9P,KACtD,KAATmP,GAA4B,KAATA,GAAiB,CAElC,GAAa,KAATA,EAEF,OADA/J,GAAS9E,EAAK4B,MAAM0O,EAAY5Q,GACzB,IAAIuO,EAAAA,GAAMjC,EAAU9B,OAAQ5J,EAAOZ,EAAW,EAAGG,EAAMoQ,EAAKZ,EAAMvK,GAI3E,GAAI+J,EAAO,IAAmB,IAATA,EACnB,MAAM5F,EAAYxJ,EAAQC,EAAU,oCAAoCuB,OAAO2N,EAAcC,GAAO,MAKtG,KAFEnP,EAEW,KAATmP,EAAa,CAKf,OAHA/J,GAAS9E,EAAK4B,MAAM0O,EAAY5Q,EAAW,GAC3CmP,EAAO7O,EAAKwP,WAAW9P,IAGrB,KAAK,GACHoF,GAAS,IACT,MAEF,KAAK,GACHA,GAAS,IACT,MAEF,KAAK,GACHA,GAAS,KACT,MAEF,KAAK,GACHA,GAAS,KACT,MAEF,KAAK,IACHA,GAAS,KACT,MAEF,KAAK,IACHA,GAAS,KACT,MAEF,KAAK,IACHA,GAAS,KACT,MAEF,KAAK,IACHA,GAAS,KACT,MAEF,KAAK,IAGD,IAAIyL,GAwFK3S,EAxFkBoC,EAAKwP,WAAW9P,EAAW,GAwF1C7B,EAxF8CmC,EAAKwP,WAAW9P,EAAW,GAwFtE5B,EAxF0EkC,EAAKwP,WAAW9P,EAAW,GAwFlG3B,EAxFsGiC,EAAKwP,WAAW9P,EAAW,GAyFtJ8Q,EAAS5S,IAAM,GAAK4S,EAAS3S,IAAM,EAAI2S,EAAS1S,IAAM,EAAI0S,EAASzS,IAvFhE,GAAIwS,EAAW,EAAG,CAChB,IAAIE,EAAkBzQ,EAAK4B,MAAMlC,EAAW,EAAGA,EAAW,GAC1D,MAAMuJ,EAAYxJ,EAAQC,EAAU,yCAAyCuB,OAAOwP,EAAiB,KACvG,CAEA3L,GAASmK,OAAOC,aAAaqB,GAC7B7Q,GAAY,EACZ,MAGJ,QACE,MAAMuJ,EAAYxJ,EAAQC,EAAU,wCAAwCuB,OAAOgO,OAAOC,aAAaL,GAAO,MAIlHyB,IADE5Q,CAEJ,CACF,CAEA,MAAMuJ,EAAYxJ,EAAQC,EAAU,uBACtC,CAQA,SAASkQ,EAAgBnQ,EAAQa,EAAOT,EAAMoQ,EAAKZ,EAAMD,GAOvD,IANA,IAAIpP,EAAOP,EAAOO,KACdN,EAAWY,EAAQ,EACnBgQ,EAAa5Q,EACbmP,EAAO,EACP6B,EAAW,GAERhR,EAAWM,EAAKE,SAAW4O,MAAMD,EAAO7O,EAAKwP,WAAW9P,KAAY,CAEzE,GAAa,KAATmP,GAAiD,KAAlC7O,EAAKwP,WAAW9P,EAAW,IAA+C,KAAlCM,EAAKwP,WAAW9P,EAAW,GAEpF,OADAgR,GAAY1Q,EAAK4B,MAAM0O,EAAY5Q,GAC5B,IAAIuO,EAAAA,GAAMjC,EAAUiB,aAAc3M,EAAOZ,EAAW,EAAGG,EAAMoQ,EAAKZ,GAAMsB,EAAAA,EAAAA,IAAuBD,IAIxG,GAAI7B,EAAO,IAAmB,IAATA,GAA4B,KAATA,GAA4B,KAATA,EACzD,MAAM5F,EAAYxJ,EAAQC,EAAU,oCAAoCuB,OAAO2N,EAAcC,GAAO,MAGzF,KAATA,KAEAnP,IACA0P,EAAMvP,KACRuP,EAAMhB,UAAY1O,GACA,KAATmP,GAE6B,KAAlC7O,EAAKwP,WAAW9P,EAAW,GAC7BA,GAAY,IAEVA,IAGF0P,EAAMvP,KACRuP,EAAMhB,UAAY1O,GAEX,KAATmP,GAAiD,KAAlC7O,EAAKwP,WAAW9P,EAAW,IAA+C,KAAlCM,EAAKwP,WAAW9P,EAAW,IAA+C,KAAlCM,EAAKwP,WAAW9P,EAAW,IACxHgR,GAAY1Q,EAAK4B,MAAM0O,EAAY5Q,GAAY,MAE/C4Q,EADA5Q,GAAY,KAGVA,CAEN,CAEA,MAAMuJ,EAAYxJ,EAAQC,EAAU,uBACtC,CA0BA,SAAS8Q,EAAS5S,GAChB,OAAOA,GAAK,IAAMA,GAAK,GAAKA,EAAI,GAC9BA,GAAK,IAAMA,GAAK,GAAKA,EAAI,GACzBA,GAAK,IAAMA,GAAK,IAAMA,EAAI,IACzB,CACL,CAQA,SAASmS,EAAStQ,EAAQa,EAAOT,EAAMoQ,EAAKZ,GAM1C,IALA,IAAIrP,EAAOP,EAAOO,KACdsP,EAAatP,EAAKE,OAClBR,EAAWY,EAAQ,EACnBuO,EAAO,EAEJnP,IAAa4P,IAAeR,MAAMD,EAAO7O,EAAKwP,WAAW9P,MAAwB,KAATmP,GAC/EA,GAAQ,IAAMA,GAAQ,IACtBA,GAAQ,IAAMA,GAAQ,IACtBA,GAAQ,IAAMA,GAAQ,QAElBnP,EAGJ,OAAO,IAAIuO,EAAAA,GAAMjC,EAAU3C,KAAM/I,EAAOZ,EAAUG,EAAMoQ,EAAKZ,EAAMrP,EAAK4B,MAAMtB,EAAOZ,GACvF,CC3lBO,IAAIkR,EAAsB,WAC/B,SAASA,EAAOnR,EAAQoR,GACtB,IAAIC,GAAYC,EAAAA,EAAAA,GAAStR,GAAUA,EAAS,IAAIuR,EAAAA,EAAOvR,GACvDmF,KAAKqM,OAAS,IAAIlD,EAAM+C,GACxBlM,KAAKsM,SAAWL,CAClB,CAMA,IAAIxC,EAASuC,EAAOrR,UA26CpB,OAz6CA8O,EAAO8C,UAAY,WACjB,IAAIhD,EAAQvJ,KAAKwM,YAAYpF,EAAU3C,MACvC,MAAO,CACLmF,KAAMrF,EAAKE,KACXvE,MAAOqJ,EAAMrJ,MACb+C,IAAKjD,KAAKiD,IAAIsG,GAElB,EAOAE,EAAOgD,cAAgB,WACrB,IAAI/Q,EAAQsE,KAAKqM,OAAO9C,MACxB,MAAO,CACLK,KAAMrF,EAAKG,SACXgI,YAAa1M,KAAK2M,KAAKvF,EAAUC,IAAKrH,KAAK4M,gBAAiBxF,EAAUE,KACtErE,IAAKjD,KAAKiD,IAAIvH,GAElB,EAaA+N,EAAOmD,gBAAkB,WACvB,GAAI5M,KAAK6M,KAAKzF,EAAU3C,MACtB,OAAQzE,KAAKqM,OAAO9C,MAAMrJ,OACxB,IAAK,QACL,IAAK,WACL,IAAK,eACH,OAAOF,KAAK8M,2BAEd,IAAK,WACH,OAAO9M,KAAK+M,0BAEd,IAAK,SACL,IAAK,SACL,IAAK,OACL,IAAK,YACL,IAAK,QACL,IAAK,OACL,IAAK,QACL,IAAK,YACH,OAAO/M,KAAKgN,4BAEd,IAAK,SACH,OAAOhN,KAAKiN,+BAEX,IAAIjN,KAAK6M,KAAKzF,EAAUc,SAC7B,OAAOlI,KAAK8M,2BACP,GAAI9M,KAAKkN,kBACd,OAAOlN,KAAKgN,2BACd,CAEA,MAAMhN,KAAKmN,YACb,EASA1D,EAAOqD,yBAA2B,WAChC,IAAIpR,EAAQsE,KAAKqM,OAAO9C,MAExB,GAAIvJ,KAAK6M,KAAKzF,EAAUc,SACtB,MAAO,CACL0B,KAAMrF,EAAKI,qBACXyI,UAAW,QACXxT,UAAML,EACN8T,oBAAqB,GACrBC,WAAY,GACZC,aAAcvN,KAAKwN,oBACnBvK,IAAKjD,KAAKiD,IAAIvH,IAIlB,IACI9B,EADAwT,EAAYpN,KAAKyN,qBAOrB,OAJIzN,KAAK6M,KAAKzF,EAAU3C,QACtB7K,EAAOoG,KAAKuM,aAGP,CACL3C,KAAMrF,EAAKI,qBACXyI,UAAWA,EACXxT,KAAMA,EACNyT,oBAAqBrN,KAAK0N,2BAC1BJ,WAAYtN,KAAK2N,iBAAgB,GACjCJ,aAAcvN,KAAKwN,oBACnBvK,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOgE,mBAAqB,WAC1B,IAAIG,EAAiB5N,KAAKwM,YAAYpF,EAAU3C,MAEhD,OAAQmJ,EAAe1N,OACrB,IAAK,QACH,MAAO,QAET,IAAK,WACH,MAAO,WAET,IAAK,eACH,MAAO,eAGX,MAAMF,KAAKmN,WAAWS,EACxB,EAMAnE,EAAOiE,yBAA2B,WAChC,OAAO1N,KAAK6N,aAAazG,EAAUM,QAAS1H,KAAK8N,wBAAyB1G,EAAUO,QACtF,EAMA8B,EAAOqE,wBAA0B,WAC/B,IAAIpS,EAAQsE,KAAKqM,OAAO9C,MACxB,MAAO,CACLK,KAAMrF,EAAKK,oBACXmJ,SAAU/N,KAAKgO,gBACfC,MAAOjO,KAAKwM,YAAYpF,EAAUS,OAAQ7H,KAAKkO,sBAC/CC,aAAcnO,KAAKoO,oBAAoBhH,EAAUU,QAAU9H,KAAKqO,mBAAkB,QAAQ9U,EAC1F+T,WAAYtN,KAAK2N,iBAAgB,GACjC1K,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOuE,cAAgB,WACrB,IAAItS,EAAQsE,KAAKqM,OAAO9C,MAExB,OADAvJ,KAAKwM,YAAYpF,EAAUI,QACpB,CACLoC,KAAMrF,EAAKY,SACXvL,KAAMoG,KAAKuM,YACXtJ,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAO+D,kBAAoB,WACzB,IAAI9R,EAAQsE,KAAKqM,OAAO9C,MACxB,MAAO,CACLK,KAAMrF,EAAKM,cACXyJ,WAAYtO,KAAK2M,KAAKvF,EAAUc,QAASlI,KAAKuO,eAAgBnH,EAAUgB,SACxEnF,IAAKjD,KAAKiD,IAAIvH,GAElB,EASA+N,EAAO8E,eAAiB,WACtB,OAAOvO,KAAK6M,KAAKzF,EAAUQ,QAAU5H,KAAKwO,gBAAkBxO,KAAKyO,YACnE,EAQAhF,EAAOgF,WAAa,WAClB,IAEIC,EACA9U,EAHA8B,EAAQsE,KAAKqM,OAAO9C,MACpBoF,EAAc3O,KAAKuM,YAWvB,OAPIvM,KAAKoO,oBAAoBhH,EAAUS,QACrC6G,EAAQC,EACR/U,EAAOoG,KAAKuM,aAEZ3S,EAAO+U,EAGF,CACL/E,KAAMrF,EAAKO,MACX4J,MAAOA,EACP9U,KAAMA,EACNkG,UAAWE,KAAK4O,gBAAe,GAC/BtB,WAAYtN,KAAK2N,iBAAgB,GACjCJ,aAAcvN,KAAK6M,KAAKzF,EAAUc,SAAWlI,KAAKwN,yBAAsBjU,EACxE0J,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOmF,eAAiB,SAAwBC,GAC9C,IAAIC,EAAOD,EAAU7O,KAAK+O,mBAAqB/O,KAAKgP,cACpD,OAAOhP,KAAK6N,aAAazG,EAAUM,QAASoH,EAAM1H,EAAUO,QAC9D,EAMA8B,EAAOuF,cAAgB,WACrB,IAAItT,EAAQsE,KAAKqM,OAAO9C,MACpB3P,EAAOoG,KAAKuM,YAEhB,OADAvM,KAAKwM,YAAYpF,EAAUS,OACpB,CACL+B,KAAMrF,EAAKQ,SACXnL,KAAMA,EACNsG,MAAOF,KAAKqO,mBAAkB,GAC9BpL,IAAKjD,KAAKiD,IAAIvH,GAElB,EAEA+N,EAAOsF,mBAAqB,WAC1B,IAAIrT,EAAQsE,KAAKqM,OAAO9C,MACxB,MAAO,CACLK,KAAMrF,EAAKQ,SACXnL,KAAMoG,KAAKuM,YACXrM,OAAQF,KAAKwM,YAAYpF,EAAUS,OAAQ7H,KAAKqO,mBAAkB,IAClEpL,IAAKjD,KAAKiD,IAAIvH,GAElB,EAWA+N,EAAO+E,cAAgB,WACrB,IAAI9S,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKwM,YAAYpF,EAAUQ,QAC3B,IAAIqH,EAAmBjP,KAAKkP,sBAAsB,MAElD,OAAKD,GAAoBjP,KAAK6M,KAAKzF,EAAU3C,MACpC,CACLmF,KAAMrF,EAAKS,gBACXpL,KAAMoG,KAAKmP,oBACX7B,WAAYtN,KAAK2N,iBAAgB,GACjC1K,IAAKjD,KAAKiD,IAAIvH,IAIX,CACLkO,KAAMrF,EAAKU,gBACXmK,cAAeH,EAAmBjP,KAAKqP,sBAAmB9V,EAC1D+T,WAAYtN,KAAK2N,iBAAgB,GACjCJ,aAAcvN,KAAKwN,oBACnBvK,IAAKjD,KAAKiD,IAAIvH,GAElB,EASA+N,EAAOsD,wBAA0B,WAC/B,IAAIuC,EAEA5T,EAAQsE,KAAKqM,OAAO9C,MAKxB,OAJAvJ,KAAKuP,cAAc,aAIsH,KAA/F,QAApCD,EAAiBtP,KAAKsM,gBAAyC,IAAnBgD,OAA4B,EAASA,EAAeE,+BAC7F,CACL5F,KAAMrF,EAAKW,oBACXtL,KAAMoG,KAAKmP,oBACX9B,oBAAqBrN,KAAK0N,2BAC1B0B,eAAgBpP,KAAKuP,cAAc,MAAOvP,KAAKqP,kBAC/C/B,WAAYtN,KAAK2N,iBAAgB,GACjCJ,aAAcvN,KAAKwN,oBACnBvK,IAAKjD,KAAKiD,IAAIvH,IAIX,CACLkO,KAAMrF,EAAKW,oBACXtL,KAAMoG,KAAKmP,oBACXC,eAAgBpP,KAAKuP,cAAc,MAAOvP,KAAKqP,kBAC/C/B,WAAYtN,KAAK2N,iBAAgB,GACjCJ,aAAcvN,KAAKwN,oBACnBvK,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAO0F,kBAAoB,WACzB,GAAgC,OAA5BnP,KAAKqM,OAAO9C,MAAMrJ,MACpB,MAAMF,KAAKmN,aAGb,OAAOnN,KAAKuM,WACd,EAsBA9C,EAAO4E,kBAAoB,SAA2BQ,GACpD,IAAItF,EAAQvJ,KAAKqM,OAAO9C,MAExB,OAAQA,EAAMK,MACZ,KAAKxC,EAAUY,UACb,OAAOhI,KAAKyP,UAAUZ,GAExB,KAAKzH,EAAUc,QACb,OAAOlI,KAAK0P,YAAYb,GAE1B,KAAKzH,EAAUhC,IAGb,OAFApF,KAAKqM,OAAO3C,UAEL,CACLE,KAAMrF,EAAKa,IACXlF,MAAOqJ,EAAMrJ,MACb+C,IAAKjD,KAAKiD,IAAIsG,IAGlB,KAAKnC,EAAU/B,MAGb,OAFArF,KAAKqM,OAAO3C,UAEL,CACLE,KAAMrF,EAAKc,MACXnF,MAAOqJ,EAAMrJ,MACb+C,IAAKjD,KAAKiD,IAAIsG,IAGlB,KAAKnC,EAAU9B,OACf,KAAK8B,EAAUiB,aACb,OAAOrI,KAAK2P,qBAEd,KAAKvI,EAAU3C,KAGb,OAFAzE,KAAKqM,OAAO3C,UAEJH,EAAMrJ,OACZ,IAAK,OACH,MAAO,CACL0J,KAAMrF,EAAKgB,QACXrF,OAAO,EACP+C,IAAKjD,KAAKiD,IAAIsG,IAGlB,IAAK,QACH,MAAO,CACLK,KAAMrF,EAAKgB,QACXrF,OAAO,EACP+C,IAAKjD,KAAKiD,IAAIsG,IAGlB,IAAK,OACH,MAAO,CACLK,KAAMrF,EAAKiB,KACXvC,IAAKjD,KAAKiD,IAAIsG,IAGlB,QACE,MAAO,CACLK,KAAMrF,EAAKkB,KACXvF,MAAOqJ,EAAMrJ,MACb+C,IAAKjD,KAAKiD,IAAIsG,IAItB,KAAKnC,EAAUI,OACb,IAAKqH,EACH,OAAO7O,KAAKgO,gBAMlB,MAAMhO,KAAKmN,YACb,EAEA1D,EAAOkG,mBAAqB,WAC1B,IAAIpG,EAAQvJ,KAAKqM,OAAO9C,MAIxB,OAFAvJ,KAAKqM,OAAO3C,UAEL,CACLE,KAAMrF,EAAKe,OACXpF,MAAOqJ,EAAMrJ,MACb0P,MAAOrG,EAAMK,OAASxC,EAAUiB,aAChCpF,IAAKjD,KAAKiD,IAAIsG,GAElB,EAQAE,EAAOgG,UAAY,SAAmBZ,GACpC,IAAIlM,EAAQ3C,KAERtE,EAAQsE,KAAKqM,OAAO9C,MAMxB,MAAO,CACLK,KAAMrF,EAAKmB,KACXmK,OAAQ7P,KAAK8P,IAAI1I,EAAUY,WANlB,WACT,OAAOrF,EAAM0L,kBAAkBQ,EACjC,GAI8CzH,EAAUa,WACtDhF,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAOiG,YAAc,SAAqBb,GACxC,IAAIkB,EAAS/P,KAETtE,EAAQsE,KAAKqM,OAAO9C,MAMxB,MAAO,CACLK,KAAMrF,EAAKoB,OACXqK,OAAQhQ,KAAK8P,IAAI1I,EAAUc,SANlB,WACT,OAAO6H,EAAOE,iBAAiBpB,EACjC,GAI4CzH,EAAUgB,SACpDnF,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOwG,iBAAmB,SAA0BpB,GAClD,IAAInT,EAAQsE,KAAKqM,OAAO9C,MACpB3P,EAAOoG,KAAKuM,YAEhB,OADAvM,KAAKwM,YAAYpF,EAAUS,OACpB,CACL+B,KAAMrF,EAAKqB,aACXhM,KAAMA,EACNsG,MAAOF,KAAKqO,kBAAkBQ,GAC9B5L,IAAKjD,KAAKiD,IAAIvH,GAElB,EAOA+N,EAAOkE,gBAAkB,SAAyBkB,GAGhD,IAFA,IAAIvB,EAAa,GAEVtN,KAAK6M,KAAKzF,EAAUW,KACzBuF,EAAWvQ,KAAKiD,KAAKkQ,eAAerB,IAGtC,OAAOvB,CACT,EAMA7D,EAAOyG,eAAiB,SAAwBrB,GAC9C,IAAInT,EAAQsE,KAAKqM,OAAO9C,MAExB,OADAvJ,KAAKwM,YAAYpF,EAAUW,IACpB,CACL6B,KAAMrF,EAAKsB,UACXjM,KAAMoG,KAAKuM,YACXzM,UAAWE,KAAK4O,eAAeC,GAC/B5L,IAAKjD,KAAKiD,IAAIvH,GAElB,EAUA+N,EAAOyE,mBAAqB,WAC1B,IACID,EADAvS,EAAQsE,KAAKqM,OAAO9C,MAexB,OAZIvJ,KAAKoO,oBAAoBhH,EAAUY,YACrCiG,EAAOjO,KAAKkO,qBACZlO,KAAKwM,YAAYpF,EAAUa,WAC3BgG,EAAO,CACLrE,KAAMrF,EAAKwB,UACXkI,KAAMA,EACNhL,IAAKjD,KAAKiD,IAAIvH,KAGhBuS,EAAOjO,KAAKqP,iBAGVrP,KAAKoO,oBAAoBhH,EAAUG,MAC9B,CACLqC,KAAMrF,EAAKyB,cACXiI,KAAMA,EACNhL,IAAKjD,KAAKiD,IAAIvH,IAIXuS,CACT,EAMAxE,EAAO4F,eAAiB,WACtB,IAAI3T,EAAQsE,KAAKqM,OAAO9C,MACxB,MAAO,CACLK,KAAMrF,EAAKuB,WACXlM,KAAMoG,KAAKuM,YACXtJ,IAAKjD,KAAKiD,IAAIvH,GAElB,EAkBA+N,EAAOuD,0BAA4B,WAEjC,IAAImD,EAAenQ,KAAKkN,kBAAoBlN,KAAKqM,OAAO1C,YAAc3J,KAAKqM,OAAO9C,MAElF,GAAI4G,EAAavG,OAASxC,EAAU3C,KAClC,OAAQ0L,EAAajQ,OACnB,IAAK,SACH,OAAOF,KAAKoQ,wBAEd,IAAK,SACH,OAAOpQ,KAAKqQ,4BAEd,IAAK,OACH,OAAOrQ,KAAKsQ,4BAEd,IAAK,YACH,OAAOtQ,KAAKuQ,+BAEd,IAAK,QACH,OAAOvQ,KAAKwQ,2BAEd,IAAK,OACH,OAAOxQ,KAAKyQ,0BAEd,IAAK,QACH,OAAOzQ,KAAK0Q,iCAEd,IAAK,YACH,OAAO1Q,KAAK2Q,2BAIlB,MAAM3Q,KAAKmN,WAAWgD,EACxB,EAEA1G,EAAOyD,gBAAkB,WACvB,OAAOlN,KAAK6M,KAAKzF,EAAU9B,SAAWtF,KAAK6M,KAAKzF,EAAUiB,aAC5D,EAMAoB,EAAOmH,iBAAmB,WACxB,GAAI5Q,KAAKkN,kBACP,OAAOlN,KAAK2P,oBAEhB,EAMAlG,EAAO2G,sBAAwB,WAC7B,IAAI1U,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,UACnB,IAAIjC,EAAatN,KAAK2N,iBAAgB,GAClCkD,EAAiB7Q,KAAK2M,KAAKvF,EAAUc,QAASlI,KAAK8Q,6BAA8B1J,EAAUgB,SAC/F,MAAO,CACLwB,KAAMrF,EAAK0B,kBACX3B,YAAaA,EACbgJ,WAAYA,EACZuD,eAAgBA,EAChB5N,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOqH,6BAA+B,WACpC,IAAIpV,EAAQsE,KAAKqM,OAAO9C,MACpB6D,EAAYpN,KAAKyN,qBACrBzN,KAAKwM,YAAYpF,EAAUS,OAC3B,IAAIoG,EAAOjO,KAAKqP,iBAChB,MAAO,CACLzF,KAAMrF,EAAK2B,0BACXkH,UAAWA,EACXa,KAAMA,EACNhL,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAO4G,0BAA4B,WACjC,IAAI3U,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,UACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GACtC,MAAO,CACL/D,KAAMrF,EAAK4B,uBACX7B,YAAaA,EACb1K,KAAMA,EACN0T,WAAYA,EACZrK,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAO6G,0BAA4B,WACjC,IAAI5U,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,QACnB,IAAI3V,EAAOoG,KAAKuM,YACZwE,EAAa/Q,KAAKgR,4BAClB1D,EAAatN,KAAK2N,iBAAgB,GAClCqC,EAAShQ,KAAKiR,wBAClB,MAAO,CACLrH,KAAMrF,EAAK6B,uBACX9B,YAAaA,EACb1K,KAAMA,EACNmX,WAAYA,EACZzD,WAAYA,EACZ0C,OAAQA,EACR/M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAOuH,0BAA4B,WACjC,IAAIE,EAEJ,IAAKlR,KAAKkP,sBAAsB,cAC9B,MAAO,GAGT,IAAiJ,KAAtG,QAArCgC,EAAkBlR,KAAKsM,gBAA0C,IAApB4E,OAA6B,EAASA,EAAgBC,oCAA8C,CACrJ,IAAIC,EAAQ,GAEZpR,KAAKoO,oBAAoBhH,EAAUK,KAEnC,GACE2J,EAAMrU,KAAKiD,KAAKqP,wBACTrP,KAAKoO,oBAAoBhH,EAAUK,MAAQzH,KAAK6M,KAAKzF,EAAU3C,OAExE,OAAO2M,CACT,CAEA,OAAOpR,KAAKqR,cAAcjK,EAAUK,IAAKzH,KAAKqP,eAChD,EAMA5F,EAAOwH,sBAAwB,WAC7B,IAAIK,EAGJ,OAAwI,KAA7F,QAArCA,EAAkBtR,KAAKsM,gBAA0C,IAApBgF,OAA6B,EAASA,EAAgBC,4BAAuCvR,KAAK6M,KAAKzF,EAAUc,UAAYlI,KAAKqM,OAAO1C,YAAYC,OAASxC,EAAUgB,SACzNpI,KAAKqM,OAAO3C,UAEZ1J,KAAKqM,OAAO3C,UAEL,IAGF1J,KAAK6N,aAAazG,EAAUc,QAASlI,KAAKwR,qBAAsBpK,EAAUgB,QACnF,EAOAqB,EAAO+H,qBAAuB,WAC5B,IAAI9V,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACnBhX,EAAOoG,KAAKuM,YACZ9S,EAAOuG,KAAKyR,oBAChBzR,KAAKwM,YAAYpF,EAAUS,OAC3B,IAAIoG,EAAOjO,KAAKkO,qBACZZ,EAAatN,KAAK2N,iBAAgB,GACtC,MAAO,CACL/D,KAAMrF,EAAK8B,iBACX/B,YAAaA,EACb1K,KAAMA,EACNkG,UAAWrG,EACXwU,KAAMA,EACNX,WAAYA,EACZrK,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOgI,kBAAoB,WACzB,OAAOzR,KAAK6N,aAAazG,EAAUM,QAAS1H,KAAK0R,mBAAoBtK,EAAUO,QACjF,EAOA8B,EAAOiI,mBAAqB,WAC1B,IAAIhW,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACnBhX,EAAOoG,KAAKuM,YAChBvM,KAAKwM,YAAYpF,EAAUS,OAC3B,IACIsG,EADAF,EAAOjO,KAAKkO,qBAGZlO,KAAKoO,oBAAoBhH,EAAUU,UACrCqG,EAAenO,KAAKqO,mBAAkB,IAGxC,IAAIf,EAAatN,KAAK2N,iBAAgB,GACtC,MAAO,CACL/D,KAAMrF,EAAK+B,uBACXhC,YAAaA,EACb1K,KAAMA,EACNqU,KAAMA,EACNE,aAAcA,EACdb,WAAYA,EACZrK,IAAKjD,KAAKiD,IAAIvH,GAElB,EAOA+N,EAAO8G,6BAA+B,WACpC,IAAI7U,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,aACnB,IAAI3V,EAAOoG,KAAKuM,YACZwE,EAAa/Q,KAAKgR,4BAClB1D,EAAatN,KAAK2N,iBAAgB,GAClCqC,EAAShQ,KAAKiR,wBAClB,MAAO,CACLrH,KAAMrF,EAAKgC,0BACXjC,YAAaA,EACb1K,KAAMA,EACNmX,WAAYA,EACZzD,WAAYA,EACZ0C,OAAQA,EACR/M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAOA+N,EAAO+G,yBAA2B,WAChC,IAAI9U,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,SACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAClCyD,EAAQpR,KAAK2R,wBACjB,MAAO,CACL/H,KAAMrF,EAAKiC,sBACXlC,YAAaA,EACb1K,KAAMA,EACN0T,WAAYA,EACZ8D,MAAOA,EACPnO,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAOkI,sBAAwB,WAC7B,OAAO3R,KAAKoO,oBAAoBhH,EAAUU,QAAU9H,KAAKqR,cAAcjK,EAAUe,KAAMnI,KAAKqP,gBAAkB,EAChH,EAOA5F,EAAOgH,wBAA0B,WAC/B,IAAI/U,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,QACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAClCkC,EAAS7P,KAAK4R,4BAClB,MAAO,CACLhI,KAAMrF,EAAKkC,qBACXnC,YAAaA,EACb1K,KAAMA,EACN0T,WAAYA,EACZuC,OAAQA,EACR5M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOmI,0BAA4B,WACjC,OAAO5R,KAAK6N,aAAazG,EAAUc,QAASlI,KAAK6R,yBAA0BzK,EAAUgB,QACvF,EAQAqB,EAAOoI,yBAA2B,WAChC,IAAInW,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACnBhX,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GACtC,MAAO,CACL/D,KAAMrF,EAAKmC,sBACXpC,YAAaA,EACb1K,KAAMA,EACN0T,WAAYA,EACZrK,IAAKjD,KAAKiD,IAAIvH,GAElB,EAOA+N,EAAOiH,+BAAiC,WACtC,IAAIhV,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,SACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAClCqC,EAAShQ,KAAK8R,6BAClB,MAAO,CACLlI,KAAMrF,EAAKoC,6BACXrC,YAAaA,EACb1K,KAAMA,EACN0T,WAAYA,EACZ0C,OAAQA,EACR/M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAMA+N,EAAOqI,2BAA6B,WAClC,OAAO9R,KAAK6N,aAAazG,EAAUc,QAASlI,KAAK0R,mBAAoBtK,EAAUgB,QACjF,EAgBAqB,EAAOwD,yBAA2B,WAChC,IAAIkD,EAAenQ,KAAKqM,OAAO1C,YAE/B,GAAIwG,EAAavG,OAASxC,EAAU3C,KAClC,OAAQ0L,EAAajQ,OACnB,IAAK,SACH,OAAOF,KAAK+R,uBAEd,IAAK,SACH,OAAO/R,KAAKgS,2BAEd,IAAK,OACH,OAAOhS,KAAKiS,2BAEd,IAAK,YACH,OAAOjS,KAAKkS,8BAEd,IAAK,QACH,OAAOlS,KAAKmS,0BAEd,IAAK,OACH,OAAOnS,KAAKoS,yBAEd,IAAK,QACH,OAAOpS,KAAKqS,gCAIlB,MAAMrS,KAAKmN,WAAWgD,EACxB,EAQA1G,EAAOsI,qBAAuB,WAC5B,IAAIrW,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,UACnB,IAAIjC,EAAatN,KAAK2N,iBAAgB,GAClCkD,EAAiB7Q,KAAK6N,aAAazG,EAAUc,QAASlI,KAAK8Q,6BAA8B1J,EAAUgB,SAEvG,GAA0B,IAAtBkF,EAAWhS,QAA0C,IAA1BuV,EAAevV,OAC5C,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAKsC,iBACXyG,WAAYA,EACZuD,eAAgBA,EAChB5N,IAAKjD,KAAKiD,IAAIvH,GAElB,EAOA+N,EAAOuI,yBAA2B,WAChC,IAAItW,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,UACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAEtC,GAA0B,IAAtBL,EAAWhS,OACb,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAKuC,sBACXlN,KAAMA,EACN0T,WAAYA,EACZrK,IAAKjD,KAAKiD,IAAIvH,GAElB,EASA+N,EAAOwI,yBAA2B,WAChC,IAAIvW,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,QACnB,IAAI3V,EAAOoG,KAAKuM,YACZwE,EAAa/Q,KAAKgR,4BAClB1D,EAAatN,KAAK2N,iBAAgB,GAClCqC,EAAShQ,KAAKiR,wBAElB,GAA0B,IAAtBF,EAAWzV,QAAsC,IAAtBgS,EAAWhS,QAAkC,IAAlB0U,EAAO1U,OAC/D,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAKwC,sBACXnN,KAAMA,EACNmX,WAAYA,EACZzD,WAAYA,EACZ0C,OAAQA,EACR/M,IAAKjD,KAAKiD,IAAIvH,GAElB,EASA+N,EAAOyI,4BAA8B,WACnC,IAAIxW,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,aACnB,IAAI3V,EAAOoG,KAAKuM,YACZwE,EAAa/Q,KAAKgR,4BAClB1D,EAAatN,KAAK2N,iBAAgB,GAClCqC,EAAShQ,KAAKiR,wBAElB,GAA0B,IAAtBF,EAAWzV,QAAsC,IAAtBgS,EAAWhS,QAAkC,IAAlB0U,EAAO1U,OAC/D,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAKyC,yBACXpN,KAAMA,EACNmX,WAAYA,EACZzD,WAAYA,EACZ0C,OAAQA,EACR/M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAO0I,wBAA0B,WAC/B,IAAIzW,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,SACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAClCyD,EAAQpR,KAAK2R,wBAEjB,GAA0B,IAAtBrE,EAAWhS,QAAiC,IAAjB8V,EAAM9V,OACnC,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAK0C,qBACXrN,KAAMA,EACN0T,WAAYA,EACZ8D,MAAOA,EACPnO,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAO2I,uBAAyB,WAC9B,IAAI1W,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,QACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAClCkC,EAAS7P,KAAK4R,4BAElB,GAA0B,IAAtBtE,EAAWhS,QAAkC,IAAlBuU,EAAOvU,OACpC,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAK2C,oBACXtN,KAAMA,EACN0T,WAAYA,EACZuC,OAAQA,EACR5M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAO4I,8BAAgC,WACrC,IAAI3W,EAAQsE,KAAKqM,OAAO9C,MACxBvJ,KAAKuP,cAAc,UACnBvP,KAAKuP,cAAc,SACnB,IAAI3V,EAAOoG,KAAKuM,YACZe,EAAatN,KAAK2N,iBAAgB,GAClCqC,EAAShQ,KAAK8R,6BAElB,GAA0B,IAAtBxE,EAAWhS,QAAkC,IAAlB0U,EAAO1U,OACpC,MAAM0E,KAAKmN,aAGb,MAAO,CACLvD,KAAMrF,EAAK4C,4BACXvN,KAAMA,EACN0T,WAAYA,EACZ0C,OAAQA,EACR/M,IAAKjD,KAAKiD,IAAIvH,GAElB,EAOA+N,EAAOkH,yBAA2B,WAChC,IAAIjV,EAAQsE,KAAKqM,OAAO9C,MACpBjF,EAActE,KAAK4Q,mBACvB5Q,KAAKuP,cAAc,aACnBvP,KAAKwM,YAAYpF,EAAUW,IAC3B,IAAInO,EAAOoG,KAAKuM,YACZ9S,EAAOuG,KAAKyR,oBACZa,EAAatS,KAAKkP,sBAAsB,cAC5ClP,KAAKuP,cAAc,MACnB,IAAI5L,EAAY3D,KAAKuS,0BACrB,MAAO,CACL3I,KAAMrF,EAAKqC,qBACXtC,YAAaA,EACb1K,KAAMA,EACNkG,UAAWrG,EACX6Y,WAAYA,EACZ3O,UAAWA,EACXV,IAAKjD,KAAKiD,IAAIvH,GAElB,EAQA+N,EAAO8I,wBAA0B,WAC/B,OAAOvS,KAAKqR,cAAcjK,EAAUe,KAAMnI,KAAKwS,uBACjD,EA8BA/I,EAAO+I,uBAAyB,WAC9B,IAAI9W,EAAQsE,KAAKqM,OAAO9C,MACpB3P,EAAOoG,KAAKuM,YAEhB,QAAsChT,IAAlCgP,EAAkB3O,EAAKsG,OACzB,OAAOtG,EAGT,MAAMoG,KAAKmN,WAAWzR,EACxB,EAOA+N,EAAOxG,IAAM,SAAawP,GACxB,IAAIC,EAEJ,IAAyH,KAA9E,QAArCA,EAAkB1S,KAAKsM,gBAA0C,IAApBoG,OAA6B,EAASA,EAAgBC,YACvG,OAAO,IAAIC,EAAAA,GAASH,EAAYzS,KAAKqM,OAAO/C,UAAWtJ,KAAKqM,OAAOxR,OAEvE,EAMA4O,EAAOoD,KAAO,SAAcjD,GAC1B,OAAO5J,KAAKqM,OAAO9C,MAAMK,OAASA,CACpC,EAOAH,EAAO+C,YAAc,SAAqB5C,GACxC,IAAIL,EAAQvJ,KAAKqM,OAAO9C,MAExB,GAAIA,EAAMK,OAASA,EAGjB,OAFA5J,KAAKqM,OAAO3C,UAELH,EAGT,MAAMlF,EAAYrE,KAAKqM,OAAOxR,OAAQ0O,EAAM7N,MAAO,YAAYW,OAAOwW,EAAiBjJ,GAAO,YAAYvN,OAAOyW,EAAavJ,GAAQ,KACxI,EAOAE,EAAO2E,oBAAsB,SAA6BxE,GACxD,IAAIL,EAAQvJ,KAAKqM,OAAO9C,MAExB,GAAIA,EAAMK,OAASA,EAGjB,OAFA5J,KAAKqM,OAAO3C,UAELH,CAIX,EAOAE,EAAO8F,cAAgB,SAAuBrP,GAC5C,IAAIqJ,EAAQvJ,KAAKqM,OAAO9C,MAExB,GAAIA,EAAMK,OAASxC,EAAU3C,MAAQ8E,EAAMrJ,QAAUA,EAGnD,MAAMmE,EAAYrE,KAAKqM,OAAOxR,OAAQ0O,EAAM7N,MAAO,aAAcW,OAAO6D,EAAO,aAAc7D,OAAOyW,EAAavJ,GAAQ,MAFzHvJ,KAAKqM,OAAO3C,SAIhB,EAOAD,EAAOyF,sBAAwB,SAA+BhP,GAC5D,IAAIqJ,EAAQvJ,KAAKqM,OAAO9C,MAExB,OAAIA,EAAMK,OAASxC,EAAU3C,MAAQ8E,EAAMrJ,QAAUA,IACnDF,KAAKqM,OAAO3C,WAEL,EAIX,EAMAD,EAAO0D,WAAa,SAAoB4F,GACtC,IAAIxJ,EAAoB,OAAZwJ,QAAgC,IAAZA,EAAqBA,EAAU/S,KAAKqM,OAAO9C,MAC3E,OAAOlF,EAAYrE,KAAKqM,OAAOxR,OAAQ0O,EAAM7N,MAAO,cAAcW,OAAOyW,EAAavJ,GAAQ,KAChG,EAQAE,EAAOqG,IAAM,SAAakD,EAAUC,EAASC,GAC3ClT,KAAKwM,YAAYwG,GAGjB,IAFA,IAAI9Q,EAAQ,IAEJlC,KAAKoO,oBAAoB8E,IAC/BhR,EAAMnF,KAAKkW,EAAQpU,KAAKmB,OAG1B,OAAOkC,CACT,EASAuH,EAAOoE,aAAe,SAAsBmF,EAAUC,EAASC,GAC7D,GAAIlT,KAAKoO,oBAAoB4E,GAAW,CACtC,IAAI9Q,EAAQ,GAEZ,GACEA,EAAMnF,KAAKkW,EAAQpU,KAAKmB,cAChBA,KAAKoO,oBAAoB8E,IAEnC,OAAOhR,CACT,CAEA,MAAO,EACT,EAQAuH,EAAOkD,KAAO,SAAcqG,EAAUC,EAASC,GAC7ClT,KAAKwM,YAAYwG,GACjB,IAAI9Q,EAAQ,GAEZ,GACEA,EAAMnF,KAAKkW,EAAQpU,KAAKmB,cAChBA,KAAKoO,oBAAoB8E,IAEnC,OAAOhR,CACT,EAQAuH,EAAO4H,cAAgB,SAAuB8B,EAAeF,GAC3DjT,KAAKoO,oBAAoB+E,GACzB,IAAIjR,EAAQ,GAEZ,GACEA,EAAMnF,KAAKkW,EAAQpU,KAAKmB,aACjBA,KAAKoO,oBAAoB+E,IAElC,OAAOjR,CACT,EAEO8J,CACT,CAv7CiC,GA47CjC,SAAS8G,EAAavJ,GACpB,IAAIrJ,EAAQqJ,EAAMrJ,MAClB,OAAO2S,EAAiBtJ,EAAMK,OAAkB,MAAT1J,EAAgB,KAAM7D,OAAO6D,EAAO,KAAQ,GACrF,CAMA,SAAS2S,EAAiBjJ,GACxB,OD77CK,SAA+BA,GACpC,OAAOA,IAASxC,EAAUG,MAAQqC,IAASxC,EAAUI,QAAUoC,IAASxC,EAAUK,KAAOmC,IAASxC,EAAUM,SAAWkC,IAASxC,EAAUO,SAAWiC,IAASxC,EAAUQ,QAAUgC,IAASxC,EAAUS,OAAS+B,IAASxC,EAAUU,QAAU8B,IAASxC,EAAUW,IAAM6B,IAASxC,EAAUY,WAAa4B,IAASxC,EAAUa,WAAa2B,IAASxC,EAAUc,SAAW0B,IAASxC,EAAUe,MAAQyB,IAASxC,EAAUgB,OAClZ,CC27CSgL,CAAsBxJ,GAAQ,IAAKvN,OAAOuN,EAAM,KAAQA,CACjE,CCjgDA,IAAMyJ,EAAW,IAAIlU,IAGfmU,EAAoB,IAAInU,IAE1BoU,GAAwB,EACxB/D,GAAgC,EAIpC,SAASgE,EAAUC,GACjB,OAAOA,EAAO9Z,QAAQ,UAAW,KAAK+Z,MACxC,CASA,SAASC,EAAiBC,GACxB,IAAMC,EAAW,IAAIC,IACfpH,EAAgC,GAgCtC,OA9BAkH,EAAIlH,YAAYqH,SAAQ,SAAAC,GACtB,GAAgC,uBAA5BA,EAAmBpK,KAA+B,CACpD,IAAIqK,EAAeD,EAAmBpa,KAAKsG,MACvCgU,EAbDV,GADgBvQ,EAca+Q,EAAmB/Q,KAblCpI,OAAOO,KAAK+Y,UAAUlR,EAAIvH,MAAOuH,EAAI0H,MAgBlDyJ,EAAed,EAAkB5T,IAAIuU,GACrCG,IAAiBA,EAAa3U,IAAIyU,GAGhCX,GACFc,QAAQC,KAAK,+BAAiCL,EAAjC,iMAILG,GACVd,EAAkB3T,IAAIsU,EAAcG,EAAe,IAAIN,KAGzDM,EAAaG,IAAIL,GAEZL,EAASpU,IAAIyU,KAChBL,EAASU,IAAIL,GACbxH,EAAY3P,KAAKiX,G,MAGnBtH,EAAY3P,KAAKiX,GArCvB,IAAyB/Q,CAuCvB,KAEAuR,EAAAA,EAAAA,WAAAA,EAAAA,EAAAA,UAAA,GACKZ,GAAG,CACNlH,YAAWA,GAEf,CAwBA,SAASD,EAAc5R,GACrB,IAAI4Z,EAAWjB,EAAU3Y,GACzB,IAAKwY,EAAS5T,IAAIgV,GAAW,CAC3B,IAAMC,EDhFH,SAAe7Z,EAAQoR,GAE5B,OADa,IAAID,EAAOnR,EAAQoR,GAClBQ,eAChB,CC6EmBkI,CAAM9Z,EAAQ,CAC3B2U,8BAA6BA,EAC7BoF,6BAA8BpF,IAEhC,IAAKkF,GAA0B,aAAhBA,EAAO9K,KACpB,MAAM,IAAIpQ,MAAM,iCAElB6Z,EAAS1T,IACP8U,EAjCN,SAAkBI,GAChB,IAAMC,EAAU,IAAIhB,IAAyBe,EAAInI,aAEjDoI,EAAQf,SAAQ,SAAAzQ,GACVA,EAAKL,YAAYK,EAAKL,IAC1BzE,OAAOuW,KAAKzR,GAAMyQ,SAAQ,SAAArV,GACxB,IAAMwB,EAAQoD,EAAK5E,GACfwB,GAA0B,kBAAVA,GAClB4U,EAAQP,IAAIrU,EAEhB,GACF,IAEA,IAAM+C,EAAM4R,EAAI5R,IAMhB,OALIA,WACKA,EAAIwP,kBACJxP,EAAI+R,UAGNH,CACT,CAgBMI,CAAStB,EAAiBe,I,CAG9B,OAAOrB,EAAS3T,IAAI+U,EACtB,CAGM,SAAUS,EACdC,G,IACA,IAAA1b,EAAA,GAAA2b,EAAA,EAAAA,EAAAtV,UAAAxE,OAAA8Z,IAAA3b,EAAA2b,EAAA,GAAAtV,UAAAsV,GAGwB,kBAAbD,IACTA,EAAW,CAACA,IAGd,IAAItT,EAASsT,EAAS,GAWtB,OATA1b,EAAKsa,SAAQ,SAACsB,EAAKvY,GACbuY,GAAoB,aAAbA,EAAIzL,KACb/H,GAAUwT,EAAIpS,IAAIpI,OAAOO,KAEzByG,GAAUwT,EAEZxT,GAAUsT,EAASrY,EAAI,EACzB,IAEO2P,EAAc5K,EACvB,CAmBA,IAQiByT,EARXC,EACDL,EADCK,EAjBA,WACJlC,EAASmC,QACTlC,EAAkBkC,OACpB,EAcMD,EAZA,WACJhC,GAAwB,CAC1B,EAUMgC,GARA,WACJ/F,GAAgC,CAClC,EAMM+F,GAJA,WACJ/F,GAAgC,CAClC,GAUiB8F,EAAAJ,IAAAA,EAAG,KAEhBA,IAKEK,EAJFD,EAAAG,YAIEF,EAHFD,EAAAI,wBAGEH,EAFFD,EAAAK,oCAEEJ,GADFD,EAAAM,qCACEL,GAGNL,EAAW,QAAGA,C,0ICtKVW,GAA6B,EAmBpBC,EANIC,EAAa,sBAMuC,SACnEC,EACAC,EACAC,GAMA,IAAMhW,EAAQ+V,IAGZ7b,UACCyb,GAGD3V,IAAU+V,MAEVJ,GAA6B,EAE7Bzb,SAAUC,EAAAA,GACRf,MAAA,yEAkBE,IAAA6c,EAAwBJ,EAAAA,SAAe,CAACK,KAAM,CAAClW,MAAKA,EAAE+V,YAAWA,KAA/DG,EAAID,EAAA,GAAAC,KAAGC,EAAWF,EAAA,GAiD1B,OA5CIG,EAAAA,GAKFP,EAAAA,iBAAsB,WACpBvX,OAAO+X,OAAOH,EAAM,CAAElW,MAAKA,EAAE+V,YAAWA,IAKpCO,EAAuBJ,IAEzBC,EAAY,CAACD,KAAIA,GAErB,GAAG,CAACJ,EAAW9V,EAAO+V,IAEtBzX,OAAO+X,OAAOH,EAAM,CAAElW,MAAKA,EAAE+V,YAAWA,IAG1CF,EAAAA,WAAgB,WASd,OANIS,EAAuBJ,IAEzBC,EAAY,CAACD,KAAIA,IAIZJ,GAAU,WAQXQ,EAAuBJ,IAEzBC,EAAY,CAACD,KAAIA,GAErB,GACF,GAAG,CAACJ,IAEG9V,CACT,EAEA,SAASsW,EAAiCL,G,IACxCjW,EAAKiW,EAAAjW,MACL+V,EAAWE,EAAAF,YAKX,IACE,OAAO/V,IAAU+V,G,CACjB,MAAAQ,GACA,OAAO,C,CAEX,C,ICtHYC,E,wDAAZ,SAAYA,GACVA,EAAAA,EAAA,iBACAA,EAAAA,EAAA,uBACAA,EAAAA,EAAA,8BACD,CAJD,CAAYA,IAAAA,EAAY,KAYxB,IAAMC,EAAQ,IAAIxX,IAEZ,SAAUyX,EAAc3I,GAC5B,IAAIrU,EACJ,OAAQqU,GACN,KAAKyI,EAAaG,MAChBjd,EAAO,QACP,MACF,KAAK8c,EAAaI,SAChBld,EAAO,WACP,MACF,KAAK8c,EAAaK,aAChBnd,EAAO,eAGX,OAAOA,CACT,CAwFE,SAAMod,EAA2BC,EAAEhJ,GACnC,IAAMb,EAtFF,SAAiB6J,GACrB,IAGIC,EAAWjJ,EAHTkJ,EAASR,EAAMjX,IAAIuX,GACzB,GAAIE,EAAQ,OAAOA,EAInB/c,SACEC,EAAAA,EAAAA,MAAU4c,KAAeA,EACzBrN,KAAA,eAAevN,OAAQ4a,EAAA,8CAAvB,gHAGA5c,EAAAA,EAAAA,MAAA4c,KAAAA,EAAArN,KAAA,IAOF,IALA,IAAMwN,EAA8B,GAC9BC,EAA4B,GAC5BC,EAA8B,GAC9BC,EAAkC,GAExBnC,EAAA,EAAAe,EAAAc,EAASvK,YAAT0I,EAAAe,EAAA7a,OAAA8Z,IAAsB,CAAjC,IAAMoC,EAACrB,EAAAf,GACV,GAAe,uBAAXoC,EAAE5N,MAKN,GAAe,wBAAX4N,EAAE5N,KACJ,OAAQ4N,EAAEpK,WACR,IAAK,QACHiK,EAAQta,KAAKya,GACb,MACF,IAAK,WACHF,EAAUva,KAAKya,GACf,MACF,IAAK,eACHD,EAAcxa,KAAKya,SAbvBJ,EAAUra,KAAKya,E,CAmBnBpd,SACEC,EAAAA,EAAAA,KAAW+c,EAAM9b,QACd+b,EAAQ/b,QAAUgc,EAAUhc,QAAUic,EAAcjc,OACvD,0HAEAjB,EAAAA,EAAAA,KAAA+c,EAAA9b,QAEF+b,EACE/b,QAAQgc,EAAShc,QAAgBic,EAAGjc,OAAoB,I,SAEtDjB,EAAAA,EAAAA,IAAGgd,EAAQ/b,OAAAgc,EAAehc,OAAOic,EAAAjc,QAAa,8EAC9C,GAAAe,OAAA4a,EAAA,SAAqB5a,OAAAgb,EAAgB/b,OAAA,cAAce,OAAAkb,EAAAjc,OAAA,KACnD,qBAAAe,OAAAib,EAAAhc,OAAA,gBAGA,0EAA+DjB,EAAAA,EAAAA,IAAAgd,EAAA/b,OAAAgc,EAAAhc,OAAAic,EAAAjc,QAAA,MACnE2S,EAAKoJ,EAAQ/b,OAAWob,EAAUG,MAAMH,EAAAI,SAAEO,EAAO/b,QAAagc,EAAAhc,SAExD2S,EAAAyI,EAAcK,c,IAClBrK,EAAS2K,EAAA/b,OACP+b,EAAAC,EACAhc,OACAgc,EAGFC,E,SAEEld,EAAAA,EAAAA,IAAqB,IAAlBqS,EAAkBpR,OAAA,sDAAgBe,OAAA4a,EAAA,SACrC,GAAA5a,OAAAqQ,EAAApR,OAAA,kBAGE,0EAAuDjB,EAAAA,EAAAA,IAAA,IAAAqS,EAAApR,OAAA,IAC7D,IAAAmc,EAAY/K,EAAW,GAEvBwK,EAAIO,EAAmBpK,qBAAoB,GAO3C,IAAAqK,EAAU,CAAA9d,KANR6d,EAAO7d,MAAsB,SAAX6d,EAAW7d,KAAAgQ,KAC9B6N,EAAA7d,KAAAsG,MAEA,OAG2B+N,KAACA,EAAAiJ,UAAAA,GAE9B,OADCP,EAAAhX,IAAOsX,EAAQS,GAChBA,CAED,CAEQC,CAAAV,GACAW,EAAoBhB,EAAc3I,GACxC4J,EACgBjB,EACdxJ,EAAAa,M,SACE5T,EAAAA,EAAAA,IAAG+S,EAAAa,OAAqBA,EAAA,aAAW5R,OAAAub,EAAiB,wBAEzD,GAAAvb,OAAAub,EAAA,YAAAvb,OAAAwb,EAAA,wBAAAxd,EAAAA,EAAAA,IAAA+S,EAAAa,OAAAA,EAAA,G,iDCjGG6J,EAEAtZ,OAAM7D,UAAAmd,eAEJ,SAAUC,EAIdC,EACA/L,GAEA,YAFA,IAAAA,IAAAA,EAA+CzN,OAAOyB,OAAO,OAEtDgY,GACLne,EAAAA,EAAAA,GAAgBmS,EAAQ9R,QACxB6d,GACAD,SAAS9L,EACb,CAEM,SAAUgM,EACd9d,EACA6d,GAEA,IAAME,GAAWC,EAAAA,EAAAA,UAEdD,EAASE,SACVje,IAAW+d,EAASE,QAAQje,QAC5B6d,IAAUE,EAASE,QAAQJ,QAE3BE,EAASE,QAAU,IAAIC,EAAcle,EAAQ6d,EAAOE,EAASE,UAE/D,IAAME,EAAQJ,EAASE,QAQjBjC,GAAmBoC,EAAAA,EAAAA,UAAS,GAApBC,GAAFrC,EAAA,GAASA,EAAA,IAKrB,OAJAmC,EAAMjC,YAAc,WAClBmC,GAAQ,SAAAC,GAAQ,OAAAA,EAAO,CAAP,GAClB,EAEOH,CACT,CAEA,IAAAD,EAAA,WACE,SAAAA,EACkBle,EACA6d,EAChBU,GAFgB,KAAAve,OAAAA,EACA,KAAA6d,MAAAA,EA2BV,KAAAW,gBAAkB,IAAI7E,IAItB,KAAA8E,oBAAsB,IAAKC,EAAAA,GAAgBC,QAAUhF,KAqPrD,KAAAiF,mBAAoBC,EAAAA,EAAAA,GAAgB,CAC1CC,SAAS,EACTC,UAAM,EACN5f,WAAO,EACP6f,cAAeC,EAAAA,EAAcH,UAGvB,KAAAI,mBAAoBL,EAAAA,EAAAA,GAAgB,CAC1CC,SAAS,EACTC,UAAM,EACN5f,WAAO,EACP6f,cAAeC,EAAAA,EAAcE,QA8JvB,KAAAC,mBAAqB,IAAKC,EAAAA,GAAgBC,QAAUta,KA1b1D6X,EAAmBgB,EAAOtB,EAAaG,OAIvC,IAAM6C,EAAiBhB,GAAYA,EAAS7W,OACtC8X,EAAeD,GAAkBA,EAAeR,KAClDS,IACF3Z,KAAK2Z,aAAeA,EAExB,CAueF,OAreEtB,EAAA1d,UAAA0b,YAAA,WAEEjc,SAAUC,EAAAA,GAAKia,KAAA,oEACjB,EAEA+D,EAAA1d,UAAAif,YAAA,eAAAjX,EAAA,KACE,OAAO,IAAIkX,SAAwC,SAAAC,GACjDnX,EAAKgW,gBAAgBpE,IAAIuF,GACzBnX,EAAKiW,oBAAoBrE,IAAI5R,EAAKoX,mBAClCpX,EAAK0T,aACP,GACF,EAaAgC,EAAA1d,UAAAod,SAAA,SAAS9L,GAAT,IAAAtJ,EAAA,KAQE3C,KAAKga,gBAAiB/f,EAAAA,EAAAA,aAAWC,EAAAA,EAAAA,MAAoB8f,eAErDha,KAAKia,WAAWhO,GAEhB,IAAMiO,EAAWla,KAAKma,qBAEhBtY,EAASiU,GACbsE,EAAAA,EAAAA,cAAY,WACV,GAAIzX,EAAKqX,eACP,OAAO,WAAO,EAGhB,IAAMK,EAAS,WACb,IAAMX,EAAiB/W,EAAKd,OAItBA,EAASqY,EAASI,mBAGtBZ,GACAA,EAAeT,UAAYpX,EAAOoX,SAClCS,EAAeP,gBAAkBtX,EAAOsX,gBACxCoB,EAAAA,EAAAA,GAAMb,EAAeR,KAAMrX,EAAOqX,OAKpCvW,EAAK6X,UAAU3Y,EACjB,EAEM4Y,EAAU,SAACnhB,GACf,IAAMohB,EAAOR,EAAe,KAC5BS,EAAaC,cAQb,IACEV,EAASW,mBACTF,EAAeT,EAASlE,UAAUqE,EAAQI,E,CAC1C,QACAP,EAAe,KAAIQ,C,CAGrB,IAAK5C,EAAejZ,KAAKvF,EAAO,iBAE9B,MAAMA,EAGR,IAAMogB,EAAiB/W,EAAKd,SAEzB6X,GACAA,GAAkBA,EAAeT,WACjCsB,EAAAA,EAAAA,GAAMjhB,EAAOogB,EAAepgB,SAE7BqJ,EAAK6X,UAAU,CACbtB,KAAOQ,GAAkBA,EAAeR,KACxC5f,MAAOA,EACP2f,SAAS,EACTE,cAAeC,EAAAA,EAAc9f,OAGnC,EAEIqhB,EAAeT,EAASlE,UAAUqE,EAAQI,GAE9C,OAAO,WAAM,OAAAE,EAAaC,aAAb,CACf,GAAG,CAODV,EACAla,KAAKga,eACLha,KAAK7F,OAAO2gB,yBAGd,WAAM,OAAAnY,EAAK2X,kBAAL,IACN,WAAM,OAAA3X,EAAK2X,kBAAL,IAIRta,KAAK+a,2BAA2BlZ,GAEhC,IAAMmZ,EAAchb,KAAKib,cAAcpZ,GAOvC,OALKmZ,EAAY/B,SAAWjZ,KAAK2Y,gBAAgBuC,OAC/Clb,KAAK2Y,gBAAgB5E,SAAQ,SAAA+F,GAAW,OAAAA,EAAQkB,EAAR,IACxChb,KAAK2Y,gBAAgBnD,SAGhBwF,CACT,EAUQ3C,EAAA1d,UAAAsf,WAAR,SACEhO,G,MAEM8N,EAAoB/Z,KAAKmb,wBAC7Bnb,KAAKob,iBAAmBnP,GAMpBoP,EAA2Brb,KAAK+Z,mBASpC/Z,KAAK4Y,oBAAoBnZ,IAAI4b,KAC5Bd,EAAAA,EAAAA,GAAMR,EAAmBsB,KAE1Brb,KAAK+Z,kBAAoBA,EAErBsB,GAA4Brb,KAAKsb,aAMnCtb,KAAK4Y,oBAAoB2C,OAAOF,GAUhCrb,KAAKsb,WAAWE,UAAUxb,KAAKyb,sBAK/Bzb,KAAK2Z,cAA0B,QAAXxD,EAAAnW,KAAK6B,cAAM,IAAAsU,OAAA,EAAAA,EAAE+C,OAAQlZ,KAAK2Z,aAC9C3Z,KAAK6B,YAAS,IAUlB7B,KAAK0b,YAAczP,EAAQyP,aAAerD,EAAc1d,UAAU+gB,YAClE1b,KAAKya,QAAUxO,EAAQwO,SAAWpC,EAAc1d,UAAU8f,SAGvDza,KAAKga,iBAAkBha,KAAK7F,OAAO2gB,wBACN,IAA9B9a,KAAKob,iBAAiBO,KACrB3b,KAAKob,iBAAiBQ,KAMvB5b,KAAKob,iBAAiBQ,MACiB,YAAvC5b,KAAK+Z,kBAAkB8B,YAYvB7b,KAAK6B,OAAS7B,KAAKqZ,kBAEnBrZ,KAAK6B,SAAW7B,KAAK+Y,mBACrB/Y,KAAK6B,SAAW7B,KAAKqZ,oBAErBrZ,KAAK6B,YAAS,GApBd7B,KAAK6B,OAAS7B,KAAK+Y,iBAsBvB,EAEQV,EAAA1d,UAAA8gB,mBAAR,WACE,IAAMK,EAEF,GAEEC,EAAiB/b,KAAK7F,OAAO6hB,eAAeC,WAsBlD,OArBIF,GAAgBD,EAAQ/e,KAAKgf,GAE7B/b,KAAKob,iBAAiBY,gBACxBF,EAAQ/e,KAAKiD,KAAKob,iBAAiBY,gBAarCF,EAAQ/e,MAAKmf,EAAAA,EAAAA,GACXlc,KAAKsb,YAActb,KAAKsb,WAAWrP,QACnCjM,KAAK+Z,oBAGA+B,EAAQ1Y,OACb+Y,EAAAA,EAEJ,EAiBQ9D,EAAA1d,UAAAwgB,wBAAR,SAAgChF,G,WAAA,IAAAA,IAAAA,EAAA,IAC9B,IAAAyF,EAAIzF,EAAAyF,KASDQ,GARAjG,EAAAwF,IACQxF,EAAAuF,YACJvF,EAAAsE,QACItE,EAAAkG,YACGlG,EAAA6F,gBAICM,EAAAA,EAAAA,QAAAnG,EAVe,wEAcxB4D,EACJvb,OAAO+X,OAAO6F,EAAc,CAAEpE,MAAOhY,KAAKgY,QAkB5C,IAfEhY,KAAKga,gBAE+B,iBAAlCD,EAAkB8B,aACgB,sBAAlC9B,EAAkB8B,cAKpB9B,EAAkB8B,YAAc,eAG7B9B,EAAkB7C,YACrB6C,EAAkB7C,UAAY,CAAC,GAG7B0E,EAAM,CAEN,IAAAW,EAEExC,EAAiB8B,YAFnBA,OAAW,IAAAU,EAAGvc,KAAKwc,wBAAuBD,EAC1CE,EACE1C,EAAiB2C,mBADnBA,OAAkB,IAAAD,EAAGZ,EAAWY,EAMlCje,OAAO+X,OAAOwD,EAAmB,CAC/B2C,mBAAkBA,EAClBb,YAAa,W,MAEL9B,EAAkB8B,cAC5B9B,EAAkB8B,aACD,QAAfpF,EAAAzW,KAAKsb,kBAAU,IAAA7E,OAAA,EAAAA,EAAExK,QAAQyQ,qBACzB1c,KAAKwc,yBAGT,OAAOzC,CACT,EAEA1B,EAAA1d,UAAA6hB,sBAAA,W,QACE,OACsC,QAApCrG,EAAAnW,KAAKob,iBAAiBY,sBAAc,IAAA7F,OAAA,EAAAA,EAAE0F,eACD,QAArCpF,EAAAzW,KAAK7F,OAAO6hB,eAAeC,kBAAU,IAAAxF,OAAA,EAAAA,EAAEoF,cACvC,aAEJ,EAKQxD,EAAA1d,UAAA+gB,YAAR,SAAoBxC,GAAc,EAC1Bb,EAAA1d,UAAA8f,QAAR,SAAgBnhB,GAAqB,EAQ7B+e,EAAA1d,UAAAwf,mBAAR,WAIE,IAAMD,EAAWla,KAAKsb,WACpBtb,KAAKga,gBACAha,KAAKga,eAAe2C,iBAAiB3c,KAAK+Z,oBAC1C/Z,KAAKsb,YACLtb,KAAK7F,OAAO8hB,WAAWjc,KAAKyb,sBAEnCzb,KAAK4c,gBAAiBC,EAAAA,EAAAA,UAAQ,WAAM,MAAC,CACnCC,QAAS5C,EAAS4C,QAAQrc,KAAKyZ,GAC/BsB,UAAWtB,EAASsB,UAAU/a,KAAKyZ,GACnC6C,UAAW7C,EAAS6C,UAAUtc,KAAKyZ,GACnC8C,YAAa9C,EAAS8C,YAAYvc,KAAKyZ,GACvC+C,aAAc/C,EAAS+C,aAAaxc,KAAKyZ,GACzCgD,YAAahD,EAASgD,YAAYzc,KAAKyZ,GACvCiD,gBAAiBjD,EAASiD,gBAAgB1c,KAAKyZ,GAPb,GAQhC,CAACA,IAEL,IAAMkD,KAC0B,IAA9Bpd,KAAKob,iBAAiBO,KACtB3b,KAAKob,iBAAiBQ,MAYxB,OATI5b,KAAKga,gBAAkBoD,IACzBpd,KAAKga,eAAeqD,sBAAsBnD,GAEtCA,EAASI,mBAAmBrB,SAE9BjZ,KAAKga,eAAesD,0BAA0BpD,IAI3CA,CACT,EAOQ7B,EAAA1d,UAAA6f,UAAR,SAAkB+C,GAChB,IAAM7D,EAAiB1Z,KAAK6B,OACxB6X,GAAkBA,EAAeR,OACnClZ,KAAK2Z,aAAeD,EAAeR,MAErClZ,KAAK6B,OAAS0b,EAGdvd,KAAKqW,cACLrW,KAAKwd,uBAAuBD,EAC9B,EAEQlF,EAAA1d,UAAA6iB,uBAAR,SAA+B3b,GACxBA,EAAOoX,UACNpX,EAAOvI,MACT0G,KAAKya,QAAQ5Y,EAAOvI,OACXuI,EAAOqX,MAChBlZ,KAAK0b,YAAY7Z,EAAOqX,MAG9B,EAEQb,EAAA1d,UAAA2f,iBAAR,WASE,OALKta,KAAK6B,QACR7B,KAAKwd,uBACHxd,KAAK6B,OAAS7B,KAAKsb,WAAWhB,oBAG3Bta,KAAK6B,MACd,EAUAwW,EAAA1d,UAAAsgB,cAAA,SACEpZ,GAEA,IAAImZ,EAAchb,KAAKuZ,mBAAmB7Z,IAAImC,GAC9C,GAAImZ,EAAa,OAAOA,EAEhB,IAAA9B,EAA2CrX,EAAMqX,KAA/BuE,GAAyB5b,EAAM6b,SAAXpB,EAAAA,EAAAA,QAAKza,EAA7C,qBAoBN,OAnBA7B,KAAKuZ,mBAAmB5Z,IAAIkC,EAAQmZ,GAAWxG,EAAAA,EAAAA,WAAAA,EAAAA,EAAAA,WAAAA,EAAAA,EAAAA,UAAA,CAC7C0E,KAAIA,GACDuE,GACAzd,KAAK4c,gBAAc,CACtBziB,OAAQ6F,KAAK7F,OACbmhB,WAAYtb,KAAKsb,WACjBpE,UAAWlX,KAAKsb,WAAWpE,UAC3ByG,QAAS3d,KAAKob,iBAAiBQ,KAC/BjC,aAAc3Z,KAAK2Z,iBAGhBqB,EAAY1hB,QAASskB,EAAAA,EAAAA,GAAgB/b,EAAOgc,UAK/C7C,EAAY1hB,MAAQ,IAAIwkB,EAAAA,EAAY,CAAEC,cAAelc,EAAOgc,UAGvD7C,CACT,EAEQ3C,EAAA1d,UAAAogB,2BAAR,SAAmClZ,IAO/BA,EAAO6b,UACP1d,KAAKob,iBAAiB4C,gBACrBnc,EAAOoX,SACNpX,EAAOqX,MAA4C,IAApC1a,OAAOuW,KAAKlT,EAAOqX,MAAM5d,QACF,eAAxC0E,KAAKsb,WAAWrP,QAAQ4P,cAExBrd,OAAO+X,OAAO1U,EAAQ,CACpBoX,SAAS,EACTE,cAAeC,EAAAA,EAAc0D,UAE/B9c,KAAKsb,WAAWwB,UAEpB,EACFzE,CAAA,CAtfA,E", "sources": ["../node_modules/invariant/browser.js", "../node_modules/@apollo/src/react/hooks/useApolloClient.ts", "../node_modules/graphql/jsutils/isObjectLike.mjs", "../node_modules/graphql/language/location.mjs", "../node_modules/graphql/language/printLocation.mjs", "../node_modules/graphql/error/GraphQLError.mjs", "../node_modules/graphql/error/syntaxError.mjs", "../node_modules/graphql/language/kinds.mjs", "../node_modules/graphql/language/tokenKind.mjs", "../node_modules/graphql/language/directiveLocation.mjs", "../node_modules/graphql/language/lexer.mjs", "../node_modules/graphql/language/parser.mjs", "../node_modules/graphql-tag/src/index.ts", "../node_modules/@apollo/src/react/hooks/useSyncExternalStore.ts", "../node_modules/@apollo/src/react/parser/index.ts", "../node_modules/@apollo/src/react/hooks/useQuery.ts"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n", "import { invariant } from '../../utilities/globals';\nimport { useContext } from 'react';\nimport { ApolloClient } from '../../core';\nimport { getApolloContext } from '../context';\n\nexport function useApolloClient(\n  override?: ApolloClient<object>,\n): ApolloClient<object> {\n  const context = useContext(getApolloContext());\n  const client = override || context.client;\n  invariant(\n    !!client,\n    'Could not find \"client\" in the context or passed in as an option. ' +\n    'Wrap the root component in an <ApolloProvider>, or pass an ApolloClient ' +\n    'instance in via options.',\n  );\n\n  return client;\n}\n", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nexport default function isObjectLike(value) {\n  return _typeof(value) == 'object' && value !== null;\n}\n", "/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nexport function getLocation(source, position) {\n  var lineRegexp = /\\r\\n|[\\n\\r]/g;\n  var line = 1;\n  var column = position + 1;\n  var match;\n\n  while ((match = lineRegexp.exec(source.body)) && match.index < position) {\n    line += 1;\n    column = position + 1 - (match.index + match[0].length);\n  }\n\n  return {\n    line: line,\n    column: column\n  };\n}\n", "import { getLocation } from \"./location.mjs\";\n/**\n * Ren<PERSON> a helpful description of the location in the GraphQL Source document.\n */\n\nexport function printLocation(location) {\n  return printSourceLocation(location.source, getLocation(location.source, location.start));\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nexport function printSourceLocation(source, sourceLocation) {\n  var firstLineColumnOffset = source.locationOffset.column - 1;\n  var body = whitespace(firstLineColumnOffset) + source.body;\n  var lineIndex = sourceLocation.line - 1;\n  var lineOffset = source.locationOffset.line - 1;\n  var lineNum = sourceLocation.line + lineOffset;\n  var columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  var columnNum = sourceLocation.column + columnOffset;\n  var locationStr = \"\".concat(source.name, \":\").concat(lineNum, \":\").concat(columnNum, \"\\n\");\n  var lines = body.split(/\\r\\n|[\\n\\r]/g);\n  var locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    var subLineIndex = Math.floor(columnNum / 80);\n    var subLineColumnNum = columnNum % 80;\n    var subLines = [];\n\n    for (var i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return locationStr + printPrefixedLines([[\"\".concat(lineNum), subLines[0]]].concat(subLines.slice(1, subLineIndex + 1).map(function (subLine) {\n      return ['', subLine];\n    }), [[' ', whitespace(subLineColumnNum - 1) + '^'], ['', subLines[subLineIndex + 1]]]));\n  }\n\n  return locationStr + printPrefixedLines([// Lines specified like this: [\"prefix\", \"string\"],\n  [\"\".concat(lineNum - 1), lines[lineIndex - 1]], [\"\".concat(lineNum), locationLine], ['', whitespace(columnNum - 1) + '^'], [\"\".concat(lineNum + 1), lines[lineIndex + 1]]]);\n}\n\nfunction printPrefixedLines(lines) {\n  var existingLines = lines.filter(function (_ref) {\n    var _ = _ref[0],\n        line = _ref[1];\n    return line !== undefined;\n  });\n  var padLen = Math.max.apply(Math, existingLines.map(function (_ref2) {\n    var prefix = _ref2[0];\n    return prefix.length;\n  }));\n  return existingLines.map(function (_ref3) {\n    var prefix = _ref3[0],\n        line = _ref3[1];\n    return leftPad(padLen, prefix) + (line ? ' | ' + line : ' |');\n  }).join('\\n');\n}\n\nfunction whitespace(len) {\n  return Array(len + 1).join(' ');\n}\n\nfunction leftPad(len, str) {\n  return whitespace(len - str.length) + str;\n}\n", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _wrapNativeSuper(Class) { var _cache = typeof Map === \"function\" ? new Map() : undefined; _wrapNativeSuper = function _wrapNativeSuper(Class) { if (Class === null || !_isNativeFunction(Class)) return Class; if (typeof Class !== \"function\") { throw new TypeError(\"Super expression must either be null or a function\"); } if (typeof _cache !== \"undefined\") { if (_cache.has(Class)) return _cache.get(Class); _cache.set(Class, Wrapper); } function Wrapper() { return _construct(Class, arguments, _getPrototypeOf(this).constructor); } Wrapper.prototype = Object.create(Class.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } }); return _setPrototypeOf(Wrapper, Class); }; return _wrapNativeSuper(Class); }\n\nfunction _construct(Parent, args, Class) { if (_isNativeReflectConstruct()) { _construct = Reflect.construct; } else { _construct = function _construct(Parent, args, Class) { var a = [null]; a.push.apply(a, args); var Constructor = Function.bind.apply(Parent, a); var instance = new Constructor(); if (Class) _setPrototypeOf(instance, Class.prototype); return instance; }; } return _construct.apply(null, arguments); }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _isNativeFunction(fn) { return Function.toString.call(fn).indexOf(\"[native code]\") !== -1; }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n// FIXME:\n// flowlint uninitialized-instance-property:off\nimport isObjectLike from \"../jsutils/isObjectLike.mjs\";\nimport { SYMBOL_TO_STRING_TAG } from \"../polyfills/symbols.mjs\";\nimport { getLocation } from \"../language/location.mjs\";\nimport { printLocation, printSourceLocation } from \"../language/printLocation.mjs\";\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nexport var GraphQLError = /*#__PURE__*/function (_Error) {\n  _inherits(GraphQLError, _Error);\n\n  var _super = _createSuper(GraphQLError);\n\n  /**\n   * A message describing the Error for debugging purposes.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   *\n   * Note: should be treated as readonly, despite invariant usage.\n   */\n\n  /**\n   * An array of { line, column } locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n  function GraphQLError(message, nodes, source, positions, path, originalError, extensions) {\n    var _locations2, _source2, _positions2, _extensions2;\n\n    var _this;\n\n    _classCallCheck(this, GraphQLError);\n\n    _this = _super.call(this, message); // Compute list of blame nodes.\n\n    var _nodes = Array.isArray(nodes) ? nodes.length !== 0 ? nodes : undefined : nodes ? [nodes] : undefined; // Compute locations in the source for the given nodes/positions.\n\n\n    var _source = source;\n\n    if (!_source && _nodes) {\n      var _nodes$0$loc;\n\n      _source = (_nodes$0$loc = _nodes[0].loc) === null || _nodes$0$loc === void 0 ? void 0 : _nodes$0$loc.source;\n    }\n\n    var _positions = positions;\n\n    if (!_positions && _nodes) {\n      _positions = _nodes.reduce(function (list, node) {\n        if (node.loc) {\n          list.push(node.loc.start);\n        }\n\n        return list;\n      }, []);\n    }\n\n    if (_positions && _positions.length === 0) {\n      _positions = undefined;\n    }\n\n    var _locations;\n\n    if (positions && source) {\n      _locations = positions.map(function (pos) {\n        return getLocation(source, pos);\n      });\n    } else if (_nodes) {\n      _locations = _nodes.reduce(function (list, node) {\n        if (node.loc) {\n          list.push(getLocation(node.loc.source, node.loc.start));\n        }\n\n        return list;\n      }, []);\n    }\n\n    var _extensions = extensions;\n\n    if (_extensions == null && originalError != null) {\n      var originalExtensions = originalError.extensions;\n\n      if (isObjectLike(originalExtensions)) {\n        _extensions = originalExtensions;\n      }\n    }\n\n    Object.defineProperties(_assertThisInitialized(_this), {\n      name: {\n        value: 'GraphQLError'\n      },\n      message: {\n        value: message,\n        // By being enumerable, JSON.stringify will include `message` in the\n        // resulting output. This ensures that the simplest possible GraphQL\n        // service adheres to the spec.\n        enumerable: true,\n        writable: true\n      },\n      locations: {\n        // Coercing falsy values to undefined ensures they will not be included\n        // in JSON.stringify() when not provided.\n        value: (_locations2 = _locations) !== null && _locations2 !== void 0 ? _locations2 : undefined,\n        // By being enumerable, JSON.stringify will include `locations` in the\n        // resulting output. This ensures that the simplest possible GraphQL\n        // service adheres to the spec.\n        enumerable: _locations != null\n      },\n      path: {\n        // Coercing falsy values to undefined ensures they will not be included\n        // in JSON.stringify() when not provided.\n        value: path !== null && path !== void 0 ? path : undefined,\n        // By being enumerable, JSON.stringify will include `path` in the\n        // resulting output. This ensures that the simplest possible GraphQL\n        // service adheres to the spec.\n        enumerable: path != null\n      },\n      nodes: {\n        value: _nodes !== null && _nodes !== void 0 ? _nodes : undefined\n      },\n      source: {\n        value: (_source2 = _source) !== null && _source2 !== void 0 ? _source2 : undefined\n      },\n      positions: {\n        value: (_positions2 = _positions) !== null && _positions2 !== void 0 ? _positions2 : undefined\n      },\n      originalError: {\n        value: originalError\n      },\n      extensions: {\n        // Coercing falsy values to undefined ensures they will not be included\n        // in JSON.stringify() when not provided.\n        value: (_extensions2 = _extensions) !== null && _extensions2 !== void 0 ? _extensions2 : undefined,\n        // By being enumerable, JSON.stringify will include `path` in the\n        // resulting output. This ensures that the simplest possible GraphQL\n        // service adheres to the spec.\n        enumerable: _extensions != null\n      }\n    }); // Include (non-enumerable) stack trace.\n\n    if (originalError !== null && originalError !== void 0 && originalError.stack) {\n      Object.defineProperty(_assertThisInitialized(_this), 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true\n      });\n      return _possibleConstructorReturn(_this);\n    } // istanbul ignore next (See: 'https://github.com/graphql/graphql-js/issues/2317')\n\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(_assertThisInitialized(_this), GraphQLError);\n    } else {\n      Object.defineProperty(_assertThisInitialized(_this), 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true\n      });\n    }\n\n    return _this;\n  }\n\n  _createClass(GraphQLError, [{\n    key: \"toString\",\n    value: function toString() {\n      return printError(this);\n    } // FIXME: workaround to not break chai comparisons, should be remove in v16\n    // $FlowFixMe[unsupported-syntax] Flow doesn't support computed properties yet\n\n  }, {\n    key: SYMBOL_TO_STRING_TAG,\n    get: function get() {\n      return 'Object';\n    }\n  }]);\n\n  return GraphQLError;\n}( /*#__PURE__*/_wrapNativeSuper(Error));\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n */\n\nexport function printError(error) {\n  var output = error.message;\n\n  if (error.nodes) {\n    for (var _i2 = 0, _error$nodes2 = error.nodes; _i2 < _error$nodes2.length; _i2++) {\n      var node = _error$nodes2[_i2];\n\n      if (node.loc) {\n        output += '\\n\\n' + printLocation(node.loc);\n      }\n    }\n  } else if (error.source && error.locations) {\n    for (var _i4 = 0, _error$locations2 = error.locations; _i4 < _error$locations2.length; _i4++) {\n      var location = _error$locations2[_i4];\n      output += '\\n\\n' + printSourceLocation(error.source, location);\n    }\n  }\n\n  return output;\n}\n", "import { GraphQLError } from \"./GraphQLError.mjs\";\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nexport function syntaxError(source, position, description) {\n  return new GraphQLError(\"Syntax Error: \".concat(description), undefined, source, [position]);\n}\n", "/**\n * The set of allowed kind values for AST nodes.\n */\nexport var Kind = Object.freeze({\n  // Name\n  NAME: 'Name',\n  // Document\n  DOCUMENT: 'Document',\n  OPERATION_DEFINITION: 'OperationDefinition',\n  VARIABLE_DEFINITION: 'VariableDefinition',\n  SELECTION_SET: 'SelectionSet',\n  FIELD: 'Field',\n  ARGUMENT: 'Argument',\n  // Fragments\n  FRAGMENT_SPREAD: 'FragmentSpread',\n  INLINE_FRAGMENT: 'InlineFragment',\n  FRAGMENT_DEFINITION: 'FragmentDefinition',\n  // Values\n  VARIABLE: 'Variable',\n  INT: 'IntValue',\n  FLOAT: 'FloatValue',\n  STRING: 'StringValue',\n  BOOLEAN: 'BooleanValue',\n  NULL: 'NullValue',\n  ENUM: 'EnumValue',\n  LIST: 'ListValue',\n  OBJECT: 'ObjectValue',\n  OBJECT_FIELD: 'ObjectField',\n  // Directives\n  DIRECTIVE: 'Directive',\n  // Types\n  NAMED_TYPE: 'NamedType',\n  LIST_TYPE: 'ListType',\n  NON_NULL_TYPE: 'NonNullType',\n  // Type System Definitions\n  SCHEMA_DEFINITION: 'SchemaDefinition',\n  OPERATION_TYPE_DEFINITION: 'OperationTypeDefinition',\n  // Type Definitions\n  SCALAR_TYPE_DEFINITION: 'ScalarTypeDefinition',\n  OBJECT_TYPE_DEFINITION: 'ObjectTypeDefinition',\n  FIELD_DEFINITION: 'FieldDefinition',\n  INPUT_VALUE_DEFINITION: 'InputValueDefinition',\n  INTERFACE_TYPE_DEFINITION: 'InterfaceTypeDefinition',\n  UNION_TYPE_DEFINITION: 'UnionTypeDefinition',\n  ENUM_TYPE_DEFINITION: 'EnumTypeDefinition',\n  ENUM_VALUE_DEFINITION: 'EnumValueDefinition',\n  INPUT_OBJECT_TYPE_DEFINITION: 'InputObjectTypeDefinition',\n  // Directive Definitions\n  DIRECTIVE_DEFINITION: 'DirectiveDefinition',\n  // Type System Extensions\n  SCHEMA_EXTENSION: 'SchemaExtension',\n  // Type Extensions\n  SCALAR_TYPE_EXTENSION: 'ScalarTypeExtension',\n  OBJECT_TYPE_EXTENSION: 'ObjectTypeExtension',\n  INTERFACE_TYPE_EXTENSION: 'InterfaceTypeExtension',\n  UNION_TYPE_EXTENSION: 'UnionTypeExtension',\n  ENUM_TYPE_EXTENSION: 'EnumTypeExtension',\n  INPUT_OBJECT_TYPE_EXTENSION: 'InputObjectTypeExtension'\n});\n/**\n * The enum type representing the possible kind values of AST nodes.\n */\n", "/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nexport var TokenKind = Object.freeze({\n  SOF: '<SOF>',\n  EOF: '<EOF>',\n  BANG: '!',\n  DOLLAR: '$',\n  AMP: '&',\n  PAREN_L: '(',\n  PAREN_R: ')',\n  SPREAD: '...',\n  COLON: ':',\n  EQUALS: '=',\n  AT: '@',\n  BRACKET_L: '[',\n  BRACKET_R: ']',\n  BRACE_L: '{',\n  PIPE: '|',\n  BRACE_R: '}',\n  NAME: 'Name',\n  INT: 'Int',\n  FLOAT: 'Float',\n  STRING: 'String',\n  BLOCK_STRING: 'BlockString',\n  COMMENT: 'Comment'\n});\n/**\n * The enum type representing the token kinds values.\n */\n", "/**\n * The set of allowed directive location values.\n */\nexport var DirectiveLocation = Object.freeze({\n  // Request Definitions\n  QUERY: 'QUERY',\n  MUTATION: 'MUTATION',\n  SUBSCRIPTION: 'SUBSCRIPTION',\n  FIELD: 'FIELD',\n  FRAGMENT_DEFINITION: 'FRAGMENT_DEFINITION',\n  FRAGMENT_SPREAD: 'FRAGMENT_SPREAD',\n  INLINE_FRAGMENT: 'INLINE_FRAGMENT',\n  VARIABLE_DEFINITION: 'VARIABLE_DEFINITION',\n  // Type System Definitions\n  SCHEMA: 'SCHEMA',\n  SCALAR: 'SCALAR',\n  OBJECT: 'OBJECT',\n  FIELD_DEFINITION: 'FIELD_DEFINITION',\n  ARGUMENT_DEFINITION: 'ARGUMENT_DEFINITION',\n  INTERFACE: 'INTERFACE',\n  UNION: 'UNION',\n  ENUM: 'ENUM',\n  ENUM_VALUE: 'ENUM_VALUE',\n  INPUT_OBJECT: 'INPUT_OBJECT',\n  INPUT_FIELD_DEFINITION: 'INPUT_FIELD_DEFINITION'\n});\n/**\n * The enum type representing the directive location values.\n */\n", "import { syntaxError } from \"../error/syntaxError.mjs\";\nimport { Token } from \"./ast.mjs\";\nimport { TokenKind } from \"./tokenKind.mjs\";\nimport { dedentBlockStringValue } from \"./blockString.mjs\";\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nexport var Lexer = /*#__PURE__*/function () {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  function Lexer(source) {\n    var startOfFileToken = new Token(TokenKind.SOF, 0, 0, 0, 0, null);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n\n  var _proto = Lexer.prototype;\n\n  _proto.advance = function advance() {\n    this.lastToken = this.token;\n    var token = this.token = this.lookahead();\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n  ;\n\n  _proto.lookahead = function lookahead() {\n    var token = this.token;\n\n    if (token.kind !== TokenKind.EOF) {\n      do {\n        var _token$next;\n\n        // Note: next is only mutable during parsing, so we cast to allow this.\n        token = (_token$next = token.next) !== null && _token$next !== void 0 ? _token$next : token.next = readToken(this, token);\n      } while (token.kind === TokenKind.COMMENT);\n    }\n\n    return token;\n  };\n\n  return Lexer;\n}();\n/**\n * @internal\n */\n\nexport function isPunctuatorTokenKind(kind) {\n  return kind === TokenKind.BANG || kind === TokenKind.DOLLAR || kind === TokenKind.AMP || kind === TokenKind.PAREN_L || kind === TokenKind.PAREN_R || kind === TokenKind.SPREAD || kind === TokenKind.COLON || kind === TokenKind.EQUALS || kind === TokenKind.AT || kind === TokenKind.BRACKET_L || kind === TokenKind.BRACKET_R || kind === TokenKind.BRACE_L || kind === TokenKind.PIPE || kind === TokenKind.BRACE_R;\n}\n\nfunction printCharCode(code) {\n  return (// NaN/undefined represents access beyond the end of the file.\n    isNaN(code) ? TokenKind.EOF : // Trust JSON for ASCII.\n    code < 0x007f ? JSON.stringify(String.fromCharCode(code)) : // Otherwise print the escaped form.\n    \"\\\"\\\\u\".concat(('00' + code.toString(16).toUpperCase()).slice(-4), \"\\\"\")\n  );\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\n\nfunction readToken(lexer, prev) {\n  var source = lexer.source;\n  var body = source.body;\n  var bodyLength = body.length;\n  var pos = prev.end;\n\n  while (pos < bodyLength) {\n    var code = body.charCodeAt(pos);\n    var _line = lexer.line;\n\n    var _col = 1 + pos - lexer.lineStart; // SourceCharacter\n\n\n    switch (code) {\n      case 0xfeff: // <BOM>\n\n      case 9: //   \\t\n\n      case 32: //  <space>\n\n      case 44:\n        //  ,\n        ++pos;\n        continue;\n\n      case 10:\n        //  \\n\n        ++pos;\n        ++lexer.line;\n        lexer.lineStart = pos;\n        continue;\n\n      case 13:\n        //  \\r\n        if (body.charCodeAt(pos + 1) === 10) {\n          pos += 2;\n        } else {\n          ++pos;\n        }\n\n        ++lexer.line;\n        lexer.lineStart = pos;\n        continue;\n\n      case 33:\n        //  !\n        return new Token(TokenKind.BANG, pos, pos + 1, _line, _col, prev);\n\n      case 35:\n        //  #\n        return readComment(source, pos, _line, _col, prev);\n\n      case 36:\n        //  $\n        return new Token(TokenKind.DOLLAR, pos, pos + 1, _line, _col, prev);\n\n      case 38:\n        //  &\n        return new Token(TokenKind.AMP, pos, pos + 1, _line, _col, prev);\n\n      case 40:\n        //  (\n        return new Token(TokenKind.PAREN_L, pos, pos + 1, _line, _col, prev);\n\n      case 41:\n        //  )\n        return new Token(TokenKind.PAREN_R, pos, pos + 1, _line, _col, prev);\n\n      case 46:\n        //  .\n        if (body.charCodeAt(pos + 1) === 46 && body.charCodeAt(pos + 2) === 46) {\n          return new Token(TokenKind.SPREAD, pos, pos + 3, _line, _col, prev);\n        }\n\n        break;\n\n      case 58:\n        //  :\n        return new Token(TokenKind.COLON, pos, pos + 1, _line, _col, prev);\n\n      case 61:\n        //  =\n        return new Token(TokenKind.EQUALS, pos, pos + 1, _line, _col, prev);\n\n      case 64:\n        //  @\n        return new Token(TokenKind.AT, pos, pos + 1, _line, _col, prev);\n\n      case 91:\n        //  [\n        return new Token(TokenKind.BRACKET_L, pos, pos + 1, _line, _col, prev);\n\n      case 93:\n        //  ]\n        return new Token(TokenKind.BRACKET_R, pos, pos + 1, _line, _col, prev);\n\n      case 123:\n        // {\n        return new Token(TokenKind.BRACE_L, pos, pos + 1, _line, _col, prev);\n\n      case 124:\n        // |\n        return new Token(TokenKind.PIPE, pos, pos + 1, _line, _col, prev);\n\n      case 125:\n        // }\n        return new Token(TokenKind.BRACE_R, pos, pos + 1, _line, _col, prev);\n\n      case 34:\n        //  \"\n        if (body.charCodeAt(pos + 1) === 34 && body.charCodeAt(pos + 2) === 34) {\n          return readBlockString(source, pos, _line, _col, prev, lexer);\n        }\n\n        return readString(source, pos, _line, _col, prev);\n\n      case 45: //  -\n\n      case 48: //  0\n\n      case 49: //  1\n\n      case 50: //  2\n\n      case 51: //  3\n\n      case 52: //  4\n\n      case 53: //  5\n\n      case 54: //  6\n\n      case 55: //  7\n\n      case 56: //  8\n\n      case 57:\n        //  9\n        return readNumber(source, pos, code, _line, _col, prev);\n\n      case 65: //  A\n\n      case 66: //  B\n\n      case 67: //  C\n\n      case 68: //  D\n\n      case 69: //  E\n\n      case 70: //  F\n\n      case 71: //  G\n\n      case 72: //  H\n\n      case 73: //  I\n\n      case 74: //  J\n\n      case 75: //  K\n\n      case 76: //  L\n\n      case 77: //  M\n\n      case 78: //  N\n\n      case 79: //  O\n\n      case 80: //  P\n\n      case 81: //  Q\n\n      case 82: //  R\n\n      case 83: //  S\n\n      case 84: //  T\n\n      case 85: //  U\n\n      case 86: //  V\n\n      case 87: //  W\n\n      case 88: //  X\n\n      case 89: //  Y\n\n      case 90: //  Z\n\n      case 95: //  _\n\n      case 97: //  a\n\n      case 98: //  b\n\n      case 99: //  c\n\n      case 100: // d\n\n      case 101: // e\n\n      case 102: // f\n\n      case 103: // g\n\n      case 104: // h\n\n      case 105: // i\n\n      case 106: // j\n\n      case 107: // k\n\n      case 108: // l\n\n      case 109: // m\n\n      case 110: // n\n\n      case 111: // o\n\n      case 112: // p\n\n      case 113: // q\n\n      case 114: // r\n\n      case 115: // s\n\n      case 116: // t\n\n      case 117: // u\n\n      case 118: // v\n\n      case 119: // w\n\n      case 120: // x\n\n      case 121: // y\n\n      case 122:\n        // z\n        return readName(source, pos, _line, _col, prev);\n    }\n\n    throw syntaxError(source, pos, unexpectedCharacterMessage(code));\n  }\n\n  var line = lexer.line;\n  var col = 1 + pos - lexer.lineStart;\n  return new Token(TokenKind.EOF, bodyLength, bodyLength, line, col, prev);\n}\n/**\n * Report a message that an unexpected character was encountered.\n */\n\n\nfunction unexpectedCharacterMessage(code) {\n  if (code < 0x0020 && code !== 0x0009 && code !== 0x000a && code !== 0x000d) {\n    return \"Cannot contain the invalid character \".concat(printCharCode(code), \".\");\n  }\n\n  if (code === 39) {\n    // '\n    return 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?';\n  }\n\n  return \"Cannot parse the unexpected character \".concat(printCharCode(code), \".\");\n}\n/**\n * Reads a comment token from the source file.\n *\n * #[\\u0009\\u0020-\\uFFFF]*\n */\n\n\nfunction readComment(source, start, line, col, prev) {\n  var body = source.body;\n  var code;\n  var position = start;\n\n  do {\n    code = body.charCodeAt(++position);\n  } while (!isNaN(code) && ( // SourceCharacter but not LineTerminator\n  code > 0x001f || code === 0x0009));\n\n  return new Token(TokenKind.COMMENT, start, position, line, col, prev, body.slice(start + 1, position));\n}\n/**\n * Reads a number token from the source file, either a float\n * or an int depending on whether a decimal point appears.\n *\n * Int:   -?(0|[1-9][0-9]*)\n * Float: -?(0|[1-9][0-9]*)(\\.[0-9]+)?((E|e)(+|-)?[0-9]+)?\n */\n\n\nfunction readNumber(source, start, firstCode, line, col, prev) {\n  var body = source.body;\n  var code = firstCode;\n  var position = start;\n  var isFloat = false;\n\n  if (code === 45) {\n    // -\n    code = body.charCodeAt(++position);\n  }\n\n  if (code === 48) {\n    // 0\n    code = body.charCodeAt(++position);\n\n    if (code >= 48 && code <= 57) {\n      throw syntaxError(source, position, \"Invalid number, unexpected digit after 0: \".concat(printCharCode(code), \".\"));\n    }\n  } else {\n    position = readDigits(source, position, code);\n    code = body.charCodeAt(position);\n  }\n\n  if (code === 46) {\n    // .\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(source, position, code);\n    code = body.charCodeAt(position);\n  }\n\n  if (code === 69 || code === 101) {\n    // E e\n    isFloat = true;\n    code = body.charCodeAt(++position);\n\n    if (code === 43 || code === 45) {\n      // + -\n      code = body.charCodeAt(++position);\n    }\n\n    position = readDigits(source, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n\n  if (code === 46 || isNameStart(code)) {\n    throw syntaxError(source, position, \"Invalid number, expected digit but got: \".concat(printCharCode(code), \".\"));\n  }\n\n  return new Token(isFloat ? TokenKind.FLOAT : TokenKind.INT, start, position, line, col, prev, body.slice(start, position));\n}\n/**\n * Returns the new position in the source after reading digits.\n */\n\n\nfunction readDigits(source, start, firstCode) {\n  var body = source.body;\n  var position = start;\n  var code = firstCode;\n\n  if (code >= 48 && code <= 57) {\n    // 0 - 9\n    do {\n      code = body.charCodeAt(++position);\n    } while (code >= 48 && code <= 57); // 0 - 9\n\n\n    return position;\n  }\n\n  throw syntaxError(source, position, \"Invalid number, expected digit but got: \".concat(printCharCode(code), \".\"));\n}\n/**\n * Reads a string token from the source file.\n *\n * \"([^\"\\\\\\u000A\\u000D]|(\\\\(u[0-9a-fA-F]{4}|[\"\\\\/bfnrt])))*\"\n */\n\n\nfunction readString(source, start, line, col, prev) {\n  var body = source.body;\n  var position = start + 1;\n  var chunkStart = position;\n  var code = 0;\n  var value = '';\n\n  while (position < body.length && !isNaN(code = body.charCodeAt(position)) && // not LineTerminator\n  code !== 0x000a && code !== 0x000d) {\n    // Closing Quote (\")\n    if (code === 34) {\n      value += body.slice(chunkStart, position);\n      return new Token(TokenKind.STRING, start, position + 1, line, col, prev, value);\n    } // SourceCharacter\n\n\n    if (code < 0x0020 && code !== 0x0009) {\n      throw syntaxError(source, position, \"Invalid character within String: \".concat(printCharCode(code), \".\"));\n    }\n\n    ++position;\n\n    if (code === 92) {\n      // \\\n      value += body.slice(chunkStart, position - 1);\n      code = body.charCodeAt(position);\n\n      switch (code) {\n        case 34:\n          value += '\"';\n          break;\n\n        case 47:\n          value += '/';\n          break;\n\n        case 92:\n          value += '\\\\';\n          break;\n\n        case 98:\n          value += '\\b';\n          break;\n\n        case 102:\n          value += '\\f';\n          break;\n\n        case 110:\n          value += '\\n';\n          break;\n\n        case 114:\n          value += '\\r';\n          break;\n\n        case 116:\n          value += '\\t';\n          break;\n\n        case 117:\n          {\n            // uXXXX\n            var charCode = uniCharCode(body.charCodeAt(position + 1), body.charCodeAt(position + 2), body.charCodeAt(position + 3), body.charCodeAt(position + 4));\n\n            if (charCode < 0) {\n              var invalidSequence = body.slice(position + 1, position + 5);\n              throw syntaxError(source, position, \"Invalid character escape sequence: \\\\u\".concat(invalidSequence, \".\"));\n            }\n\n            value += String.fromCharCode(charCode);\n            position += 4;\n            break;\n          }\n\n        default:\n          throw syntaxError(source, position, \"Invalid character escape sequence: \\\\\".concat(String.fromCharCode(code), \".\"));\n      }\n\n      ++position;\n      chunkStart = position;\n    }\n  }\n\n  throw syntaxError(source, position, 'Unterminated string.');\n}\n/**\n * Reads a block string token from the source file.\n *\n * \"\"\"(\"?\"?(\\\\\"\"\"|\\\\(?!=\"\"\")|[^\"\\\\]))*\"\"\"\n */\n\n\nfunction readBlockString(source, start, line, col, prev, lexer) {\n  var body = source.body;\n  var position = start + 3;\n  var chunkStart = position;\n  var code = 0;\n  var rawValue = '';\n\n  while (position < body.length && !isNaN(code = body.charCodeAt(position))) {\n    // Closing Triple-Quote (\"\"\")\n    if (code === 34 && body.charCodeAt(position + 1) === 34 && body.charCodeAt(position + 2) === 34) {\n      rawValue += body.slice(chunkStart, position);\n      return new Token(TokenKind.BLOCK_STRING, start, position + 3, line, col, prev, dedentBlockStringValue(rawValue));\n    } // SourceCharacter\n\n\n    if (code < 0x0020 && code !== 0x0009 && code !== 0x000a && code !== 0x000d) {\n      throw syntaxError(source, position, \"Invalid character within String: \".concat(printCharCode(code), \".\"));\n    }\n\n    if (code === 10) {\n      // new line\n      ++position;\n      ++lexer.line;\n      lexer.lineStart = position;\n    } else if (code === 13) {\n      // carriage return\n      if (body.charCodeAt(position + 1) === 10) {\n        position += 2;\n      } else {\n        ++position;\n      }\n\n      ++lexer.line;\n      lexer.lineStart = position;\n    } else if ( // Escape Triple-Quote (\\\"\"\")\n    code === 92 && body.charCodeAt(position + 1) === 34 && body.charCodeAt(position + 2) === 34 && body.charCodeAt(position + 3) === 34) {\n      rawValue += body.slice(chunkStart, position) + '\"\"\"';\n      position += 4;\n      chunkStart = position;\n    } else {\n      ++position;\n    }\n  }\n\n  throw syntaxError(source, position, 'Unterminated string.');\n}\n/**\n * Converts four hexadecimal chars to the integer that the\n * string represents. For example, uniCharCode('0','0','0','f')\n * will return 15, and uniCharCode('0','0','f','f') returns 255.\n *\n * Returns a negative number on error, if a char was invalid.\n *\n * This is implemented by noting that char2hex() returns -1 on error,\n * which means the result of ORing the char2hex() will also be negative.\n */\n\n\nfunction uniCharCode(a, b, c, d) {\n  return char2hex(a) << 12 | char2hex(b) << 8 | char2hex(c) << 4 | char2hex(d);\n}\n/**\n * Converts a hex character to its integer value.\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 on error.\n */\n\n\nfunction char2hex(a) {\n  return a >= 48 && a <= 57 ? a - 48 // 0-9\n  : a >= 65 && a <= 70 ? a - 55 // A-F\n  : a >= 97 && a <= 102 ? a - 87 // a-f\n  : -1;\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * [_A-Za-z][_0-9A-Za-z]*\n */\n\n\nfunction readName(source, start, line, col, prev) {\n  var body = source.body;\n  var bodyLength = body.length;\n  var position = start + 1;\n  var code = 0;\n\n  while (position !== bodyLength && !isNaN(code = body.charCodeAt(position)) && (code === 95 || // _\n  code >= 48 && code <= 57 || // 0-9\n  code >= 65 && code <= 90 || // A-Z\n  code >= 97 && code <= 122) // a-z\n  ) {\n    ++position;\n  }\n\n  return new Token(TokenKind.NAME, start, position, line, col, prev, body.slice(start, position));\n} // _ A-Z a-z\n\n\nfunction isNameStart(code) {\n  return code === 95 || code >= 65 && code <= 90 || code >= 97 && code <= 122;\n}\n", "import { syntaxError } from \"../error/syntaxError.mjs\";\nimport { Kind } from \"./kinds.mjs\";\nimport { Location } from \"./ast.mjs\";\nimport { TokenKind } from \"./tokenKind.mjs\";\nimport { Source, isSource } from \"./source.mjs\";\nimport { DirectiveLocation } from \"./directiveLocation.mjs\";\nimport { Lexer, isPunctuatorTokenKind } from \"./lexer.mjs\";\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nexport function parse(source, options) {\n  var parser = new Parser(source, options);\n  return parser.parseDocument();\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nexport function parseValue(source, options) {\n  var parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  var value = parser.parseValueLiteral(false);\n  parser.expectToken(TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nexport function parseType(source, options) {\n  var parser = new Parser(source, options);\n  parser.expectToken(TokenKind.SOF);\n  var type = parser.parseTypeReference();\n  parser.expectToken(TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nexport var Parser = /*#__PURE__*/function () {\n  function Parser(source, options) {\n    var sourceObj = isSource(source) ? source : new Source(source);\n    this._lexer = new Lexer(sourceObj);\n    this._options = options;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n\n  var _proto = Parser.prototype;\n\n  _proto.parseName = function parseName() {\n    var token = this.expectToken(TokenKind.NAME);\n    return {\n      kind: Kind.NAME,\n      value: token.value,\n      loc: this.loc(token)\n    };\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n  ;\n\n  _proto.parseDocument = function parseDocument() {\n    var start = this._lexer.token;\n    return {\n      kind: Kind.DOCUMENT,\n      definitions: this.many(TokenKind.SOF, this.parseDefinition, TokenKind.EOF),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   */\n  ;\n\n  _proto.parseDefinition = function parseDefinition() {\n    if (this.peek(TokenKind.NAME)) {\n      switch (this._lexer.token.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n\n        case 'fragment':\n          return this.parseFragmentDefinition();\n\n        case 'schema':\n        case 'scalar':\n        case 'type':\n        case 'interface':\n        case 'union':\n        case 'enum':\n        case 'input':\n        case 'directive':\n          return this.parseTypeSystemDefinition();\n\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    } else if (this.peek(TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } else if (this.peekDescription()) {\n      return this.parseTypeSystemDefinition();\n    }\n\n    throw this.unexpected();\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n  ;\n\n  _proto.parseOperationDefinition = function parseOperationDefinition() {\n    var start = this._lexer.token;\n\n    if (this.peek(TokenKind.BRACE_L)) {\n      return {\n        kind: Kind.OPERATION_DEFINITION,\n        operation: 'query',\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet(),\n        loc: this.loc(start)\n      };\n    }\n\n    var operation = this.parseOperationType();\n    var name;\n\n    if (this.peek(TokenKind.NAME)) {\n      name = this.parseName();\n    }\n\n    return {\n      kind: Kind.OPERATION_DEFINITION,\n      operation: operation,\n      name: name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n  ;\n\n  _proto.parseOperationType = function parseOperationType() {\n    var operationToken = this.expectToken(TokenKind.NAME);\n\n    switch (operationToken.value) {\n      case 'query':\n        return 'query';\n\n      case 'mutation':\n        return 'mutation';\n\n      case 'subscription':\n        return 'subscription';\n    }\n\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n  ;\n\n  _proto.parseVariableDefinitions = function parseVariableDefinitions() {\n    return this.optionalMany(TokenKind.PAREN_L, this.parseVariableDefinition, TokenKind.PAREN_R);\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n  ;\n\n  _proto.parseVariableDefinition = function parseVariableDefinition() {\n    var start = this._lexer.token;\n    return {\n      kind: Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(TokenKind.EQUALS) ? this.parseValueLiteral(true) : undefined,\n      directives: this.parseDirectives(true),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * Variable : $ Name\n   */\n  ;\n\n  _proto.parseVariable = function parseVariable() {\n    var start = this._lexer.token;\n    this.expectToken(TokenKind.DOLLAR);\n    return {\n      kind: Kind.VARIABLE,\n      name: this.parseName(),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * SelectionSet : { Selection+ }\n   */\n  ;\n\n  _proto.parseSelectionSet = function parseSelectionSet() {\n    var start = this._lexer.token;\n    return {\n      kind: Kind.SELECTION_SET,\n      selections: this.many(TokenKind.BRACE_L, this.parseSelection, TokenKind.BRACE_R),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n  ;\n\n  _proto.parseSelection = function parseSelection() {\n    return this.peek(TokenKind.SPREAD) ? this.parseFragment() : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n  ;\n\n  _proto.parseField = function parseField() {\n    var start = this._lexer.token;\n    var nameOrAlias = this.parseName();\n    var alias;\n    var name;\n\n    if (this.expectOptionalToken(TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n\n    return {\n      kind: Kind.FIELD,\n      alias: alias,\n      name: name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(TokenKind.BRACE_L) ? this.parseSelectionSet() : undefined,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n  ;\n\n  _proto.parseArguments = function parseArguments(isConst) {\n    var item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(TokenKind.PAREN_L, item, TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n  ;\n\n  _proto.parseArgument = function parseArgument() {\n    var start = this._lexer.token;\n    var name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return {\n      kind: Kind.ARGUMENT,\n      name: name,\n      value: this.parseValueLiteral(false),\n      loc: this.loc(start)\n    };\n  };\n\n  _proto.parseConstArgument = function parseConstArgument() {\n    var start = this._lexer.token;\n    return {\n      kind: Kind.ARGUMENT,\n      name: this.parseName(),\n      value: (this.expectToken(TokenKind.COLON), this.parseValueLiteral(true)),\n      loc: this.loc(start)\n    };\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n  ;\n\n  _proto.parseFragment = function parseFragment() {\n    var start = this._lexer.token;\n    this.expectToken(TokenKind.SPREAD);\n    var hasTypeCondition = this.expectOptionalKeyword('on');\n\n    if (!hasTypeCondition && this.peek(TokenKind.NAME)) {\n      return {\n        kind: Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false),\n        loc: this.loc(start)\n      };\n    }\n\n    return {\n      kind: Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n  ;\n\n  _proto.parseFragmentDefinition = function parseFragmentDefinition() {\n    var _this$_options;\n\n    var start = this._lexer.token;\n    this.expectKeyword('fragment'); // Experimental support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (((_this$_options = this._options) === null || _this$_options === void 0 ? void 0 : _this$_options.experimentalFragmentVariables) === true) {\n      return {\n        kind: Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet(),\n        loc: this.loc(start)\n      };\n    }\n\n    return {\n      kind: Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n  ;\n\n  _proto.parseFragmentName = function parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n  ;\n\n  _proto.parseValueLiteral = function parseValueLiteral(isConst) {\n    var token = this._lexer.token;\n\n    switch (token.kind) {\n      case TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n\n      case TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n\n      case TokenKind.INT:\n        this._lexer.advance();\n\n        return {\n          kind: Kind.INT,\n          value: token.value,\n          loc: this.loc(token)\n        };\n\n      case TokenKind.FLOAT:\n        this._lexer.advance();\n\n        return {\n          kind: Kind.FLOAT,\n          value: token.value,\n          loc: this.loc(token)\n        };\n\n      case TokenKind.STRING:\n      case TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n\n      case TokenKind.NAME:\n        this._lexer.advance();\n\n        switch (token.value) {\n          case 'true':\n            return {\n              kind: Kind.BOOLEAN,\n              value: true,\n              loc: this.loc(token)\n            };\n\n          case 'false':\n            return {\n              kind: Kind.BOOLEAN,\n              value: false,\n              loc: this.loc(token)\n            };\n\n          case 'null':\n            return {\n              kind: Kind.NULL,\n              loc: this.loc(token)\n            };\n\n          default:\n            return {\n              kind: Kind.ENUM,\n              value: token.value,\n              loc: this.loc(token)\n            };\n        }\n\n      case TokenKind.DOLLAR:\n        if (!isConst) {\n          return this.parseVariable();\n        }\n\n        break;\n    }\n\n    throw this.unexpected();\n  };\n\n  _proto.parseStringLiteral = function parseStringLiteral() {\n    var token = this._lexer.token;\n\n    this._lexer.advance();\n\n    return {\n      kind: Kind.STRING,\n      value: token.value,\n      block: token.kind === TokenKind.BLOCK_STRING,\n      loc: this.loc(token)\n    };\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n  ;\n\n  _proto.parseList = function parseList(isConst) {\n    var _this = this;\n\n    var start = this._lexer.token;\n\n    var item = function item() {\n      return _this.parseValueLiteral(isConst);\n    };\n\n    return {\n      kind: Kind.LIST,\n      values: this.any(TokenKind.BRACKET_L, item, TokenKind.BRACKET_R),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   */\n  ;\n\n  _proto.parseObject = function parseObject(isConst) {\n    var _this2 = this;\n\n    var start = this._lexer.token;\n\n    var item = function item() {\n      return _this2.parseObjectField(isConst);\n    };\n\n    return {\n      kind: Kind.OBJECT,\n      fields: this.any(TokenKind.BRACE_L, item, TokenKind.BRACE_R),\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n  ;\n\n  _proto.parseObjectField = function parseObjectField(isConst) {\n    var start = this._lexer.token;\n    var name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    return {\n      kind: Kind.OBJECT_FIELD,\n      name: name,\n      value: this.parseValueLiteral(isConst),\n      loc: this.loc(start)\n    };\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n  ;\n\n  _proto.parseDirectives = function parseDirectives(isConst) {\n    var directives = [];\n\n    while (this.peek(TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n\n    return directives;\n  }\n  /**\n   * Directive[Const] : @ Name Arguments[?Const]?\n   */\n  ;\n\n  _proto.parseDirective = function parseDirective(isConst) {\n    var start = this._lexer.token;\n    this.expectToken(TokenKind.AT);\n    return {\n      kind: Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst),\n      loc: this.loc(start)\n    };\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n  ;\n\n  _proto.parseTypeReference = function parseTypeReference() {\n    var start = this._lexer.token;\n    var type;\n\n    if (this.expectOptionalToken(TokenKind.BRACKET_L)) {\n      type = this.parseTypeReference();\n      this.expectToken(TokenKind.BRACKET_R);\n      type = {\n        kind: Kind.LIST_TYPE,\n        type: type,\n        loc: this.loc(start)\n      };\n    } else {\n      type = this.parseNamedType();\n    }\n\n    if (this.expectOptionalToken(TokenKind.BANG)) {\n      return {\n        kind: Kind.NON_NULL_TYPE,\n        type: type,\n        loc: this.loc(start)\n      };\n    }\n\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n  ;\n\n  _proto.parseNamedType = function parseNamedType() {\n    var start = this._lexer.token;\n    return {\n      kind: Kind.NAMED_TYPE,\n      name: this.parseName(),\n      loc: this.loc(start)\n    };\n  } // Implements the parsing rules in the Type Definition section.\n\n  /**\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n  ;\n\n  _proto.parseTypeSystemDefinition = function parseTypeSystemDefinition() {\n    // Many definitions begin with a description and require a lookahead.\n    var keywordToken = this.peekDescription() ? this._lexer.lookahead() : this._lexer.token;\n\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n\n        case 'type':\n          return this.parseObjectTypeDefinition();\n\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n\n        case 'union':\n          return this.parseUnionTypeDefinition();\n\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  };\n\n  _proto.peekDescription = function peekDescription() {\n    return this.peek(TokenKind.STRING) || this.peek(TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n  ;\n\n  _proto.parseDescription = function parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   */\n  ;\n\n  _proto.parseSchemaDefinition = function parseSchemaDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('schema');\n    var directives = this.parseDirectives(true);\n    var operationTypes = this.many(TokenKind.BRACE_L, this.parseOperationTypeDefinition, TokenKind.BRACE_R);\n    return {\n      kind: Kind.SCHEMA_DEFINITION,\n      description: description,\n      directives: directives,\n      operationTypes: operationTypes,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n  ;\n\n  _proto.parseOperationTypeDefinition = function parseOperationTypeDefinition() {\n    var start = this._lexer.token;\n    var operation = this.parseOperationType();\n    this.expectToken(TokenKind.COLON);\n    var type = this.parseNamedType();\n    return {\n      kind: Kind.OPERATION_TYPE_DEFINITION,\n      operation: operation,\n      type: type,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n  ;\n\n  _proto.parseScalarTypeDefinition = function parseScalarTypeDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('scalar');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    return {\n      kind: Kind.SCALAR_TYPE_DEFINITION,\n      description: description,\n      name: name,\n      directives: directives,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n  ;\n\n  _proto.parseObjectTypeDefinition = function parseObjectTypeDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('type');\n    var name = this.parseName();\n    var interfaces = this.parseImplementsInterfaces();\n    var directives = this.parseDirectives(true);\n    var fields = this.parseFieldsDefinition();\n    return {\n      kind: Kind.OBJECT_TYPE_DEFINITION,\n      description: description,\n      name: name,\n      interfaces: interfaces,\n      directives: directives,\n      fields: fields,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n  ;\n\n  _proto.parseImplementsInterfaces = function parseImplementsInterfaces() {\n    var _this$_options2;\n\n    if (!this.expectOptionalKeyword('implements')) {\n      return [];\n    }\n\n    if (((_this$_options2 = this._options) === null || _this$_options2 === void 0 ? void 0 : _this$_options2.allowLegacySDLImplementsInterfaces) === true) {\n      var types = []; // Optional leading ampersand\n\n      this.expectOptionalToken(TokenKind.AMP);\n\n      do {\n        types.push(this.parseNamedType());\n      } while (this.expectOptionalToken(TokenKind.AMP) || this.peek(TokenKind.NAME));\n\n      return types;\n    }\n\n    return this.delimitedMany(TokenKind.AMP, this.parseNamedType);\n  }\n  /**\n   * FieldsDefinition : { FieldDefinition+ }\n   */\n  ;\n\n  _proto.parseFieldsDefinition = function parseFieldsDefinition() {\n    var _this$_options3;\n\n    // Legacy support for the SDL?\n    if (((_this$_options3 = this._options) === null || _this$_options3 === void 0 ? void 0 : _this$_options3.allowLegacySDLEmptyFields) === true && this.peek(TokenKind.BRACE_L) && this._lexer.lookahead().kind === TokenKind.BRACE_R) {\n      this._lexer.advance();\n\n      this._lexer.advance();\n\n      return [];\n    }\n\n    return this.optionalMany(TokenKind.BRACE_L, this.parseFieldDefinition, TokenKind.BRACE_R);\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n  ;\n\n  _proto.parseFieldDefinition = function parseFieldDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    var name = this.parseName();\n    var args = this.parseArgumentDefs();\n    this.expectToken(TokenKind.COLON);\n    var type = this.parseTypeReference();\n    var directives = this.parseDirectives(true);\n    return {\n      kind: Kind.FIELD_DEFINITION,\n      description: description,\n      name: name,\n      arguments: args,\n      type: type,\n      directives: directives,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n  ;\n\n  _proto.parseArgumentDefs = function parseArgumentDefs() {\n    return this.optionalMany(TokenKind.PAREN_L, this.parseInputValueDef, TokenKind.PAREN_R);\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n  ;\n\n  _proto.parseInputValueDef = function parseInputValueDef() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    var name = this.parseName();\n    this.expectToken(TokenKind.COLON);\n    var type = this.parseTypeReference();\n    var defaultValue;\n\n    if (this.expectOptionalToken(TokenKind.EQUALS)) {\n      defaultValue = this.parseValueLiteral(true);\n    }\n\n    var directives = this.parseDirectives(true);\n    return {\n      kind: Kind.INPUT_VALUE_DEFINITION,\n      description: description,\n      name: name,\n      type: type,\n      defaultValue: defaultValue,\n      directives: directives,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n  ;\n\n  _proto.parseInterfaceTypeDefinition = function parseInterfaceTypeDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('interface');\n    var name = this.parseName();\n    var interfaces = this.parseImplementsInterfaces();\n    var directives = this.parseDirectives(true);\n    var fields = this.parseFieldsDefinition();\n    return {\n      kind: Kind.INTERFACE_TYPE_DEFINITION,\n      description: description,\n      name: name,\n      interfaces: interfaces,\n      directives: directives,\n      fields: fields,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n  ;\n\n  _proto.parseUnionTypeDefinition = function parseUnionTypeDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('union');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    var types = this.parseUnionMemberTypes();\n    return {\n      kind: Kind.UNION_TYPE_DEFINITION,\n      description: description,\n      name: name,\n      directives: directives,\n      types: types,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n  ;\n\n  _proto.parseUnionMemberTypes = function parseUnionMemberTypes() {\n    return this.expectOptionalToken(TokenKind.EQUALS) ? this.delimitedMany(TokenKind.PIPE, this.parseNamedType) : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n  ;\n\n  _proto.parseEnumTypeDefinition = function parseEnumTypeDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('enum');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    var values = this.parseEnumValuesDefinition();\n    return {\n      kind: Kind.ENUM_TYPE_DEFINITION,\n      description: description,\n      name: name,\n      directives: directives,\n      values: values,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   */\n  ;\n\n  _proto.parseEnumValuesDefinition = function parseEnumValuesDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseEnumValueDefinition, TokenKind.BRACE_R);\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   *\n   * EnumValue : Name\n   */\n  ;\n\n  _proto.parseEnumValueDefinition = function parseEnumValueDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    return {\n      kind: Kind.ENUM_VALUE_DEFINITION,\n      description: description,\n      name: name,\n      directives: directives,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n  ;\n\n  _proto.parseInputObjectTypeDefinition = function parseInputObjectTypeDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('input');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    var fields = this.parseInputFieldsDefinition();\n    return {\n      kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description: description,\n      name: name,\n      directives: directives,\n      fields: fields,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   */\n  ;\n\n  _proto.parseInputFieldsDefinition = function parseInputFieldsDefinition() {\n    return this.optionalMany(TokenKind.BRACE_L, this.parseInputValueDef, TokenKind.BRACE_R);\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n  ;\n\n  _proto.parseTypeSystemExtension = function parseTypeSystemExtension() {\n    var keywordToken = this._lexer.lookahead();\n\n    if (keywordToken.kind === TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n\n        case 'type':\n          return this.parseObjectTypeExtension();\n\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n\n        case 'union':\n          return this.parseUnionTypeExtension();\n\n        case 'enum':\n          return this.parseEnumTypeExtension();\n\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   */\n  ;\n\n  _proto.parseSchemaExtension = function parseSchemaExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    var directives = this.parseDirectives(true);\n    var operationTypes = this.optionalMany(TokenKind.BRACE_L, this.parseOperationTypeDefinition, TokenKind.BRACE_R);\n\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.SCHEMA_EXTENSION,\n      directives: directives,\n      operationTypes: operationTypes,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n  ;\n\n  _proto.parseScalarTypeExtension = function parseScalarTypeExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.SCALAR_TYPE_EXTENSION,\n      name: name,\n      directives: directives,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n  ;\n\n  _proto.parseObjectTypeExtension = function parseObjectTypeExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    var name = this.parseName();\n    var interfaces = this.parseImplementsInterfaces();\n    var directives = this.parseDirectives(true);\n    var fields = this.parseFieldsDefinition();\n\n    if (interfaces.length === 0 && directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.OBJECT_TYPE_EXTENSION,\n      name: name,\n      interfaces: interfaces,\n      directives: directives,\n      fields: fields,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n  ;\n\n  _proto.parseInterfaceTypeExtension = function parseInterfaceTypeExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    var name = this.parseName();\n    var interfaces = this.parseImplementsInterfaces();\n    var directives = this.parseDirectives(true);\n    var fields = this.parseFieldsDefinition();\n\n    if (interfaces.length === 0 && directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.INTERFACE_TYPE_EXTENSION,\n      name: name,\n      interfaces: interfaces,\n      directives: directives,\n      fields: fields,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n  ;\n\n  _proto.parseUnionTypeExtension = function parseUnionTypeExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    var types = this.parseUnionMemberTypes();\n\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.UNION_TYPE_EXTENSION,\n      name: name,\n      directives: directives,\n      types: types,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n  ;\n\n  _proto.parseEnumTypeExtension = function parseEnumTypeExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    var values = this.parseEnumValuesDefinition();\n\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.ENUM_TYPE_EXTENSION,\n      name: name,\n      directives: directives,\n      values: values,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n  ;\n\n  _proto.parseInputObjectTypeExtension = function parseInputObjectTypeExtension() {\n    var start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    var name = this.parseName();\n    var directives = this.parseDirectives(true);\n    var fields = this.parseInputFieldsDefinition();\n\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return {\n      kind: Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name: name,\n      directives: directives,\n      fields: fields,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   */\n  ;\n\n  _proto.parseDirectiveDefinition = function parseDirectiveDefinition() {\n    var start = this._lexer.token;\n    var description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(TokenKind.AT);\n    var name = this.parseName();\n    var args = this.parseArgumentDefs();\n    var repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    var locations = this.parseDirectiveLocations();\n    return {\n      kind: Kind.DIRECTIVE_DEFINITION,\n      description: description,\n      name: name,\n      arguments: args,\n      repeatable: repeatable,\n      locations: locations,\n      loc: this.loc(start)\n    };\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n  ;\n\n  _proto.parseDirectiveLocations = function parseDirectiveLocations() {\n    return this.delimitedMany(TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n  ;\n\n  _proto.parseDirectiveLocation = function parseDirectiveLocation() {\n    var start = this._lexer.token;\n    var name = this.parseName();\n\n    if (DirectiveLocation[name.value] !== undefined) {\n      return name;\n    }\n\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a location object, used to identify the place in the source that created a given parsed object.\n   */\n  ;\n\n  _proto.loc = function loc(startToken) {\n    var _this$_options4;\n\n    if (((_this$_options4 = this._options) === null || _this$_options4 === void 0 ? void 0 : _this$_options4.noLocation) !== true) {\n      return new Location(startToken, this._lexer.lastToken, this._lexer.source);\n    }\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n  ;\n\n  _proto.peek = function peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n  ;\n\n  _proto.expectToken = function expectToken(kind) {\n    var token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this._lexer.advance();\n\n      return token;\n    }\n\n    throw syntaxError(this._lexer.source, token.start, \"Expected \".concat(getTokenKindDesc(kind), \", found \").concat(getTokenDesc(token), \".\"));\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and return undefined.\n   */\n  ;\n\n  _proto.expectOptionalToken = function expectOptionalToken(kind) {\n    var token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this._lexer.advance();\n\n      return token;\n    }\n\n    return undefined;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n  ;\n\n  _proto.expectKeyword = function expectKeyword(value) {\n    var token = this._lexer.token;\n\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this._lexer.advance();\n    } else {\n      throw syntaxError(this._lexer.source, token.start, \"Expected \\\"\".concat(value, \"\\\", found \").concat(getTokenDesc(token), \".\"));\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n  ;\n\n  _proto.expectOptionalKeyword = function expectOptionalKeyword(value) {\n    var token = this._lexer.token;\n\n    if (token.kind === TokenKind.NAME && token.value === value) {\n      this._lexer.advance();\n\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n  ;\n\n  _proto.unexpected = function unexpected(atToken) {\n    var token = atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return syntaxError(this._lexer.source, token.start, \"Unexpected \".concat(getTokenDesc(token), \".\"));\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n  ;\n\n  _proto.any = function any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    var nodes = [];\n\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n  ;\n\n  _proto.optionalMany = function optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      var nodes = [];\n\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n\n      return nodes;\n    }\n\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n  ;\n\n  _proto.many = function many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    var nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n  ;\n\n  _proto.delimitedMany = function delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    var nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n\n    return nodes;\n  };\n\n  return Parser;\n}();\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  var value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? \" \\\"\".concat(value, \"\\\"\") : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\n\nfunction getTokenKindDesc(kind) {\n  return isPunctuatorTokenKind(kind) ? \"\\\"\".concat(kind, \"\\\"\") : kind;\n}\n", "import { parse } from 'graphql';\n\nimport {\n  DocumentNode,\n  DefinitionNode,\n  Location,\n} from 'graphql/language/ast';\n\n// A map docString -> graphql document\nconst docCache = new Map<string, DocumentNode>();\n\n// A map fragmentName -> [normalized source]\nconst fragmentSourceMap = new Map<string, Set<string>>();\n\nlet printFragmentWarnings = true;\nlet experimentalFragmentVariables = false;\n\n// Strip insignificant whitespace\n// Note that this could do a lot more, such as reorder fields etc.\nfunction normalize(string: string) {\n  return string.replace(/[\\s,]+/g, ' ').trim();\n}\n\nfunction cacheKeyFromLoc(loc: Location) {\n  return normalize(loc.source.body.substring(loc.start, loc.end));\n}\n\n// Take a unstripped parsed document (query/mutation or even fragment), and\n// check all fragment definitions, checking for name->source uniqueness.\n// We also want to make sure only unique fragments exist in the document.\nfunction processFragments(ast: DocumentNode) {\n  const seenKeys = new Set<string>();\n  const definitions: DefinitionNode[] = [];\n\n  ast.definitions.forEach(fragmentDefinition => {\n    if (fragmentDefinition.kind === 'FragmentDefinition') {\n      var fragmentName = fragmentDefinition.name.value;\n      var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc!);\n\n      // We know something about this fragment\n      let sourceKeySet = fragmentSourceMap.get(fragmentName)!;\n      if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n        // this is a problem because the app developer is trying to register another fragment with\n        // the same name as one previously registered. So, we tell them about it.\n        if (printFragmentWarnings) {\n          console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n            + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n            + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n        }\n      } else if (!sourceKeySet) {\n        fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n      }\n\n      sourceKeySet.add(sourceKey);\n\n      if (!seenKeys.has(sourceKey)) {\n        seenKeys.add(sourceKey);\n        definitions.push(fragmentDefinition);\n      }\n    } else {\n      definitions.push(fragmentDefinition);\n    }\n  });\n\n  return {\n    ...ast,\n    definitions,\n  };\n}\n\nfunction stripLoc(doc: DocumentNode) {\n  const workSet = new Set<Record<string, any>>(doc.definitions);\n\n  workSet.forEach(node => {\n    if (node.loc) delete node.loc;\n    Object.keys(node).forEach(key => {\n      const value = node[key];\n      if (value && typeof value === 'object') {\n        workSet.add(value);\n      }\n    });\n  });\n\n  const loc = doc.loc as Record<string, any>;\n  if (loc) {\n    delete loc.startToken;\n    delete loc.endToken;\n  }\n\n  return doc;\n}\n\nfunction parseDocument(source: string) {\n  var cacheKey = normalize(source);\n  if (!docCache.has(cacheKey)) {\n    const parsed = parse(source, {\n      experimentalFragmentVariables,\n      allowLegacyFragmentVariables: experimentalFragmentVariables,\n    } as any);\n    if (!parsed || parsed.kind !== 'Document') {\n      throw new Error('Not a valid GraphQL document.');\n    }\n    docCache.set(\n      cacheKey,\n      // check that all \"new\" fragments inside the documents are consistent with\n      // existing fragments of the same name\n      stripLoc(processFragments(parsed)),\n    );\n  }\n  return docCache.get(cacheKey)!;\n}\n\n// XXX This should eventually disallow arbitrary string interpolation, like Relay does\nexport function gql(\n  literals: string | readonly string[],\n  ...args: any[]\n) {\n\n  if (typeof literals === 'string') {\n    literals = [literals];\n  }\n\n  let result = literals[0];\n\n  args.forEach((arg, i) => {\n    if (arg && arg.kind === 'Document') {\n      result += arg.loc.source.body;\n    } else {\n      result += arg;\n    }\n    result += literals[i + 1];\n  });\n\n  return parseDocument(result);\n}\n\nexport function resetCaches() {\n  docCache.clear();\n  fragmentSourceMap.clear();\n}\n\nexport function disableFragmentWarnings() {\n  printFragmentWarnings = false;\n}\n\nexport function enableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = true;\n}\n\nexport function disableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = false;\n}\n\nconst extras = {\n  gql,\n  resetCaches,\n  disableFragmentWarnings,\n  enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables,\n};\n\nexport namespace gql {\n  export const {\n    gql,\n    resetCaches,\n    disableFragmentWarnings,\n    enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables,\n  } = extras;\n}\n\ngql.default = gql;\n\nexport default gql;\n", "import { invariant } from '../../utilities/globals';\nimport * as React from 'react';\n\nimport { canUseLayoutEffect } from '../../utilities';\n\nlet didWarnUncachedGetSnapshot = false;\n\ntype RealUseSESHookType =\n  // This import depends only on the @types/use-sync-external-store package, not\n  // the actual use-sync-external-store package, which is not installed. It\n  // might be nice to get this type from React 18, but it still needs to work\n  // when only React 17 or earlier is installed.\n  typeof import(\"use-sync-external-store\").useSyncExternalStore;\n\n// Prevent webpack from complaining about our feature detection of the\n// useSyncExternalStore property of the React namespace, which is expected not\n// to exist when using React 17 and earlier, and that's fine.\nconst uSESKey = \"useSyncExternalStore\" as keyof typeof React;\nconst realHook = React[uSESKey] as RealUseSESHookType | undefined;\n\n// Adapted from https://www.npmjs.com/package/use-sync-external-store, with\n// Apollo Client deviations called out by \"// DEVIATION ...\" comments.\n\n// When/if React.useSyncExternalStore is defined, delegate fully to it.\nexport const useSyncExternalStore: RealUseSESHookType = realHook || ((\n  subscribe,\n  getSnapshot,\n  getServerSnapshot,\n) => {\n  // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n  const value = getSnapshot();\n  if (\n    // DEVIATION: Using our own __DEV__ polyfill (from ../../utilities/globals).\n    __DEV__ &&\n    !didWarnUncachedGetSnapshot &&\n    // DEVIATION: Not using Object.is because we know our snapshots will never\n    // be exotic primitive values like NaN, which is !== itself.\n    value !== getSnapshot()\n  ) {\n    didWarnUncachedGetSnapshot = true;\n    // DEVIATION: Using invariant.error instead of console.error directly.\n    invariant.error(\n      'The result of getSnapshot should be cached to avoid an infinite loop',\n    );\n  }\n\n  // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n  const [{inst}, forceUpdate] = React.useState({inst: {value, getSnapshot}});\n\n  // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n  if (canUseLayoutEffect) {\n    // DEVIATION: We avoid calling useLayoutEffect when !canUseLayoutEffect,\n    // which may seem like a conditional hook, but this code ends up behaving\n    // unconditionally (one way or the other) because canUseLayoutEffect is\n    // constant.\n    React.useLayoutEffect(() => {\n      Object.assign(inst, { value, getSnapshot });\n      // Whenever getSnapshot or subscribe changes, we need to check in the\n      // commit phase if there was an interleaved mutation. In concurrent mode\n      // this can happen all the time, but even in synchronous mode, an earlier\n      // effect may have mutated the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({inst});\n      }\n    }, [subscribe, value, getSnapshot]);\n  } else {\n    Object.assign(inst, { value, getSnapshot });\n  }\n\n  React.useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({inst});\n    }\n\n    // Subscribe to the store and return a clean-up function.\n    return subscribe(function handleStoreChange() {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({inst});\n      }\n    });\n  }, [subscribe]);\n\n  return value;\n});\n\nfunction checkIfSnapshotChanged<Snapshot>({\n  value,\n  getSnapshot,\n}: {\n  value: Snapshot;\n  getSnapshot: () => Snapshot;\n}): boolean {\n  try {\n    return value !== getSnapshot();\n  } catch {\n    return true;\n  }\n}\n", "import { invariant } from '../../utilities/globals';\n\nimport {\n  DocumentNode,\n  DefinitionNode,\n  VariableDefinitionNode,\n  OperationDefinitionNode\n} from 'graphql';\n\nexport enum DocumentType {\n  Query,\n  Mutation,\n  Subscription\n}\n\nexport interface IDocumentDefinition {\n  type: DocumentType;\n  name: string;\n  variables: ReadonlyArray<VariableDefinitionNode>;\n}\n\nconst cache = new Map();\n\nexport function operationName(type: DocumentType) {\n  let name;\n  switch (type) {\n    case DocumentType.Query:\n      name = 'Query';\n      break;\n    case DocumentType.Mutation:\n      name = 'Mutation';\n      break;\n    case DocumentType.Subscription:\n      name = 'Subscription';\n      break;\n  }\n  return name;\n}\n\n// This parser is mostly used to safety check incoming documents.\nexport function parser(document: DocumentNode): IDocumentDefinition {\n  const cached = cache.get(document);\n  if (cached) return cached;\n\n  let variables, type, name;\n\n  invariant(\n    !!document && !!document.kind,\n    `Argument of ${document} passed to parser was not a valid GraphQL ` +\n      `DocumentNode. You may need to use 'graphql-tag' or another method ` +\n      `to convert your operation into a document`\n  );\n\n  const fragments: DefinitionNode[] = []\n  const queries: DefinitionNode[] = []\n  const mutations: DefinitionNode[] = []\n  const subscriptions: DefinitionNode[] = []\n\n  for (const x of document.definitions) {\n    if (x.kind === 'FragmentDefinition') {\n      fragments.push(x);\n      continue\n    }\n\n    if (x.kind === 'OperationDefinition') {\n      switch (x.operation) {\n        case 'query':\n          queries.push(x);\n          break;\n        case 'mutation':\n          mutations.push(x);\n          break;\n        case 'subscription':\n          subscriptions.push(x);\n          break;\n      }\n    }\n  }\n\n  invariant(\n    !fragments.length ||\n      (queries.length || mutations.length || subscriptions.length),\n    `Passing only a fragment to 'graphql' is not yet supported. ` +\n      `You must include a query, subscription or mutation as well`\n  );\n\n  invariant(\n    queries.length + mutations.length + subscriptions.length <= 1,\n    `react-apollo only supports a query, subscription, or a mutation per HOC. ` +\n      `${document} had ${queries.length} queries, ${subscriptions.length} ` +\n      `subscriptions and ${mutations.length} mutations. ` +\n      `You can use 'compose' to join multiple operation types to a component`\n  );\n\n  type = queries.length ? DocumentType.Query : DocumentType.Mutation;\n  if (!queries.length && !mutations.length) type = DocumentType.Subscription;\n\n  const definitions = queries.length\n    ? queries\n    : mutations.length\n    ? mutations\n    : subscriptions;\n\n  invariant(\n    definitions.length === 1,\n    `react-apollo only supports one definition per HOC. ${document} had ` +\n      `${definitions.length} definitions. ` +\n      `You can use 'compose' to join multiple operation types to a component`\n  );\n\n  const definition = definitions[0] as OperationDefinitionNode;\n  variables = definition.variableDefinitions || [];\n\n  if (definition.name && definition.name.kind === 'Name') {\n    name = definition.name.value;\n  } else {\n    name = 'data'; // fallback to using data if no name\n  }\n\n  const payload = { name, type, variables };\n  cache.set(document, payload);\n  return payload;\n}\n\nexport function verifyDocumentType(document: DocumentNode, type: DocumentType) {\n  const operation = parser(document);\n  const requiredOperationName = operationName(type);\n  const usedOperationName = operationName(operation.type);\n  invariant(\n    operation.type === type,\n    `Running a ${requiredOperationName} requires a graphql ` +\n      `${requiredOperationName}, but a ${usedOperationName} was used instead.`\n  );\n}\n\n", "import { invariant } from '../../utilities/globals';\n\nimport {\n  useCallback,\n  useContext,\n  useMemo,\n  useRef,\n  useState,\n} from 'react';\nimport { useSyncExternalStore } from './useSyncExternalStore';\nimport { equal } from '@wry/equality';\n\nimport { mergeOptions, OperationVariables, WatchQueryFetchPolicy } from '../../core';\nimport { ApolloContextValue, getApolloContext } from '../context';\nimport { ApolloError } from '../../errors';\nimport {\n  ApolloClient,\n  ApolloQueryResult,\n  NetworkStatus,\n  ObservableQuery,\n  DocumentNode,\n  TypedDocumentNode,\n  WatchQueryOptions,\n} from '../../core';\nimport {\n  QueryHookOptions,\n  QueryResult,\n  ObservableQueryFields,\n} from '../types/types';\n\nimport { DocumentType, verifyDocumentType } from '../parser';\nimport { useApolloClient } from './useApolloClient';\nimport { canUseWeakMap, canUseWeakSet, compact, isNonEmptyArray, maybeDeepFreeze } from '../../utilities';\n\nconst {\n  prototype: {\n    hasOwnProperty,\n  },\n} = Object;\n\nexport function useQuery<\n  TData = any,\n  TVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<TData, TVariables> = Object.create(null),\n): QueryResult<TData, TVariables> {\n  return useInternalState(\n    useApolloClient(options.client),\n    query,\n  ).useQuery(options);\n}\n\nexport function useInternalState<TData, TVariables>(\n  client: ApolloClient<any>,\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n): InternalState<TData, TVariables> {\n  const stateRef = useRef<InternalState<TData, TVariables>>();\n  if (\n    !stateRef.current ||\n    client !== stateRef.current.client ||\n    query !== stateRef.current.query\n  ) {\n    stateRef.current = new InternalState(client, query, stateRef.current);\n  }\n  const state = stateRef.current;\n\n  // By default, InternalState.prototype.forceUpdate is an empty function, but\n  // we replace it here (before anyone has had a chance to see this state yet)\n  // with a function that unconditionally forces an update, using the latest\n  // setTick function. Updating this state by calling state.forceUpdate is the\n  // only way we trigger React component updates (no other useState calls within\n  // the InternalState class).\n  const [_tick, setTick] = useState(0);\n  state.forceUpdate = () => {\n    setTick(tick => tick + 1);\n  };\n\n  return state;\n}\n\nclass InternalState<TData, TVariables> {\n  constructor(\n    public readonly client: ReturnType<typeof useApolloClient>,\n    public readonly query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n    previous?: InternalState<TData, TVariables>,\n  ) {\n    verifyDocumentType(query, DocumentType.Query);\n\n    // Reuse previousData from previous InternalState (if any) to provide\n    // continuity of previousData even if/when the query or client changes.\n    const previousResult = previous && previous.result;\n    const previousData = previousResult && previousResult.data;\n    if (previousData) {\n      this.previousData = previousData;\n    }\n  }\n\n  forceUpdate() {\n    // Replaced (in useInternalState) with a method that triggers an update.\n    invariant.warn(\"Calling default no-op implementation of InternalState#forceUpdate\");\n  }\n\n  asyncUpdate() {\n    return new Promise<QueryResult<TData, TVariables>>(resolve => {\n      this.asyncResolveFns.add(resolve);\n      this.optionsToIgnoreOnce.add(this.watchQueryOptions);\n      this.forceUpdate();\n    });\n  }\n\n  private asyncResolveFns = new Set<\n    (result: QueryResult<TData, TVariables>) => void\n  >();\n\n  private optionsToIgnoreOnce = new (canUseWeakSet ? WeakSet : Set)<\n    WatchQueryOptions<TVariables, TData>\n  >();\n\n  // Methods beginning with use- should be called according to the standard\n  // rules of React hooks: only at the top level of the calling function, and\n  // without any dynamic conditional logic.\n  useQuery(options: QueryHookOptions<TData, TVariables>) {\n    // The renderPromises field gets initialized here in the useQuery method, at\n    // the beginning of everything (for a given component rendering, at least),\n    // so we can safely use this.renderPromises in other/later InternalState\n    // methods without worrying it might be uninitialized. Even after\n    // initialization, this.renderPromises is usually undefined (unless SSR is\n    // happening), but that's fine as long as it has been initialized that way,\n    // rather than left uninitialized.\n    this.renderPromises = useContext(getApolloContext()).renderPromises;\n\n    this.useOptions(options);\n\n    const obsQuery = this.useObservableQuery();\n\n    const result = useSyncExternalStore(\n      useCallback(() => {\n        if (this.renderPromises) {\n          return () => {};\n        }\n\n        const onNext = () => {\n          const previousResult = this.result;\n          // We use `getCurrentResult()` instead of the onNext argument because\n          // the values differ slightly. Specifically, loading results will have\n          // an empty object for data instead of `undefined` for some reason.\n          const result = obsQuery.getCurrentResult();\n          // Make sure we're not attempting to re-render similar results\n          if (\n            previousResult &&\n            previousResult.loading === result.loading &&\n            previousResult.networkStatus === result.networkStatus &&\n            equal(previousResult.data, result.data)\n          ) {\n            return;\n          }\n\n          this.setResult(result);\n        };\n\n        const onError = (error: Error) => {\n          const last = obsQuery[\"last\"];\n          subscription.unsubscribe();\n          // Unfortunately, if `lastError` is set in the current\n          // `observableQuery` when the subscription is re-created,\n          // the subscription will immediately receive the error, which will\n          // cause it to terminate again. To avoid this, we first clear\n          // the last error/result from the `observableQuery` before re-starting\n          // the subscription, and restore it afterwards (so the subscription\n          // has a chance to stay open).\n          try {\n            obsQuery.resetLastResults();\n            subscription = obsQuery.subscribe(onNext, onError);\n          } finally {\n            obsQuery[\"last\"] = last;\n          }\n\n          if (!hasOwnProperty.call(error, 'graphQLErrors')) {\n            // The error is not a GraphQL error\n            throw error;\n          }\n\n          const previousResult = this.result;\n          if (\n            !previousResult ||\n            (previousResult && previousResult.loading) ||\n            !equal(error, previousResult.error)\n          ) {\n            this.setResult({\n              data: (previousResult && previousResult.data) as TData,\n              error: error as ApolloError,\n              loading: false,\n              networkStatus: NetworkStatus.error,\n            });\n          }\n        };\n\n        let subscription = obsQuery.subscribe(onNext, onError);\n\n        return () => subscription.unsubscribe();\n      }, [\n        // We memoize the subscribe function using useCallback and the following\n        // dependency keys, because the subscribe function reference is all that\n        // useSyncExternalStore uses internally as a dependency key for the\n        // useEffect ultimately responsible for the subscription, so we are\n        // effectively passing this dependency array to that useEffect buried\n        // inside useSyncExternalStore, as desired.\n        obsQuery,\n        this.renderPromises,\n        this.client.disableNetworkFetches,\n      ]),\n\n      () => this.getCurrentResult(),\n      () => this.getCurrentResult(),\n    );\n\n    // TODO Remove this method when we remove support for options.partialRefetch.\n    this.unsafeHandlePartialRefetch(result);\n\n    const queryResult = this.toQueryResult(result);\n\n    if (!queryResult.loading && this.asyncResolveFns.size) {\n      this.asyncResolveFns.forEach(resolve => resolve(queryResult));\n      this.asyncResolveFns.clear();\n    }\n\n    return queryResult;\n  }\n\n  // These members (except for renderPromises) are all populated by the\n  // useOptions method, which is called unconditionally at the beginning of the\n  // useQuery method, so we can safely use these members in other/later methods\n  // without worrying they might be uninitialized.\n  private renderPromises: ApolloContextValue[\"renderPromises\"];\n  private queryHookOptions: QueryHookOptions<TData, TVariables>;\n  private watchQueryOptions: WatchQueryOptions<TVariables, TData>;\n\n  private useOptions(\n    options: QueryHookOptions<TData, TVariables>,\n  ) {\n    const watchQueryOptions = this.createWatchQueryOptions(\n      this.queryHookOptions = options,\n    );\n\n    // Update this.watchQueryOptions, but only when they have changed, which\n    // allows us to depend on the referential stability of\n    // this.watchQueryOptions elsewhere.\n    const currentWatchQueryOptions = this.watchQueryOptions;\n\n    // To force this equality test to \"fail,\" thereby reliably triggering\n    // observable.reobserve, add any current WatchQueryOptions object(s) you\n    // want to be ignored to this.optionsToIgnoreOnce. A similar effect could be\n    // achieved by nullifying this.watchQueryOptions so the equality test\n    // immediately fails because currentWatchQueryOptions is null, but this way\n    // we can promise a truthy this.watchQueryOptions at all times.\n    if (\n      this.optionsToIgnoreOnce.has(currentWatchQueryOptions) ||\n      !equal(watchQueryOptions, currentWatchQueryOptions)\n    ) {\n      this.watchQueryOptions = watchQueryOptions;\n\n      if (currentWatchQueryOptions && this.observable) {\n        // As advertised in the -Once of this.optionsToIgnoreOnce, this trick is\n        // only good for one forced execution of observable.reobserve per\n        // ignored WatchQueryOptions object, though it is unlikely we will ever\n        // see this exact currentWatchQueryOptions object again here, since we\n        // just replaced this.watchQueryOptions with watchQueryOptions.\n        this.optionsToIgnoreOnce.delete(currentWatchQueryOptions);\n\n        // Though it might be tempting to postpone this reobserve call to the\n        // useEffect block, we need getCurrentResult to return an appropriate\n        // loading:true result synchronously (later within the same call to\n        // useQuery). Since we already have this.observable here (not true for\n        // the very first call to useQuery), we are not initiating any new\n        // subscriptions, though it does feel less than ideal that reobserve\n        // (potentially) kicks off a network request (for example, when the\n        // variables have changed), which is technically a side-effect.\n        this.observable.reobserve(this.getObsQueryOptions());\n\n        // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n        // but save the current data as this.previousData, just like setResult\n        // usually does.\n        this.previousData = this.result?.data || this.previousData;\n        this.result = void 0;\n      }\n    }\n\n    // Make sure state.onCompleted and state.onError always reflect the latest\n    // options.onCompleted and options.onError callbacks provided to useQuery,\n    // since those functions are often recreated every time useQuery is called.\n    // Like the forceUpdate method, the versions of these methods inherited from\n    // InternalState.prototype are empty no-ops, but we can override them on the\n    // base state object (without modifying the prototype).\n    this.onCompleted = options.onCompleted || InternalState.prototype.onCompleted;\n    this.onError = options.onError || InternalState.prototype.onError;\n\n    if (\n      (this.renderPromises || this.client.disableNetworkFetches) &&\n      this.queryHookOptions.ssr === false &&\n      !this.queryHookOptions.skip\n    ) {\n      // If SSR has been explicitly disabled, and this function has been called\n      // on the server side, return the default loading state.\n      this.result = this.ssrDisabledResult;\n    } else if (\n      this.queryHookOptions.skip ||\n      this.watchQueryOptions.fetchPolicy === 'standby'\n    ) {\n      // When skipping a query (ie. we're not querying for data but still want to\n      // render children), make sure the `data` is cleared out and `loading` is\n      // set to `false` (since we aren't loading anything).\n      //\n      // NOTE: We no longer think this is the correct behavior. Skipping should\n      // not automatically set `data` to `undefined`, but instead leave the\n      // previous data in place. In other words, skipping should not mandate that\n      // previously received data is all of a sudden removed. Unfortunately,\n      // changing this is breaking, so we'll have to wait until Apollo Client 4.0\n      // to address this.\n      this.result = this.skipStandbyResult;\n    } else if (\n      this.result === this.ssrDisabledResult ||\n      this.result === this.skipStandbyResult\n    ) {\n      this.result = void 0;\n    }\n  }\n\n  private getObsQueryOptions(): WatchQueryOptions<TVariables, TData> {\n    const toMerge: Array<\n      Partial<WatchQueryOptions<TVariables, TData>>\n    > = [];\n\n    const globalDefaults = this.client.defaultOptions.watchQuery;\n    if (globalDefaults) toMerge.push(globalDefaults);\n\n    if (this.queryHookOptions.defaultOptions) {\n      toMerge.push(this.queryHookOptions.defaultOptions);\n    }\n\n    // We use compact rather than mergeOptions for this part of the merge,\n    // because we want watchQueryOptions.variables (if defined) to replace\n    // this.observable.options.variables whole. This replacement allows\n    // removing variables by removing them from the variables input to\n    // useQuery. If the variables were always merged together (rather than\n    // replaced), there would be no way to remove existing variables.\n    // However, the variables from options.defaultOptions and globalDefaults\n    // (if provided) should be merged, to ensure individual defaulted\n    // variables always have values, if not otherwise defined in\n    // observable.options or watchQueryOptions.\n    toMerge.push(compact(\n      this.observable && this.observable.options,\n      this.watchQueryOptions,\n    ));\n\n    return toMerge.reduce(\n      mergeOptions\n    ) as WatchQueryOptions<TVariables, TData>;\n  }\n\n  private ssrDisabledResult = maybeDeepFreeze({\n    loading: true,\n    data: void 0 as unknown as TData,\n    error: void 0,\n    networkStatus: NetworkStatus.loading,\n  });\n\n  private skipStandbyResult = maybeDeepFreeze({\n    loading: false,\n    data: void 0 as unknown as TData,\n    error: void 0,\n    networkStatus: NetworkStatus.ready,\n  });\n\n  // A function to massage options before passing them to ObservableQuery.\n  private createWatchQueryOptions({\n    skip,\n    ssr,\n    onCompleted,\n    onError,\n    displayName,\n    defaultOptions,\n    // The above options are useQuery-specific, so this ...otherOptions spread\n    // makes otherOptions almost a WatchQueryOptions object, except for the\n    // query property that we add below.\n    ...otherOptions\n  }: QueryHookOptions<TData, TVariables> = {}): WatchQueryOptions<TVariables, TData> {\n    // This Object.assign is safe because otherOptions is a fresh ...rest object\n    // that did not exist until just now, so modifications are still allowed.\n    const watchQueryOptions: WatchQueryOptions<TVariables, TData> =\n      Object.assign(otherOptions, { query: this.query });\n\n    if (\n      this.renderPromises &&\n      (\n        watchQueryOptions.fetchPolicy === 'network-only' ||\n        watchQueryOptions.fetchPolicy === 'cache-and-network'\n      )\n    ) {\n      // this behavior was added to react-apollo without explanation in this PR\n      // https://github.com/apollographql/react-apollo/pull/1579\n      watchQueryOptions.fetchPolicy = 'cache-first';\n    }\n\n    if (!watchQueryOptions.variables) {\n      watchQueryOptions.variables = {} as TVariables;\n    }\n\n    if (skip) {\n      const {\n        fetchPolicy = this.getDefaultFetchPolicy(),\n        initialFetchPolicy = fetchPolicy,\n      } = watchQueryOptions;\n\n      // When skipping, we set watchQueryOptions.fetchPolicy initially to\n      // \"standby\", but we also need/want to preserve the initial non-standby\n      // fetchPolicy that would have been used if not skipping.\n      Object.assign(watchQueryOptions, {\n        initialFetchPolicy,\n        fetchPolicy: 'standby',\n      });\n    } else if (!watchQueryOptions.fetchPolicy) {\n      watchQueryOptions.fetchPolicy =\n        this.observable?.options.initialFetchPolicy ||\n        this.getDefaultFetchPolicy();\n    }\n\n    return watchQueryOptions;\n  }\n\n  getDefaultFetchPolicy(): WatchQueryFetchPolicy {\n    return (\n      this.queryHookOptions.defaultOptions?.fetchPolicy ||\n      this.client.defaultOptions.watchQuery?.fetchPolicy ||\n      \"cache-first\"\n    );\n  }\n\n  // Defining these methods as no-ops on the prototype allows us to call\n  // state.onCompleted and/or state.onError without worrying about whether a\n  // callback was provided.\n  private onCompleted(data: TData) {}\n  private onError(error: ApolloError) {}\n\n  private observable: ObservableQuery<TData, TVariables>;\n  private obsQueryFields: Omit<\n    ObservableQueryFields<TData, TVariables>,\n    \"variables\"\n  >;\n\n  private useObservableQuery() {\n    // See if there is an existing observable that was used to fetch the same\n    // data and if so, use it instead since it will contain the proper queryId\n    // to fetch the result set. This is used during SSR.\n    const obsQuery = this.observable =\n      this.renderPromises\n        && this.renderPromises.getSSRObservable(this.watchQueryOptions)\n        || this.observable // Reuse this.observable if possible (and not SSR)\n        || this.client.watchQuery(this.getObsQueryOptions());\n\n    this.obsQueryFields = useMemo(() => ({\n      refetch: obsQuery.refetch.bind(obsQuery),\n      reobserve: obsQuery.reobserve.bind(obsQuery),\n      fetchMore: obsQuery.fetchMore.bind(obsQuery),\n      updateQuery: obsQuery.updateQuery.bind(obsQuery),\n      startPolling: obsQuery.startPolling.bind(obsQuery),\n      stopPolling: obsQuery.stopPolling.bind(obsQuery),\n      subscribeToMore: obsQuery.subscribeToMore.bind(obsQuery),\n    }), [obsQuery]);\n\n    const ssrAllowed = !(\n      this.queryHookOptions.ssr === false ||\n      this.queryHookOptions.skip\n    );\n\n    if (this.renderPromises && ssrAllowed) {\n      this.renderPromises.registerSSRObservable(obsQuery);\n\n      if (obsQuery.getCurrentResult().loading) {\n        // TODO: This is a legacy API which could probably be cleaned up\n        this.renderPromises.addObservableQueryPromise(obsQuery);\n      }\n    }\n\n    return obsQuery;\n  }\n\n  // These members are populated by getCurrentResult and setResult, and it's\n  // okay/normal for them to be initially undefined.\n  private result: undefined | ApolloQueryResult<TData>;\n  private previousData: undefined | TData;\n\n  private setResult(nextResult: ApolloQueryResult<TData>) {\n    const previousResult = this.result;\n    if (previousResult && previousResult.data) {\n      this.previousData = previousResult.data;\n    }\n    this.result = nextResult;\n    // Calling state.setResult always triggers an update, though some call sites\n    // perform additional equality checks before committing to an update.\n    this.forceUpdate();\n    this.handleErrorOrCompleted(nextResult);\n  }\n\n  private handleErrorOrCompleted(result: ApolloQueryResult<TData>) {\n    if (!result.loading) {\n      if (result.error) {\n        this.onError(result.error);\n      } else if (result.data) {\n        this.onCompleted(result.data);\n      }\n    }\n  }\n\n  private getCurrentResult(): ApolloQueryResult<TData> {\n    // Using this.result as a cache ensures getCurrentResult continues returning\n    // the same (===) result object, unless state.setResult has been called, or\n    // we're doing server rendering and therefore override the result below.\n    if (!this.result) {\n      this.handleErrorOrCompleted(\n        this.result = this.observable.getCurrentResult()\n      );\n    }\n    return this.result;\n  }\n\n  // This cache allows the referential stability of this.result (as returned by\n  // getCurrentResult) to translate into referential stability of the resulting\n  // QueryResult object returned by toQueryResult.\n  private toQueryResultCache = new (canUseWeakMap ? WeakMap : Map)<\n    ApolloQueryResult<TData>,\n    QueryResult<TData, TVariables>\n  >();\n\n  toQueryResult(\n    result: ApolloQueryResult<TData>,\n  ): QueryResult<TData, TVariables> {\n    let queryResult = this.toQueryResultCache.get(result);\n    if (queryResult) return queryResult;\n\n    const { data, partial, ...resultWithoutPartial } = result;\n    this.toQueryResultCache.set(result, queryResult = {\n      data, // Ensure always defined, even if result.data is missing.\n      ...resultWithoutPartial,\n      ...this.obsQueryFields,\n      client: this.client,\n      observable: this.observable,\n      variables: this.observable.variables,\n      called: !this.queryHookOptions.skip,\n      previousData: this.previousData,\n    });\n\n    if (!queryResult.error && isNonEmptyArray(result.errors)) {\n      // Until a set naming convention for networkError and graphQLErrors is\n      // decided upon, we map errors (graphQLErrors) to the error options.\n      // TODO: Is it possible for both result.error and result.errors to be\n      // defined here?\n      queryResult.error = new ApolloError({ graphQLErrors: result.errors });\n    }\n\n    return queryResult;\n  }\n\n  private unsafeHandlePartialRefetch(result: ApolloQueryResult<TData>) {\n    // WARNING: SIDE-EFFECTS IN THE RENDER FUNCTION\n    //\n    // TODO: This code should be removed when the partialRefetch option is\n    // removed. I was unable to get this hook to behave reasonably in certain\n    // edge cases when this block was put in an effect.\n    if (\n      result.partial &&\n      this.queryHookOptions.partialRefetch &&\n      !result.loading &&\n      (!result.data || Object.keys(result.data).length === 0) &&\n      this.observable.options.fetchPolicy !== 'cache-only'\n    ) {\n      Object.assign(result, {\n        loading: true,\n        networkStatus: NetworkStatus.refetch,\n      });\n      this.observable.refetch();\n    }\n  }\n}\n"], "names": ["module", "exports", "condition", "format", "a", "b", "c", "d", "e", "f", "error", "undefined", "Error", "args", "argIndex", "replace", "name", "framesToPop", "useApolloClient", "override", "context", "useContext", "getApolloContext", "client", "__DEV__", "invariant", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "getLocation", "source", "position", "match", "lineRegexp", "line", "column", "exec", "body", "index", "length", "printLocation", "location", "printSourceLocation", "start", "sourceLocation", "firstLineColumnOffset", "locationOffset", "whitespace", "lineIndex", "lineOffset", "lineNum", "columnOffset", "columnNum", "locationStr", "concat", "lines", "split", "locationLine", "subLineIndex", "Math", "floor", "subLineColumnNum", "subLines", "i", "push", "slice", "printPrefixedLines", "map", "subLine", "existingLines", "filter", "_ref", "padLen", "max", "apply", "_ref2", "_ref3", "str", "prefix", "join", "len", "Array", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_possibleConstructorReturn", "self", "call", "_assertThisInitialized", "ReferenceError", "_wrapNativeSuper", "Class", "_cache", "Map", "fn", "Function", "toString", "indexOf", "TypeError", "has", "get", "set", "Wrapper", "_construct", "arguments", "_getPrototypeOf", "this", "create", "value", "_setPrototypeOf", "Parent", "_isNativeReflectConstruct", "Reflect", "construct", "instance", "bind", "sham", "Proxy", "Date", "o", "p", "setPrototypeOf", "__proto__", "getPrototypeOf", "GraphQLError", "_Error", "subClass", "superClass", "_inherits", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_super", "Derived", "hasNativeReflectConstruct", "result", "Super", "<PERSON><PERSON><PERSON><PERSON>", "_createSuper", "message", "nodes", "positions", "path", "originalError", "extensions", "_locations2", "_source2", "_positions2", "_extensions2", "_this", "_classCallCheck", "_nodes$0$loc", "_nodes", "isArray", "_source", "loc", "_locations", "_positions", "reduce", "list", "node", "pos", "_extensions", "originalExtensions", "defineProperties", "locations", "stack", "captureStackTrace", "output", "_i2", "_error$nodes2", "_i4", "_error$locations2", "printError", "SYMBOL_TO_STRING_TAG", "syntaxError", "description", "Kind", "freeze", "NAME", "DOCUMENT", "OPERATION_DEFINITION", "VARIABLE_DEFINITION", "SELECTION_SET", "FIELD", "ARGUMENT", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "FRAGMENT_DEFINITION", "VARIABLE", "INT", "FLOAT", "STRING", "BOOLEAN", "NULL", "ENUM", "LIST", "OBJECT", "OBJECT_FIELD", "DIRECTIVE", "NAMED_TYPE", "LIST_TYPE", "NON_NULL_TYPE", "SCHEMA_DEFINITION", "OPERATION_TYPE_DEFINITION", "SCALAR_TYPE_DEFINITION", "OBJECT_TYPE_DEFINITION", "FIELD_DEFINITION", "INPUT_VALUE_DEFINITION", "INTERFACE_TYPE_DEFINITION", "UNION_TYPE_DEFINITION", "ENUM_TYPE_DEFINITION", "ENUM_VALUE_DEFINITION", "INPUT_OBJECT_TYPE_DEFINITION", "DIRECTIVE_DEFINITION", "SCHEMA_EXTENSION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION", "TokenKind", "SOF", "EOF", "BANG", "DOLLAR", "AMP", "PAREN_L", "PAREN_R", "SPREAD", "COLON", "EQUALS", "AT", "BRACKET_L", "BRACKET_R", "BRACE_L", "PIPE", "BRACE_R", "BLOCK_STRING", "COMMENT", "DirectiveLocation", "QUERY", "MUTATION", "SUBSCRIPTION", "SCHEMA", "SCALAR", "ARGUMENT_DEFINITION", "INTERFACE", "UNION", "ENUM_VALUE", "INPUT_OBJECT", "INPUT_FIELD_DEFINITION", "<PERSON><PERSON>", "startOfFileToken", "Token", "lastToken", "token", "lineStart", "_proto", "advance", "<PERSON><PERSON><PERSON>", "kind", "_token$next", "next", "readToken", "printCharCode", "code", "isNaN", "JSON", "stringify", "String", "fromCharCode", "toUpperCase", "lexer", "prev", "<PERSON><PERSON><PERSON><PERSON>", "end", "charCodeAt", "_line", "_col", "readComment", "readBlockString", "readString", "readNumber", "readName", "unexpectedCharacterMessage", "col", "firstCode", "isFloat", "readDigits", "isNameStart", "chunkStart", "charCode", "char2hex", "invalidSequence", "rawValue", "dedentBlockStringValue", "<PERSON><PERSON><PERSON>", "options", "sourceObj", "isSource", "Source", "_lexer", "_options", "parseName", "expectToken", "parseDocument", "definitions", "many", "parseDefinition", "peek", "parseOperationDefinition", "parseFragmentDefinition", "parseTypeSystemDefinition", "parseTypeSystemExtension", "peekDescription", "unexpected", "operation", "variableDefinitions", "directives", "selectionSet", "parseSelectionSet", "parseOperationType", "parseVariableDefinitions", "parseDirectives", "operationToken", "optional<PERSON><PERSON>", "parseVariableDefinition", "variable", "parseVariable", "type", "parseTypeReference", "defaultValue", "expectOptionalToken", "parseValueLiteral", "selections", "parseSelection", "parseFragment", "parseField", "alias", "nameOr<PERSON><PERSON><PERSON>", "parseArguments", "isConst", "item", "parseConstArgument", "parseArgument", "hasTypeCondition", "expectOptionalKeyword", "parseFragmentName", "typeCondition", "parseNamedType", "_this$_options", "expectKeyword", "experimentalFragmentVariables", "parseList", "parseObject", "parseStringLiteral", "block", "values", "any", "_this2", "fields", "parseObjectField", "parseDirective", "keywordToken", "parseSchemaDefinition", "parseScalarTypeDefinition", "parseObjectTypeDefinition", "parseInterfaceTypeDefinition", "parseUnionTypeDefinition", "parseEnumTypeDefinition", "parseInputObjectTypeDefinition", "parseDirectiveDefinition", "parseDescription", "operationTypes", "parseOperationTypeDefinition", "interfaces", "parseImplementsInterfaces", "parseFieldsDefinition", "_this$_options2", "allowLegacySDLImplementsInterfaces", "types", "delimitedMany", "_this$_options3", "allowLegacySDLEmptyFields", "parseFieldDefinition", "parseArgumentDefs", "parseInputValueDef", "parseUnionMemberTypes", "parseEnumValuesDefinition", "parseEnumValueDefinition", "parseInputFieldsDefinition", "parseSchemaExtension", "parseScalarTypeExtension", "parseObjectTypeExtension", "parseInterfaceTypeExtension", "parseUnionTypeExtension", "parseEnumTypeExtension", "parseInputObjectTypeExtension", "repeatable", "parseDirectiveLocations", "parseDirectiveLocation", "startToken", "_this$_options4", "noLocation", "Location", "getTokenKindDesc", "getTokenDesc", "atToken", "openKind", "parseFn", "<PERSON><PERSON><PERSON>", "delimiterKind", "isPunctuatorTokenKind", "<PERSON><PERSON><PERSON><PERSON>", "fragmentSourceMap", "printFragmentWarnings", "normalize", "string", "trim", "processFragments", "ast", "<PERSON><PERSON><PERSON><PERSON>", "Set", "for<PERSON>ach", "fragmentDefinition", "fragmentName", "sourceKey", "substring", "sourceKeySet", "console", "warn", "add", "__assign", "cache<PERSON>ey", "parsed", "parse", "allowLegacyFragmentVariables", "doc", "workSet", "keys", "endToken", "stripLoc", "gql", "literals", "_i", "arg", "gql_1", "extras", "clear", "resetCaches", "disableFragmentWarnings", "enableExperimentalFragmentVariables", "disableExperimentalFragmentVariables", "didWarnUncachedGetSnapshot", "useSyncExternalStore", "React", "subscribe", "getSnapshot", "getServerSnapshot", "_a", "inst", "forceUpdate", "canUseLayoutEffect", "assign", "checkIfSnapshotChanged", "_b", "DocumentType", "cache", "operationName", "Query", "Mutation", "Subscription", "verifyDocumentType", "document", "variables", "cached", "fragments", "queries", "mutations", "subscriptions", "x", "definition", "payload", "parser", "requiredOperationName", "usedOperationName", "hasOwnProperty", "useQuery", "query", "useInternalState", "stateRef", "useRef", "current", "InternalState", "state", "useState", "setTick", "tick", "previous", "asyncResolveFns", "optionsToIgnoreOnce", "canUseWeakSet", "WeakSet", "ssrDisabledResult", "maybeDeepFreeze", "loading", "data", "networkStatus", "NetworkStatus", "skipStandbyResult", "ready", "toQueryResultCache", "canUseWeakMap", "WeakMap", "previousResult", "previousData", "asyncUpdate", "Promise", "resolve", "watchQueryOptions", "renderPromises", "useOptions", "obsQuery", "useObservableQuery", "useCallback", "onNext", "getCurrentResult", "equal", "setResult", "onError", "last", "subscription", "unsubscribe", "resetLastResults", "disableNetworkFetches", "unsafeHandlePartialRefetch", "query<PERSON><PERSON>ult", "to<PERSON><PERSON><PERSON><PERSON><PERSON>ult", "size", "createWatchQueryOptions", "queryHookOptions", "currentWatchQueryOptions", "observable", "delete", "reobserve", "getObsQueryOptions", "onCompleted", "ssr", "skip", "fetchPolicy", "toMerge", "globalDefaults", "defaultOptions", "watch<PERSON><PERSON>y", "compact", "mergeOptions", "otherOptions", "displayName", "__rest", "_c", "getDefaultFetchPolicy", "_d", "initialFetchPolicy", "getSSRObservable", "obsQuery<PERSON>ields", "useMemo", "refetch", "fetchMore", "updateQuery", "startPolling", "stopPolling", "subscribeToMore", "ssrAllowed", "registerSSRObservable", "addObservableQueryPromise", "nextResult", "handleErrorOrCompleted", "resultWithoutPartial", "partial", "called", "isNonEmptyArray", "errors", "ApolloError", "graphQLErrors", "partialRefetch"], "sourceRoot": ""}