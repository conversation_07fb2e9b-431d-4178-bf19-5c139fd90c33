"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[9159],{6091:function(e,t,n){n.r(t),n.d(t,{default:function(){return Q}});var r=n(31014),o=n(51079),a=n.n(o),s=n(10811),i=n(26809),l=n(53140),c=n(88443),d=n(64912),u=n(48012),p=n(15579),h=n(72877),f=(n(71832),n(51882)),m=n(7871),g=n(87234),b=n(58481),y=n(93215),x=n(85017),v=n(24406),w=n(76010),S=n(55406),Y=n(79085),M=n(10405),k=n(91144),C=n(89555),A=n(62680);const E=e=>{const[t,n]=(0,r.useState)({}),[o,a]=(0,r.useState)(!0),[s,l]=(0,r.useState)(null);return(0,r.useEffect)((()=>{e.length>0?(async()=>{a(!0),l(null);const t={};try{await Promise.all(e.map((async e=>{const n=(0,i.cN)(e),r=await n.payload;t[e]=r}))),n(t)}catch(r){l(r)}finally{a(!1)}})():(n({}),a(!1))}),[e]),{artifactsKeyedByRun:t,isLoading:o,error:s}};var T=n(32599),D=n(17275),R=n(64408),N=n(50111);var I={name:"1ua6b7f",styles:"display:flex;flex-direction:row;height:100vh"},z={name:"jovua5",styles:"display:flex;flex-direction:row;flex-wrap:wrap"};const L=e=>{let{runUuids:t,runInfos:n,colWidth:o}=e;const{theme:a}=(0,T.u)(),[s,i]=(0,r.useState)(),{artifactsKeyedByRun:l}=E(t),d=(e=>{var t,n,r;const o=Object.keys(e);if(0===o.length)return[];let a=null===(t=e[o[0]])||void 0===t||null===(n=t.files)||void 0===n||null===(r=n.map((e=>e.is_dir?null:e.path)))||void 0===r?void 0:r.filter((e=>null!==e));if(!a||0===a.length)return[];for(let c=1;c<o.length;c++){var s,i,l;const t=null===(s=e[o[c]])||void 0===s||null===(i=s.files)||void 0===i?void 0:i.map((e=>e.path));if(a=null===(l=a)||void 0===l?void 0:l.filter((e=>t.includes(e))),0===a.length)break}return a})(l);return 0===d.length?(0,N.Y)("h2",{children:(0,N.Y)(c.A,{id:"IUAmWX",defaultMessage:"No common artifacts to display."})}):(0,N.FD)("div",{css:I,children:[(0,N.Y)("div",{css:(0,C.AH)({backgroundColor:a.colors.backgroundPrimary,color:a.colors.textPrimary,flex:"1 1 0%",whiteSpace:"nowrap",border:`1px solid ${a.colors.grey300}`,overflowY:"auto"},""),children:(0,N.Y)(D.K,{data:d.map((e=>({id:e,active:s===e,name:(0,R.hA)(e)}))),onToggleTreebeard:e=>{let{id:t}=e;return i(t)}})}),(0,N.Y)("div",{css:(0,C.AH)({border:`1px solid ${a.colors.grey300}`,borderLeft:"none",display:"flex",flexDirection:"column",overflow:"hidden"},""),children:(0,N.Y)("div",{css:z,children:t.map(((e,t)=>(0,N.Y)("div",{style:{width:`${o}px`,borderBottom:`1px solid ${a.colors.grey300}`,padding:s?0:a.spacing.md,overflow:"auto",whiteSpace:"nowrap"},children:(0,N.Y)(A.A,{runUuid:e,artifactRootUri:n[t].artifactUri,path:s,experimentId:n[t].experimentId})},e)))})})]})};const{TabPane:P}=u.Y6f;var U={name:"m4bh15",styles:"max-height:500px"},H={name:"1o6wc9k",styles:"padding-left:6px"},_={name:"1f6izsi",styles:"max-height:300px"},F={name:"m4bh15",styles:"max-height:500px"};class O extends r.Component{constructor(e){super(e),this.compareRunViewRef=void 0,this.runDetailsTableRef=void 0,this.state={tableWidth:null,onlyShowParamDiff:!1,onlyShowTagDiff:!1,onlyShowMetricDiff:!1},this.onResizeHandler=this.onResizeHandler.bind(this),this.onCompareRunTableScrollHandler=this.onCompareRunTableScrollHandler.bind(this),this.runDetailsTableRef=r.createRef(),this.compareRunViewRef=r.createRef()}onResizeHandler(e){const t=this.runDetailsTableRef.current;if(null!==t){const e=t.clientWidth;this.setState({tableWidth:e})}}onCompareRunTableScrollHandler(e){const t=this.compareRunViewRef.current.querySelectorAll(".compare-run-table");t.forEach(((n,r)=>{const o=t[r];o!==e.target&&(o.scrollLeft=e.target.scrollLeft)}))}componentDidMount(){const e=this.props.intl.formatMessage({id:"ufamzi",defaultMessage:"Comparing {runs} MLflow Runs"},{runs:this.props.runInfos.length});w.A.updatePageTitle(e),window.addEventListener("resize",this.onResizeHandler,!0),window.dispatchEvent(new Event("resize"))}componentWillUnmount(){window.removeEventListener("resize",this.onResizeHandler,!0)}getTableColumnWidth(){let e=200;return null!==this.state.tableWidth&&(e=Math.round(this.state.tableWidth/(this.props.runInfos.length+1)),e<200&&(e=200)),e}renderExperimentNameRowItems(){const{experiments:e}=this.props,t=w.A.getExperimentNameMap(w.A.sortExperimentsById(e));return this.props.runInfos.map((e=>{let{experimentId:n,runUuid:r}=e;const{name:o,basename:a}=t[n];return(0,N.Y)("td",{className:"meta-info",children:(0,N.Y)(y.N_,{to:b.h.getExperimentPageRoute(n),title:o,children:a})},r)}))}hasMultipleExperiments(){return this.props.experimentIds.length>1}shouldShowExperimentNameRow(){return this.props.hasComparedExperimentsBefore||this.hasMultipleExperiments()}getExperimentPageLink(e,t){return(0,N.Y)(y.N_,{to:b.h.getExperimentPageRoute(e),children:t})}getCompareExperimentsPageLinkText(e){return(0,N.Y)(c.A,{id:"YkcoeV",defaultMessage:"Displaying Runs from {numExperiments} Experiments",values:{numExperiments:e}})}getCompareExperimentsPageLink(e){return(0,N.Y)(y.N_,{to:b.h.getCompareExperimentsPageRoute(e),children:this.getCompareExperimentsPageLinkText(e.length)})}getExperimentLink(){const{comparedExperimentIds:e,hasComparedExperimentsBefore:t,experimentIds:n,experiments:r}=this.props;return n[0]&&r[0]?t?this.getCompareExperimentsPageLink(e):this.hasMultipleExperiments()?this.getCompareExperimentsPageLink(n):this.getExperimentPageLink(n[0],r[0].name):""}getTitle(){return this.hasMultipleExperiments()?(0,N.Y)(c.A,{id:"IEORCP",defaultMessage:"Comparing {numRuns} Runs from {numExperiments} Experiments",values:{numRuns:this.props.runInfos.length,numExperiments:this.props.experimentIds.length}}):(0,N.Y)(c.A,{id:"NN0ScV",defaultMessage:"Comparing {numRuns} Runs from 1 Experiment",values:{numRuns:this.props.runInfos.length}})}renderParamTable(e){const t=this.renderDataRows(this.props.paramLists,e,this.state.onlyShowParamDiff,!0,((e,t)=>e),(e=>{try{const t=W(e);return"object"===typeof t&&null!==t?this.renderPrettyJson(t):e}catch(t){return e}}));return 0===t.length?(0,N.Y)("h2",{children:(0,N.Y)(c.A,{id:"lNO3kK",defaultMessage:"No parameters to display."})}):(0,N.Y)("table",{className:"table compare-table compare-run-table",css:U,onScroll:this.onCompareRunTableScrollHandler,children:(0,N.Y)("tbody",{children:t})})}renderPrettyJson(e){return(0,N.Y)("pre",{children:JSON.stringify(e,null,2)})}renderMetricTable(e,t){const n=this.renderDataRows(this.props.metricLists,e,this.state.onlyShowMetricDiff,!1,((e,n)=>(0,N.FD)(y.N_,{to:b.h.getMetricPageRoute(this.props.runInfos.map((e=>e.runUuid)).filter(((e,t)=>void 0!==n[t])),e,t),title:"Plot chart",children:[e,(0,N.Y)("i",{className:"fas fa-chart-line",css:H})]})),w.A.formatMetric);return 0===n.length?(0,N.Y)("h2",{children:(0,N.Y)(c.A,{id:"eBGO2d",defaultMessage:"No metrics to display."})}):(0,N.Y)("table",{className:"table compare-table compare-run-table",css:_,onScroll:this.onCompareRunTableScrollHandler,children:(0,N.Y)("tbody",{children:n})})}renderArtifactTable(e){return(0,N.Y)(L,{runUuids:this.props.runUuids,runInfos:this.props.runInfos,colWidth:e})}renderTagTable(e){const t=this.renderDataRows(this.props.tagLists,e,this.state.onlyShowTagDiff,!0);return 0===t.length?(0,N.Y)("h2",{children:(0,N.Y)(c.A,{id:"m9e01X",defaultMessage:"No tags to display."})}):(0,N.Y)("table",{className:"table compare-table compare-run-table",css:F,onScroll:this.onCompareRunTableScrollHandler,children:(0,N.Y)("tbody",{children:t})})}renderTimeRows(e){const t=(0,N.Y)(c.A,{id:"Zc48NC",defaultMessage:"(unknown)"}),n=this.props.runInfos.map((e=>{const n=e.startTime,r=e.endTime;return{runUuid:e.runUuid,startTime:n?w.A.formatTimestamp(n,this.props.intl):t,endTime:r?w.A.formatTimestamp(r,this.props.intl):t,duration:n&&r?w.A.getDuration(n,r):t}}));return[{key:"startTime",title:(0,N.Y)(c.A,{id:"O9r1RR",defaultMessage:"Start Time:"}),data:n.map((e=>{let{runUuid:t,startTime:n}=e;return[t,n]}))},{key:"endTime",title:(0,N.Y)(c.A,{id:"G0gYkS",defaultMessage:"End Time:"}),data:n.map((e=>{let{runUuid:t,endTime:n}=e;return[t,n]}))},{key:"duration",title:(0,N.Y)(c.A,{id:"7kUi8J",defaultMessage:"Duration:"}),data:n.map((e=>{let{runUuid:t,duration:n}=e;return[t,n]}))}].map((t=>{let{key:n,title:r,data:o}=t;return(0,N.FD)("tr",{children:[(0,N.Y)("th",{scope:"row",className:"head-value sticky-header",css:e,children:r}),o.map((t=>{let[n,r]=t;return(0,N.Y)("td",{className:"data-value",css:e,children:(0,N.Y)(u.paO,{title:r,color:"gray",placement:"topLeft",overlayStyle:{maxWidth:"400px"},dangerouslySetAntdProps:{mouseEnterDelay:1},children:r})},n)}))]},n)}))}render(){const{experimentIds:e}=this.props,{runInfos:t,runNames:n,paramLists:r,metricLists:o,runUuids:a}=this.props,s=this.getTableColumnWidth(),i=this.genWidthStyle(s),l=this.getTitle();let d=[this.getExperimentLink()];const h=this.props.intl.formatMessage({id:"eOxs/C",defaultMessage:"Parameters"}),x=this.props.intl.formatMessage({id:"frPtD/",defaultMessage:"Metrics"}),v=this.props.intl.formatMessage({id:"Ccc4/4",defaultMessage:"Artifacts"}),w=this.props.intl.formatMessage({id:"9KXBFn",defaultMessage:"Tags"}),C=this.props.intl.formatMessage({id:"VP+qFF",defaultMessage:"Show diff only"}),A=!(0,k.g$)();return(0,N.FD)("div",{className:"CompareRunView",ref:this.compareRunViewRef,children:[(0,N.Y)(Y.z,{title:l,breadcrumbs:d,spacerSize:"xs"}),A&&(0,N.Y)(M.i,{title:this.props.intl.formatMessage({id:"S7ESYH",defaultMessage:"Visualizations"}),children:(0,N.FD)(u.Y6f,{children:[(0,N.Y)(P,{tab:(0,N.Y)(c.A,{id:"sTp/V3",defaultMessage:"Parallel Coordinates Plot"}),children:(0,N.Y)(S.Ay,{runUuids:this.props.runUuids})},"parallel-coordinates-plot"),(0,N.Y)(P,{tab:(0,N.Y)(c.A,{id:"OimAJb",defaultMessage:"Scatter Plot"}),children:(0,N.Y)(f.J,{runUuids:this.props.runUuids,runDisplayNames:this.props.runDisplayNames})},"scatter-plot"),(0,N.Y)(P,{tab:(0,N.Y)(c.A,{id:"iXb99e",defaultMessage:"Box Plot"}),children:(0,N.Y)(m.e,{runUuids:a,runInfos:t,paramLists:r,metricLists:o})},"box-plot"),(0,N.Y)(P,{tab:(0,N.Y)(c.A,{id:"B80MC6",defaultMessage:"Contour Plot"}),children:(0,N.Y)(g.A,{runUuids:this.props.runUuids,runDisplayNames:this.props.runDisplayNames})},"contour-plot")]})}),(0,N.Y)(M.i,{title:this.props.intl.formatMessage({id:"lISqyJ",defaultMessage:"Run details"}),children:(0,N.FD)("table",{className:"table compare-table compare-run-table",ref:this.runDetailsTableRef,onScroll:this.onCompareRunTableScrollHandler,children:[(0,N.Y)("thead",{children:(0,N.FD)("tr",{children:[(0,N.Y)("th",{scope:"row",className:"head-value sticky-header",css:i,children:(0,N.Y)(c.A,{id:"/2MZix",defaultMessage:"Run ID:"})}),this.props.runInfos.map((e=>{var t,n;return(0,N.Y)("th",{scope:"row",className:"data-value",css:i,children:(0,N.Y)(u.paO,{title:e.runUuid,color:"gray",placement:"topLeft",overlayStyle:{maxWidth:"400px"},mouseEnterDelay:1,children:(0,N.Y)(y.N_,{to:b.h.getRunPageRoute(null!==(t=e.experimentId)&&void 0!==t?t:"0",null!==(n=e.runUuid)&&void 0!==n?n:""),children:e.runUuid})})},e.runUuid)}))]})}),(0,N.FD)("tbody",{children:[(0,N.FD)("tr",{children:[(0,N.Y)("th",{scope:"row",className:"head-value sticky-header",css:i,children:(0,N.Y)(c.A,{id:"DUnrWL",defaultMessage:"Run Name:"})}),n.map(((e,n)=>(0,N.Y)("td",{className:"data-value",css:i,children:(0,N.Y)("div",{className:"truncate-text single-line",children:(0,N.Y)(u.paO,{title:e,color:"gray",placement:"topLeft",overlayStyle:{maxWidth:"400px"},mouseEnterDelay:1,children:e})})},t[n].runUuid)))]}),this.renderTimeRows(i),this.shouldShowExperimentNameRow()&&(0,N.FD)("tr",{children:[(0,N.Y)("th",{scope:"row",className:"data-value",children:(0,N.Y)(c.A,{id:"Qsgg+9",defaultMessage:"Experiment Name:"})}),this.renderExperimentNameRowItems()]})]})]})}),(0,N.FD)(M.i,{title:h,children:[(0,N.Y)(u.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_comparerunview.tsx_570",label:C,"aria-label":[h,C].join(" - "),checked:this.state.onlyShowParamDiff,onChange:(e,t)=>this.setState({onlyShowParamDiff:e})}),(0,N.Y)(p.S,{size:"lg"}),this.renderParamTable(s)]}),(0,N.FD)(M.i,{title:x,children:[(0,N.Y)(u.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_comparerunview.tsx_581",label:C,"aria-label":[x,C].join(" - "),checked:this.state.onlyShowMetricDiff,onChange:(e,t)=>this.setState({onlyShowMetricDiff:e})}),(0,N.Y)(p.S,{size:"lg"}),this.renderMetricTable(s,e)]}),(0,N.Y)(M.i,{title:v,children:this.renderArtifactTable(s)}),(0,N.FD)(M.i,{title:w,children:[(0,N.Y)(u.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_comparerunview.tsx_592",label:C,"aria-label":[w,C].join(" - "),checked:this.state.onlyShowTagDiff,onChange:(e,t)=>this.setState({onlyShowTagDiff:e})}),(0,N.Y)(p.S,{size:"lg"}),this.renderTagTable(s)]})]})}genWidthStyle(e){return{width:`${e}px`,minWidth:`${e}px`,maxWidth:`${e}px`}}renderDataRows(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:(e,t)=>e,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e=>e;const s=v.A.getKeys(e),i={};s.forEach((t=>i[t]={values:Array(e.length).fill(void 0)})),e.forEach(((e,t)=>{e.forEach((e=>i[e.key].values[t]=e.value))})),s.forEach((e=>{return i[e].hasDiff=(t=i[e].values).some((e=>e!==t[0]));var t}));const l=this.genWidthStyle(t);return s.filter((e=>!n||i[e].hasDiff)).map((e=>{const{values:t,hasDiff:n}=i[e],s=r&&n?"diff-row":void 0;return(0,N.FD)("tr",{className:s,children:[(0,N.Y)("th",{scope:"row",className:"head-value sticky-header",css:l,children:o(e,t)}),t.map(((e,t)=>{const n=void 0===e?"":a(e);return(0,N.Y)("td",{className:"data-value",css:l,children:(0,N.Y)(u.paO,{title:n,color:"gray",placement:"topLeft",overlayStyle:{maxWidth:"400px"},mouseEnterDelay:1,children:(0,N.Y)("span",{className:"truncate-text single-line",children:n})})},this.props.runInfos[t].runUuid)}))]},e)}))}}const W=e=>{try{const t=e.replace(/'/g,'"');return JSON.parse(t)}catch(t){return null}};var B=(0,s.Ng)(((e,t)=>{const{comparedExperimentIds:n,hasComparedExperimentsBefore:r}=e.compareExperiments,o=[],a=[],s=[],i=[],l=[],c=[],{experimentIds:d,runUuids:u}=t,p=d.map((t=>(0,h._m)(t,e)));return u.forEach((t=>{const n=(0,h.K4)(t,e);if(!n)return;o.push(n),a.push(Object.values((0,x.d0)(t,e))),s.push(Object.values((0,h.tI)(t,e)));const r=(0,h.X3)(t,e),d=w.A.getVisibleTagValues(r).map((e=>{let[t,n]=e;return{key:t,value:n}}));i.push(d),c.push(w.A.getRunDisplayName(n,t)),l.push(w.A.getRunName(n))})),{experiments:p,runInfos:o,metricLists:a,paramLists:s,tagLists:i,runNames:l,runDisplayNames:c,comparedExperimentIds:n,hasComparedExperimentsBefore:r}}))((0,d.Ay)(O)),j=n(7204),V=n(48588),J=n(25869),q=n(20109),$=n(62448);class K extends r.Component{constructor(e){super(e),this.requestIds=void 0,this.state={requestError:void 0},this.requestIds=[]}fetchExperiments(){return this.props.experimentIds.map((e=>{const t=(0,j.yk)();return this.props.dispatch((0,i.yc)(e,t)).catch((e=>this.setState({requestError:e}))),t}))}componentDidMount(){this.requestIds.push(...this.fetchExperiments()),this.props.runUuids.forEach((e=>{const t=(0,j.yk)();this.requestIds.push(t),this.props.dispatch((0,i.aO)(e,t)).catch((e=>{this.setState({requestError:e})}))}))}render(){if(this.state.requestError){var e;const{requestError:t}=this.state;throw t instanceof Error?t:new Error(null===(e=t.getMessageField)||void 0===e?void 0:e.call(t))}return(0,N.Y)(V.L,{children:(0,N.Y)(l.Ay,{suppressErrorThrow:!0,requestIds:this.requestIds,children:(0,N.Y)(B,{runUuids:this.props.runUuids,experimentIds:this.props.experimentIds})})})}}const X=e=>{const t=decodeURIComponent(e);return e!==t?X(t):t};var G={name:"64bku7",styles:"height:100%;align-items:center;justify-content:center;display:flex"};const Z=(0,J.h)((0,s.Ng)(((e,t)=>{try{const{location:e}=t,n=X(e.search),r=a().parse(n),o=JSON.parse(r["?runs"]);return{experimentIds:JSON.parse(r.experiments),runUuids:o}}catch(n){if(n instanceof SyntaxError)throw new SyntaxError(`Error while parsing URL: ${n.message}`);throw n}}))(K));var Q=(0,q.X)($.A.mlflowServices.RUN_TRACKING,Z,void 0,(e=>{let{error:t}=e;return(0,N.Y)("div",{css:G,children:(0,N.Y)(u.SvL,{title:(0,N.Y)(c.A,{id:"ua6IVs",defaultMessage:"Error while loading compare runs page"}),description:t.message,image:(0,N.Y)(T.j,{})})})}))},10405:function(e,t,n){n.d(t,{i:function(){return u}});var r=n(89555),o=n(31014),a=n(27705),s=n(32599),i=n(48012),l=n(88464),c=n(50111);const d=e=>{let{theme:t,getPrefixedClassName:n}=e;const r=n("collapse"),o=`.${r}-item`,a=`.${r}-header`,s=`.${r}-content-box`;return{fontSize:14,[`& > ${o} > ${a}`]:{paddingLeft:0,paddingTop:12,paddingBottom:12,display:"flex",alignItems:"center",fontSize:16,fontWeight:"normal",lineHeight:t.typography.lineHeightLg},[s]:{padding:`${t.spacing.xs}px 0 ${t.spacing.md}px 0`}}};function u(e){const{title:t,forceOpen:n,showServerError:u,defaultCollapsed:p,onChange:h,className:f,componentId:m="mlflow.common.generic_collapsible_section"}=e,g=n&&{activeKey:["1"]},b=p?null:["1"],{theme:y,getPrefixedClassName:x}=(0,s.u)(),{formatMessage:v}=(0,l.A)(),w=(0,o.useCallback)((e=>{let{isActive:n}=e;return(0,c.Y)("div",{css:(0,s.i)({width:y.general.heightBase/2,transform:n?"rotate(90deg)":void 0}),children:(0,c.Y)(s.q,{css:(0,r.AH)({svg:{width:y.general.heightBase/2,height:y.general.heightBase/2}},""),"aria-label":v(n?{id:"iaomsd",defaultMessage:"collapse {title}"}:{id:"dTLq6E",defaultMessage:"expand {title}"},{title:t})})})}),[y,t,v]);return(0,c.Y)(i.nD3,{componentId:m,...g,dangerouslyAppendEmotionCSS:d({theme:y,getPrefixedClassName:x}),dangerouslySetAntdProps:{className:f,expandIconPosition:"left",expandIcon:w},defaultActiveKey:null!==b&&void 0!==b?b:void 0,onChange:h,children:(0,c.Y)(i.nD3.Panel,{header:t,children:(0,c.Y)(a.g,{showServerError:u,children:e.children})},"1")})}},14343:function(e,t,n){n.d(t,{k:function(){return i}});var r=n(89555),o=n(31014),a=n(32599),s=n(50111);const i=o.forwardRef(((e,t)=>{const{pressed:n,onClick:o,icon:i,onBlur:l,onFocus:c,onMouseEnter:d,onMouseLeave:u,componentId:p,analyticsEvents:h,type:f,...m}=e,{theme:g}=(0,a.u)(),b=(0,a.f)({componentType:a.h.Button,componentId:p,analyticsEvents:null!==h&&void 0!==h?h:[a.e.OnClick]});return(0,s.Y)("button",{onClick:e=>{b.onClick(e),null===o||void 0===o||o(e)},css:(0,r.AH)({cursor:"pointer",width:g.general.heightSm,height:g.general.heightSm,borderRadius:g.legacyBorders.borderRadiusMd,lineHeight:g.typography.lineHeightBase,padding:0,border:0,display:"flex",alignItems:"center",justifyContent:"center",background:n?g.colors.actionDefaultBackgroundPress:"transparent",color:n?g.colors.actionDefaultTextPress:g.colors.textSecondary,"&:hover":{background:g.colors.actionDefaultBackgroundHover,color:g.colors.actionDefaultTextHover}},""),ref:t,onBlur:l,onFocus:c,onMouseEnter:d,onMouseLeave:u,...m,children:i})}))},37616:function(e,t,n){n.d(t,{z7:function(){return p}});var r=n(17729),o=n(74702),a=n(42346);var s={'code[class*="language-"]':{fontFamily:'Consolas, Menlo, Monaco, "Andale Mono WT", "Andale Mono", "Lucida Console", "Lucida Sans Typewriter", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Liberation Mono", "Nimbus Mono L", "Courier New", Courier, monospace',fontSize:"14px",lineHeight:"1.375",direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",background:"#2a2734",color:"#5DFAFC"},'pre[class*="language-"]':{fontFamily:'Consolas, Menlo, Monaco, "Andale Mono WT", "Andale Mono", "Lucida Console", "Lucida Sans Typewriter", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Liberation Mono", "Nimbus Mono L", "Courier New", Courier, monospace',fontSize:"14px",lineHeight:"1.375",direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",background:"#2a2734",color:"#5DFAFC",padding:"1em",margin:".5em 0",overflow:"auto"},'pre > code[class*="language-"]':{fontSize:"1em"},'pre[class*="language-"]::-moz-selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"] ::-moz-selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"]::-moz-selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"] ::-moz-selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"]::selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"] ::selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"]::selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"] ::selection':{textShadow:"none",background:"#6a51e6"},':not(pre) > code[class*="language-"]':{padding:".1em",borderRadius:".3em"},comment:{color:"#6c6783"},prolog:{color:"#6c6783"},doctype:{color:"#6c6783"},cdata:{color:"#6c6783"},punctuation:{color:"#6c6783"},namespace:{Opacity:".7"},tag:{color:"#3AACE2"},operator:{color:"#3AACE2"},number:{color:"#3AACE2"},property:{color:"#5DFAFC"},function:{color:"#5DFAFC"},"tag-id":{color:"#eeebff"},selector:{color:"#eeebff"},"atrule-id":{color:"#eeebff"},"code.language-javascript":{color:"#c4b9fe"},"attr-name":{color:"#c4b9fe"},"code.language-css":{color:"#ffffff"},"code.language-scss":{color:"#ffffff"},boolean:{color:"#ffffff"},string:{color:"#ffffff"},entity:{color:"#ffffff",cursor:"help"},url:{color:"#ffffff"},".language-css .token.string":{color:"#ffffff"},".language-scss .token.string":{color:"#ffffff"},".style .token.string":{color:"#ffffff"},"attr-value":{color:"#ffffff"},keyword:{color:"#ffffff"},control:{color:"#ffffff"},directive:{color:"#ffffff"},unit:{color:"#ffffff"},statement:{color:"#ffffff"},regex:{color:"#ffffff"},atrule:{color:"#ffffff"},placeholder:{color:"#ffffff"},variable:{color:"#ffffff"},deleted:{textDecoration:"line-through"},inserted:{borderBottom:"1px dotted #eeebff",textDecoration:"none"},italic:{fontStyle:"italic"},important:{fontWeight:"bold",color:"#c4b9fe"},bold:{fontWeight:"bold"},"pre > code.highlight":{Outline:".4em solid #8a75f5",OutlineOffset:".4em"},".line-numbers.line-numbers .line-numbers-rows":{borderRightColor:"#2c2937"},".line-numbers .line-numbers-rows > span:before":{color:"#3c3949"},".line-highlight.line-highlight":{background:"linear-gradient(to right, rgba(224, 145, 66, 0.2) 70%, rgba(224, 145, 66, 0))"}};var i={'code[class*="language-"]':{textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",color:"rgb(77, 77, 76)",background:"#fafafa",fontFamily:"Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace",fontSize:"12px",lineHeight:"1.5em",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none"},'pre[class*="language-"]':{textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",color:"rgb(77, 77, 76)",background:"#fafafa",fontFamily:"Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace",fontSize:"12px",lineHeight:"1.5em",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",overflow:"auto",position:"relative",margin:"0.5em 0",padding:"1.25em 1em"},'code[class*="language-"]::-moz-selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"]::-moz-selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"] ::-moz-selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"] ::-moz-selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"]::selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"]::selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"] ::selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"] ::selection':{background:"#cceae7",color:"#263238"},':not(pre) > code[class*="language-"]':{whiteSpace:"normal",borderRadius:"0.2em",padding:"0.1em"},".language-css > code":{color:"#f5871f"},".language-sass > code":{color:"#f5871f"},".language-scss > code":{color:"#f5871f"},'[class*="language-"] .namespace':{Opacity:"0.7"},atrule:{color:"#7c4dff"},"attr-name":{color:"#39adb5"},"attr-value":{color:"#f6a434"},attribute:{color:"#f6a434"},boolean:{color:"#7c4dff"},builtin:{color:"#39adb5"},cdata:{color:"#39adb5"},char:{color:"#39adb5"},class:{color:"#39adb5"},"class-name":{color:"#6182b8"},comment:{color:"#8e908c"},constant:{color:"#7c4dff"},deleted:{color:"#e53935"},doctype:{color:"#aabfc9"},entity:{color:"#e53935"},function:{color:"#4271ae"},hexcode:{color:"#f5871f"},id:{color:"#7c4dff",fontWeight:"bold"},important:{color:"#7c4dff",fontWeight:"bold"},inserted:{color:"#39adb5"},keyword:{color:"#8959a8"},number:{color:"#f5871f"},operator:{color:"#3e999f"},prolog:{color:"#aabfc9"},property:{color:"#39adb5"},"pseudo-class":{color:"#f6a434"},"pseudo-element":{color:"#f6a434"},punctuation:{color:"rgb(77, 77, 76)"},regex:{color:"#6182b8"},selector:{color:"#e53935"},string:{color:"#3ba85f"},symbol:{color:"#7c4dff"},tag:{color:"#e53935"},unit:{color:"#f5871f"},url:{color:"#e53935"},variable:{color:"#c72d4c"}},l=n(9133),c=n(50111);r.A.registerLanguage("python",o.A),r.A.registerLanguage("json",a.A);const d="24px",u={light:i,duotoneDark:s};function p(e){let{theme:t="light",language:n,actions:o,style:a,children:s,showLineNumbers:i,lineNumberStyle:p,wrapLongLines:h}=e;const f={border:"none",borderRadius:0,margin:0,padding:d,...a};return(0,c.Y)(r.A,{showLineNumbers:i,lineNumberStyle:p,language:n,style:u[t],customStyle:f,codeTagProps:{style:(0,l.pick)(a,"backgroundColor")},wrapLongLines:h,children:s})}},40555:function(e,t,n){n.d(t,{f:function(){return u},y:function(){return l}});var r=n(89555),o=n(31014),a=n(37616),s=n(9133),i=n(50111);const l=e=>{let{json:t,wrapperStyle:n,overlayStyle:o,codeSnippetStyle:s}=e;const{formattedJson:l,isJsonContent:d}=c(t);return(0,i.Y)("div",{style:{position:"relative",maxHeight:"calc(1.5em * 9)",overflow:"hidden",...n},children:d?(0,i.FD)(i.FK,{children:[(0,i.Y)(a.z7,{language:"json",style:{padding:"5px",overflowX:"hidden",...s},children:l}),(0,i.Y)("div",{css:(0,r.AH)({position:"absolute",bottom:0,right:0,left:6,height:"2em",background:"linear-gradient(transparent, white)",...o},"")})]}):(0,i.Y)(i.FK,{children:t})})};function c(e){return o.useMemo((()=>{try{const t=JSON.parse(e),n=(0,s.isObject)(t)&&"function"!==typeof t&&!(t instanceof Date);return{formattedJson:n?JSON.stringify(t,null,2):e,isJsonContent:n}}catch(t){return{formattedJson:e,isJsonContent:!1}}}),[e])}var d={name:"1089mxj",styles:"white-space:pre-wrap"};const u=e=>{let{json:t}=e;const{formattedJson:n,isJsonContent:r}=c(t);return(0,i.Y)("div",{css:d,children:r?(0,i.Y)(a.z7,{language:"json",wrapLongLines:!0,children:n}):(0,i.Y)("span",{children:t})})}},46536:function(e,t,n){n.d(t,{_:function(){return s.A},b:function(){return c}});var r=n(48012),o=n(32599),a=n(31014),s=n(10298),i=n(50111);const l={rotateLeft:(0,i.Y)(r.ejX,{}),rotateRight:(0,i.Y)(r.UfX,{}),zoomIn:(0,i.Y)(r.ADv,{}),zoomOut:(0,i.Y)(r.LoD,{}),close:(0,i.Y)(o.C,{}),left:(0,i.Y)(r.A60,{}),right:(0,i.Y)(r.flY,{})},c=e=>{let{children:t,visible:n,onVisibleChange:r}=e;const{getPopupContainer:c}=(0,a.useContext)(o.al);return(0,i.Y)(s.A.PreviewGroup,{icons:l,preview:{visible:n,getContainer:c,onVisibleChange:e=>r(e)},children:t})}},54421:function(e,t,n){n.d(t,{Mc:function(){return u},Rn:function(){return f},TV:function(){return h},mQ:function(){return y}});var r=n(89555),o=n(32599),a=n(48012),s=n(88443),i=n(77484),l=n(31014),c=n(46536),d=n(50111);const u=200;var p={name:"49aokf",styles:"display:contents"};const h=e=>{let{imageUrl:t,compressedImageUrl:n,imageSize:a,maxImageSize:s}=e;const[i,u]=(0,l.useState)(!1),{theme:h}=(0,o.u)(),[f,m]=(0,l.useState)(!0);return(0,l.useEffect)((()=>{m(!0);const e=new window.Image;return e.onload=()=>m(!1),e.onerror=()=>m(!1),e.src=n,()=>{e.src=""}}),[n]),(0,d.Y)("div",{css:(0,r.AH)({width:a||"100%",height:a||"100%"},""),children:(0,d.Y)("div",{css:p,children:void 0===n||f?(0,d.Y)("div",{css:(0,r.AH)({width:"100%",backgroundColor:h.colors.backgroundSecondary,display:"flex",aspectRatio:"1",justifyContent:"center",alignItems:"center"},""),children:(0,d.Y)(o.S,{})}):(0,d.Y)("div",{css:(0,r.AH)({display:"flex",alignItems:"center",justifyContent:"center",width:a||"100%",aspectRatio:"1",maxWidth:s,maxHeight:s,backgroundColor:h.colors.backgroundSecondary,".rc-image":{cursor:"pointer"}},""),children:(0,d.Y)(c.b,{visible:i,onVisibleChange:u,children:(0,d.Y)(c._,{src:n,preview:{src:t},style:{maxWidth:s||"100%",maxHeight:s||"100%"}})})})})})},f=e=>{let{metadataByStep:t,imageSize:n,step:l,runUuid:c}=e;const{theme:u}=(0,o.u)();return void 0===t[l]?(0,d.FD)("div",{css:(0,r.AH)({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",width:n,backgroundColor:u.colors.backgroundSecondary,padding:u.spacing.md,aspectRatio:"1"},""),children:[(0,d.Y)(a.xfq,{}),(0,d.Y)(s.A,{id:"0XZ2zu",defaultMessage:"No image logged at this step"})]}):(0,d.Y)(h,{imageUrl:(0,i.To)(t[l].filepath,c),compressedImageUrl:(0,i.To)(t[l].compressed_filepath,c),imageSize:n})};var m={name:"1pf7ok1",styles:"display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%;width:100%;font-size:16px"},g={name:"yz1nei",styles:"margin-top:16px"},b={name:"18uqayh",styles:"margin-bottom:16px"};const y=()=>(0,d.FD)("div",{css:m,children:[(0,d.Y)(o.T.Title,{css:g,color:"secondary",level:3,children:"Compare logged images"}),(0,d.Y)(o.T.Text,{css:b,color:"secondary",children:"Use the image grid chart to compare logged images across runs."})]})},56412:function(e,t,n){n.d(t,{i:function(){return c}});var r=n(31014),o=n(88443),a=n(48012),s=n(32599),i=n(50111);var l={name:"1739oy8",styles:"z-index:1"};const c=e=>{let{copyText:t,showLabel:n=!0,componentId:c,...d}=e;const[u,p]=(0,r.useState)(!1);return(0,i.Y)(a.paO,{title:(0,i.Y)(o.A,{id:"X+boXI",defaultMessage:"Copied"}),dangerouslySetAntdProps:{visible:u},children:(0,i.Y)(s.B,{componentId:null!==c&&void 0!==c?c:"mlflow.shared.copy_button",type:"primary",onClick:()=>{navigator.clipboard.writeText(t),p(!0),setTimeout((()=>{p(!1)}),3e3)},onMouseLeave:()=>{p(!1)},css:l,children:n?(0,i.Y)(o.A,{id:"1Iq+NW",defaultMessage:"Copy"}):void 0,...d})})}}}]);
//# sourceMappingURL=9159.86329039.chunk.js.map