{"version": 3, "file": "static/js/9159.86329039.chunk.js", "mappings": "kcASO,MAAMA,EAAoBC,IAC/B,MAAOC,EAAqBC,IAA0BC,EAAAA,EAAAA,UAAoD,CAAC,IACpGC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,MAiCnC,OA/BAK,EAAAA,EAAAA,YAAU,KAuBJR,EAASS,OAAS,EAtBCC,WACrBL,GAAa,GACbE,EAAS,MAET,MAAMI,EAA4D,CAAC,EAEnE,UACQC,QAAQC,IACZb,EAASc,KAAIJ,UACX,MAAMK,GAAWC,EAAAA,EAAAA,IAAiBC,GAC5BC,QAAmBH,EAASI,QAClCR,EAAeM,GAAWC,CAAS,KAGvChB,EAAuBS,EACzB,CAAE,MAAOS,GACPb,EAASa,EACX,CAAC,QACCf,GAAa,EACf,GAIAgB,IAEAnB,EAAuB,CAAC,GACxBG,GAAa,GACf,GACC,CAACL,IAEG,CAAEC,sBAAqBG,YAAWE,QAAO,E,gDCrCS,IAAAgB,EAAA,CAAAC,KAAA,UAAAC,OAAA,gDAAAC,EAAA,CAAAF,KAAA,SAAAC,OAAA,kDAEpD,MAAME,EAAyBC,IAQ/B,IARgC,SACrC3B,EAAQ,SACR4B,EAAQ,SACRC,GAKDF,EACC,MAAM,MAAEG,IAAUC,EAAAA,EAAAA,MACXC,EAAcC,IAAmB9B,EAAAA,EAAAA,aAElC,oBAAEF,GAAwBF,EAAiBC,GAC3CkC,EChB2BjC,KAAoE,IAADkC,EAAAC,EAAAC,EACpG,MAAMrC,EAAWsC,OAAOC,KAAKtC,GAE7B,GAAwB,IAApBD,EAASS,OAAc,MAAO,GAElC,IAAIyB,EAAkD,QAAnCC,EAAGlC,EAAoBD,EAAS,WAAG,IAAAmC,GAAO,QAAPC,EAAhCD,EAAkCK,aAAK,IAAAJ,GACQ,QADRC,EAAvCD,EAClBtB,KAAK2B,GAA4BA,EAAKC,OAAS,KAAOD,EAAKE,cAAM,IAAAN,OADf,EAAhCA,EAElBO,QAAQD,GAAiC,OAATA,IAEpC,IAAKT,GAA8C,IAA3BA,EAAgBzB,OAAc,MAAO,GAE7D,IAAK,IAAIoC,EAAI,EAAGA,EAAI7C,EAASS,OAAQoC,IAAK,CAAC,IAADC,EAAAC,EAAAC,EACxC,MAAMC,EAAsD,QAAnCH,EAAG7C,EAAoBD,EAAS6C,WAAG,IAAAC,GAAO,QAAPC,EAAhCD,EAAkCN,aAAK,IAAAO,OAAP,EAAhCA,EAAyCjC,KAAK2B,GAAcA,EAAKE,OAE7F,GADAT,EAAiC,QAAlBc,EAAGd,SAAe,IAAAc,OAAA,EAAfA,EAAiBJ,QAAQD,GAAcM,EAAoBC,SAASP,KACvD,IAA3BT,EAAgBzB,OAClB,KAEJ,CAEA,OAAOyB,CAAe,EDHEiB,CAAmBlD,GAE3C,OAA+B,IAA3BiC,EAAgBzB,QAEhB2C,EAAAA,EAAAA,GAAA,MAAAC,UACED,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uCAOrBC,EAAAA,EAAAA,IAAA,OACEC,IAAGpC,EAID+B,SAAA,EAEFD,EAAAA,EAAAA,GAAA,OACEM,KAAGC,EAAAA,EAAAA,IAAE,CACHC,gBAAiB9B,EAAM+B,OAAOC,kBAC9BC,MAAOjC,EAAM+B,OAAOG,YACpBC,KAAM,SACNC,WAAY,SACZC,OAAQ,aAAarC,EAAM+B,OAAOO,UAClCC,UAAW,QACZ,IAAChB,UAEFD,EAAAA,EAAAA,GAACkB,EAAAA,EAAgB,CACfC,KAAMrC,EAAgBpB,KAAK6B,IAAY,CACrCY,GAAIZ,EACJ6B,OAAQxC,IAAiBW,EACzBpB,MAAMkD,EAAAA,EAAAA,IAAY9B,OAEpB+B,kBAAmBC,IAAA,IAAC,GAAEpB,GAAIoB,EAAA,OAAK1C,EAAgBsB,EAAG,OAGtDH,EAAAA,EAAAA,GAAA,OACEM,KAAGC,EAAAA,EAAAA,IAAE,CACHQ,OAAQ,aAAarC,EAAM+B,OAAOO,UAClCQ,WAAY,OACZC,QAAS,OACTC,cAAe,SACfC,SAAU,UACX,IAAC1B,UAEFD,EAAAA,EAAAA,GAAA,OAAKM,IAAGjC,EAA8D4B,SACnErD,EAASc,KAAI,CAACG,EAAS+D,KACtB5B,EAAAA,EAAAA,GAAA,OAEE6B,MAAO,CACLC,MAAO,GAAGrD,MACVsD,aAAc,aAAarD,EAAM+B,OAAOO,UACxCgB,QAAUpD,EAAkC,EAAnBF,EAAMuD,QAAQC,GACvCP,SAAU,OACVb,WAAY,UACZb,UAEFD,EAAAA,EAAAA,GAACmC,EAAAA,EAAgB,CACftE,QAASA,EACTuE,gBAAiB5D,EAASoD,GAAOS,YACjC9C,KAAMX,EACN0D,aAAc9D,EAASoD,GAAOU,gBAb3BzE,WAmBT,EEhEV,MAAM,QAAE0E,GAAYC,EAAAA,IAAW,IAAAtE,EAAA,CAAAC,KAAA,SAAAC,OAAA,oBAAAmD,EAAA,CAAApD,KAAA,UAAAC,OAAA,oBAAAC,EAAA,CAAAF,KAAA,UAAAC,OAAA,oBAAAqE,EAAA,CAAAtE,KAAA,SAAAC,OAAA,oBAkB/B,MAAMsE,UAAuBC,EAAAA,UAI3BC,WAAAA,CAAYC,GACVC,MAAMD,GAAO,KAJfE,uBAAiB,OACjBC,wBAAkB,EAIhBC,KAAKC,MAAQ,CACXC,WAAY,KACZC,mBAAmB,EACnBC,iBAAiB,EACjBC,oBAAoB,GAEtBL,KAAKM,gBAAkBN,KAAKM,gBAAgBC,KAAKP,MACjDA,KAAKQ,+BAAiCR,KAAKQ,+BAA+BD,KAAKP,MAE/EA,KAAKD,mBAAqBU,EAAAA,YAC1BT,KAAKF,kBAAoBW,EAAAA,WAC3B,CAEAH,eAAAA,CAAgBI,GACd,MAAMC,EAAQX,KAAKD,mBAAmBa,QACtC,GAAc,OAAVD,EAAgB,CAClB,MAAME,EAAiBF,EAAMG,YAC7Bd,KAAKe,SAAS,CAAEb,WAAYW,GAC9B,CACF,CAEAL,8BAAAA,CAA+BE,GAC7B,MAAMM,EAAShB,KAAKF,kBAAkBc,QAAQK,iBAAiB,sBAC/DD,EAAOE,SAAQ,CAACC,EAAQxC,KACtB,MAAMyC,EAAQJ,EAAOrC,GACjByC,IAAUV,EAAEW,SACdD,EAAME,WAAaZ,EAAEW,OAAOC,WAC9B,GAEJ,CAEAC,iBAAAA,GACE,MAAMC,EAAYxB,KAAKJ,MAAM6B,KAAKC,cAChC,CAAAxE,GAAA,SAEEC,eAAe,gCAEjB,CACEwE,KAAM3B,KAAKJ,MAAMrE,SAASnB,SAG9BwH,EAAAA,EAAMC,gBAAgBL,GAEtBM,OAAOC,iBAAiB,SAAU/B,KAAKM,iBAAiB,GACxDwB,OAAOE,cAAc,IAAIC,MAAM,UACjC,CAEAC,oBAAAA,GAEEJ,OAAOK,oBAAoB,SAAUnC,KAAKM,iBAAiB,EAC7D,CAEA8B,mBAAAA,GAEE,IAAI5G,EADgB,IAWpB,OAP8B,OAA1BwE,KAAKC,MAAMC,aAEb1E,EAAW6G,KAAKC,MAAMtC,KAAKC,MAAMC,YAAcF,KAAKJ,MAAMrE,SAASnB,OAAS,IACxEoB,EAPc,MAQhBA,EARgB,MAWbA,CACT,CAEA+G,4BAAAA,GACE,MAAM,YAAEC,GAAgBxC,KAAKJ,MACvB6C,EAAoBb,EAAAA,EAAMc,qBAAqBd,EAAAA,EAAMe,oBAAoBH,IAC/E,OAAOxC,KAAKJ,MAAMrE,SAASd,KAAIa,IAAgC,IAA/B,aAAE+D,EAAY,QAAEzE,GAASU,EAEvD,MAAM,KAAEJ,EAAI,SAAE0H,GAAaH,EAAkBpD,GAC7C,OACEtC,EAAAA,EAAAA,GAAA,MAAI8F,UAAU,YAAW7F,UACvBD,EAAAA,EAAAA,GAAC+F,EAAAA,GAAI,CAACC,GAAIC,EAAAA,EAAOC,uBAAuB5D,GAAe6D,MAAOhI,EAAK8B,SAChE4F,KAF0BhI,EAI1B,GAGX,CAEAuI,sBAAAA,GACE,OAAOnD,KAAKJ,MAAMwD,cAAchJ,OAAS,CAC3C,CAEAiJ,2BAAAA,GACE,OAAOrD,KAAKJ,MAAM0D,8BAAgCtD,KAAKmD,wBACzD,CAEAI,qBAAAA,CAAsBlE,EAAmBmE,GACvC,OAAOzG,EAAAA,EAAAA,GAAC+F,EAAAA,GAAI,CAACC,GAAIC,EAAAA,EAAOC,uBAAuB5D,GAAcrC,SAAEwG,GACjE,CAEAC,iCAAAA,CAAkCC,GAChC,OACE3G,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,oDAGfwG,OAAQ,CAAED,mBAGhB,CAEAE,6BAAAA,CAA8BR,GAC5B,OACErG,EAAAA,EAAAA,GAAC+F,EAAAA,GAAI,CAACC,GAAIC,EAAAA,EAAOa,+BAA+BT,GAAepG,SAC5DgD,KAAKyD,kCAAkCL,EAAchJ,SAG5D,CAEA0J,iBAAAA,GACE,MAAM,sBAAEC,EAAqB,6BAAET,EAA4B,cAAEF,EAAa,YAAEZ,GAAgBxC,KAAKJ,MAGjG,OAAKwD,EAAc,IAAOZ,EAAY,GAIlCc,EACKtD,KAAK4D,8BAA8BG,GAGxC/D,KAAKmD,yBACAnD,KAAK4D,8BAA8BR,GAGrCpD,KAAKuD,sBAAsBH,EAAc,GAAIZ,EAAY,GAAGtH,MAX1D,EAYX,CAEA8I,QAAAA,GACE,OAAOhE,KAAKmD,0BACVpG,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,6DAGfwG,OAAQ,CACNM,QAASjE,KAAKJ,MAAMrE,SAASnB,OAC7BsJ,eAAgB1D,KAAKJ,MAAMwD,cAAchJ,WAI7C2C,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,6CAEfwG,OAAQ,CACNM,QAASjE,KAAKJ,MAAMrE,SAASnB,SAIrC,CAEA8J,gBAAAA,CAAiB1I,GACf,MAAM2I,EAAWnE,KAAKoE,eACpBpE,KAAKJ,MAAMyE,WACX7I,EAEAwE,KAAKC,MAAME,mBACX,GACA,CAACmE,EAAUpG,IAAcoG,IACxBC,IACC,IACE,MAAMC,EAAYC,EAAsBF,GAGxC,MAAyB,kBAAdC,GAAwC,OAAdA,EAC5BxE,KAAK0E,iBAAiBF,GAEtBD,CAEX,CAAE,MAAO7D,GACP,OAAO6D,CACT,KAGJ,OAAwB,IAApBJ,EAAS/J,QAET2C,EAAAA,EAAAA,GAAA,MAAAC,UACED,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iCAOrBJ,EAAAA,EAAAA,GAAA,SACE8F,UAAU,wCACVxF,IAAGpC,EACH0J,SAAU3E,KAAKQ,+BAA+BxD,UAE9CD,EAAAA,EAAAA,GAAA,SAAAC,SAAQmH,KAGd,CAEAO,gBAAAA,CAAiBF,GACf,OAAOzH,EAAAA,EAAAA,GAAA,OAAAC,SAAM4H,KAAKC,UAAUL,EAAW,KAAM,IAC/C,CAEAM,iBAAAA,CAAkBtJ,EAAe4H,GAC/B,MAAMe,EAAWnE,KAAKoE,eACpBpE,KAAKJ,MAAMmF,YACXvJ,EAEAwE,KAAKC,MAAMI,oBACX,GACA,CAACiE,EAAKpG,KAEFd,EAAAA,EAAAA,IAAC0F,EAAAA,GAAI,CACHC,GAAIC,EAAAA,EAAOgC,mBACThF,KAAKJ,MAAMrE,SAASd,KAAKwK,GAASA,EAAKrK,UAAS2B,QAAO,CAAC2I,EAAMC,SAAsBC,IAAdlH,EAAKiH,KAC3Eb,EACAlB,GAEFF,MAAM,aAAYlG,SAAA,CAEjBsH,GACDvH,EAAAA,EAAAA,GAAA,KAAG8F,UAAU,oBAAoBxF,IAAGiB,QAI1CsD,EAAAA,EAAMyD,cAER,OAAwB,IAApBlB,EAAS/J,QAET2C,EAAAA,EAAAA,GAAA,MAAAC,UACED,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,8BAOrBJ,EAAAA,EAAAA,GAAA,SACE8F,UAAU,wCACVxF,IAAGjC,EACHuJ,SAAU3E,KAAKQ,+BAA+BxD,UAE9CD,EAAAA,EAAAA,GAAA,SAAAC,SAAQmH,KAGd,CAEAmB,mBAAAA,CAAoB9J,GAClB,OAAOuB,EAAAA,EAAAA,GAAC1B,EAAsB,CAAC1B,SAAUqG,KAAKJ,MAAMjG,SAAU4B,SAAUyE,KAAKJ,MAAMrE,SAAUC,SAAUA,GACzG,CAEA+J,cAAAA,CAAe/J,GACb,MAAM2I,EAAWnE,KAAKoE,eACpBpE,KAAKJ,MAAM4F,SACXhK,EAEAwE,KAAKC,MAAMG,iBACX,GAEF,OAAwB,IAApB+D,EAAS/J,QAET2C,EAAAA,EAAAA,GAAA,MAAAC,UACED,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,2BAOrBJ,EAAAA,EAAAA,GAAA,SACE8F,UAAU,wCACVxF,IAAGmC,EACHmF,SAAU3E,KAAKQ,+BAA+BxD,UAE9CD,EAAAA,EAAAA,GAAA,SAAAC,SAAQmH,KAGd,CAEAsB,cAAAA,CAAeC,GACb,MAAMC,GACJ5I,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAcbyI,EAAiB5F,KAAKJ,MAAMrE,SAASd,KAVhBoL,IACzB,MAAMC,EAAYD,EAAQC,UACpBC,EAAUF,EAAQE,QACxB,MAAO,CACLnL,QAASiL,EAAQjL,QACjBkL,UAAWA,EAAYlE,EAAAA,EAAMoE,gBAAgBF,EAAW9F,KAAKJ,MAAM6B,MAAQkE,EAC3EI,QAASA,EAAUnE,EAAAA,EAAMoE,gBAAgBD,EAAS/F,KAAKJ,MAAM6B,MAAQkE,EACrEM,SAAUH,GAAaC,EAAUnE,EAAAA,EAAMsE,YAAYJ,EAAWC,GAAWJ,EAC1E,IAmCH,MAhCa,CACX,CACErB,IAAK,YACLpB,OACEnG,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAInBe,KAAM0H,EAAenL,KAAI0L,IAAA,IAAC,QAAEvL,EAAO,UAAEkL,GAAWK,EAAA,MAAK,CAACvL,EAASkL,EAAU,KAE3E,CACExB,IAAK,UACLpB,OACEnG,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAInBe,KAAM0H,EAAenL,KAAI2L,IAAA,IAAC,QAAExL,EAAO,QAAEmL,GAASK,EAAA,MAAK,CAACxL,EAASmL,EAAQ,KAEvE,CACEzB,IAAK,WACLpB,OACEnG,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAInBe,KAAM0H,EAAenL,KAAI4L,IAAA,IAAC,QAAEzL,EAAO,SAAEqL,GAAUI,EAAA,MAAK,CAACzL,EAASqL,EAAS,MAG/DxL,KAAI6L,IAAA,IAAC,IAAEhC,EAAG,MAAEpB,EAAK,KAAEhF,GAAMoI,EAAA,OACnClJ,EAAAA,EAAAA,IAAA,MAAAJ,SAAA,EACED,EAAAA,EAAAA,GAAA,MAAIwJ,MAAM,MAAM1D,UAAU,2BAA2BxF,IAAKqI,EAAc1I,SACrEkG,IAEFhF,EAAKzD,KAAI+L,IAAA,IAAE5L,EAAS2J,GAAMiC,EAAA,OACzBzJ,EAAAA,EAAAA,GAAA,MAAI8F,UAAU,aAAqCxF,IAAKqI,EAAc1I,UACpED,EAAAA,EAAAA,GAAC0J,EAAAA,IAAa,CACZvD,MAAOqB,EAEP7G,MAAM,OACNgJ,UAAU,UACVC,aAAc,CAAEC,SAAU,SAE1BC,wBAAyB,CAAEC,gBAAiB,GAAI9J,SAE/CuH,KAV2B3J,EAY3B,MAjBA0J,EAmBJ,GAET,CAEAyC,MAAAA,GACE,MAAM,cAAE3D,GAAkBpD,KAAKJ,OACzB,SAAErE,EAAQ,SAAEyL,EAAQ,WAAE3C,EAAU,YAAEU,EAAW,SAAEpL,GAAaqG,KAAKJ,MAEjEpE,EAAWwE,KAAKoC,sBAChBsD,EAAgB1F,KAAKiH,cAAczL,GAEnC0H,EAAQlD,KAAKgE,WAEnB,IAAIkD,EAAc,CAAClH,KAAK8D,qBAExB,MAAMqD,EAAcnH,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SAChDC,eAAe,eAIXiK,EAAepH,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SACjDC,eAAe,YAIXkK,EAAiBrH,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SACnDC,eAAe,cAIXmK,EAAYtH,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SAC9CC,eAAe,SAGXoK,EAAgBvH,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SAClDC,eAAe,mBAMXqK,IAAuBC,EAAAA,EAAAA,MAE7B,OACErK,EAAAA,EAAAA,IAAA,OAAKyF,UAAU,iBAAiB6E,IAAK1H,KAAKF,kBAAkB9C,SAAA,EAC1DD,EAAAA,EAAAA,GAAC4K,EAAAA,EAAU,CAACzE,MAAOA,EAAOgE,YAAaA,EAAaU,WAAW,OAC9DJ,IACCzK,EAAAA,EAAAA,GAAC8K,EAAAA,EAAkB,CACjB3E,MAAOlD,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SACnCC,eAAe,mBAEdH,UAEHI,EAAAA,EAAAA,IAACmC,EAAAA,IAAU,CAAAvC,SAAA,EACTD,EAAAA,EAAAA,GAACuC,EAAO,CACNwI,KACE/K,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,8BAIlBH,UAGDD,EAAAA,EAAAA,GAACgL,EAAAA,GAA4B,CAACpO,SAAUqG,KAAKJ,MAAMjG,YAF/C,8BAINoD,EAAAA,EAAAA,GAACuC,EAAO,CACNwI,KACE/K,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAGlBH,UAGDD,EAAAA,EAAAA,GAACiL,EAAAA,EAAiB,CAACrO,SAAUqG,KAAKJ,MAAMjG,SAAUsO,gBAAiBjI,KAAKJ,MAAMqI,mBAF1E,iBAINlL,EAAAA,EAAAA,GAACuC,EAAO,CACNwI,KACE/K,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,aAGlBH,UAGDD,EAAAA,EAAAA,GAACmL,EAAAA,EAAa,CACZvO,SAAUA,EACV4B,SAAUA,EACV8I,WAAYA,EACZU,YAAaA,KANX,aASNhI,EAAAA,EAAAA,GAACuC,EAAO,CACNwI,KACE/K,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAGlBH,UAGDD,EAAAA,EAAAA,GAACoL,EAAAA,EAAiB,CAACxO,SAAUqG,KAAKJ,MAAMjG,SAAUsO,gBAAiBjI,KAAKJ,MAAMqI,mBAF1E,sBAOZlL,EAAAA,EAAAA,GAAC8K,EAAAA,EAAkB,CACjB3E,MAAOlD,KAAKJ,MAAM6B,KAAKC,cAAc,CAAAxE,GAAA,SACnCC,eAAe,gBAEdH,UAEHI,EAAAA,EAAAA,IAAA,SACEyF,UAAU,wCACV6E,IAAK1H,KAAKD,mBACV4E,SAAU3E,KAAKQ,+BAA+BxD,SAAA,EAE9CD,EAAAA,EAAAA,GAAA,SAAAC,UACEI,EAAAA,EAAAA,IAAA,MAAAJ,SAAA,EACED,EAAAA,EAAAA,GAAA,MAAIwJ,MAAM,MAAM1D,UAAU,2BAA2BxF,IAAKqI,EAAc1I,UACtED,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAIlB6C,KAAKJ,MAAMrE,SAASd,KAAK2N,IAAC,IAAAC,EAAAC,EAAA,OACzBvL,EAAAA,EAAAA,GAAA,MAAIwJ,MAAM,MAAM1D,UAAU,aAA6BxF,IAAKqI,EAAc1I,UACxED,EAAAA,EAAAA,GAAC0J,EAAAA,IAAa,CACZvD,MAAOkF,EAAExN,QAET8C,MAAM,OACNgJ,UAAU,UACVC,aAAc,CAAEC,SAAU,SAC1BE,gBAAiB,EAAI9J,UAErBD,EAAAA,EAAAA,GAAC+F,EAAAA,GAAI,CAACC,GAAIC,EAAAA,EAAOuF,gBAA8B,QAAfF,EAACD,EAAE/I,oBAAY,IAAAgJ,EAAAA,EAAI,IAAc,QAAXC,EAAEF,EAAExN,eAAO,IAAA0N,EAAAA,EAAI,IAAItL,SAAEoL,EAAExN,aATrCwN,EAAExN,QAWzC,UAIXwC,EAAAA,EAAAA,IAAA,SAAAJ,SAAA,EACEI,EAAAA,EAAAA,IAAA,MAAAJ,SAAA,EACED,EAAAA,EAAAA,GAAA,MAAIwJ,MAAM,MAAM1D,UAAU,2BAA2BxF,IAAKqI,EAAc1I,UACtED,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAIlB6J,EAASvM,KAAI,CAAC+N,EAAShM,KAEpBO,EAAAA,EAAAA,GAAA,MAAI8F,UAAU,aAAuCxF,IAAKqI,EAAc1I,UACtED,EAAAA,EAAAA,GAAA,OAAK8F,UAAU,4BAA2B7F,UACxCD,EAAAA,EAAAA,GAAC0J,EAAAA,IAAa,CACZvD,MAAOsF,EAEP9K,MAAM,OACNgJ,UAAU,UACVC,aAAc,CAAEC,SAAU,SAC1BE,gBAAiB,EAAI9J,SAEpBwL,OAVyBjN,EAASiB,GAAG5B,cAiBjDoF,KAAKyF,eAAeC,GACpB1F,KAAKqD,gCACJjG,EAAAA,EAAAA,IAAA,MAAAJ,SAAA,EACED,EAAAA,EAAAA,GAAA,MAAIwJ,MAAM,MAAM1D,UAAU,aAAY7F,UACpCD,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uBAKlB6C,KAAKuC,2CAMhBnF,EAAAA,EAAAA,IAACyK,EAAAA,EAAkB,CAAC3E,MAAOiE,EAAYnK,SAAA,EACrCD,EAAAA,EAAAA,GAAC0L,EAAAA,IAAM,CACLC,YAAY,+EACZC,MAAOpB,EACP,aAAY,CAACJ,EAAaI,GAAeqB,KAAK,OAE9CC,QAAS7I,KAAKC,MAAME,kBACpB2I,SAAUA,CAACD,EAASnI,IAAMV,KAAKe,SAAS,CAAEZ,kBAAmB0I,OAE/D9L,EAAAA,EAAAA,GAACgM,EAAAA,EAAM,CAACC,KAAK,OACZhJ,KAAKkE,iBAAiB1I,OAEzB4B,EAAAA,EAAAA,IAACyK,EAAAA,EAAkB,CAAC3E,MAAOkE,EAAapK,SAAA,EACtCD,EAAAA,EAAAA,GAAC0L,EAAAA,IAAM,CACLC,YAAY,+EACZC,MAAOpB,EACP,aAAY,CAACH,EAAcG,GAAeqB,KAAK,OAE/CC,QAAS7I,KAAKC,MAAMI,mBACpByI,SAAUA,CAACD,EAASnI,IAAMV,KAAKe,SAAS,CAAEV,mBAAoBwI,OAEhE9L,EAAAA,EAAAA,GAACgM,EAAAA,EAAM,CAACC,KAAK,OACZhJ,KAAK8E,kBAAkBtJ,EAAU4H,OAEpCrG,EAAAA,EAAAA,GAAC8K,EAAAA,EAAkB,CAAC3E,MAAOmE,EAAerK,SAAEgD,KAAKsF,oBAAoB9J,MACrE4B,EAAAA,EAAAA,IAACyK,EAAAA,EAAkB,CAAC3E,MAAOoE,EAAUtK,SAAA,EACnCD,EAAAA,EAAAA,GAAC0L,EAAAA,IAAM,CACLC,YAAY,+EACZC,MAAOpB,EACP,aAAY,CAACD,EAAWC,GAAeqB,KAAK,OAE5CC,QAAS7I,KAAKC,MAAMG,gBACpB0I,SAAUA,CAACD,EAASnI,IAAMV,KAAKe,SAAS,CAAEX,gBAAiByI,OAE7D9L,EAAAA,EAAAA,GAACgM,EAAAA,EAAM,CAACC,KAAK,OACZhJ,KAAKuF,eAAe/J,QAI7B,CAEAyL,aAAAA,CAAcpI,GACZ,MAAO,CACLA,MAAO,GAAGA,MACVoK,SAAU,GAAGpK,MACb+H,SAAU,GAAG/H,MAEjB,CAGAuF,cAAAA,CACE8E,EACA1N,EACA2N,GAIC,IAHDC,EAAaC,UAAAjP,OAAA,QAAAgL,IAAAiE,UAAA,IAAAA,UAAA,GACbC,EAASD,UAAAjP,OAAA,QAAAgL,IAAAiE,UAAA,GAAAA,UAAA,GAAG,CAAC/E,EAAUpG,IAAcoG,EACrCiF,EAASF,UAAAjP,OAAA,QAAAgL,IAAAiE,UAAA,GAAAA,UAAA,GAAI9E,GAAeA,EAG5B,MAAMrI,EAAOsN,EAAAA,EAAeC,QAAQP,GAC9BhL,EAAO,CAAC,EAGdhC,EAAKgF,SAASwI,GAAOxL,EAAKwL,GAAK,CAAE/F,OAAQgG,MAAMT,EAAK9O,QAAQwP,UAAKxE,MACjE8D,EAAKhI,SAAQ,CAAC2I,EAAcrN,KAE1BqN,EAAQ3I,SAASkH,GAAYlK,EAAKkK,EAAE9D,KAAKX,OAAOnH,GAAK4L,EAAE7D,OAAO,IAGhErI,EAAKgF,SAASwI,IAAOxL,SAAKwL,GAAGI,SARPnG,EAQ8BzF,EAAKwL,GAAG/F,QARfoG,MAAMC,GAAWA,IAAMrG,EAAO,KAArDA,KAQ8C,IAEpE,MAAM+B,EAAgB1F,KAAKiH,cAAczL,GAEzC,OACEU,EAEGK,QAAQmN,IAAOP,GAAgBjL,EAAKwL,GAAGI,UACvCrP,KAAKiP,IAEJ,MAAM,OAAE/F,EAAM,QAAEmG,GAAY5L,EAAKwL,GAC3BO,EAAWb,GAAiBU,EAAU,gBAAa1E,EACzD,OACEhI,EAAAA,EAAAA,IAAA,MAAYyF,UAAWoH,EAASjN,SAAA,EAC9BD,EAAAA,EAAAA,GAAA,MAAIwJ,MAAM,MAAM1D,UAAU,2BAA2BxF,IAAKqI,EAAc1I,SACrEsM,EAAUI,EAAG/F,KAEfA,EAAOlJ,KAAI,CAAC8J,EAAY/H,KACvB,MAAM0N,OAAqB9E,IAAVb,EAAsB,GAAKgF,EAAUhF,GACtD,OACExH,EAAAA,EAAAA,GAAA,MAAI8F,UAAU,aAAkDxF,IAAKqI,EAAc1I,UACjFD,EAAAA,EAAAA,GAAC0J,EAAAA,IAAa,CACZvD,MAAOgH,EAEPxM,MAAM,OACNgJ,UAAU,UACVC,aAAc,CAAEC,SAAU,SAC1BE,gBAAiB,EAAI9J,UAErBD,EAAAA,EAAAA,GAAA,QAAM8F,UAAU,4BAA2B7F,SAAEkN,OATjBlK,KAAKJ,MAAMrE,SAASiB,GAAG5B,QAWlD,MAlBF8O,EAqBJ,GAIf,EAGF,MA8CMjF,EAAyBF,IAC7B,IACE,MAAM4F,EAAa5F,EAAM6F,QAAQ,KAAM,KACvC,OAAOxF,KAAKyF,MAAMF,EACpB,CAAE,MAAOzJ,GACP,OAAO,IACT,GAGF,OAAe4J,EAAAA,EAAAA,KAvDSC,CAACtK,EAAYuK,KACnC,MAAM,sBAAEzG,EAAqB,6BAAET,GAAiCrD,EAAMwK,mBAChElP,EAAgB,GAChBwJ,EAAmB,GACnBV,EAAkB,GAClBmB,EAAgB,GAChBwB,EAAgB,GAChBiB,EAAuB,IACvB,cAAE7E,EAAa,SAAEzJ,GAAa6Q,EAC9BhI,EAAcY,EAAc3I,KAAK4E,IAAsBqL,EAAAA,EAAAA,IAAcrL,EAAcY,KAmBzF,OAlBAtG,EAASuH,SAAStG,IAChB,MAAMiL,GAAU8E,EAAAA,EAAAA,IAAW/P,EAASqF,GAEpC,IAAK4F,EACH,OAEFtK,EAASqP,KAAK/E,GACdd,EAAY6F,KAAK3O,OAAO0H,QAAOkH,EAAAA,EAAAA,IAAiBjQ,EAASqF,KACzDoE,EAAWuG,KAAK3O,OAAO0H,QAAOmH,EAAAA,EAAAA,IAAUlQ,EAASqF,KACjD,MAAM8K,GAAUC,EAAAA,EAAAA,IAAWpQ,EAASqF,GAC9BgL,EAAcrJ,EAAAA,EAAMsJ,oBAAoBH,GAAStQ,KAAI0Q,IAAA,IAAE7G,EAAKC,GAAM4G,EAAA,MAAM,CAC5E7G,MACAC,QACD,IACDiB,EAASoF,KAAKK,GACdhD,EAAgB2C,KAAKhJ,EAAAA,EAAMwJ,kBAAkBvF,EAASjL,IACtDoM,EAAS4D,KAAKhJ,EAAAA,EAAMyJ,WAAWxF,GAAS,IAEnC,CACLrD,cACAjH,WACAwJ,cACAV,aACAmB,WACAwB,WACAiB,kBACAlE,wBACAT,+BACD,GAiBH,EAAwCgI,EAAAA,EAAAA,IAAW7L,I,sDCltBnD,MAAM8L,UAA2B7L,EAAAA,UAG/BC,WAAAA,CAAYC,GACVC,MAAMD,GAAO,KAHf4L,gBAAU,OAOVvL,MAEI,CACFwL,kBAAcrG,GANdpF,KAAKwL,WAAa,EACpB,CAQAE,gBAAAA,GACE,OAAO1L,KAAKJ,MAAMwD,cAAc3I,KAAK4E,IACnC,MAAMsM,GAAsBC,EAAAA,EAAAA,MAI5B,OAHA5L,KAAKJ,MACFiM,UAASC,EAAAA,EAAAA,IAAiBzM,EAAcsM,IACxCI,OAAON,GAAuCzL,KAAKe,SAAS,CAAE0K,mBAC1DE,CAAmB,GAE9B,CAEApK,iBAAAA,GACEvB,KAAKwL,WAAWZ,QAAQ5K,KAAK0L,oBAC7B1L,KAAKJ,MAAMjG,SAASuH,SAAStG,IAC3B,MAAMoR,GAAYJ,EAAAA,EAAAA,MAClB5L,KAAKwL,WAAWZ,KAAKoB,GAErBhM,KAAKJ,MAAMiM,UAASI,EAAAA,EAAAA,IAAUrR,EAASoR,IAAYD,OAAON,IACxDzL,KAAKe,SAAS,CAAE0K,gBAAe,GAC/B,GAEN,CAEA1E,MAAAA,GAEE,GAAI/G,KAAKC,MAAMwL,aAAc,CAAC,IAADS,EAC3B,MAAM,aAAET,GAAiBzL,KAAKC,MAE9B,MADqBwL,aAAwBU,MAAQV,EAAe,IAAIU,MAAkC,QAA7BD,EAACT,EAAaW,uBAAe,IAAAF,OAAA,EAA5BA,EAAAG,KAAAZ,GAEhF,CACA,OACE1O,EAAAA,EAAAA,GAACuP,EAAAA,EAAa,CAAAtP,UACZD,EAAAA,EAAAA,GAACwP,EAAAA,GACC,CACAC,oBAAkB,EAClBhB,WAAYxL,KAAKwL,WAAWxO,UAE5BD,EAAAA,EAAAA,GAAC0C,EAAc,CAAC9F,SAAUqG,KAAKJ,MAAMjG,SAAUyJ,cAAepD,KAAKJ,MAAMwD,mBAIjF,EAOF,MAAMqJ,EAAaC,IACjB,MAAMC,EAAaC,mBAAmBF,GACtC,OAAIA,IAAQC,EACHF,EAAUE,GAEZA,CAAU,EAoBjB,IAAA1R,EAAA,CAAAC,KAAA,SAAAC,OAAA,sEAEF,MAeM0R,GAAiBC,EAAAA,EAAAA,IAAexC,EAAAA,EAAAA,KAlCdC,CAACtK,EAAYuK,KACnC,IACE,MAAM,SAAEuC,GAAavC,EACfwC,EAAwBP,EAAUM,EAASE,QAC3CC,EAAeC,IAAAA,MAASH,GAExBrT,EAAWiL,KAAKyF,MAAM6C,EAAa,UAGzC,MAAO,CAAE9J,cADawB,KAAKyF,MAAM6C,EAA0B,aACnCvT,WAC1B,CAAE,MAAO+G,GACP,GAAIA,aAAa0M,YACf,MAAM,IAAIA,YAAY,4BAA4B1M,EAAE2M,WAGtD,MAAM3M,CACR,IAkBoC4J,CAAyBiB,IAE/D,OAAe+B,EAAAA,EAAAA,GACbC,EAAAA,EAAWC,eAAeC,aAC1BZ,OACAzH,GApBkC9J,IAAA,IAAC,MAAErB,GAAyBqB,EAAA,OAC9DyB,EAAAA,EAAAA,GAAA,OAAKM,IAAGpC,EAAsF+B,UAC5FD,EAAAA,EAAAA,GAAC2Q,EAAAA,IAAK,CACJxK,OACEnG,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,0CAInBwQ,YAAa1T,EAAMoT,QACnBO,OAAO7Q,EAAAA,EAAAA,GAAC8Q,EAAAA,EAAU,OAEhB,G,yIC7GR,MAAMC,EAAqBxS,IAGgD,IAH/C,MAC1BG,EAAK,qBACLsS,GACmEzS,EACnE,MAAM0S,EAAYD,EAAqB,YAEjCE,EAAY,IAAID,SAChBE,EAAc,IAAIF,WAClBG,EAAkB,IAAIH,gBAE5B,MAAO,CACLI,SAAU,GACV,CAAC,OAAOH,OAAeC,KAAgB,CACrCG,YAAa,EACbC,WAAY,GACZC,cAAe,GACf/P,QAAS,OACTgQ,WAAY,SACZJ,SAAU,GACVK,WAAY,SACZC,WAAYjT,EAAMkT,WAAWC,cAE/B,CAACT,GAAkB,CACjBpP,QAAS,GAAGtD,EAAMuD,QAAQ6P,UAAUpT,EAAMuD,QAAQC,UAErD,EAGI,SAAS4I,EAAmBjI,GACjC,MAAM,MACJsD,EAAK,UACL4L,EAAS,gBACTC,EAAe,iBACfC,EAAgB,SAChBlG,EAAQ,UACRjG,EAAS,YACT6F,EAAc,6CACZ9I,EAIEqP,EAAgBH,GAAa,CAAEI,UAAW,CAAC,MAC3CC,EAAmBH,EAAmB,KAAO,CAAC,MAE9C,MAAEvT,EAAK,qBAAEsS,IAAyBrS,EAAAA,EAAAA,MAClC,cAAEgG,IAAkB0N,EAAAA,EAAAA,KAEpBC,GAAgBC,EAAAA,EAAAA,cACpBrU,IAAA,IAAC,SAAEsU,GAAkCtU,EAAA,OACnC8B,EAAAA,EAAAA,GAAA,OACEM,KAAKmS,EAAAA,EAAAA,GAAa,CAAE3Q,MAAOpD,EAAMgU,QAAQC,WAAa,EAAGC,UAAWJ,EAAW,qBAAkBnK,IAAapI,UAE9GD,EAAAA,EAAAA,GAAC6S,EAAAA,EAAgB,CACfvS,KAAGC,EAAAA,EAAAA,IAAE,CACHuS,IAAK,CAAEhR,MAAOpD,EAAMgU,QAAQC,WAAa,EAAGI,OAAQrU,EAAMgU,QAAQC,WAAa,IAChF,IACD,aAEMhO,EADJ6N,EAEM,CAAArS,GAAA,SACEC,eAAe,oBAMjB,CAAAD,GAAA,SACEC,eAAe,kBAJjB,CAAE+F,aAWR,GAER,CAACzH,EAAOyH,EAAOxB,IAGjB,OACE3E,EAAAA,EAAAA,GAACgT,EAAAA,IAAS,CACRrH,YAAaA,KACTuG,EACJe,4BAA6BlC,EAAmB,CAAErS,QAAOsS,yBACzDlH,wBAAyB,CACvBhE,YACAoN,mBAAoB,OACpBC,WAAYb,GAEdF,iBAAkC,OAAhBA,QAAgB,IAAhBA,EAAAA,OAAoB/J,EACtC0D,SAAUA,EAAS9L,UAEnBD,EAAAA,EAAAA,GAACgT,EAAAA,IAAUI,MAAK,CAACC,OAAQlN,EAAMlG,UAC7BD,EAAAA,EAAAA,GAACsT,EAAAA,EAAoB,CAACtB,gBAAiBA,EAAgB/R,SAAE4C,EAAM5C,YAD7B,MAK1C,C,wGClGA,MAAMsT,EAAmB7P,EAAAA,YACvB,CAACb,EAA8B8H,KAC7B,MAAM,QACJ6I,EAAO,QACPC,EAAO,KACPC,EAAI,OACJC,EAAM,QACNC,EAAO,aACPC,EAAY,aACZC,EAAY,YACZnI,EAAW,gBACXoI,EAAe,KACfC,KACGC,GACDpR,GACE,MAAEnE,IAAUC,EAAAA,EAAAA,KAEZuV,GAAeC,EAAAA,EAAAA,GAAuC,CAC1DC,cAAeC,EAAAA,EAAwCC,OACvD3I,cACAoI,gBAAgC,OAAfA,QAAe,IAAfA,EAAAA,EAAmB,CAACQ,EAAAA,EAA6CC,WAGpF,OACExU,EAAAA,EAAAA,GAAA,UACEyT,QAAUgB,IACRP,EAAaT,QAAQgB,GACd,OAAPhB,QAAO,IAAPA,GAAAA,EAAUgB,EAAM,EAElBnU,KAAGC,EAAAA,EAAAA,IAAE,CACHmU,OAAQ,UACR5S,MAAOpD,EAAMgU,QAAQiC,SACrB5B,OAAQrU,EAAMgU,QAAQiC,SACtBC,aAAclW,EAAMmW,cAAcC,eAClCnD,WAAYjT,EAAMkT,WAAWmD,eAC7B/S,QAAS,EACTjB,OAAQ,EACRU,QAAS,OACTgQ,WAAY,SACZuD,eAAgB,SAChBC,WAAYzB,EAAU9U,EAAM+B,OAAOyU,6BAA+B,cAClEvU,MAAO6S,EAAU9U,EAAM+B,OAAO0U,uBAAyBzW,EAAM+B,OAAO2U,cACpE,UAAW,CACTH,WAAYvW,EAAM+B,OAAO4U,6BACzB1U,MAAOjC,EAAM+B,OAAO6U,yBAEvB,IACD3K,IAAKA,EACLgJ,OAAQA,EACRC,QAASA,EACTC,aAAcA,EACdC,aAAcA,KACVG,EAAchU,SAEjByT,GACM,G,8FCqJf,MA7NmC,CACjC,2BAA4B,CAC1B6B,WACE,8NACFlE,SAAU,OACVM,WAAY,QACZ6D,UAAW,MACXC,UAAW,OACX3U,WAAY,MACZ4U,YAAa,SACbC,UAAW,SACXC,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,OACTjB,WAAY,UACZtU,MAAO,WAET,0BAA2B,CACzB4U,WACE,8NACFlE,SAAU,OACVM,WAAY,QACZ6D,UAAW,MACXC,UAAW,OACX3U,WAAY,MACZ4U,YAAa,SACbC,UAAW,SACXC,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,OACTjB,WAAY,UACZtU,MAAO,UACPqB,QAAS,MACTmU,OAAQ,SACRxU,SAAU,QAEZ,iCAAkC,CAChC0P,SAAU,OAEZ,0CAA2C,CACzC+E,WAAY,OACZnB,WAAY,WAEd,2CAA4C,CAC1CmB,WAAY,OACZnB,WAAY,WAEd,2CAA4C,CAC1CmB,WAAY,OACZnB,WAAY,WAEd,4CAA6C,CAC3CmB,WAAY,OACZnB,WAAY,WAEd,qCAAsC,CACpCmB,WAAY,OACZnB,WAAY,WAEd,sCAAuC,CACrCmB,WAAY,OACZnB,WAAY,WAEd,sCAAuC,CACrCmB,WAAY,OACZnB,WAAY,WAEd,uCAAwC,CACtCmB,WAAY,OACZnB,WAAY,WAEd,uCAAwC,CACtCjT,QAAS,OACT4S,aAAc,QAEhByB,QAAS,CACP1V,MAAO,WAET2V,OAAQ,CACN3V,MAAO,WAET4V,QAAS,CACP5V,MAAO,WAET6V,MAAO,CACL7V,MAAO,WAET8V,YAAa,CACX9V,MAAO,WAET+V,UAAW,CACTC,QAAS,MAEXC,IAAK,CACHjW,MAAO,WAETkW,SAAU,CACRlW,MAAO,WAETmW,OAAQ,CACNnW,MAAO,WAEToW,SAAU,CACRpW,MAAO,WAETqW,SAAU,CACRrW,MAAO,WAET,SAAU,CACRA,MAAO,WAETsW,SAAU,CACRtW,MAAO,WAET,YAAa,CACXA,MAAO,WAET,2BAA4B,CAC1BA,MAAO,WAET,YAAa,CACXA,MAAO,WAET,oBAAqB,CACnBA,MAAO,WAET,qBAAsB,CACpBA,MAAO,WAETuW,QAAS,CACPvW,MAAO,WAETwW,OAAQ,CACNxW,MAAO,WAETyW,OAAQ,CACNzW,MAAO,UACP+T,OAAQ,QAEV2C,IAAK,CACH1W,MAAO,WAET,8BAA+B,CAC7BA,MAAO,WAET,+BAAgC,CAC9BA,MAAO,WAET,uBAAwB,CACtBA,MAAO,WAET,aAAc,CACZA,MAAO,WAET2W,QAAS,CACP3W,MAAO,WAET4W,QAAS,CACP5W,MAAO,WAET6W,UAAW,CACT7W,MAAO,WAET8W,KAAM,CACJ9W,MAAO,WAET+W,UAAW,CACT/W,MAAO,WAETgX,MAAO,CACLhX,MAAO,WAETiX,OAAQ,CACNjX,MAAO,WAETkX,YAAa,CACXlX,MAAO,WAETmX,SAAU,CACRnX,MAAO,WAEToX,QAAS,CACPC,eAAgB,gBAElBC,SAAU,CACRlW,aAAc,qBACdiW,eAAgB,QAElBE,OAAQ,CACNC,UAAW,UAEbC,UAAW,CACT1G,WAAY,OACZ/Q,MAAO,WAET0X,KAAM,CACJ3G,WAAY,QAEd,uBAAwB,CACtB4G,QAAS,qBACTC,cAAe,QAEjB,gDAAiD,CAC/CC,iBAAkB,WAEpB,iDAAkD,CAChD7X,MAAO,WAET,iCAAkC,CAChCsU,WAAY,kFCXhB,MA5M6B,CAC3B,2BAA4B,CAC1BQ,UAAW,OACX3U,WAAY,MACZ4U,YAAa,SACbC,UAAW,SACX8C,SAAU,SACV9X,MAAO,kBACPsU,WAAY,UACZM,WAAY,mEACZlE,SAAU,OACVM,WAAY,QACZiE,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,QAEX,0BAA2B,CACzBT,UAAW,OACX3U,WAAY,MACZ4U,YAAa,SACbC,UAAW,SACX8C,SAAU,SACV9X,MAAO,kBACPsU,WAAY,UACZM,WAAY,mEACZlE,SAAU,OACVM,WAAY,QACZiE,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,OACTvU,SAAU,OACV+W,SAAU,WACVvC,OAAQ,UACRnU,QAAS,cAEX,2CAA4C,CAC1CiT,WAAY,UACZtU,MAAO,WAET,0CAA2C,CACzCsU,WAAY,UACZtU,MAAO,WAET,4CAA6C,CAC3CsU,WAAY,UACZtU,MAAO,WAET,2CAA4C,CAC1CsU,WAAY,UACZtU,MAAO,WAET,sCAAuC,CACrCsU,WAAY,UACZtU,MAAO,WAET,qCAAsC,CACpCsU,WAAY,UACZtU,MAAO,WAET,uCAAwC,CACtCsU,WAAY,UACZtU,MAAO,WAET,sCAAuC,CACrCsU,WAAY,UACZtU,MAAO,WAET,uCAAwC,CACtCG,WAAY,SACZ8T,aAAc,QACd5S,QAAS,SAEX,uBAAwB,CACtBrB,MAAO,WAET,wBAAyB,CACvBA,MAAO,WAET,wBAAyB,CACvBA,MAAO,WAET,kCAAmC,CACjCgW,QAAS,OAEXiB,OAAQ,CACNjX,MAAO,WAET,YAAa,CACXA,MAAO,WAET,aAAc,CACZA,MAAO,WAETgY,UAAW,CACThY,MAAO,WAETuW,QAAS,CACPvW,MAAO,WAETiY,QAAS,CACPjY,MAAO,WAET6V,MAAO,CACL7V,MAAO,WAETkY,KAAM,CACJlY,MAAO,WAETmY,MAAO,CACLnY,MAAO,WAET,aAAc,CACZA,MAAO,WAET0V,QAAS,CACP1V,MAAO,WAEToY,SAAU,CACRpY,MAAO,WAEToX,QAAS,CACPpX,MAAO,WAET4V,QAAS,CACP5V,MAAO,WAETyW,OAAQ,CACNzW,MAAO,WAETqW,SAAU,CACRrW,MAAO,WAETqY,QAAS,CACPrY,MAAO,WAETR,GAAI,CACFQ,MAAO,UACP+Q,WAAY,QAEd0G,UAAW,CACTzX,MAAO,UACP+Q,WAAY,QAEduG,SAAU,CACRtX,MAAO,WAET2W,QAAS,CACP3W,MAAO,WAETmW,OAAQ,CACNnW,MAAO,WAETkW,SAAU,CACRlW,MAAO,WAET2V,OAAQ,CACN3V,MAAO,WAEToW,SAAU,CACRpW,MAAO,WAET,eAAgB,CACdA,MAAO,WAET,iBAAkB,CAChBA,MAAO,WAET8V,YAAa,CACX9V,MAAO,mBAETgX,MAAO,CACLhX,MAAO,WAETsW,SAAU,CACRtW,MAAO,WAETwW,OAAQ,CACNxW,MAAO,WAETsY,OAAQ,CACNtY,MAAO,WAETiW,IAAK,CACHjW,MAAO,WAET8W,KAAM,CACJ9W,MAAO,WAET0W,IAAK,CACH1W,MAAO,WAETmX,SAAU,CACRnX,MAAO,Y,qBC3MXuY,EAAAA,EAAkBC,iBAAiB,SAAUC,EAAAA,GAC7CF,EAAAA,EAAkBC,iBAAiB,OAAQE,EAAAA,GASpC,MAKMC,EAAiB,OAExBC,EAA8C,CAClDC,MAAOC,EACPC,YAAaC,GA4CR,SAASC,EAAWrb,GASL,IATM,MAC1BG,EAAQ,QAAO,SACfmb,EAAQ,QACRC,EAAO,MACPjY,EAAK,SACL5B,EAAQ,gBACR8Z,EAAe,gBACfC,EAAe,cACfC,GACiB1b,EACjB,MAAM2b,EAAc,CAClBnZ,OAAQ,OACR6T,aAAc,EACduB,OAAQ,EACRnU,QAASsX,KACNzX,GAGL,OACE7B,EAAAA,EAAAA,GAACkZ,EAAAA,EAAiB,CAChBa,gBAAiBA,EACjBC,gBAAiBA,EACjBH,SAAUA,EACVhY,MAAO0X,EAAa7a,GACpBwb,YAAaA,EACbC,aAAc,CACZtY,OAAOuY,EAAAA,EAAAA,MAAKvY,EAAO,oBAErBoY,cAAeA,EAAcha,SAE5BA,GAGP,C,yICzFO,MAAMoa,EAA0C9b,IAA6D,IAA5D,KAAE8a,EAAI,aAAEiB,EAAY,aAAE1Q,EAAY,iBAAE2Q,GAAkBhc,EAC5G,MAAM,cAAEic,EAAa,cAAEC,GAAkBC,EAAiBrB,GAsB1D,OACErZ,EAAAA,EAAAA,GAAA,OAAK6B,MAAO,CApBZ6W,SAAU,WACViC,UAAW,kBACXhZ,SAAU,YAkB+B2Y,GAAera,SACrDwa,GACCpa,EAAAA,EAAAA,IAAAua,EAAAA,GAAA,CAAA3a,SAAA,EACED,EAAAA,EAAAA,GAAC4Z,EAAAA,GAAW,CAACC,SAAS,OAAOhY,MAAO,CAR1CG,QAAS,MACT6Y,UAAW,YAOgEN,GAAmBta,SACrFua,KAEHxa,EAAAA,EAAAA,GAAA,OAAKM,KAAGC,EAAAA,EAAAA,IAAE,CApBhBmY,SAAU,WACVoC,OAAQ,EACRC,MAAO,EACPC,KAAM,EACNjI,OAAQ,MACRkC,WAAY,yCAeiCrL,GAAc,UAGvD5J,EAAAA,EAAAA,GAAA4a,EAAAA,GAAA,CAAA3a,SAAGoZ,KAED,EAIV,SAASqB,EAAiBrB,GACxB,OAAO3V,EAAAA,SAAc,KACnB,IACE,MAAMuX,EAASpT,KAAKyF,MAAM+L,GACpB6B,GAASC,EAAAA,EAAAA,UAASF,IAA6B,oBAAXA,KAA2BA,aAAkBG,MACvF,MAAO,CACLZ,cAAeU,EAASrT,KAAKC,UAAUmT,EAAQ,KAAM,GAAK5B,EAC1DoB,cAAeS,EAEnB,CAAE,MAAOvX,GACP,MAAO,CACL6W,cAAenB,EACfoB,eAAe,EAEnB,IACC,CAACpB,GACN,CAAC,IAAA9X,EAAA,CAAApD,KAAA,UAAAC,OAAA,wBAEM,MAAMid,EAAmDnd,IAAe,IAAd,KAAEmb,GAAMnb,EACvE,MAAM,cAAEsc,EAAa,cAAEC,GAAkBC,EAAiBrB,GAE1D,OACErZ,EAAAA,EAAAA,GAAA,OAAKM,IAAGiB,EAA6BtB,SAClCwa,GACCza,EAAAA,EAAAA,GAAC4Z,EAAAA,GAAW,CAACC,SAAS,OAAOI,eAAa,EAAAha,SACvCua,KAGHxa,EAAAA,EAAAA,GAAA,QAAAC,SAAOoZ,KAEL,C,4IClEV,MAAMiC,EAAQ,CACZC,YAAYvb,EAAAA,EAAAA,GAACwb,EAAAA,IAAQ,IACrBC,aAAazb,EAAAA,EAAAA,GAAC0b,EAAAA,IAAQ,IACtBC,QAAQ3b,EAAAA,EAAAA,GAAC4b,EAAAA,IAAU,IACnBC,SAAS7b,EAAAA,EAAAA,GAAC8b,EAAAA,IAAW,IACrBC,OAAO/b,EAAAA,EAAAA,GAACgc,EAAAA,EAAS,IACjBhB,MAAMhb,EAAAA,EAAAA,GAACic,EAAAA,IAAa,IACpBlB,OAAO/a,EAAAA,EAAAA,GAACkc,EAAAA,IAAc,KAGXC,EAAoB5d,IAQ1B,IAR2B,SAChC0B,EAAQ,QACRmc,EAAO,gBACPC,GAKD9d,EACC,MAAM,kBAAE+d,IAAsBC,EAAAA,EAAAA,YAAWC,EAAAA,IAEzC,OACExc,EAAAA,EAAAA,GAACyc,EAAAA,EAAQC,aAAY,CACnBpB,MAAOA,EACPqB,QAAS,CACPP,QAASA,EACTQ,aAAcN,EACdD,gBAAkBQ,GAAMR,EAAgBQ,IACxC5c,SAEDA,GACoB,C,6NCjCpB,MAAM6c,EAAsB,IAAI,IAAA5e,EAAA,CAAAC,KAAA,SAAAC,OAAA,oBAShC,MAAM2e,EAAYxe,IAAgF,IAA/E,SAAEye,EAAQ,mBAAEC,EAAkB,UAAEC,EAAS,aAAEC,GAA8B5e,EACjG,MAAO6e,EAAgBC,IAAqBtgB,EAAAA,EAAAA,WAAS,IAC/C,MAAE2B,IAAUC,EAAAA,EAAAA,MAEX2e,EAAcC,IAAmBxgB,EAAAA,EAAAA,WAAS,GAcjD,OAZAK,EAAAA,EAAAA,YAAU,KAERmgB,GAAgB,GAChB,MAAMC,EAAM,IAAIzY,OAAO0Y,MAIvB,OAHAD,EAAIE,OAAS,IAAMH,GAAgB,GACnCC,EAAIG,QAAU,IAAMJ,GAAgB,GACpCC,EAAII,IAAMX,EACH,KACLO,EAAII,IAAM,EAAE,CACb,GACA,CAACX,KAGFjd,EAAAA,EAAAA,GAAA,OAAKM,KAAGC,EAAAA,EAAAA,IAAE,CAAEuB,MAAOob,GAAa,OAAQnK,OAAQmK,GAAa,QAAQ,IAACjd,UACpED,EAAAA,EAAAA,GAAA,OAAKM,IAAGpC,EAA0B+B,cACRoI,IAAvB4U,GAAoCK,GACnCtd,EAAAA,EAAAA,GAAA,OACEM,KAAGC,EAAAA,EAAAA,IAAE,CACHuB,MAAO,OACPtB,gBAAiB9B,EAAM+B,OAAOod,oBAC9Bpc,QAAS,OACTqc,YAAa,IACb9I,eAAgB,SAChBvD,WAAY,UACb,IAACxR,UAEFD,EAAAA,EAAAA,GAAC+d,EAAAA,EAAO,OAGV/d,EAAAA,EAAAA,GAAA,OACEM,KAAGC,EAAAA,EAAAA,IAAE,CACHkB,QAAS,OACTgQ,WAAY,SACZuD,eAAgB,SAChBlT,MAAOob,GAAa,OACpBY,YAAa,IACbjU,SAAUsT,EACVxC,UAAWwC,EACX3c,gBAAiB9B,EAAM+B,OAAOod,oBAC9B,YAAa,CACXnJ,OAAQ,YAEX,IAACzU,UAEFD,EAAAA,EAAAA,GAACmc,EAAAA,EAAiB,CAACC,QAASgB,EAAgBf,gBAAiBgB,EAAkBpd,UAC7ED,EAAAA,EAAAA,GAACyd,EAAAA,EAAK,CACJG,IAAKX,EACLN,QAAS,CAAEiB,IAAKZ,GAChBnb,MAAO,CAAEgI,SAAUsT,GAAgB,OAAQxC,UAAWwC,GAAgB,iBAM5E,EAIGa,EAAuBzc,IAU7B,IAV8B,eACnC0c,EAAc,UACdf,EAAS,KACTgB,EAAI,QACJrgB,GAMD0D,EACC,MAAM,MAAE7C,IAAUC,EAAAA,EAAAA,KAElB,YAA6B0J,IAAzB4V,EAAeC,IAEf7d,EAAAA,EAAAA,IAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHkB,QAAS,OACTC,cAAe,SACf+P,WAAY,SACZuD,eAAgB,SAChBS,UAAW,SACX3T,MAAOob,EACP1c,gBAAiB9B,EAAM+B,OAAOod,oBAC9B7b,QAAStD,EAAMuD,QAAQC,GACvB4b,YAAa,KACd,IAAC7d,SAAA,EAEFD,EAAAA,EAAAA,GAACme,EAAAA,IAAS,KACVne,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qCAOrBJ,EAAAA,EAAAA,GAAC+c,EAAS,CACRC,UAAUoB,EAAAA,EAAAA,IAAuBH,EAAeC,GAAMG,SAAUxgB,GAChEof,oBAAoBmB,EAAAA,EAAAA,IAAuBH,EAAeC,GAAMI,oBAAqBzgB,GACrFqf,UAAWA,GACX,EAEJ,IAAA7e,EAAA,CAAAF,KAAA,UAAAC,OAAA,sHAAAqE,EAAA,CAAAtE,KAAA,SAAAC,OAAA,mBAAAgL,EAAA,CAAAjL,KAAA,UAAAC,OAAA,sBAEK,MAAMmgB,EAAqBA,KAE9Ble,EAAAA,EAAAA,IAAA,OACEC,IAAGjC,EAQD4B,SAAA,EAEFD,EAAAA,EAAAA,GAACwe,EAAAA,EAAWC,MAAK,CAACne,IAAGmC,EAAqB9B,MAAM,YAAY+d,MAAO,EAAEze,SAAC,2BAGtED,EAAAA,EAAAA,GAACwe,EAAAA,EAAWG,KAAI,CAACre,IAAG8I,EAAwBzI,MAAM,YAAWV,SAAC,qE,mHChJgB,IAAA/B,EAAA,CAAAC,KAAA,UAAAC,OAAA,aAQ7E,MAAMwgB,EAAargB,IAAmF,IAAlF,SAAEsgB,EAAQ,UAAEC,GAAY,EAAI,YAAEnT,KAAgBoT,GAA8BxgB,EACrG,MAAOygB,EAAaC,IAAkBliB,EAAAA,EAAAA,WAAS,GAc/C,OACEiD,EAAAA,EAAAA,GAAC0J,EAAAA,IAAa,CACZvD,OACEnG,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,WAEnC0J,wBAAyB,CACvBsS,QAAS4C,GACT/e,UAEFD,EAAAA,EAAAA,GAACsU,EAAAA,EAAM,CACL3I,YAAwB,OAAXA,QAAW,IAAXA,EAAAA,EAAe,4BAC5BqI,KAAK,UACLP,QAxBcyL,KAClBC,UAAUC,UAAUC,UAAUR,GAC9BI,GAAe,GACfK,YAAW,KACTL,GAAe,EAAM,GACpB,IAAK,EAoBJnL,aAjBmByL,KACvBN,GAAe,EAAM,EAiBjB3e,IAAGpC,EAEH+B,SACE6e,GAAY9e,EAAAA,EAAAA,GAACE,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,cAAsDiI,KAEjG0W,KAEQ,C", "sources": ["experiment-tracking/components/experiment-page/hooks/useRunsArtifacts.tsx", "experiment-tracking/components/CompareRunArtifactView.tsx", "experiment-tracking/components/experiment-page/utils/getCommonArtifacts.ts", "experiment-tracking/components/CompareRunView.tsx", "experiment-tracking/components/CompareRunPage.tsx", "common/components/CollapsibleSection.tsx", "common/components/ToggleIconButton.tsx", "shared/web-shared/snippet/theme/databricks-duotone-dark.ts", "shared/web-shared/snippet/theme/databricks-light.ts", "shared/web-shared/snippet/index.tsx", "common/components/JsonFormatting.tsx", "shared/building_blocks/Image.tsx", "experiment-tracking/components/runs-charts/components/charts/ImageGridPlot.common.tsx", "shared/building_blocks/CopyButton.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { listArtifactsApi } from '../../../actions';\nimport { ArtifactListFilesResponse } from '../../../types';\n\n/**\n * Fetches artifacts given a list of run UUIDs\n * @param runUuids List of run UUIDs\n * @returns Object containing artifacts keyed by run UUID\n */\nexport const useRunsArtifacts = (runUuids: string[]) => {\n  const [artifactsKeyedByRun, setArtifactsKeyedByRun] = useState<Record<string, ArtifactListFilesResponse>>({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    const fetchArtifacts = async () => {\n      setIsLoading(true);\n      setError(null);\n\n      const artifactsByRun: Record<string, ArtifactListFilesResponse> = {};\n\n      try {\n        await Promise.all(\n          runUuids.map(async (runUuid) => {\n            const response = listArtifactsApi(runUuid);\n            const artifacts = (await response.payload) as ArtifactListFilesResponse;\n            artifactsByRun[runUuid] = artifacts;\n          }),\n        );\n        setArtifactsKeyedByRun(artifactsByRun);\n      } catch (err: any) {\n        setError(err);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (runUuids.length > 0) {\n      fetchArtifacts();\n    } else {\n      setArtifactsKeyedByRun({});\n      setIsLoading(false);\n    }\n  }, [runUuids]);\n\n  return { artifactsKeyedByRun, isLoading, error };\n};\n", "import { useState } from 'react';\nimport ShowArtifactPage from './artifact-view-components/ShowArtifactPage';\nimport { RunInfoEntity } from '../types';\nimport { useRunsArtifacts } from './experiment-page/hooks/useRunsArtifacts';\nimport { getCommonArtifacts } from './experiment-page/utils/getCommonArtifacts';\nimport { useDesignSystemTheme } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { ArtifactViewTree } from './ArtifactViewTree';\nimport { getBasename } from '../../common/utils/FileUtils';\n\nexport const CompareRunArtifactView = ({\n  runUuids,\n  runInfos,\n  colWidth,\n}: {\n  runUuids: string[];\n  runInfos: RunInfoEntity[];\n  colWidth: number;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const [artifactPath, setArtifactPath] = useState<string | undefined>();\n\n  const { artifactsKeyedByRun } = useRunsArtifacts(runUuids);\n  const commonArtifacts = getCommonArtifacts(artifactsKeyedByRun);\n\n  if (commonArtifacts.length === 0) {\n    return (\n      <h2>\n        <FormattedMessage\n          defaultMessage=\"No common artifacts to display.\"\n          description=\"Text shown when there are no common artifacts between the runs\"\n        />\n      </h2>\n    );\n  }\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexDirection: 'row',\n        height: '100vh',\n      }}\n    >\n      <div\n        css={{\n          backgroundColor: theme.colors.backgroundPrimary,\n          color: theme.colors.textPrimary,\n          flex: '1 1 0%',\n          whiteSpace: 'nowrap',\n          border: `1px solid ${theme.colors.grey300}`,\n          overflowY: 'auto',\n        }}\n      >\n        <ArtifactViewTree\n          data={commonArtifacts.map((path: string) => ({\n            id: path,\n            active: artifactPath === path,\n            name: getBasename(path),\n          }))}\n          onToggleTreebeard={({ id }) => setArtifactPath(id)}\n        />\n      </div>\n      <div\n        css={{\n          border: `1px solid ${theme.colors.grey300}`,\n          borderLeft: 'none',\n          display: 'flex',\n          flexDirection: 'column',\n          overflow: 'hidden',\n        }}\n      >\n        <div css={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>\n          {runUuids.map((runUuid, index) => (\n            <div\n              key={runUuid}\n              style={{\n                width: `${colWidth}px`,\n                borderBottom: `1px solid ${theme.colors.grey300}`,\n                padding: !artifactPath ? theme.spacing.md : 0,\n                overflow: 'auto',\n                whiteSpace: 'nowrap',\n              }}\n            >\n              <ShowArtifactPage\n                runUuid={runUuid}\n                artifactRootUri={runInfos[index].artifactUri}\n                path={artifactPath}\n                experimentId={runInfos[index].experimentId}\n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import { ArtifactFileInfo, ArtifactListFilesResponse } from '../../../types';\n\n/**\n * Gets the list of artifacts that are present in all runs in the given list of runs.\n * @param artifactsKeyedByRun Object containing artifacts keyed by run UUID\n * @returns List of common artifacts\n */\nexport const getCommonArtifacts = (artifactsKeyedByRun: Record<string, ArtifactListFilesResponse>) => {\n  const runUuids = Object.keys(artifactsKeyedByRun);\n\n  if (runUuids.length === 0) return [];\n\n  let commonArtifacts = artifactsKeyedByRun[runUuids[0]]?.files\n    ?.map((file: ArtifactFileInfo) => (file.is_dir ? null : file.path))\n    ?.filter((path: string | null) => path !== null);\n\n  if (!commonArtifacts || commonArtifacts.length === 0) return [];\n\n  for (let i = 1; i < runUuids.length; i++) {\n    const currentRunArtifacts = artifactsKeyedByRun[runUuids[i]]?.files?.map((file: any) => file.path);\n    commonArtifacts = commonArtifacts?.filter((path: any) => currentRunArtifacts.includes(path));\n    if (commonArtifacts.length === 0) {\n      break;\n    }\n  }\n\n  return commonArtifacts;\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport { connect } from 'react-redux';\nimport { injectIntl, FormattedMessage, type IntlShape } from 'react-intl';\nimport { Spacer, Switch, LegacyTabs, LegacyTooltip } from '@databricks/design-system';\n\nimport { getExperiment, getParams, getRunInfo, getRunTags } from '../reducers/Reducers';\nimport './CompareRunView.css';\nimport { CompareRunScatter } from './CompareRunScatter';\nimport { CompareRunBox } from './CompareRunBox';\nimport CompareRunContour from './CompareRunContour';\nimport Routes from '../routes';\nimport { Link } from '../../common/utils/RoutingUtils';\nimport { getLatestMetrics } from '../reducers/MetricReducer';\nimport CompareRunUtil from './CompareRunUtil';\nimport Utils from '../../common/utils/Utils';\nimport ParallelCoordinatesPlotPanel from './ParallelCoordinatesPlotPanel';\nimport { PageHeader } from '../../shared/building_blocks/PageHeader';\nimport { CollapsibleSection } from '../../common/components/CollapsibleSection';\nimport { shouldDisableLegacyRunCompareCharts } from '../../common/utils/FeatureUtils';\nimport { RunInfoEntity } from '../types';\nimport { CompareRunArtifactView } from './CompareRunArtifactView';\n\nconst { TabPane } = LegacyTabs;\n\ntype CompareRunViewProps = {\n  experiments: any[]; // TODO: PropTypes.instanceOf(Experiment)\n  experimentIds: string[];\n  comparedExperimentIds?: string[];\n  hasComparedExperimentsBefore?: boolean;\n  runInfos: RunInfoEntity[];\n  runUuids: string[];\n  metricLists: any[][];\n  paramLists: any[][];\n  tagLists: any[][];\n  runNames: string[];\n  runDisplayNames: string[];\n  intl: IntlShape;\n};\n\ntype CompareRunViewState = any;\nclass CompareRunView extends Component<CompareRunViewProps, CompareRunViewState> {\n  compareRunViewRef: any;\n  runDetailsTableRef: any;\n\n  constructor(props: CompareRunViewProps) {\n    super(props);\n    this.state = {\n      tableWidth: null,\n      onlyShowParamDiff: false,\n      onlyShowTagDiff: false,\n      onlyShowMetricDiff: false,\n    };\n    this.onResizeHandler = this.onResizeHandler.bind(this);\n    this.onCompareRunTableScrollHandler = this.onCompareRunTableScrollHandler.bind(this);\n\n    this.runDetailsTableRef = React.createRef();\n    this.compareRunViewRef = React.createRef();\n  }\n\n  onResizeHandler(e: any) {\n    const table = this.runDetailsTableRef.current;\n    if (table !== null) {\n      const containerWidth = table.clientWidth;\n      this.setState({ tableWidth: containerWidth });\n    }\n  }\n\n  onCompareRunTableScrollHandler(e: any) {\n    const blocks = this.compareRunViewRef.current.querySelectorAll('.compare-run-table');\n    blocks.forEach((_: any, index: any) => {\n      const block = blocks[index];\n      if (block !== e.target) {\n        block.scrollLeft = e.target.scrollLeft;\n      }\n    });\n  }\n\n  componentDidMount() {\n    const pageTitle = this.props.intl.formatMessage(\n      {\n        description: 'Page title for the compare runs page',\n        defaultMessage: 'Comparing {runs} MLflow Runs',\n      },\n      {\n        runs: this.props.runInfos.length,\n      },\n    );\n    Utils.updatePageTitle(pageTitle);\n\n    window.addEventListener('resize', this.onResizeHandler, true);\n    window.dispatchEvent(new Event('resize'));\n  }\n\n  componentWillUnmount() {\n    // Avoid registering `onResizeHandler` every time this component mounts\n    window.removeEventListener('resize', this.onResizeHandler, true);\n  }\n\n  getTableColumnWidth() {\n    const minColWidth = 200;\n    let colWidth = minColWidth;\n\n    // @ts-expect-error TS(4111): Property 'tableWidth' comes from an index signatur... Remove this comment to see the full error message\n    if (this.state.tableWidth !== null) {\n      // @ts-expect-error TS(4111): Property 'tableWidth' comes from an index signatur... Remove this comment to see the full error message\n      colWidth = Math.round(this.state.tableWidth / (this.props.runInfos.length + 1));\n      if (colWidth < minColWidth) {\n        colWidth = minColWidth;\n      }\n    }\n    return colWidth;\n  }\n\n  renderExperimentNameRowItems() {\n    const { experiments } = this.props;\n    const experimentNameMap = Utils.getExperimentNameMap(Utils.sortExperimentsById(experiments));\n    return this.props.runInfos.map(({ experimentId, runUuid }) => {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      const { name, basename } = experimentNameMap[experimentId];\n      return (\n        <td className=\"meta-info\" key={runUuid}>\n          <Link to={Routes.getExperimentPageRoute(experimentId)} title={name}>\n            {basename}\n          </Link>\n        </td>\n      );\n    });\n  }\n\n  hasMultipleExperiments() {\n    return this.props.experimentIds.length > 1;\n  }\n\n  shouldShowExperimentNameRow() {\n    return this.props.hasComparedExperimentsBefore || this.hasMultipleExperiments();\n  }\n\n  getExperimentPageLink(experimentId: any, experimentName: any) {\n    return <Link to={Routes.getExperimentPageRoute(experimentId)}>{experimentName}</Link>;\n  }\n\n  getCompareExperimentsPageLinkText(numExperiments: any) {\n    return (\n      <FormattedMessage\n        defaultMessage=\"Displaying Runs from {numExperiments} Experiments\"\n        // eslint-disable-next-line max-len\n        description=\"Breadcrumb nav item to link to compare-experiments page on compare runs page\"\n        values={{ numExperiments }}\n      />\n    );\n  }\n\n  getCompareExperimentsPageLink(experimentIds: any) {\n    return (\n      <Link to={Routes.getCompareExperimentsPageRoute(experimentIds)}>\n        {this.getCompareExperimentsPageLinkText(experimentIds.length)}\n      </Link>\n    );\n  }\n\n  getExperimentLink() {\n    const { comparedExperimentIds, hasComparedExperimentsBefore, experimentIds, experiments } = this.props;\n\n    // Do not attempt to construct experiment links if they are not loaded\n    if (!experimentIds[0] || !experiments[0]) {\n      return '';\n    }\n\n    if (hasComparedExperimentsBefore) {\n      return this.getCompareExperimentsPageLink(comparedExperimentIds);\n    }\n\n    if (this.hasMultipleExperiments()) {\n      return this.getCompareExperimentsPageLink(experimentIds);\n    }\n\n    return this.getExperimentPageLink(experimentIds[0], experiments[0].name);\n  }\n\n  getTitle() {\n    return this.hasMultipleExperiments() ? (\n      <FormattedMessage\n        defaultMessage=\"Comparing {numRuns} Runs from {numExperiments} Experiments\"\n        // eslint-disable-next-line max-len\n        description=\"Breadcrumb title for compare runs page with multiple experiments\"\n        values={{\n          numRuns: this.props.runInfos.length,\n          numExperiments: this.props.experimentIds.length,\n        }}\n      />\n    ) : (\n      <FormattedMessage\n        defaultMessage=\"Comparing {numRuns} Runs from 1 Experiment\"\n        description=\"Breadcrumb title for compare runs page with single experiment\"\n        values={{\n          numRuns: this.props.runInfos.length,\n        }}\n      />\n    );\n  }\n\n  renderParamTable(colWidth: any) {\n    const dataRows = this.renderDataRows(\n      this.props.paramLists,\n      colWidth,\n      // @ts-expect-error TS(4111): Property 'onlyShowParamDiff' comes from an index s... Remove this comment to see the full error message\n      this.state.onlyShowParamDiff,\n      true,\n      (key: any, data: any) => key,\n      (value) => {\n        try {\n          const jsonValue = parsePythonDictString(value);\n\n          // Pretty print if parsed value is an object or array\n          if (typeof jsonValue === 'object' && jsonValue !== null) {\n            return this.renderPrettyJson(jsonValue);\n          } else {\n            return value;\n          }\n        } catch (e) {\n          return value;\n        }\n      },\n    );\n    if (dataRows.length === 0) {\n      return (\n        <h2>\n          <FormattedMessage\n            defaultMessage=\"No parameters to display.\"\n            description=\"Text shown when there are no parameters to display\"\n          />\n        </h2>\n      );\n    }\n    return (\n      <table\n        className=\"table compare-table compare-run-table\"\n        css={{ maxHeight: '500px' }}\n        onScroll={this.onCompareRunTableScrollHandler}\n      >\n        <tbody>{dataRows}</tbody>\n      </table>\n    );\n  }\n\n  renderPrettyJson(jsonValue: any) {\n    return <pre>{JSON.stringify(jsonValue, null, 2)}</pre>;\n  }\n\n  renderMetricTable(colWidth: any, experimentIds: any) {\n    const dataRows = this.renderDataRows(\n      this.props.metricLists,\n      colWidth,\n      // @ts-expect-error TS(4111): Property 'onlyShowMetricDiff' comes from an index ... Remove this comment to see the full error message\n      this.state.onlyShowMetricDiff,\n      false,\n      (key, data) => {\n        return (\n          <Link\n            to={Routes.getMetricPageRoute(\n              this.props.runInfos.map((info) => info.runUuid).filter((uuid, idx) => data[idx] !== undefined),\n              key,\n              experimentIds,\n            )}\n            title=\"Plot chart\"\n          >\n            {key}\n            <i className=\"fas fa-chart-line\" css={{ paddingLeft: '6px' }} />\n          </Link>\n        );\n      },\n      Utils.formatMetric,\n    );\n    if (dataRows.length === 0) {\n      return (\n        <h2>\n          <FormattedMessage\n            defaultMessage=\"No metrics to display.\"\n            description=\"Text shown when there are no metrics to display\"\n          />\n        </h2>\n      );\n    }\n    return (\n      <table\n        className=\"table compare-table compare-run-table\"\n        css={{ maxHeight: '300px' }}\n        onScroll={this.onCompareRunTableScrollHandler}\n      >\n        <tbody>{dataRows}</tbody>\n      </table>\n    );\n  }\n\n  renderArtifactTable(colWidth: any) {\n    return <CompareRunArtifactView runUuids={this.props.runUuids} runInfos={this.props.runInfos} colWidth={colWidth} />;\n  }\n\n  renderTagTable(colWidth: any) {\n    const dataRows = this.renderDataRows(\n      this.props.tagLists,\n      colWidth,\n      // @ts-expect-error TS(4111): Property 'onlyShowTagDiff' comes from an index sig... Remove this comment to see the full error message\n      this.state.onlyShowTagDiff,\n      true,\n    );\n    if (dataRows.length === 0) {\n      return (\n        <h2>\n          <FormattedMessage\n            defaultMessage=\"No tags to display.\"\n            description=\"Text shown when there are no tags to display\"\n          />\n        </h2>\n      );\n    }\n    return (\n      <table\n        className=\"table compare-table compare-run-table\"\n        css={{ maxHeight: '500px' }}\n        onScroll={this.onCompareRunTableScrollHandler}\n      >\n        <tbody>{dataRows}</tbody>\n      </table>\n    );\n  }\n\n  renderTimeRows(colWidthStyle: any) {\n    const unknown = (\n      <FormattedMessage\n        defaultMessage=\"(unknown)\"\n        description=\"Filler text when run's time information is unavailable\"\n      />\n    );\n    const getTimeAttributes = (runInfo: RunInfoEntity) => {\n      const startTime = runInfo.startTime;\n      const endTime = runInfo.endTime;\n      return {\n        runUuid: runInfo.runUuid,\n        startTime: startTime ? Utils.formatTimestamp(startTime, this.props.intl) : unknown,\n        endTime: endTime ? Utils.formatTimestamp(endTime, this.props.intl) : unknown,\n        duration: startTime && endTime ? Utils.getDuration(startTime, endTime) : unknown,\n      };\n    };\n    const timeAttributes = this.props.runInfos.map(getTimeAttributes);\n    const rows = [\n      {\n        key: 'startTime',\n        title: (\n          <FormattedMessage\n            defaultMessage=\"Start Time:\"\n            description=\"Row title for the start time of runs on the experiment compare runs page\"\n          />\n        ),\n        data: timeAttributes.map(({ runUuid, startTime }) => [runUuid, startTime]),\n      },\n      {\n        key: 'endTime',\n        title: (\n          <FormattedMessage\n            defaultMessage=\"End Time:\"\n            description=\"Row title for the end time of runs on the experiment compare runs page\"\n          />\n        ),\n        data: timeAttributes.map(({ runUuid, endTime }) => [runUuid, endTime]),\n      },\n      {\n        key: 'duration',\n        title: (\n          <FormattedMessage\n            defaultMessage=\"Duration:\"\n            description=\"Row title for the duration of runs on the experiment compare runs page\"\n          />\n        ),\n        data: timeAttributes.map(({ runUuid, duration }) => [runUuid, duration]),\n      },\n    ];\n    return rows.map(({ key, title, data }) => (\n      <tr key={key}>\n        <th scope=\"row\" className=\"head-value sticky-header\" css={colWidthStyle}>\n          {title}\n        </th>\n        {data.map(([runUuid, value]) => (\n          <td className=\"data-value\" key={runUuid as string} css={colWidthStyle}>\n            <LegacyTooltip\n              title={value}\n              // @ts-expect-error TS(2322): Type '{ children: any; title: any; color: string; ... Remove this comment to see the full error message\n              color=\"gray\"\n              placement=\"topLeft\"\n              overlayStyle={{ maxWidth: '400px' }}\n              // mouseEnterDelay prop is not available in DuBois design system (yet)\n              dangerouslySetAntdProps={{ mouseEnterDelay: 1 }}\n            >\n              {value}\n            </LegacyTooltip>\n          </td>\n        ))}\n      </tr>\n    ));\n  }\n\n  render() {\n    const { experimentIds } = this.props;\n    const { runInfos, runNames, paramLists, metricLists, runUuids } = this.props;\n\n    const colWidth = this.getTableColumnWidth();\n    const colWidthStyle = this.genWidthStyle(colWidth);\n\n    const title = this.getTitle();\n    /* eslint-disable-next-line prefer-const */\n    let breadcrumbs = [this.getExperimentLink()];\n\n    const paramsLabel = this.props.intl.formatMessage({\n      defaultMessage: 'Parameters',\n      description: 'Row group title for parameters of runs on the experiment compare runs page',\n    });\n\n    const metricsLabel = this.props.intl.formatMessage({\n      defaultMessage: 'Metrics',\n      description: 'Row group title for metrics of runs on the experiment compare runs page',\n    });\n\n    const artifactsLabel = this.props.intl.formatMessage({\n      defaultMessage: 'Artifacts',\n      description: 'Row group title for artifacts of runs on the experiment compare runs page',\n    });\n\n    const tagsLabel = this.props.intl.formatMessage({\n      defaultMessage: 'Tags',\n      description: 'Row group title for tags of runs on the experiment compare runs page',\n    });\n    const diffOnlyLabel = this.props.intl.formatMessage({\n      defaultMessage: 'Show diff only',\n      description:\n        // eslint-disable-next-line max-len\n        'Label next to the switch that controls displaying only differing values in comparision tables on the compare runs page',\n    });\n\n    const displayChartSection = !shouldDisableLegacyRunCompareCharts();\n\n    return (\n      <div className=\"CompareRunView\" ref={this.compareRunViewRef}>\n        <PageHeader title={title} breadcrumbs={breadcrumbs} spacerSize=\"xs\" />\n        {displayChartSection && (\n          <CollapsibleSection\n            title={this.props.intl.formatMessage({\n              defaultMessage: 'Visualizations',\n              description: 'Tabs title for plots on the compare runs page',\n            })}\n          >\n            <LegacyTabs>\n              <TabPane\n                tab={\n                  <FormattedMessage\n                    defaultMessage=\"Parallel Coordinates Plot\"\n                    // eslint-disable-next-line max-len\n                    description=\"Tab pane title for parallel coordinate plots on the compare runs page\"\n                  />\n                }\n                key=\"parallel-coordinates-plot\"\n              >\n                <ParallelCoordinatesPlotPanel runUuids={this.props.runUuids} />\n              </TabPane>\n              <TabPane\n                tab={\n                  <FormattedMessage\n                    defaultMessage=\"Scatter Plot\"\n                    description=\"Tab pane title for scatterplots on the compare runs page\"\n                  />\n                }\n                key=\"scatter-plot\"\n              >\n                <CompareRunScatter runUuids={this.props.runUuids} runDisplayNames={this.props.runDisplayNames} />\n              </TabPane>\n              <TabPane\n                tab={\n                  <FormattedMessage\n                    defaultMessage=\"Box Plot\"\n                    description=\"Tab pane title for box plot on the compare runs page\"\n                  />\n                }\n                key=\"box-plot\"\n              >\n                <CompareRunBox\n                  runUuids={runUuids}\n                  runInfos={runInfos}\n                  paramLists={paramLists}\n                  metricLists={metricLists}\n                />\n              </TabPane>\n              <TabPane\n                tab={\n                  <FormattedMessage\n                    defaultMessage=\"Contour Plot\"\n                    description=\"Tab pane title for contour plots on the compare runs page\"\n                  />\n                }\n                key=\"contour-plot\"\n              >\n                <CompareRunContour runUuids={this.props.runUuids} runDisplayNames={this.props.runDisplayNames} />\n              </TabPane>\n            </LegacyTabs>\n          </CollapsibleSection>\n        )}\n        <CollapsibleSection\n          title={this.props.intl.formatMessage({\n            defaultMessage: 'Run details',\n            description: 'Compare table title on the compare runs page',\n          })}\n        >\n          <table\n            className=\"table compare-table compare-run-table\"\n            ref={this.runDetailsTableRef}\n            onScroll={this.onCompareRunTableScrollHandler}\n          >\n            <thead>\n              <tr>\n                <th scope=\"row\" className=\"head-value sticky-header\" css={colWidthStyle}>\n                  <FormattedMessage\n                    defaultMessage=\"Run ID:\"\n                    description=\"Row title for the run id on the experiment compare runs page\"\n                  />\n                </th>\n                {this.props.runInfos.map((r) => (\n                  <th scope=\"row\" className=\"data-value\" key={r.runUuid} css={colWidthStyle}>\n                    <LegacyTooltip\n                      title={r.runUuid}\n                      // @ts-expect-error TS(2322): Type '{ children: Element; title: any; color: stri... Remove this comment to see the full error message\n                      color=\"gray\"\n                      placement=\"topLeft\"\n                      overlayStyle={{ maxWidth: '400px' }}\n                      mouseEnterDelay={1.0}\n                    >\n                      <Link to={Routes.getRunPageRoute(r.experimentId ?? '0', r.runUuid ?? '')}>{r.runUuid}</Link>\n                    </LegacyTooltip>\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            <tbody>\n              <tr>\n                <th scope=\"row\" className=\"head-value sticky-header\" css={colWidthStyle}>\n                  <FormattedMessage\n                    defaultMessage=\"Run Name:\"\n                    description=\"Row title for the run name on the experiment compare runs page\"\n                  />\n                </th>\n                {runNames.map((runName, i) => {\n                  return (\n                    <td className=\"data-value\" key={runInfos[i].runUuid} css={colWidthStyle}>\n                      <div className=\"truncate-text single-line\">\n                        <LegacyTooltip\n                          title={runName}\n                          // @ts-expect-error TS(2322): Type '{ children: string; title: string; color: st... Remove this comment to see the full error message\n                          color=\"gray\"\n                          placement=\"topLeft\"\n                          overlayStyle={{ maxWidth: '400px' }}\n                          mouseEnterDelay={1.0}\n                        >\n                          {runName}\n                        </LegacyTooltip>\n                      </div>\n                    </td>\n                  );\n                })}\n              </tr>\n              {this.renderTimeRows(colWidthStyle)}\n              {this.shouldShowExperimentNameRow() && (\n                <tr>\n                  <th scope=\"row\" className=\"data-value\">\n                    <FormattedMessage\n                      defaultMessage=\"Experiment Name:\"\n                      // eslint-disable-next-line max-len\n                      description=\"Row title for the experiment IDs of runs on the experiment compare runs page\"\n                    />\n                  </th>\n                  {this.renderExperimentNameRowItems()}\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </CollapsibleSection>\n        <CollapsibleSection title={paramsLabel}>\n          <Switch\n            componentId=\"codegen_mlflow_app_src_experiment-tracking_components_comparerunview.tsx_570\"\n            label={diffOnlyLabel}\n            aria-label={[paramsLabel, diffOnlyLabel].join(' - ')}\n            // @ts-expect-error TS(4111): Property 'onlyShowParamDiff' comes from an index s... Remove this comment to see the full error message\n            checked={this.state.onlyShowParamDiff}\n            onChange={(checked, e) => this.setState({ onlyShowParamDiff: checked })}\n          />\n          <Spacer size=\"lg\" />\n          {this.renderParamTable(colWidth)}\n        </CollapsibleSection>\n        <CollapsibleSection title={metricsLabel}>\n          <Switch\n            componentId=\"codegen_mlflow_app_src_experiment-tracking_components_comparerunview.tsx_581\"\n            label={diffOnlyLabel}\n            aria-label={[metricsLabel, diffOnlyLabel].join(' - ')}\n            // @ts-expect-error TS(4111): Property 'onlyShowMetricDiff' comes from an index ... Remove this comment to see the full error message\n            checked={this.state.onlyShowMetricDiff}\n            onChange={(checked, e) => this.setState({ onlyShowMetricDiff: checked })}\n          />\n          <Spacer size=\"lg\" />\n          {this.renderMetricTable(colWidth, experimentIds)}\n        </CollapsibleSection>\n        <CollapsibleSection title={artifactsLabel}>{this.renderArtifactTable(colWidth)}</CollapsibleSection>\n        <CollapsibleSection title={tagsLabel}>\n          <Switch\n            componentId=\"codegen_mlflow_app_src_experiment-tracking_components_comparerunview.tsx_592\"\n            label={diffOnlyLabel}\n            aria-label={[tagsLabel, diffOnlyLabel].join(' - ')}\n            // @ts-expect-error TS(4111): Property 'onlyShowTagDiff' comes from an index sig... Remove this comment to see the full error message\n            checked={this.state.onlyShowTagDiff}\n            onChange={(checked, e) => this.setState({ onlyShowTagDiff: checked })}\n          />\n          <Spacer size=\"lg\" />\n          {this.renderTagTable(colWidth)}\n        </CollapsibleSection>\n      </div>\n    );\n  }\n\n  genWidthStyle(width: any) {\n    return {\n      width: `${width}px`,\n      minWidth: `${width}px`,\n      maxWidth: `${width}px`,\n    };\n  }\n\n  // eslint-disable-next-line no-unused-vars\n  renderDataRows(\n    list: any,\n    colWidth: any,\n    onlyShowDiff: any,\n    highlightDiff = false,\n    headerMap = (key: any, data: any) => key,\n    formatter = (value: any) => value,\n  ) {\n    // @ts-expect-error TS(2554): Expected 2 arguments, but got 1.\n    const keys = CompareRunUtil.getKeys(list);\n    const data = {};\n    const checkHasDiff = (values: any) => values.some((x: any) => x !== values[0]);\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    keys.forEach((k) => (data[k] = { values: Array(list.length).fill(undefined) }));\n    list.forEach((records: any, i: any) => {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      records.forEach((r: any) => (data[r.key].values[i] = r.value));\n    });\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    keys.forEach((k) => (data[k].hasDiff = checkHasDiff(data[k].values)));\n\n    const colWidthStyle = this.genWidthStyle(colWidth);\n\n    return (\n      keys\n        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n        .filter((k) => !onlyShowDiff || data[k].hasDiff)\n        .map((k) => {\n          // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n          const { values, hasDiff } = data[k];\n          const rowClass = highlightDiff && hasDiff ? 'diff-row' : undefined;\n          return (\n            <tr key={k} className={rowClass}>\n              <th scope=\"row\" className=\"head-value sticky-header\" css={colWidthStyle}>\n                {headerMap(k, values)}\n              </th>\n              {values.map((value: any, i: any) => {\n                const cellText = value === undefined ? '' : formatter(value);\n                return (\n                  <td className=\"data-value\" key={this.props.runInfos[i].runUuid} css={colWidthStyle}>\n                    <LegacyTooltip\n                      title={cellText}\n                      // @ts-expect-error TS(2322): Type '{ children: Element; title: any; color: stri... Remove this comment to see the full error message\n                      color=\"gray\"\n                      placement=\"topLeft\"\n                      overlayStyle={{ maxWidth: '400px' }}\n                      mouseEnterDelay={1.0}\n                    >\n                      <span className=\"truncate-text single-line\">{cellText}</span>\n                    </LegacyTooltip>\n                  </td>\n                );\n              })}\n            </tr>\n          );\n        })\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { comparedExperimentIds, hasComparedExperimentsBefore } = state.compareExperiments;\n  const runInfos: any = [];\n  const metricLists: any = [];\n  const paramLists: any = [];\n  const tagLists: any = [];\n  const runNames: any = [];\n  const runDisplayNames: any = [];\n  const { experimentIds, runUuids } = ownProps;\n  const experiments = experimentIds.map((experimentId: any) => getExperiment(experimentId, state));\n  runUuids.forEach((runUuid: any) => {\n    const runInfo = getRunInfo(runUuid, state);\n    // Skip processing data if run info is not available yet\n    if (!runInfo) {\n      return;\n    }\n    runInfos.push(runInfo);\n    metricLists.push(Object.values(getLatestMetrics(runUuid, state)));\n    paramLists.push(Object.values(getParams(runUuid, state)));\n    const runTags = getRunTags(runUuid, state);\n    const visibleTags = Utils.getVisibleTagValues(runTags).map(([key, value]) => ({\n      key,\n      value,\n    }));\n    tagLists.push(visibleTags);\n    runDisplayNames.push(Utils.getRunDisplayName(runInfo, runUuid));\n    runNames.push(Utils.getRunName(runInfo));\n  });\n  return {\n    experiments,\n    runInfos,\n    metricLists,\n    paramLists,\n    tagLists,\n    runNames,\n    runDisplayNames,\n    comparedExperimentIds,\n    hasComparedExperimentsBefore,\n  };\n};\n\n/**\n * Parse a Python dictionary in string format into a JSON object.\n * @param value The Python dictionary string to parse\n * @returns The parsed JSON object, or null if parsing fails\n */\nconst parsePythonDictString = (value: string) => {\n  try {\n    const jsonString = value.replace(/'/g, '\"');\n    return JSON.parse(jsonString);\n  } catch (e) {\n    return null;\n  }\n};\n\nexport default connect(mapStateToProps)(injectIntl(CompareRunView));\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport qs from 'qs';\nimport { connect } from 'react-redux';\nimport { getRunApi, getExperimentApi } from '../actions';\nimport RequestStateWrapper from '../../common/components/RequestStateWrapper';\nimport CompareRunView from './CompareRunView';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { PageContainer } from '../../common/components/PageContainer';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\nimport { DangerIcon, Empty, Spinner } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport Utils from '../../common/utils/Utils';\nimport { FallbackProps } from 'react-error-boundary';\n\ntype CompareRunPageProps = {\n  experimentIds: string[];\n  runUuids: string[];\n  urlDecodeError?: boolean;\n  dispatch: (...args: any[]) => any;\n};\n\nclass CompareRunPageImpl extends Component<CompareRunPageProps> {\n  requestIds: any;\n\n  constructor(props: CompareRunPageProps) {\n    super(props);\n    this.requestIds = [];\n  }\n\n  state: {\n    requestError?: Error | ErrorWrapper;\n  } = {\n    requestError: undefined,\n  };\n\n  fetchExperiments() {\n    return this.props.experimentIds.map((experimentId) => {\n      const experimentRequestId = getUUID();\n      this.props\n        .dispatch(getExperimentApi(experimentId, experimentRequestId))\n        .catch((requestError: Error | ErrorWrapper) => this.setState({ requestError }));\n      return experimentRequestId;\n    });\n  }\n\n  componentDidMount() {\n    this.requestIds.push(...this.fetchExperiments());\n    this.props.runUuids.forEach((runUuid) => {\n      const requestId = getUUID();\n      this.requestIds.push(requestId);\n\n      this.props.dispatch(getRunApi(runUuid, requestId)).catch((requestError: Error | ErrorWrapper) => {\n        this.setState({ requestError });\n      });\n    });\n  }\n\n  render() {\n    // If the error is set, throw it to be caught by the error boundary\n    if (this.state.requestError) {\n      const { requestError } = this.state;\n      const errorToThrow = requestError instanceof Error ? requestError : new Error(requestError.getMessageField?.());\n      throw errorToThrow;\n    }\n    return (\n      <PageContainer>\n        <RequestStateWrapper\n          // We suppress throwing error by RequestStateWrapper since we handle it using component and error boundary\n          suppressErrorThrow\n          requestIds={this.requestIds}\n        >\n          <CompareRunView runUuids={this.props.runUuids} experimentIds={this.props.experimentIds} />\n        </RequestStateWrapper>\n      </PageContainer>\n    );\n  }\n}\n\n/**\n * When integrated via IFrame in Kubeflow it re-encodes the URI (sometimes multiple times), leading to an unparsable JSON.\n * This function decodes the URI until it is parsable.\n */\nconst decodeURI = (uri: string): string => {\n  const decodedURI = decodeURIComponent(uri);\n  if (uri !== decodedURI) {\n    return decodeURI(decodedURI);\n  }\n  return decodedURI;\n};\n\nconst mapStateToProps = (state: any, ownProps: WithRouterNextProps) => {\n  try {\n    const { location } = ownProps;\n    const locationSearchDecoded = decodeURI(location.search);\n    const searchValues = qs.parse(locationSearchDecoded);\n    // @ts-expect-error TS(2345): Argument of type 'string | string[] | ParsedQs | P... Remove this comment to see the full error message\n    const runUuids = JSON.parse(searchValues['?runs']);\n    // @ts-expect-error TS(2345): Argument of type 'string | string[] | ParsedQs | P... Remove this comment to see the full error message\n    const experimentIds = JSON.parse(searchValues['experiments']);\n    return { experimentIds, runUuids };\n  } catch (e) {\n    if (e instanceof SyntaxError) {\n      throw new SyntaxError(`Error while parsing URL: ${e.message}`);\n    }\n\n    throw e;\n  }\n};\n\nconst CompareRunPageErrorFallback = ({ error }: { error: Error }) => (\n  <div css={{ height: '100%', alignItems: 'center', justifyContent: 'center', display: 'flex' }}>\n    <Empty\n      title={\n        <FormattedMessage\n          defaultMessage=\"Error while loading compare runs page\"\n          description=\"Title of the error state on the run compare page\"\n        />\n      }\n      description={error.message}\n      image={<DangerIcon />}\n    />\n  </div>\n);\n\nconst CompareRunPage = withRouterNext(connect(mapStateToProps)(CompareRunPageImpl));\n\nexport default withErrorBoundary(\n  ErrorUtils.mlflowServices.RUN_TRACKING,\n  CompareRunPage,\n  undefined,\n  CompareRunPageErrorFallback,\n);\n", "import React, { useCallback } from 'react';\nimport { SectionErrorBoundary } from './error-boundaries/SectionErrorBoundary';\nimport {\n  ChevronRightIcon,\n  useDesignSystemTheme,\n  Accordion,\n  DesignSystemThemeInterface,\n  importantify,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\ninterface CollapsibleSectionProps {\n  title: string | any;\n  forceOpen?: boolean;\n  children: React.ReactNode;\n  showServerError?: boolean;\n  defaultCollapsed?: boolean;\n  onChange?: (key: string | string[]) => void;\n  className?: string;\n  componentId?: string;\n}\n\n// Custom styles to make <Accordion> look like previously used <Collapse> from antd\nconst getAccordionStyles = ({\n  theme,\n  getPrefixedClassName,\n}: Pick<DesignSystemThemeInterface, 'theme' | 'getPrefixedClassName'>) => {\n  const clsPrefix = getPrefixedClassName('collapse');\n\n  const classItem = `.${clsPrefix}-item`;\n  const classHeader = `.${clsPrefix}-header`;\n  const classContentBox = `.${clsPrefix}-content-box`;\n\n  return {\n    fontSize: 14,\n    [`& > ${classItem} > ${classHeader}`]: {\n      paddingLeft: 0,\n      paddingTop: 12,\n      paddingBottom: 12,\n      display: 'flex',\n      alignItems: 'center',\n      fontSize: 16,\n      fontWeight: 'normal',\n      lineHeight: theme.typography.lineHeightLg,\n    },\n    [classContentBox]: {\n      padding: `${theme.spacing.xs}px 0 ${theme.spacing.md}px 0`,\n    },\n  };\n};\n\nexport function CollapsibleSection(props: CollapsibleSectionProps) {\n  const {\n    title,\n    forceOpen,\n    showServerError,\n    defaultCollapsed,\n    onChange,\n    className,\n    componentId = 'mlflow.common.generic_collapsible_section',\n  } = props;\n\n  // We need to spread `activeKey` into <Collapse/> as an optional prop because its enumerability\n  // affects rendering, i.e. passing `activeKey={undefined}` is different from not passing activeKey\n  const activeKeyProp = forceOpen && { activeKey: ['1'] };\n  const defaultActiveKey = defaultCollapsed ? null : ['1'];\n\n  const { theme, getPrefixedClassName } = useDesignSystemTheme();\n  const { formatMessage } = useIntl();\n\n  const getExpandIcon = useCallback(\n    ({ isActive }: { isActive?: boolean }) => (\n      <div\n        css={importantify({ width: theme.general.heightBase / 2, transform: isActive ? 'rotate(90deg)' : undefined })}\n      >\n        <ChevronRightIcon\n          css={{\n            svg: { width: theme.general.heightBase / 2, height: theme.general.heightBase / 2 },\n          }}\n          aria-label={\n            isActive\n              ? formatMessage(\n                  {\n                    defaultMessage: 'collapse {title}',\n                    description: 'Common component > collapsible section > alternative label when expand',\n                  },\n                  { title },\n                )\n              : formatMessage(\n                  {\n                    defaultMessage: 'expand {title}',\n                    description: 'Common component > collapsible section > alternative label when collapsed',\n                  },\n                  { title },\n                )\n          }\n        />\n      </div>\n    ),\n    [theme, title, formatMessage],\n  );\n\n  return (\n    <Accordion\n      componentId={componentId}\n      {...activeKeyProp}\n      dangerouslyAppendEmotionCSS={getAccordionStyles({ theme, getPrefixedClassName })}\n      dangerouslySetAntdProps={{\n        className,\n        expandIconPosition: 'left',\n        expandIcon: getExpandIcon,\n      }}\n      defaultActiveKey={defaultActiveKey ?? undefined}\n      onChange={onChange}\n    >\n      <Accordion.Panel header={title} key=\"1\">\n        <SectionErrorBoundary showServerError={showServerError}>{props.children}</SectionErrorBoundary>\n      </Accordion.Panel>\n    </Accordion>\n  );\n}\n", "import React from 'react';\n\nimport type { ButtonProps } from '@databricks/design-system';\nimport {\n  DesignSystemEventProviderAnalyticsEventTypes,\n  DesignSystemEventProviderComponentTypes,\n  useDesignSystemEventComponentCallbacks,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\n\nexport interface ToggleIconButtonProps extends ButtonProps {\n  pressed?: boolean;\n}\n\n/**\n * WARNING: Temporary component!\n *\n * This component recreates \"Toggle button with icon\" pattern which is not\n * available in the design system yet.\n *\n * TODO: replace this component with the one from DuBois design system when available.\n */\nconst ToggleIconButton = React.forwardRef<HTMLButtonElement, ToggleIconButtonProps>(\n  (props: ToggleIconButtonProps, ref) => {\n    const {\n      pressed,\n      onClick,\n      icon,\n      onBlur,\n      onFocus,\n      onMouseEnter,\n      onMouseLeave,\n      componentId,\n      analyticsEvents,\n      type,\n      ...remainingProps\n    } = props;\n    const { theme } = useDesignSystemTheme();\n\n    const eventContext = useDesignSystemEventComponentCallbacks({\n      componentType: DesignSystemEventProviderComponentTypes.Button,\n      componentId,\n      analyticsEvents: analyticsEvents ?? [DesignSystemEventProviderAnalyticsEventTypes.OnClick],\n    });\n\n    return (\n      <button\n        onClick={(event) => {\n          eventContext.onClick(event);\n          onClick?.(event);\n        }}\n        css={{\n          cursor: 'pointer',\n          width: theme.general.heightSm,\n          height: theme.general.heightSm,\n          borderRadius: theme.legacyBorders.borderRadiusMd,\n          lineHeight: theme.typography.lineHeightBase,\n          padding: 0,\n          border: 0,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: pressed ? theme.colors.actionDefaultBackgroundPress : 'transparent',\n          color: pressed ? theme.colors.actionDefaultTextPress : theme.colors.textSecondary,\n          '&:hover': {\n            background: theme.colors.actionDefaultBackgroundHover,\n            color: theme.colors.actionDefaultTextHover,\n          },\n        }}\n        ref={ref}\n        onBlur={onBlur}\n        onFocus={onFocus}\n        onMouseEnter={onMouseEnter}\n        onMouseLeave={onMouseLeave}\n        {...remainingProps}\n      >\n        {icon}\n      </button>\n    );\n  },\n);\n\nexport { ToggleIconButton };\n", "/**\n * Adapted from `duotone-dark`\n * Ref: https://github.com/react-syntax-highlighter/react-syntax-highlighter/blob/b2457268891948f7005ccf539a70c000f0695bde/src/styles/prism/duotone-dark.js\n */\n\nconst databricksDuotoneDarkTheme = {\n  'code[class*=\"language-\"]': {\n    fontFamily:\n      'Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace',\n    fontSize: '14px',\n    lineHeight: '1.375',\n    direction: 'ltr',\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n    background: '#2a2734',\n    color: '#5DFAFC', // D\n  },\n  'pre[class*=\"language-\"]': {\n    fontFamily:\n      'Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace',\n    fontSize: '14px',\n    lineHeight: '1.375',\n    direction: 'ltr',\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n    background: '#2a2734',\n    color: '#5DFAFC', // D\n    padding: '1em',\n    margin: '.5em 0',\n    overflow: 'auto',\n  },\n  'pre > code[class*=\"language-\"]': {\n    fontSize: '1em',\n  },\n  'pre[class*=\"language-\"]::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'pre[class*=\"language-\"] ::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"]::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"] ::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'pre[class*=\"language-\"]::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'pre[class*=\"language-\"] ::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"]::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"] ::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    padding: '.1em',\n    borderRadius: '.3em',\n  },\n  comment: {\n    color: '#6c6783',\n  },\n  prolog: {\n    color: '#6c6783',\n  },\n  doctype: {\n    color: '#6c6783',\n  },\n  cdata: {\n    color: '#6c6783',\n  },\n  punctuation: {\n    color: '#6c6783',\n  },\n  namespace: {\n    Opacity: '.7',\n  },\n  tag: {\n    color: '#3AACE2', // D\n  },\n  operator: {\n    color: '#3AACE2', // D\n  },\n  number: {\n    color: '#3AACE2', // D\n  },\n  property: {\n    color: '#5DFAFC', // D\n  },\n  function: {\n    color: '#5DFAFC', // D\n  },\n  'tag-id': {\n    color: '#eeebff',\n  },\n  selector: {\n    color: '#eeebff',\n  },\n  'atrule-id': {\n    color: '#eeebff',\n  },\n  'code.language-javascript': {\n    color: '#c4b9fe',\n  },\n  'attr-name': {\n    color: '#c4b9fe',\n  },\n  'code.language-css': {\n    color: '#ffffff', // D\n  },\n  'code.language-scss': {\n    color: '#ffffff', // D\n  },\n  boolean: {\n    color: '#ffffff', // D\n  },\n  string: {\n    color: '#ffffff', // D\n  },\n  entity: {\n    color: '#ffffff', // D\n    cursor: 'help',\n  },\n  url: {\n    color: '#ffffff', // D\n  },\n  '.language-css .token.string': {\n    color: '#ffffff', // D\n  },\n  '.language-scss .token.string': {\n    color: '#ffffff', // D\n  },\n  '.style .token.string': {\n    color: '#ffffff', // D\n  },\n  'attr-value': {\n    color: '#ffffff', // D\n  },\n  keyword: {\n    color: '#ffffff', // D\n  },\n  control: {\n    color: '#ffffff', // D\n  },\n  directive: {\n    color: '#ffffff', // D\n  },\n  unit: {\n    color: '#ffffff', // D\n  },\n  statement: {\n    color: '#ffffff', // D\n  },\n  regex: {\n    color: '#ffffff', // D\n  },\n  atrule: {\n    color: '#ffffff', // D\n  },\n  placeholder: {\n    color: '#ffffff', // D\n  },\n  variable: {\n    color: '#ffffff', // D\n  },\n  deleted: {\n    textDecoration: 'line-through',\n  },\n  inserted: {\n    borderBottom: '1px dotted #eeebff',\n    textDecoration: 'none',\n  },\n  italic: {\n    fontStyle: 'italic',\n  },\n  important: {\n    fontWeight: 'bold',\n    color: '#c4b9fe',\n  },\n  bold: {\n    fontWeight: 'bold',\n  },\n  'pre > code.highlight': {\n    Outline: '.4em solid #8a75f5',\n    OutlineOffset: '.4em',\n  },\n  '.line-numbers.line-numbers .line-numbers-rows': {\n    borderRightColor: '#2c2937',\n  },\n  '.line-numbers .line-numbers-rows > span:before': {\n    color: '#3c3949',\n  },\n  '.line-highlight.line-highlight': {\n    background: 'linear-gradient(to right, rgba(224, 145, 66, 0.2) 70%, rgba(224, 145, 66, 0))',\n  },\n};\n\nexport default databricksDuotoneDarkTheme;\n", "/**\n * Adapted from `material-light`\n * Ref: https://github.com/react-syntax-highlighter/react-syntax-highlighter/blob/b2457268891948f7005ccf539a70c000f0695bde/src/styles/prism/material-light.js#L1\n *\n * This theme overwrites colors to be similiar to the `@databricks/editor` theme.\n */\n\nconst databricksLightTheme = {\n  'code[class*=\"language-\"]': {\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    wordWrap: 'normal',\n    color: 'rgb(77, 77, 76)', // D\n    background: '#fafafa',\n    fontFamily: 'Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace',\n    fontSize: '12px', // D\n    lineHeight: '1.5em',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n  },\n  'pre[class*=\"language-\"]': {\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    wordWrap: 'normal',\n    color: 'rgb(77, 77, 76)', // D\n    background: '#fafafa',\n    fontFamily: 'Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace',\n    fontSize: '12px', // D\n    lineHeight: '1.5em',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n    overflow: 'auto',\n    position: 'relative',\n    margin: '0.5em 0',\n    padding: '1.25em 1em',\n  },\n  'code[class*=\"language-\"]::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"]::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'code[class*=\"language-\"] ::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"] ::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'code[class*=\"language-\"]::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"]::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'code[class*=\"language-\"] ::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"] ::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    whiteSpace: 'normal',\n    borderRadius: '0.2em',\n    padding: '0.1em',\n  },\n  '.language-css > code': {\n    color: '#f5871f', // D\n  },\n  '.language-sass > code': {\n    color: '#f5871f', // D\n  },\n  '.language-scss > code': {\n    color: '#f5871f', // D\n  },\n  '[class*=\"language-\"] .namespace': {\n    Opacity: '0.7',\n  },\n  atrule: {\n    color: '#7c4dff',\n  },\n  'attr-name': {\n    color: '#39adb5',\n  },\n  'attr-value': {\n    color: '#f6a434',\n  },\n  attribute: {\n    color: '#f6a434',\n  },\n  boolean: {\n    color: '#7c4dff', // D\n  },\n  builtin: {\n    color: '#39adb5',\n  },\n  cdata: {\n    color: '#39adb5',\n  },\n  char: {\n    color: '#39adb5',\n  },\n  class: {\n    color: '#39adb5',\n  },\n  'class-name': {\n    color: '#6182b8',\n  },\n  comment: {\n    color: '#8e908c', // D\n  },\n  constant: {\n    color: '#7c4dff', // D\n  },\n  deleted: {\n    color: '#e53935',\n  },\n  doctype: {\n    color: '#aabfc9',\n  },\n  entity: {\n    color: '#e53935',\n  },\n  function: {\n    color: '#4271ae', // D\n  },\n  hexcode: {\n    color: '#f5871f', // D\n  },\n  id: {\n    color: '#7c4dff',\n    fontWeight: 'bold',\n  },\n  important: {\n    color: '#7c4dff',\n    fontWeight: 'bold',\n  },\n  inserted: {\n    color: '#39adb5',\n  },\n  keyword: {\n    color: '#8959a8', // D\n  },\n  number: {\n    color: '#f5871f', // D\n  },\n  operator: {\n    color: '#3e999f', // D\n  },\n  prolog: {\n    color: '#aabfc9',\n  },\n  property: {\n    color: '#39adb5',\n  },\n  'pseudo-class': {\n    color: '#f6a434',\n  },\n  'pseudo-element': {\n    color: '#f6a434',\n  },\n  punctuation: {\n    color: 'rgb(77, 77, 76)', // D\n  },\n  regex: {\n    color: '#6182b8',\n  },\n  selector: {\n    color: '#e53935',\n  },\n  string: {\n    color: '#3ba85f', // D\n  },\n  symbol: {\n    color: '#7c4dff',\n  },\n  tag: {\n    color: '#e53935',\n  },\n  unit: {\n    color: '#f5871f', // D\n  },\n  url: {\n    color: '#e53935',\n  },\n  variable: {\n    color: '#c72d4c', // D\n  },\n};\n\nexport default databricksLightTheme;\n", "import { PrismLight as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';\nimport python from 'react-syntax-highlighter/dist/cjs/languages/prism/python';\nimport json from 'react-syntax-highlighter/dist/cjs/languages/prism/json';\n\nSyntaxHighlighter.registerLanguage('python', python);\nSyntaxHighlighter.registerLanguage('json', json);\n\nimport duotoneDarkStyle from './theme/databricks-duotone-dark';\nimport lightStyle from './theme/databricks-light';\nimport { CSSProperties, ReactNode } from 'react';\nimport { pick } from 'lodash';\n\nexport type CodeSnippetTheme = 'duotoneDark' | 'light';\n\nexport const buttonBackgroundColorDark = 'rgba(140, 203, 255, 0)';\nexport const buttonColorDark = 'rgba(255, 255, 255, 0.84)';\nexport const buttonHoverColorDark = '#8ccbffcc';\nexport const buttonHoverBackgroundColorDark = 'rgba(140, 203, 255, 0.08)';\nexport const duboisAlertBackgroundColor = '#fff0f0';\nexport const snippetPadding = '24px';\n\nconst themesStyles: Record<CodeSnippetTheme, any> = {\n  light: lightStyle,\n  duotoneDark: duotoneDarkStyle,\n};\n\nexport type CodeSnippetLanguage = 'python' | 'json' | 'text';\n\nexport interface CodeSnippetProps {\n  /**\n   * The code string\n   */\n  children: string;\n  /**\n   * The actions that are displayed on the right top corner of the component\n   *  see `./actions` for built-in actions\n   */\n  actions?: NonNullable<ReactNode> | NonNullable<ReactNode>[];\n  /**\n   * The theme, default theme is `light`\n   */\n  theme?: CodeSnippetTheme;\n  /**\n   * Language of the code (`children`)\n   */\n  language: CodeSnippetLanguage;\n  /**\n   * Custom styles (passed to the internal `<pre>`)\n   */\n  style?: CSSProperties;\n  /**\n   * Whether to show line numbers on the left or not\n   */\n  showLineNumbers?: boolean;\n  /**\n   * Custom styles for line numbers\n   */\n  lineNumberStyle?: CSSProperties;\n  /**\n   * Whether or not to wrap long lines\n   */\n  wrapLongLines?: boolean;\n}\n\n/**\n * `CodeSnippet` is used for highlighting code, use this instead of\n */\nexport function CodeSnippet({\n  theme = 'light',\n  language,\n  actions,\n  style,\n  children,\n  showLineNumbers,\n  lineNumberStyle,\n  wrapLongLines,\n}: CodeSnippetProps) {\n  const customStyle = {\n    border: 'none',\n    borderRadius: 0,\n    margin: 0,\n    padding: snippetPadding,\n    ...style,\n  };\n\n  return (\n    <SyntaxHighlighter\n      showLineNumbers={showLineNumbers}\n      lineNumberStyle={lineNumberStyle}\n      language={language}\n      style={themesStyles[theme]}\n      customStyle={customStyle}\n      codeTagProps={{\n        style: pick(style, 'backgroundColor'),\n      }}\n      wrapLongLines={wrapLongLines}\n    >\n      {children}\n    </SyntaxHighlighter>\n  );\n}\n", "import React from 'react';\nimport { CodeSnippet } from '@databricks/web-shared/snippet';\nimport { isObject } from 'lodash';\n\ninterface JsonPreviewProps {\n  json: string;\n  wrapperStyle?: React.CSSProperties;\n  overlayStyle?: React.CSSProperties;\n  codeSnippetStyle?: React.CSSProperties;\n}\n\nexport const JsonPreview: React.FC<JsonPreviewProps> = ({ json, wrapperStyle, overlayStyle, codeSnippetStyle }) => {\n  const { formattedJson, isJsonContent } = useFormattedJson(json);\n\n  const defaultWrapperStyle: React.CSSProperties = {\n    position: 'relative',\n    maxHeight: 'calc(1.5em * 9)',\n    overflow: 'hidden',\n  };\n\n  const defaultOverlayStyle: React.CSSProperties = {\n    position: 'absolute',\n    bottom: 0,\n    right: 0,\n    left: 6,\n    height: '2em',\n    background: 'linear-gradient(transparent, white)',\n  };\n\n  const defaultCodeSnippetStyle: React.CSSProperties = {\n    padding: '5px',\n    overflowX: 'hidden',\n  };\n\n  return (\n    <div style={{ ...defaultWrapperStyle, ...wrapperStyle }}>\n      {isJsonContent ? (\n        <>\n          <CodeSnippet language=\"json\" style={{ ...defaultCodeSnippetStyle, ...codeSnippetStyle }}>\n            {formattedJson}\n          </CodeSnippet>\n          <div css={{ ...defaultOverlayStyle, ...overlayStyle }}></div>\n        </>\n      ) : (\n        <>{json}</>\n      )}\n    </div>\n  );\n};\n\nfunction useFormattedJson(json: string) {\n  return React.useMemo(() => {\n    try {\n      const parsed = JSON.parse(json);\n      const isJson = isObject(parsed) && typeof parsed !== 'function' && !(parsed instanceof Date);\n      return {\n        formattedJson: isJson ? JSON.stringify(parsed, null, 2) : json,\n        isJsonContent: isJson,\n      };\n    } catch (e) {\n      return {\n        formattedJson: json,\n        isJsonContent: false,\n      };\n    }\n  }, [json]);\n}\n\nexport const FormattedJsonDisplay: React.FC<{ json: string }> = ({ json }) => {\n  const { formattedJson, isJsonContent } = useFormattedJson(json);\n\n  return (\n    <div css={{ whiteSpace: 'pre-wrap' }}>\n      {isJsonContent ? (\n        <CodeSnippet language=\"json\" wrapLongLines>\n          {formattedJson}\n        </CodeSnippet>\n      ) : (\n        <span>{json}</span>\n      )}\n    </div>\n  );\n};\n", "import {\n  ArrowLeftIcon,\n  ArrowRightIcon,\n  CloseIcon,\n  DesignSystemContext,\n  RedoIcon,\n  UndoIcon,\n  ZoomInIcon,\n  ZoomOutIcon,\n} from '@databricks/design-system';\nimport { useContext } from 'react';\nimport RcImage from 'rc-image';\nimport './Image.css';\n\nconst icons = {\n  rotateLeft: <UndoIcon />,\n  rotateRight: <RedoIcon />,\n  zoomIn: <ZoomInIcon />,\n  zoomOut: <ZoomOutIcon />,\n  close: <CloseIcon />,\n  left: <ArrowLeftIcon />,\n  right: <ArrowRightIcon />,\n};\n\nexport const ImagePreviewGroup = ({\n  children,\n  visible,\n  onVisibleChange,\n}: {\n  children: React.ReactNode;\n  visible: boolean;\n  onVisibleChange: (v: boolean) => void;\n}) => {\n  const { getPopupContainer } = useContext(DesignSystemContext);\n\n  return (\n    <RcImage.PreviewGroup\n      icons={icons}\n      preview={{\n        visible: visible,\n        getContainer: getPopupContainer,\n        onVisibleChange: (v) => onVisibleChange(v),\n      }}\n    >\n      {children}\n    </RcImage.PreviewGroup>\n  );\n};\n\nexport { RcImage as Image };\n", "import { <PERSON><PERSON><PERSON>, Spinner } from '@databricks/design-system';\nimport { useDesignSystemTheme } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { getArtifactLocationUrl } from '@mlflow/mlflow/src/common/utils/ArtifactUtils';\nimport { ImageEntity } from '@mlflow/mlflow/src/experiment-tracking/types';\nimport { useState, useEffect } from 'react';\nimport { Typography } from '@databricks/design-system';\nimport { ImagePreviewGroup, Image } from '../../../../../shared/building_blocks/Image';\n\n/**\n * Despite image size being dynamic, we want to set a minimum size for the grid images.\n */\nexport const MIN_GRID_IMAGE_SIZE = 200;\n\ntype ImagePlotProps = {\n  imageUrl: string;\n  compressedImageUrl: string;\n  imageSize?: number;\n  maxImageSize?: number;\n};\n\nexport const ImagePlot = ({ imageUrl, compressedImageUrl, imageSize, maxImageSize }: ImagePlotProps) => {\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const { theme } = useDesignSystemTheme();\n\n  const [imageLoading, setImageLoading] = useState(true);\n\n  useEffect(() => {\n    // Load the image in the memory (should reuse the same request) in order to get the loading state\n    setImageLoading(true);\n    const img = new window.Image();\n    img.onload = () => setImageLoading(false);\n    img.onerror = () => setImageLoading(false);\n    img.src = compressedImageUrl;\n    return () => {\n      img.src = '';\n    };\n  }, [compressedImageUrl]);\n\n  return (\n    <div css={{ width: imageSize || '100%', height: imageSize || '100%' }}>\n      <div css={{ display: 'contents' }}>\n        {compressedImageUrl === undefined || imageLoading ? (\n          <div\n            css={{\n              width: '100%',\n              backgroundColor: theme.colors.backgroundSecondary,\n              display: 'flex',\n              aspectRatio: '1',\n              justifyContent: 'center',\n              alignItems: 'center',\n            }}\n          >\n            <Spinner />\n          </div>\n        ) : (\n          <div\n            css={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              width: imageSize || '100%',\n              aspectRatio: '1',\n              maxWidth: maxImageSize,\n              maxHeight: maxImageSize,\n              backgroundColor: theme.colors.backgroundSecondary,\n              '.rc-image': {\n                cursor: 'pointer',\n              },\n            }}\n          >\n            <ImagePreviewGroup visible={previewVisible} onVisibleChange={setPreviewVisible}>\n              <Image\n                src={compressedImageUrl}\n                preview={{ src: imageUrl }}\n                style={{ maxWidth: maxImageSize || '100%', maxHeight: maxImageSize || '100%' }}\n              />\n            </ImagePreviewGroup>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport const ImagePlotWithHistory = ({\n  metadataByStep,\n  imageSize,\n  step,\n  runUuid,\n}: {\n  metadataByStep: Record<number, ImageEntity>;\n  imageSize?: number;\n  step: number;\n  runUuid: string;\n}) => {\n  const { theme } = useDesignSystemTheme();\n\n  if (metadataByStep[step] === undefined) {\n    return (\n      <div\n        css={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          textAlign: 'center',\n          width: imageSize,\n          backgroundColor: theme.colors.backgroundSecondary,\n          padding: theme.spacing.md,\n          aspectRatio: '1',\n        }}\n      >\n        <ImageIcon />\n        <FormattedMessage\n          defaultMessage=\"No image logged at this step\"\n          description=\"Experiment tracking > runs charts > charts > image plot with history > no image text\"\n        />\n      </div>\n    );\n  }\n  return (\n    <ImagePlot\n      imageUrl={getArtifactLocationUrl(metadataByStep[step].filepath, runUuid)}\n      compressedImageUrl={getArtifactLocationUrl(metadataByStep[step].compressed_filepath, runUuid)}\n      imageSize={imageSize}\n    />\n  );\n};\n\nexport const EmptyImageGridPlot = () => {\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100%',\n        width: '100%',\n        fontSize: 16,\n      }}\n    >\n      <Typography.Title css={{ marginTop: 16 }} color=\"secondary\" level={3}>\n        Compare logged images\n      </Typography.Title>\n      <Typography.Text css={{ marginBottom: 16 }} color=\"secondary\">\n        Use the image grid chart to compare logged images across runs.\n      </Typography.Text>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { Button, type ButtonProps, LegacyTooltip } from '@databricks/design-system';\n\ninterface CopyButtonProps extends Partial<ButtonProps> {\n  copyText: string;\n  showLabel?: React.ReactNode;\n  componentId?: string;\n}\n\nexport const CopyButton = ({ copyText, showLabel = true, componentId, ...buttonProps }: CopyButtonProps) => {\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  const handleClick = () => {\n    navigator.clipboard.writeText(copyText);\n    setShowTooltip(true);\n    setTimeout(() => {\n      setShowTooltip(false);\n    }, 3000);\n  };\n\n  const handleMouseLeave = () => {\n    setShowTooltip(false);\n  };\n\n  return (\n    <LegacyTooltip\n      title={\n        <FormattedMessage defaultMessage=\"Copied\" description=\"Tooltip text shown when copy operation completes\" />\n      }\n      dangerouslySetAntdProps={{\n        visible: showTooltip,\n      }}\n    >\n      <Button\n        componentId={componentId ?? 'mlflow.shared.copy_button'}\n        type=\"primary\"\n        onClick={handleClick}\n        onMouseLeave={handleMouseLeave}\n        css={{ 'z-index': 1 }}\n        // Define children as a explicit prop so it can be easily overrideable\n        children={\n          showLabel ? <FormattedMessage defaultMessage=\"Copy\" description=\"Button text for copy button\" /> : undefined\n        }\n        {...buttonProps}\n      />\n    </LegacyTooltip>\n  );\n};\n"], "names": ["useRunsArtifacts", "runUuids", "artifactsKeyedByRun", "setArtifactsKeyedByRun", "useState", "isLoading", "setIsLoading", "error", "setError", "useEffect", "length", "async", "artifactsByRun", "Promise", "all", "map", "response", "listArtifactsApi", "runUuid", "artifacts", "payload", "err", "fetchArtifacts", "_ref2", "name", "styles", "_ref4", "CompareRunArtifactView", "_ref", "runInfos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "useDesignSystemTheme", "artifactPath", "setArtifactPath", "commonArtifacts", "_artifactsKeyedByRun$", "_artifactsKeyedByRun$2", "_artifactsKeyedByRun$3", "Object", "keys", "files", "file", "is_dir", "path", "filter", "i", "_artifactsKeyedByRun$4", "_artifactsKeyedByRun$5", "_commonArtifacts", "currentRunArtifacts", "includes", "getCommonArtifacts", "_jsx", "children", "FormattedMessage", "id", "defaultMessage", "_jsxs", "css", "_css", "backgroundColor", "colors", "backgroundPrimary", "color", "textPrimary", "flex", "whiteSpace", "border", "grey300", "overflowY", "ArtifactViewTree", "data", "active", "getBasename", "onToggleTreebeard", "_ref3", "borderLeft", "display", "flexDirection", "overflow", "index", "style", "width", "borderBottom", "padding", "spacing", "md", "ShowArtifactPage", "artifactRootUri", "artifactUri", "experimentId", "TabPane", "LegacyTabs", "_ref5", "CompareRunView", "Component", "constructor", "props", "super", "compareRunViewRef", "runDetailsTableRef", "this", "state", "tableWidth", "onlyShowParamDiff", "onlyShowTagDiff", "onlyShowMetricDiff", "onResizeHandler", "bind", "onCompareRunTableScrollHandler", "React", "e", "table", "current", "containerWidth", "clientWidth", "setState", "blocks", "querySelectorAll", "for<PERSON>ach", "_", "block", "target", "scrollLeft", "componentDidMount", "pageTitle", "intl", "formatMessage", "runs", "Utils", "updatePageTitle", "window", "addEventListener", "dispatchEvent", "Event", "componentWillUnmount", "removeEventListener", "getTableColumnWidth", "Math", "round", "renderExperimentNameRowItems", "experiments", "experimentNameMap", "getExperimentNameMap", "sortExperimentsById", "basename", "className", "Link", "to", "Routes", "getExperimentPageRoute", "title", "hasMultipleExperiments", "experimentIds", "shouldShowExperimentNameRow", "hasComparedExperimentsBefore", "getExperimentPageLink", "experimentName", "getCompareExperimentsPageLinkText", "numExperiments", "values", "getCompareExperimentsPageLink", "getCompareExperimentsPageRoute", "getExperimentLink", "comparedExperimentIds", "getTitle", "numRuns", "renderParamTable", "dataRows", "renderDataRows", "paramLists", "key", "value", "jsonValue", "parsePythonDictString", "<PERSON><PERSON><PERSON><PERSON><PERSON>son", "onScroll", "JSON", "stringify", "renderMetricTable", "metricLists", "getMetricPageRoute", "info", "uuid", "idx", "undefined", "formatMetric", "renderArtifactTable", "renderTagTable", "tagLists", "renderTimeRows", "colWidthStyle", "unknown", "timeAttributes", "runInfo", "startTime", "endTime", "formatTimestamp", "duration", "getDuration", "_ref6", "_ref7", "_ref8", "_ref9", "scope", "_ref0", "LegacyTooltip", "placement", "overlayStyle", "max<PERSON><PERSON><PERSON>", "dangerouslySetAntdProps", "mouseEnterDelay", "render", "runNames", "genWidthStyle", "breadcrumbs", "params<PERSON><PERSON>l", "metricsLabel", "artifactsLabel", "tagsLabel", "diffOn<PERSON><PERSON><PERSON>l", "displayChartSection", "shouldDisableLegacyRunCompareCharts", "ref", "<PERSON><PERSON><PERSON><PERSON>", "spacerSize", "CollapsibleSection", "tab", "ParallelCoordinatesPlotPanel", "CompareRunScatter", "runDisplayNames", "CompareRunBox", "CompareRunContour", "r", "_r$experimentId", "_r$runUuid", "getRunPageRoute", "runName", "Switch", "componentId", "label", "join", "checked", "onChange", "Spacer", "size", "min<PERSON><PERSON><PERSON>", "list", "onlyShowDiff", "highlightDiff", "arguments", "headerMap", "formatter", "CompareRunUtil", "get<PERSON><PERSON><PERSON>", "k", "Array", "fill", "records", "hasDiff", "some", "x", "rowClass", "cellText", "jsonString", "replace", "parse", "connect", "mapStateToProps", "ownProps", "compareExperiments", "getExperiment", "getRunInfo", "push", "getLatestMetrics", "getParams", "runTags", "getRunTags", "visibleTags", "getVisibleTagValues", "_ref1", "getRunDisplayName", "getRunName", "injectIntl", "CompareRunPageImpl", "requestIds", "requestError", "fetchExperiments", "experimentRequestId", "getUUID", "dispatch", "getExperimentApi", "catch", "requestId", "getRunApi", "_requestError$getMess", "Error", "getMessageField", "call", "<PERSON><PERSON><PERSON><PERSON>", "RequestStateWrapper", "suppressErrorThrow", "decodeURI", "uri", "decodedURI", "decodeURIComponent", "CompareRunPage", "withRouterNext", "location", "locationSearchDecoded", "search", "searchValues", "qs", "SyntaxError", "message", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "RUN_TRACKING", "Empty", "description", "image", "DangerIcon", "getAccordionStyles", "getPrefixedClassName", "clsPrefix", "classItem", "classHeader", "classContentBox", "fontSize", "paddingLeft", "paddingTop", "paddingBottom", "alignItems", "fontWeight", "lineHeight", "typography", "lineHeightLg", "xs", "forceOpen", "showServerError", "defaultCollapsed", "activeKeyProp", "active<PERSON><PERSON>", "defaultActiveKey", "useIntl", "getExpandIcon", "useCallback", "isActive", "importantify", "general", "heightBase", "transform", "ChevronRightIcon", "svg", "height", "Accordion", "dangerouslyAppendEmotionCSS", "expandIconPosition", "expandIcon", "Panel", "header", "SectionErrorBoundary", "ToggleIconButton", "pressed", "onClick", "icon", "onBlur", "onFocus", "onMouseEnter", "onMouseLeave", "analyticsEvents", "type", "remainingProps", "eventContext", "useDesignSystemEventComponentCallbacks", "componentType", "DesignSystemEventProviderComponentTypes", "<PERSON><PERSON>", "DesignSystemEventProviderAnalyticsEventTypes", "OnClick", "event", "cursor", "heightSm", "borderRadius", "legacyBorders", "borderRadiusMd", "lineHeightBase", "justifyContent", "background", "actionDefaultBackgroundPress", "actionDefaultTextPress", "textSecondary", "actionDefaultBackgroundHover", "actionDefaultTextHover", "fontFamily", "direction", "textAlign", "wordSpacing", "wordBreak", "MozTabSize", "OTabSize", "tabSize", "WebkitHyphens", "MozHyphens", "msHyphens", "hyphens", "margin", "textShadow", "comment", "prolog", "doctype", "cdata", "punctuation", "namespace", "Opacity", "tag", "operator", "number", "property", "function", "selector", "boolean", "string", "entity", "url", "keyword", "control", "directive", "unit", "statement", "regex", "at<PERSON>le", "placeholder", "variable", "deleted", "textDecoration", "inserted", "italic", "fontStyle", "important", "bold", "Outline", "OutlineOffset", "borderRightColor", "wordWrap", "position", "attribute", "builtin", "char", "class", "constant", "hexcode", "symbol", "Syntax<PERSON><PERSON><PERSON><PERSON>", "registerLanguage", "python", "json", "snippetPadding", "themesStyles", "light", "lightStyle", "duotoneDark", "duotoneDarkStyle", "CodeSnippet", "language", "actions", "showLineNumbers", "lineNumberStyle", "wrapLongLines", "customStyle", "codeTagProps", "pick", "JsonPreview", "wrapperStyle", "codeSnippetStyle", "formattedJson", "is<PERSON>son<PERSON><PERSON>nt", "useFormattedJson", "maxHeight", "_Fragment", "overflowX", "bottom", "right", "left", "parsed", "isJson", "isObject", "Date", "FormattedJsonDisplay", "icons", "rotateLeft", "UndoIcon", "rotateRight", "RedoIcon", "zoomIn", "ZoomInIcon", "zoomOut", "ZoomOutIcon", "close", "CloseIcon", "ArrowLeftIcon", "ArrowRightIcon", "ImagePreviewGroup", "visible", "onVisibleChange", "getPopupContainer", "useContext", "DesignSystemContext", "RcImage", "PreviewGroup", "preview", "getContainer", "v", "MIN_GRID_IMAGE_SIZE", "ImagePlot", "imageUrl", "compressedImageUrl", "imageSize", "maxImageSize", "previewVisible", "setPreviewVisible", "imageLoading", "setImageLoading", "img", "Image", "onload", "onerror", "src", "backgroundSecondary", "aspectRatio", "Spinner", "ImagePlotWithHistory", "metadataByStep", "step", "ImageIcon", "getArtifactLocationUrl", "filepath", "compressed_filepath", "EmptyImageGridPlot", "Typography", "Title", "level", "Text", "Copy<PERSON><PERSON><PERSON>", "copyText", "showLabel", "buttonProps", "showTooltip", "setShowTooltip", "handleClick", "navigator", "clipboard", "writeText", "setTimeout", "handleMouseLeave"], "sourceRoot": ""}