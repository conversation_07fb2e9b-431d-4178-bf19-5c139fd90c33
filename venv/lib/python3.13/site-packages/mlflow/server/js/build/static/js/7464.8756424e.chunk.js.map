{"version": 3, "file": "static/js/7464.8756424e.chunk.js", "mappings": "2NAWA,SAASA,IACP,OACEC,EAAAA,EAAAA,GAACC,EAAAA,IAAK,CACJ,cAAY,WACZC,OAAOF,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,UACxCC,aACEN,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sDAInBE,OAAOP,EAAAA,EAAAA,GAACQ,EAAAA,EAAU,KAGxB,CAEA,SAASC,EAAmBC,GAAsF,IAArF,SAAEC,EAAQ,wBAAEC,GAAsEF,EAC7G,SAASG,EAAkBC,EAAcC,GAEvCC,QAAQF,MAAM,4BAA6BA,EAAOC,EAAKE,eACzD,CAEA,OAAIL,GAEAZ,EAAAA,EAAAA,GAACkB,EAAAA,GAAa,CAACC,QAASN,EAAmBO,kBAAmBR,EAAwBD,SACnFA,KAMLX,EAAAA,EAAAA,GAACkB,EAAAA,GAAa,CAACC,QAASN,EAAmBQ,UAAUrB,EAAAA,EAAAA,GAACD,EAAa,IAAIY,SACpEA,GAGP,CAEO,SAASW,EACdC,EACAC,EACAC,EACAb,GAEA,OAAO,SAAoCc,GACzC,OACE1B,EAAAA,EAAAA,GAACS,EAAmB,CAACG,wBAAyBA,EAAwBD,UAEpEX,EAAAA,EAAAA,GAACwB,EAAS,IAAKE,KAGrB,CACF,C,sOCtDO,MAAMC,GACX,EAAAC,EAAAA,eAA+C,MCK3CC,EAAmC,CACvCC,UAAU,EACVhB,MAAO,MAGF,MAAMiB,UAAsBC,EAAAA,UAIjCC,MAAA,KAAQJ,EAAR,GAEA,+BAAOK,CAAyBpB,GAC9B,MAAO,CAAEgB,UAAU,E,MAAMhB,EAC3B,CAEAqB,mBAAA,UAAAC,EAAA,YAAqB,WACnB,MAAMtB,MAAEA,GAAUsB,EAAKH,MAEvB,GAAc,OAAVnB,EAAgB,SAAAuB,EAAAC,UAAAC,OAHGC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAIrBN,EAAKV,MAAMiB,UAAU,C,KACnBH,EACAI,OAAQ,mBAGVR,EAAKS,SAAShB,EAChB,CACF,CAAC,EAXD,GAaAiB,iBAAAA,CAAkBhC,EAAcC,GAC9BgC,KAAKrB,MAAMP,UAAUL,EAAOC,EAC9B,CAEAiC,kBAAAA,CACEC,EACAC,GAEA,MAAMpB,SAAEA,GAAaiB,KAAKd,OACpBkB,UAAEA,GAAcJ,KAAKrB,MAQzBI,GACoB,OAApBoB,EAAUpC,OAqDhB,WAAuD,IAA9BsC,EAAAd,UAAAC,OAAA,QAAAc,IAAAf,UAAA,GAAAA,UAAA,GAAW,GAAIgB,EAAAhB,UAAAC,OAAA,QAAAc,IAAAf,UAAA,GAAAA,UAAA,GAAW,GACjD,OACEc,EAAEb,SAAWe,EAAEf,QAAUa,EAAEG,MAAK,CAACC,EAAMC,KAAWC,OAAOC,GAAGH,EAAMF,EAAEG,KAExE,CAxDMG,CAAgBX,EAAUE,UAAWA,KAErCJ,KAAKrB,MAAMiB,UAAU,CACnBkB,KAAMV,EACNW,KAAMb,EAAUE,UAChBP,OAAQ,SAGVG,KAAKF,SAAShB,GAElB,CAEAkC,MAAAA,GACE,MAAMpD,SAAEA,EAAQqD,eAAEA,EAAc5C,kBAAEA,EAAiBC,SAAEA,GACnD0B,KAAKrB,OACDI,SAAEA,EAAQhB,MAAEA,GAAUiC,KAAKd,MAEjC,IAAIgC,EAAgBtD,EAEpB,GAAImB,EAAU,CACZ,MAAMJ,EAAuB,C,MAC3BZ,EACAqB,mBAAoBY,KAAKZ,oBAG3B,IAAI,EAAA+B,EAAAA,gBAAe7C,GACjB4C,EAAgB5C,OACX,GAA8B,oBAAnB2C,EAChBC,EAAgBD,EAAetC,OAC1B,KAAIN,EAGT,MAAM,IAAI+C,MACR,8FAHFF,GAAgB,EAAAG,EAAAA,eAAchD,EAAmBM,EAG/C,CAGN,CAEA,OAAO,EAAA0C,EAAAA,eACLzC,EAAqB0C,SACrB,CACEC,MAAO,C,SACLxC,E,MACAhB,EACAqB,mBAAoBY,KAAKZ,qBAG7B8B,EAEJ,EC5GK,SAASM,EACdD,GAEA,GACW,MAATA,GAC0B,mBAAnBA,EAAMxC,UACuB,oBAA7BwC,EAAMnC,mBAEb,MAAM,IAAIgC,MAAM,kCAGlB,OAAO,CACT,CCLO,SAASK,IACd,MAAMC,GAAU,EAAAC,EAAAA,YAAW/C,GAE3B4C,EAA2BE,GAE3B,MAAOxC,EAAOY,IAAY,EAAA8B,EAAAA,UAGvB,CACD7D,MAAO,KACP8D,UAAU,IAGNC,GAAW,EAAAC,EAAAA,UACf,KAAM,CACJC,cAAeA,KACbN,GAAStC,qBACTU,EAAS,CAAE/B,MAAO,KAAM8D,UAAU,GAAQ,EAE5CI,aAAelE,GACb+B,EAAS,C,MACP/B,EACA8D,UAAU,OAGhB,CAACH,GAAStC,qBAGZ,GAAIF,EAAM2C,SACR,MAAM3C,EAAMnB,MAGd,OAAO+D,CACT,C,iCCtCO,SAASI,EACdzD,EACA0D,GAEA,MAAMC,EAAiCzD,IAC9B,EAAA0C,EAAAA,eACLrC,EACAmD,GACA,EAAAd,EAAAA,eAAc5C,EAAWE,IAKvB0D,EAAO5D,EAAU6D,aAAe7D,EAAU4D,MAAQ,UAGxD,OAFAD,EAAQE,YAAc,qBAAqBD,KAEpCD,CACT,C,uICHO,SAASG,EAAc5D,GAC5B,MAAM,eAAE6D,KAAmBC,GAAc9D,EACzC,OAEE+D,EAAAA,EAAAA,IAACC,EAAAA,IAAW,CAACC,IAAKJ,EAAiBK,EAAOC,oBAAsBD,EAAOE,QAAQnF,SAAA,EAE7EX,EAAAA,EAAAA,GAAC+F,EAAAA,EAAM,CAACJ,IAAKC,EAAOI,cACnBT,EAAiB7D,EAAMf,UAAWX,EAAAA,EAAAA,GAAA,UAASwF,EAAWG,IAAKC,EAAOK,cAGzE,CAEAX,EAAcY,aAAe,CAC3BX,gBAAgB,GAGlB,MAAMK,EAAS,CACbC,oBAAqB,CACnBM,OAAQ,oBACRC,QAAS,OACTC,cAAe,SACf,eAAgB,CACdC,SAAU,IAGdR,QAAS,CAAES,KAAM,GACjBP,YAAa,CAEXQ,WAAY,GAEdP,UAAW,CACTQ,MAAO,OACPH,SAAU,EACVI,cAAe,I,6FC9CnB,MAAMC,GAAAA,EACGC,eAAiB,CACtBC,eAAgB,iBAChBC,YAAa,cACbC,cAAe,gBACfC,aAAc,gBAOX,MAAMC,EAAmCA,CAACC,EAA4BC,KAC3E,KAAMD,aAAwBE,EAAAA,GAC5B,OAEF,MAAM,OAAEC,GAAWH,EACnB,IAAIpG,EACJ,MAAMwG,EAAsB,CAAED,UAC1BH,EAAaK,iBAAmBC,EAAAA,GAAWC,0BAC7C3G,EAAQ,IAAI4G,EAAAA,GAAcJ,IAExBJ,EAAaK,iBAAmBC,EAAAA,GAAWG,oBAC7C7G,EAAQ,IAAI8G,EAAAA,GAAgBN,IAE1BJ,EAAaK,iBAAmBC,EAAAA,GAAWK,iBAC7C/G,EAAQ,IAAIgH,EAAAA,GAAoBR,IAE9BJ,EAAaK,iBAAmBC,EAAAA,GAAWO,0BAC7CjH,EAAQ,IAAIkH,EAAAA,GAAgBV,IAI9B,MAAMW,EAA0Bf,EAAagB,kBAK7C,OAJIpH,GAASmH,IACXnH,EAAMqH,QAAUF,GAGXnH,CAAK,EAEd,K,wGCpCA,MAIasH,EAAuB1H,IAMF,IANG,MACnC4D,EAAK,SACL+D,EAAQ,QACRC,EAAO,UACPC,EAAS,QACTC,GAAU,GACgB9H,EAC1B,MAAM,MAAE+H,IAAUC,EAAAA,EAAAA,KAClB,OACE1I,EAAAA,EAAAA,GAAC2I,EAAAA,IAAG,CACFC,YAAY,uFACZjD,KAAGkD,EAAAA,EAAAA,IAAE,CACHC,WAAYL,EAAMM,WAAWC,yBAC7BC,YAAaR,EAAMS,QAAQC,IAC5B,IACDZ,UAAWA,EACXF,SAAUA,EACVC,QAASA,EACTpI,MAAOoE,EAAM3D,UAEb8E,EAAAA,EAAAA,IAAA,QACEE,KAAGkD,EAAAA,EAAAA,IAAE,CACHzC,QAAS,QACTgD,WAAY,SACZC,SAAUb,EA5BW,IACL,IA4BhBc,aAAc,WACdC,SAAU,UACX,IAAC5I,SAAA,CA7BS,IA+BC,OAAO2D,MAEjB,C,oHCzCV,MAAMkF,EAA2B,sDAE3BC,EAAsBC,EAAAA,cAGzB,CACDC,mBAAmBC,EAAAA,EAAAA,MACnBC,qBAAsBA,SAMXC,EAAyBA,KAAMC,EAAAA,EAAAA,YAAWN,GAS1CO,EAQTxI,GAEDE,IACC,MAAOiI,EAAmBE,IAAwBI,EAAAA,EAAAA,UACG,SAAnDC,aAAaC,QAAQX,KAGvBY,EAAAA,EAAAA,YAAU,KACRF,aAAaG,QAAQb,IAA4BG,GAAmBW,WAAW,GAC9E,CAACX,IAEJ,MAAMY,GAAeC,EAAAA,EAAAA,UAAQ,MAASb,oBAAmBE,0BAAyB,CAACF,IAEnF,OAAKC,EAAAA,EAAAA,OAKH5J,EAAAA,EAAAA,GAACyJ,EAAoBpF,SAAQ,CAACC,MAAOiG,EAAa5J,UAChDX,EAAAA,EAAAA,GAACwB,EAAS,IAAKE,EAAOiI,kBAAmBY,EAAaZ,uBALjD3J,EAAAA,EAAAA,GAACwB,EAAS,IAAKE,EAAOiI,mBAAmB,GAMjB,C,4DCrDjCc,EAAOC,EAAOC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAAQC,EAASC,EAASC,EAAQC,EAASC,EAAQC,EAASC,EAAQC,EAASC,E,uDACvL,SAASC,IAAa,OAAOA,EAAWtI,OAAOuI,OAASvI,OAAOuI,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAI9J,UAAUC,OAAQ6J,IAAK,CAAE,IAAIC,EAAI/J,UAAU8J,GAAI,IAAK,IAAIE,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOH,EAAEG,GAAKD,EAAEC,GAAK,CAAE,OAAOH,CAAG,EAAGH,EAASS,MAAM,KAAMnK,UAAY,CAEnR,SAASoK,EAAqBhM,EAAMiM,GAClC,IAAI,MACFzM,EAAK,QACL0M,KACGlL,GACDhB,EACJ,OAAoB,gBAAoB,MAAOsL,EAAS,CACtDvF,MAAO,IACPN,OAAQ,IACR0G,QAAS,cACTC,KAAM,OACNC,MAAO,6BACPC,IAAKL,EACL,kBAAmBC,GAClBlL,GAAQxB,EAAqB,gBAAoB,QAAS,CAC3DE,GAAIwM,GACH1M,GAAS,KAAMuK,IAAUA,EAAqB,gBAAoB,OAAQ,CAC3EhE,MAAO,IACPN,OAAQ,IACR2G,KAAM,aACHpC,IAAUA,EAAqB,gBAAoB,OAAQ,CAC9DuC,EAAG,49MACHH,KAAM,aACHnC,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEsC,EAAG,y4LACHH,KAAM,aACHlC,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEqC,EAAG,m6BACHH,KAAM,aACHjC,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEqC,EAAG,GACHC,EAAG,IACH1G,MAAO,GACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,aACHhC,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEmC,EAAG,mgHACHH,KAAM,aACH/B,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEkC,EAAG,svCACHH,KAAM,aACH9B,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEkC,EAAG,GACHC,EAAG,IACH1G,MAAO,GACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,aACH7B,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEgC,EAAG,65GACHH,KAAM,WACH5B,IAAWA,EAAsB,gBAAoB,OAAQ,CAChE+B,EAAG,gMACHH,KAAM,oCACH3B,IAAWA,EAAsB,gBAAoB,OAAQ,CAChE8B,EAAG,s6BACHH,KAAM,aACH1B,IAAWA,EAAsB,gBAAoB,OAAQ,CAChE8B,EAAG,MACHC,EAAG,IACH1G,MAAO,GACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,aACHzB,IAAWA,EAAsB,gBAAoB,OAAQ,CAChE4B,EAAG,2oJACHH,KAAM,WACHxB,IAAWA,EAAsB,gBAAoB,OAAQ,CAChE4B,EAAG,IACHC,EAAG,IACH1G,MAAO,QACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,UACNO,YAAa,OACV9B,IAAYA,EAAuB,gBAAoB,OAAQ,CAClE0B,EAAG,spEACHH,KAAM,aACHtB,IAAYA,EAAuB,gBAAoB,OAAQ,CAClEyB,EAAG,yvCACHH,KAAM,aACHrB,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEyB,EAAG,IACHC,EAAG,IACH1G,MAAO,GACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,aACHpB,IAAYA,EAAuB,gBAAoB,OAAQ,CAClEuB,EAAG,qzIACHH,KAAM,WACHnB,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEuB,EAAG,IACHC,EAAG,IACH1G,MAAO,GACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,aACHlB,IAAYA,EAAuB,gBAAoB,OAAQ,CAClEqB,EAAG,qgHACHH,KAAM,WACHjB,IAAWA,EAAsB,gBAAoB,OAAQ,CAChEqB,EAAG,IACHC,EAAG,IACH1G,MAAO,GACPN,OAAQ,GACRiH,GAAI,EACJN,KAAM,UACNO,YAAa,OACVvB,IAAYA,EAAuB,gBAAoB,OAAQ,CAClEmB,EAAG,s1FACHH,KAAM,aACHf,IAAUA,EAAqB,gBAAoB,OAAQ,KAAmB,gBAAoB,iBAAkB,CACvH3L,GAAI,2BACJkN,GAAI,QACJC,GAAI,IACJC,GAAI,IACJC,GAAI,IACJC,cAAe,kBACD,gBAAoB,OAAQ,CAC1CC,UAAW,YACI,gBAAoB,OAAQ,CAC3CC,OAAQ,EACRD,UAAW,UACXE,YAAa,OAEjB,CACA,MAAMC,EAA0B,aAAiBpB,GAClC,IAAf,I,sBC/HO,MAAMqB,EAAyBrN,IAAA,IAAC,QACrCsN,EAAO,QACP1F,EAAO,WACP2F,GAKDvN,EAAA,OACC+E,EAAAA,EAAAA,IAACyI,EAAAA,EAAK,CACJtF,YAAY,iFACZoF,QAASA,EACT9N,OACEF,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kEAInB8N,SAAU7F,EACV8F,QACE3I,EAAAA,EAAAA,IAAA4I,EAAAA,GAAA,CAAA1N,SAAA,EACEX,EAAAA,EAAAA,GAACsO,EAAAA,EAAM,CACL1F,YAAY,iFACZ2F,KAAMC,EAAAA,GACNC,IAAI,WACJC,OAAO,SAAQ/N,UAEfX,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAInBL,EAAAA,EAAAA,GAACsO,EAAAA,EAAM,CACL1F,YAAY,iFACZ+F,KAAK,UACLC,QAASX,EAAWtN,UAEpBX,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,oBAKtBM,SAAA,EAEDX,EAAAA,EAAAA,GAAC6O,EAAe,CAACpI,MAAM,UACvBzG,EAAAA,EAAAA,GAAC8O,EAAAA,EAAWC,KAAI,CAAApO,UACdX,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAgB,wSAIhB2O,OAAQ,CACN1L,EAAI2L,IAAgBjP,EAAAA,EAAAA,GAAA,KAAAW,SAAIsO,WAIxB,E,0BCxDV,MAAMC,EAA2B,+CAEpBC,EAA2BA,KACtC,MAAM,kBAAExF,EAAiB,qBAAEE,IAAyBC,EAAAA,EAAAA,KAE9CsF,EAA8E,SAA1DC,OAAOnF,aAAaC,QAAQ+E,IAE/CI,EAAmBC,IAAwBtF,EAAAA,EAAAA,WAAUmF,IACrDI,EAA4BC,IAAiCxF,EAAAA,EAAAA,WAAS,GAEvEyF,GAAuBC,EAAAA,EAAAA,cAAY,KACvCJ,GAAqB,GACrBF,OAAOnF,aAAaG,QAAQ6E,EAA0B,OAAO,GAC5D,IAGGU,GADOC,EAAAA,EAAAA,KACMC,cAAc,CAAA1P,GAAA,SAC/BC,eAAe,2BAUX,MAAEoI,IAAUC,EAAAA,EAAAA,KAClB,OACEjD,EAAAA,EAAAA,IAAA4I,EAAAA,GAAA,CAAA1N,SAAA,EACE8E,EAAAA,EAAAA,IAAA,OAAKsK,MAAO,CAAE3J,QAAS,OAAQ4J,WAAY,SAAUC,IAAKxH,EAAMS,QAAQC,IAAKxI,SAAA,EAC3EX,EAAAA,EAAAA,GAAA,SAAAW,SAAQiP,KACR5P,EAAAA,EAAAA,GAACkQ,EAAAA,IAAM,CACLtH,YAAY,mFACZuH,QAASxG,EACT,aAAYiG,EACZQ,SAhBcC,IACfA,EAGHxG,GAAqB,GAFrB4F,GAA8B,EAGhC,QAcEzP,EAAAA,EAAAA,GAAC+N,EAAsB,CACrBC,QAASsB,EACThH,QAASA,KACPoH,GAAsB,EAExBzB,WAAYA,KACVyB,GAAsB,KAG1B1P,EAAAA,EAAAA,GAACkO,EAAAA,EAAK,CACJtF,YAAY,mFACZoF,QAASwB,EACTtP,OACEF,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iCAInBiQ,OAAO,UACPnC,SAAUA,KACRsB,GAA8B,EAAM,EAEtCc,KAAMA,KACJ1G,GAAqB,GACrB4F,GAA8B,EAAM,EACpC9O,UAEFX,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gMAIf2O,OAAQ,CACNwB,KAAOvB,IACLjP,EAAAA,EAAAA,GAAC8O,EAAAA,EAAW2B,KAAI,CACd7H,YAAY,mFACZ2F,KAAK,sCACLmC,cAAY,EAAA/P,SAEXsO,WAMV,C", "sources": ["common/utils/withErrorBoundary.tsx", "../node_modules/react-error-boundary/dist/src/ErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/ErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/assertErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/useErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/withErrorBoundary.ts", "common/components/PageContainer.tsx", "common/utils/ErrorUtils.tsx", "model-registry/components/aliases/ModelVersionAliasTag.tsx", "model-registry/hooks/useNextModelsUI.tsx", "common/static/promo-modal-content.svg", "model-registry/components/ModelsNextUIPromoModal.tsx", "model-registry/components/ModelsNextUIToggleSwitch.tsx"], "sourcesContent": ["import React from 'react';\nimport { ErrorBoundary, ErrorBoundaryPropsWithComponent, FallbackProps } from 'react-error-boundary';\nimport ErrorUtils from './ErrorUtils';\nimport { DangerIcon, Empty } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nexport type ErrorBoundaryProps = {\n  children: React.Component;\n  customFallbackComponent?: ErrorBoundaryPropsWithComponent['FallbackComponent'];\n};\n\nfunction ErrorFallback() {\n  return (\n    <Empty\n      data-testid=\"fallback\"\n      title={<FormattedMessage defaultMessage=\"Error\" description=\"Title of editor error fallback component\" />}\n      description={\n        <FormattedMessage\n          defaultMessage=\"An error occurred while rendering this component.\"\n          description=\"Description of error fallback component\"\n        />\n      }\n      image={<DangerIcon />}\n    />\n  );\n}\n\nfunction CustomErrorBoundary({ children, customFallbackComponent }: React.PropsWithChildren<ErrorBoundaryProps>) {\n  function logErrorToConsole(error: Error, info: { componentStack: string }) {\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    console.error('Caught Unexpected Error: ', error, info.componentStack);\n  }\n\n  if (customFallbackComponent) {\n    return (\n      <ErrorBoundary onError={logErrorToConsole} FallbackComponent={customFallbackComponent}>\n        {children}\n      </ErrorBoundary>\n    );\n  }\n\n  return (\n    <ErrorBoundary onError={logErrorToConsole} fallback={<ErrorFallback />}>\n      {children}\n    </ErrorBoundary>\n  );\n}\n\nexport function withErrorBoundary<P>(\n  service: string,\n  Component: React.ComponentType<P>,\n  errorMessage?: React.ReactNode,\n  customFallbackComponent?: React.ComponentType<FallbackProps>,\n): React.ComponentType<P> {\n  return function CustomErrorBoundaryWrapper(props: P) {\n    return (\n      <CustomErrorBoundary customFallbackComponent={customFallbackComponent}>\n        {/* @ts-expect-error Generics don't play well with WithConditionalCSSProp type coming @emotion/react jsx typing to validate css= prop values typing. More details here: emotion-js/emotion#2169 */}\n        <Component {...props} />\n      </CustomErrorBoundary>\n    );\n  };\n}\n", "import { createContext } from \"react\";\n\nexport type ErrorBoundaryContextType = {\n  didCatch: boolean;\n  error: any;\n  resetErrorBoundary: (...args: any[]) => void;\n};\n\nexport const ErrorBoundaryContext =\n  createContext<ErrorBoundaryContextType | null>(null);\n", "import {\n  Component,\n  createElement,\n  ErrorInfo,\n  isValidElement,\n  PropsWithChildren,\n  PropsWithRef,\n  ReactElement,\n} from \"react\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\nimport { ErrorBoundaryProps, FallbackProps } from \"./types\";\n\ntype ErrorBoundaryState = { didCatch: boolean; error: any };\n\nconst initialState: ErrorBoundaryState = {\n  didCatch: false,\n  error: null,\n};\n\nexport class ErrorBoundary extends Component<\n  PropsWithRef<PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  state = initialState;\n\n  static getDerivedStateFromError(error: Error) {\n    return { didCatch: true, error };\n  }\n\n  resetErrorBoundary = (...args: any[]) => {\n    const { error } = this.state;\n\n    if (error !== null) {\n      this.props.onReset?.({\n        args,\n        reason: \"imperative-api\",\n      });\n\n      this.setState(initialState);\n    }\n  };\n\n  componentDidCatch(error: Error, info: ErrorInfo) {\n    this.props.onError?.(error, info);\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState\n  ) {\n    const { didCatch } = this.state;\n    const { resetKeys } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (\n      didCatch &&\n      prevState.error !== null &&\n      hasArrayChanged(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onReset?.({\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\",\n      });\n\n      this.setState(initialState);\n    }\n  }\n\n  render() {\n    const { children, fallbackRender, FallbackComponent, fallback } =\n      this.props;\n    const { didCatch, error } = this.state;\n\n    let childToRender = children;\n\n    if (didCatch) {\n      const props: FallbackProps = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      };\n\n      if (isValidElement(fallback)) {\n        childToRender = fallback;\n      } else if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else {\n        throw new Error(\n          \"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\"\n        );\n      }\n    }\n\n    return createElement(\n      ErrorBoundaryContext.Provider,\n      {\n        value: {\n          didCatch,\n          error,\n          resetErrorBoundary: this.resetErrorBoundary,\n        },\n      },\n      childToRender\n    ) as ReactElement;\n  }\n}\n\nfunction hasArrayChanged(a: any[] = [], b: any[] = []) {\n  return (\n    a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n  );\n}\n", "import { ErrorBoundaryContextType } from \"./ErrorBoundaryContext\";\n\nexport function assertErrorBoundaryContext(\n  value: any\n): value is ErrorBoundaryContextType {\n  if (\n    value == null ||\n    typeof value.didCatch !== \"boolean\" ||\n    typeof value.resetErrorBoundary !== \"function\"\n  ) {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n\n  return true;\n}\n", "import { useContext, useMemo, useState } from \"react\";\nimport { assertErrorBoundaryContext } from \"./assertErrorBoundaryContext\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\n\nexport type UseErrorBoundaryApi<Error> = {\n  resetBoundary: () => void;\n  showBoundary: (error: Error) => void;\n};\n\nexport function useErrorBoundary<Error = any>(): UseErrorBoundaryApi<Error> {\n  const context = useContext(ErrorBoundaryContext);\n\n  assertErrorBoundaryContext(context);\n\n  const [state, setState] = useState<{\n    error: Error | null;\n    hasError: boolean;\n  }>({\n    error: null,\n    hasError: false,\n  });\n\n  const memoized = useMemo(\n    () => ({\n      resetBoundary: () => {\n        context?.resetErrorBoundary();\n        setState({ error: null, hasError: false });\n      },\n      showBoundary: (error: Error) =>\n        setState({\n          error,\n          hasError: true,\n        }),\n    }),\n    [context?.resetErrorBoundary]\n  );\n\n  if (state.hasError) {\n    throw state.error;\n  }\n\n  return memoized;\n}\n", "import { ComponentType, createElement } from \"react\";\nimport { ErrorBoundary } from \"./ErrorBoundary\";\nimport { ErrorBoundaryProps } from \"./types\";\n\nexport function withErrorBoundary<Props extends Object>(\n  Component: ComponentType<Props>,\n  errorBoundaryProps: ErrorBoundaryProps\n): ComponentType<Props> {\n  const Wrapped: ComponentType<Props> = (props: Props) => {\n    return createElement(\n      ErrorBoundary,\n      errorBoundaryProps,\n      createElement(Component, props)\n    );\n  };\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || \"Unknown\";\n  Wrapped.displayName = `withErrorBoundary(${name})`;\n\n  return Wrapped;\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { PageWrapper, Spacer } from '@databricks/design-system';\n\ntype OwnProps = {\n  usesFullHeight?: boolean;\n  children?: React.ReactNode;\n};\n\n// @ts-expect-error TS(2565): Property 'defaultProps' is used before being assig... Remove this comment to see the full error message\ntype Props = OwnProps & typeof PageContainer.defaultProps;\n\nexport function PageContainer(props: Props) {\n  const { usesFullHeight, ...restProps } = props;\n  return (\n    // @ts-expect-error TS(2322): Type '{ height: string; display: string; flexDirec... Remove this comment to see the full error message\n    <PageWrapper css={usesFullHeight ? styles.useFullHeightLayout : styles.wrapper}>\n      {/* @ts-expect-error TS(2322): Type '{ css: { flexShrink: number; }; }' is not as... Remove this comment to see the full error message */}\n      <Spacer css={styles.fixedSpacer} />\n      {usesFullHeight ? props.children : <div {...restProps} css={styles.container} />}\n    </PageWrapper>\n  );\n}\n\nPageContainer.defaultProps = {\n  usesFullHeight: false,\n};\n\nconst styles = {\n  useFullHeightLayout: {\n    height: 'calc(100% - 60px)', // 60px comes from header height\n    display: 'flex',\n    flexDirection: 'column',\n    '&:last-child': {\n      flexGrow: 1,\n    },\n  },\n  wrapper: { flex: 1 },\n  fixedSpacer: {\n    // Ensure spacer's fixed height regardless of flex\n    flexShrink: 0,\n  },\n  container: {\n    width: '100%',\n    flexGrow: 1,\n    paddingBottom: 24,\n  },\n};\n", "import React from 'react';\nimport { BadRequestError, InternalServerError, NotFoundError, PermissionError } from '@databricks/web-shared/errors';\nimport { ErrorWrapper } from './ErrorWrapper';\nimport { ErrorCodes } from '../constants';\n\nclass ErrorUtils {\n  static mlflowServices = {\n    MODEL_REGISTRY: 'Model Registry',\n    EXPERIMENTS: 'Experiments',\n    MODEL_SERVING: 'Model Serving',\n    RUN_TRACKING: 'Run Tracking',\n  };\n}\n\n/**\n * Maps known types of ErrorWrapper (legacy) to platform's predefined error instances.\n */\nexport const mapErrorWrapperToPredefinedError = (errorWrapper: ErrorWrapper, requestId?: string) => {\n  if (!(errorWrapper instanceof ErrorWrapper)) {\n    return undefined;\n  }\n  const { status } = errorWrapper;\n  let error: Error | undefined = undefined;\n  const networkErrorDetails = { status };\n  if (errorWrapper.getErrorCode() === ErrorCodes.RESOURCE_DOES_NOT_EXIST) {\n    error = new NotFoundError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.PERMISSION_DENIED) {\n    error = new PermissionError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.INTERNAL_ERROR) {\n    error = new InternalServerError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.INVALID_PARAMETER_VALUE) {\n    error = new BadRequestError(networkErrorDetails);\n  }\n\n  // Attempt to extract message from error wrapper and assign it to the error instance.\n  const messageFromErrorWrapper = errorWrapper.getMessageField();\n  if (error && messageFromErrorWrapper) {\n    error.message = messageFromErrorWrapper;\n  }\n\n  return error;\n};\nexport default ErrorUtils;\n", "import { Tag, useDesignSystemTheme } from '@databricks/design-system';\nimport type { TagProps } from '@databricks/design-system';\n\ntype ModelVersionAliasTagProps = { value: string; compact?: boolean } & Pick<\n  TagProps,\n  'closable' | 'onClose' | 'className'\n>;\n\n// When displayed in compact mode (e.g. within <Select>), constrain the width to 160 pixels\nconst COMPACT_MODE_MAX_WIDTH = 160;\nconst REGULAR_MAX_WIDTH = 300;\nconst TAG_SYMBOL = '@';\n\nexport const ModelVersionAliasTag = ({\n  value,\n  closable,\n  onClose,\n  className,\n  compact = false,\n}: ModelVersionAliasTagProps) => {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <Tag\n      componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversionaliastag.tsx_23\"\n      css={{\n        fontWeight: theme.typography.typographyBoldFontWeight,\n        marginRight: theme.spacing.xs,\n      }}\n      className={className}\n      closable={closable}\n      onClose={onClose}\n      title={value}\n    >\n      <span\n        css={{\n          display: 'block',\n          whiteSpace: 'nowrap',\n          maxWidth: compact ? COMPACT_MODE_MAX_WIDTH : REGULAR_MAX_WIDTH,\n          textOverflow: 'ellipsis',\n          overflow: 'hidden',\n        }}\n      >\n        {TAG_SYMBOL}&nbsp;{value}\n      </span>\n    </Tag>\n  );\n};\n", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport { shouldShowModelsNextUI } from '../../common/utils/FeatureUtils';\n\nconst useOldModelsUIStorageKey = '_mlflow_user_setting_dismiss_next_model_registry_ui';\n\nconst NextModelsUIContext = React.createContext<{\n  usingNextModelsUI: boolean;\n  setUsingNextModelsUI: (newValue: boolean) => void;\n}>({\n  usingNextModelsUI: shouldShowModelsNextUI(),\n  setUsingNextModelsUI: () => {},\n});\n\n/**\n * Get\n */\nexport const useNextModelsUIContext = () => useContext(NextModelsUIContext);\n\n/**\n * Wraps the component with tools allowing to get and change the current value of\n * \"use next models UI\" toggle flag. It will wrap the component with the relevant React Context\n * and in order to make it usable in class components, it also injects `usingNextModelsUI`\n * boolean prop with the current flag value. To easily access the context in the downstream\n * function components, use `useNextModelsUIContext()` hook.\n */\nexport const withNextModelsUIContext =\n  <\n    BaseProps,\n    P extends JSX.IntrinsicAttributes &\n      JSX.LibraryManagedAttributes<React.ComponentType<BaseProps>, React.PropsWithChildren<BaseProps>> & {\n        usingNextModelsUI?: boolean;\n      },\n  >(\n    Component: React.ComponentType<BaseProps>,\n  ) =>\n  (props: P) => {\n    const [usingNextModelsUI, setUsingNextModelsUI] = useState(\n      localStorage.getItem(useOldModelsUIStorageKey) !== 'true',\n    );\n\n    useEffect(() => {\n      localStorage.setItem(useOldModelsUIStorageKey, (!usingNextModelsUI).toString());\n    }, [usingNextModelsUI]);\n\n    const contextValue = useMemo(() => ({ usingNextModelsUI, setUsingNextModelsUI }), [usingNextModelsUI]);\n\n    if (!shouldShowModelsNextUI()) {\n      return <Component {...props} usingNextModelsUI={false} />;\n    }\n\n    return (\n      <NextModelsUIContext.Provider value={contextValue}>\n        <Component {...props} usingNextModelsUI={contextValue.usingNextModelsUI} />\n      </NextModelsUIContext.Provider>\n    );\n  };\n", "var _rect, _path, _path2, _path3, _rect2, _path4, _path5, _rect3, _path6, _path7, _path8, _rect4, _path9, _rect5, _path10, _path11, _rect6, _path12, _rect7, _path13, _rect8, _path14, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgPromoModalContent(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 596,\n    height: 283,\n    viewBox: \"0 0 596 283\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    width: 596,\n    height: 283,\n    fill: \"#F2F5F7\"\n  })), _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M29.1299 118V108.84H32.7861C34.646 108.84 35.9028 110.065 35.9028 111.9V111.913C35.9028 113.741 34.646 114.972 32.7861 114.972H30.7676V118H29.1299ZM32.3862 110.18H30.7676V113.652H32.3862C33.5605 113.652 34.2461 113.017 34.2461 111.919V111.906C34.2461 110.808 33.5605 110.18 32.3862 110.18ZM37.5469 118V111.062H39.1274V112.128H39.2354C39.4702 111.386 40.105 110.948 41.019 110.948C41.2603 110.948 41.5078 110.979 41.6602 111.024V112.439C41.4062 112.389 41.146 112.351 40.873 112.351C39.8193 112.351 39.1274 112.979 39.1274 113.969V118H37.5469ZM45.748 118.14C43.6851 118.14 42.4346 116.756 42.4346 114.547V114.541C42.4346 112.357 43.6978 110.929 45.6719 110.929C47.646 110.929 48.8647 112.312 48.8647 114.407V114.928H44.0151C44.0342 116.172 44.707 116.896 45.7798 116.896C46.6367 116.896 47.1255 116.464 47.2778 116.146L47.2969 116.102H48.8013L48.7822 116.159C48.5601 117.054 47.6333 118.14 45.748 118.14ZM45.6909 112.167C44.8086 112.167 44.1484 112.763 44.0278 113.868H47.3223C47.2144 112.731 46.5732 112.167 45.6909 112.167ZM52.1465 118L49.6772 111.062H51.3657L52.9717 116.458H53.0796L54.6729 111.062H56.3359L53.8794 118H52.1465ZM58.4814 109.9C57.9609 109.9 57.5356 109.488 57.5356 108.967C57.5356 108.453 57.9609 108.034 58.4814 108.034C58.9956 108.034 59.4209 108.453 59.4209 108.967C59.4209 109.488 58.9956 109.9 58.4814 109.9ZM57.688 118V111.062H59.2686V118H57.688ZM64.207 118.14C62.125 118.14 60.8682 116.781 60.8682 114.534V114.521C60.8682 112.293 62.144 110.929 64.207 110.929C66.2764 110.929 67.5459 112.287 67.5459 114.521V114.534C67.5459 116.781 66.2827 118.14 64.207 118.14ZM64.207 116.857C65.3052 116.857 65.9272 116 65.9272 114.541V114.528C65.9272 113.068 65.2988 112.205 64.207 112.205C63.1089 112.205 62.4805 113.068 62.4805 114.528V114.541C62.4805 116 63.1089 116.857 64.207 116.857ZM71.4307 118.14C69.8691 118.14 69.0693 117.2 69.0693 115.55V111.062H70.6499V115.188C70.6499 116.261 71.0498 116.807 72.002 116.807C73.0239 116.807 73.5698 116.146 73.5698 115.105V111.062H75.1504V118H73.5698V116.946H73.4619C73.1382 117.689 72.4336 118.14 71.4307 118.14ZM79.7017 118.14C77.937 118.14 76.8706 117.327 76.7373 116.153V116.14H78.3052L78.3115 116.153C78.4766 116.635 78.9399 116.972 79.7271 116.972C80.5142 116.972 81.0601 116.616 81.0601 116.096V116.083C81.0601 115.677 80.7554 115.41 79.9937 115.239L78.8701 114.985C77.5498 114.693 76.9214 114.071 76.9214 113.049V113.042C76.9214 111.792 78.064 110.929 79.7017 110.929C81.3901 110.929 82.3994 111.767 82.501 112.89V112.903H81.0156L81.0093 112.884C80.895 112.446 80.438 112.09 79.6953 112.09C78.9907 112.09 78.4829 112.433 78.4829 112.954V112.96C78.4829 113.366 78.7749 113.614 79.5239 113.785L80.6475 114.033C82.0059 114.337 82.647 114.915 82.647 115.924V115.937C82.647 117.257 81.4092 118.14 79.7017 118.14ZM90.6768 118.14C88.6138 118.14 87.3633 116.756 87.3633 114.547V114.541C87.3633 112.357 88.6265 110.929 90.6006 110.929C92.5747 110.929 93.7935 112.312 93.7935 114.407V114.928H88.9438C88.9629 116.172 89.6357 116.896 90.7085 116.896C91.5654 116.896 92.0542 116.464 92.2065 116.146L92.2256 116.102H93.73L93.7109 116.159C93.4888 117.054 92.562 118.14 90.6768 118.14ZM90.6196 112.167C89.7373 112.167 89.0771 112.763 88.9565 113.868H92.251C92.1431 112.731 91.502 112.167 90.6196 112.167ZM94.5679 118L96.8149 114.56L94.5488 111.062H96.377L97.7544 113.468H97.8623L99.2334 111.062H100.966L98.7256 114.49L100.992 118H99.2144L97.7925 115.588H97.6846L96.269 118H94.5679ZM102.382 120.304V111.062H103.962V112.147H104.07C104.451 111.392 105.207 110.948 106.178 110.948C107.917 110.948 109.028 112.332 109.028 114.528V114.541C109.028 116.75 107.936 118.114 106.178 118.114C105.226 118.114 104.432 117.645 104.07 116.896H103.962V120.304H102.382ZM105.683 116.781C106.762 116.781 107.416 115.931 107.416 114.541V114.528C107.416 113.131 106.762 112.281 105.683 112.281C104.61 112.281 103.943 113.131 103.943 114.521V114.534C103.943 115.924 104.61 116.781 105.683 116.781ZM113.592 118.14C111.529 118.14 110.278 116.756 110.278 114.547V114.541C110.278 112.357 111.542 110.929 113.516 110.929C115.49 110.929 116.708 112.312 116.708 114.407V114.928H111.859C111.878 116.172 112.551 116.896 113.624 116.896C114.48 116.896 114.969 116.464 115.122 116.146L115.141 116.102H116.645L116.626 116.159C116.404 117.054 115.477 118.14 113.592 118.14ZM113.535 112.167C112.652 112.167 111.992 112.763 111.872 113.868H115.166C115.058 112.731 114.417 112.167 113.535 112.167ZM118.289 118V111.062H119.87V112.128H119.978C120.212 111.386 120.847 110.948 121.761 110.948C122.002 110.948 122.25 110.979 122.402 111.024V112.439C122.148 112.389 121.888 112.351 121.615 112.351C120.562 112.351 119.87 112.979 119.87 113.969V118H118.289ZM124.51 109.9C123.989 109.9 123.564 109.488 123.564 108.967C123.564 108.453 123.989 108.034 124.51 108.034C125.024 108.034 125.449 108.453 125.449 108.967C125.449 109.488 125.024 109.9 124.51 109.9ZM123.716 118V111.062H125.297V118H123.716ZM130.21 118.14C128.147 118.14 126.896 116.756 126.896 114.547V114.541C126.896 112.357 128.16 110.929 130.134 110.929C132.108 110.929 133.327 112.312 133.327 114.407V114.928H128.477C128.496 116.172 129.169 116.896 130.242 116.896C131.099 116.896 131.587 116.464 131.74 116.146L131.759 116.102H133.263L133.244 116.159C133.022 117.054 132.095 118.14 130.21 118.14ZM130.153 112.167C129.271 112.167 128.61 112.763 128.49 113.868H131.784C131.676 112.731 131.035 112.167 130.153 112.167ZM134.907 118V111.062H136.488V112.122H136.596C136.926 111.379 137.592 110.929 138.595 110.929C140.144 110.929 140.988 111.862 140.988 113.512V118H139.408V113.88C139.408 112.801 138.97 112.255 138.011 112.255C137.072 112.255 136.488 112.916 136.488 113.957V118H134.907ZM145.832 118.14C143.743 118.14 142.512 116.788 142.512 114.521V114.509C142.512 112.268 143.737 110.929 145.825 110.929C147.609 110.929 148.675 111.919 148.853 113.354V113.379H147.361L147.355 113.36C147.209 112.687 146.695 112.205 145.832 112.205C144.752 112.205 144.118 113.055 144.118 114.509V114.521C144.118 115.994 144.759 116.857 145.832 116.857C146.65 116.857 147.152 116.483 147.349 115.753L147.361 115.728L148.853 115.721L148.84 115.772C148.612 117.194 147.59 118.14 145.832 118.14ZM153.328 118.14C151.265 118.14 150.015 116.756 150.015 114.547V114.541C150.015 112.357 151.278 110.929 153.252 110.929C155.226 110.929 156.445 112.312 156.445 114.407V114.928H151.595C151.614 116.172 152.287 116.896 153.36 116.896C154.217 116.896 154.706 116.464 154.858 116.146L154.877 116.102H156.381L156.362 116.159C156.14 117.054 155.213 118.14 153.328 118.14ZM153.271 112.167C152.389 112.167 151.729 112.763 151.608 113.868H154.902C154.794 112.731 154.153 112.167 153.271 112.167Z\",\n    fill: \"#181818\"\n  })), _path2 || (_path2 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M290.13 118V108.84H291.552L296.052 115.15H296.154V108.84H297.728V118H296.312L291.812 111.69H291.71V118H290.13ZM302.8 118.14C300.737 118.14 299.486 116.756 299.486 114.547V114.541C299.486 112.357 300.75 110.929 302.724 110.929C304.698 110.929 305.917 112.312 305.917 114.407V114.928H301.067C301.086 116.172 301.759 116.896 302.832 116.896C303.688 116.896 304.177 116.464 304.33 116.146L304.349 116.102H305.853L305.834 116.159C305.612 117.054 304.685 118.14 302.8 118.14ZM302.743 112.167C301.86 112.167 301.2 112.763 301.08 113.868H304.374C304.266 112.731 303.625 112.167 302.743 112.167ZM308.729 118L306.85 111.062H308.437L309.592 116.273H309.7L311.033 111.062H312.537L313.883 116.273H313.991L315.14 111.062H316.708L314.829 118H313.172L311.82 112.973H311.712L310.366 118H308.729ZM321.716 118V108.84H323.595L326.35 115.886H326.458L329.206 108.84H331.085V118H329.612V111.487H329.498L326.959 118H325.842L323.303 111.487H323.195V118H321.716ZM336.176 118.14C334.094 118.14 332.837 116.781 332.837 114.534V114.521C332.837 112.293 334.113 110.929 336.176 110.929C338.245 110.929 339.515 112.287 339.515 114.521V114.534C339.515 116.781 338.251 118.14 336.176 118.14ZM336.176 116.857C337.274 116.857 337.896 116 337.896 114.541V114.528C337.896 113.068 337.268 112.205 336.176 112.205C335.078 112.205 334.449 113.068 334.449 114.528V114.541C334.449 116 335.078 116.857 336.176 116.857ZM343.603 118.114C341.863 118.114 340.759 116.737 340.759 114.541V114.528C340.759 112.319 341.844 110.948 343.603 110.948C344.555 110.948 345.348 111.417 345.71 112.167H345.818V108.377H347.405V118H345.818V116.915H345.71C345.329 117.67 344.58 118.114 343.603 118.114ZM344.098 116.781C345.177 116.781 345.843 115.931 345.843 114.541V114.528C345.843 113.138 345.17 112.281 344.098 112.281C343.025 112.281 342.365 113.131 342.365 114.528V114.541C342.365 115.937 343.019 116.781 344.098 116.781ZM352.362 118.14C350.299 118.14 349.049 116.756 349.049 114.547V114.541C349.049 112.357 350.312 110.929 352.286 110.929C354.26 110.929 355.479 112.312 355.479 114.407V114.928H350.629C350.648 116.172 351.321 116.896 352.394 116.896C353.251 116.896 353.74 116.464 353.892 116.146L353.911 116.102H355.416L355.396 116.159C355.174 117.054 354.248 118.14 352.362 118.14ZM352.305 112.167C351.423 112.167 350.763 112.763 350.642 113.868H353.937C353.829 112.731 353.188 112.167 352.305 112.167ZM357.123 118V108.377H358.704V118H357.123ZM364.334 118V108.84H368.035C370.002 108.84 371.183 109.938 371.183 111.684V111.697C371.183 112.909 370.542 113.912 369.457 114.306L371.456 118H369.583L367.787 114.553H365.972V118H364.334ZM365.972 113.296H367.863C368.898 113.296 369.501 112.719 369.501 111.735V111.722C369.501 110.764 368.873 110.167 367.832 110.167H365.972V113.296ZM375.76 118.14C373.697 118.14 372.446 116.756 372.446 114.547V114.541C372.446 112.357 373.709 110.929 375.684 110.929C377.658 110.929 378.876 112.312 378.876 114.407V114.928H374.027C374.046 116.172 374.719 116.896 375.792 116.896C376.648 116.896 377.137 116.464 377.29 116.146L377.309 116.102H378.813L378.794 116.159C378.572 117.054 377.645 118.14 375.76 118.14ZM375.703 112.167C374.82 112.167 374.16 112.763 374.04 113.868H377.334C377.226 112.731 376.585 112.167 375.703 112.167ZM383.44 120.438C381.612 120.438 380.514 119.657 380.355 118.489L380.362 118.47H381.942L381.949 118.489C382.057 118.933 382.583 119.244 383.466 119.244C384.545 119.244 385.173 118.743 385.173 117.854V116.527H385.065C384.666 117.27 383.91 117.676 382.952 117.676C381.193 117.676 380.102 116.318 380.102 114.35V114.337C380.102 112.332 381.193 110.948 382.983 110.948C383.942 110.948 384.691 111.417 385.084 112.205H385.167V111.062H386.748V117.829C386.748 119.428 385.465 120.438 383.44 120.438ZM383.44 116.419C384.539 116.419 385.199 115.575 385.199 114.35V114.337C385.199 113.112 384.532 112.262 383.44 112.262C382.342 112.262 381.72 113.112 381.72 114.337V114.35C381.72 115.575 382.342 116.419 383.44 116.419ZM389.496 109.9C388.976 109.9 388.55 109.488 388.55 108.967C388.55 108.453 388.976 108.034 389.496 108.034C390.01 108.034 390.436 108.453 390.436 108.967C390.436 109.488 390.01 109.9 389.496 109.9ZM388.703 118V111.062H390.283V118H388.703ZM394.847 118.14C393.083 118.14 392.016 117.327 391.883 116.153V116.14H393.451L393.457 116.153C393.622 116.635 394.085 116.972 394.873 116.972C395.66 116.972 396.206 116.616 396.206 116.096V116.083C396.206 115.677 395.901 115.41 395.139 115.239L394.016 114.985C392.695 114.693 392.067 114.071 392.067 113.049V113.042C392.067 111.792 393.209 110.929 394.847 110.929C396.536 110.929 397.545 111.767 397.646 112.89V112.903H396.161L396.155 112.884C396.041 112.446 395.583 112.09 394.841 112.09C394.136 112.09 393.628 112.433 393.628 112.954V112.96C393.628 113.366 393.92 113.614 394.669 113.785L395.793 114.033C397.151 114.337 397.792 114.915 397.792 115.924V115.937C397.792 117.257 396.555 118.14 394.847 118.14ZM402.064 118.032C400.471 118.032 399.817 117.499 399.817 116.159V112.287H398.738V111.062H399.817V109.361H401.423V111.062H402.89V112.287H401.423V115.785C401.423 116.477 401.696 116.769 402.356 116.769C402.579 116.769 402.699 116.762 402.89 116.743V117.962C402.661 118.006 402.369 118.032 402.064 118.032ZM404.464 118V111.062H406.044V112.128H406.152C406.387 111.386 407.022 110.948 407.936 110.948C408.177 110.948 408.425 110.979 408.577 111.024V112.439C408.323 112.389 408.063 112.351 407.79 112.351C406.736 112.351 406.044 112.979 406.044 113.969V118H404.464ZM410.716 120.438C410.526 120.438 410.304 120.431 410.113 120.412V119.2C410.24 119.212 410.418 119.219 410.583 119.219C411.23 119.219 411.618 118.952 411.789 118.324L411.872 118.006L409.39 111.062H411.116L412.735 116.483H412.855L414.468 111.062H416.131L413.643 118.146C413.046 119.885 412.252 120.438 410.716 120.438ZM424.878 118.229C422.561 118.229 421.114 116.864 421.114 114.845V108.84H422.751V114.712C422.751 115.975 423.52 116.813 424.884 116.813C426.243 116.813 427.004 115.975 427.004 114.712V108.84H428.642V114.845C428.642 116.864 427.208 118.229 424.878 118.229ZM430.896 118V108.84H432.533V118H430.896Z\",\n    fill: \"#181818\"\n  })), _path3 || (_path3 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36.6182 191L33.4507 181.84H35.2534L37.5195 189.108H37.6274L39.8936 181.84H41.7026L38.5352 191H36.6182ZM45.3652 191.14C43.3022 191.14 42.0518 189.756 42.0518 187.547V187.541C42.0518 185.357 43.3149 183.929 45.2891 183.929C47.2632 183.929 48.4819 185.312 48.4819 187.407V187.928H43.6323C43.6514 189.172 44.3242 189.896 45.397 189.896C46.2539 189.896 46.7427 189.464 46.895 189.146L46.9141 189.102H48.4185L48.3994 189.159C48.1772 190.054 47.2505 191.14 45.3652 191.14ZM45.3081 185.167C44.4258 185.167 43.7656 185.763 43.645 186.868H46.9395C46.8315 185.731 46.1904 185.167 45.3081 185.167ZM50.0625 191V184.062H51.6431V185.128H51.751C51.9858 184.386 52.6206 183.948 53.5347 183.948C53.7759 183.948 54.0234 183.979 54.1758 184.024V185.439C53.9219 185.389 53.6616 185.351 53.3887 185.351C52.335 185.351 51.6431 185.979 51.6431 186.969V191H50.0625ZM61.1709 191V183.453H61.063L58.7842 185.065V183.522L61.1772 181.84H62.8086V191H61.1709Z\",\n    fill: \"#5D7283\"\n  })), _rect2 || (_rect2 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 78,\n    y: 176,\n    width: 58,\n    height: 20,\n    rx: 4,\n    fill: \"#F1CB7F\"\n  })), _path4 || (_path4 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M86.7319 191.229C84.6055 191.229 83.2534 190.181 83.1392 188.601L83.1328 188.512H84.7197L84.7261 188.575C84.8022 189.343 85.6211 189.851 86.7827 189.851C87.8872 189.851 88.6616 189.305 88.6616 188.518V188.512C88.6616 187.852 88.1855 187.452 87.0049 187.198L86.021 186.995C84.1484 186.601 83.3359 185.725 83.3359 184.36V184.354C83.3359 182.716 84.7451 181.612 86.7256 181.612C88.7568 181.612 90.02 182.723 90.1343 184.195L90.1406 184.278H88.5728L88.5601 184.202C88.4458 183.491 87.7666 182.983 86.7192 182.989C85.7036 182.989 84.98 183.465 84.98 184.246V184.252C84.98 184.9 85.4434 185.306 86.5859 185.547L87.5698 185.757C89.4995 186.163 90.3057 186.938 90.3057 188.321V188.328C90.3057 190.111 88.9282 191.229 86.7319 191.229ZM94.7109 191.032C93.1177 191.032 92.4639 190.499 92.4639 189.159V185.287H91.3848V184.062H92.4639V182.361H94.0698V184.062H95.5361V185.287H94.0698V188.785C94.0698 189.477 94.3428 189.769 95.0029 189.769C95.2251 189.769 95.3457 189.762 95.5361 189.743V190.962C95.3076 191.006 95.0156 191.032 94.7109 191.032ZM99.002 191.114C97.6816 191.114 96.7231 190.302 96.7231 189.039V189.026C96.7231 187.788 97.6689 187.064 99.3574 186.963L101.141 186.855V186.258C101.141 185.566 100.69 185.186 99.8398 185.186C99.1162 185.186 98.6465 185.446 98.4878 185.903L98.4814 185.928H96.9897L96.9961 185.871C97.1484 184.703 98.2656 183.929 99.916 183.929C101.7 183.929 102.703 184.792 102.703 186.258V191H101.141V190.048H101.033C100.652 190.721 99.916 191.114 99.002 191.114ZM98.2847 188.962C98.2847 189.553 98.7861 189.902 99.4844 189.902C100.437 189.902 101.141 189.28 101.141 188.455V187.896L99.5796 187.998C98.6973 188.055 98.2847 188.378 98.2847 188.95V188.962ZM107.584 193.438C105.756 193.438 104.658 192.657 104.499 191.489L104.505 191.47H106.086L106.092 191.489C106.2 191.933 106.727 192.244 107.609 192.244C108.688 192.244 109.317 191.743 109.317 190.854V189.527H109.209C108.809 190.27 108.054 190.676 107.095 190.676C105.337 190.676 104.245 189.318 104.245 187.35V187.337C104.245 185.332 105.337 183.948 107.127 183.948C108.085 183.948 108.834 184.417 109.228 185.205H109.311V184.062H110.891V190.829C110.891 192.428 109.609 193.438 107.584 193.438ZM107.584 189.419C108.682 189.419 109.342 188.575 109.342 187.35V187.337C109.342 186.112 108.676 185.262 107.584 185.262C106.486 185.262 105.864 186.112 105.864 187.337V187.35C105.864 188.575 106.486 189.419 107.584 189.419ZM113.64 182.9C113.119 182.9 112.694 182.488 112.694 181.967C112.694 181.453 113.119 181.034 113.64 181.034C114.154 181.034 114.579 181.453 114.579 181.967C114.579 182.488 114.154 182.9 113.64 182.9ZM112.846 191V184.062H114.427V191H112.846ZM116.369 191V184.062H117.95V185.122H118.058C118.388 184.379 119.054 183.929 120.057 183.929C121.606 183.929 122.45 184.862 122.45 186.512V191H120.87V186.88C120.87 185.801 120.432 185.255 119.473 185.255C118.534 185.255 117.95 185.916 117.95 186.957V191H116.369ZM127.3 193.438C125.472 193.438 124.374 192.657 124.215 191.489L124.221 191.47H125.802L125.808 191.489C125.916 191.933 126.443 192.244 127.325 192.244C128.404 192.244 129.033 191.743 129.033 190.854V189.527H128.925C128.525 190.27 127.77 190.676 126.811 190.676C125.053 190.676 123.961 189.318 123.961 187.35V187.337C123.961 185.332 125.053 183.948 126.843 183.948C127.801 183.948 128.55 184.417 128.944 185.205H129.026V184.062H130.607V190.829C130.607 192.428 129.325 193.438 127.3 193.438ZM127.3 189.419C128.398 189.419 129.058 188.575 129.058 187.35V187.337C129.058 186.112 128.392 185.262 127.3 185.262C126.202 185.262 125.58 186.112 125.58 187.337V187.35C125.58 188.575 126.202 189.419 127.3 189.419Z\",\n    fill: \"#1F272D\"\n  })), _path5 || (_path5 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M35.6182 156L32.4507 146.84H34.2534L36.5195 154.108H36.6274L38.8936 146.84H40.7026L37.5352 156H35.6182ZM44.3652 156.14C42.3022 156.14 41.0518 154.756 41.0518 152.547V152.541C41.0518 150.357 42.3149 148.929 44.2891 148.929C46.2632 148.929 47.4819 150.312 47.4819 152.407V152.928H42.6323C42.6514 154.172 43.3242 154.896 44.397 154.896C45.2539 154.896 45.7427 154.464 45.895 154.146L45.9141 154.102H47.4185L47.3994 154.159C47.1772 155.054 46.2505 156.14 44.3652 156.14ZM44.3081 150.167C43.4258 150.167 42.7656 150.763 42.645 151.868H45.9395C45.8315 150.731 45.1904 150.167 44.3081 150.167ZM49.0625 156V149.062H50.6431V150.128H50.751C50.9858 149.386 51.6206 148.948 52.5347 148.948C52.7759 148.948 53.0234 148.979 53.1758 149.024V150.439C52.9219 150.389 52.6616 150.351 52.3887 150.351C51.335 150.351 50.6431 150.979 50.6431 151.969V156H49.0625ZM57.9302 156V154.896L60.939 151.823C62.1831 150.566 62.5322 150.103 62.5322 149.398V149.379C62.5322 148.535 61.9546 147.926 61.0024 147.926C60.0312 147.926 59.3774 148.567 59.3774 149.513L59.3711 149.538L57.8477 149.532L57.8413 149.513C57.8413 147.805 59.1743 146.612 61.085 146.612C62.8687 146.612 64.1509 147.697 64.1509 149.24V149.259C64.1509 150.3 63.6494 151.125 61.98 152.731L60.1455 154.508V154.642H64.2905V156H57.9302Z\",\n    fill: \"#5D7283\"\n  })), _rect3 || (_rect3 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 78,\n    y: 142,\n    width: 79,\n    height: 20,\n    rx: 4,\n    fill: \"#3CAA60\"\n  })), _path6 || (_path6 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M83.5781 157V147.84H87.2344C89.0942 147.84 90.3511 149.065 90.3511 150.9V150.913C90.3511 152.741 89.0942 153.972 87.2344 153.972H85.2158V157H83.5781ZM86.8345 149.18H85.2158V152.652H86.8345C88.0088 152.652 88.6943 152.017 88.6943 150.919V150.906C88.6943 149.808 88.0088 149.18 86.8345 149.18ZM91.9951 157V150.062H93.5757V151.128H93.6836C93.9185 150.386 94.5532 149.948 95.4673 149.948C95.7085 149.948 95.9561 149.979 96.1084 150.024V151.439C95.8545 151.389 95.5942 151.351 95.3213 151.351C94.2676 151.351 93.5757 151.979 93.5757 152.969V157H91.9951ZM100.222 157.14C98.1396 157.14 96.8828 155.781 96.8828 153.534V153.521C96.8828 151.293 98.1587 149.929 100.222 149.929C102.291 149.929 103.561 151.287 103.561 153.521V153.534C103.561 155.781 102.297 157.14 100.222 157.14ZM100.222 155.857C101.32 155.857 101.942 155 101.942 153.541V153.528C101.942 152.068 101.313 151.205 100.222 151.205C99.1235 151.205 98.4951 152.068 98.4951 153.528V153.541C98.4951 155 99.1235 155.857 100.222 155.857ZM107.648 157.114C105.909 157.114 104.805 155.737 104.805 153.541V153.528C104.805 151.319 105.89 149.948 107.648 149.948C108.601 149.948 109.394 150.417 109.756 151.167H109.864V147.377H111.451V157H109.864V155.915H109.756C109.375 156.67 108.626 157.114 107.648 157.114ZM108.144 155.781C109.223 155.781 109.889 154.931 109.889 153.541V153.528C109.889 152.138 109.216 151.281 108.144 151.281C107.071 151.281 106.411 152.131 106.411 153.528V153.541C106.411 154.937 107.064 155.781 108.144 155.781ZM115.735 157.14C114.174 157.14 113.374 156.2 113.374 154.55V150.062H114.955V154.188C114.955 155.261 115.354 155.807 116.307 155.807C117.329 155.807 117.875 155.146 117.875 154.105V150.062H119.455V157H117.875V155.946H117.767C117.443 156.689 116.738 157.14 115.735 157.14ZM124.362 157.14C122.273 157.14 121.042 155.788 121.042 153.521V153.509C121.042 151.268 122.267 149.929 124.355 149.929C126.139 149.929 127.206 150.919 127.383 152.354V152.379H125.892L125.885 152.36C125.739 151.687 125.225 151.205 124.362 151.205C123.283 151.205 122.648 152.055 122.648 153.509V153.521C122.648 154.994 123.289 155.857 124.362 155.857C125.181 155.857 125.682 155.483 125.879 154.753L125.892 154.728L127.383 154.721L127.371 154.772C127.142 156.194 126.12 157.14 124.362 157.14ZM131.706 157.032C130.113 157.032 129.459 156.499 129.459 155.159V151.287H128.38V150.062H129.459V148.361H131.065V150.062H132.531V151.287H131.065V154.785C131.065 155.477 131.338 155.769 131.998 155.769C132.22 155.769 132.341 155.762 132.531 155.743V156.962C132.303 157.006 132.011 157.032 131.706 157.032ZM134.918 148.9C134.397 148.9 133.972 148.488 133.972 147.967C133.972 147.453 134.397 147.034 134.918 147.034C135.432 147.034 135.857 147.453 135.857 147.967C135.857 148.488 135.432 148.9 134.918 148.9ZM134.125 157V150.062H135.705V157H134.125ZM140.644 157.14C138.562 157.14 137.305 155.781 137.305 153.534V153.521C137.305 151.293 138.581 149.929 140.644 149.929C142.713 149.929 143.982 151.287 143.982 153.521V153.534C143.982 155.781 142.719 157.14 140.644 157.14ZM140.644 155.857C141.742 155.857 142.364 155 142.364 153.541V153.528C142.364 152.068 141.735 151.205 140.644 151.205C139.545 151.205 138.917 152.068 138.917 153.528V153.541C138.917 155 139.545 155.857 140.644 155.857ZM145.569 157V150.062H147.15V151.122H147.258C147.588 150.379 148.254 149.929 149.257 149.929C150.806 149.929 151.65 150.862 151.65 152.512V157H150.07V152.88C150.07 151.801 149.632 151.255 148.673 151.255C147.734 151.255 147.15 151.916 147.15 152.957V157H145.569Z\",\n    fill: \"white\"\n  })), _path7 || (_path7 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M169 141H249.666C250.628 141 251.558 141.347 252.286 141.977L276.513 162.977C278.353 164.572 278.353 167.428 276.513 169.023L252.286 190.023C251.558 190.653 250.628 191 249.666 191H169V141Z\",\n    fill: \"url(#paint0_linear_6213_11592)\"\n  })), _path8 || (_path8 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M295.618 188L292.451 178.84H294.253L296.52 186.108H296.627L298.894 178.84H300.703L297.535 188H295.618ZM304.365 188.14C302.302 188.14 301.052 186.756 301.052 184.547V184.541C301.052 182.357 302.315 180.929 304.289 180.929C306.263 180.929 307.482 182.312 307.482 184.407V184.928H302.632C302.651 186.172 303.324 186.896 304.397 186.896C305.254 186.896 305.743 186.464 305.895 186.146L305.914 186.102H307.418L307.399 186.159C307.177 187.054 306.25 188.14 304.365 188.14ZM304.308 182.167C303.426 182.167 302.766 182.763 302.645 183.868H305.939C305.832 182.731 305.19 182.167 304.308 182.167ZM309.062 188V181.062H310.643V182.128H310.751C310.986 181.386 311.621 180.948 312.535 180.948C312.776 180.948 313.023 180.979 313.176 181.024V182.439C312.922 182.389 312.662 182.351 312.389 182.351C311.335 182.351 310.643 182.979 310.643 183.969V188H309.062ZM320.171 188V180.453H320.063L317.784 182.065V180.522L320.177 178.84H321.809V188H320.171Z\",\n    fill: \"#5D7283\"\n  })), _rect4 || (_rect4 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 335.5,\n    y: 175,\n    width: 90,\n    height: 20,\n    rx: 4,\n    fill: \"#5D7283\"\n  })), _path9 || (_path9 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M345.695 190.312C342.654 190.312 340.686 188.391 340.686 185.432V185.42C340.686 182.531 342.684 180.539 345.596 180.539C348.391 180.539 350.312 182.309 350.312 184.875V184.887C350.312 186.762 349.439 187.945 348.045 187.945C347.342 187.945 346.814 187.588 346.697 187.037L346.691 187.002H346.586C346.34 187.594 345.842 187.916 345.162 187.916C343.943 187.916 343.129 186.92 343.129 185.438V185.426C343.129 184.008 343.943 183.029 345.127 183.029C345.748 183.029 346.264 183.34 346.492 183.855H346.598V183.152H347.67V186.398C347.67 186.809 347.887 187.055 348.268 187.055C348.918 187.055 349.328 186.223 349.328 184.934V184.922C349.328 182.812 347.811 181.4 345.543 181.4C343.27 181.4 341.67 183.076 341.67 185.455V185.467C341.67 187.893 343.258 189.439 345.754 189.439C346.445 189.439 347.143 189.346 347.482 189.205V190.072C346.996 190.225 346.357 190.312 345.695 190.312ZM345.391 186.926C346.082 186.926 346.521 186.363 346.521 185.473V185.461C346.521 184.564 346.088 184.002 345.396 184.002C344.717 184.002 344.312 184.547 344.312 185.461V185.473C344.312 186.375 344.723 186.926 345.391 186.926ZM358.762 189.211C356.365 189.211 354.877 187.512 354.877 184.781V184.77C354.877 182.027 356.359 180.334 358.756 180.334C360.678 180.334 362.137 181.553 362.336 183.281V183.328H360.854L360.848 183.305C360.631 182.303 359.822 181.641 358.756 181.641C357.32 181.641 356.424 182.842 356.424 184.764V184.775C356.424 186.703 357.32 187.904 358.762 187.904C359.834 187.904 360.631 187.307 360.848 186.375L360.854 186.346H362.336V186.387C362.113 188.08 360.713 189.211 358.762 189.211ZM363.93 189V180.117H365.389V183.574H365.488C365.793 182.889 366.408 182.473 367.334 182.473C368.764 182.473 369.543 183.334 369.543 184.857V189H368.084V185.197C368.084 184.201 367.68 183.697 366.795 183.697C365.928 183.697 365.389 184.307 365.389 185.268V189H363.93ZM373.012 189.105C371.793 189.105 370.908 188.355 370.908 187.189V187.178C370.908 186.035 371.781 185.367 373.34 185.273L374.986 185.174V184.623C374.986 183.984 374.57 183.633 373.785 183.633C373.117 183.633 372.684 183.873 372.537 184.295L372.531 184.318H371.154L371.16 184.266C371.301 183.188 372.332 182.473 373.855 182.473C375.502 182.473 376.428 183.27 376.428 184.623V189H374.986V188.121H374.887C374.535 188.742 373.855 189.105 373.012 189.105ZM372.35 187.119C372.35 187.664 372.812 187.986 373.457 187.986C374.336 187.986 374.986 187.412 374.986 186.65V186.135L373.545 186.229C372.73 186.281 372.35 186.58 372.35 187.107V187.119ZM378.238 189V180.117H379.697V189H378.238ZM381.602 189V180.117H383.061V189H381.602ZM387.648 189.129C385.744 189.129 384.59 187.852 384.59 185.812V185.807C384.59 183.791 385.756 182.473 387.578 182.473C389.4 182.473 390.525 183.75 390.525 185.684V186.164H386.049C386.066 187.312 386.688 187.98 387.678 187.98C388.469 187.98 388.92 187.582 389.061 187.289L389.078 187.248H390.467L390.449 187.301C390.244 188.127 389.389 189.129 387.648 189.129ZM387.596 183.615C386.781 183.615 386.172 184.166 386.061 185.186H389.102C389.002 184.137 388.41 183.615 387.596 183.615ZM391.984 189V182.596H393.443V183.574H393.543C393.848 182.889 394.463 182.473 395.389 182.473C396.818 182.473 397.598 183.334 397.598 184.857V189H396.139V185.197C396.139 184.201 395.734 183.697 394.85 183.697C393.982 183.697 393.443 184.307 393.443 185.268V189H391.984ZM402.074 191.25C400.387 191.25 399.373 190.529 399.227 189.451L399.232 189.434H400.691L400.697 189.451C400.797 189.861 401.283 190.148 402.098 190.148C403.094 190.148 403.674 189.686 403.674 188.865V187.641H403.574C403.205 188.326 402.508 188.701 401.623 188.701C400 188.701 398.992 187.447 398.992 185.631V185.619C398.992 183.768 400 182.49 401.652 182.49C402.537 182.49 403.229 182.924 403.592 183.65H403.668V182.596H405.127V188.842C405.127 190.318 403.943 191.25 402.074 191.25ZM402.074 187.541C403.088 187.541 403.697 186.762 403.697 185.631V185.619C403.697 184.488 403.082 183.703 402.074 183.703C401.061 183.703 400.486 184.488 400.486 185.619V185.631C400.486 186.762 401.061 187.541 402.074 187.541ZM409.656 189.129C407.752 189.129 406.598 187.852 406.598 185.812V185.807C406.598 183.791 407.764 182.473 409.586 182.473C411.408 182.473 412.533 183.75 412.533 185.684V186.164H408.057C408.074 187.312 408.695 187.98 409.686 187.98C410.477 187.98 410.928 187.582 411.068 187.289L411.086 187.248H412.475L412.457 187.301C412.252 188.127 411.396 189.129 409.656 189.129ZM409.604 183.615C408.789 183.615 408.18 184.166 408.068 185.186H411.109C411.01 184.137 410.418 183.615 409.604 183.615ZM413.992 189V182.596H415.451V183.58H415.551C415.768 182.895 416.354 182.49 417.197 182.49C417.42 182.49 417.648 182.52 417.789 182.561V183.867C417.555 183.82 417.314 183.785 417.062 183.785C416.09 183.785 415.451 184.365 415.451 185.279V189H413.992Z\",\n    fill: \"white\"\n  })), _rect5 || (_rect5 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 431,\n    y: 175,\n    width: 64.6965,\n    height: 20,\n    rx: 4,\n    fill: \"#5D7283\",\n    fillOpacity: 0.08\n  })), _path10 || (_path10 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M436.281 189V180.545H437.793V189H436.281ZM439.727 189V182.596H441.186V183.574H441.285C441.59 182.889 442.205 182.473 443.131 182.473C444.561 182.473 445.34 183.334 445.34 184.857V189H443.881V185.197C443.881 184.201 443.477 183.697 442.592 183.697C441.725 183.697 441.186 184.307 441.186 185.268V189H439.727ZM447.092 186.252V184.928H451.006V186.252H447.092ZM452.98 189V180.545H456.396C458.213 180.545 459.303 181.559 459.303 183.17V183.182C459.303 184.301 458.711 185.227 457.709 185.59L459.555 189H457.826L456.168 185.818H454.492V189H452.98ZM454.492 184.658H456.238C457.193 184.658 457.75 184.125 457.75 183.217V183.205C457.75 182.32 457.17 181.77 456.209 181.77H454.492V184.658ZM463.527 189.129C461.623 189.129 460.469 187.852 460.469 185.812V185.807C460.469 183.791 461.635 182.473 463.457 182.473C465.279 182.473 466.404 183.75 466.404 185.684V186.164H461.928C461.945 187.312 462.566 187.98 463.557 187.98C464.348 187.98 464.799 187.582 464.939 187.289L464.957 187.248H466.346L466.328 187.301C466.123 188.127 465.268 189.129 463.527 189.129ZM463.475 183.615C462.66 183.615 462.051 184.166 461.939 185.186H464.98C464.881 184.137 464.289 183.615 463.475 183.615ZM469.434 189L467.154 182.596H468.713L470.195 187.576H470.295L471.766 182.596H473.301L471.033 189H469.434ZM475.281 181.523C474.801 181.523 474.408 181.143 474.408 180.662C474.408 180.188 474.801 179.801 475.281 179.801C475.756 179.801 476.148 180.188 476.148 180.662C476.148 181.143 475.756 181.523 475.281 181.523ZM474.549 189V182.596H476.008V189H474.549ZM480.543 189.129C478.639 189.129 477.484 187.852 477.484 185.812V185.807C477.484 183.791 478.65 182.473 480.473 182.473C482.295 182.473 483.42 183.75 483.42 185.684V186.164H478.943C478.961 187.312 479.582 187.98 480.572 187.98C481.363 187.98 481.814 187.582 481.955 187.289L481.973 187.248H483.361L483.344 187.301C483.139 188.127 482.283 189.129 480.543 189.129ZM480.49 183.615C479.676 183.615 479.066 184.166 478.955 185.186H481.996C481.896 184.137 481.305 183.615 480.49 183.615ZM486.016 189L484.281 182.596H485.746L486.812 187.406H486.912L488.143 182.596H489.531L490.773 187.406H490.873L491.934 182.596H493.381L491.646 189H490.117L488.869 184.359H488.77L487.527 189H486.016Z\",\n    fill: \"#1F272D\"\n  })), _path11 || (_path11 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M294.618 158L291.451 148.84H293.253L295.52 156.108H295.627L297.894 148.84H299.703L296.535 158H294.618ZM303.365 158.14C301.302 158.14 300.052 156.756 300.052 154.547V154.541C300.052 152.357 301.315 150.929 303.289 150.929C305.263 150.929 306.482 152.312 306.482 154.407V154.928H301.632C301.651 156.172 302.324 156.896 303.397 156.896C304.254 156.896 304.743 156.464 304.895 156.146L304.914 156.102H306.418L306.399 156.159C306.177 157.054 305.25 158.14 303.365 158.14ZM303.308 152.167C302.426 152.167 301.766 152.763 301.645 153.868H304.939C304.832 152.731 304.19 152.167 303.308 152.167ZM308.062 158V151.062H309.643V152.128H309.751C309.986 151.386 310.621 150.948 311.535 150.948C311.776 150.948 312.023 150.979 312.176 151.024V152.439C311.922 152.389 311.662 152.351 311.389 152.351C310.335 152.351 309.643 152.979 309.643 153.969V158H308.062ZM316.93 158V156.896L319.939 153.823C321.183 152.566 321.532 152.103 321.532 151.398V151.379C321.532 150.535 320.955 149.926 320.002 149.926C319.031 149.926 318.377 150.567 318.377 151.513L318.371 151.538L316.848 151.532L316.841 151.513C316.841 149.805 318.174 148.612 320.085 148.612C321.869 148.612 323.151 149.697 323.151 151.24V151.259C323.151 152.3 322.649 153.125 320.98 154.731L319.146 156.508V156.642H323.291V158H316.93Z\",\n    fill: \"#5D7283\"\n  })), _rect6 || (_rect6 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 336,\n    y: 141,\n    width: 85,\n    height: 20,\n    rx: 4,\n    fill: \"#5D7283\"\n  })), _path12 || (_path12 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M345.875 156.312C342.834 156.312 340.865 154.391 340.865 151.432V151.42C340.865 148.531 342.863 146.539 345.775 146.539C348.57 146.539 350.492 148.309 350.492 150.875V150.887C350.492 152.762 349.619 153.945 348.225 153.945C347.521 153.945 346.994 153.588 346.877 153.037L346.871 153.002H346.766C346.52 153.594 346.021 153.916 345.342 153.916C344.123 153.916 343.309 152.92 343.309 151.438V151.426C343.309 150.008 344.123 149.029 345.307 149.029C345.928 149.029 346.443 149.34 346.672 149.855H346.777V149.152H347.85V152.398C347.85 152.809 348.066 153.055 348.447 153.055C349.098 153.055 349.508 152.223 349.508 150.934V150.922C349.508 148.812 347.99 147.4 345.723 147.4C343.449 147.4 341.85 149.076 341.85 151.455V151.467C341.85 153.893 343.438 155.439 345.934 155.439C346.625 155.439 347.322 155.346 347.662 155.205V156.072C347.176 156.225 346.537 156.312 345.875 156.312ZM345.57 152.926C346.262 152.926 346.701 152.363 346.701 151.473V151.461C346.701 150.564 346.268 150.002 345.576 150.002C344.896 150.002 344.492 150.547 344.492 151.461V151.473C344.492 152.375 344.902 152.926 345.57 152.926ZM358.941 155.211C356.545 155.211 355.057 153.512 355.057 150.781V150.77C355.057 148.027 356.539 146.334 358.936 146.334C360.857 146.334 362.316 147.553 362.516 149.281V149.328H361.033L361.027 149.305C360.811 148.303 360.002 147.641 358.936 147.641C357.5 147.641 356.604 148.842 356.604 150.764V150.775C356.604 152.703 357.5 153.904 358.941 153.904C360.014 153.904 360.811 153.307 361.027 152.375L361.033 152.346H362.516V152.387C362.293 154.08 360.893 155.211 358.941 155.211ZM364.109 155V146.117H365.568V149.574H365.668C365.973 148.889 366.588 148.473 367.514 148.473C368.943 148.473 369.723 149.334 369.723 150.857V155H368.264V151.197C368.264 150.201 367.859 149.697 366.975 149.697C366.107 149.697 365.568 150.307 365.568 151.268V155H364.109ZM373.191 155.105C371.973 155.105 371.088 154.355 371.088 153.189V153.178C371.088 152.035 371.961 151.367 373.52 151.273L375.166 151.174V150.623C375.166 149.984 374.75 149.633 373.965 149.633C373.297 149.633 372.863 149.873 372.717 150.295L372.711 150.318H371.334L371.34 150.266C371.48 149.188 372.512 148.473 374.035 148.473C375.682 148.473 376.607 149.27 376.607 150.623V155H375.166V154.121H375.066C374.715 154.742 374.035 155.105 373.191 155.105ZM372.529 153.119C372.529 153.664 372.992 153.986 373.637 153.986C374.516 153.986 375.166 153.412 375.166 152.65V152.135L373.725 152.229C372.91 152.281 372.529 152.58 372.529 153.107V153.119ZM378.359 155V148.596H379.818V149.592H379.918C380.193 148.877 380.838 148.473 381.676 148.473C382.543 148.473 383.176 148.918 383.451 149.627H383.551C383.861 148.93 384.6 148.473 385.49 148.473C386.779 148.473 387.553 149.264 387.553 150.594V155H386.094V150.951C386.094 150.119 385.713 149.697 384.934 149.697C384.172 149.697 383.68 150.266 383.68 150.998V155H382.221V150.846C382.221 150.137 381.781 149.697 381.066 149.697C380.346 149.697 379.818 150.307 379.818 151.104V155H378.359ZM389.281 157.127V148.596H390.74V149.598H390.84C391.191 148.9 391.889 148.49 392.785 148.49C394.391 148.49 395.416 149.768 395.416 151.795V151.807C395.416 153.846 394.408 155.105 392.785 155.105C391.906 155.105 391.174 154.672 390.84 153.98H390.74V157.127H389.281ZM392.328 153.875C393.324 153.875 393.928 153.09 393.928 151.807V151.795C393.928 150.506 393.324 149.721 392.328 149.721C391.338 149.721 390.723 150.506 390.723 151.789V151.801C390.723 153.084 391.338 153.875 392.328 153.875ZM397.637 147.523C397.156 147.523 396.764 147.143 396.764 146.662C396.764 146.188 397.156 145.801 397.637 145.801C398.111 145.801 398.504 146.188 398.504 146.662C398.504 147.143 398.111 147.523 397.637 147.523ZM396.904 155V148.596H398.363V155H396.904ZM402.922 155.129C401 155.129 399.84 153.875 399.84 151.801V151.789C399.84 149.732 401.018 148.473 402.922 148.473C404.832 148.473 406.004 149.727 406.004 151.789V151.801C406.004 153.875 404.838 155.129 402.922 155.129ZM402.922 153.945C403.936 153.945 404.51 153.154 404.51 151.807V151.795C404.51 150.447 403.93 149.65 402.922 149.65C401.908 149.65 401.328 150.447 401.328 151.795V151.807C401.328 153.154 401.908 153.945 402.922 153.945ZM407.469 155V148.596H408.928V149.574H409.027C409.332 148.889 409.947 148.473 410.873 148.473C412.303 148.473 413.082 149.334 413.082 150.857V155H411.623V151.197C411.623 150.201 411.219 149.697 410.334 149.697C409.467 149.697 408.928 150.307 408.928 151.268V155H407.469Z\",\n    fill: \"white\"\n  })), _rect7 || (_rect7 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 427,\n    y: 141,\n    width: 63,\n    height: 20,\n    rx: 4,\n    fill: \"#5D7283\"\n  })), _path13 || (_path13 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M437.154 156.312C434.113 156.312 432.145 154.391 432.145 151.432V151.42C432.145 148.531 434.143 146.539 437.055 146.539C439.85 146.539 441.771 148.309 441.771 150.875V150.887C441.771 152.762 440.898 153.945 439.504 153.945C438.801 153.945 438.273 153.588 438.156 153.037L438.15 153.002H438.045C437.799 153.594 437.301 153.916 436.621 153.916C435.402 153.916 434.588 152.92 434.588 151.438V151.426C434.588 150.008 435.402 149.029 436.586 149.029C437.207 149.029 437.723 149.34 437.951 149.855H438.057V149.152H439.129V152.398C439.129 152.809 439.346 153.055 439.727 153.055C440.377 153.055 440.787 152.223 440.787 150.934V150.922C440.787 148.812 439.27 147.4 437.002 147.4C434.729 147.4 433.129 149.076 433.129 151.455V151.467C433.129 153.893 434.717 155.439 437.213 155.439C437.904 155.439 438.602 155.346 438.941 155.205V156.072C438.455 156.225 437.816 156.312 437.154 156.312ZM436.85 152.926C437.541 152.926 437.98 152.363 437.98 151.473V151.461C437.98 150.564 437.547 150.002 436.855 150.002C436.176 150.002 435.771 150.547 435.771 151.461V151.473C435.771 152.375 436.182 152.926 436.85 152.926ZM446.693 155V146.545H448.205V153.729H452.084V155H446.693ZM455.307 155.105C454.088 155.105 453.203 154.355 453.203 153.189V153.178C453.203 152.035 454.076 151.367 455.635 151.273L457.281 151.174V150.623C457.281 149.984 456.865 149.633 456.08 149.633C455.412 149.633 454.979 149.873 454.832 150.295L454.826 150.318H453.449L453.455 150.266C453.596 149.188 454.627 148.473 456.15 148.473C457.797 148.473 458.723 149.27 458.723 150.623V155H457.281V154.121H457.182C456.83 154.742 456.15 155.105 455.307 155.105ZM454.645 153.119C454.645 153.664 455.107 153.986 455.752 153.986C456.631 153.986 457.281 153.412 457.281 152.65V152.135L455.84 152.229C455.025 152.281 454.645 152.58 454.645 153.107V153.119ZM462.994 155.029C461.523 155.029 460.92 154.537 460.92 153.301V149.727H459.924V148.596H460.92V147.025H462.402V148.596H463.756V149.727H462.402V152.955C462.402 153.594 462.654 153.863 463.264 153.863C463.469 153.863 463.58 153.857 463.756 153.84V154.965C463.545 155.006 463.275 155.029 462.994 155.029ZM467.893 155.129C465.988 155.129 464.834 153.852 464.834 151.812V151.807C464.834 149.791 466 148.473 467.822 148.473C469.645 148.473 470.77 149.75 470.77 151.684V152.164H466.293C466.311 153.312 466.932 153.98 467.922 153.98C468.713 153.98 469.164 153.582 469.305 153.289L469.322 153.248H470.711L470.693 153.301C470.488 154.127 469.633 155.129 467.893 155.129ZM467.84 149.615C467.025 149.615 466.416 150.166 466.305 151.186H469.346C469.246 150.137 468.654 149.615 467.84 149.615ZM474.648 155.129C473.02 155.129 472.035 154.379 471.912 153.295V153.283H473.359L473.365 153.295C473.518 153.74 473.945 154.051 474.672 154.051C475.398 154.051 475.902 153.723 475.902 153.242V153.23C475.902 152.855 475.621 152.609 474.918 152.451L473.881 152.217C472.662 151.947 472.082 151.373 472.082 150.43V150.424C472.082 149.27 473.137 148.473 474.648 148.473C476.207 148.473 477.139 149.246 477.232 150.283V150.295H475.861L475.855 150.277C475.75 149.873 475.328 149.545 474.643 149.545C473.992 149.545 473.523 149.861 473.523 150.342V150.348C473.523 150.723 473.793 150.951 474.484 151.109L475.521 151.338C476.775 151.619 477.367 152.152 477.367 153.084V153.096C477.367 154.314 476.225 155.129 474.648 155.129ZM481.311 155.029C479.84 155.029 479.236 154.537 479.236 153.301V149.727H478.24V148.596H479.236V147.025H480.719V148.596H482.072V149.727H480.719V152.955C480.719 153.594 480.971 153.863 481.58 153.863C481.785 153.863 481.896 153.857 482.072 153.84V154.965C481.861 155.006 481.592 155.029 481.311 155.029Z\",\n    fill: \"white\"\n  })), _rect8 || (_rect8 = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 496,\n    y: 141,\n    width: 65,\n    height: 20,\n    rx: 4,\n    fill: \"#5D7283\",\n    fillOpacity: 0.08\n  })), _path14 || (_path14 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M500.457 155L503.457 146.545H505.162L508.162 155H506.58L505.871 152.814H502.742L502.039 155H500.457ZM504.26 148.115L503.123 151.648H505.496L504.359 148.115H504.26ZM509.463 157.127V148.596H510.922V149.598H511.021C511.373 148.9 512.07 148.49 512.967 148.49C514.572 148.49 515.598 149.768 515.598 151.795V151.807C515.598 153.846 514.59 155.105 512.967 155.105C512.088 155.105 511.355 154.672 511.021 153.98H510.922V157.127H509.463ZM512.51 153.875C513.506 153.875 514.109 153.09 514.109 151.807V151.795C514.109 150.506 513.506 149.721 512.51 149.721C511.52 149.721 510.904 150.506 510.904 151.789V151.801C510.904 153.084 511.52 153.875 512.51 153.875ZM517.068 157.127V148.596H518.527V149.598H518.627C518.979 148.9 519.676 148.49 520.572 148.49C522.178 148.49 523.203 149.768 523.203 151.795V151.807C523.203 153.846 522.195 155.105 520.572 155.105C519.693 155.105 518.961 154.672 518.627 153.98H518.527V157.127H517.068ZM520.115 153.875C521.111 153.875 521.715 153.09 521.715 151.807V151.795C521.715 150.506 521.111 149.721 520.115 149.721C519.125 149.721 518.51 150.506 518.51 151.789V151.801C518.51 153.084 519.125 153.875 520.115 153.875ZM524.674 155V148.596H526.133V149.58H526.232C526.449 148.895 527.035 148.49 527.879 148.49C528.102 148.49 528.33 148.52 528.471 148.561V149.867C528.236 149.82 527.996 149.785 527.744 149.785C526.771 149.785 526.133 150.365 526.133 151.279V155H524.674ZM532.268 155.129C530.346 155.129 529.186 153.875 529.186 151.801V151.789C529.186 149.732 530.363 148.473 532.268 148.473C534.178 148.473 535.35 149.727 535.35 151.789V151.801C535.35 153.875 534.184 155.129 532.268 155.129ZM532.268 153.945C533.281 153.945 533.855 153.154 533.855 151.807V151.795C533.855 150.447 533.275 149.65 532.268 149.65C531.254 149.65 530.674 150.447 530.674 151.795V151.807C530.674 153.154 531.254 153.945 532.268 153.945ZM538.279 155L536 148.596H537.559L539.041 153.576H539.141L540.611 148.596H542.146L539.879 155H538.279ZM545.861 155.129C543.957 155.129 542.803 153.852 542.803 151.812V151.807C542.803 149.791 543.969 148.473 545.791 148.473C547.613 148.473 548.738 149.75 548.738 151.684V152.164H544.262C544.279 153.312 544.9 153.98 545.891 153.98C546.682 153.98 547.133 153.582 547.273 153.289L547.291 153.248H548.68L548.662 153.301C548.457 154.127 547.602 155.129 545.861 155.129ZM545.809 149.615C544.994 149.615 544.385 150.166 544.273 151.186H547.314C547.215 150.137 546.623 149.615 545.809 149.615ZM552.506 155.105C550.9 155.105 549.881 153.834 549.881 151.807V151.795C549.881 149.756 550.883 148.49 552.506 148.49C553.385 148.49 554.117 148.924 554.451 149.615H554.551V146.117H556.016V155H554.551V153.998H554.451C554.1 154.695 553.408 155.105 552.506 155.105ZM552.963 153.875C553.959 153.875 554.574 153.09 554.574 151.807V151.795C554.574 150.512 553.953 149.721 552.963 149.721C551.973 149.721 551.363 150.506 551.363 151.795V151.807C551.363 153.096 551.967 153.875 552.963 153.875Z\",\n    fill: \"#1F272D\"\n  })), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"linearGradient\", {\n    id: \"paint0_linear_6213_11592\",\n    x1: 288.605,\n    y1: 141,\n    x2: 169,\n    y2: 141,\n    gradientUnits: \"userSpaceOnUse\"\n  }, /*#__PURE__*/React.createElement(\"stop\", {\n    stopColor: \"#8DDDA8\"\n  }), /*#__PURE__*/React.createElement(\"stop\", {\n    offset: 1,\n    stopColor: \"#8DDDA8\",\n    stopOpacity: 0\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgPromoModalContent);\nexport default __webpack_public_path__ + \"static/media/promo-modal-content.e3b2c6c568ac192b9bec54b838b54850.svg\";\nexport { ForwardRef as ReactComponent };", "import { Button, Modal, Typography } from '@databricks/design-system';\nimport { ReactComponent as PromoContentSvg } from '../../common/static/promo-modal-content.svg';\nimport { FormattedMessage } from 'react-intl';\nimport { modelStagesMigrationGuideLink } from '../../common/constants';\n\nexport const ModelsNextUIPromoModal = ({\n  visible,\n  onClose,\n  onTryItNow,\n}: {\n  visible: boolean;\n  onClose: () => void;\n  onTryItNow: () => void;\n}) => (\n  <Modal\n    componentId=\"codegen_mlflow_app_src_model-registry_components_modelsnextuipromomodal.tsx_15\"\n    visible={visible}\n    title={\n      <FormattedMessage\n        defaultMessage=\"Flexible, governed deployments with the new Model Registry UI\"\n        description=\"Model registry > OSS Promo modal for model version aliases > modal title\"\n      />\n    }\n    onCancel={onClose}\n    footer={\n      <>\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_modelsnextuipromomodal.tsx_26\"\n          href={modelStagesMigrationGuideLink}\n          rel=\"noopener\"\n          target=\"_blank\"\n        >\n          <FormattedMessage\n            defaultMessage=\"Learn more\"\n            description=\"Model registry > OSS Promo modal for model version aliases > learn more link\"\n          />\n        </Button>\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_modelsnextuipromomodal.tsx_32\"\n          type=\"primary\"\n          onClick={onTryItNow}\n        >\n          <FormattedMessage\n            defaultMessage=\"Try it now\"\n            description=\"Model registry > OSS Promo modal for model version aliases > try it now button label\"\n          />\n        </Button>\n      </>\n    }\n  >\n    <PromoContentSvg width=\"100%\" />\n    <Typography.Text>\n      <FormattedMessage\n        defaultMessage={`With the latest Model Registry UI, you can use <b>Model Aliases</b> for flexible \n        references to specific model versions, streamlining deployment in a given environment. Use \n        <b>Model Tags</b> to annotate model versions with metadata, like the status of pre-deployment checks.`}\n        description=\"Model registry > OSS Promo modal for model version aliases > description paragraph body\"\n        values={{\n          b: (chunks: any) => <b>{chunks}</b>,\n        }}\n      />\n    </Typography.Text>\n  </Modal>\n);\n", "import { useCallback, useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { ModelsNextUIPromoModal } from './ModelsNextUIPromoModal';\nimport { Modal, Switch, Typography, useDesignSystemTheme } from '@databricks/design-system';\nimport { useNextModelsUIContext } from '../hooks/useNextModelsUI';\n\nconst promoModalSeenStorageKey = '_mlflow_model_registry_promo_modal_dismissed';\n\nexport const ModelsNextUIToggleSwitch = () => {\n  const { usingNextModelsUI, setUsingNextModelsUI } = useNextModelsUIContext();\n\n  const promoModalVisited = window.localStorage.getItem(promoModalSeenStorageKey) === 'true';\n\n  const [promoModalVisible, setPromoModalVisible] = useState(!promoModalVisited);\n  const [confirmDisableModalVisible, setConfirmDisableModalVisible] = useState(false);\n\n  const setPromoModalVisited = useCallback(() => {\n    setPromoModalVisible(false);\n    window.localStorage.setItem(promoModalSeenStorageKey, 'true');\n  }, []);\n\n  const intl = useIntl();\n  const label = intl.formatMessage({\n    defaultMessage: 'New model registry UI',\n    description: 'Model registry > Switcher for the new model registry UI containing aliases > label',\n  });\n  const switchNextUI = (newUsingNewUIValue: boolean) => {\n    if (!newUsingNewUIValue) {\n      setConfirmDisableModalVisible(true);\n    } else {\n      setUsingNextModelsUI(true);\n    }\n  };\n  const { theme } = useDesignSystemTheme();\n  return (\n    <>\n      <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing.xs }}>\n        <label>{label}</label>\n        <Switch\n          componentId=\"codegen_mlflow_app_src_model-registry_components_modelsnextuitoggleswitch.tsx_39\"\n          checked={usingNextModelsUI}\n          aria-label={label}\n          onChange={switchNextUI}\n        />\n      </div>\n      <ModelsNextUIPromoModal\n        visible={promoModalVisible}\n        onClose={() => {\n          setPromoModalVisited();\n        }}\n        onTryItNow={() => {\n          setPromoModalVisited();\n        }}\n      />\n      <Modal\n        componentId=\"codegen_mlflow_app_src_model-registry_components_modelsnextuitoggleswitch.tsx_50\"\n        visible={confirmDisableModalVisible}\n        title={\n          <FormattedMessage\n            defaultMessage=\"Disable the new model stages\"\n            description=\"Model registry > Switcher for the new model registry UI containing aliases > disable confirmation modal title\"\n          />\n        }\n        okText=\"Disable\"\n        onCancel={() => {\n          setConfirmDisableModalVisible(false);\n        }}\n        onOk={() => {\n          setUsingNextModelsUI(false);\n          setConfirmDisableModalVisible(false);\n        }}\n      >\n        <FormattedMessage\n          defaultMessage=\"\n          Thank you for exploring the new Model Registry UI. We are dedicated to providing the best experience, and your feedback is invaluable.\n          Please share your thoughts with us <link>here</link>.\"\n          description=\"Model registry > Switcher for the new model registry UI containing aliases > disable confirmation modal content\"\n          values={{\n            link: (chunks) => (\n              <Typography.Link\n                componentId=\"codegen_mlflow_app_src_model-registry_components_modelsnextuitoggleswitch.tsx_74\"\n                href=\"https://forms.gle/aMB4qDrhMeEm2r359\"\n                openInNewTab\n              >\n                {chunks}\n              </Typography.Link>\n            ),\n          }}\n        />\n      </Modal>\n    </>\n  );\n};\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsx", "Empty", "title", "FormattedMessage", "id", "defaultMessage", "description", "image", "DangerIcon", "CustomErrorBoundary", "_ref", "children", "customFallbackComponent", "logErrorToConsole", "error", "info", "console", "componentStack", "Error<PERSON>ou<PERSON><PERSON>", "onError", "FallbackComponent", "fallback", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "service", "Component", "errorMessage", "props", "$ebb31c7feaa4405e$export$b16d9fb1a22de840", "$hgUW1$createContext", "$44d7e150ebc754d2$var$initialState", "didCatch", "$44d7e150ebc754d2$export$e926676385687eaf", "$hgUW1$Component", "state", "getDerivedStateFromError", "resetErrorBoundary", "_this", "_len", "arguments", "length", "args", "Array", "_key", "onReset", "reason", "setState", "componentDidCatch", "this", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "a", "undefined", "b", "some", "item", "index", "Object", "is", "$44d7e150ebc754d2$var$hasArrayChanged", "next", "prev", "render", "fallback<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$hgUW1$isValidElement", "Error", "$hgUW1$createElement", "Provider", "value", "$75c9d331f9c1ed1a$export$f20aa86254872370", "$7c3c25b3f398a9d6$export$c052f6604b7d51fe", "context", "$hgUW1$useContext", "$hgUW1$useState", "<PERSON><PERSON><PERSON><PERSON>", "memoized", "$hgUW1$useMemo", "resetBoundary", "showBoundary", "$62ff477d53f02a5b$export$f0c7a449e0cfaec7", "errorBoundaryProps", "Wrapped", "name", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "usesFullHeight", "restProps", "_jsxs", "PageWrapper", "css", "styles", "useFullHeightLayout", "wrapper", "Spacer", "fixedSpacer", "container", "defaultProps", "height", "display", "flexDirection", "flexGrow", "flex", "flexShrink", "width", "paddingBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY", "EXPERIMENTS", "MODEL_SERVING", "RUN_TRACKING", "mapErrorWrapperToPredefinedError", "errorWrapper", "requestId", "ErrorWrapper", "status", "networkErrorDetails", "getErrorCode", "ErrorCodes", "RESOURCE_DOES_NOT_EXIST", "NotFoundError", "PERMISSION_DENIED", "PermissionError", "INTERNAL_ERROR", "InternalServerError", "INVALID_PARAMETER_VALUE", "BadRequestError", "messageFromErrorWrapper", "getMessageField", "message", "ModelVersionAliasTag", "closable", "onClose", "className", "compact", "theme", "useDesignSystemTheme", "Tag", "componentId", "_css", "fontWeight", "typography", "typographyBoldFontWeight", "marginRight", "spacing", "xs", "whiteSpace", "max<PERSON><PERSON><PERSON>", "textOverflow", "overflow", "useOldModelsUIStorageKey", "NextModelsUIContext", "React", "usingNextModelsUI", "shouldShowModelsNextUI", "setUsingNextModelsUI", "useNextModelsUIContext", "useContext", "withNextModelsUIContext", "useState", "localStorage", "getItem", "useEffect", "setItem", "toString", "contextValue", "useMemo", "_rect", "_path", "_path2", "_path3", "_rect2", "_path4", "_path5", "_rect3", "_path6", "_path7", "_path8", "_rect4", "_path9", "_rect5", "_path10", "_path11", "_rect6", "_path12", "_rect7", "_path13", "_rect8", "_path14", "_defs", "_extends", "assign", "bind", "n", "e", "t", "r", "hasOwnProperty", "call", "apply", "SvgPromoModalContent", "svgRef", "titleId", "viewBox", "fill", "xmlns", "ref", "d", "x", "y", "rx", "fillOpacity", "x1", "y1", "x2", "y2", "gradientUnits", "stopColor", "offset", "stopOpacity", "ForwardRef", "ModelsNextUIPromoModal", "visible", "onTryItNow", "Modal", "onCancel", "footer", "_Fragment", "<PERSON><PERSON>", "href", "modelStagesMigrationGuideLink", "rel", "target", "type", "onClick", "PromoContentSvg", "Typography", "Text", "values", "chunks", "promoModalSeenStorageKey", "ModelsNextUIToggleSwitch", "promoModalVisited", "window", "promoModalVisible", "setPromoModalVisible", "confirmDisableModalVisible", "setConfirmDisableModalVisible", "setPromoModalVisited", "useCallback", "label", "useIntl", "formatMessage", "style", "alignItems", "gap", "Switch", "checked", "onChange", "newUsingNewUIValue", "okText", "onOk", "link", "Link", "openInNewTab"], "sourceRoot": ""}