"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[3794],{37616:function(e,o,a){a.d(o,{z7:function(){return g}});var l=a(17729),n=a(74702),s=a(42346);var t={'code[class*="language-"]':{fontFamily:'Consolas, Menlo, Monaco, "Andale Mono WT", "Andale Mono", "Lucida Console", "Lucida Sans Typewriter", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Liberation Mono", "Nimbus Mono L", "Courier New", Courier, monospace',fontSize:"14px",lineHeight:"1.375",direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",background:"#2a2734",color:"#5DFAFC"},'pre[class*="language-"]':{fontFamily:'Consolas, Menlo, Monaco, "Andale Mono WT", "Andale Mono", "Lucida Console", "Lucida Sans Typewriter", "DejaVu Sans Mono", "Bitstream Vera Sans Mono", "Liberation Mono", "Nimbus Mono L", "Courier New", Courier, monospace',fontSize:"14px",lineHeight:"1.375",direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",background:"#2a2734",color:"#5DFAFC",padding:"1em",margin:".5em 0",overflow:"auto"},'pre > code[class*="language-"]':{fontSize:"1em"},'pre[class*="language-"]::-moz-selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"] ::-moz-selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"]::-moz-selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"] ::-moz-selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"]::selection':{textShadow:"none",background:"#6a51e6"},'pre[class*="language-"] ::selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"]::selection':{textShadow:"none",background:"#6a51e6"},'code[class*="language-"] ::selection':{textShadow:"none",background:"#6a51e6"},':not(pre) > code[class*="language-"]':{padding:".1em",borderRadius:".3em"},comment:{color:"#6c6783"},prolog:{color:"#6c6783"},doctype:{color:"#6c6783"},cdata:{color:"#6c6783"},punctuation:{color:"#6c6783"},namespace:{Opacity:".7"},tag:{color:"#3AACE2"},operator:{color:"#3AACE2"},number:{color:"#3AACE2"},property:{color:"#5DFAFC"},function:{color:"#5DFAFC"},"tag-id":{color:"#eeebff"},selector:{color:"#eeebff"},"atrule-id":{color:"#eeebff"},"code.language-javascript":{color:"#c4b9fe"},"attr-name":{color:"#c4b9fe"},"code.language-css":{color:"#ffffff"},"code.language-scss":{color:"#ffffff"},boolean:{color:"#ffffff"},string:{color:"#ffffff"},entity:{color:"#ffffff",cursor:"help"},url:{color:"#ffffff"},".language-css .token.string":{color:"#ffffff"},".language-scss .token.string":{color:"#ffffff"},".style .token.string":{color:"#ffffff"},"attr-value":{color:"#ffffff"},keyword:{color:"#ffffff"},control:{color:"#ffffff"},directive:{color:"#ffffff"},unit:{color:"#ffffff"},statement:{color:"#ffffff"},regex:{color:"#ffffff"},atrule:{color:"#ffffff"},placeholder:{color:"#ffffff"},variable:{color:"#ffffff"},deleted:{textDecoration:"line-through"},inserted:{borderBottom:"1px dotted #eeebff",textDecoration:"none"},italic:{fontStyle:"italic"},important:{fontWeight:"bold",color:"#c4b9fe"},bold:{fontWeight:"bold"},"pre > code.highlight":{Outline:".4em solid #8a75f5",OutlineOffset:".4em"},".line-numbers.line-numbers .line-numbers-rows":{borderRightColor:"#2c2937"},".line-numbers .line-numbers-rows > span:before":{color:"#3c3949"},".line-highlight.line-highlight":{background:"linear-gradient(to right, rgba(224, 145, 66, 0.2) 70%, rgba(224, 145, 66, 0))"}};var r={'code[class*="language-"]':{textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",color:"rgb(77, 77, 76)",background:"#fafafa",fontFamily:"Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace",fontSize:"12px",lineHeight:"1.5em",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none"},'pre[class*="language-"]':{textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",color:"rgb(77, 77, 76)",background:"#fafafa",fontFamily:"Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace",fontSize:"12px",lineHeight:"1.5em",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",overflow:"auto",position:"relative",margin:"0.5em 0",padding:"1.25em 1em"},'code[class*="language-"]::-moz-selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"]::-moz-selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"] ::-moz-selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"] ::-moz-selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"]::selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"]::selection':{background:"#cceae7",color:"#263238"},'code[class*="language-"] ::selection':{background:"#cceae7",color:"#263238"},'pre[class*="language-"] ::selection':{background:"#cceae7",color:"#263238"},':not(pre) > code[class*="language-"]':{whiteSpace:"normal",borderRadius:"0.2em",padding:"0.1em"},".language-css > code":{color:"#f5871f"},".language-sass > code":{color:"#f5871f"},".language-scss > code":{color:"#f5871f"},'[class*="language-"] .namespace':{Opacity:"0.7"},atrule:{color:"#7c4dff"},"attr-name":{color:"#39adb5"},"attr-value":{color:"#f6a434"},attribute:{color:"#f6a434"},boolean:{color:"#7c4dff"},builtin:{color:"#39adb5"},cdata:{color:"#39adb5"},char:{color:"#39adb5"},class:{color:"#39adb5"},"class-name":{color:"#6182b8"},comment:{color:"#8e908c"},constant:{color:"#7c4dff"},deleted:{color:"#e53935"},doctype:{color:"#aabfc9"},entity:{color:"#e53935"},function:{color:"#4271ae"},hexcode:{color:"#f5871f"},id:{color:"#7c4dff",fontWeight:"bold"},important:{color:"#7c4dff",fontWeight:"bold"},inserted:{color:"#39adb5"},keyword:{color:"#8959a8"},number:{color:"#f5871f"},operator:{color:"#3e999f"},prolog:{color:"#aabfc9"},property:{color:"#39adb5"},"pseudo-class":{color:"#f6a434"},"pseudo-element":{color:"#f6a434"},punctuation:{color:"rgb(77, 77, 76)"},regex:{color:"#6182b8"},selector:{color:"#e53935"},string:{color:"#3ba85f"},symbol:{color:"#7c4dff"},tag:{color:"#e53935"},unit:{color:"#f5871f"},url:{color:"#e53935"},variable:{color:"#c72d4c"}},c=a(9133),i=a(50111);l.A.registerLanguage("python",n.A),l.A.registerLanguage("json",s.A);const d="24px",u={light:r,duotoneDark:t};function g(e){let{theme:o="light",language:a,actions:n,style:s,children:t,showLineNumbers:r,lineNumberStyle:g,wrapLongLines:f}=e;const m={border:"none",borderRadius:0,margin:0,padding:d,...s};return(0,i.Y)(l.A,{showLineNumbers:r,lineNumberStyle:g,language:a,style:u[o],customStyle:m,codeTagProps:{style:(0,c.pick)(s,"backgroundColor")},wrapLongLines:f,children:t})}},43683:function(e,o,a){a.d(o,{Q:function(){return M}});var l=a(89555),n=a(9133),s=a(31014),t=a(32599),r=a(15579),c=a(48012),i=a(88464),d=a(88443),u=a(13369),g=a(50111);var f={name:"4zleql",styles:"display:block"};function m(e){return o=>function(e,o){const a=(0,i.A)(),{theme:r}=(0,t.u)(),d=e.props.searchValue.toLowerCase();return(0,s.useMemo)((()=>{if(!d)return e;if((0,n.sortedIndexOf)(o,d)>=0)return e;const t=/^[^,.:/=\-\s]+$/.test(d);return s.cloneElement(e,{flattenOptions:[{data:{value:d,disabled:!t,style:{color:t?r.colors.actionTertiaryTextDefault:r.colors.actionDisabledText},children:(0,g.Y)(c.paO,{title:t?void 0:a.formatMessage({id:"fWEvZL",defaultMessage:", . : / - = and blank spaces are not allowed"}),placement:"right",children:(0,g.FD)("span",{css:f,children:[(0,g.Y)(c.c11,{css:(0,l.AH)({marginRight:r.spacing.sm},"")}),a.formatMessage({id:"IJbauF",defaultMessage:'Add tag "{tagKey}"'},{tagKey:d})]})})},key:d,groupOption:!1},...e.props.flattenOptions]})}),[o,e,d,a,r])}(o,e)}var p={name:"1d3w5wq",styles:"width:100%"};function h(e){let{allAvailableTags:o,control:a,onKeyChangeCallback:l}=e;const n=(0,i.A)(),[t,r]=(0,s.useState)(!1),d=(0,s.useRef)(null),{field:f,fieldState:h}=(0,u.as)({control:a,name:"key",rules:{required:{message:n.formatMessage({id:"RlBbjb",defaultMessage:"A tag key is required"}),value:!0}}});return(0,g.Y)(c._vn,{allowClear:!0,ref:d,dangerouslySetAntdProps:{showSearch:!0,dropdownRender:m(o)},css:p,placeholder:n.formatMessage({id:"8ALhnh",defaultMessage:"Type a key"}),value:f.value,defaultValue:f.value,open:t,onDropdownVisibleChange:e=>{r(e)},filterOption:(e,o)=>null===o||void 0===o?void 0:o.value.toLowerCase().includes(e.toLowerCase()),onSelect:e=>{f.onChange(e),null===l||void 0===l||l(e)},onClear:()=>{f.onChange(void 0),null===l||void 0===l||l(void 0)},validationState:h.error?"error":void 0,children:o.map((e=>(0,g.Y)(c._vn.Option,{value:e,children:e},e)))})}var b=a(98597),y=a(52350);function v(e){return new Map(e.map((e=>[e.key,e])))}var k={name:"82a6rk",styles:"flex:1"},w={name:"82a6rk",styles:"flex:1"};const M=e=>{let{onSuccess:o,saveTagsHandler:a,allAvailableTags:f,valueRequired:m=!1,title:p}=e;const M=(0,s.useRef)(),[_,x]=(0,s.useState)(""),{theme:C}=(0,t.u)(),[A,T]=(0,s.useState)(new Map),[Y,z]=(0,s.useState)(new Map),[F,V]=(0,s.useState)(!1),L=(0,u.mN)({defaultValues:{key:void 0,value:""}}),D=()=>V(!1),H=(0,s.useCallback)((e=>{M.current=e,T(v(e.tags||[])),z(v(e.tags||[])),L.reset(),V(!0)}),[L]),O=async()=>{M.current&&(x(""),K(!0),a(M.current,Array.from(A.values()),Array.from(Y.values())).then((()=>{D(),null===o||void 0===o||o(),K(!1)})).catch((e=>{var o;K(!1),x(e instanceof y.s?null===(o=e.getUserVisibleError())||void 0===o?void 0:o.message:e.message)})))},I=(0,i.A)(),B=L.watch(),[W,K]=(0,s.useState)(!1),R=(0,s.useMemo)((()=>!(0,n.isEqual)((0,n.sortBy)(Array.from(A.values()),"key"),(0,n.sortBy)(Array.from(Y.values()),"key"))),[A,Y]),N=B.key||B.value,E=R&&N;return{EditTagsModal:(0,g.FD)(r.d,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_135",destroyOnClose:!0,visible:F,title:null!==p&&void 0!==p?p:(0,g.Y)(d.A,{id:"TBX+Gs",defaultMessage:"Add/Edit tags"}),onCancel:D,footer:(0,g.FD)(t.y,{children:[(0,g.Y)(t.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_147",dangerouslyUseFocusPseudoClass:!0,onClick:D,css:(0,l.AH)({marginRight:R?0:C.spacing.sm},""),children:I.formatMessage({id:"2a/rR8",defaultMessage:"Cancel"})}),E?(0,g.Y)(S,{formValues:B,isLoading:W,onSaveTask:O}):(0,g.Y)(c.paO,{title:R?void 0:I.formatMessage({id:"16onEc",defaultMessage:"Please add or remove one or more tags before saving"}),children:(0,g.Y)(t.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_174",dangerouslyUseFocusPseudoClass:!0,disabled:!R,loading:W,type:"primary",onClick:O,children:I.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})})]}),children:[(0,g.FD)("form",{onSubmit:L.handleSubmit((()=>{if(m&&!B.value.trim())return;const e=new Map(Y);e.set(B.key,B),z(e),L.reset()})),css:(0,l.AH)({display:"flex",alignItems:"flex-end",gap:C.spacing.md},""),children:[(0,g.FD)("div",{css:(0,l.AH)({minWidth:0,display:"flex",gap:C.spacing.md,flex:1},""),children:[(0,g.FD)("div",{css:k,children:[(0,g.Y)(c.D$Q.Label,{htmlFor:"key",children:I.formatMessage({id:"crTWax",defaultMessage:"Key"})}),(0,g.Y)(h,{allAvailableTags:f||[],control:L.control,onKeyChangeCallback:e=>{var o;const a=e?Y.get(e):void 0;L.setValue("value",null!==(o=null===a||void 0===a?void 0:a.value)&&void 0!==o?o:"")}})]}),(0,g.FD)("div",{css:w,children:[(0,g.Y)(c.D$Q.Label,{htmlFor:"value",children:m?I.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):I.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"})}),(0,g.Y)(c.tc_.Input,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_223",name:"value",control:L.control,"aria-label":m?I.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):I.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"}),placeholder:I.formatMessage({id:"FFe+Ug",defaultMessage:"Type a value"})})]})]}),(0,g.Y)(c.paO,{title:I.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,g.Y)(t.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_248",htmlType:"submit","aria-label":I.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,g.Y)(c.c11,{})})})]}),_&&(0,g.Y)(c.D$Q.Message,{type:"error",message:_}),(0,g.Y)("div",{css:(0,l.AH)({display:"flex",rowGap:C.spacing.xs,flexWrap:"wrap",marginTop:C.spacing.sm},""),children:Array.from(Y.values()).map((e=>(0,g.Y)(b.t,{isClosable:!0,tag:e,onClose:()=>(e=>{let{key:o}=e;z((e=>(e.delete(o),new Map(e))))})(e)},e.key)))})]}),showEditTagsModal:H,isLoading:W}};var _={name:"1y0ex1",styles:"max-width:400px"};function S(e){let{isLoading:o,formValues:a,onSaveTask:s}=e;const r=(0,i.A)(),{theme:c}=(0,t.u)(),d=`${`${(0,n.truncate)(a.key,{length:20})||"_"}`}${a.value?`:${(0,n.truncate)(a.value,{length:20})}`:""}`,u=r.formatMessage({id:"wcSVYI",defaultMessage:'Are you sure you want to save and close without adding "{tag}"'},{tag:d});return(0,g.FD)(t.av.Root,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_309",children:[(0,g.Y)(t.av.Trigger,{asChild:!0,children:(0,g.Y)(t.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_306",dangerouslyUseFocusPseudoClass:!0,loading:o,type:"primary",children:r.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})}),(0,g.FD)(t.av.Content,{align:"end","aria-label":u,children:[(0,g.Y)(t.T.Paragraph,{css:_,children:u}),(0,g.Y)(t.av.Close,{asChild:!0,children:(0,g.Y)(t.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_316",onClick:s,children:r.formatMessage({id:"mv6CY3",defaultMessage:"Yes, save and close"})})}),(0,g.Y)(t.av.Close,{asChild:!0,children:(0,g.Y)(t.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_324",type:"primary",css:(0,l.AH)({marginLeft:c.spacing.sm},""),children:r.formatMessage({id:"geizp1",defaultMessage:"Cancel"})})}),(0,g.Y)(t.av.Arrow,{})]})]})}},56412:function(e,o,a){a.d(o,{i:function(){return i}});var l=a(31014),n=a(88443),s=a(48012),t=a(32599),r=a(50111);var c={name:"1739oy8",styles:"z-index:1"};const i=e=>{let{copyText:o,showLabel:a=!0,componentId:i,...d}=e;const[u,g]=(0,l.useState)(!1);return(0,r.Y)(s.paO,{title:(0,r.Y)(n.A,{id:"X+boXI",defaultMessage:"Copied"}),dangerouslySetAntdProps:{visible:u},children:(0,r.Y)(t.B,{componentId:null!==i&&void 0!==i?i:"mlflow.shared.copy_button",type:"primary",onClick:()=>{navigator.clipboard.writeText(o),g(!0),setTimeout((()=>{g(!1)}),3e3)},onMouseLeave:()=>{g(!1)},css:c,children:a?(0,r.Y)(n.A,{id:"1Iq+NW",defaultMessage:"Copy"}):void 0,...d})})}},98597:function(e,o,a){a.d(o,{t:function(){return b}});var l=a(89555),n=a(48012),s=a(32599),t=a(31014),r=a(88464),c=a(15579),i=a(56412),d=a(50111);const{Paragraph:u}=s.T;var g={name:"zjik7",styles:"display:flex"},f={name:"1ff36h2",styles:"flex-grow:1"};const m=t.memo((e=>{const{theme:o}=(0,s.u)();return(0,d.Y)(c.d,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetagfullviewmodal.tsx_17",title:"Tag: "+e.tagKey,visible:e.isKeyValueTagFullViewModalVisible,onCancel:()=>e.setIsKeyValueTagFullViewModalVisible(!1),children:(0,d.FD)("div",{css:g,children:[(0,d.Y)(u,{css:f,children:(0,d.Y)("pre",{css:(0,l.AH)({backgroundColor:o.colors.backgroundPrimary,marginTop:o.spacing.sm,whiteSpace:"pre-wrap",wordBreak:"break-all"},""),children:e.tagValue})}),(0,d.Y)("div",{css:(0,l.AH)({marginTop:o.spacing.sm},""),children:(0,d.Y)(i.i,{copyText:e.tagValue,showLabel:!1,icon:(0,d.Y)(n.TdU,{}),"aria-label":"Copy"})})]})})})),p=30;function h(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?{overflow:"hidden",textOverflow:"ellipsis",textWrap:"nowrap",whiteSpace:"nowrap"}:{whiteSpace:"nowrap"}}const b=e=>{let{isClosable:o=!1,onClose:a,tag:c,enableFullViewModal:i=!1,charLimit:u=p,maxWidth:g=300,className:f}=e;const b=(0,r.A)(),[y,v]=(0,t.useState)(!1),{shouldTruncateKey:k,shouldTruncateValue:w}=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;const{key:a,value:l}=e,n=a.length+l.length,s=a.length>l.length,t=s?l.length:a.length;return n<=o?{shouldTruncateKey:!1,shouldTruncateValue:!1}:t>o/2?{shouldTruncateKey:!0,shouldTruncateValue:!0}:{shouldTruncateKey:s,shouldTruncateValue:!s}}(c,u),M=i&&(k||w),_=b.formatMessage({id:"ZXUtU8",defaultMessage:"Click to see more"});return(0,d.FD)("div",{children:[(0,d.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetag.tsx_60",closable:o,onClose:a,title:c.key,className:f,children:(0,d.Y)(n.paO,{title:M?_:"",children:(0,d.FD)("span",{css:(0,l.AH)({maxWidth:g,display:"inline-flex"},""),onClick:()=>M?v(!0):void 0,children:[(0,d.Y)(s.T.Text,{bold:!0,title:c.key,css:h(k),children:c.key}),c.value&&(0,d.FD)(s.T.Text,{title:c.value,css:h(w),children:[": ",c.value]})]})})}),(0,d.Y)("div",{children:y&&(0,d.Y)(m,{tagKey:c.key,tagValue:c.value,isKeyValueTagFullViewModalVisible:y,setIsKeyValueTagFullViewModalVisible:v})})]})}}}]);
//# sourceMappingURL=3794.d9e54950.chunk.js.map