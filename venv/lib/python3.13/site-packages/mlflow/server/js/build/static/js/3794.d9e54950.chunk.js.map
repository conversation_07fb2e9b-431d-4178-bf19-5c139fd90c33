{"version": 3, "file": "static/js/3794.d9e54950.chunk.js", "mappings": "6LAkOA,MA7NmC,CACjC,2BAA4B,CAC1BA,WACE,8NACFC,SAAU,OACVC,WAAY,QACZC,UAAW,MACXC,UAAW,OACXC,WAAY,MACZC,YAAa,SACbC,UAAW,SACXC,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,OACTC,WAAY,UACZC,MAAO,WAET,0BAA2B,CACzBhB,WACE,8NACFC,SAAU,OACVC,WAAY,QACZC,UAAW,MACXC,UAAW,OACXC,WAAY,MACZC,YAAa,SACbC,UAAW,SACXC,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,OACTC,WAAY,UACZC,MAAO,UACPC,QAAS,MACTC,OAAQ,SACRC,SAAU,QAEZ,iCAAkC,CAChClB,SAAU,OAEZ,0CAA2C,CACzCmB,WAAY,OACZL,WAAY,WAEd,2CAA4C,CAC1CK,WAAY,OACZL,WAAY,WAEd,2CAA4C,CAC1CK,WAAY,OACZL,WAAY,WAEd,4CAA6C,CAC3CK,WAAY,OACZL,WAAY,WAEd,qCAAsC,CACpCK,WAAY,OACZL,WAAY,WAEd,sCAAuC,CACrCK,WAAY,OACZL,WAAY,WAEd,sCAAuC,CACrCK,WAAY,OACZL,WAAY,WAEd,uCAAwC,CACtCK,WAAY,OACZL,WAAY,WAEd,uCAAwC,CACtCE,QAAS,OACTI,aAAc,QAEhBC,QAAS,CACPN,MAAO,WAETO,OAAQ,CACNP,MAAO,WAETQ,QAAS,CACPR,MAAO,WAETS,MAAO,CACLT,MAAO,WAETU,YAAa,CACXV,MAAO,WAETW,UAAW,CACTC,QAAS,MAEXC,IAAK,CACHb,MAAO,WAETc,SAAU,CACRd,MAAO,WAETe,OAAQ,CACNf,MAAO,WAETgB,SAAU,CACRhB,MAAO,WAETiB,SAAU,CACRjB,MAAO,WAET,SAAU,CACRA,MAAO,WAETkB,SAAU,CACRlB,MAAO,WAET,YAAa,CACXA,MAAO,WAET,2BAA4B,CAC1BA,MAAO,WAET,YAAa,CACXA,MAAO,WAET,oBAAqB,CACnBA,MAAO,WAET,qBAAsB,CACpBA,MAAO,WAETmB,QAAS,CACPnB,MAAO,WAEToB,OAAQ,CACNpB,MAAO,WAETqB,OAAQ,CACNrB,MAAO,UACPsB,OAAQ,QAEVC,IAAK,CACHvB,MAAO,WAET,8BAA+B,CAC7BA,MAAO,WAET,+BAAgC,CAC9BA,MAAO,WAET,uBAAwB,CACtBA,MAAO,WAET,aAAc,CACZA,MAAO,WAETwB,QAAS,CACPxB,MAAO,WAETyB,QAAS,CACPzB,MAAO,WAET0B,UAAW,CACT1B,MAAO,WAET2B,KAAM,CACJ3B,MAAO,WAET4B,UAAW,CACT5B,MAAO,WAET6B,MAAO,CACL7B,MAAO,WAET8B,OAAQ,CACN9B,MAAO,WAET+B,YAAa,CACX/B,MAAO,WAETgC,SAAU,CACRhC,MAAO,WAETiC,QAAS,CACPC,eAAgB,gBAElBC,SAAU,CACRC,aAAc,qBACdF,eAAgB,QAElBG,OAAQ,CACNC,UAAW,UAEbC,UAAW,CACTC,WAAY,OACZxC,MAAO,WAETyC,KAAM,CACJD,WAAY,QAEd,uBAAwB,CACtBE,QAAS,qBACTC,cAAe,QAEjB,gDAAiD,CAC/CC,iBAAkB,WAEpB,iDAAkD,CAChD5C,MAAO,WAET,iCAAkC,CAChCD,WAAY,kFCXhB,MA5M6B,CAC3B,2BAA4B,CAC1BX,UAAW,OACXC,WAAY,MACZC,YAAa,SACbC,UAAW,SACXsD,SAAU,SACV7C,MAAO,kBACPD,WAAY,UACZf,WAAY,mEACZC,SAAU,OACVC,WAAY,QACZM,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,QAEX,0BAA2B,CACzBV,UAAW,OACXC,WAAY,MACZC,YAAa,SACbC,UAAW,SACXsD,SAAU,SACV7C,MAAO,kBACPD,WAAY,UACZf,WAAY,mEACZC,SAAU,OACVC,WAAY,QACZM,WAAY,IACZC,SAAU,IACVC,QAAS,IACTC,cAAe,OACfC,WAAY,OACZC,UAAW,OACXC,QAAS,OACTK,SAAU,OACV2C,SAAU,WACV5C,OAAQ,UACRD,QAAS,cAEX,2CAA4C,CAC1CF,WAAY,UACZC,MAAO,WAET,0CAA2C,CACzCD,WAAY,UACZC,MAAO,WAET,4CAA6C,CAC3CD,WAAY,UACZC,MAAO,WAET,2CAA4C,CAC1CD,WAAY,UACZC,MAAO,WAET,sCAAuC,CACrCD,WAAY,UACZC,MAAO,WAET,qCAAsC,CACpCD,WAAY,UACZC,MAAO,WAET,uCAAwC,CACtCD,WAAY,UACZC,MAAO,WAET,sCAAuC,CACrCD,WAAY,UACZC,MAAO,WAET,uCAAwC,CACtCX,WAAY,SACZgB,aAAc,QACdJ,QAAS,SAEX,uBAAwB,CACtBD,MAAO,WAET,wBAAyB,CACvBA,MAAO,WAET,wBAAyB,CACvBA,MAAO,WAET,kCAAmC,CACjCY,QAAS,OAEXkB,OAAQ,CACN9B,MAAO,WAET,YAAa,CACXA,MAAO,WAET,aAAc,CACZA,MAAO,WAET+C,UAAW,CACT/C,MAAO,WAETmB,QAAS,CACPnB,MAAO,WAETgD,QAAS,CACPhD,MAAO,WAETS,MAAO,CACLT,MAAO,WAETiD,KAAM,CACJjD,MAAO,WAETkD,MAAO,CACLlD,MAAO,WAET,aAAc,CACZA,MAAO,WAETM,QAAS,CACPN,MAAO,WAETmD,SAAU,CACRnD,MAAO,WAETiC,QAAS,CACPjC,MAAO,WAETQ,QAAS,CACPR,MAAO,WAETqB,OAAQ,CACNrB,MAAO,WAETiB,SAAU,CACRjB,MAAO,WAEToD,QAAS,CACPpD,MAAO,WAETqD,GAAI,CACFrD,MAAO,UACPwC,WAAY,QAEdD,UAAW,CACTvC,MAAO,UACPwC,WAAY,QAEdL,SAAU,CACRnC,MAAO,WAETwB,QAAS,CACPxB,MAAO,WAETe,OAAQ,CACNf,MAAO,WAETc,SAAU,CACRd,MAAO,WAETO,OAAQ,CACNP,MAAO,WAETgB,SAAU,CACRhB,MAAO,WAET,eAAgB,CACdA,MAAO,WAET,iBAAkB,CAChBA,MAAO,WAETU,YAAa,CACXV,MAAO,mBAET6B,MAAO,CACL7B,MAAO,WAETkB,SAAU,CACRlB,MAAO,WAEToB,OAAQ,CACNpB,MAAO,WAETsD,OAAQ,CACNtD,MAAO,WAETa,IAAK,CACHb,MAAO,WAET2B,KAAM,CACJ3B,MAAO,WAETuB,IAAK,CACHvB,MAAO,WAETgC,SAAU,CACRhC,MAAO,Y,qBC3MXuD,EAAAA,EAAkBC,iBAAiB,SAAUC,EAAAA,GAC7CF,EAAAA,EAAkBC,iBAAiB,OAAQE,EAAAA,GASpC,MAKMC,EAAiB,OAExBC,EAA8C,CAClDC,MAAOC,EACPC,YAAaC,GA4CR,SAASC,EAAWC,GASL,IATM,MAC1BC,EAAQ,QAAO,SACfC,EAAQ,QACRC,EAAO,MACPC,EAAK,SACLC,EAAQ,gBACRC,EAAe,gBACfC,EAAe,cACfC,GACiBR,EACjB,MAAMS,EAAc,CAClBC,OAAQ,OACRvE,aAAc,EACdH,OAAQ,EACRD,QAAS0D,KACNW,GAGL,OACEO,EAAAA,EAAAA,GAACtB,EAAAA,EAAiB,CAChBiB,gBAAiBA,EACjBC,gBAAiBA,EACjBL,SAAUA,EACVE,MAAOV,EAAaO,GACpBQ,YAAaA,EACbG,aAAc,CACZR,OAAOS,EAAAA,EAAAA,MAAKT,EAAO,oBAErBI,cAAeA,EAAcH,SAE5BA,GAGP,C,yKC/FwG,IAAAL,EAAA,CAAAc,KAAA,SAAAC,OAAA,iBAyExG,SAASC,EAAgBC,GACvB,OAAQC,GA/DV,SAAsBA,EAA0BD,GAC9C,MAAME,GAAOC,EAAAA,EAAAA,MACP,MAAEnB,IAAUoB,EAAAA,EAAAA,KACZC,EAAcJ,EAAKK,MAAMD,YAAYE,cAwD3C,OAtDqBC,EAAAA,EAAAA,UAAQ,KAC3B,IAAKH,EAAa,OAAOJ,EAGzB,IADsBQ,EAAAA,EAAAA,eAAcT,EAAkBK,IAAgB,EACnD,OAAOJ,EAE1B,MAAMS,EAAgB,kBAAkBC,KAAKN,GAG7C,OAAOO,EAAAA,aAAmBX,EAAM,CAC9BY,eAAgB,CACd,CACEC,KAAM,CACJC,MAAOV,EACPW,UAAWN,EACXvB,MAAO,CACLtE,MAAO6F,EAAgB1B,EAAMiC,OAAOC,0BAA4BlC,EAAMiC,OAAOE,oBAE/E/B,UACEM,EAAAA,EAAAA,GAAC0B,EAAAA,IAAa,CACZC,MACEX,OACIY,EACApB,EAAKqB,cAAc,CAAArD,GAAA,SACjBsD,eAAe,iDAKvBC,UAAU,QAAOrC,UAEjBsC,EAAAA,EAAAA,IAAA,QAAMC,IAAG5C,EAAuBK,SAAA,EAC9BM,EAAAA,EAAAA,GAACkC,EAAAA,IAAQ,CAACD,KAAGE,EAAAA,EAAAA,IAAE,CAAEC,YAAa9C,EAAM+C,QAAQC,IAAI,MAC/C9B,EAAKqB,cACJ,CAAArD,GAAA,SACEsD,eAAe,sBAGjB,CACES,OAAQ5B,UAOpB6B,IAAK7B,EACL8B,aAAa,MAEZlC,EAAKK,MAAMO,iBAEhB,GACD,CAACb,EAAkBC,EAAMI,EAAaH,EAAMlB,GAGjD,CAGuCoD,CAAanC,EAAMD,EAC1D,CAEA,IAAAqC,EAAA,CAAAxC,KAAA,UAAAC,OAAA,cAGO,SAASwC,EAAoBC,GAQhC,IARiC,iBACnCvC,EAAgB,QAChB1D,EAAO,oBACPkG,GAKDD,EACC,MAAMrC,GAAOC,EAAAA,EAAAA,MACNsC,EAAQC,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAYC,EAAAA,EAAAA,QAAgD,OAE5D,MAAEC,EAAK,WAAEC,IAAeC,EAAAA,EAAAA,IAAc,CAC1C1G,QAASA,EACTuD,KAAM,MACNoD,MAAO,CACLC,SAAU,CACRC,QAASjD,EAAKqB,cAAc,CAAArD,GAAA,SAC1BsD,eAAe,0BAGjBT,OAAO,MAmBb,OACErB,EAAAA,EAAAA,GAAC0D,EAAAA,IAAY,CACXC,YAAU,EACVC,IAAKV,EACLW,wBAAyB,CACvBC,YAAY,EACZC,eAAgB1D,EAAgBC,IAElC2B,IAAGU,EACHzF,YAAasD,EAAKqB,cAAc,CAAArD,GAAA,SAC9BsD,eAAe,eAGjBT,MAAO+B,EAAM/B,MACb2C,aAAcZ,EAAM/B,MACpB4C,KAAMlB,EACNmB,wBA9BiCC,IACnCnB,EAAUmB,EAAQ,EA8BhBC,aAAcA,CAACC,EAAOC,IAAiB,OAANA,QAAM,IAANA,OAAM,EAANA,EAAQjD,MAAMR,cAAc0D,SAASF,EAAMxD,eAC5E2D,SAvBkBhC,IACpBY,EAAMqB,SAASjC,GACI,OAAnBM,QAAmB,IAAnBA,GAAAA,EAAsBN,EAAI,EAsBxBkC,QA7BgBC,KAClBvB,EAAMqB,cAAS7C,GACI,OAAnBkB,QAAmB,IAAnBA,GAAAA,OAAsBlB,EAAU,EA4B9BgD,gBAAiBvB,EAAWwB,MAAQ,aAAUjD,EAAUlC,SAEvDY,EAAiBwE,KAAK9I,IACrBgE,EAAAA,EAAAA,GAAC0D,EAAAA,IAAaqB,OAAM,CAAC1D,MAAOrF,EAAI0D,SAC7B1D,GADmCA,MAM9C,C,0BCpIA,SAASgJ,EAAWC,GAClB,OAAO,IAAIC,IAAID,EAAKH,KAAK9I,GAAQ,CAACA,EAAIwG,IAAKxG,KAC7C,CAEA,IAAA2G,EAAA,CAAAxC,KAAA,SAAAC,OAAA,UAAA+E,EAAA,CAAAhF,KAAA,SAAAC,OAAA,UAGO,MAAMgF,EAA2B/F,IAYjC,IAZyE,UAC9EgG,EAAS,gBACTC,EAAe,iBACfhF,EAAgB,cAChBiF,GAAgB,EAAK,MACrB5D,GAODtC,EACC,MAAMmG,GAAkBrC,EAAAA,EAAAA,WACjBsC,EAAcC,IAAmBzC,EAAAA,EAAAA,UAAiB,KACnD,MAAE3D,IAAUoB,EAAAA,EAAAA,MAEXiF,EAAaC,IAAkB3C,EAAAA,EAAAA,UAAsC,IAAIiC,MACzEW,EAAWC,IAAgB7C,EAAAA,EAAAA,UAAsC,IAAIiC,MAErEa,EAAWC,IAAgB/C,EAAAA,EAAAA,WAAS,GAErCgD,GAAOC,EAAAA,EAAAA,IAAwB,CACnCC,cAAe,CACb3D,SAAKZ,EACLP,MAAO,MAIL+E,EAAYA,IAAMJ,GAAa,GAK/BK,GAAoBC,EAAAA,EAAAA,cACvBC,IACCf,EAAgBgB,QAAUD,EAC1BX,EAAeZ,EAAWuB,EAAatB,MAAQ,KAC/Ca,EAAad,EAAWuB,EAAatB,MAAQ,KAC7CgB,EAAKQ,QAELT,GAAa,EAAK,GAEpB,CAACC,IAGGS,EAAWC,UACVnB,EAAgBgB,UAGrBd,EAAgB,IAChBkB,GAAa,GACbtB,EAAgBE,EAAgBgB,QAASK,MAAMC,KAAKnB,EAAYoB,UAAWF,MAAMC,KAAKjB,EAAUkB,WAC7FC,MAAK,KACJZ,IACS,OAATf,QAAS,IAATA,GAAAA,IACAuB,GAAa,EAAM,IAEpBK,OAAOC,IAA6B,IAADC,EAClCP,GAAa,GACblB,EAAgBwB,aAAaE,EAAAA,EAAsC,QAA1BD,EAAGD,EAAEG,6BAAqB,IAAAF,OAAA,EAAvBA,EAAyB1D,QAAUyD,EAAEzD,QAAQ,IACzF,EAGAjD,GAAOC,EAAAA,EAAAA,KACP6G,EAAarB,EAAKsB,SAEjBC,EAAWZ,IAAgB3D,EAAAA,EAAAA,WAAS,GAErCwE,GAAe3G,EAAAA,EAAAA,UACnB,MAAO4G,EAAAA,EAAAA,UAAQC,EAAAA,EAAAA,QAAOd,MAAMC,KAAKnB,EAAYoB,UAAW,QAAQY,EAAAA,EAAAA,QAAOd,MAAMC,KAAKjB,EAAUkB,UAAW,SACvG,CAACpB,EAAaE,IAEV+B,EAAUN,EAAW9E,KAAO8E,EAAWjG,MACvCwG,EAAqBJ,GAAgBG,EAmL3C,MAAO,CAAEE,eAnJP9F,EAAAA,EAAAA,IAAC+F,EAAAA,EAAK,CACJC,YAAY,uEACZC,gBAAc,EACd9D,QAAS4B,EACTpE,MACO,OAALA,QAAK,IAALA,EAAAA,GACE3B,EAAAA,EAAAA,GAACkI,EAAAA,EAAgB,CAAA1J,GAAA,SACfsD,eAAe,kBAKrBqG,SAAU/B,EACVgC,QACEpG,EAAAA,EAAAA,IAACqG,EAAAA,EAA2B,CAAA3I,SAAA,EAC1BM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAY,uEACZO,gCAA8B,EAC9BC,QAASpC,EAKTnE,KAAGE,EAAAA,EAAAA,IAAE,CAAEC,YAAcqF,EAAkC,EAAnBnI,EAAM+C,QAAQC,IAAQ,IAAC5C,SAE1Dc,EAAKqB,cAAc,CAAArD,GAAA,SAClBsD,eAAe,aAIlB+F,GACC7H,EAAAA,EAAAA,GAACyI,EAAwB,CAACnB,WAAYA,EAAYE,UAAWA,EAAWkB,WAAYhC,KAEpF1G,EAAAA,EAAAA,GAAC0B,EAAAA,IAAa,CACZC,MACG8F,OAKG7F,EAJApB,EAAKqB,cAAc,CAAArD,GAAA,SACjBsD,eAAe,wDAItBpC,UAEDM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAY,uEACZO,gCAA8B,EAC9BjH,UAAWmG,EACXkB,QAASnB,EACToB,KAAK,UACLJ,QAAS9B,EAAShH,SAEjBc,EAAKqB,cAAc,CAAArD,GAAA,SAClBsD,eAAe,qBAO1BpC,SAAA,EAEDsC,EAAAA,EAAAA,IAAA,QACE6G,SAAU5C,EAAK6C,cA7EJD,KAEf,GAAItD,IAAkB+B,EAAWjG,MAAM0H,OACrC,OAIF,MAAMC,EAAa,IAAI9D,IAAIW,GAC3BmD,EAAWC,IAAI3B,EAAW9E,IAAK8E,GAE/BxB,EAAakD,GACb/C,EAAKQ,OAAO,IAmERxE,KAAGE,EAAAA,EAAAA,IAAE,CAAE+G,QAAS,OAAQC,WAAY,WAAYC,IAAK9J,EAAM+C,QAAQgH,IAAI,IAAC3J,SAAA,EAExEsC,EAAAA,EAAAA,IAAA,OAAKC,KAAGE,EAAAA,EAAAA,IAAE,CAAEmH,SAAU,EAAGJ,QAAS,OAAQE,IAAK9J,EAAM+C,QAAQgH,GAAIE,KAAM,GAAG,IAAC7J,SAAA,EACzEsC,EAAAA,EAAAA,IAAA,OAAKC,IAAGU,EAAcjD,SAAA,EACpBM,EAAAA,EAAAA,GAACwJ,EAAAA,IAAOC,MAAK,CAACC,QAAQ,MAAKhK,SACxBc,EAAKqB,cAAc,CAAArD,GAAA,SAClBsD,eAAe,WAInB9B,EAAAA,EAAAA,GAAC4C,EAAoB,CACnBtC,iBAAkBA,GAAoB,GACtC1D,QAASqJ,EAAKrJ,QACdkG,oBA1GiBN,IAA6B,IAADmH,EACvD,MAAM3N,EAAMwG,EAAMqD,EAAU+D,IAAIpH,QAAOZ,EAIvCqE,EAAK4D,SAAS,QAAmB,QAAZF,EAAK,OAAH3N,QAAG,IAAHA,OAAG,EAAHA,EAAKqF,aAAK,IAAAsI,EAAAA,EAAI,GAAG,QAwGlC3H,EAAAA,EAAAA,IAAA,OAAKC,IAAGkD,EAAczF,SAAA,EACpBM,EAAAA,EAAAA,GAACwJ,EAAAA,IAAOC,MAAK,CAACC,QAAQ,QAAOhK,SAC1B6F,EACG/E,EAAKqB,cAAc,CAAArD,GAAA,SACjBsD,eAAe,UAGjBtB,EAAKqB,cAAc,CAAArD,GAAA,SACjBsD,eAAe,wBAIvB9B,EAAAA,EAAAA,GAAC8J,EAAAA,IAAwBC,MAAK,CAC5B/B,YAAY,uEACZ7H,KAAK,QACLvD,QAASqJ,EAAKrJ,QACd,aACE2I,EACI/E,EAAKqB,cAAc,CAAArD,GAAA,SACjBsD,eAAe,UAGjBtB,EAAKqB,cAAc,CAAArD,GAAA,SACjBsD,eAAe,qBAIvB5E,YAAasD,EAAKqB,cAAc,CAAArD,GAAA,SAC9BsD,eAAe,0BAMvB9B,EAAAA,EAAAA,GAAC0B,EAAAA,IAAa,CACZC,MAAOnB,EAAKqB,cAAc,CAAArD,GAAA,SACxBsD,eAAe,YAEdpC,UAEHM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAY,uEACZgC,SAAS,SACT,aAAYxJ,EAAKqB,cAAc,CAAArD,GAAA,SAC7BsD,eAAe,YAEdpC,UAEHM,EAAAA,EAAAA,GAACkC,EAAAA,IAAQ,WAIduD,IAAgBzF,EAAAA,EAAAA,GAACwJ,EAAAA,IAAOS,QAAO,CAACrB,KAAK,QAAQnF,QAASgC,KACvDzF,EAAAA,EAAAA,GAAA,OACEiC,KAAGE,EAAAA,EAAAA,IAAE,CACH+G,QAAS,OACTgB,OAAQ5K,EAAM+C,QAAQ8H,GACtBC,SAAU,OACVC,UAAW/K,EAAM+C,QAAQC,IAC1B,IAAC5C,SAEDmH,MAAMC,KAAKjB,EAAUkB,UAAUjC,KAAK9I,IACnCgE,EAAAA,EAAAA,GAACsK,EAAAA,EAAW,CAACC,YAAU,EAACvO,IAAKA,EAAKwO,QAASA,IAnK3B3H,KAA8B,IAA7B,IAAEL,GAAqBK,EAC9CiD,GAAc2E,IACZA,EAAiBC,OAAOlI,GACjB,IAAI0C,IAAIuF,KACf,EA+JqDE,CAAgB3O,IAAWA,EAAIwG,YAMhE6D,oBAAmBmB,YAAW,EACtD,IAAAoD,EAAA,CAAAzK,KAAA,SAAAC,OAAA,mBAEF,SAASqI,EAAwBoC,GAQ7B,IAR8B,UAChCrD,EAAS,WACTF,EAAU,WACVoB,GAKDmC,EACC,MAAMrK,GAAOC,EAAAA,EAAAA,MACP,MAAEnB,IAAUoB,EAAAA,EAAAA,KAIZoK,EAAiB,GAFD,IAAGC,EAAAA,EAAAA,UAASzD,EAAW9E,IAAK,CAAEwI,OAAQ,MAAS,QAC7C1D,EAAWjG,MAAQ,KAAI0J,EAAAA,EAAAA,UAASzD,EAAWjG,MAAO,CAAE2J,OAAQ,OAAU,KAGxFC,EAAYzK,EAAKqB,cACrB,CAAArD,GAAA,SACEsD,eAAe,kEAGjB,CACE9F,IAAK8O,IAGT,OACE9I,EAAAA,EAAAA,IAACkJ,EAAAA,GAAQC,KAAI,CAACnD,YAAY,uEAAsEtI,SAAA,EAC9FM,EAAAA,EAAAA,GAACkL,EAAAA,GAAQE,QAAO,CAACC,SAAO,EAAA3L,UACtBM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAY,uEACZO,gCAA8B,EAC9BI,QAASnB,EACToB,KAAK,UAASlJ,SAEbc,EAAKqB,cAAc,CAAArD,GAAA,SAClBsD,eAAe,mBAKrBE,EAAAA,EAAAA,IAACkJ,EAAAA,GAAQI,QAAO,CAACC,MAAM,MAAM,aAAYN,EAAUvL,SAAA,EACjDM,EAAAA,EAAAA,GAACwL,EAAAA,EAAWC,UAAS,CAACxJ,IAAG2I,EAAoBlL,SAAEuL,KAC/CjL,EAAAA,EAAAA,GAACkL,EAAAA,GAAQQ,MAAK,CAACL,SAAO,EAAA3L,UACpBM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAY,uEACZQ,QAASE,EAAWhJ,SAEnBc,EAAKqB,cAAc,CAAArD,GAAA,SAClBsD,eAAe,6BAKrB9B,EAAAA,EAAAA,GAACkL,EAAAA,GAAQQ,MAAK,CAACL,SAAO,EAAA3L,UACpBM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAY,uEACZY,KAAK,UACL3G,KAAGE,EAAAA,EAAAA,IAAE,CAAEwJ,WAAYrM,EAAM+C,QAAQC,IAAI,IAAC5C,SAErCc,EAAKqB,cAAc,CAAArD,GAAA,SAClBsD,eAAe,gBAKrB9B,EAAAA,EAAAA,GAACkL,EAAAA,GAAQU,MAAK,SAItB,C,mHCjWoF,IAAA/I,EAAA,CAAA1C,KAAA,UAAAC,OAAA,aAQ7E,MAAMyL,EAAaxM,IAAmF,IAAlF,SAAEyM,EAAQ,UAAEC,GAAY,EAAI,YAAE/D,KAAgBgE,GAA8B3M,EACrG,MAAO4M,EAAaC,IAAkBjJ,EAAAA,EAAAA,WAAS,GAc/C,OACEjD,EAAAA,EAAAA,GAAC0B,EAAAA,IAAa,CACZC,OACE3B,EAAAA,EAAAA,GAACkI,EAAAA,EAAgB,CAAA1J,GAAA,SAACsD,eAAe,WAEnC+B,wBAAyB,CACvBM,QAAS8H,GACTvM,UAEFM,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLN,YAAwB,OAAXA,QAAW,IAAXA,EAAAA,EAAe,4BAC5BY,KAAK,UACLJ,QAxBc2D,KAClBC,UAAUC,UAAUC,UAAUR,GAC9BI,GAAe,GACfK,YAAW,KACTL,GAAe,EAAM,GACpB,IAAK,EAoBJM,aAjBmBC,KACvBP,GAAe,EAAM,EAiBjBjK,IAAGY,EAEHnD,SACEqM,GAAY/L,EAAAA,EAAAA,GAACkI,EAAAA,EAAgB,CAAA1J,GAAA,SAACsD,eAAe,cAAsDF,KAEjGoK,KAEQ,C,oJC5CpB,MAAM,UAAEP,GAAcD,EAAAA,EAC4D,IAAAnM,EAAA,CAAAc,KAAA,QAAAC,OAAA,gBAAAyC,EAAA,CAAA1C,KAAA,UAAAC,OAAA,eAS3E,MAAMsM,EAA2BxL,EAAAA,MAAYN,IAClD,MAAM,MAAEtB,IAAUoB,EAAAA,EAAAA,KAElB,OACEV,EAAAA,EAAAA,GAAC+H,EAAAA,EAAK,CACJC,YAAY,2EACZrG,MAAO,QAAUf,EAAM2B,OACvB4B,QAASvD,EAAM+L,kCACfxE,SAAUA,IAAMvH,EAAMgM,sCAAqC,GAAOlN,UAElEsC,EAAAA,EAAAA,IAAA,OAAKC,IAAG5C,EAAsBK,SAAA,EAC5BM,EAAAA,EAAAA,GAACyL,EAAS,CAACxJ,IAAGY,EAAkBnD,UAC9BM,EAAAA,EAAAA,GAAA,OACEiC,KAAGE,EAAAA,EAAAA,IAAE,CACH0K,gBAAiBvN,EAAMiC,OAAOuL,kBAC9BzC,UAAW/K,EAAM+C,QAAQC,GACzB9H,WAAY,WACZE,UAAW,aACZ,IAACgF,SAEDkB,EAAMmM,cAGX/M,EAAAA,EAAAA,GAAA,OACEiC,KAAGE,EAAAA,EAAAA,IAAE,CACHkI,UAAW/K,EAAM+C,QAAQC,IAC1B,IAAC5C,UAEFM,EAAAA,EAAAA,GAAC6L,EAAAA,EAAU,CAACC,SAAUlL,EAAMmM,SAAUhB,WAAW,EAAOiB,MAAMhN,EAAAA,EAAAA,GAACiN,EAAAA,IAAQ,IAAK,aAAW,eAGrF,IC/BNC,EAA2B,GAEjC,SAASC,IACP,QADwCC,UAAApC,OAAA,QAAApJ,IAAAwL,UAAA,KAAAA,UAAA,GAEpC,CACE9R,SAAU,SACV+R,aAAc,WACdC,SAAU,SACV9S,WAAY,UAEd,CAAEA,WAAY,SACpB,CAKO,MAAM8P,EAAcjL,IAgBpB,IAhBqB,WAC1BkL,GAAa,EAAK,QAClBC,EAAO,IACPxO,EAAG,oBACHuR,GAAsB,EAAK,UAC3BC,EAAYN,EAAwB,SACpCO,EAAW,IAAG,UACdC,GASDrO,EACC,MAAMmB,GAAOC,EAAAA,EAAAA,MAENkM,EAAmCC,IAAwC3J,EAAAA,EAAAA,WAAS,IAErF,kBAAE0K,EAAiB,oBAAEC,GA+CtB,SACL5R,GAE+D,IAD/DwR,EAASJ,UAAApC,OAAA,QAAApJ,IAAAwL,UAAA,GAAAA,UAAA,GAAGF,EAEZ,MAAM,IAAE1K,EAAG,MAAEnB,GAAUrF,EACjB6R,EAAarL,EAAIwI,OAAS3J,EAAM2J,OAChC8C,EAActL,EAAIwI,OAAS3J,EAAM2J,OACjC+C,EAAgBD,EAAczM,EAAM2J,OAASxI,EAAIwI,OAGvD,OAAI6C,GAAcL,EAAkB,CAAEG,mBAAmB,EAAOC,qBAAqB,GAEjFG,EAAgBP,EAAY,EAAU,CAAEG,mBAAmB,EAAMC,qBAAqB,GAGnF,CACLD,kBAAmBG,EACnBF,qBAAsBE,EAE1B,CAlEqDE,CAAgChS,EAAKwR,GAClFS,EAAqBV,IAAwBI,GAAqBC,GAElEM,EAAqB1N,EAAKqB,cAAc,CAAArD,GAAA,SAC5CsD,eAAe,sBAIjB,OACEE,EAAAA,EAAAA,IAAA,OAAAtC,SAAA,EACEM,EAAAA,EAAAA,GAACmO,EAAAA,IAAG,CACFnG,YAAY,8DACZoG,SAAU7D,EACVC,QAASA,EACT7I,MAAO3F,EAAIwG,IACXkL,UAAWA,EAAUhO,UAErBM,EAAAA,EAAAA,GAAC0B,EAAAA,IAAa,CAACC,MAAOsM,EAAqBC,EAAqB,GAAGxO,UACjEsC,EAAAA,EAAAA,IAAA,QACEC,KAAGE,EAAAA,EAAAA,IAAE,CAAEsL,WAAUvE,QAAS,eAAe,IACzCV,QAASA,IAAOyF,EAAqBrB,GAAqC,QAAQhL,EAAWlC,SAAA,EAE7FM,EAAAA,EAAAA,GAACwL,EAAAA,EAAW6C,KAAI,CAACzQ,MAAI,EAAC+D,MAAO3F,EAAIwG,IAAKP,IAAKkL,EAAmBQ,GAAmBjO,SAC9E1D,EAAIwG,MAENxG,EAAIqF,QACHW,EAAAA,EAAAA,IAACwJ,EAAAA,EAAW6C,KAAI,CAAC1M,MAAO3F,EAAIqF,MAAOY,IAAKkL,EAAmBS,GAAqBlO,SAAA,CAAC,KAC5E1D,EAAIqF,iBAMjBrB,EAAAA,EAAAA,GAAA,OAAAN,SACGiN,IACC3M,EAAAA,EAAAA,GAAC0M,EAAwB,CACvBnK,OAAQvG,EAAIwG,IACZuK,SAAU/Q,EAAIqF,MACdsL,kCAAmCA,EACnCC,qCAAsCA,QAIxC,C", "sources": ["shared/web-shared/snippet/theme/databricks-duotone-dark.ts", "shared/web-shared/snippet/theme/databricks-light.ts", "shared/web-shared/snippet/index.tsx", "common/components/TagSelectDropdown.tsx", "common/hooks/useEditKeyValueTagsModal.tsx", "shared/building_blocks/CopyButton.tsx", "common/components/KeyValueTagFullViewModal.tsx", "common/components/KeyValueTag.tsx"], "sourcesContent": ["/**\n * Adapted from `duotone-dark`\n * Ref: https://github.com/react-syntax-highlighter/react-syntax-highlighter/blob/b2457268891948f7005ccf539a70c000f0695bde/src/styles/prism/duotone-dark.js\n */\n\nconst databricksDuotoneDarkTheme = {\n  'code[class*=\"language-\"]': {\n    fontFamily:\n      'Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace',\n    fontSize: '14px',\n    lineHeight: '1.375',\n    direction: 'ltr',\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n    background: '#2a2734',\n    color: '#5DFAFC', // D\n  },\n  'pre[class*=\"language-\"]': {\n    fontFamily:\n      'Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace',\n    fontSize: '14px',\n    lineHeight: '1.375',\n    direction: 'ltr',\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n    background: '#2a2734',\n    color: '#5DFAFC', // D\n    padding: '1em',\n    margin: '.5em 0',\n    overflow: 'auto',\n  },\n  'pre > code[class*=\"language-\"]': {\n    fontSize: '1em',\n  },\n  'pre[class*=\"language-\"]::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'pre[class*=\"language-\"] ::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"]::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"] ::-moz-selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'pre[class*=\"language-\"]::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'pre[class*=\"language-\"] ::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"]::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  'code[class*=\"language-\"] ::selection': {\n    textShadow: 'none',\n    background: '#6a51e6',\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    padding: '.1em',\n    borderRadius: '.3em',\n  },\n  comment: {\n    color: '#6c6783',\n  },\n  prolog: {\n    color: '#6c6783',\n  },\n  doctype: {\n    color: '#6c6783',\n  },\n  cdata: {\n    color: '#6c6783',\n  },\n  punctuation: {\n    color: '#6c6783',\n  },\n  namespace: {\n    Opacity: '.7',\n  },\n  tag: {\n    color: '#3AACE2', // D\n  },\n  operator: {\n    color: '#3AACE2', // D\n  },\n  number: {\n    color: '#3AACE2', // D\n  },\n  property: {\n    color: '#5DFAFC', // D\n  },\n  function: {\n    color: '#5DFAFC', // D\n  },\n  'tag-id': {\n    color: '#eeebff',\n  },\n  selector: {\n    color: '#eeebff',\n  },\n  'atrule-id': {\n    color: '#eeebff',\n  },\n  'code.language-javascript': {\n    color: '#c4b9fe',\n  },\n  'attr-name': {\n    color: '#c4b9fe',\n  },\n  'code.language-css': {\n    color: '#ffffff', // D\n  },\n  'code.language-scss': {\n    color: '#ffffff', // D\n  },\n  boolean: {\n    color: '#ffffff', // D\n  },\n  string: {\n    color: '#ffffff', // D\n  },\n  entity: {\n    color: '#ffffff', // D\n    cursor: 'help',\n  },\n  url: {\n    color: '#ffffff', // D\n  },\n  '.language-css .token.string': {\n    color: '#ffffff', // D\n  },\n  '.language-scss .token.string': {\n    color: '#ffffff', // D\n  },\n  '.style .token.string': {\n    color: '#ffffff', // D\n  },\n  'attr-value': {\n    color: '#ffffff', // D\n  },\n  keyword: {\n    color: '#ffffff', // D\n  },\n  control: {\n    color: '#ffffff', // D\n  },\n  directive: {\n    color: '#ffffff', // D\n  },\n  unit: {\n    color: '#ffffff', // D\n  },\n  statement: {\n    color: '#ffffff', // D\n  },\n  regex: {\n    color: '#ffffff', // D\n  },\n  atrule: {\n    color: '#ffffff', // D\n  },\n  placeholder: {\n    color: '#ffffff', // D\n  },\n  variable: {\n    color: '#ffffff', // D\n  },\n  deleted: {\n    textDecoration: 'line-through',\n  },\n  inserted: {\n    borderBottom: '1px dotted #eeebff',\n    textDecoration: 'none',\n  },\n  italic: {\n    fontStyle: 'italic',\n  },\n  important: {\n    fontWeight: 'bold',\n    color: '#c4b9fe',\n  },\n  bold: {\n    fontWeight: 'bold',\n  },\n  'pre > code.highlight': {\n    Outline: '.4em solid #8a75f5',\n    OutlineOffset: '.4em',\n  },\n  '.line-numbers.line-numbers .line-numbers-rows': {\n    borderRightColor: '#2c2937',\n  },\n  '.line-numbers .line-numbers-rows > span:before': {\n    color: '#3c3949',\n  },\n  '.line-highlight.line-highlight': {\n    background: 'linear-gradient(to right, rgba(224, 145, 66, 0.2) 70%, rgba(224, 145, 66, 0))',\n  },\n};\n\nexport default databricksDuotoneDarkTheme;\n", "/**\n * Adapted from `material-light`\n * Ref: https://github.com/react-syntax-highlighter/react-syntax-highlighter/blob/b2457268891948f7005ccf539a70c000f0695bde/src/styles/prism/material-light.js#L1\n *\n * This theme overwrites colors to be similiar to the `@databricks/editor` theme.\n */\n\nconst databricksLightTheme = {\n  'code[class*=\"language-\"]': {\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    wordWrap: 'normal',\n    color: 'rgb(77, 77, 76)', // D\n    background: '#fafafa',\n    fontFamily: 'Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace',\n    fontSize: '12px', // D\n    lineHeight: '1.5em',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n  },\n  'pre[class*=\"language-\"]': {\n    textAlign: 'left',\n    whiteSpace: 'pre',\n    wordSpacing: 'normal',\n    wordBreak: 'normal',\n    wordWrap: 'normal',\n    color: 'rgb(77, 77, 76)', // D\n    background: '#fafafa',\n    fontFamily: 'Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace',\n    fontSize: '12px', // D\n    lineHeight: '1.5em',\n    MozTabSize: '4',\n    OTabSize: '4',\n    tabSize: '4',\n    WebkitHyphens: 'none',\n    MozHyphens: 'none',\n    msHyphens: 'none',\n    hyphens: 'none',\n    overflow: 'auto',\n    position: 'relative',\n    margin: '0.5em 0',\n    padding: '1.25em 1em',\n  },\n  'code[class*=\"language-\"]::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"]::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'code[class*=\"language-\"] ::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"] ::-moz-selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'code[class*=\"language-\"]::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"]::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'code[class*=\"language-\"] ::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  'pre[class*=\"language-\"] ::selection': {\n    background: '#cceae7',\n    color: '#263238',\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    whiteSpace: 'normal',\n    borderRadius: '0.2em',\n    padding: '0.1em',\n  },\n  '.language-css > code': {\n    color: '#f5871f', // D\n  },\n  '.language-sass > code': {\n    color: '#f5871f', // D\n  },\n  '.language-scss > code': {\n    color: '#f5871f', // D\n  },\n  '[class*=\"language-\"] .namespace': {\n    Opacity: '0.7',\n  },\n  atrule: {\n    color: '#7c4dff',\n  },\n  'attr-name': {\n    color: '#39adb5',\n  },\n  'attr-value': {\n    color: '#f6a434',\n  },\n  attribute: {\n    color: '#f6a434',\n  },\n  boolean: {\n    color: '#7c4dff', // D\n  },\n  builtin: {\n    color: '#39adb5',\n  },\n  cdata: {\n    color: '#39adb5',\n  },\n  char: {\n    color: '#39adb5',\n  },\n  class: {\n    color: '#39adb5',\n  },\n  'class-name': {\n    color: '#6182b8',\n  },\n  comment: {\n    color: '#8e908c', // D\n  },\n  constant: {\n    color: '#7c4dff', // D\n  },\n  deleted: {\n    color: '#e53935',\n  },\n  doctype: {\n    color: '#aabfc9',\n  },\n  entity: {\n    color: '#e53935',\n  },\n  function: {\n    color: '#4271ae', // D\n  },\n  hexcode: {\n    color: '#f5871f', // D\n  },\n  id: {\n    color: '#7c4dff',\n    fontWeight: 'bold',\n  },\n  important: {\n    color: '#7c4dff',\n    fontWeight: 'bold',\n  },\n  inserted: {\n    color: '#39adb5',\n  },\n  keyword: {\n    color: '#8959a8', // D\n  },\n  number: {\n    color: '#f5871f', // D\n  },\n  operator: {\n    color: '#3e999f', // D\n  },\n  prolog: {\n    color: '#aabfc9',\n  },\n  property: {\n    color: '#39adb5',\n  },\n  'pseudo-class': {\n    color: '#f6a434',\n  },\n  'pseudo-element': {\n    color: '#f6a434',\n  },\n  punctuation: {\n    color: 'rgb(77, 77, 76)', // D\n  },\n  regex: {\n    color: '#6182b8',\n  },\n  selector: {\n    color: '#e53935',\n  },\n  string: {\n    color: '#3ba85f', // D\n  },\n  symbol: {\n    color: '#7c4dff',\n  },\n  tag: {\n    color: '#e53935',\n  },\n  unit: {\n    color: '#f5871f', // D\n  },\n  url: {\n    color: '#e53935',\n  },\n  variable: {\n    color: '#c72d4c', // D\n  },\n};\n\nexport default databricksLightTheme;\n", "import { PrismLight as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';\nimport python from 'react-syntax-highlighter/dist/cjs/languages/prism/python';\nimport json from 'react-syntax-highlighter/dist/cjs/languages/prism/json';\n\nSyntaxHighlighter.registerLanguage('python', python);\nSyntaxHighlighter.registerLanguage('json', json);\n\nimport duotoneDarkStyle from './theme/databricks-duotone-dark';\nimport lightStyle from './theme/databricks-light';\nimport { CSSProperties, ReactNode } from 'react';\nimport { pick } from 'lodash';\n\nexport type CodeSnippetTheme = 'duotoneDark' | 'light';\n\nexport const buttonBackgroundColorDark = 'rgba(140, 203, 255, 0)';\nexport const buttonColorDark = 'rgba(255, 255, 255, 0.84)';\nexport const buttonHoverColorDark = '#8ccbffcc';\nexport const buttonHoverBackgroundColorDark = 'rgba(140, 203, 255, 0.08)';\nexport const duboisAlertBackgroundColor = '#fff0f0';\nexport const snippetPadding = '24px';\n\nconst themesStyles: Record<CodeSnippetTheme, any> = {\n  light: lightStyle,\n  duotoneDark: duotoneDarkStyle,\n};\n\nexport type CodeSnippetLanguage = 'python' | 'json' | 'text';\n\nexport interface CodeSnippetProps {\n  /**\n   * The code string\n   */\n  children: string;\n  /**\n   * The actions that are displayed on the right top corner of the component\n   *  see `./actions` for built-in actions\n   */\n  actions?: NonNullable<ReactNode> | NonNullable<ReactNode>[];\n  /**\n   * The theme, default theme is `light`\n   */\n  theme?: CodeSnippetTheme;\n  /**\n   * Language of the code (`children`)\n   */\n  language: CodeSnippetLanguage;\n  /**\n   * Custom styles (passed to the internal `<pre>`)\n   */\n  style?: CSSProperties;\n  /**\n   * Whether to show line numbers on the left or not\n   */\n  showLineNumbers?: boolean;\n  /**\n   * Custom styles for line numbers\n   */\n  lineNumberStyle?: CSSProperties;\n  /**\n   * Whether or not to wrap long lines\n   */\n  wrapLongLines?: boolean;\n}\n\n/**\n * `CodeSnippet` is used for highlighting code, use this instead of\n */\nexport function CodeSnippet({\n  theme = 'light',\n  language,\n  actions,\n  style,\n  children,\n  showLineNumbers,\n  lineNumberStyle,\n  wrapLongLines,\n}: CodeSnippetProps) {\n  const customStyle = {\n    border: 'none',\n    borderRadius: 0,\n    margin: 0,\n    padding: snippetPadding,\n    ...style,\n  };\n\n  return (\n    <SyntaxHighlighter\n      showLineNumbers={showLineNumbers}\n      lineNumberStyle={lineNumberStyle}\n      language={language}\n      style={themesStyles[theme]}\n      customStyle={customStyle}\n      codeTagProps={{\n        style: pick(style, 'backgroundColor'),\n      }}\n      wrapLongLines={wrapLongLines}\n    >\n      {children}\n    </SyntaxHighlighter>\n  );\n}\n", "import { sortedIndexOf } from 'lodash';\nimport React, { useMemo, useRef, useState } from 'react';\nimport { Control, useController } from 'react-hook-form';\nimport { useIntl } from 'react-intl';\n\nimport { PlusIcon, LegacySelect, LegacyTooltip, useDesignSystemTheme } from '@databricks/design-system';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\n\n/**\n * Will show an extra row at the bottom of the dropdown menu to create a new tag when\n * The user has typed something in the search input\n * and either\n * 1. The search input is not an exact match for an existing tag name\n * 2. There are no tags available based on search input\n */\n\nfunction DropdownMenu(menu: React.ReactElement, allAvailableTags: string[]) {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n  const searchValue = menu.props.searchValue.toLowerCase();\n\n  const resolvedMenu = useMemo(() => {\n    if (!searchValue) return menu;\n\n    const doesTagExists = sortedIndexOf(allAvailableTags, searchValue) >= 0;\n    if (doesTagExists) return menu;\n\n    const isValidTagKey = /^[^,.:/=\\-\\s]+$/.test(searchValue);\n\n    // Overriding the menu to add a new option at the top\n    return React.cloneElement(menu, {\n      flattenOptions: [\n        {\n          data: {\n            value: searchValue,\n            disabled: !isValidTagKey,\n            style: {\n              color: isValidTagKey ? theme.colors.actionTertiaryTextDefault : theme.colors.actionDisabledText,\n            },\n            children: (\n              <LegacyTooltip\n                title={\n                  isValidTagKey\n                    ? undefined\n                    : intl.formatMessage({\n                        defaultMessage: ', . : / - = and blank spaces are not allowed',\n                        description:\n                          'Key-value tag editor modal > Tag dropdown Manage Modal > Invalid characters error',\n                      })\n                }\n                placement=\"right\"\n              >\n                <span css={{ display: 'block' }}>\n                  <PlusIcon css={{ marginRight: theme.spacing.sm }} />\n                  {intl.formatMessage(\n                    {\n                      defaultMessage: 'Add tag \"{tagKey}\"',\n                      description: 'Key-value tag editor modal > Tag dropdown Manage Modal > Add new tag button',\n                    },\n                    {\n                      tagKey: searchValue,\n                    },\n                  )}\n                </span>\n              </LegacyTooltip>\n            ),\n          },\n          key: searchValue,\n          groupOption: false,\n        },\n        ...menu.props.flattenOptions,\n      ],\n    });\n  }, [allAvailableTags, menu, searchValue, intl, theme]);\n\n  return resolvedMenu;\n}\n\nfunction getDropdownMenu(allAvailableTags: string[]) {\n  return (menu: React.ReactElement) => DropdownMenu(menu, allAvailableTags);\n}\n\n/**\n * Used in tag edit feature, allows selecting existing / adding new tag value\n */\nexport function TagKeySelectDropdown({\n  allAvailableTags,\n  control,\n  onKeyChangeCallback,\n}: {\n  allAvailableTags: string[];\n  control: Control<KeyValueEntity>;\n  onKeyChangeCallback?: (key?: string) => void;\n}) {\n  const intl = useIntl();\n  const [isOpen, setIsOpen] = useState(false);\n  const selectRef = useRef<{ blur: () => void; focus: () => void }>(null);\n\n  const { field, fieldState } = useController({\n    control: control,\n    name: 'key',\n    rules: {\n      required: {\n        message: intl.formatMessage({\n          defaultMessage: 'A tag key is required',\n          description: 'Key-value tag editor modal > Tag dropdown > Tag key required error message',\n        }),\n        value: true,\n      },\n    },\n  });\n\n  const handleDropdownVisibleChange = (visible: boolean) => {\n    setIsOpen(visible);\n  };\n\n  const handleClear = () => {\n    field.onChange(undefined);\n    onKeyChangeCallback?.(undefined);\n  };\n\n  const handleSelect = (key: string) => {\n    field.onChange(key);\n    onKeyChangeCallback?.(key);\n  };\n\n  return (\n    <LegacySelect\n      allowClear\n      ref={selectRef}\n      dangerouslySetAntdProps={{\n        showSearch: true,\n        dropdownRender: getDropdownMenu(allAvailableTags),\n      }}\n      css={{ width: '100%' }}\n      placeholder={intl.formatMessage({\n        defaultMessage: 'Type a key',\n        description: 'Key-value tag editor modal > Tag dropdown > Tag input placeholder',\n      })}\n      value={field.value}\n      defaultValue={field.value}\n      open={isOpen}\n      onDropdownVisibleChange={handleDropdownVisibleChange}\n      filterOption={(input, option) => option?.value.toLowerCase().includes(input.toLowerCase())}\n      onSelect={handleSelect}\n      onClear={handleClear}\n      validationState={fieldState.error ? 'error' : undefined}\n    >\n      {allAvailableTags.map((tag) => (\n        <LegacySelect.Option value={tag} key={tag}>\n          {tag}\n        </LegacySelect.Option>\n      ))}\n    </LegacySelect>\n  );\n}\n", "import { isEqual, sortBy } from 'lodash';\nimport { useCallback, useMemo, useRef, useState } from 'react';\nimport { truncate } from 'lodash';\n\nimport {\n  Button,\n  FormUI,\n  Modal,\n  PlusIcon,\n  Popover,\n  RHFControlledComponents,\n  RestoreAntDDefaultClsPrefix,\n  LegacyTooltip,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { Typography } from '@databricks/design-system';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { useForm } from 'react-hook-form';\nimport { TagKeySelectDropdown } from '../components/TagSelectDropdown';\nimport { KeyValueTag } from '../components/KeyValueTag';\nimport { ErrorWrapper } from '../utils/ErrorWrapper';\n\nfunction getTagsMap(tags: KeyValueEntity[]) {\n  return new Map(tags.map((tag) => [tag.key, tag]));\n}\n\n/**\n * Provides methods to initialize and display modal used to add and remove tags from any compatible entity\n */\nexport const useEditKeyValueTagsModal = <T extends { tags?: KeyValueEntity[] }>({\n  onSuccess,\n  saveTagsHandler,\n  allAvailableTags,\n  valueRequired = false,\n  title,\n}: {\n  onSuccess?: () => void;\n  saveTagsHandler: (editedEntity: T, existingTags: KeyValueEntity[], newTags: KeyValueEntity[]) => Promise<any>;\n  allAvailableTags?: string[];\n  valueRequired?: boolean;\n  title?: React.ReactNode;\n}) => {\n  const editedEntityRef = useRef<T>();\n  const [errorMessage, setErrorMessage] = useState<string>('');\n  const { theme } = useDesignSystemTheme();\n\n  const [initialTags, setInitialTags] = useState<Map<string, KeyValueEntity>>(new Map());\n  const [finalTags, setFinalTags] = useState<Map<string, KeyValueEntity>>(new Map());\n\n  const [showModal, setShowModal] = useState(false);\n\n  const form = useForm<KeyValueEntity>({\n    defaultValues: {\n      key: undefined,\n      value: '',\n    },\n  });\n\n  const hideModal = () => setShowModal(false);\n\n  /**\n   * Function used to invoke the modal and start editing tags of the particular model version\n   */\n  const showEditTagsModal = useCallback(\n    (editedEntity: T) => {\n      editedEntityRef.current = editedEntity;\n      setInitialTags(getTagsMap(editedEntity.tags || []));\n      setFinalTags(getTagsMap(editedEntity.tags || []));\n      form.reset();\n\n      setShowModal(true);\n    },\n    [form],\n  );\n\n  const saveTags = async () => {\n    if (!editedEntityRef.current) {\n      return;\n    }\n    setErrorMessage('');\n    setIsLoading(true);\n    saveTagsHandler(editedEntityRef.current, Array.from(initialTags.values()), Array.from(finalTags.values()))\n      .then(() => {\n        hideModal();\n        onSuccess?.();\n        setIsLoading(false);\n      })\n      .catch((e: ErrorWrapper | Error) => {\n        setIsLoading(false);\n        setErrorMessage(e instanceof ErrorWrapper ? e.getUserVisibleError()?.message : e.message);\n      });\n  };\n\n  const intl = useIntl();\n  const formValues = form.watch();\n\n  const [isLoading, setIsLoading] = useState(false);\n\n  const hasNewValues = useMemo(\n    () => !isEqual(sortBy(Array.from(initialTags.values()), 'key'), sortBy(Array.from(finalTags.values()), 'key')),\n    [initialTags, finalTags],\n  );\n  const isDirty = formValues.key || formValues.value;\n  const showPopoverMessage = hasNewValues && isDirty;\n\n  const onKeyChangeCallback = (key: string | undefined) => {\n    const tag = key ? finalTags.get(key) : undefined;\n    /**\n     * If a tag value exists for provided key, set the value to the existing tag value\n     */\n    form.setValue('value', tag?.value ?? '');\n  };\n\n  const handleTagDelete = ({ key }: KeyValueEntity) => {\n    setFinalTags((currentFinalTags) => {\n      currentFinalTags.delete(key);\n      return new Map(currentFinalTags);\n    });\n  };\n\n  const onSubmit = () => {\n    // Do not accept form if no value provided while it's required\n    if (valueRequired && !formValues.value.trim()) {\n      return;\n    }\n\n    // Add new tag to existing tags leaving only one tag per key value\n    const newEntries = new Map(finalTags);\n    newEntries.set(formValues.key, formValues);\n\n    setFinalTags(newEntries);\n    form.reset();\n  };\n\n  const EditTagsModal = (\n    <Modal\n      componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_135\"\n      destroyOnClose\n      visible={showModal}\n      title={\n        title ?? (\n          <FormattedMessage\n            defaultMessage=\"Add/Edit tags\"\n            description=\"Key-value tag editor modal > Title of the update tags modal\"\n          />\n        )\n      }\n      onCancel={hideModal}\n      footer={\n        <RestoreAntDDefaultClsPrefix>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_147\"\n            dangerouslyUseFocusPseudoClass\n            onClick={hideModal}\n            /**\n             * Hack: The footer will remove the margin to the save tags button\n             * if the button if wrapped on another component.\n             */\n            css={{ marginRight: !hasNewValues ? theme.spacing.sm : 0 }}\n          >\n            {intl.formatMessage({\n              defaultMessage: 'Cancel',\n              description: 'Key-value tag editor modal > Manage Tag cancel button',\n            })}\n          </Button>\n          {showPopoverMessage ? (\n            <UnsavedTagPopoverTrigger formValues={formValues} isLoading={isLoading} onSaveTask={saveTags} />\n          ) : (\n            <LegacyTooltip\n              title={\n                !hasNewValues\n                  ? intl.formatMessage({\n                      defaultMessage: 'Please add or remove one or more tags before saving',\n                      description: 'Key-value tag editor modal > Tag disabled message',\n                    })\n                  : undefined\n              }\n            >\n              <Button\n                componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_174\"\n                dangerouslyUseFocusPseudoClass\n                disabled={!hasNewValues}\n                loading={isLoading}\n                type=\"primary\"\n                onClick={saveTags}\n              >\n                {intl.formatMessage({\n                  defaultMessage: 'Save tags',\n                  description: 'Key-value tag editor modal > Manage Tag save button',\n                })}\n              </Button>\n            </LegacyTooltip>\n          )}\n        </RestoreAntDDefaultClsPrefix>\n      }\n    >\n      <form\n        onSubmit={form.handleSubmit(onSubmit)}\n        css={{ display: 'flex', alignItems: 'flex-end', gap: theme.spacing.md }}\n      >\n        <div css={{ minWidth: 0, display: 'flex', gap: theme.spacing.md, flex: 1 }}>\n          <div css={{ flex: 1 }}>\n            <FormUI.Label htmlFor=\"key\">\n              {intl.formatMessage({\n                defaultMessage: 'Key',\n                description: 'Key-value tag editor modal > Key input label',\n              })}\n            </FormUI.Label>\n            <TagKeySelectDropdown\n              allAvailableTags={allAvailableTags || []}\n              control={form.control}\n              onKeyChangeCallback={onKeyChangeCallback}\n            />\n          </div>\n          <div css={{ flex: 1 }}>\n            <FormUI.Label htmlFor=\"value\">\n              {valueRequired\n                ? intl.formatMessage({\n                    defaultMessage: 'Value',\n                    description: 'Key-value tag editor modal > Value input label (required)',\n                  })\n                : intl.formatMessage({\n                    defaultMessage: 'Value (optional)',\n                    description: 'Key-value tag editor modal > Value input label',\n                  })}\n            </FormUI.Label>\n            <RHFControlledComponents.Input\n              componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_223\"\n              name=\"value\"\n              control={form.control}\n              aria-label={\n                valueRequired\n                  ? intl.formatMessage({\n                      defaultMessage: 'Value',\n                      description: 'Key-value tag editor modal > Value input label (required)',\n                    })\n                  : intl.formatMessage({\n                      defaultMessage: 'Value (optional)',\n                      description: 'Key-value tag editor modal > Value input label',\n                    })\n              }\n              placeholder={intl.formatMessage({\n                defaultMessage: 'Type a value',\n                description: 'Key-value tag editor modal > Value input placeholder',\n              })}\n            />\n          </div>\n        </div>\n        <LegacyTooltip\n          title={intl.formatMessage({\n            defaultMessage: 'Add tag',\n            description: 'Key-value tag editor modal > Add tag button',\n          })}\n        >\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_248\"\n            htmlType=\"submit\"\n            aria-label={intl.formatMessage({\n              defaultMessage: 'Add tag',\n              description: 'Key-value tag editor modal > Add tag button',\n            })}\n          >\n            <PlusIcon />\n          </Button>\n        </LegacyTooltip>\n      </form>\n      {errorMessage && <FormUI.Message type=\"error\" message={errorMessage} />}\n      <div\n        css={{\n          display: 'flex',\n          rowGap: theme.spacing.xs,\n          flexWrap: 'wrap',\n          marginTop: theme.spacing.sm,\n        }}\n      >\n        {Array.from(finalTags.values()).map((tag) => (\n          <KeyValueTag isClosable tag={tag} onClose={() => handleTagDelete(tag)} key={tag.key} />\n        ))}\n      </div>\n    </Modal>\n  );\n\n  return { EditTagsModal, showEditTagsModal, isLoading };\n};\n\nfunction UnsavedTagPopoverTrigger({\n  isLoading,\n  formValues,\n  onSaveTask,\n}: {\n  isLoading: boolean;\n  formValues: any;\n  onSaveTask: () => void;\n}) {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n\n  const tagKeyDisplay = `${truncate(formValues.key, { length: 20 }) || '_'}`;\n  const tagValueDisplay = formValues.value ? `:${truncate(formValues.value, { length: 20 })}` : '';\n  const fullTagDisplay = `${tagKeyDisplay}${tagValueDisplay}`;\n\n  const shownText = intl.formatMessage(\n    {\n      defaultMessage: 'Are you sure you want to save and close without adding \"{tag}\"',\n      description: 'Key-value tag editor modal > Unsaved tag message',\n    },\n    {\n      tag: fullTagDisplay,\n    },\n  );\n  return (\n    <Popover.Root componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_309\">\n      <Popover.Trigger asChild>\n        <Button\n          componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_306\"\n          dangerouslyUseFocusPseudoClass\n          loading={isLoading}\n          type=\"primary\"\n        >\n          {intl.formatMessage({\n            defaultMessage: 'Save tags',\n            description: 'Key-value tag editor modal > Manage Tag save button',\n          })}\n        </Button>\n      </Popover.Trigger>\n      <Popover.Content align=\"end\" aria-label={shownText}>\n        <Typography.Paragraph css={{ maxWidth: 400 }}>{shownText}</Typography.Paragraph>\n        <Popover.Close asChild>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_316\"\n            onClick={onSaveTask}\n          >\n            {intl.formatMessage({\n              defaultMessage: 'Yes, save and close',\n              description: 'Key-value tag editor modal > Unsaved tag message > Yes, save and close button',\n            })}\n          </Button>\n        </Popover.Close>\n        <Popover.Close asChild>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_324\"\n            type=\"primary\"\n            css={{ marginLeft: theme.spacing.sm }}\n          >\n            {intl.formatMessage({\n              defaultMessage: 'Cancel',\n              description: 'Key-value tag editor modal > Unsaved tag message > cancel button',\n            })}\n          </Button>\n        </Popover.Close>\n        <Popover.Arrow />\n      </Popover.Content>\n    </Popover.Root>\n  );\n}\n", "import React, { useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { Button, type ButtonProps, LegacyTooltip } from '@databricks/design-system';\n\ninterface CopyButtonProps extends Partial<ButtonProps> {\n  copyText: string;\n  showLabel?: React.ReactNode;\n  componentId?: string;\n}\n\nexport const CopyButton = ({ copyText, showLabel = true, componentId, ...buttonProps }: CopyButtonProps) => {\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  const handleClick = () => {\n    navigator.clipboard.writeText(copyText);\n    setShowTooltip(true);\n    setTimeout(() => {\n      setShowTooltip(false);\n    }, 3000);\n  };\n\n  const handleMouseLeave = () => {\n    setShowTooltip(false);\n  };\n\n  return (\n    <LegacyTooltip\n      title={\n        <FormattedMessage defaultMessage=\"Copied\" description=\"Tooltip text shown when copy operation completes\" />\n      }\n      dangerouslySetAntdProps={{\n        visible: showTooltip,\n      }}\n    >\n      <Button\n        componentId={componentId ?? 'mlflow.shared.copy_button'}\n        type=\"primary\"\n        onClick={handleClick}\n        onMouseLeave={handleMouseLeave}\n        css={{ 'z-index': 1 }}\n        // Define children as a explicit prop so it can be easily overrideable\n        children={\n          showLabel ? <FormattedMessage defaultMessage=\"Copy\" description=\"Button text for copy button\" /> : undefined\n        }\n        {...buttonProps}\n      />\n    </LegacyTooltip>\n  );\n};\n", "import React from 'react';\nimport { Modal, Typography, CopyIcon, useDesignSystemTheme } from '@databricks/design-system';\nconst { Paragraph } = Typography;\nimport { CopyButton } from '@mlflow/mlflow/src/shared/building_blocks/CopyButton';\n\nexport interface KeyValueTagFullViewModalProps {\n  tagKey: string;\n  tagValue: string;\n  setIsKeyValueTagFullViewModalVisible: React.Dispatch<React.SetStateAction<boolean>>;\n  isKeyValueTagFullViewModalVisible: boolean;\n}\n\nexport const KeyValueTagFullViewModal = React.memo((props: KeyValueTagFullViewModalProps) => {\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <Modal\n      componentId=\"codegen_mlflow_app_src_common_components_keyvaluetagfullviewmodal.tsx_17\"\n      title={'Tag: ' + props.tagKey}\n      visible={props.isKeyValueTagFullViewModalVisible}\n      onCancel={() => props.setIsKeyValueTagFullViewModalVisible(false)}\n    >\n      <div css={{ display: 'flex' }}>\n        <Paragraph css={{ flexGrow: 1 }}>\n          <pre\n            css={{\n              backgroundColor: theme.colors.backgroundPrimary,\n              marginTop: theme.spacing.sm,\n              whiteSpace: 'pre-wrap',\n              wordBreak: 'break-all',\n            }}\n          >\n            {props.tagValue}\n          </pre>\n        </Paragraph>\n        <div\n          css={{\n            marginTop: theme.spacing.sm,\n          }}\n        >\n          <CopyButton copyText={props.tagValue} showLabel={false} icon={<CopyIcon />} aria-label=\"Copy\" />\n        </div>\n      </div>\n    </Modal>\n  );\n});\n", "import { Tag, LegacyTooltip, Typography } from '@databricks/design-system';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\nimport React, { useState } from 'react';\nimport { useIntl } from 'react-intl';\nimport { KeyValueTagFullViewModal } from './KeyValueTagFullViewModal';\nimport { Interpolation, Theme } from '@emotion/react';\n\n/**\n * An arbitrary number that is used to determine if a tag is too\n * long and should be truncated. We want to avoid short keys or values\n * in a long tag to be truncated\n * */\nconst TRUNCATE_ON_CHARS_LENGTH = 30;\n\nfunction getTruncatedStyles(shouldTruncate = true): Interpolation<Theme> {\n  return shouldTruncate\n    ? {\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        textWrap: 'nowrap',\n        whiteSpace: 'nowrap' as const,\n      }\n    : { whiteSpace: 'nowrap' as const };\n}\n\n/**\n * A <Tag /> wrapper used for displaying key-value entity\n */\nexport const KeyValueTag = ({\n  isClosable = false,\n  onClose,\n  tag,\n  enableFullViewModal = false,\n  charLimit = TRUNCATE_ON_CHARS_LENGTH,\n  maxWidth = 300,\n  className,\n}: {\n  isClosable?: boolean;\n  onClose?: () => void;\n  tag: KeyValueEntity;\n  enableFullViewModal?: boolean;\n  charLimit?: number;\n  maxWidth?: number;\n  className?: string;\n}) => {\n  const intl = useIntl();\n\n  const [isKeyValueTagFullViewModalVisible, setIsKeyValueTagFullViewModalVisible] = useState(false);\n\n  const { shouldTruncateKey, shouldTruncateValue } = getKeyAndValueComplexTruncation(tag, charLimit);\n  const allowFullViewModal = enableFullViewModal && (shouldTruncateKey || shouldTruncateValue);\n\n  const fullViewModalLabel = intl.formatMessage({\n    defaultMessage: 'Click to see more',\n    description: 'Run page > Overview > Tags cell > Tag',\n  });\n\n  return (\n    <div>\n      <Tag\n        componentId=\"codegen_mlflow_app_src_common_components_keyvaluetag.tsx_60\"\n        closable={isClosable}\n        onClose={onClose}\n        title={tag.key}\n        className={className}\n      >\n        <LegacyTooltip title={allowFullViewModal ? fullViewModalLabel : ''}>\n          <span\n            css={{ maxWidth, display: 'inline-flex' }}\n            onClick={() => (allowFullViewModal ? setIsKeyValueTagFullViewModalVisible(true) : undefined)}\n          >\n            <Typography.Text bold title={tag.key} css={getTruncatedStyles(shouldTruncateKey)}>\n              {tag.key}\n            </Typography.Text>\n            {tag.value && (\n              <Typography.Text title={tag.value} css={getTruncatedStyles(shouldTruncateValue)}>\n                : {tag.value}\n              </Typography.Text>\n            )}\n          </span>\n        </LegacyTooltip>\n      </Tag>\n      <div>\n        {isKeyValueTagFullViewModalVisible && (\n          <KeyValueTagFullViewModal\n            tagKey={tag.key}\n            tagValue={tag.value}\n            isKeyValueTagFullViewModalVisible={isKeyValueTagFullViewModalVisible}\n            setIsKeyValueTagFullViewModalVisible={setIsKeyValueTagFullViewModalVisible}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport function getKeyAndValueComplexTruncation(\n  tag: KeyValueEntity,\n  charLimit = TRUNCATE_ON_CHARS_LENGTH,\n): { shouldTruncateKey: boolean; shouldTruncateValue: boolean } {\n  const { key, value } = tag;\n  const fullLength = key.length + value.length;\n  const isKeyLonger = key.length > value.length;\n  const shorterLength = isKeyLonger ? value.length : key.length;\n\n  // No need to truncate if tag is short enough\n  if (fullLength <= charLimit) return { shouldTruncateKey: false, shouldTruncateValue: false };\n  // If the shorter string is too long, truncate both key and value.\n  if (shorterLength > charLimit / 2) return { shouldTruncateKey: true, shouldTruncateValue: true };\n\n  // Otherwise truncate the longer string\n  return {\n    shouldTruncateKey: isKeyLonger,\n    shouldTruncateValue: !isKeyLonger,\n  };\n}\n"], "names": ["fontFamily", "fontSize", "lineHeight", "direction", "textAlign", "whiteSpace", "wordSpacing", "wordBreak", "MozTabSize", "OTabSize", "tabSize", "WebkitHyphens", "MozHyphens", "msHyphens", "hyphens", "background", "color", "padding", "margin", "overflow", "textShadow", "borderRadius", "comment", "prolog", "doctype", "cdata", "punctuation", "namespace", "Opacity", "tag", "operator", "number", "property", "function", "selector", "boolean", "string", "entity", "cursor", "url", "keyword", "control", "directive", "unit", "statement", "regex", "at<PERSON>le", "placeholder", "variable", "deleted", "textDecoration", "inserted", "borderBottom", "italic", "fontStyle", "important", "fontWeight", "bold", "Outline", "OutlineOffset", "borderRightColor", "wordWrap", "position", "attribute", "builtin", "char", "class", "constant", "hexcode", "id", "symbol", "Syntax<PERSON><PERSON><PERSON><PERSON>", "registerLanguage", "python", "json", "snippetPadding", "themesStyles", "light", "lightStyle", "duotoneDark", "duotoneDarkStyle", "CodeSnippet", "_ref", "theme", "language", "actions", "style", "children", "showLineNumbers", "lineNumberStyle", "wrapLongLines", "customStyle", "border", "_jsx", "codeTagProps", "pick", "name", "styles", "getDropdownMenu", "allAvailableTags", "menu", "intl", "useIntl", "useDesignSystemTheme", "searchValue", "props", "toLowerCase", "useMemo", "sortedIndexOf", "isValidTag<PERSON>ey", "test", "React", "flattenOptions", "data", "value", "disabled", "colors", "actionTertiaryTextDefault", "actionDisabledText", "LegacyTooltip", "title", "undefined", "formatMessage", "defaultMessage", "placement", "_jsxs", "css", "PlusIcon", "_css", "marginRight", "spacing", "sm", "<PERSON><PERSON><PERSON>", "key", "groupOption", "DropdownMenu", "_ref3", "TagKeySelectDropdown", "_ref2", "onKeyChangeCallback", "isOpen", "setIsOpen", "useState", "selectRef", "useRef", "field", "fieldState", "useController", "rules", "required", "message", "LegacySelect", "allowClear", "ref", "dangerouslySetAntdProps", "showSearch", "dropdownRender", "defaultValue", "open", "onDropdownVisibleChange", "visible", "filterOption", "input", "option", "includes", "onSelect", "onChange", "onClear", "handleClear", "validationState", "error", "map", "Option", "getTagsMap", "tags", "Map", "_ref4", "useEditKeyValueTagsModal", "onSuccess", "saveTagsHandler", "valueRequired", "editedEntityRef", "errorMessage", "setErrorMessage", "initialTags", "setInitialTags", "finalTags", "setFinalTags", "showModal", "setShowModal", "form", "useForm", "defaultValues", "hideModal", "showEditTagsModal", "useCallback", "editedEntity", "current", "reset", "saveTags", "async", "setIsLoading", "Array", "from", "values", "then", "catch", "e", "_e$getUserVisibleErro", "ErrorWrapper", "getUserVisibleError", "formValues", "watch", "isLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEqual", "sortBy", "isDirty", "showPopoverMessage", "EditTagsModal", "Modal", "componentId", "destroyOnClose", "FormattedMessage", "onCancel", "footer", "RestoreAntDDefaultClsPrefix", "<PERSON><PERSON>", "dangerouslyUseFocusPseudoClass", "onClick", "UnsavedTagPopoverTrigger", "onSaveTask", "loading", "type", "onSubmit", "handleSubmit", "trim", "newEntries", "set", "display", "alignItems", "gap", "md", "min<PERSON><PERSON><PERSON>", "flex", "FormUI", "Label", "htmlFor", "_tag$value", "get", "setValue", "RHFControlledComponents", "Input", "htmlType", "Message", "rowGap", "xs", "flexWrap", "marginTop", "KeyValueTag", "isClosable", "onClose", "currentFinalTags", "delete", "handleTagDelete", "_ref6", "_ref5", "fullTagDisplay", "truncate", "length", "shownText", "Popover", "Root", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Content", "align", "Typography", "Paragraph", "Close", "marginLeft", "Arrow", "Copy<PERSON><PERSON><PERSON>", "copyText", "showLabel", "buttonProps", "showTooltip", "setShowTooltip", "handleClick", "navigator", "clipboard", "writeText", "setTimeout", "onMouseLeave", "handleMouseLeave", "KeyValueTagFullViewModal", "isKeyValueTagFullViewModalVisible", "setIsKeyValueTagFullViewModalVisible", "backgroundColor", "backgroundPrimary", "tagValue", "icon", "CopyIcon", "TRUNCATE_ON_CHARS_LENGTH", "getTruncatedStyles", "arguments", "textOverflow", "textWrap", "enableFullViewModal", "charLimit", "max<PERSON><PERSON><PERSON>", "className", "shouldTruncateKey", "shouldTruncateValue", "full<PERSON>ength", "is<PERSON>ey<PERSON>onger", "<PERSON><PERSON><PERSON><PERSON>", "getKeyAndValueComplexTruncation", "allowFullViewModal", "fullViewModalLabel", "Tag", "closable", "Text"], "sourceRoot": ""}