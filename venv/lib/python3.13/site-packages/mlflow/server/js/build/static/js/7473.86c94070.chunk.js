"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[7473],{4877:function(e){e.exports=function(e,t,n,i,r,s,o,a){if(!e){var c;if(void 0===t)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,i,r,s,o,a],l=0;(c=new Error(t.replace(/%s/g,(function(){return u[l++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}},18815:function(e,t,n){n.d(t,{m:function(){return o}});var i=n(57914),r=n(31014),s=n(26901);function o(e){var t=(0,r.useContext)((0,s.l)()),n=e||t.client;return __DEV__?(0,i.V1)(!!n,'Could not find "client" in the context or passed in as an option. Wrap the root component in an <ApolloProvider>, or pass an ApolloClient instance in via options.'):(0,i.V1)(!!n,29),n}},56675:function(e,t,n){n.d(t,{J1:function(){return X}});var i=n(97779);function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}var s=n(78870);function o(e,t){for(var n,i=/\r\n|[\n\r]/g,r=1,s=t+1;(n=i.exec(e.body))&&n.index<t;)r+=1,s=t+1-(n.index+n[0].length);return{line:r,column:s}}function a(e){return c(e.source,o(e.source,e.start))}function c(e,t){var n=e.locationOffset.column-1,i=l(n)+e.body,r=t.line-1,s=e.locationOffset.line-1,o=t.line+s,a=1===t.line?n:0,c=t.column+a,p="".concat(e.name,":").concat(o,":").concat(c,"\n"),h=i.split(/\r\n|[\n\r]/g),d=h[r];if(d.length>120){for(var f=Math.floor(c/80),v=c%80,E=[],y=0;y<d.length;y+=80)E.push(d.slice(y,y+80));return p+u([["".concat(o),E[0]]].concat(E.slice(1,f+1).map((function(e){return["",e]})),[[" ",l(v-1)+"^"],["",E[f+1]]]))}return p+u([["".concat(o-1),h[r-1]],["".concat(o),d],["",l(c-1)+"^"],["".concat(o+1),h[r+1]]])}function u(e){var t=e.filter((function(e){e[0];return void 0!==e[1]})),n=Math.max.apply(Math,t.map((function(e){return e[0].length})));return t.map((function(e){var t,i=e[0],r=e[1];return l(n-(t=i).length)+t+(r?" | "+r:" |")})).join("\n")}function l(e){return Array(e+1).join(" ")}function p(e){return p="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function h(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function d(e,t){return!t||"object"!==p(t)&&"function"!==typeof t?f(e):t}function f(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(e){var t="function"===typeof Map?new Map:void 0;return v=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,i)}function i(){return E(e,arguments,T(this).constructor)}return i.prototype=Object.create(e.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),N(i,e)},v(e)}function E(e,t,n){return E=y()?Reflect.construct:function(e,t,n){var i=[null];i.push.apply(i,t);var r=new(Function.bind.apply(e,i));return n&&N(r,n.prototype),r},E.apply(null,arguments)}function y(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function N(e,t){return N=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},N(e,t)}function T(e){return T=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},T(e)}var _=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&N(e,t)}(l,e);var t,n,i,u=function(e){var t=y();return function(){var n,i=T(e);if(t){var r=T(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return d(this,n)}}(l);function l(e,t,n,i,s,a,c){var p,h,v,E,y;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),y=u.call(this,e);var N,T=Array.isArray(t)?0!==t.length?t:void 0:t?[t]:void 0,_=n;!_&&T&&(_=null===(N=T[0].loc)||void 0===N?void 0:N.source);var m,O=i;!O&&T&&(O=T.reduce((function(e,t){return t.loc&&e.push(t.loc.start),e}),[])),O&&0===O.length&&(O=void 0),i&&n?m=i.map((function(e){return o(n,e)})):T&&(m=T.reduce((function(e,t){return t.loc&&e.push(o(t.loc.source,t.loc.start)),e}),[]));var I,k=c;if(null==k&&null!=a){var b=a.extensions;"object"==r(I=b)&&null!==I&&(k=b)}return Object.defineProperties(f(y),{name:{value:"GraphQLError"},message:{value:e,enumerable:!0,writable:!0},locations:{value:null!==(p=m)&&void 0!==p?p:void 0,enumerable:null!=m},path:{value:null!==s&&void 0!==s?s:void 0,enumerable:null!=s},nodes:{value:null!==T&&void 0!==T?T:void 0},source:{value:null!==(h=_)&&void 0!==h?h:void 0},positions:{value:null!==(v=O)&&void 0!==v?v:void 0},originalError:{value:a},extensions:{value:null!==(E=k)&&void 0!==E?E:void 0,enumerable:null!=k}}),null!==a&&void 0!==a&&a.stack?(Object.defineProperty(f(y),"stack",{value:a.stack,writable:!0,configurable:!0}),d(y)):(Error.captureStackTrace?Error.captureStackTrace(f(y),l):Object.defineProperty(f(y),"stack",{value:Error().stack,writable:!0,configurable:!0}),y)}return t=l,(n=[{key:"toString",value:function(){return function(e){var t=e.message;if(e.nodes)for(var n=0,i=e.nodes;n<i.length;n++){var r=i[n];r.loc&&(t+="\n\n"+a(r.loc))}else if(e.source&&e.locations)for(var s=0,o=e.locations;s<o.length;s++){var u=o[s];t+="\n\n"+c(e.source,u)}return t}(this)}},{key:s.Kp,get:function(){return"Object"}}])&&h(t.prototype,n),i&&h(t,i),l}(v(Error));function m(e,t,n){return new _("Syntax Error: ".concat(n),void 0,e,[t])}var O=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"}),I=n(13006),k=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"}),b=n(24971),D=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"}),A=n(80251),g=function(){function e(e){var t=new I.ou(k.SOF,0,0,0,0,null);this.source=e,this.lastToken=t,this.token=t,this.line=1,this.lineStart=0}var t=e.prototype;return t.advance=function(){return this.lastToken=this.token,this.token=this.lookahead()},t.lookahead=function(){var e=this.token;if(e.kind!==k.EOF)do{var t;e=null!==(t=e.next)&&void 0!==t?t:e.next=R(this,e)}while(e.kind===k.COMMENT);return e},e}();function x(e){return isNaN(e)?k.EOF:e<127?JSON.stringify(String.fromCharCode(e)):'"\\u'.concat(("00"+e.toString(16).toUpperCase()).slice(-4),'"')}function R(e,t){for(var n=e.source,i=n.body,r=i.length,s=t.end;s<r;){var o=i.charCodeAt(s),a=e.line,c=1+s-e.lineStart;switch(o){case 65279:case 9:case 32:case 44:++s;continue;case 10:++s,++e.line,e.lineStart=s;continue;case 13:10===i.charCodeAt(s+1)?s+=2:++s,++e.line,e.lineStart=s;continue;case 33:return new I.ou(k.BANG,s,s+1,a,c,t);case 35:return C(n,s,a,c,t);case 36:return new I.ou(k.DOLLAR,s,s+1,a,c,t);case 38:return new I.ou(k.AMP,s,s+1,a,c,t);case 40:return new I.ou(k.PAREN_L,s,s+1,a,c,t);case 41:return new I.ou(k.PAREN_R,s,s+1,a,c,t);case 46:if(46===i.charCodeAt(s+1)&&46===i.charCodeAt(s+2))return new I.ou(k.SPREAD,s,s+3,a,c,t);break;case 58:return new I.ou(k.COLON,s,s+1,a,c,t);case 61:return new I.ou(k.EQUALS,s,s+1,a,c,t);case 64:return new I.ou(k.AT,s,s+1,a,c,t);case 91:return new I.ou(k.BRACKET_L,s,s+1,a,c,t);case 93:return new I.ou(k.BRACKET_R,s,s+1,a,c,t);case 123:return new I.ou(k.BRACE_L,s,s+1,a,c,t);case 124:return new I.ou(k.PIPE,s,s+1,a,c,t);case 125:return new I.ou(k.BRACE_R,s,s+1,a,c,t);case 34:return 34===i.charCodeAt(s+1)&&34===i.charCodeAt(s+2)?P(n,s,a,c,t,e):F(n,s,a,c,t);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return w(n,s,o,a,c,t);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return V(n,s,a,c,t)}throw m(n,s,S(o))}var u=e.line,l=1+s-e.lineStart;return new I.ou(k.EOF,r,r,u,l,t)}function S(e){return e<32&&9!==e&&10!==e&&13!==e?"Cannot contain the invalid character ".concat(x(e),"."):39===e?"Unexpected single quote character ('), did you mean to use a double quote (\")?":"Cannot parse the unexpected character ".concat(x(e),".")}function C(e,t,n,i,r){var s,o=e.body,a=t;do{s=o.charCodeAt(++a)}while(!isNaN(s)&&(s>31||9===s));return new I.ou(k.COMMENT,t,a,n,i,r,o.slice(t+1,a))}function w(e,t,n,i,r,s){var o=e.body,a=n,c=t,u=!1;if(45===a&&(a=o.charCodeAt(++c)),48===a){if((a=o.charCodeAt(++c))>=48&&a<=57)throw m(e,c,"Invalid number, unexpected digit after 0: ".concat(x(a),"."))}else c=L(e,c,a),a=o.charCodeAt(c);if(46===a&&(u=!0,a=o.charCodeAt(++c),c=L(e,c,a),a=o.charCodeAt(c)),69!==a&&101!==a||(u=!0,43!==(a=o.charCodeAt(++c))&&45!==a||(a=o.charCodeAt(++c)),c=L(e,c,a),a=o.charCodeAt(c)),46===a||function(e){return 95===e||e>=65&&e<=90||e>=97&&e<=122}(a))throw m(e,c,"Invalid number, expected digit but got: ".concat(x(a),"."));return new I.ou(u?k.FLOAT:k.INT,t,c,i,r,s,o.slice(t,c))}function L(e,t,n){var i=e.body,r=t,s=n;if(s>=48&&s<=57){do{s=i.charCodeAt(++r)}while(s>=48&&s<=57);return r}throw m(e,r,"Invalid number, expected digit but got: ".concat(x(s),"."))}function F(e,t,n,i,r){for(var s,o,a,c,u=e.body,l=t+1,p=l,h=0,d="";l<u.length&&!isNaN(h=u.charCodeAt(l))&&10!==h&&13!==h;){if(34===h)return d+=u.slice(p,l),new I.ou(k.STRING,t,l+1,n,i,r,d);if(h<32&&9!==h)throw m(e,l,"Invalid character within String: ".concat(x(h),"."));if(++l,92===h){switch(d+=u.slice(p,l-1),h=u.charCodeAt(l)){case 34:d+='"';break;case 47:d+="/";break;case 92:d+="\\";break;case 98:d+="\b";break;case 102:d+="\f";break;case 110:d+="\n";break;case 114:d+="\r";break;case 116:d+="\t";break;case 117:var f=(s=u.charCodeAt(l+1),o=u.charCodeAt(l+2),a=u.charCodeAt(l+3),c=u.charCodeAt(l+4),M(s)<<12|M(o)<<8|M(a)<<4|M(c));if(f<0){var v=u.slice(l+1,l+5);throw m(e,l,"Invalid character escape sequence: \\u".concat(v,"."))}d+=String.fromCharCode(f),l+=4;break;default:throw m(e,l,"Invalid character escape sequence: \\".concat(String.fromCharCode(h),"."))}p=++l}}throw m(e,l,"Unterminated string.")}function P(e,t,n,i,r,s){for(var o=e.body,a=t+3,c=a,u=0,l="";a<o.length&&!isNaN(u=o.charCodeAt(a));){if(34===u&&34===o.charCodeAt(a+1)&&34===o.charCodeAt(a+2))return l+=o.slice(c,a),new I.ou(k.BLOCK_STRING,t,a+3,n,i,r,(0,A.i$)(l));if(u<32&&9!==u&&10!==u&&13!==u)throw m(e,a,"Invalid character within String: ".concat(x(u),"."));10===u?(++a,++s.line,s.lineStart=a):13===u?(10===o.charCodeAt(a+1)?a+=2:++a,++s.line,s.lineStart=a):92===u&&34===o.charCodeAt(a+1)&&34===o.charCodeAt(a+2)&&34===o.charCodeAt(a+3)?(l+=o.slice(c,a)+'"""',c=a+=4):++a}throw m(e,a,"Unterminated string.")}function M(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function V(e,t,n,i,r){for(var s=e.body,o=s.length,a=t+1,c=0;a!==o&&!isNaN(c=s.charCodeAt(a))&&(95===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122);)++a;return new I.ou(k.NAME,t,a,n,i,r,s.slice(t,a))}var U=function(){function e(e,t){var n=(0,b._)(e)?e:new b.k(e);this._lexer=new g(n),this._options=t}var t=e.prototype;return t.parseName=function(){var e=this.expectToken(k.NAME);return{kind:O.NAME,value:e.value,loc:this.loc(e)}},t.parseDocument=function(){var e=this._lexer.token;return{kind:O.DOCUMENT,definitions:this.many(k.SOF,this.parseDefinition,k.EOF),loc:this.loc(e)}},t.parseDefinition=function(){if(this.peek(k.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(k.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},t.parseOperationDefinition=function(){var e=this._lexer.token;if(this.peek(k.BRACE_L))return{kind:O.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(e)};var t,n=this.parseOperationType();return this.peek(k.NAME)&&(t=this.parseName()),{kind:O.OPERATION_DEFINITION,operation:n,name:t,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},t.parseOperationType=function(){var e=this.expectToken(k.NAME);switch(e.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(e)},t.parseVariableDefinitions=function(){return this.optionalMany(k.PAREN_L,this.parseVariableDefinition,k.PAREN_R)},t.parseVariableDefinition=function(){var e=this._lexer.token;return{kind:O.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(k.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(k.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(e)}},t.parseVariable=function(){var e=this._lexer.token;return this.expectToken(k.DOLLAR),{kind:O.VARIABLE,name:this.parseName(),loc:this.loc(e)}},t.parseSelectionSet=function(){var e=this._lexer.token;return{kind:O.SELECTION_SET,selections:this.many(k.BRACE_L,this.parseSelection,k.BRACE_R),loc:this.loc(e)}},t.parseSelection=function(){return this.peek(k.SPREAD)?this.parseFragment():this.parseField()},t.parseField=function(){var e,t,n=this._lexer.token,i=this.parseName();return this.expectOptionalToken(k.COLON)?(e=i,t=this.parseName()):t=i,{kind:O.FIELD,alias:e,name:t,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(k.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(n)}},t.parseArguments=function(e){var t=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(k.PAREN_L,t,k.PAREN_R)},t.parseArgument=function(){var e=this._lexer.token,t=this.parseName();return this.expectToken(k.COLON),{kind:O.ARGUMENT,name:t,value:this.parseValueLiteral(!1),loc:this.loc(e)}},t.parseConstArgument=function(){var e=this._lexer.token;return{kind:O.ARGUMENT,name:this.parseName(),value:(this.expectToken(k.COLON),this.parseValueLiteral(!0)),loc:this.loc(e)}},t.parseFragment=function(){var e=this._lexer.token;this.expectToken(k.SPREAD);var t=this.expectOptionalKeyword("on");return!t&&this.peek(k.NAME)?{kind:O.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(e)}:{kind:O.INLINE_FRAGMENT,typeCondition:t?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},t.parseFragmentDefinition=function(){var e,t=this._lexer.token;return this.expectKeyword("fragment"),!0===(null===(e=this._options)||void 0===e?void 0:e.experimentalFragmentVariables)?{kind:O.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(t)}:{kind:O.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(t)}},t.parseFragmentName=function(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()},t.parseValueLiteral=function(e){var t=this._lexer.token;switch(t.kind){case k.BRACKET_L:return this.parseList(e);case k.BRACE_L:return this.parseObject(e);case k.INT:return this._lexer.advance(),{kind:O.INT,value:t.value,loc:this.loc(t)};case k.FLOAT:return this._lexer.advance(),{kind:O.FLOAT,value:t.value,loc:this.loc(t)};case k.STRING:case k.BLOCK_STRING:return this.parseStringLiteral();case k.NAME:switch(this._lexer.advance(),t.value){case"true":return{kind:O.BOOLEAN,value:!0,loc:this.loc(t)};case"false":return{kind:O.BOOLEAN,value:!1,loc:this.loc(t)};case"null":return{kind:O.NULL,loc:this.loc(t)};default:return{kind:O.ENUM,value:t.value,loc:this.loc(t)}}case k.DOLLAR:if(!e)return this.parseVariable()}throw this.unexpected()},t.parseStringLiteral=function(){var e=this._lexer.token;return this._lexer.advance(),{kind:O.STRING,value:e.value,block:e.kind===k.BLOCK_STRING,loc:this.loc(e)}},t.parseList=function(e){var t=this,n=this._lexer.token;return{kind:O.LIST,values:this.any(k.BRACKET_L,(function(){return t.parseValueLiteral(e)}),k.BRACKET_R),loc:this.loc(n)}},t.parseObject=function(e){var t=this,n=this._lexer.token;return{kind:O.OBJECT,fields:this.any(k.BRACE_L,(function(){return t.parseObjectField(e)}),k.BRACE_R),loc:this.loc(n)}},t.parseObjectField=function(e){var t=this._lexer.token,n=this.parseName();return this.expectToken(k.COLON),{kind:O.OBJECT_FIELD,name:n,value:this.parseValueLiteral(e),loc:this.loc(t)}},t.parseDirectives=function(e){for(var t=[];this.peek(k.AT);)t.push(this.parseDirective(e));return t},t.parseDirective=function(e){var t=this._lexer.token;return this.expectToken(k.AT),{kind:O.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e),loc:this.loc(t)}},t.parseTypeReference=function(){var e,t=this._lexer.token;return this.expectOptionalToken(k.BRACKET_L)?(e=this.parseTypeReference(),this.expectToken(k.BRACKET_R),e={kind:O.LIST_TYPE,type:e,loc:this.loc(t)}):e=this.parseNamedType(),this.expectOptionalToken(k.BANG)?{kind:O.NON_NULL_TYPE,type:e,loc:this.loc(t)}:e},t.parseNamedType=function(){var e=this._lexer.token;return{kind:O.NAMED_TYPE,name:this.parseName(),loc:this.loc(e)}},t.parseTypeSystemDefinition=function(){var e=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(e.kind===k.NAME)switch(e.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(e)},t.peekDescription=function(){return this.peek(k.STRING)||this.peek(k.BLOCK_STRING)},t.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},t.parseSchemaDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("schema");var n=this.parseDirectives(!0),i=this.many(k.BRACE_L,this.parseOperationTypeDefinition,k.BRACE_R);return{kind:O.SCHEMA_DEFINITION,description:t,directives:n,operationTypes:i,loc:this.loc(e)}},t.parseOperationTypeDefinition=function(){var e=this._lexer.token,t=this.parseOperationType();this.expectToken(k.COLON);var n=this.parseNamedType();return{kind:O.OPERATION_TYPE_DEFINITION,operation:t,type:n,loc:this.loc(e)}},t.parseScalarTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("scalar");var n=this.parseName(),i=this.parseDirectives(!0);return{kind:O.SCALAR_TYPE_DEFINITION,description:t,name:n,directives:i,loc:this.loc(e)}},t.parseObjectTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("type");var n=this.parseName(),i=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),s=this.parseFieldsDefinition();return{kind:O.OBJECT_TYPE_DEFINITION,description:t,name:n,interfaces:i,directives:r,fields:s,loc:this.loc(e)}},t.parseImplementsInterfaces=function(){var e;if(!this.expectOptionalKeyword("implements"))return[];if(!0===(null===(e=this._options)||void 0===e?void 0:e.allowLegacySDLImplementsInterfaces)){var t=[];this.expectOptionalToken(k.AMP);do{t.push(this.parseNamedType())}while(this.expectOptionalToken(k.AMP)||this.peek(k.NAME));return t}return this.delimitedMany(k.AMP,this.parseNamedType)},t.parseFieldsDefinition=function(){var e;return!0===(null===(e=this._options)||void 0===e?void 0:e.allowLegacySDLEmptyFields)&&this.peek(k.BRACE_L)&&this._lexer.lookahead().kind===k.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(k.BRACE_L,this.parseFieldDefinition,k.BRACE_R)},t.parseFieldDefinition=function(){var e=this._lexer.token,t=this.parseDescription(),n=this.parseName(),i=this.parseArgumentDefs();this.expectToken(k.COLON);var r=this.parseTypeReference(),s=this.parseDirectives(!0);return{kind:O.FIELD_DEFINITION,description:t,name:n,arguments:i,type:r,directives:s,loc:this.loc(e)}},t.parseArgumentDefs=function(){return this.optionalMany(k.PAREN_L,this.parseInputValueDef,k.PAREN_R)},t.parseInputValueDef=function(){var e=this._lexer.token,t=this.parseDescription(),n=this.parseName();this.expectToken(k.COLON);var i,r=this.parseTypeReference();this.expectOptionalToken(k.EQUALS)&&(i=this.parseValueLiteral(!0));var s=this.parseDirectives(!0);return{kind:O.INPUT_VALUE_DEFINITION,description:t,name:n,type:r,defaultValue:i,directives:s,loc:this.loc(e)}},t.parseInterfaceTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("interface");var n=this.parseName(),i=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),s=this.parseFieldsDefinition();return{kind:O.INTERFACE_TYPE_DEFINITION,description:t,name:n,interfaces:i,directives:r,fields:s,loc:this.loc(e)}},t.parseUnionTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("union");var n=this.parseName(),i=this.parseDirectives(!0),r=this.parseUnionMemberTypes();return{kind:O.UNION_TYPE_DEFINITION,description:t,name:n,directives:i,types:r,loc:this.loc(e)}},t.parseUnionMemberTypes=function(){return this.expectOptionalToken(k.EQUALS)?this.delimitedMany(k.PIPE,this.parseNamedType):[]},t.parseEnumTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("enum");var n=this.parseName(),i=this.parseDirectives(!0),r=this.parseEnumValuesDefinition();return{kind:O.ENUM_TYPE_DEFINITION,description:t,name:n,directives:i,values:r,loc:this.loc(e)}},t.parseEnumValuesDefinition=function(){return this.optionalMany(k.BRACE_L,this.parseEnumValueDefinition,k.BRACE_R)},t.parseEnumValueDefinition=function(){var e=this._lexer.token,t=this.parseDescription(),n=this.parseName(),i=this.parseDirectives(!0);return{kind:O.ENUM_VALUE_DEFINITION,description:t,name:n,directives:i,loc:this.loc(e)}},t.parseInputObjectTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("input");var n=this.parseName(),i=this.parseDirectives(!0),r=this.parseInputFieldsDefinition();return{kind:O.INPUT_OBJECT_TYPE_DEFINITION,description:t,name:n,directives:i,fields:r,loc:this.loc(e)}},t.parseInputFieldsDefinition=function(){return this.optionalMany(k.BRACE_L,this.parseInputValueDef,k.BRACE_R)},t.parseTypeSystemExtension=function(){var e=this._lexer.lookahead();if(e.kind===k.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)},t.parseSchemaExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var t=this.parseDirectives(!0),n=this.optionalMany(k.BRACE_L,this.parseOperationTypeDefinition,k.BRACE_R);if(0===t.length&&0===n.length)throw this.unexpected();return{kind:O.SCHEMA_EXTENSION,directives:t,operationTypes:n,loc:this.loc(e)}},t.parseScalarTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var t=this.parseName(),n=this.parseDirectives(!0);if(0===n.length)throw this.unexpected();return{kind:O.SCALAR_TYPE_EXTENSION,name:t,directives:n,loc:this.loc(e)}},t.parseObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var t=this.parseName(),n=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),r=this.parseFieldsDefinition();if(0===n.length&&0===i.length&&0===r.length)throw this.unexpected();return{kind:O.OBJECT_TYPE_EXTENSION,name:t,interfaces:n,directives:i,fields:r,loc:this.loc(e)}},t.parseInterfaceTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var t=this.parseName(),n=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),r=this.parseFieldsDefinition();if(0===n.length&&0===i.length&&0===r.length)throw this.unexpected();return{kind:O.INTERFACE_TYPE_EXTENSION,name:t,interfaces:n,directives:i,fields:r,loc:this.loc(e)}},t.parseUnionTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var t=this.parseName(),n=this.parseDirectives(!0),i=this.parseUnionMemberTypes();if(0===n.length&&0===i.length)throw this.unexpected();return{kind:O.UNION_TYPE_EXTENSION,name:t,directives:n,types:i,loc:this.loc(e)}},t.parseEnumTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var t=this.parseName(),n=this.parseDirectives(!0),i=this.parseEnumValuesDefinition();if(0===n.length&&0===i.length)throw this.unexpected();return{kind:O.ENUM_TYPE_EXTENSION,name:t,directives:n,values:i,loc:this.loc(e)}},t.parseInputObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var t=this.parseName(),n=this.parseDirectives(!0),i=this.parseInputFieldsDefinition();if(0===n.length&&0===i.length)throw this.unexpected();return{kind:O.INPUT_OBJECT_TYPE_EXTENSION,name:t,directives:n,fields:i,loc:this.loc(e)}},t.parseDirectiveDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("directive"),this.expectToken(k.AT);var n=this.parseName(),i=this.parseArgumentDefs(),r=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var s=this.parseDirectiveLocations();return{kind:O.DIRECTIVE_DEFINITION,description:t,name:n,arguments:i,repeatable:r,locations:s,loc:this.loc(e)}},t.parseDirectiveLocations=function(){return this.delimitedMany(k.PIPE,this.parseDirectiveLocation)},t.parseDirectiveLocation=function(){var e=this._lexer.token,t=this.parseName();if(void 0!==D[t.value])return t;throw this.unexpected(e)},t.loc=function(e){var t;if(!0!==(null===(t=this._options)||void 0===t?void 0:t.noLocation))return new I.aZ(e,this._lexer.lastToken,this._lexer.source)},t.peek=function(e){return this._lexer.token.kind===e},t.expectToken=function(e){var t=this._lexer.token;if(t.kind===e)return this._lexer.advance(),t;throw m(this._lexer.source,t.start,"Expected ".concat(K(e),", found ").concat(B(t),"."))},t.expectOptionalToken=function(e){var t=this._lexer.token;if(t.kind===e)return this._lexer.advance(),t},t.expectKeyword=function(e){var t=this._lexer.token;if(t.kind!==k.NAME||t.value!==e)throw m(this._lexer.source,t.start,'Expected "'.concat(e,'", found ').concat(B(t),"."));this._lexer.advance()},t.expectOptionalKeyword=function(e){var t=this._lexer.token;return t.kind===k.NAME&&t.value===e&&(this._lexer.advance(),!0)},t.unexpected=function(e){var t=null!==e&&void 0!==e?e:this._lexer.token;return m(this._lexer.source,t.start,"Unexpected ".concat(B(t),"."))},t.any=function(e,t,n){this.expectToken(e);for(var i=[];!this.expectOptionalToken(n);)i.push(t.call(this));return i},t.optionalMany=function(e,t,n){if(this.expectOptionalToken(e)){var i=[];do{i.push(t.call(this))}while(!this.expectOptionalToken(n));return i}return[]},t.many=function(e,t,n){this.expectToken(e);var i=[];do{i.push(t.call(this))}while(!this.expectOptionalToken(n));return i},t.delimitedMany=function(e,t){this.expectOptionalToken(e);var n=[];do{n.push(t.call(this))}while(this.expectOptionalToken(e));return n},e}();function B(e){var t=e.value;return K(e.kind)+(null!=t?' "'.concat(t,'"'):"")}function K(e){return function(e){return e===k.BANG||e===k.DOLLAR||e===k.AMP||e===k.PAREN_L||e===k.PAREN_R||e===k.SPREAD||e===k.COLON||e===k.EQUALS||e===k.AT||e===k.BRACKET_L||e===k.BRACKET_R||e===k.BRACE_L||e===k.PIPE||e===k.BRACE_R}(e)?'"'.concat(e,'"'):e}var j=new Map,Q=new Map,G=!0,Y=!1;function q(e){return e.replace(/[\s,]+/g," ").trim()}function H(e){var t=new Set,n=[];return e.definitions.forEach((function(e){if("FragmentDefinition"===e.kind){var i=e.name.value,r=q((o=e.loc).source.body.substring(o.start,o.end)),s=Q.get(i);s&&!s.has(r)?G&&console.warn("Warning: fragment with name "+i+" already exists.\ngraphql-tag enforces all fragment names across your application to be unique; read more about\nthis in the docs: http://dev.apollodata.com/core/fragments.html#unique-names"):s||Q.set(i,s=new Set),s.add(r),t.has(r)||(t.add(r),n.push(e))}else n.push(e);var o})),(0,i.__assign)((0,i.__assign)({},e),{definitions:n})}function J(e){var t=q(e);if(!j.has(t)){var n=function(e,t){return new U(e,t).parseDocument()}(e,{experimentalFragmentVariables:Y,allowLegacyFragmentVariables:Y});if(!n||"Document"!==n.kind)throw new Error("Not a valid GraphQL document.");j.set(t,function(e){var t=new Set(e.definitions);t.forEach((function(e){e.loc&&delete e.loc,Object.keys(e).forEach((function(n){var i=e[n];i&&"object"===typeof i&&t.add(i)}))}));var n=e.loc;return n&&(delete n.startToken,delete n.endToken),e}(H(n)))}return j.get(t)}function X(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];"string"===typeof e&&(e=[e]);var i=e[0];return t.forEach((function(t,n){t&&"Document"===t.kind?i+=t.loc.source.body:i+=t,i+=e[n+1]})),J(i)}var W,z=X,$=function(){j.clear(),Q.clear()},Z=function(){G=!1},ee=function(){Y=!0},te=function(){Y=!1};(W=X||(X={})).gql=z,W.resetCaches=$,W.disableFragmentWarnings=Z,W.enableExperimentalFragmentVariables=ee,W.disableExperimentalFragmentVariables=te,X.default=X},95947:function(e,t,n){n.d(t,{k:function(){return D},I:function(){return b}});var i=n(97779),r=n(57914),s=n(31014),o=n.t(s,2),a=n(11451),c=!1,u=o.useSyncExternalStore||function(e,t,n){var i=t();__DEV__&&!c&&i!==t()&&(c=!0,__DEV__&&r.V1.error("The result of getSnapshot should be cached to avoid an infinite loop"));var o=s.useState({inst:{value:i,getSnapshot:t}}),u=o[0].inst,p=o[1];return a.JR?s.useLayoutEffect((function(){Object.assign(u,{value:i,getSnapshot:t}),l(u)&&p({inst:u})}),[e,i,t]):Object.assign(u,{value:i,getSnapshot:t}),s.useEffect((function(){return l(u)&&p({inst:u}),e((function(){l(u)&&p({inst:u})}))}),[e]),i};function l(e){var t=e.value,n=e.getSnapshot;try{return t!==n()}catch(i){return!0}}var p,h=n(79584),d=n(86896),f=n(26901),v=n(55003),E=n(67671);!function(e){e[e.Query=0]="Query",e[e.Mutation=1]="Mutation",e[e.Subscription=2]="Subscription"}(p||(p={}));var y=new Map;function N(e){var t;switch(e){case p.Query:t="Query";break;case p.Mutation:t="Mutation";break;case p.Subscription:t="Subscription"}return t}function T(e,t){var n=function(e){var t,n,i=y.get(e);if(i)return i;__DEV__?(0,r.V1)(!!e&&!!e.kind,"Argument of ".concat(e," passed to parser was not a valid GraphQL ")+"DocumentNode. You may need to use 'graphql-tag' or another method to convert your operation into a document"):(0,r.V1)(!!e&&!!e.kind,30);for(var s=[],o=[],a=[],c=[],u=0,l=e.definitions;u<l.length;u++){var h=l[u];if("FragmentDefinition"!==h.kind){if("OperationDefinition"===h.kind)switch(h.operation){case"query":o.push(h);break;case"mutation":a.push(h);break;case"subscription":c.push(h)}}else s.push(h)}__DEV__?(0,r.V1)(!s.length||o.length||a.length||c.length,"Passing only a fragment to 'graphql' is not yet supported. You must include a query, subscription or mutation as well"):(0,r.V1)(!s.length||o.length||a.length||c.length,31),__DEV__?(0,r.V1)(o.length+a.length+c.length<=1,"react-apollo only supports a query, subscription, or a mutation per HOC. "+"".concat(e," had ").concat(o.length," queries, ").concat(c.length," ")+"subscriptions and ".concat(a.length," mutations. ")+"You can use 'compose' to join multiple operation types to a component"):(0,r.V1)(o.length+a.length+c.length<=1,32),n=o.length?p.Query:p.Mutation,o.length||a.length||(n=p.Subscription);var d=o.length?o:a.length?a:c;__DEV__?(0,r.V1)(1===d.length,"react-apollo only supports one definition per HOC. ".concat(e," had ")+"".concat(d.length," definitions. ")+"You can use 'compose' to join multiple operation types to a component"):(0,r.V1)(1===d.length,33);var f=d[0];t=f.variableDefinitions||[];var v={name:f.name&&"Name"===f.name.kind?f.name.value:"data",type:n,variables:t};return y.set(e,v),v}(e),i=N(t),s=N(n.type);__DEV__?(0,r.V1)(n.type===t,"Running a ".concat(i," requires a graphql ")+"".concat(i,", but a ").concat(s," was used instead.")):(0,r.V1)(n.type===t,34)}var _=n(18815),m=n(93981),O=n(25641),I=n(87556),k=Object.prototype.hasOwnProperty;function b(e,t){return void 0===t&&(t=Object.create(null)),D((0,_.m)(t.client),e).useQuery(t)}function D(e,t){var n=(0,s.useRef)();n.current&&e===n.current.client&&t===n.current.query||(n.current=new A(e,t,n.current));var i=n.current,r=(0,s.useState)(0),o=(r[0],r[1]);return i.forceUpdate=function(){o((function(e){return e+1}))},i}var A=function(){function e(e,t,n){this.client=e,this.query=t,this.asyncResolveFns=new Set,this.optionsToIgnoreOnce=new(a.En?WeakSet:Set),this.ssrDisabledResult=(0,m.G)({loading:!0,data:void 0,error:void 0,networkStatus:E.p.loading}),this.skipStandbyResult=(0,m.G)({loading:!1,data:void 0,error:void 0,networkStatus:E.p.ready}),this.toQueryResultCache=new(a.et?WeakMap:Map),T(t,p.Query);var i=n&&n.result,r=i&&i.data;r&&(this.previousData=r)}return e.prototype.forceUpdate=function(){__DEV__&&r.V1.warn("Calling default no-op implementation of InternalState#forceUpdate")},e.prototype.asyncUpdate=function(){var e=this;return new Promise((function(t){e.asyncResolveFns.add(t),e.optionsToIgnoreOnce.add(e.watchQueryOptions),e.forceUpdate()}))},e.prototype.useQuery=function(e){var t=this;this.renderPromises=(0,s.useContext)((0,f.l)()).renderPromises,this.useOptions(e);var n=this.useObservableQuery(),i=u((0,s.useCallback)((function(){if(t.renderPromises)return function(){};var e=function(){var e=t.result,i=n.getCurrentResult();e&&e.loading===i.loading&&e.networkStatus===i.networkStatus&&(0,h.L)(e.data,i.data)||t.setResult(i)},i=function(s){var o=n.last;r.unsubscribe();try{n.resetLastResults(),r=n.subscribe(e,i)}finally{n.last=o}if(!k.call(s,"graphQLErrors"))throw s;var a=t.result;(!a||a&&a.loading||!(0,h.L)(s,a.error))&&t.setResult({data:a&&a.data,error:s,loading:!1,networkStatus:E.p.error})},r=n.subscribe(e,i);return function(){return r.unsubscribe()}}),[n,this.renderPromises,this.client.disableNetworkFetches]),(function(){return t.getCurrentResult()}),(function(){return t.getCurrentResult()}));this.unsafeHandlePartialRefetch(i);var r=this.toQueryResult(i);return!r.loading&&this.asyncResolveFns.size&&(this.asyncResolveFns.forEach((function(e){return e(r)})),this.asyncResolveFns.clear()),r},e.prototype.useOptions=function(t){var n,i=this.createWatchQueryOptions(this.queryHookOptions=t),r=this.watchQueryOptions;!this.optionsToIgnoreOnce.has(r)&&(0,h.L)(i,r)||(this.watchQueryOptions=i,r&&this.observable&&(this.optionsToIgnoreOnce.delete(r),this.observable.reobserve(this.getObsQueryOptions()),this.previousData=(null===(n=this.result)||void 0===n?void 0:n.data)||this.previousData,this.result=void 0)),this.onCompleted=t.onCompleted||e.prototype.onCompleted,this.onError=t.onError||e.prototype.onError,!this.renderPromises&&!this.client.disableNetworkFetches||!1!==this.queryHookOptions.ssr||this.queryHookOptions.skip?this.queryHookOptions.skip||"standby"===this.watchQueryOptions.fetchPolicy?this.result=this.skipStandbyResult:this.result!==this.ssrDisabledResult&&this.result!==this.skipStandbyResult||(this.result=void 0):this.result=this.ssrDisabledResult},e.prototype.getObsQueryOptions=function(){var e=[],t=this.client.defaultOptions.watchQuery;return t&&e.push(t),this.queryHookOptions.defaultOptions&&e.push(this.queryHookOptions.defaultOptions),e.push((0,O.o)(this.observable&&this.observable.options,this.watchQueryOptions)),e.reduce(d.l)},e.prototype.createWatchQueryOptions=function(e){var t;void 0===e&&(e={});var n=e.skip,r=(e.ssr,e.onCompleted,e.onError,e.displayName,e.defaultOptions,(0,i.__rest)(e,["skip","ssr","onCompleted","onError","displayName","defaultOptions"])),s=Object.assign(r,{query:this.query});if(!this.renderPromises||"network-only"!==s.fetchPolicy&&"cache-and-network"!==s.fetchPolicy||(s.fetchPolicy="cache-first"),s.variables||(s.variables={}),n){var o=s.fetchPolicy,a=void 0===o?this.getDefaultFetchPolicy():o,c=s.initialFetchPolicy,u=void 0===c?a:c;Object.assign(s,{initialFetchPolicy:u,fetchPolicy:"standby"})}else s.fetchPolicy||(s.fetchPolicy=(null===(t=this.observable)||void 0===t?void 0:t.options.initialFetchPolicy)||this.getDefaultFetchPolicy());return s},e.prototype.getDefaultFetchPolicy=function(){var e,t;return(null===(e=this.queryHookOptions.defaultOptions)||void 0===e?void 0:e.fetchPolicy)||(null===(t=this.client.defaultOptions.watchQuery)||void 0===t?void 0:t.fetchPolicy)||"cache-first"},e.prototype.onCompleted=function(e){},e.prototype.onError=function(e){},e.prototype.useObservableQuery=function(){var e=this.observable=this.renderPromises&&this.renderPromises.getSSRObservable(this.watchQueryOptions)||this.observable||this.client.watchQuery(this.getObsQueryOptions());this.obsQueryFields=(0,s.useMemo)((function(){return{refetch:e.refetch.bind(e),reobserve:e.reobserve.bind(e),fetchMore:e.fetchMore.bind(e),updateQuery:e.updateQuery.bind(e),startPolling:e.startPolling.bind(e),stopPolling:e.stopPolling.bind(e),subscribeToMore:e.subscribeToMore.bind(e)}}),[e]);var t=!(!1===this.queryHookOptions.ssr||this.queryHookOptions.skip);return this.renderPromises&&t&&(this.renderPromises.registerSSRObservable(e),e.getCurrentResult().loading&&this.renderPromises.addObservableQueryPromise(e)),e},e.prototype.setResult=function(e){var t=this.result;t&&t.data&&(this.previousData=t.data),this.result=e,this.forceUpdate(),this.handleErrorOrCompleted(e)},e.prototype.handleErrorOrCompleted=function(e){e.loading||(e.error?this.onError(e.error):e.data&&this.onCompleted(e.data))},e.prototype.getCurrentResult=function(){return this.result||this.handleErrorOrCompleted(this.result=this.observable.getCurrentResult()),this.result},e.prototype.toQueryResult=function(e){var t=this.toQueryResultCache.get(e);if(t)return t;var n=e.data,r=(e.partial,(0,i.__rest)(e,["data","partial"]));return this.toQueryResultCache.set(e,t=(0,i.__assign)((0,i.__assign)((0,i.__assign)({data:n},r),this.obsQueryFields),{client:this.client,observable:this.observable,variables:this.observable.variables,called:!this.queryHookOptions.skip,previousData:this.previousData})),!t.error&&(0,I.E)(e.errors)&&(t.error=new v.K({graphQLErrors:e.errors})),t},e.prototype.unsafeHandlePartialRefetch=function(e){!e.partial||!this.queryHookOptions.partialRefetch||e.loading||e.data&&0!==Object.keys(e.data).length||"cache-only"===this.observable.options.fetchPolicy||(Object.assign(e,{loading:!0,networkStatus:E.p.refetch}),this.observable.refetch())},e}()}}]);
//# sourceMappingURL=7473.86c94070.chunk.js.map