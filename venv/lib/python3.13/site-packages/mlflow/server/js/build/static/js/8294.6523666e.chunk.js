"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[8294],{4877:function(t){t.exports=function(t,e,r,n,s,o,i,u){if(!t){var a;if(void 0===e)a=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[r,n,s,o,i,u],c=0;(a=new Error(e.replace(/%s/g,(function(){return l[c++]})))).name="Invariant Violation"}throw a.framesToPop=1,a}}},21678:function(t,e,r){function n(){}function s(t,e,r,n,s){for(var o=0,i=e.length,u=0,a=0;o<i;o++){var l=e[o];if(l.removed){if(l.value=t.join(n.slice(a,a+l.count)),a+=l.count,o&&e[o-1].added){var c=e[o-1];e[o-1]=e[o],e[o]=c}}else{if(!l.added&&s){var h=r.slice(u,u+l.count);h=h.map((function(t,e){var r=n[a+e];return r.length>t.length?r:t})),l.value=t.join(h)}else l.value=t.join(r.slice(u,u+l.count));u+=l.count,l.added||(a+=l.count)}}var f=e[i-1];return i>1&&"string"===typeof f.value&&(f.added||f.removed)&&t.equals("",f.value)&&(e[i-2].value+=f.value,e.pop()),e}r.d(e,{b2:function(){return l}}),n.prototype={diff:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.callback;"function"===typeof r&&(n=r,r={}),this.options=r;var o=this;function i(t){return n?(setTimeout((function(){n(void 0,t)}),0),!0):t}t=this.castInput(t),e=this.castInput(e),t=this.removeEmpty(this.tokenize(t));var u=(e=this.removeEmpty(this.tokenize(e))).length,a=t.length,l=1,c=u+a;r.maxEditLength&&(c=Math.min(c,r.maxEditLength));var h=[{newPos:-1,components:[]}],f=this.extractCommon(h[0],e,t,0);if(h[0].newPos+1>=u&&f+1>=a)return i([{value:this.join(e),count:e.length}]);function p(){for(var r=-1*l;r<=l;r+=2){var n=void 0,c=h[r-1],f=h[r+1],p=(f?f.newPos:0)-r;c&&(h[r-1]=void 0);var d=c&&c.newPos+1<u,v=f&&0<=p&&p<a;if(d||v){if(!d||v&&c.newPos<f.newPos?(n={newPos:(m=f).newPos,components:m.components.slice(0)},o.pushComponent(n.components,void 0,!0)):((n=c).newPos++,o.pushComponent(n.components,!0,void 0)),p=o.extractCommon(n,e,t,r),n.newPos+1>=u&&p+1>=a)return i(s(o,n.components,e,t,o.useLongestToken));h[r]=n}else h[r]=void 0}var m;l++}if(n)!function t(){setTimeout((function(){if(l>c)return n();p()||t()}),0)}();else for(;l<=c;){var d=p();if(d)return d}},pushComponent:function(t,e,r){var n=t[t.length-1];n&&n.added===e&&n.removed===r?t[t.length-1]={count:n.count+1,added:e,removed:r}:t.push({count:1,added:e,removed:r})},extractCommon:function(t,e,r,n){for(var s=e.length,o=r.length,i=t.newPos,u=i-n,a=0;i+1<s&&u+1<o&&this.equals(e[i+1],r[u+1]);)i++,u++,a++;return a&&t.components.push({count:a}),t.newPos=i,u},equals:function(t,e){return this.options.comparator?this.options.comparator(t,e):t===e||this.options.ignoreCase&&t.toLowerCase()===e.toLowerCase()},removeEmpty:function(t){for(var e=[],r=0;r<t.length;r++)t[r]&&e.push(t[r]);return e},castInput:function(t){return t},tokenize:function(t){return t.split("")},join:function(t){return t.join("")}};new n;function o(t,e){if("function"===typeof t)e.callback=t;else if(t)for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);return e}var i=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,u=/\S/,a=new n;function l(t,e,r){return r=o(r,{ignoreWhitespace:!0}),a.diff(t,e,r)}a.equals=function(t,e){return this.options.ignoreCase&&(t=t.toLowerCase(),e=e.toLowerCase()),t===e||this.options.ignoreWhitespace&&!u.test(t)&&!u.test(e)},a.tokenize=function(t){for(var e=t.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),r=0;r<e.length-1;r++)!e[r+1]&&e[r+2]&&i.test(e[r])&&i.test(e[r+2])&&(e[r]+=e[r+2],e.splice(r+1,2),r--);return e};var c=new n;c.tokenize=function(t){var e=[],r=t.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var n=0;n<r.length;n++){var s=r[n];n%2&&!this.options.newlineIsToken?e[e.length-1]+=s:(this.options.ignoreWhitespace&&(s=s.trim()),e.push(s))}return e};var h=new n;h.tokenize=function(t){return t.split(/(\S.+?[.!?])(?=\s+|$)/)};var f=new n;function p(t){return p="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}f.tokenize=function(t){return t.split(/([{}:;,]|\s+)/)};var d=Object.prototype.toString,v=new n;function m(t,e,r,n,s){var o,i;for(e=e||[],r=r||[],n&&(t=n(s,t)),o=0;o<e.length;o+=1)if(e[o]===t)return r[o];if("[object Array]"===d.call(t)){for(e.push(t),i=new Array(t.length),r.push(i),o=0;o<t.length;o+=1)i[o]=m(t[o],e,r,n,s);return e.pop(),r.pop(),i}if(t&&t.toJSON&&(t=t.toJSON()),"object"===p(t)&&null!==t){e.push(t),i={},r.push(i);var u,a=[];for(u in t)t.hasOwnProperty(u)&&a.push(u);for(a.sort(),o=0;o<a.length;o+=1)i[u=a[o]]=m(t[u],e,r,n,u);e.pop(),r.pop()}else i=t;return i}v.useLongestToken=!0,v.tokenize=c.tokenize,v.castInput=function(t){var e=this.options,r=e.undefinedReplacement,n=e.stringifyReplacer,s=void 0===n?function(t,e){return"undefined"===typeof e?r:e}:n;return"string"===typeof t?t:JSON.stringify(m(t,null,null,s),s,"  ")},v.equals=function(t,e){return n.prototype.equals.call(v,t.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"))};var y=new n;y.tokenize=function(t){return t.slice()},y.join=y.removeEmpty=function(t){return t}},28486:function(t,e,r){r.d(e,{tH:function(){return u}});var n=r(31014);function s(t,e,r,n){Object.defineProperty(t,e,{get:r,set:n,enumerable:!0,configurable:!0})}s({},"ErrorBoundary",(()=>u));s({},"ErrorBoundaryContext",(()=>o));const o=(0,n.createContext)(null),i={didCatch:!1,error:null};class u extends n.Component{state=(()=>i)();static getDerivedStateFromError(t){return{didCatch:!0,error:t}}resetErrorBoundary=(()=>{var t=this;return function(){const{error:e}=t.state;if(null!==e){for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];t.props.onReset?.({args:n,reason:"imperative-api"}),t.setState(i)}}})();componentDidCatch(t,e){this.props.onError?.(t,e)}componentDidUpdate(t,e){const{didCatch:r}=this.state,{resetKeys:n}=this.props;r&&null!==e.error&&function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.length!==e.length||t.some(((t,r)=>!Object.is(t,e[r])))}(t.resetKeys,n)&&(this.props.onReset?.({next:n,prev:t.resetKeys,reason:"keys"}),this.setState(i))}render(){const{children:t,fallbackRender:e,FallbackComponent:r,fallback:s}=this.props,{didCatch:i,error:u}=this.state;let a=t;if(i){const t={error:u,resetErrorBoundary:this.resetErrorBoundary};if((0,n.isValidElement)(s))a=s;else if("function"===typeof e)a=e(t);else{if(!r)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");a=(0,n.createElement)(r,t)}}return(0,n.createElement)(o.Provider,{value:{didCatch:i,error:u,resetErrorBoundary:this.resetErrorBoundary}},a)}}function a(t){if(null==t||"boolean"!==typeof t.didCatch||"function"!==typeof t.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function l(){const t=(0,n.useContext)(o);a(t);const[e,r]=(0,n.useState)({error:null,hasError:!1}),s=(0,n.useMemo)((()=>({resetBoundary:()=>{t?.resetErrorBoundary(),r({error:null,hasError:!1})},showBoundary:t=>r({error:t,hasError:!0})})),[t?.resetErrorBoundary]);if(e.hasError)throw e.error;return s}s({},"useErrorBoundary",(()=>l));function c(t,e){const r=r=>(0,n.createElement)(u,e,(0,n.createElement)(t,r)),s=t.displayName||t.name||"Unknown";return r.displayName=`withErrorBoundary(${s})`,r}s({},"withErrorBoundary",(()=>c))},41261:function(t,e,r){r.d(e,{I:function(){return i}});var n=r(28999),s=r(45586),o=r(44200);function i(t,e,r){const i=(0,n.vh)(t,e,r);return(0,o.t)(i,s.$)}},77020:function(t,e,r){r.d(e,{n:function(){return f}});var n=r(31014),s=r(61226),o=r(28999),i=r(84865),u=r(95904),a=r(21363);class l extends a.Q{constructor(t,e){super(),this.client=t,this.setOptions(e),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;const r=this.options;this.options=this.client.defaultMutationOptions(t),(0,o.f8)(r,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(e=this.currentMutation)||e.setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.currentMutation)||t.removeObserver(this))}onMutationUpdate(t){this.updateResult();const e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:"undefined"!==typeof t?t:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const t=this.currentMutation?this.currentMutation.state:(0,i.$)(),e={...t,isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset};this.currentResult=e}notify(t){u.j.batch((()=>{var e,r,n,s;if(this.mutateOptions&&this.hasListeners())if(t.onSuccess)null==(e=(r=this.mutateOptions).onSuccess)||e.call(r,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(n=(s=this.mutateOptions).onSettled)||n.call(s,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(t.onError){var o,i,u,a;null==(o=(i=this.mutateOptions).onError)||o.call(i,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(u=(a=this.mutateOptions).onSettled)||u.call(a,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}t.listeners&&this.listeners.forEach((t=>{let{listener:e}=t;e(this.currentResult)}))}))}}var c=r(27288),h=r(71233);function f(t,e,r){const i=(0,o.GR)(t,e,r),a=(0,c.jE)({context:i.context}),[f]=n.useState((()=>new l(a,i)));n.useEffect((()=>{f.setOptions(i)}),[f,i]);const d=(0,s.r)(n.useCallback((t=>f.subscribe(u.j.batchCalls(t))),[f]),(()=>f.getCurrentResult()),(()=>f.getCurrentResult())),v=n.useCallback(((t,e)=>{f.mutate(t,e).catch(p)}),[f]);if(d.error&&(0,h.G)(f.options.useErrorBoundary,[d.error]))throw d.error;return{...d,mutate:v,mutateAsync:d.mutate}}function p(){}},77735:function(t,e,r){r.d(e,{E:function(){return v}});var n=r(31014),s=r(61226),o=r(28999),i=r(95904),u=r(45586),a=r(21363);class l extends a.Q{constructor(t,e){super(),this.client=t,this.queries=[],this.result=[],this.observers=[],this.observersMap={},e&&this.setQueries(e)}onSubscribe(){1===this.listeners.size&&this.observers.forEach((t=>{t.subscribe((e=>{this.onUpdate(t,e)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.observers.forEach((t=>{t.destroy()}))}setQueries(t,e){this.queries=t,i.j.batch((()=>{const t=this.observers,r=this.findMatchingObservers(this.queries);r.forEach((t=>t.observer.setOptions(t.defaultedQueryOptions,e)));const n=r.map((t=>t.observer)),s=Object.fromEntries(n.map((t=>[t.options.queryHash,t]))),i=n.map((t=>t.getCurrentResult())),u=n.some(((e,r)=>e!==t[r]));(t.length!==n.length||u)&&(this.observers=n,this.observersMap=s,this.result=i,this.hasListeners()&&((0,o.iv)(t,n).forEach((t=>{t.destroy()})),(0,o.iv)(n,t).forEach((t=>{t.subscribe((e=>{this.onUpdate(t,e)}))})),this.notify()))}))}getCurrentResult(){return this.result}getQueries(){return this.observers.map((t=>t.getCurrentQuery()))}getObservers(){return this.observers}getOptimisticResult(t){return this.findMatchingObservers(t).map((t=>t.observer.getOptimisticResult(t.defaultedQueryOptions)))}findMatchingObservers(t){const e=this.observers,r=new Map(e.map((t=>[t.options.queryHash,t]))),n=t.map((t=>this.client.defaultQueryOptions(t))),s=n.flatMap((t=>{const e=r.get(t.queryHash);return null!=e?[{defaultedQueryOptions:t,observer:e}]:[]})),o=new Set(s.map((t=>t.defaultedQueryOptions.queryHash))),i=n.filter((t=>!o.has(t.queryHash))),a=new Set(s.map((t=>t.observer))),l=e.filter((t=>!a.has(t))),c=t=>{const e=this.client.defaultQueryOptions(t),r=this.observersMap[e.queryHash];return null!=r?r:new u.$(this.client,e)},h=i.map(((t,e)=>{if(t.keepPreviousData){const r=l[e];if(void 0!==r)return{defaultedQueryOptions:t,observer:r}}return{defaultedQueryOptions:t,observer:c(t)}}));return s.concat(h).sort(((t,e)=>n.indexOf(t.defaultedQueryOptions)-n.indexOf(e.defaultedQueryOptions)))}onUpdate(t,e){const r=this.observers.indexOf(t);-1!==r&&(this.result=(0,o._D)(this.result,r,e),this.notify())}notify(){i.j.batch((()=>{this.listeners.forEach((t=>{let{listener:e}=t;e(this.result)}))}))}}var c=r(27288),h=r(26737),f=r(35067),p=r(52165),d=r(41698);function v(t){let{queries:e,context:r}=t;const o=(0,c.jE)({context:r}),u=(0,h.w)(),a=(0,f.h)(),v=n.useMemo((()=>e.map((t=>{const e=o.defaultQueryOptions(t);return e._optimisticResults=u?"isRestoring":"optimistic",e}))),[e,o,u]);v.forEach((t=>{(0,d.tu)(t),(0,p.LJ)(t,a)})),(0,p.wZ)(a);const[m]=n.useState((()=>new l(o,v))),y=m.getOptimisticResult(v);(0,s.r)(n.useCallback((t=>u?()=>{}:m.subscribe(i.j.batchCalls(t))),[m,u]),(()=>m.getCurrentResult()),(()=>m.getCurrentResult())),n.useEffect((()=>{m.setQueries(v,{listeners:!1})}),[v,m]);const b=y.some(((t,e)=>(0,d.EU)(v[e],t,u)))?y.flatMap(((t,e)=>{const r=v[e],n=m.getObservers()[e];if(r&&n){if((0,d.EU)(r,t,u))return(0,d.iL)(r,n,a);(0,d.nE)(t,u)&&(0,d.iL)(r,n,a)}return[]})):[];if(b.length>0)throw Promise.all(b);const g=m.getQueries(),E=y.find(((t,e)=>{var r,n;return(0,p.$1)({result:t,errorResetBoundary:a,useErrorBoundary:null!=(r=null==(n=v[e])?void 0:n.useErrorBoundary)&&r,query:g[e]})}));if(null!=E&&E.error)throw E.error;return y}}}]);
//# sourceMappingURL=8294.6523666e.chunk.js.map