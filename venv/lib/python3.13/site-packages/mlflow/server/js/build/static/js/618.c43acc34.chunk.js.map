{"version": 3, "file": "static/js/618.c43acc34.chunk.js", "mappings": ";qHA2EO,SAASA,EAAoBC,EAAqBC,GACvD,MAA0B,oBAAZD,EACTA,EAA4BC,GAC7BD,CACN,CAMO,SAASE,EACdC,EACAC,GAEA,OAAQJ,IACJI,EAAiBC,UAAuBC,IACjC,IACFA,EACH,CAACH,GAAMJ,EAAiBC,EAAUM,EAAYH,OAEhD,CAEN,CAIO,SAASI,EAAkCC,GAChD,OAAOA,aAAaC,QACtB,CAMO,SAASC,EACdC,EACAC,GAEA,MAAMC,EAAgB,GAEhBC,EAAWC,IACfA,EAAOC,SAAQC,IACbJ,EAAKK,KAAKD,GACV,MAAME,EAAWP,EAAYK,GACjB,MAARE,GAAAA,EAAUC,QACZN,EAAQK,EACV,GACA,EAKJ,OAFAL,EAAQH,GAEDE,CACT,CAEO,SAASQ,EACdC,EACAC,EACAC,GAMA,IACIC,EADAC,EAAc,GAGlB,MAAO,KACL,IAAIC,EACAH,EAAKrB,KAAOqB,EAAKI,QAAOD,EAAUE,KAAKC,OAE3C,MAAMC,EAAUT,IAMhB,KAHES,EAAQX,SAAWM,EAAKN,QACxBW,EAAQC,MAAK,CAACC,EAAUC,IAAkBR,EAAKQ,KAAWD,KAG1D,OAAOR,EAKT,IAAIU,EAMJ,GARAT,EAAOK,EAGHP,EAAKrB,KAAOqB,EAAKI,QAAOO,EAAaN,KAAKC,OAE9CL,EAASF,KAAMQ,GACX,MAAJP,GAAc,MAAdA,EAAMY,UAANZ,EAAMY,SAAWX,GAEbD,EAAKrB,KAAOqB,EAAKI,OACf,MAAAJ,GAAAA,EAAMI,QAAS,CACjB,MAAMS,EAAaC,KAAKC,MAAgC,KAAzBV,KAAKC,MAAQH,IAAmB,IACzDa,EAAgBF,KAAKC,MAAmC,KAA5BV,KAAKC,MAAQK,IAAsB,IAC/DM,EAAsBD,EAAgB,GAEtCE,EAAMA,CAACC,EAAsBC,KAEjC,IADAD,EAAME,OAAOF,GACNA,EAAIvB,OAASwB,GAClBD,EAAM,IAAMA,EAEd,OAAOA,CAAG,EAGZG,QAAQC,KACL,YAAML,EAAIF,EAAe,OAAOE,EAAIL,EAAY,QAChD,2FAGcC,KAAKU,IAChB,EACAV,KAAKW,IAAI,IAAM,IAAMR,EAAqB,sBAE9C,MAAAjB,OAAA,EAAAA,EAAMrB,IAEV,CAGF,OAAOsB,CAAM,CAEjB,CC3IA,SAASyB,EACPC,EACAC,EACAC,GAOuB,IAAAC,EAGvB,IAAIC,EAAoC,CACtCC,GAHmB,OAAbF,EAAGD,EAAQG,IAAEF,EAAIF,EAAOI,GAI9BJ,SACAlB,MAAOmB,EAAQnB,MACfuB,gBAAiBJ,EAAQI,cACzBC,cAAeL,EAAQK,cACvBC,MAAON,EAAQM,MACfC,WAAY,GACZC,QAAS,EACTC,QAAS,EACTC,YAAa,KACbC,eAAgBA,KACd,MAAMC,EAAwC,GAExCC,EAAiBC,IACjBA,EAAEP,YAAcO,EAAEP,WAAWxC,QAC/B+C,EAAEP,WAAWQ,IAAIF,GAEnBD,EAAY/C,KAAKiD,EAA4B,EAK/C,OAFAD,EAAcX,GAEPU,CAAW,EAEpBI,WAAYA,KAAA,CACVlB,QACAI,OAAQA,EACRH,YAQJ,OAJAD,EAAMmB,UAAUtD,SAAQuD,IACtBC,OAAOC,OAAOlB,EAAQ,MAAAgB,EAAQrB,kBAAR,EAAAqB,EAAQrB,aAAeK,EAAQJ,GAAO,IAGvDI,CACT,kIAEO,MAAMmB,EAAwB,CACnCC,YACExB,IAEO,CAGLyB,gBAAiBvD,GACf,IAAM,CACJ8B,EAAM0B,gBACN1B,EAAM2B,wBACN3B,EAAM4B,WAAWC,cAAcC,KAC/B9B,EAAM4B,WAAWC,cAAcE,SAEjC,CAACC,EAAYC,EAAaH,EAAMC,KAAU,IAAAG,EAAAC,EACxC,MAAMC,EAGc,OAHHF,EACf,MAAAJ,OAAA,EAAAA,EACIb,KAAIoB,GAAYJ,EAAYK,MAAKjF,GAAKA,EAAEgD,KAAOgC,MAChDE,OAAOC,UAAQN,EAAI,GAElBO,EAGc,OAHFN,EAChB,MAAAJ,OAAA,EAAAA,EACId,KAAIoB,GAAYJ,EAAYK,MAAKjF,GAAKA,EAAEgD,KAAOgC,MAChDE,OAAOC,UAAQL,EAAI,GAYxB,OANqBO,EACnBV,EACA,IAAII,KANgBH,EAAYM,QAChCtC,KAAe,MAAJ6B,GAAAA,EAAMa,SAAS1C,EAAOI,QAAa,MAAL0B,GAAAA,EAAOY,SAAS1C,EAAOI,UAK1BoC,GACtCzC,EAGiB,GAErB,CACEhD,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQ6C,YAAY,IAIrEC,sBAAuB9E,GACrB,IAAM,CACJ8B,EAAM0B,gBACN1B,EAAM2B,wBACN3B,EAAM4B,WAAWC,cAAcC,KAC/B9B,EAAM4B,WAAWC,cAAcE,SAEjC,CAACC,EAAYC,EAAaH,EAAMC,IAIvBW,EAAkBV,EAHzBC,EAAcA,EAAYM,QACxBtC,KAAe,MAAJ6B,GAAAA,EAAMa,SAAS1C,EAAOI,QAAa,MAAL0B,GAAAA,EAAOY,SAAS1C,EAAOI,OAEhBL,EAAO,WAE3D,CACEhD,KACE4F,EACFnE,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQ6C,YAAY,IAIrEG,oBAAqBhF,GACnB,IAAM,CACJ8B,EAAM0B,gBACN1B,EAAM2B,wBACN3B,EAAM4B,WAAWC,cAAcC,QAEjC,CAACE,EAAYC,EAAaH,KAAS,IAAAqB,EAMjC,OAAOT,EACLV,EAHkB,OAHImB,EACtB,MAAArB,OAAA,EAAAA,EACIb,KAAIoB,GAAYJ,EAAYK,MAAKjF,GAAKA,EAAEgD,KAAOgC,MAChDE,OAAOC,UAAQW,EAAI,GAKtBnD,EACA,OACD,GAEH,CACEhD,KAAK4F,EACLnE,MAAOA,KAAA,IAAA2E,EAAA,OAA4B,OAA5BA,EAAMpD,EAAME,QAAQ4C,UAAQM,EAAIpD,EAAME,QAAQ6C,YAAY,IAIrEM,qBAAsBnF,GACpB,IAAM,CACJ8B,EAAM0B,gBACN1B,EAAM2B,wBACN3B,EAAM4B,WAAWC,cAAcE,SAEjC,CAACC,EAAYC,EAAaF,KAAU,IAAAuB,EAMlC,OAAOZ,EACLV,EAHkB,OAHIsB,EACtB,MAAAvB,OAAA,EAAAA,EACId,KAAIoB,GAAYJ,EAAYK,MAAKjF,GAAKA,EAAEgD,KAAOgC,MAChDE,OAAOC,UAAQc,EAAI,GAKtBtD,EACA,QACD,GAEH,CACEhD,KAAK4F,EACLnE,MAAOA,KAAA,IAAA8E,EAAA,OAA4B,OAA5BA,EAAMvD,EAAME,QAAQ4C,UAAQS,EAAIvD,EAAME,QAAQ6C,YAAY,IAMrES,gBAAiBtF,GACf,IAAM,CAAC8B,EAAMyB,qBACbgC,GACS,IAAIA,GAAcC,WAE3B,CACE1G,KAAK4F,EACLnE,MAAOA,KAAA,IAAAkF,EAAA,OAA4B,OAA5BA,EAAM3D,EAAME,QAAQ4C,UAAQa,EAAI3D,EAAME,QAAQ6C,YAAY,IAIrEa,oBAAqB1F,GACnB,IAAM,CAAC8B,EAAMkD,yBACbO,GACS,IAAIA,GAAcC,WAE3B,CACE1G,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoF,EAAA,OAA4B,OAA5BA,EAAM7D,EAAME,QAAQ4C,UAAQe,EAAI7D,EAAME,QAAQ6C,YAAY,IAIrEe,sBAAuB5F,GACrB,IAAM,CAAC8B,EAAMgD,2BACbS,GACS,IAAIA,GAAcC,WAE3B,CACE1G,KACE4F,EACFnE,MAAOA,KAAA,IAAAsF,EAAA,OAA4B,OAA5BA,EAAM/D,EAAME,QAAQ4C,UAAQiB,EAAI/D,EAAME,QAAQ6C,YAAY,IAIrEiB,qBAAsB9F,GACpB,IAAM,CAAC8B,EAAMqD,0BACbI,GACS,IAAIA,GAAcC,WAE3B,CACE1G,KAAK4F,EACLnE,MAAOA,KAAA,IAAAwF,EAAA,OAA4B,OAA5BA,EAAMjE,EAAME,QAAQ4C,UAAQmB,EAAIjE,EAAME,QAAQ6C,YAAY,IAMrEmB,eAAgBhG,GACd,IAAM,CAAC8B,EAAMyB,qBACbgC,GACSA,EACJxC,KAAIL,GACIA,EAAYuD,UAEpBzG,QAEL,CACEV,KAAK4F,EACLnE,MAAOA,KAAA,IAAA2F,EAAA,OAA4B,OAA5BA,EAAMpE,EAAME,QAAQ4C,UAAQsB,EAAIpE,EAAME,QAAQ6C,YAAY,IAIrEsB,mBAAoBnG,GAClB,IAAM,CAAC8B,EAAMkD,yBACbpB,GACSA,EACJb,KAAIL,GACIA,EAAYuD,UAEpBzG,QAEL,CACEV,KAAK4F,EACLnE,MAAOA,KAAA,IAAA6F,EAAA,OAA4B,OAA5BA,EAAMtE,EAAME,QAAQ4C,UAAQwB,EAAItE,EAAME,QAAQ6C,YAAY,IAIrEwB,qBAAsBrG,GACpB,IAAM,CAAC8B,EAAMgD,2BACblB,GACSA,EACJb,KAAIL,GACIA,EAAYuD,UAEpBzG,QAEL,CACEV,KAAK4F,EACLnE,MAAOA,KAAA,IAAA+F,EAAA,OAA4B,OAA5BA,EAAMxE,EAAME,QAAQ4C,UAAQ0B,EAAIxE,EAAME,QAAQ6C,YAAY,IAIrE0B,oBAAqBvG,GACnB,IAAM,CAAC8B,EAAMqD,0BACbvB,GACSA,EACJb,KAAIL,GACIA,EAAYuD,UAEpBzG,QAEL,CACEV,KAAK4F,EACLnE,MAAOA,KAAA,IAAAiG,EAAA,OAA4B,OAA5BA,EAAM1E,EAAME,QAAQ4C,UAAQ4B,EAAI1E,EAAME,QAAQ6C,YAAY,IAMrE4B,qBAAsBzG,GACpB,IAAM,CAAC8B,EAAMuE,0BACbK,GACSA,EAAYrC,QAAOnC,IAAM,IAAAyE,EAAA,QAAsB,OAAlBA,EAACzE,EAAOK,aAAPoE,EAAmB5G,OAAO,KAEjE,CACEjB,KAAK4F,EACLnE,MAAOA,KAAA,IAAAqG,EAAA,OAA4B,OAA5BA,EAAM9E,EAAME,QAAQ4C,UAAQgC,EAAI9E,EAAME,QAAQ6C,YAAY,IAIrEgC,mBAAoB7G,GAClB,IAAM,CAAC8B,EAAMqE,wBACbO,GACSA,EAAYrC,QAAOnC,IAAM,IAAA4E,EAAA,QAAsB,OAAlBA,EAAC5E,EAAOK,aAAPuE,EAAmB/G,OAAO,KAEjE,CACEjB,KAAK4F,EACLnE,MAAOA,KAAA,IAAAwG,EAAA,OAA4B,OAA5BA,EAAMjF,EAAME,QAAQ4C,UAAQmC,EAAIjF,EAAME,QAAQ6C,YAAY,IAIrEmC,oBAAqBhH,GACnB,IAAM,CAAC8B,EAAMyE,yBACbG,GACSA,EAAYrC,QAAOnC,IAAM,IAAA+E,EAAA,QAAsB,OAAlBA,EAAC/E,EAAOK,aAAP0E,EAAmBlH,OAAO,KAEjE,CACEjB,KAAK4F,EACLnE,MAAOA,KAAA,IAAA2G,EAAA,OAA4B,OAA5BA,EAAMpF,EAAME,QAAQ4C,UAAQsC,EAAIpF,EAAME,QAAQ6C,YAAY,IAIrElC,eAAgB3C,GACd,IAAM,CACJ8B,EAAMkD,sBACNlD,EAAMgD,wBACNhD,EAAMqD,0BAER,CAACvB,EAAMuD,EAAQtD,KAAU,IAAAuD,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACvB,MAAO,IACe,OAApBL,EAAI,OAAJC,EAAIzD,EAAK,SAAL,EAAAyD,EAASpB,SAAOmB,EAAI,MACF,OAAtBE,EAAI,OAAJC,EAAIJ,EAAO,SAAP,EAAAI,EAAWtB,SAAOqB,EAAI,MACL,OAArBE,EAAY,OAAZC,EAAI5D,EAAM,SAAE,EAAR4D,EAAUxB,SAAOuB,EAAI,IAExBzE,KAAIb,GACIA,EAAOS,mBAEfnD,MAAM,GAEX,CACEV,KAAK4F,EACLnE,MAAOA,KAAA,IAAAmH,EAAA,OAA4B,OAA5BA,EAAM5F,EAAME,QAAQ4C,UAAQ8C,EAAI5F,EAAME,QAAQ6C,YAAY,OAOpE,SAASL,EACdV,EACA6D,EACA7F,EACA8F,GACA,IAAAC,EAAAC,EAOA,IAAIC,EAAW,EAEf,MAAMC,EAAe,SAACC,EAAmC3F,QAAK,IAALA,IAAAA,EAAQ,GAC/DyF,EAAW9G,KAAKU,IAAIoG,EAAUzF,GAE9B2F,EACG5D,QAAOtC,GAAUA,EAAOmG,iBACxBvI,SAAQoC,IAAU,IAAAoG,EACb,OAAJA,EAAIpG,EAAOkG,UAAPE,EAAgBpI,QAClBiI,EAAajG,EAAOkG,QAAS3F,EAAQ,EACvC,GACC,IAGP0F,EAAalE,GAEb,IAAIyB,EAAqC,GAEzC,MAAM6C,EAAoBA,CACxBC,EACA/F,KAGA,MAAMI,EAAkC,CACtCJ,QACAH,GAAI,CAACyF,EAAe,GAAEtF,KAAS+B,OAAOC,SAASgE,KAAK,KACpDrC,QAAS,IAILsC,EAAiD,GAGvDF,EAAe1I,SAAQ6I,IAGrB,MAAMC,EAA4B,IAAIF,GAAsB/C,UAAU,GAItE,IAAIzD,EACAK,GAAgB,EAWpB,GAdqBoG,EAAczG,OAAOO,QAAUI,EAAYJ,OAK5CkG,EAAczG,OAAO2G,OAEvC3G,EAASyG,EAAczG,OAAO2G,QAG9B3G,EAASyG,EAAczG,OACvBK,GAAgB,GAIhBqG,IACyB,MAAzBA,OAAyB,EAAzBA,EAA2B1G,UAAWA,EAGtC0G,EAA0BlG,WAAW1C,KAAK2I,OACrC,CAEL,MAAMtG,EAASL,EAAaC,EAAOC,EAAQ,CACzCI,GAAI,CAACyF,EAActF,EAAOP,EAAOI,GAAiB,MAAbqG,OAAa,EAAbA,EAAerG,IACjDkC,OAAOC,SACPgE,KAAK,KACRlG,gBACAC,cAAeD,EACV,GAAEmG,EAAqBlE,QAAOlF,GAAKA,EAAE4C,SAAWA,IAAQhC,cACzD4I,EACJrG,QACAzB,MAAO0H,EAAqBxI,SAI9BmC,EAAOK,WAAW1C,KAAK2I,GAGvBD,EAAqB1I,KAAKqC,EAC5B,CAEAQ,EAAYuD,QAAQpG,KAAK2I,GACzBA,EAAc9F,YAAcA,CAAW,IAGzC6C,EAAa1F,KAAK6C,GAEdJ,EAAQ,GACV8F,EAAkBG,EAAsBjG,EAAQ,EAClD,EAGIsG,EAAgBjB,EAAe5E,KAAI,CAAChB,EAAQlB,IAChDgB,EAAaC,EAAOC,EAAQ,CAC1BO,MAAOyF,EACPlH,YAIJuH,EAAkBQ,EAAeb,EAAW,GAE5CxC,EAAaC,UAMb,MAAMqD,EACJ5C,GAEwBA,EAAQ5B,QAAOnC,GACrCA,EAAOH,OAAOmG,iBAGOnF,KAAIb,IACzB,IAAIM,EAAU,EACVC,EAAU,EACVqG,EAAgB,CAAC,GAEjB5G,EAAOK,YAAcL,EAAOK,WAAWxC,QACzC+I,EAAgB,GAEhBD,EAAuB3G,EAAOK,YAAY5C,SACxCoJ,IAAsD,IAAnDvG,QAASwG,EAAcvG,QAASwG,GAAcF,EAC/CvG,GAAWwG,EACXF,EAAcjJ,KAAKoJ,EAAa,KAIpCzG,EAAU,EASZ,OALAC,GADwBxB,KAAKW,OAAOkH,GAGpC5G,EAAOM,QAAUA,EACjBN,EAAOO,QAAUA,EAEV,CAAED,UAASC,UAAS,IAM/B,OAFAoG,EAA+C,OAAzBhB,EAAgB,OAAhBC,EAACvC,EAAa,SAAE,EAAfuC,EAAiB7B,SAAO4B,EAAI,IAE5CtC,CACT,CCzdO,MAAM2D,EAAsB,CACjCC,KAAM,IACNC,QAAS,GACTC,QAASC,OAAOC,kBAYLC,EAA6B,CACxCC,oBAAqBA,IACZP,EAETQ,gBAAkBC,IACT,CACLC,aAAc,CAAC,EACfC,iBAhBkC,CACtCC,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,OAWZR,IAIPS,kBACEtI,IAEO,CACLuI,iBAAkB,QAClBC,qBAAsBzL,EAAiB,eAAgBiD,GACvDyI,yBAA0B1L,EAAiB,mBAAoBiD,KAInE0I,aAAcA,CACZzI,EACAD,KAEO,CACL2I,QAASA,KAAM,IAAAC,EAAA3B,EAAA4B,EACb,MAAMC,EAAa9I,EAAM4B,WAAWkG,aAAa7H,EAAOI,IAExD,OAAOlB,KAAKW,IACVX,KAAKU,IACqB,OADlB+I,EACN3I,EAAO8I,UAAUzB,SAAOsB,EAAIxB,EAAoBE,QACb,OADoBL,EACvD,MAAA6B,EAAAA,EAAc7I,EAAO8I,UAAU1B,MAAIJ,EAAIG,EAAoBC,MAErC,OADvBwB,EACD5I,EAAO8I,UAAUxB,SAAOsB,EAAIzB,EAAoBG,QACjD,EAEHyB,SAAUC,IACR,MAAM9C,EAAW8C,EAEA,SAAbA,EACAjJ,EAAMkJ,4BACNlJ,EAAMmJ,6BAHNnJ,EAAM2B,wBAKJ5C,EAAQoH,EAAQiD,WAAU/L,GAAKA,EAAEgD,KAAOJ,EAAOI,KAErD,GAAItB,EAAQ,EAAG,CACb,MAAMsK,EAAoBlD,EAAQpH,EAAQ,GAE1C,OACEsK,EAAkBL,SAASC,GAAYI,EAAkBV,SAE7D,CAEA,OAAO,CAAC,EAEVW,UAAWA,KACTtJ,EAAMuJ,iBAAgBC,IAAiC,IAA9B,CAACvJ,EAAOI,IAAKoJ,KAAMC,GAAMF,EAChD,OAAOE,CAAI,GACX,EAEJC,aAAcA,KAAM,IAAAC,EAAAC,EAClB,OACkC,OAAhCD,EAAC3J,EAAO8I,UAAUe,iBAAcF,KACG,OADKC,EACvC7J,EAAME,QAAQ6J,uBAAoBF,EAAS,EAGhDG,cAAeA,IACNhK,EAAM4B,WAAWmG,iBAAiBK,mBAAqBnI,EAAOI,KAK3EN,aAAcA,CACZK,EACAJ,KAEO,CACL2I,QAASA,KACP,IAAIsB,EAAM,EAEV,MAAMtM,EAAWyC,IAGR,IAAA8J,EAFH9J,EAAOK,WAAWxC,OACpBmC,EAAOK,WAAW5C,QAAQF,GAE1BsM,GAA8B,OAA3BC,EAAI9J,EAAOH,OAAO0I,WAASuB,EAAI,CACpC,EAKF,OAFAvM,EAAQyC,GAED6J,CAAG,EAEZjB,SAAUA,KACR,GAAI5I,EAAOrB,MAAQ,EAAG,CACpB,MAAMoL,EACJ/J,EAAOQ,YAAYuD,QAAQ/D,EAAOrB,MAAQ,GAC5C,OAAOoL,EAAkBnB,WAAamB,EAAkBxB,SAC1D,CAEA,OAAO,CAAC,EAEVyB,iBAAkBA,KAChB,MAAMnK,EAASD,EAAMqK,UAAUjK,EAAOH,OAAOI,IACvCiK,EAAkB,MAANrK,OAAM,EAANA,EAAQ0J,eAE1B,OAAQY,IACN,IAAKtK,IAAWqK,EACd,OAKF,GAFmB,MAAjBC,EAAUC,SAAVD,EAAUC,UAERC,EAAkBF,IAEhBA,EAAEG,SAAWH,EAAEG,QAAQzM,OAAS,EAClC,OAIJ,MAAMgK,EAAY7H,EAAOuI,UAEnBN,EAAwCjI,EAC1CA,EACGS,iBACAI,KAAI5D,GAAK,CAACA,EAAE4C,OAAOI,GAAIhD,EAAE4C,OAAO0I,aACnC,CAAC,CAAC1I,EAAOI,GAAIJ,EAAO0I,YAElBgC,EAAUF,EAAkBF,GAC9BpL,KAAKC,MAAMmL,EAAEG,QAAQ,GAAIC,SACxBJ,EAAiBI,QAEhBC,EAAqC,CAAC,EAEtCC,EAAeA,CACnBC,EACAC,KAE0B,kBAAfA,IAIX/K,EAAMgL,qBAAoB7N,IAAO,IAAA8N,EAAAC,EAC/B,MAAMhD,EAAc6C,GAA8B,OAApBE,EAAI,MAAA9N,OAAA,EAAAA,EAAK6K,aAAWiD,EAAI,GAChD9C,EAAkBhJ,KAAKU,IAC3BqI,GAA6B,OAAlBgD,EAAO,MAAH/N,OAAG,EAAHA,EAAK8K,WAASiD,EAAI,IAChC,SAUH,OAPA/N,EAAIkL,kBAAkBxK,SAAQsN,IAA4B,IAA1B9I,EAAU+I,GAAWD,EACnDP,EAAgBvI,GACdlD,KAAKC,MACsD,IAAzDD,KAAKU,IAAIuL,EAAaA,EAAajD,EAAiB,IAClD,GAAG,IAGJ,IACFhL,EACH+K,cACAC,kBACD,IAIkC,aAAnCnI,EAAME,QAAQqI,kBACA,QAAduC,GAEA9K,EAAMuJ,iBAAgBpM,IAAO,IACxBA,KACAyN,MAEP,EAGIS,EAAUN,GACdF,EAAa,OAAQE,GAEjBO,EAASP,IACbF,EAAa,MAAOE,GAEpB/K,EAAMgL,qBAAoB7N,IAAO,IAC5BA,EACHiL,kBAAkB,EAClBJ,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBE,kBAAmB,MAClB,EAGCkD,EAAc,CAClBC,YAAcjB,GAAkBc,EAAOd,EAAEI,SACzCc,UAAYlB,IACVmB,SAASC,oBAAoB,YAAaJ,EAAYC,aACtDE,SAASC,oBAAoB,UAAWJ,EAAYE,WACpDH,EAAMf,EAAEI,QAAQ,GAIdiB,EAAc,CAClBJ,YAAcjB,IACRA,EAAEsB,aACJtB,EAAEuB,iBACFvB,EAAEwB,mBAEJV,EAAOd,EAAEG,QAAQ,GAAIC,UACd,GAETc,UAAYlB,IAAkB,IAAAyB,EAC5BN,SAASC,oBAAoB,YAAaC,EAAYJ,aACtDE,SAASC,oBAAoB,WAAYC,EAAYH,WACjDlB,EAAEsB,aACJtB,EAAEuB,iBACFvB,EAAEwB,mBAEJT,EAAkB,OAAbU,EAACzB,EAAEG,QAAQ,SAAE,EAAZsB,EAAcrB,QAAQ,GAI1BsB,IAmFT,WACL,GAAgC,mBAArBC,EAAgC,OAAOA,EAElD,IAAIC,GAAY,EAChB,IACE,MAAMjM,EAAU,CACd,WAAIkM,GAEF,OADAD,GAAY,GACL,CACT,GAGIE,EAAOA,OAEbC,OAAOC,iBAAiB,OAAQF,EAAMnM,GACtCoM,OAAOX,oBAAoB,OAAQU,GACnC,MAAOG,GACPL,GAAY,CACd,CAEA,OADAD,EAAmBC,EACZD,CACT,CAxGqCO,IACvB,CAAEL,SAAS,GAGX3B,EAAkBF,IACpBmB,SAASa,iBACP,YACAX,EAAYJ,YACZS,GAEFP,SAASa,iBACP,WACAX,EAAYH,UACZQ,KAGFP,SAASa,iBACP,YACAhB,EAAYC,YACZS,GAEFP,SAASa,iBACP,UACAhB,EAAYE,UACZQ,IAIJjM,EAAMgL,qBAAoB7N,IAAO,IAC5BA,EACH6K,YAAa2C,EACb1C,YACAC,YAAa,EACbC,gBAAiB,EACjBE,oBACAD,iBAAkBnI,EAAOI,MACxB,CACJ,IAKPmB,YACExB,IAEO,CACLuJ,gBAAiB1M,GAAW,MAAAmD,EAAME,QAAQsI,0BAAd,EAAAxI,EAAME,QAAQsI,qBAAuB3L,GACjEmO,oBAAqBnO,GACnB,MAAAmD,EAAME,QAAQuI,8BAAd,EAAAzI,EAAME,QAAQuI,yBAA2B5L,GAC3C6P,kBAAmBC,IAAgB,IAAAC,EACjC5M,EAAMuJ,gBACJoD,EAAe,CAAC,EAAmC,OAAlCC,EAAG5M,EAAM6M,aAAa/E,cAAY8E,EAAI,CAAC,EACzD,EAEHE,oBAAqBH,IAAgB,IAAAI,EACnC/M,EAAMgL,oBACJ2B,EA9R8B,CACtC3E,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,IA0R4B,OADF0E,EACjC/M,EAAM6M,aAAa9E,kBAAgBgF,EAhST,CACtC/E,YAAa,KACbC,UAAW,KACXC,YAAa,KACbC,gBAAiB,KACjBC,kBAAkB,EAClBC,kBAAmB,IA4RZ,EAEH2E,aAAcA,KAAA,IAAAC,EAAAC,EAAA,OAGP,OAHOD,EACZ,OADYC,EACZlN,EAAMyB,kBAAkB,SAAxB,EAAAyL,EAA4B/I,QAAQgJ,QAAO,CAAClD,EAAK7J,IACxC6J,EAAM7J,EAAOuI,WACnB,IAAEsE,EAAI,CAAC,EACZG,iBAAkBA,KAAA,IAAAC,EAAAC,EAAA,OAGX,OAHWD,EAChB,OADgBC,EAChBtN,EAAMkD,sBAAsB,SAA5B,EAAAoK,EAAgCnJ,QAAQgJ,QAAO,CAAClD,EAAK7J,IAC5C6J,EAAM7J,EAAOuI,WACnB,IAAE0E,EAAI,CAAC,EACZE,mBAAoBA,KAAA,IAAAC,EAAAC,EAAA,OAGb,OAHaD,EAClB,OADkBC,EAClBzN,EAAMgD,wBAAwB,SAA9B,EAAAyK,EAAkCtJ,QAAQgJ,QAAO,CAAClD,EAAK7J,IAC9C6J,EAAM7J,EAAOuI,WACnB,IAAE6E,EAAI,CAAC,EACZE,kBAAmBA,KAAA,IAAAC,EAAAC,EAAA,OAGZ,OAHYD,EACjB,OADiBC,EACjB5N,EAAMqD,uBAAuB,SAA7B,EAAAuK,EAAiCzJ,QAAQgJ,QAAO,CAAClD,EAAK7J,IAC7C6J,EAAM7J,EAAOuI,WACnB,IAAEgF,EAAI,CAAC,KAKlB,IAAIzB,EAAmC,KAwBvC,SAASzB,EAAkBF,GACzB,MAAkC,eAA1BA,EAAiBsD,IAC3B,CClXO,MAAMC,EAA0B,CACrClG,gBAAkBC,IACT,CACLkG,SAAU,CAAC,KACRlG,IAIPS,kBACEtI,IAEO,CACLgO,iBAAkBjR,EAAiB,WAAYiD,GAC/CiO,sBAAsB,IAI1BzM,YACExB,IAEA,IAAIkO,GAAa,EACbC,GAAS,EAEb,MAAO,CACLC,mBAAoBA,KAAM,IAAAnH,EAAAoH,EACxB,GAAKH,GAOL,GAEiC,OAFjCjH,EAC4B,OAD5BoH,EACErO,EAAME,QAAQoO,cAAYD,EAC1BrO,EAAME,QAAQqO,mBAAiBtH,GAC9BjH,EAAME,QAAQsO,gBACf,CACA,GAAIL,EAAQ,OACZA,GAAS,EACTnO,EAAMyO,QAAO,KACXzO,EAAM0O,gBACNP,GAAS,CAAK,GAElB,OAjBEnO,EAAMyO,QAAO,KACXP,GAAa,CAAI,GAgBrB,EAEFS,YAAa9R,GAAW,MAAAmD,EAAME,QAAQ8N,sBAAd,EAAAhO,EAAME,QAAQ8N,iBAAmBnR,GACzD+R,sBAAuBb,KACjB,MAAAA,EAAAA,GAAa/N,EAAM6O,wBACrB7O,EAAM2O,aAAY,GAElB3O,EAAM2O,YAAY,CAAC,EACrB,EAEFD,cAAe/B,IAAgB,IAAAmC,EAAAC,EAC7B/O,EAAM2O,YACJhC,EAAe,CAAC,EAAgC,OAA/BmC,EAAG,OAAHC,EAAG/O,EAAM6M,mBAAN,EAAAkC,EAAoBhB,UAAQe,EAAI,CAAC,EACtD,EAEHE,qBAAsBA,IACbhP,EACJiP,2BACAC,SAASrQ,MAAKsQ,GAAOA,EAAIC,iBAE9BC,gCAAiCA,IACvB9E,IACa,MAAjBA,EAAUC,SAAVD,EAAUC,UACZxK,EAAM4O,uBAAuB,EAGjCU,sBAAuBA,KACrB,MAAMvB,EAAW/N,EAAM4B,WAAWmM,SAClC,OAAoB,IAAbA,GAAqB1M,OAAOkO,OAAOxB,GAAUlP,KAAK2D,QAAQ,EAEnEqM,qBAAsBA,KACpB,MAAMd,EAAW/N,EAAM4B,WAAWmM,SAGlC,MAAwB,mBAAbA,GACW,IAAbA,IAGJ1M,OAAOmO,KAAKzB,GAAU9P,SAKvB+B,EAAMyP,cAAcP,SAASrQ,MAAKsQ,IAAQA,EAAIO,iBAKvC,EAEbC,iBAAkBA,KAChB,IAAI1J,EAAW,EAYf,QATgC,IAA9BjG,EAAM4B,WAAWmM,SACb1M,OAAOmO,KAAKxP,EAAMyP,cAAcG,UAChCvO,OAAOmO,KAAKxP,EAAM4B,WAAWmM,WAE5BlQ,SAAQwC,IACb,MAAMwP,EAAUxP,EAAGyP,MAAM,KACzB7J,EAAW9G,KAAKU,IAAIoG,EAAU4J,EAAQ5R,OAAO,IAGxCgI,CAAQ,EAEjB8J,uBAAwBA,IAAM/P,EAAMgQ,oBACpCC,oBAAqBA,MACdjQ,EAAMkQ,sBAAwBlQ,EAAME,QAAQ+P,sBAC/CjQ,EAAMkQ,qBAAuBlQ,EAAME,QAAQ+P,oBAAoBjQ,IAG7DA,EAAME,QAAQsO,kBAAoBxO,EAAMkQ,qBACnClQ,EAAM+P,yBAGR/P,EAAMkQ,wBAEhB,EAGHC,UAAWA,CACThB,EACAnP,KAEO,CACLoQ,eAAgBrC,IACd/N,EAAM2O,aAAYxR,IAAO,IAAAkT,EACvB,MAAMC,GAAiB,IAARnT,KAAwB,MAAAA,IAAAA,EAAMgS,EAAI9O,KAEjD,IAAIkQ,EAAiC,CAAC,EAYtC,IAVY,IAARpT,EACFkE,OAAOmO,KAAKxP,EAAMyP,cAAcG,UAAU/R,SAAQ2S,IAChDD,EAAYC,IAAS,CAAI,IAG3BD,EAAcpT,EAGhB4Q,EAAmB,OAAXsC,EAAGtC,GAAQsC,GAAKC,GAEnBA,GAAUvC,EACb,MAAO,IACFwC,EACH,CAACpB,EAAI9O,KAAK,GAId,GAAIiQ,IAAWvC,EAAU,CACvB,MAAQ,CAACoB,EAAI9O,IAAKoJ,KAAMC,GAAS6G,EACjC,OAAO7G,CACT,CAEA,OAAOvM,CAAG,GACV,EAEJuS,cAAeA,KAAM,IAAAe,EACnB,MAAM1C,EAAW/N,EAAM4B,WAAWmM,SAElC,SACuC,OAD/B0C,EACN,MAAAzQ,EAAME,QAAQwQ,sBAAd,EAAA1Q,EAAME,QAAQwQ,iBAAmBvB,IAAIsB,GACvB,IAAb1C,IAA6B,MAARA,OAAQ,EAARA,EAAWoB,EAAI9O,KACtC,EAEH+O,aAAcA,KAAM,IAAAuB,EAAA9G,EAAA+G,EAClB,OACsC,OADtCD,EACE,MAAA3Q,EAAME,QAAQ2Q,qBAAd,EAAA7Q,EAAME,QAAQ2Q,gBAAkB1B,IAAIwB,GACL,OAA9B9G,EAAC7J,EAAME,QAAQ4Q,kBAAejH,MAAe,OAAD+G,EAACzB,EAAI4B,WAAJH,EAAa3S,OAAO,EAGtE+S,yBAA0BA,KACxB,MAAMC,EAAY9B,EAAIC,eAEtB,MAAO,KACA6B,GACL9B,EAAIiB,gBAAgB,CACrB,KC/NHc,EAAgCA,CACpC/B,EACA9M,EACA8O,KACG,IAAAC,EAAAC,EAAAC,EACH,MAAMC,EAASJ,EAAYK,cAC3B,OAAOhP,QAE+B,OAFxB4O,EACZjC,EACGsC,SAAwBpP,KACb,OADsBgP,EADpCD,EAEIM,aACa,OADHJ,EAFdD,EAGIG,oBAFgC,EADpCF,EAII3O,SAAS4O,GACd,EAGHL,EAAeS,WAAcC,GAAaC,EAAWD,GAErD,MAAME,EAAyCA,CAC7C3C,EACA9M,EACA8O,KACG,IAAAY,EAAAC,EACH,OAAOxP,QACgC,OADzBuP,EACZ5C,EAAIsC,SAAwBpP,KAAqB,OAAZ2P,EAArCD,EAAuCL,iBAAF,EAArCM,EAAmDrP,SAASwO,GAC7D,EAGHW,EAAwBH,WAAcC,GAAaC,EAAWD,GAE9D,MAAMK,EAA8BA,CAClC9C,EACA9M,EACA8O,KACG,IAAAe,EAAAC,EACH,OACuC,OAArCD,EAAA/C,EAAIsC,SAAwBpP,KAA5B,OAAqC8P,EAArCD,EAAuCR,iBAAF,EAArCS,EAAmDX,kBACnD,MAAAL,OAAA,EAAAA,EAAaK,cAAa,EAI9BS,EAAaN,WAAcC,GAAaC,EAAWD,GAEnD,MAAMQ,EAA6BA,CACjCjD,EACA9M,EACA8O,KACG,IAAAkB,EACH,OAAwC,OAAxCA,EAAOlD,EAAIsC,SAAoBpP,SAAS,EAAjCgQ,EAAmC1P,SAASwO,EAAY,EAGjEiB,EAAYT,WAAcC,GAAaC,EAAWD,MAAS,MAAAA,GAAAA,EAAK3T,QAEhE,MAAMqU,EAAgCA,CACpCnD,EACA9M,EACA8O,KAEQA,EAAYtS,MAClB+S,IAAG,IAAAW,EAAA,QAAsC,OAAlCA,EAACpD,EAAIsC,SAAoBpP,KAAxBkQ,EAAmC5P,SAASiP,GAAI,IAI5DU,EAAeX,WAAcC,GAAaC,EAAWD,MAAS,MAAAA,GAAAA,EAAK3T,QAEnE,MAAMuU,EAAiCA,CACrCrD,EACA9M,EACA8O,IAEOA,EAAYtS,MAAK+S,IAAG,IAAAa,EAAA,OACQ,OADRA,EACzBtD,EAAIsC,SAAoBpP,SAAS,EAAjCoQ,EAAmC9P,SAASiP,EAAI,IAIpDY,EAAgBb,WAAcC,GAAaC,EAAWD,MAAS,MAAAA,GAAAA,EAAK3T,QAEpE,MAAMyU,EAAwBA,CAACvD,EAAK9M,EAAkB8O,IAC7ChC,EAAIsC,SAASpP,KAAc8O,EAGpCuB,EAAOf,WAAcC,GAAaC,EAAWD,GAE7C,MAAMe,EAA4BA,CAChCxD,EACA9M,EACA8O,IAEOhC,EAAIsC,SAASpP,IAAa8O,EAGnCwB,EAAWhB,WAAcC,GAAaC,EAAWD,GAEjD,MAAMgB,EAA+BA,CACnCzD,EACA9M,EACA8O,KAEA,IAAKrR,EAAKD,GAAOsR,EAEjB,MAAM0B,EAAW1D,EAAIsC,SAAiBpP,GACtC,OAAOwQ,GAAY/S,GAAO+S,GAAYhT,CAAG,EAG3C+S,EAAcE,mBAAsBlB,IAClC,IAAKmB,EAAWC,GAAapB,EAEzBqB,EACmB,kBAAdF,EAAyBG,WAAWH,GAAuBA,EAChEI,EACmB,kBAAdH,EAAyBE,WAAWF,GAAuBA,EAEhElT,EACY,OAAdiT,GAAsBvL,OAAO4L,MAAMH,IAAcI,IAAWJ,EAC1DpT,EAAoB,OAAdmT,GAAsBxL,OAAO4L,MAAMD,GAAaE,IAAWF,EAErE,GAAIrT,EAAMD,EAAK,CACb,MAAMyT,EAAOxT,EACbA,EAAMD,EACNA,EAAMyT,CACR,CAEA,MAAO,CAACxT,EAAKD,EAAI,EAGnB+S,EAAcjB,WAAcC,GAC1BC,EAAWD,IAASC,EAAWD,EAAI,KAAOC,EAAWD,EAAI,IAIpD,MAAM2B,EAAY,CACvBrC,iBACAY,0BACAG,eACAG,cACAE,iBACAE,kBACAE,SACAC,aACAC,iBAOF,SAASf,EAAWD,GAClB,YAAe/K,IAAR+K,GAA6B,OAARA,GAAwB,KAARA,CAC9C,CC0TO,SAAS4B,EACdC,EACAC,EACAzT,GAEA,SACGwT,IAAYA,EAAS9B,aAClB8B,EAAS9B,WAAW+B,EAAOzT,IAEd,qBAAVyT,GACW,kBAAVA,IAAuBA,CAEnC,CCxdA,MA2GaC,EAAiB,CAC5B1J,IA5G8BA,CAAC5H,EAAUuR,EAAWC,IAG7CA,EAAU1G,QAAO,CAAClD,EAAK6J,KAC5B,MAAMC,EAAYD,EAAKrC,SAASpP,GAChC,OAAO4H,GAA4B,kBAAd8J,EAAyBA,EAAY,EAAE,GAC3D,GAuGHjU,IApG8BA,CAACuC,EAAUuR,EAAWC,KACpD,IAAI/T,EAaJ,OAXA+T,EAAUhW,SAAQsR,IAChB,MAAMuE,EAAQvE,EAAIsC,SAAiBpP,GAGxB,MAATqR,IACC5T,EAAO4T,QAAkB7M,IAAR/G,GAAqB4T,GAASA,KAEhD5T,EAAM4T,EACR,IAGK5T,CAAG,EAuFVD,IApF8BA,CAACwC,EAAUuR,EAAWC,KACpD,IAAIhU,EAYJ,OAVAgU,EAAUhW,SAAQsR,IAChB,MAAMuE,EAAQvE,EAAIsC,SAAiBpP,GAExB,MAATqR,IACC7T,EAAO6T,QAAkB7M,IAARhH,GAAqB6T,GAASA,KAEhD7T,EAAM6T,EACR,IAGK7T,CAAG,EAwEVmU,OArEiCA,CAAC3R,EAAUuR,EAAWC,KACvD,IAAI/T,EACAD,EAcJ,OAZAgU,EAAUhW,SAAQsR,IAChB,MAAMuE,EAAQvE,EAAIsC,SAAiBpP,GACtB,MAATqR,SACU7M,IAAR/G,EACE4T,GAASA,IAAO5T,EAAMD,EAAM6T,IAE5B5T,EAAM4T,IAAO5T,EAAM4T,GACnB7T,EAAO6T,IAAO7T,EAAM6T,IAE5B,IAGK,CAAC5T,EAAKD,EAAI,EAsDjBoU,KAnD+BA,CAAC5R,EAAU6R,KAC1C,IAAIC,EAAQ,EACRlK,EAAM,EASV,GAPAiK,EAASrW,SAAQsR,IACf,IAAIuE,EAAQvE,EAAIsC,SAAiBpP,GACpB,MAATqR,IAAkBA,GAASA,IAAUA,MACrCS,EAAQlK,GAAOyJ,EACnB,IAGES,EAAO,OAAOlK,EAAMkK,CAElB,EAuCNC,OApCiCA,CAAC/R,EAAU6R,KAC5C,IAAKA,EAASjW,OACZ,OAGF,MAAMsR,EAAS2E,EAASjT,KAAIkO,GAAOA,EAAIsC,SAASpP,KAChD,GNmB4BhF,EMnBTkS,GNoBZ8E,MAAMC,QAAQjX,KAAMA,EAAEkX,OAAM3C,GAAsB,kBAARA,IMnB/C,ONkBG,IAAuBvU,EMhB5B,GAAsB,IAAlBkS,EAAOtR,OACT,OAAOsR,EAAO,GAGhB,MAAMiF,EAAMrV,KAAKsV,MAAMlF,EAAOtR,OAAS,GACjCyW,EAAOnF,EAAOoF,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IACvC,OAAOtF,EAAOtR,OAAS,IAAM,EAAIyW,EAAKF,IAAQE,EAAKF,EAAM,GAAME,EAAKF,IAAS,CAAC,EAsB9EM,OAnBiCA,CAACzS,EAAU6R,IACrCG,MAAMU,KAAK,IAAIC,IAAId,EAASjT,KAAI5D,GAAKA,EAAEoU,SAASpP,MAAYkN,UAmBnE0F,YAhBsCA,CAAC5S,EAAU6R,IAC1C,IAAIc,IAAId,EAASjT,KAAI5D,GAAKA,EAAEoU,SAASpP,MAAYgF,KAgBxD8M,MAbgCA,CAACe,EAAWhB,IACrCA,EAASjW,QC5EX,MC2BMkX,EAA6B,CACxCvN,gBAAkBC,IACT,CACLuN,aAAc,CAAC,KACZvN,IAIPS,kBACEtI,IAEO,CACLqV,qBAAsBtY,EAAiB,eAAgBiD,GACvDsV,oBAAoB,EACpBC,yBAAyB,EACzBC,uBAAuB,IAO3BhU,YACExB,IAEO,CACLyV,gBAAiB5Y,GAAW,MAAAmD,EAAME,QAAQmV,0BAAd,EAAArV,EAAME,QAAQmV,qBAAuBxY,GACjE6Y,kBAAmB/I,IAAY,IAAAgJ,EAAA,OAC7B3V,EAAMyV,gBACJ9I,EAAe,CAAC,EAAmC,OAAlCgJ,EAAG3V,EAAM6M,aAAauI,cAAYO,EAAI,CAAC,EACzD,EACHC,sBAAuBlC,IACrB1T,EAAMyV,iBAAgBtY,IACpBuW,EACmB,qBAAVA,EAAwBA,GAAS1T,EAAM6V,uBAEhD,MAAMT,EAAe,IAAKjY,GAEpB2Y,EAAqB9V,EAAM+V,wBAAwB7G,SAiBzD,OAbIwE,EACFoC,EAAmBjY,SAAQsR,IACpBA,EAAI6G,iBAGTZ,EAAajG,EAAI9O,KAAM,EAAI,IAG7ByV,EAAmBjY,SAAQsR,WAClBiG,EAAajG,EAAI9O,GAAG,IAIxB+U,CAAY,GACnB,EAEJa,0BAA2BvC,GACzB1T,EAAMyV,iBAAgBtY,IACpB,MAAM+Y,EACa,qBAAVxC,EACHA,GACC1T,EAAMmW,2BAEPf,EAAkC,IAAKjY,GAM7C,OAJA6C,EAAMyP,cAAc2G,KAAKvY,SAAQsR,IAC/BkH,EAAoBjB,EAAcjG,EAAI9O,GAAI6V,EAAelW,EAAM,IAG1DoV,CAAY,IA6DvBkB,uBAAwBA,IAAMtW,EAAMuW,kBACpCC,oBAAqBtY,GACnB,IAAM,CAAC8B,EAAM4B,WAAWwT,aAAcpV,EAAMuW,qBAC5C,CAACnB,EAAcqB,IACRpV,OAAOmO,KAAK4F,GAAcnX,OAQxByY,EAAa1W,EAAOyW,GAPlB,CACLL,KAAM,GACNlH,SAAU,GACVU,SAAU,CAAC,IAMjB,CACE5S,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQyW,UAAU,IAInEC,4BAA6B1Y,GAC3B,IAAM,CAAC8B,EAAM4B,WAAWwT,aAAcpV,EAAM6W,yBAC5C,CAACzB,EAAcqB,IACRpV,OAAOmO,KAAK4F,GAAcnX,OAQxByY,EAAa1W,EAAOyW,GAPlB,CACLL,KAAM,GACNlH,SAAU,GACVU,SAAU,CAAC,IAMjB,CACE5S,IAEE,8BACFyB,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQyW,UAAU,IAInEG,2BAA4B5Y,GAC1B,IAAM,CAAC8B,EAAM4B,WAAWwT,aAAcpV,EAAMgQ,uBAC5C,CAACoF,EAAcqB,IACRpV,OAAOmO,KAAK4F,GAAcnX,OAQxByY,EAAa1W,EAAOyW,GAPlB,CACLL,KAAM,GACNlH,SAAU,GACVU,SAAU,CAAC,IAMjB,CACE5S,IAEE,6BACFyB,MAAOA,KAAA,IAAA2E,EAAA,OAA4B,OAA5BA,EAAMpD,EAAME,QAAQ4C,UAAQM,EAAIpD,EAAME,QAAQyW,UAAU,IAoBnEd,qBAAsBA,KACpB,MAAMC,EAAqB9V,EAAM6W,sBAAsB3H,UACjD,aAAEkG,GAAiBpV,EAAM4B,WAE/B,IAAImV,EAAoBvU,QACtBsT,EAAmB7X,QAAUoD,OAAOmO,KAAK4F,GAAcnX,QAazD,OAVI8Y,GAEAjB,EAAmBjX,MACjBsQ,GAAOA,EAAI6G,iBAAmBZ,EAAajG,EAAI9O,QAGjD0W,GAAoB,GAIjBA,CAAiB,EAG1BZ,yBAA0BA,KACxB,MAAMa,EAAqBhX,EACxBiX,wBACA/H,SAAS3M,QAAO4M,GAAOA,EAAI6G,kBACxB,aAAEZ,GAAiBpV,EAAM4B,WAE/B,IAAIsV,IAA0BF,EAAmB/Y,OASjD,OANEiZ,GACAF,EAAmBnY,MAAKsQ,IAAQiG,EAAajG,EAAI9O,QAEjD6W,GAAwB,GAGnBA,CAAqB,EAG9BC,sBAAuBA,KAAM,IAAAC,EAC3B,MAAMC,EAAgBhW,OAAOmO,KACE,OADE4H,EAC/BpX,EAAM4B,WAAWwT,cAAYgC,EAAI,CAAC,GAClCnZ,OACF,OACEoZ,EAAgB,GAChBA,EAAgBrX,EAAM6W,sBAAsB3H,SAASjR,MAAM,EAI/DqZ,0BAA2BA,KACzB,MAAMN,EAAqBhX,EAAMiX,wBAAwB/H,SACzD,OAAOlP,EAAMmW,4BAETa,EACGzU,QAAO4M,GAAOA,EAAI6G,iBAClBnX,MAAKxB,GAAKA,EAAEka,iBAAmBla,EAAEma,qBAAoB,EAG9DC,gCAAiCA,IACvBlN,IACNvK,EAAM4V,sBACFrL,EAAiBmN,OAA4BC,QAChD,EAILC,oCAAqCA,IAC3BrN,IACNvK,EAAMiW,0BACF1L,EAAiBmN,OAA4BC,QAChD,IAMTxH,UAAWA,CACThB,EACAnP,KAEO,CACL6X,eAAgBnE,IACd,MAAMoE,EAAa3I,EAAIoI,gBAEvBvX,EAAMyV,iBAAgBtY,IAGpB,GAAI2a,KAFJpE,EAAyB,qBAAVA,EAAwBA,GAASoE,GAG9C,OAAO3a,EAGT,MAAM4a,EAAiB,IAAK5a,GAI5B,OAFAkZ,EAAoB0B,EAAgB5I,EAAI9O,GAAIqT,EAAO1T,GAE5C+X,CAAc,GACrB,EAEJR,cAAeA,KACb,MAAM,aAAEnC,GAAiBpV,EAAM4B,WAC/B,OAAOoW,EAAc7I,EAAKiG,EAAa,EAGzCoC,kBAAmBA,KACjB,MAAM,aAAEpC,GAAiBpV,EAAM4B,WAC/B,MAAsD,SAA/CqW,EAAiB9I,EAAKiG,EAA+B,EAG9D8C,wBAAyBA,KACvB,MAAM,aAAE9C,GAAiBpV,EAAM4B,WAC/B,MAAsD,QAA/CqW,EAAiB9I,EAAKiG,EAA8B,EAG7DY,aAAcA,KAAM,IAAAnM,EAClB,MAAgD,oBAArC7J,EAAME,QAAQoV,mBAChBtV,EAAME,QAAQoV,mBAAmBnG,GAGH,OAAvCtF,EAAO7J,EAAME,QAAQoV,qBAAkBzL,CAAQ,EAGjDsO,oBAAqBA,KAAM,IAAAC,EACzB,MAAmD,oBAAxCpY,EAAME,QAAQsV,sBAChBxV,EAAME,QAAQsV,sBAAsBrG,GAGH,OAA1CiJ,EAAOpY,EAAME,QAAQsV,wBAAqB4C,CAAQ,EAGpDC,kBAAmBA,KAAM,IAAAC,EACvB,MAAqD,oBAA1CtY,EAAME,QAAQqV,wBAChBvV,EAAME,QAAQqV,wBAAwBpG,GAGH,OAA5CmJ,EAAOtY,EAAME,QAAQqV,0BAAuB+C,CAAQ,EAEtDC,yBAA0BA,KACxB,MAAMC,EAAYrJ,EAAI6G,eAEtB,OAAQzL,IAAe,IAAAkO,EAChBD,GACLrJ,EAAI0I,eACF,OADgBY,EACdlO,EAAiBmN,aAAnB,EAAAe,EAAgDd,QACjD,CACF,KAMHtB,EAAsBA,CAC1B0B,EACA1X,EACAqT,EACA1T,KACG,IAAA4Q,EACH,MAAMzB,EAAMnP,EAAM0Y,OAAOrY,GAQrBqT,GACGvE,EAAIkJ,qBACPhX,OAAOmO,KAAKuI,GAAgBla,SAAQb,UAAc+a,EAAe/a,KAE/DmS,EAAI6G,iBACN+B,EAAe1X,IAAM,WAGhB0X,EAAe1X,GAIpB,OAAAuQ,EAAAzB,EAAI4B,UAAJH,EAAa3S,QAAUkR,EAAIgJ,uBAC7BhJ,EAAI4B,QAAQlT,SAAQsR,GAClBkH,EAAoB0B,EAAgB5I,EAAI9O,GAAIqT,EAAO1T,IAEvD,EAGK,SAAS0W,EACd1W,EACAyW,GAEA,MAAMrB,EAAepV,EAAM4B,WAAWwT,aAEhCuD,EAAoC,GACpCC,EAAkD,CAAC,EAGnDC,EAAc,SAACzC,EAAoB5V,GACvC,OAAO4V,EACJnV,KAAIkO,IAAO,IAAA2J,EACV,MAAMhB,EAAaE,EAAc7I,EAAKiG,GActC,GAZI0C,IACFa,EAAoB5a,KAAKoR,GACzByJ,EAAoBzJ,EAAI9O,IAAM8O,GAG5B,OAAJ2J,EAAI3J,EAAI4B,UAAJ+H,EAAa7a,SACfkR,EAAM,IACDA,EACH4B,QAAS8H,EAAY1J,EAAI4B,WAIzB+G,EACF,OAAO3I,CACT,IAED5M,OAAOC,UAGZ,MAAO,CACL4T,KAAMyC,EAAYpC,EAASL,MAC3BlH,SAAUyJ,EACV/I,SAAUgJ,EAEd,CAEO,SAASZ,EACd7I,EACA4J,GACS,IAAAC,EACT,OAAwB,OAAxBA,EAAOD,EAAU5J,EAAI9O,MAAG2Y,CAC1B,CAEO,SAASf,EACd9I,EACA4J,EACA/Y,GAEA,GAAImP,EAAI4B,SAAW5B,EAAI4B,QAAQ9S,OAAQ,CACrC,IAAIgb,GAAsB,EACtBC,GAAe,EAenB,OAbA/J,EAAI4B,QAAQlT,SAAQsb,IAEdD,IAAiBD,IAIjBjB,EAAcmB,EAAQJ,GACxBG,GAAe,EAEfD,GAAsB,EACxB,IAGKA,EAAsB,QAAQC,GAAe,MACtD,CAEA,OAAO,CACT,CC3gBO,MAAME,EAAsB,aAkDnC,SAASC,EAAazE,EAAQC,GAC5B,OAAOD,IAAMC,EAAI,EAAID,EAAIC,EAAI,GAAK,CACpC,CAEA,SAASnD,EAASkD,GAChB,MAAiB,kBAANA,EACLxB,MAAMwB,IAAMA,IAAMvB,KAAYuB,KAAOvB,IAChC,GAEF3T,OAAOkV,GAEC,kBAANA,EACFA,EAEF,EACT,CAKA,SAAS0E,EAAoBC,EAAcC,GAGzC,MAAM5E,EAAI2E,EAAKzJ,MAAMsJ,GAAqB7W,OAAOC,SAC3CqS,EAAI2E,EAAK1J,MAAMsJ,GAAqB7W,OAAOC,SAGjD,KAAOoS,EAAE3W,QAAU4W,EAAE5W,QAAQ,CAC3B,MAAMwb,EAAK7E,EAAE8E,QACPC,EAAK9E,EAAE6E,QAEPE,EAAKC,SAASJ,EAAI,IAClBK,EAAKD,SAASF,EAAI,IAElBI,EAAQ,CAACH,EAAIE,GAAInF,OAGvB,GAAIvB,MAAM2G,EAAM,IAAhB,CACE,GAAIN,EAAKE,EACP,OAAO,EAET,GAAIA,EAAKF,EACP,OAAQ,CAGZ,KARA,CAWA,GAAIrG,MAAM2G,EAAM,IACd,OAAO3G,MAAMwG,IAAO,EAAI,EAI1B,GAAIA,EAAKE,EACP,OAAO,EAET,GAAIA,EAAKF,EACP,OAAQ,CAZV,CAcF,CAEA,OAAOhF,EAAE3W,OAAS4W,EAAE5W,MACtB,CAIO,MAAM+b,EAAa,CACxBC,aAnHmCA,CAACC,EAAMC,EAAM9X,IACzCiX,EACL5H,EAASwI,EAAKzI,SAASpP,IAAWmP,cAClCE,EAASyI,EAAK1I,SAASpP,IAAWmP,eAiHpC4I,0BA7GgDA,CAACF,EAAMC,EAAM9X,IACtDiX,EACL5H,EAASwI,EAAKzI,SAASpP,IACvBqP,EAASyI,EAAK1I,SAASpP,KA2GzBgY,KArG2BA,CAACH,EAAMC,EAAM9X,IACjCgX,EACL3H,EAASwI,EAAKzI,SAASpP,IAAWmP,cAClCE,EAASyI,EAAK1I,SAASpP,IAAWmP,eAmGpC8I,kBA7FwCA,CAACJ,EAAMC,EAAM9X,IAC9CgX,EACL3H,EAASwI,EAAKzI,SAASpP,IACvBqP,EAASyI,EAAK1I,SAASpP,KA2FzBkY,SAvF+BA,CAACL,EAAMC,EAAM9X,KAC5C,MAAMuS,EAAIsF,EAAKzI,SAAepP,GACxBwS,EAAIsF,EAAK1I,SAAepP,GAK9B,OAAOuS,EAAIC,EAAI,EAAID,EAAIC,GAAK,EAAI,CAAC,EAiFjC2F,MA9E4BA,CAACN,EAAMC,EAAM9X,IAClCgX,EAAaa,EAAKzI,SAASpP,GAAW8X,EAAK1I,SAASpP,KCDvDoY,EAAW,CACflZ,ECYsC,CACtCqG,gBAAkBC,IACT,CACL6S,iBAAkB,CAAC,KAChB7S,IAIPS,kBACEtI,IAEO,CACL2a,yBAA0B5d,EAAiB,mBAAoBiD,KAInE0I,aAAcA,CACZzI,EACAD,KAEO,CACL4a,iBAAkBlH,IACZzT,EAAO4a,cACT7a,EAAM8a,qBAAoB3d,IAAO,IAC5BA,EACH,CAAC8C,EAAOI,IAAU,MAALqT,EAAAA,GAAUzT,EAAOmG,kBAElC,EAEFA,aAAcA,KAAM,IAAA2U,EAAAC,EAClB,OAAqD,OAArDD,EAAO,OAAPC,EAAOhb,EAAM4B,WAAW8Y,uBAAjB,EAAAM,EAAoC/a,EAAOI,MAAG0a,CAAQ,EAG/DF,WAAYA,KAAM,IAAAjR,EAAAC,EAChB,OACgC,OAA9BD,EAAC3J,EAAO8I,UAAUkS,eAAYrR,KACH,OADWC,EACrC7J,EAAME,QAAQ+a,eAAYpR,EAAS,EAGxCqR,2BAA4BA,IAClB3Q,IACN,MAAAtK,EAAO2a,kBAAP3a,EAAO2a,iBACHrQ,EAAiBmN,OAA4BC,QAChD,IAMTxH,UAAWA,CACThB,EACAnP,KAEO,CACLmb,oBAAqBjd,GACnB,IAAM,CAACiR,EAAIiM,cAAepb,EAAM4B,WAAW8Y,oBAC3CW,GACSA,EAAM9Y,QAAO+Y,GAAQA,EAAKrb,OAAOmG,kBAE1C,CACEpJ,IAC2C,0BAC3CyB,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQqb,SAAS,IAGlEC,gBAAiBtd,GACf,IAAM,CACJiR,EAAIsM,sBACJtM,EAAIuM,wBACJvM,EAAIwM,0BAEN,CAAC7Z,EAAMuD,EAAQtD,IAAU,IAAID,KAASuD,KAAWtD,IACjD,CACE/E,KAAK4F,EACLnE,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQqb,SAAS,MAMtE/Z,YACExB,IAEA,MAAM4b,EAA2BA,CAC/B5e,EACA6e,IAEO3d,GACL,IAAM,CACJ2d,IACAA,IACGtZ,QAAOlF,GAAKA,EAAE+I,iBACdnF,KAAI5D,GAAKA,EAAEgD,KACXmG,KAAK,QAEVL,GACSA,EAAQ5D,QAAOlF,GAAmB,MAAdA,EAAE+I,kBAAY,EAAd/I,EAAE+I,kBAE/B,CACEpJ,MACAyB,MAAOA,KAAA,IAAA2E,EAAA,OAA4B,OAA5BA,EAAMpD,EAAME,QAAQ4C,UAAQM,EAAIpD,EAAME,QAAQ4b,YAAY,IAKvE,MAAO,CACLC,sBAAuBH,EACrB,yBACA,IAAM5b,EAAMgc,sBAEdra,sBAAuBia,EACrB,yBACA,IAAM5b,EAAMic,sBAEd/S,0BAA2B0S,EACzB,6BACA,IAAM5b,EAAMkc,uBAEd/S,2BAA4ByS,EAC1B,8BACA,IAAM5b,EAAMmc,wBAEdC,4BAA6BR,EAC3B,+BACA,IAAM5b,EAAMqc,yBAGdvB,oBAAqBje,GACnB,MAAAmD,EAAME,QAAQya,8BAAd,EAAA3a,EAAME,QAAQya,yBAA2B9d,GAE3Cyf,sBAAuB3P,IAAgB,IAAAC,EACrC5M,EAAM8a,oBACJnO,EAAe,CAAC,EAAuC,OAAtCC,EAAG5M,EAAM6M,aAAa6N,kBAAgB9N,EAAI,CAAC,EAC7D,EAGH2P,wBAAyB7I,IAAS,IAAA8I,EAChC9I,EAAa,OAAR8I,EAAG9I,GAAK8I,GAAKxc,EAAMyc,yBAExBzc,EAAM8a,oBACJ9a,EAAMic,oBAAoB9O,QACxB,CAACuP,EAAKzc,KAAW,IACZyc,EACH,CAACzc,EAAOI,IAAMqT,KAAS,MAAAzT,EAAO4a,YAAP5a,EAAO4a,iBAEhC,CAAC,GAEJ,EAGH4B,uBAAwBA,KACrBzc,EAAMic,oBAAoBpd,MAAKoB,KAA8B,MAAnBA,EAAOmG,cAAPnG,EAAOmG,kBAEpDuW,wBAAyBA,IACvB3c,EAAMic,oBAAoBpd,MAAKoB,GAA6B,MAAnBA,EAAOmG,kBAAY,EAAnBnG,EAAOmG,iBAElDwW,qCAAsCA,IAC5BrS,IAAe,IAAAkO,EACrBzY,EAAMuc,wBACJ,OAD2B9D,EACzBlO,EAAiBmN,aAAnB,EAAAe,EAAgDd,QACjD,EAGN,GJ/LiC,CACpC/P,gBAAkBC,IACT,CACLgV,YAAa,MACVhV,IAIPS,kBACEtI,IAEO,CACL8c,oBAAqB/f,EAAiB,cAAeiD,KAIzDwB,YACExB,IAEO,CACL+c,eAAgBlgB,GAAW,MAAAmD,EAAME,QAAQ4c,yBAAd,EAAA9c,EAAME,QAAQ4c,oBAAsBjgB,GAC/DmgB,iBAAkBrQ,IAAgB,IAAAC,EAChC5M,EAAM+c,eACJpQ,EAAe,GAAmC,OAAjCC,EAAG5M,EAAM6M,aAAagQ,aAAWjQ,EAAI,GACvD,EAEHqQ,mBAAoB/e,GAClB,IAAM,CACJ8B,EAAM4B,WAAWib,YACjB7c,EAAM4B,WAAWsb,SACjBld,EAAME,QAAQid,qBAEhB,CAACN,EAAaK,EAAUC,IAAsBhX,IAG5C,IAAIiX,EAA2C,GAG/C,GAAgB,MAAXP,GAAAA,EAAa5e,OAEX,CACL,MAAMof,EAAkB,IAAIR,GAGtBS,EAAc,IAAInX,GAKxB,KAAOmX,EAAYrf,QAAUof,EAAgBpf,QAAQ,CACnD,MAAMsf,EAAiBF,EAAgB3D,QACjC8D,EAAaF,EAAYlU,WAC7B/L,GAAKA,EAAEgD,KAAOkd,IAEZC,GAAc,GAChBJ,EAAerf,KAAKuf,EAAYG,OAAOD,EAAY,GAAG,GAE1D,CAGAJ,EAAiB,IAAIA,KAAmBE,EAC1C,MAtBEF,EAAiBjX,EAwBnB,OK2LH,SACLlE,EACAib,EACAC,GAEA,GAAK,MAAAD,IAAAA,EAAUjf,SAAWkf,EACxB,OAAOlb,EAGT,MAAMyb,EAAqBzb,EAAYM,QACrCob,IAAQT,EAASva,SAASgb,EAAItd,MAGhC,MAA0B,WAAtB8c,EACKO,EAOF,IAJiBR,EACrBjc,KAAI2c,GAAK3b,EAAYK,MAAKqb,GAAOA,EAAItd,KAAOud,MAC5Crb,OAAOC,YAEqBkb,EACjC,CLjNiBG,CAAaT,EAAgBF,EAAUC,EAAkB,GAElE,CACEngB,KAAK4F,OMhCsB,CACnCgF,gBAAkBC,IACT,CACLhG,cARyB,CAC7BC,KAAM,GACNC,MAAO,OAOA8F,IAIPS,kBACEtI,IAEO,CACL8d,sBAAuB/gB,EAAiB,gBAAiBiD,KAI7D0I,aAAcA,CACZzI,EACAD,KAEO,CACL+d,IAAK9U,IACH,MAAM+U,EAAY/d,EACfge,iBACAhd,KAAI5D,GAAKA,EAAEgD,KACXkC,OAAOC,SAEVxC,EAAMke,kBAAiB/gB,IAAO,IAAAghB,EAAAC,EACFC,EAAAC,EAUDC,EAAAC,EAVzB,MAAiB,UAAbvV,EACK,CACLnH,MAAgB,OAAVuc,EAAC,MAAAlhB,OAAA,EAAAA,EAAK2E,MAAIuc,EAAI,IAAI9b,QAAOlF,KAAe,MAAT2gB,GAAAA,EAAWrb,SAAStF,MACzD0E,MAAO,KACS,OAAXuc,EAAC,MAAAnhB,OAAA,EAAAA,EAAK4E,OAAKuc,EAAI,IAAI/b,QAAOlF,KAAe,MAAT2gB,GAAAA,EAAWrb,SAAStF,SACpD2gB,IAKQ,SAAb/U,EACK,CACLnH,KAAM,KACS,OAAVyc,EAAC,MAAAphB,OAAA,EAAAA,EAAK2E,MAAIyc,EAAI,IAAIhc,QAAOlF,KAAe,MAAT2gB,GAAAA,EAAWrb,SAAStF,SACnD2gB,GAELjc,OAAkB,OAAXyc,EAAC,MAAArhB,OAAA,EAAAA,EAAK4E,OAAKyc,EAAI,IAAIjc,QAAOlF,KAAM,MAAA2gB,GAAAA,EAAWrb,SAAStF,OAIxD,CACLyE,MAAgB,OAAVqc,EAAC,MAAAhhB,OAAA,EAAAA,EAAK2E,MAAIqc,EAAI,IAAI5b,QAAOlF,KAAe,MAAT2gB,GAAAA,EAAWrb,SAAStF,MACzD0E,OAAkB,OAAXqc,EAAC,MAAAjhB,OAAA,EAAAA,EAAK4E,OAAKqc,EAAI,IAAI7b,QAAOlF,KAAM,MAAA2gB,GAAAA,EAAWrb,SAAStF,MAC5D,GACD,EAGJohB,UAAWA,IACWxe,EAAOge,iBAERpf,MACjBxB,IAAC,IAAAqhB,EAAA7U,EAAA,OAC2B,OAA1B6U,EAACrhB,EAAE0L,UAAU4V,gBAAaD,KACE,OADM7U,EACjC7J,EAAME,QAAQye,gBAAa9U,EAAS,IAI3C+U,YAAaA,KACX,MAAMC,EAAgB5e,EAAOge,iBAAiBhd,KAAI5D,GAAKA,EAAEgD,MAEnD,KAAEyB,EAAI,MAAEC,GAAU/B,EAAM4B,WAAWC,cAEnCid,EAASD,EAAchgB,MAAKxB,GAAS,MAAJyE,OAAI,EAAJA,EAAMa,SAAStF,KAChD0hB,EAAUF,EAAchgB,MAAKxB,GAAU,MAAL0E,OAAK,EAALA,EAAOY,SAAStF,KAExD,OAAOyhB,EAAS,SAASC,GAAU,OAAe,EAGpDC,eAAgBA,KAAM,IAAAjE,EAAAC,EAAAiE,EACpB,MAAMhW,EAAWhJ,EAAO2e,cAExB,OAAO3V,EAC2D,OADnD8R,EACmB,OADnBC,EACXhb,EAAM4B,WAAWC,gBAAjB,OAA8Bod,EAA9BjE,EAAiC/R,SAAH,EAA9BgW,EAA4CC,QAAQjf,EAAOI,KAAG0a,GAAK,EACnE,CAAC,IAKX5K,UAAWA,CACThB,EACAnP,KAEO,CACL0b,sBAAuBxd,GACrB,IAAM,CACJiR,EAAIgM,sBACJnb,EAAM4B,WAAWC,cAAcC,KAC/B9B,EAAM4B,WAAWC,cAAcE,SAEjC,CAACod,EAAUrd,EAAMC,KACf,MAAMqd,EAAyB,IAAS,MAAJtd,EAAAA,EAAQ,MAAc,MAALC,EAAAA,EAAS,IAE9D,OAAOod,EAAS5c,QAAOlF,IAAM+hB,EAAazc,SAAStF,EAAE4C,OAAOI,KAAI,GAElE,CACErD,IAEE,4BACFyB,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQqb,SAAS,IAGlEE,oBAAqBvd,GACnB,IAAM,CACJiR,EAAIgM,sBACJnb,EAAM4B,WAAWC,cAAcC,KAAK,KAGtC,CAACqd,EAAUrd,KACU,MAAJA,EAAAA,EAAQ,IACpBb,KACCoB,GAAY8c,EAAS7c,MAAKgZ,GAAQA,EAAKrb,OAAOI,KAAOgC,MAEtDE,OAAOC,SACPvB,KAAI5D,IAAK,IAAMA,EAAG4L,SAAU,YAIjC,CACEjM,IAC2C,0BAC3CyB,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQqb,SAAS,IAGlEI,qBAAsBzd,GACpB,IAAM,CAACiR,EAAIgM,sBAAuBnb,EAAM4B,WAAWC,cAAcE,SACjE,CAACod,EAAUpd,KACW,MAALA,EAAAA,EAAS,IACrBd,KACCoB,GAAY8c,EAAS7c,MAAKgZ,GAAQA,EAAKrb,OAAOI,KAAOgC,MAEtDE,OAAOC,SACPvB,KAAI5D,IAAK,IAAMA,EAAG4L,SAAU,aAIjC,CACEjM,IAC2C,2BAC3CyB,MAAOA,KAAA,IAAA2E,EAAA,OAA4B,OAA5BA,EAAMpD,EAAME,QAAQ4C,UAAQM,EAAIpD,EAAME,QAAQqb,SAAS,MAMtE/Z,YACExB,IAEO,CACLke,iBAAkBrhB,GAChB,MAAAmD,EAAME,QAAQ4d,2BAAd,EAAA9d,EAAME,QAAQ4d,sBAAwBjhB,GAExCwiB,mBAAoB1S,IAAY,IAAAC,EAAAmC,EAAA,OAC9B/O,EAAMke,iBACJvR,EAtKqB,CAC7B7K,KAAM,GACNC,MAAO,IAsKsC,OADT6K,EACxB,OADwBmC,EACxB/O,EAAM6M,mBAAN,EAAAkC,EAAoBlN,eAAa+K,EAxKhB,CAC7B9K,KAAM,GACNC,MAAO,IAuKA,EAEHud,uBAAwBrW,IAAY,IAAAsW,EAClC,MAAMC,EAAexf,EAAM4B,WAAWC,cAEvB,IAAA4d,EAAAC,EAAf,OAAKzW,EAKEzG,QAAQ,OAAD+c,EAACC,EAAavW,SAAb,EAAAsW,EAAwBthB,QAJ9BuE,SACY,OAAjBid,EAAAD,EAAa1d,WAAI,EAAjB2d,EAAmBxhB,UAA4B,OAAtByhB,EAAIF,EAAazd,YAAK,EAAlB2d,EAAoBzhB,QAGP,EAGhDie,mBAAoBhe,GAClB,IAAM,CAAC8B,EAAMic,oBAAqBjc,EAAM4B,WAAWC,cAAcC,QACjE,CAACE,EAAYF,KACH,MAAAA,EAAAA,EAAQ,IACbb,KAAIoB,GAAYL,EAAWM,MAAKrC,GAAUA,EAAOI,KAAOgC,MACxDE,OAAOC,UAEZ,CACExF,KAAK4F,EACLnE,MAAOA,KAAA,IAAA8E,EAAA,OAA4B,OAA5BA,EAAMvD,EAAME,QAAQ4C,UAAQS,EAAIvD,EAAME,QAAQ4b,YAAY,IAIrEK,oBAAqBje,GACnB,IAAM,CAAC8B,EAAMic,oBAAqBjc,EAAM4B,WAAWC,cAAcE,SACjE,CAACC,EAAYD,KACH,MAAAA,EAAAA,EAAS,IACdd,KAAIoB,GAAYL,EAAWM,MAAKrC,GAAUA,EAAOI,KAAOgC,MACxDE,OAAOC,UAEZ,CACExF,KAAK4F,EACLnE,MAAOA,KAAA,IAAAkF,EAAA,OAA4B,OAA5BA,EAAM3D,EAAME,QAAQ4C,UAAQa,EAAI3D,EAAME,QAAQ4b,YAAY,IAIrEO,qBAAsBne,GACpB,IAAM,CACJ8B,EAAMic,oBACNjc,EAAM4B,WAAWC,cAAcC,KAC/B9B,EAAM4B,WAAWC,cAAcE,SAEjC,CAACC,EAAYF,EAAMC,KACjB,MAAMqd,EAAyB,IAAS,MAAJtd,EAAAA,EAAQ,MAAc,MAALC,EAAAA,EAAS,IAE9D,OAAOC,EAAWO,QAAOlF,IAAM+hB,EAAazc,SAAStF,EAAEgD,KAAI,GAE7D,CACErD,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoF,EAAA,OAA4B,OAA5BA,EAAM7D,EAAME,QAAQ4C,UAAQe,EAAI7D,EAAME,QAAQ4b,YAAY,ORtHtC,CACnCnU,oBAAqBA,KACZ,CACL8L,SAAU,SAId7L,gBAAkBC,IACT,CACL8X,cAAe,GACfC,kBAAc/Y,KAGXgB,IAIPS,kBACEtI,IAEO,CACL6f,sBAAuB9iB,EAAiB,gBAAiBiD,GACzD8f,qBAAsB/iB,EAAiB,eAAgBiD,GACvD+f,oBAAoB,EACpBC,sBAAuB,IACvBC,eAAgB,OAChBC,yBAA0BjgB,IAAU,IAAAkgB,EAAAC,EAClC,MAAM1M,EAEQ,OAFHyM,EAAGngB,EACXuW,kBACArH,SAAS,KAFE,OAEAkR,EAFAD,EAEEE,yBACbpgB,EAAOI,UADI,EAFA+f,EAGC3O,WAEf,MAAwB,kBAAViC,GAAuC,kBAAVA,CAAkB,IAKnEhL,aAAcA,CACZzI,EACAD,KAEO,CACLsgB,gBAAiBA,KACf,MAAMC,EAAWvgB,EAAMuW,kBAAkBrH,SAAS,GAE5CwE,EAAQ,MAAA6M,OAAA,EAAAA,EAAU9O,SAASxR,EAAOI,IAExC,MAAqB,kBAAVqT,EACFH,EAAUrC,eAGE,kBAAVwC,EACFH,EAAUX,cAGE,mBAAVc,GAIG,OAAVA,GAAmC,kBAAVA,EAHpBH,EAAUb,OAOf2B,MAAMC,QAAQZ,GACTH,EAAUnB,YAGZmB,EAAUZ,UAAU,EAE7B6N,YAAaA,KAAM,IAAAC,EAAAC,EACjB,OAAOtjB,EAAW6C,EAAO8I,UAAU0K,UAC/BxT,EAAO8I,UAAU0K,SACa,SAA9BxT,EAAO8I,UAAU0K,SACjBxT,EAAOqgB,kBAEuD,OADhEG,EACyB,OADzBC,EACE1gB,EAAME,QAAQqT,gBAAS,EAAvBmN,EAA0BzgB,EAAO8I,UAAU0K,WAAmBgN,EAC9DlN,EAAUtT,EAAO8I,UAAU0K,SAA4B,EAE7DkN,aAAcA,KAAM,IAAA/W,EAAAC,EAAAuO,EAClB,OACsC,OAApCxO,EAAC3J,EAAO8I,UAAU6X,qBAAkBhX,KACF,OADUC,EAC3C7J,EAAME,QAAQ2gB,sBAAmBhX,KACN,OADeuO,EAC1CpY,EAAME,QAAQ4gB,gBAAa1I,MAC1BnY,EAAO8gB,UAAU,EAIvBC,mBAAoBA,KAAM,IAAAC,EAAA3I,EAAA4I,EAAAC,EACxB,OACsC,OAApCF,EAAChhB,EAAO8I,UAAUqY,qBAAkBH,KACH,OADW3I,EAC3CtY,EAAME,QAAQkhB,qBAAkB9I,KACL,OADc4I,EACzClhB,EAAME,QAAQ4gB,gBAAaI,KACqB,OADZC,EACE,MAAtCnhB,EAAME,QAAQggB,8BAAwB,EAAtClgB,EAAME,QAAQggB,yBAA2BjgB,KAAOkhB,MAC/ClhB,EAAO8gB,UAAU,EAIvBM,cAAeA,IAAMphB,EAAOqhB,kBAAoB,EAEhDC,eAAgBA,KAAA,IAAAxG,EAAAC,EAAA,OACgB,OADhBD,EACd/a,EAAM4B,WAAW+d,gBAAjB,OAA8B3E,EAA9BD,EAAgCzY,MAAKjF,GAAKA,EAAEgD,KAAOJ,EAAOI,WAA5B,EAA9B2a,EAA+DtH,KAAK,EAEtE4N,eAAgBA,KAAA,IAAArC,EAAAuC,EAAA,OACoD,OADpDvC,EACgB,OADhBuC,EACdxhB,EAAM4B,WAAW+d,oBAAa,EAA9B6B,EAAgCpY,WAAU/L,GAAKA,EAAEgD,KAAOJ,EAAOI,MAAG4e,GACjE,CAAC,EAEJwC,eAAgB/N,IACd1T,EAAM0hB,kBAAiBvkB,IACrB,MAAMsW,EAAWxT,EAAOugB,cAClBmB,EAAiB,MAAAxkB,OAAA,EAAAA,EAAKmF,MAAKjF,GAAKA,EAAEgD,KAAOJ,EAAOI,KAEhDuhB,EAAYhlB,EAChB8W,EACAiO,EAAiBA,EAAejO,WAAQ7M,GAUxC,IAAAgb,EANF,GACErO,EACEC,EACAmO,EACA3hB,GAGF,OAA2C,OAA3C4hB,EAAU,MAAH1kB,OAAG,EAAHA,EAAKoF,QAAOlF,GAAKA,EAAEgD,KAAOJ,EAAOI,MAAGwhB,EAAI,GAGjD,MAAMC,EAAe,CAAEzhB,GAAIJ,EAAOI,GAAIqT,MAAOkO,GAEzB,IAAAG,EAApB,OAAIJ,EAOE,OANJI,EACK,MAAH5kB,OAAG,EAAHA,EAAK8D,KAAI5D,GACHA,EAAEgD,KAAOJ,EAAOI,GACXyhB,EAEFzkB,KACP0kB,EAAI,GAIH,MAAH5kB,GAAAA,EAAKc,OACA,IAAId,EAAK2kB,GAGX,CAACA,EAAa,GACrB,EAEJE,oBACEhiB,EAAME,QAAQ+hB,oBACdjiB,EAAME,QAAQ+hB,mBAAmBjiB,EAAOC,EAAOI,IACjD4hB,mBAAoBA,IACbhiB,EAAO+hB,oBAIL/hB,EAAO+hB,sBAHLhiB,EAAMkiB,yBAKjBC,wBACEniB,EAAME,QAAQkiB,wBACdpiB,EAAME,QAAQkiB,uBAAuBpiB,EAAOC,EAAOI,IACrD+hB,uBAAwBA,IACjBniB,EAAOkiB,wBAILliB,EAAOkiB,0BAHL,IAAIE,IAKfC,wBACEtiB,EAAME,QAAQqiB,wBACdviB,EAAME,QAAQqiB,uBAAuBviB,EAAOC,EAAOI,IACrDkiB,uBAAwBA,KACtB,GAAKtiB,EAAOqiB,wBAIZ,OAAOriB,EAAOqiB,yBAAyB,IAO7CnS,UAAWA,CACThB,EACAnP,KAEO,CACL2f,cAAe,CAAC,EAChB6C,kBAAmB,CAAC,IAIxBhhB,YACExB,IAEO,CACLyiB,sBAAuBA,IACdlP,EAAUrC,eAGnBwR,kBAAmBA,KAAM,IAAAC,EAAAC,EACvB,MAAQ3C,eAAgBA,GAAmBjgB,EAAME,QAEjD,OAAO9C,EAAW6iB,GACdA,EACmB,SAAnBA,EACAjgB,EAAMyiB,wBAE6C,OADrDE,EACyB,OADzBC,EACE5iB,EAAME,QAAQqT,gBAAS,EAAvBqP,EAA0B3C,IAAyB0C,EACnDpP,EAAU0M,EAAkC,EAGlDyB,iBAAmB7kB,IACjB,MAAMoF,EAAcjC,EAAMic,oBAkB1B,MAAAjc,EAAME,QAAQ2f,uBAAd7f,EAAME,QAAQ2f,uBAhBI1iB,IAA4B,IAAA0lB,EAC5C,OAAqC,OAArCA,EAAOjmB,EAAiBC,EAASM,SAAI,EAA9B0lB,EAAgCtgB,QAAOA,IAC5C,MAAMtC,EAASgC,EAAYK,MAAKjF,GAAKA,EAAEgD,KAAOkC,EAAOlC,KAErD,GAAIJ,EAAQ,CAGV,GAAIuT,EAFavT,EAAOugB,cAEaje,EAAOmR,MAAOzT,GACjD,OAAO,CAEX,CAEA,OAAO,CAAI,GACX,GAG2C,EAGjD6iB,gBAAiBjmB,IACf,MAAAmD,EAAME,QAAQ4f,sBAAd9f,EAAME,QAAQ4f,qBAAuBjjB,EAAQ,EAG/CkmB,kBAAmBpW,IACjB3M,EAAM8iB,gBACJnW,OAAe9F,EAAY7G,EAAM6M,aAAa+S,aAC/C,EAGHoD,mBAAoBrW,IAAgB,IAAAC,EAAAmC,EAClC/O,EAAM0hB,iBACJ/U,EAAe,GAAsC,OAApCC,EAAqB,OAArBmC,EAAG/O,EAAM6M,mBAAY,EAAlBkC,EAAoB4Q,eAAa/S,EAAI,GAC1D,EAGHsV,uBAAwBA,IAAMliB,EAAMuW,kBACpCM,oBAAqBA,MACd7W,EAAMijB,sBAAwBjjB,EAAME,QAAQ2W,sBAC/C7W,EAAMijB,qBAAuBjjB,EAAME,QAAQ2W,oBAAoB7W,IAG7DA,EAAME,QAAQgjB,kBAAoBljB,EAAMijB,qBACnCjjB,EAAMkiB,yBAGRliB,EAAMijB,wBAGfE,0BACEnjB,EAAME,QAAQ+hB,oBACdjiB,EAAME,QAAQ+hB,mBAAmBjiB,EAAO,cAE1CojB,yBAA0BA,IACpBpjB,EAAME,QAAQgjB,kBAAoBljB,EAAMmjB,0BACnCnjB,EAAMkiB,yBAGRliB,EAAMmjB,4BAGfE,8BACErjB,EAAME,QAAQkiB,wBACdpiB,EAAME,QAAQkiB,uBAAuBpiB,EAAO,cAC9CsjB,6BAA8BA,IACvBtjB,EAAMqjB,8BAIJrjB,EAAMqjB,gCAHJ,IAAIhB,IAMfkB,8BACEvjB,EAAME,QAAQqiB,wBACdviB,EAAME,QAAQqiB,uBAAuBviB,EAAO,cAC9CwjB,6BAA8BA,KAC5B,GAAKxjB,EAAMujB,8BAIX,OAAOvjB,EAAMujB,+BAA+B,KS9Vf,CACnC3b,gBAAkBC,IACT,CACL4b,QAAS,MACN5b,IAIPF,oBAAqBA,KACZ,CACL+b,UAAW,SAIfpb,kBACEtI,IAEO,CACL2jB,gBAAiB5mB,EAAiB,UAAWiD,GAC7C4jB,iBAAmBrZ,GACTA,EAAiBsZ,WAK/Bnb,aAAcA,CACZzI,EACAD,KAEO,CACL8jB,iBAAkBA,KAChB,MAAMC,EAAY/jB,EAAM6W,sBAAsB3H,SAAS8U,MAAM,IAE7D,IAAIC,GAAW,EAEf,IAAK,MAAM9U,KAAO4U,EAAW,CAC3B,MAAMrQ,EAAQ,MAAAvE,OAAA,EAAAA,EAAKsC,SAASxR,EAAOI,IAEnC,GAA8C,kBAA1CgB,OAAO6iB,UAAUxS,SAASyS,KAAKzQ,GACjC,OAAOsG,EAAWO,SAGpB,GAAqB,kBAAV7G,IACTuQ,GAAW,EAEPvQ,EAAM5D,MAAMsJ,GAAqBnb,OAAS,GAC5C,OAAO+b,EAAWC,YAGxB,CAEA,OAAIgK,EACKjK,EAAWK,KAGbL,EAAWQ,KAAK,EAEzB4J,eAAgBA,KACd,MAAM7D,EAAWvgB,EAAM6W,sBAAsB3H,SAAS,GAItD,MAAqB,kBAFP,MAAAqR,OAAA,EAAAA,EAAU9O,SAASxR,EAAOI,KAG/B,MAGF,MAAM,EAEfgkB,aAAcA,KAAM,IAAAC,EAAAC,EAClB,IAAKtkB,EACH,MAAM,IAAIukB,MAGZ,OAAOpnB,EAAW6C,EAAO8I,UAAU2a,WAC/BzjB,EAAO8I,UAAU2a,UACc,SAA/BzjB,EAAO8I,UAAU2a,UACjBzjB,EAAO6jB,mBACyD,OADvCQ,EACD,OADCC,EACzBvkB,EAAME,QAAQ8Z,iBAAU,EAAxBuK,EAA2BtkB,EAAO8I,UAAU2a,YAAoBY,EAChEtK,EAAW/Z,EAAO8I,UAAU2a,UAA8B,EAEhEe,cAAeA,CAACC,EAAMC,KAWpB,MAAMC,EAAmB3kB,EAAO4kB,sBAC1BC,EAAiC,qBAATJ,GAAiC,OAATA,EAEtD1kB,EAAM+kB,YAAW5nB,IAEf,MAAM6nB,EAAkB,MAAA7nB,OAAA,EAAAA,EAAKmF,MAAKjF,GAAKA,EAAEgD,KAAOJ,EAAOI,KACjD4kB,EAAgB,MAAA9nB,OAAA,EAAAA,EAAKiM,WAAU/L,GAAKA,EAAEgD,KAAOJ,EAAOI,KAE1D,IAGI6kB,EAHAC,EAA2B,GAI3BC,EAAWN,EAAiBJ,EAA4B,SAArBE,EA+Bb,IAAAS,GA1BtBH,EAFG,MAAH/nB,GAAAA,EAAKc,QAAUgC,EAAOqlB,mBAAqBX,EACzCK,EACW,SAEA,MAIR,MAAH7nB,GAAAA,EAAKc,QAAUgnB,IAAkB9nB,EAAIc,OAAS,EACnC,UACJ+mB,EACI,SAEA,UAKE,WAAfE,IAEGJ,GAEEF,IACHM,EAAa,WAKA,QAAfA,IACFC,EAAa,IACRhoB,EACH,CACEkD,GAAIJ,EAAOI,GACXqkB,KAAMU,IAIVD,EAAW1H,OACT,EACA0H,EAAWlnB,QAC0B,OADpBonB,EACdrlB,EAAME,QAAQqlB,sBAAoBF,EAAI7d,OAAOC,oBAIlD0d,EAFwB,WAAfD,EAEI/nB,EAAI8D,KAAI5D,GACfA,EAAEgD,KAAOJ,EAAOI,GACX,IACFhD,EACHqnB,KAAMU,GAGH/nB,IAEe,WAAf6nB,EACI/nB,EAAIoF,QAAOlF,GAAKA,EAAEgD,KAAOJ,EAAOI,KAEhC,CACX,CACEA,GAAIJ,EAAOI,GACXqkB,KAAMU,IAKZ,OAAOD,CAAU,GACjB,EAGJK,gBAAiBA,KAAM,IAAAve,EAAAwe,EAKrB,OAF6B,OAFVxe,EACa,OADbwe,EACjBxlB,EAAO8I,UAAU2c,eAAaD,EAC9BzlB,EAAME,QAAQwlB,eAAaze,EACC,SAA5BhH,EAAOmkB,kBACc,OAAS,KAAK,EAGvCS,oBAAsBF,IAAoB,IAAA9a,EAAAuO,EACxC,MAAMuN,EAAqB1lB,EAAOulB,kBAC5BI,EAAW3lB,EAAO4lB,cAExB,OAAKD,KAKHA,IAAaD,GACsB,OADJ9b,EAC9B7J,EAAME,QAAQ4lB,wBAAoBjc,GAClC8a,GAAuC,OAAlCvM,EAAGpY,EAAME,QAAQ6lB,qBAAiB3N,KAItB,SAAbwN,EAAsB,MAAQ,QAV5BD,CAUkC,EAG7CK,WAAYA,KAAM,IAAApc,EAAA0O,EAChB,OACiC,OAA/B1O,EAAC3J,EAAO8I,UAAUkd,gBAAarc,KACH,OADW0O,EACtCtY,EAAME,QAAQ+lB,gBAAa3N,MAC1BrY,EAAO8gB,UAAU,EAIvBuE,gBAAiBA,KAAM,IAAA9b,EAAAyX,EACrB,OAE+B,OAF/BzX,EACkC,OADlCyX,EACEhhB,EAAO8I,UAAUmd,iBAAejF,EAChCjhB,EAAME,QAAQgmB,iBAAe1c,IAC3BvJ,EAAO8gB,UAAU,EAIvB8E,YAAaA,KAAM,IAAAM,EACjB,MAAMC,EAEI,OAFMD,EAAGnmB,EAChB4B,WACA6hB,cAAO,EAFS0C,EAEP7jB,MAAKjF,GAAKA,EAAEgD,KAAOJ,EAAOI,KAEtC,QAAQ+lB,IAAqBA,EAAW1B,KAAO,OAAS,MAAK,EAG/D2B,aAAcA,KAAA,IAAAC,EAAAC,EAAA,OACgD,OADhDD,EACY,OADZC,EACZvmB,EAAM4B,WAAW6hB,cAAO,EAAxB8C,EAA0Bnd,WAAU/L,GAAKA,EAAEgD,KAAOJ,EAAOI,MAAGimB,GAAK,CAAC,EAEpEE,aAAcA,KAEZxmB,EAAM+kB,YAAW5nB,GACZ,MAAHA,GAAAA,EAAKc,OAASd,EAAIoF,QAAOlF,GAAKA,EAAEgD,KAAOJ,EAAOI,KAAM,IACrD,EAGHomB,wBAAyBA,KACvB,MAAMC,EAAUzmB,EAAO+lB,aAEvB,OAAQzb,IACDmc,IACc,MAAjBnc,EAAUC,SAAVD,EAAUC,UACZ,MAAAvK,EAAOwkB,eAAPxkB,EAAOwkB,mBACL5d,IACA5G,EAAOqlB,oBAC2B,MAA9BtlB,EAAME,QAAQ0jB,sBAAgB,EAA9B5jB,EAAME,QAAQ0jB,iBAAmBrZ,KAEtC,CACF,IAKP/I,YACExB,IAEO,CACL+kB,WAAYloB,GAAW,MAAAmD,EAAME,QAAQyjB,qBAAd,EAAA3jB,EAAME,QAAQyjB,gBAAkB9mB,GACvD8pB,aAAcha,IAAgB,IAAAia,EAAA7X,EAC5B/O,EAAM+kB,WAAWpY,EAAe,GAAgC,OAA9Bia,EAAqB,OAArB7X,EAAG/O,EAAM6M,mBAAY,EAAlBkC,EAAoB0U,SAAOmD,EAAI,GAAG,EAEzEC,qBAAsBA,IAAM7mB,EAAM8mB,qBAClC9W,kBAAmBA,MACZhQ,EAAM+mB,oBAAsB/mB,EAAME,QAAQ8P,oBAC7ChQ,EAAM+mB,mBAAqB/mB,EAAME,QAAQ8P,kBAAkBhQ,IAGzDA,EAAME,QAAQ8mB,gBAAkBhnB,EAAM+mB,mBACjC/mB,EAAM6mB,uBAGR7mB,EAAM+mB,yBF7QiB,CACpCpf,oBAAqBA,KAIZ,CACLsf,eAAgBC,IAAK,IAAAC,EAAAC,EAAA,OAA2C,OAA3CD,EAAI,OAAJC,EAAKF,EAAMzV,aAA4B,MAAnC2V,EAA2B1V,cAA3B,EAAA0V,EAA2B1V,YAAYyV,EAAI,IAAI,EACxEE,cAAe,SAInBzf,gBAAkBC,IACT,CACLqV,SAAU,MACPrV,IAIPS,kBACEtI,IAEO,CACLsnB,iBAAkBvqB,EAAiB,WAAYiD,GAC/Cmd,kBAAmB,YAIvBzU,aAAcA,CACZzI,EACAD,KAEO,CACLunB,eAAgBA,KACdvnB,EAAMwnB,aAAYrqB,GAEZ,MAAAA,GAAAA,EAAKwF,SAAS1C,EAAOI,IAChBlD,EAAIoF,QAAOlF,GAAKA,IAAM4C,EAAOI,KAG/B,IAAQ,MAAHlD,EAAAA,EAAO,GAAK8C,EAAOI,KAC/B,EAGJonB,YAAaA,KAAM,IAAAxgB,EAAAuC,EAAA2B,EAAAvB,EACjB,OAIM,OAJN3C,EAG8B,OAH9BuC,EAEM,OAFN2B,EACiC,OADjCvB,EACE3J,EAAO8I,UAAU2e,iBAAc9d,GAC3BuB,EACJnL,EAAME,QAAQwnB,iBAAcle,GACxBvC,IACFhH,EAAO8gB,UAAU,EAIvB4G,aAAcA,KAAM,IAAAC,EAClB,OAAgC,OAAhCA,EAAO5nB,EAAM4B,WAAWsb,eAAQ,EAAzB0K,EAA2BjlB,SAAS1C,EAAOI,GAAG,EAGvDwnB,gBAAiBA,KAAA,IAAAC,EAAA,OAA+B,OAA/BA,EAAM9nB,EAAM4B,WAAWsb,eAAQ,EAAzB4K,EAA2B5I,QAAQjf,EAAOI,GAAG,EAEpE0nB,yBAA0BA,KACxB,MAAMC,EAAW/nB,EAAOwnB,cAExB,MAAO,KACAO,GACL/nB,EAAOsnB,gBAAgB,CACxB,EAEHU,qBAAsBA,KACpB,MAAM1H,EAAWvgB,EAAMuW,kBAAkBrH,SAAS,GAE5CwE,EAAQ,MAAA6M,OAAA,EAAAA,EAAU9O,SAASxR,EAAOI,IAExC,MAAqB,kBAAVqT,EACFC,EAAe1J,IAGsB,kBAA1C5I,OAAO6iB,UAAUxS,SAASyS,KAAKzQ,GAC1BC,EAAeK,YADxB,CAEA,EAEFkU,iBAAkBA,KAAM,IAAAC,EAAAC,EACtB,IAAKnoB,EACH,MAAM,IAAIukB,MAGZ,OAAOpnB,EAAW6C,EAAO8I,UAAUse,eAC/BpnB,EAAO8I,UAAUse,cACkB,SAAnCpnB,EAAO8I,UAAUse,cACjBpnB,EAAOgoB,uBAGN,OAH4BE,EACD,OADCC,EAC7BpoB,EAAME,QAAQyT,qBAAc,EAA5ByU,EACEnoB,EAAO8I,UAAUse,gBAClBc,EACDxU,EACE1T,EAAO8I,UAAUse,cAClB,IAKX7lB,YACExB,IAEO,CACLwnB,YAAa3qB,GAAW,MAAAmD,EAAME,QAAQonB,sBAAd,EAAAtnB,EAAME,QAAQonB,iBAAmBzqB,GAEzDwrB,cAAe1b,IAAgB,IAAA2b,EAAAvZ,EAC7B/O,EAAMwnB,YACJ7a,EAAe,GAAiC,OAA/B2b,EAAqB,OAArBvZ,EAAG/O,EAAM6M,mBAAY,EAAlBkC,EAAoBmO,UAAQoL,EAAI,GACrD,EAGHvS,sBAAuBA,IAAM/V,EAAM6W,sBACnCiQ,mBAAoBA,MACb9mB,EAAMuoB,qBAAuBvoB,EAAME,QAAQ4mB,qBAC9C9mB,EAAMuoB,oBAAsBvoB,EAAME,QAAQ4mB,mBAAmB9mB,IAG3DA,EAAME,QAAQsoB,iBAAmBxoB,EAAMuoB,oBAClCvoB,EAAM+V,wBAGR/V,EAAMuoB,yBAKnBpY,UAAWA,CACThB,EACAnP,KAEO,CACL2nB,aAAcA,MAAQxY,EAAIsZ,iBAC1BC,iBAAkBrmB,IAChB,GAAI8M,EAAIwZ,qBAAqBC,eAAevmB,GAC1C,OAAO8M,EAAIwZ,qBAAqBtmB,GAGlC,MAAMpC,EAASD,EAAMqK,UAAUhI,GAE/B,OAAK,MAAApC,GAAAA,EAAQ8I,UAAU2f,kBAIvBvZ,EAAIwZ,qBAAqBtmB,GAAYpC,EAAO8I,UAAU2f,iBACpDvZ,EAAI0Z,UAGC1Z,EAAIwZ,qBAAqBtmB,IAPvB8M,EAAIsC,SAASpP,EAOmB,EAE3CsmB,qBAAsB,CAAC,IAI3BG,WAAYA,CACVxN,EACArb,EACAkP,EACAnP,KAKO,CACL2nB,aAAcA,IACZ1nB,EAAO0nB,gBAAkB1nB,EAAOI,KAAO8O,EAAIsZ,iBAC7CM,iBAAkBA,KAAOzN,EAAKqM,gBAAkB1nB,EAAO0nB,eACvDqB,gBAAiBA,KAAA,IAAApY,EAAA,OACd0K,EAAKqM,iBACLrM,EAAKyN,sBACO,OAAZnY,EAACzB,EAAI4B,WAAJH,EAAa3S,OAAM,KF9N3B6P,EKKsC,CACtClG,gBAAkBC,IACT,IACFA,EACHohB,WAAY,CARhBC,UAJuB,EAKvBC,SAJsB,MAaR,MAALthB,OAAK,EAALA,EAAOohB,cAKhB3gB,kBACEtI,IAEO,CACLopB,mBAAoBrsB,EAAiB,aAAciD,KAIvDwB,YACExB,IAEA,IAAIkO,GAAa,EACbC,GAAS,EAEb,MAAO,CACLkb,oBAAqBA,KAAM,IAAApiB,EAAAoH,EACzB,GAAKH,GAOL,GAEkC,OAFlCjH,EAC4B,OAD5BoH,EACErO,EAAME,QAAQoO,cAAYD,EAC1BrO,EAAME,QAAQopB,oBAAkBriB,GAC/BjH,EAAME,QAAQqpB,iBACf,CACA,GAAIpb,EAAQ,OACZA,GAAS,EACTnO,EAAMyO,QAAO,KACXzO,EAAMwpB,iBACNrb,GAAS,CAAK,GAElB,OAjBEnO,EAAMyO,QAAO,KACXP,GAAa,CAAI,GAgBrB,EAEFub,cAAe5sB,GAO0B,MAAhCmD,EAAME,QAAQkpB,wBAAkB,EAAhCppB,EAAME,QAAQkpB,oBANyBjsB,GAC7BP,EAAiBC,EAASM,KAO7CusB,gBAAiB/c,IAAgB,IAAAgd,EAC/B3pB,EAAMypB,cACJ9c,EA/DwB,CAChCuc,UAJuB,EAKvBC,SAJsB,IAmEmB,OADFQ,EAC3B3pB,EAAM6M,aAAaoc,YAAUU,EAjET,CAChCT,UAJuB,EAKvBC,SAJsB,IAoEf,EAEHS,aAAc/sB,IACZmD,EAAMypB,eAActsB,IAClB,IAAI+rB,EAAYtsB,EAAiBC,EAASM,EAAI+rB,WAE9C,MAAMW,EAC+B,qBAA5B7pB,EAAME,QAAQ4pB,YACQ,IAA7B9pB,EAAME,QAAQ4pB,UACVtiB,OAAOC,iBACPzH,EAAME,QAAQ4pB,UAAY,EAIhC,OAFAZ,EAAY/pB,KAAKU,IAAI,EAAGV,KAAKW,IAAIopB,EAAWW,IAErC,IACF1sB,EACH+rB,YACD,GACD,EAEJM,eAAgB7c,IAAgB,IAAAod,EAAAhb,EAAAib,EAC9BhqB,EAAM4pB,aACJjd,EA3Fe,EA6F8B,OADzBod,EACE,OADFhb,EAChB/O,EAAM6M,eAAN,OAAkBmd,EAAlBjb,EAAoBka,iBAAF,EAAlBe,EAAgCd,WAASa,EA7F9B,EA8FhB,EAEHE,cAAetd,IAAgB,IAAAud,EAAAC,EAAAC,EAC7BpqB,EAAMqqB,YACJ1d,EAjGc,GAmG8B,OADzBud,EACG,OADHC,EACfnqB,EAAM6M,eAAN,OAAkBud,EAAlBD,EAAoBlB,iBAAF,EAAlBmB,EAAgCjB,UAAQe,EAnG9B,GAoGf,EAEHG,YAAaxtB,IACXmD,EAAMypB,eAActsB,IAClB,MAAMgsB,EAAWhqB,KAAKU,IAAI,EAAGjD,EAAiBC,EAASM,EAAIgsB,WACrDmB,EAAcntB,EAAIgsB,SAAWhsB,EAAI+rB,UACjCA,EAAY/pB,KAAKsV,MAAM6V,EAAcnB,GAE3C,MAAO,IACFhsB,EACH+rB,YACAC,WACD,GACD,EAEJoB,aAAc1tB,GACZmD,EAAMypB,eAActsB,IAAO,IAAAqtB,EACzB,IAAIC,EAAe7tB,EACjBC,EACuB,OADhB2tB,EACPxqB,EAAME,QAAQ4pB,WAASU,GAAK,GAO9B,MAJ4B,kBAAjBC,IACTA,EAAetrB,KAAKU,KAAK,EAAG4qB,IAGvB,IACFttB,EACH2sB,UAAWW,EACZ,IAGLC,eAAgBxsB,GACd,IAAM,CAAC8B,EAAM2qB,kBACbb,IACE,IAAIc,EAAwB,GAI5B,OAHId,GAAaA,EAAY,IAC3Bc,EAAc,IAAI,IAAIvW,MAAMyV,IAAYe,KAAK,MAAM5pB,KAAI,CAACwI,EAAGqhB,IAAMA,KAE5DF,CAAW,GAEpB,CACE5tB,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQyW,UAAU,IAInEoU,mBAAoBA,IAAM/qB,EAAM4B,WAAWqnB,WAAWC,UAAY,EAElE8B,eAAgBA,KACd,MAAM,UAAE9B,GAAclpB,EAAM4B,WAAWqnB,WAEjCa,EAAY9pB,EAAM2qB,eAExB,OAAmB,IAAfb,GAIc,IAAdA,GAIGZ,EAAYY,EAAY,CAAC,EAGlCmB,aAAcA,IACLjrB,EAAM4pB,cAAazsB,GAAOA,EAAM,IAGzC+tB,SAAUA,IACDlrB,EAAM4pB,cAAazsB,GACjBA,EAAM,IAIjB8R,yBAA0BA,IAAMjP,EAAMiQ,sBACtCgH,sBAAuBA,MAElBjX,EAAMmrB,wBACPnrB,EAAME,QAAQ+W,wBAEdjX,EAAMmrB,uBACJnrB,EAAME,QAAQ+W,sBAAsBjX,IAGpCA,EAAME,QAAQqpB,mBAAqBvpB,EAAMmrB,uBACpCnrB,EAAMiP,2BAGRjP,EAAMmrB,0BAGfR,aAAcA,KAAM,IAAAS,EAClB,OACyB,OADzBA,EACEprB,EAAME,QAAQ4pB,WAASsB,EACvBjsB,KAAKksB,KACHrrB,EAAMiP,2BAA2BmH,KAAKnY,OACpC+B,EAAM4B,WAAWqnB,WAAWE,SAC/B,EAGN,GLrMHhU,EACAzN,GAsDK,SAASlG,EACdtB,GACc,IAAAorB,GACVprB,EAAQ4C,UAAY5C,EAAQyW,aAC9BhX,QAAQC,KAAK,8BAGf,IAAII,EAAQ,CAAEmB,UAAWsZ,GAEzB,MAAM8Q,EAAiBvrB,EAAMmB,UAAUgM,QAAO,CAACuP,EAAKtb,IAC3CC,OAAOC,OAAOob,EAA8B,MAAzBtb,EAAQkH,uBAAiB,EAAzBlH,EAAQkH,kBAAoBtI,KACrD,CAAC,GAeJ,IAAI6M,EAAe,IAEO,OAAxBye,EAAIprB,EAAQ2M,cAAYye,EAAI,CAAC,GAG/BtrB,EAAMmB,UAAUtD,SAAQuD,IAAW,IAAAoqB,EACjC3e,EAAsD,OAA1C2e,EAAG,MAAApqB,EAAQwG,qBAAR,EAAAxG,EAAQwG,gBAAkBiF,IAAa2e,EAAI3e,CAAY,IAGxE,MAAMsB,EAAyB,GAC/B,IAAIsd,GAAgB,EAEpB,MAAMC,EAAoC,CACxCvqB,UAAWsZ,EACXva,QAAS,IACJqrB,KACArrB,GAEL2M,eACA4B,OAAQkd,IACNxd,EAAOpQ,KAAK4tB,GAEPF,IACHA,GAAgB,EAIhBG,QAAQC,UACLC,MAAK,KACJ,KAAO3d,EAAOlQ,QACZkQ,EAAOuL,OAAPvL,GAEFsd,GAAgB,CAAK,IAEtBM,OAAMC,GACLC,YAAW,KACT,MAAMD,CAAK,MAGnB,EAEFE,MAAOA,KACLlsB,EAAM9C,SAAS8C,EAAM6M,aAAa,EAEpCsf,WAAYtvB,IACV,MAAMuvB,EAAaxvB,EAAiBC,EAASmD,EAAME,SACnDF,EAAME,QA3DYA,IAChBF,EAAME,QAAQmsB,aACTrsB,EAAME,QAAQmsB,aAAad,EAAgBrrB,GAG7C,IACFqrB,KACArrB,GAoDamsB,CAAaD,EAG5B,EAGHxqB,SAAUA,IACD5B,EAAME,QAAQ2H,MAGvB3K,SAAWL,IACT,MAAAmD,EAAME,QAAQosB,eAAdtsB,EAAME,QAAQosB,cAAgBzvB,EAAQ,EAGxC0vB,UAAWA,CAACpd,EAAYpQ,EAAe6H,KAAmB,IAAA+J,EAAA,OACZ,OADYA,EACxD,MAAA3Q,EAAME,QAAQssB,cAAd,EAAAxsB,EAAME,QAAQssB,SAAWrd,EAAKpQ,EAAO6H,IAAO+J,EAC3C,GAAE/J,EAAS,CAACA,EAAOvG,GAAItB,GAAOyH,KAAK,KAAOzH,GAAO,EAEpDwX,gBAAiBA,KACVvW,EAAMysB,mBACTzsB,EAAMysB,iBAAmBzsB,EAAME,QAAQqW,gBAAgBvW,IAGlDA,EAAMysB,oBAMfhd,YAAaA,IACJzP,EAAMiX,wBAEfyB,OAASrY,IACP,MAAM8O,EAAMnP,EAAMyP,cAAcG,SAASvP,GAEzC,IAAK8O,EAIH,MAAM,IAAIqV,MAGZ,OAAOrV,CAAG,EAEZud,qBAAsBxuB,GACpB,IAAM,CAAC8B,EAAME,QAAQysB,iBACrBA,IAAiB,IAAAC,EAKf,OAJAD,EAA8B,OAAjBC,EAAID,GAAaC,EAAI,CAAC,EAI5B,CACLxsB,OAAQ8mB,IACN,MAAM2F,EAAoB3F,EAAM9mB,OAAOH,OACpC8I,UAEH,OAAI8jB,EAAkBC,YACbD,EAAkBC,YAGvBD,EAAkB9L,WACb8L,EAAkBxsB,GAGpB,IAAI,EAGbib,KAAM4L,IAAK,IAAA6F,EAAAC,EAAA,OAA0C,OAA1CD,EAAI,OAAJC,EAAI9F,EAAM+F,gBAA4B,MAAlCD,EAA0Btb,cAA1B,EAAAsb,EAA0Btb,YAAYqb,EAAI,IAAI,KAC1D/sB,EAAMmB,UAAUgM,QAAO,CAACuP,EAAKtb,IACvBC,OAAOC,OAAOob,EAAgC,MAA3Btb,EAAQuG,yBAAmB,EAA3BvG,EAAQuG,wBACjC,CAAC,MACDglB,EACJ,GAEH,CACEluB,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQ4b,YAAY,EACjE9e,KAAK4F,IAITsqB,eAAgBA,IAAMltB,EAAME,QAAQiG,QAEpCzE,cAAexD,GACb,IAAM,CAAC8B,EAAMktB,oBACbC,IACE,MAAMC,EAAiB,SACrBD,EACAvmB,EACApG,GAEA,YAFK,IAALA,IAAAA,EAAQ,GAED2sB,EAAWlsB,KAAI8H,IACpB,MAAM9I,EM7PX,SACLD,EACA+I,EACAvI,EACAoG,GACuB,IAAAK,EAAAomB,EACvB,MAEMR,EAAoB,IAFJ7sB,EAAM0sB,0BAIvB3jB,GAGC+jB,EAAcD,EAAkBC,YAEtC,IAOI/L,EAPA1gB,EAEsD,OAFpD4G,EACgB,OADhBomB,EACJR,EAAkBxsB,IAAEgtB,EACnBP,EAAcA,EAAYQ,QAAQ,IAAK,UAAOzmB,GAASI,EACnB,kBAA7B4lB,EAAkBzsB,OACtBysB,EAAkBzsB,YAClByG,EA6BN,GAzBIgmB,EAAkB9L,WACpBA,EAAa8L,EAAkB9L,WACtB+L,IAGP/L,EADE+L,EAAYnqB,SAAS,KACT4qB,IACZ,IAAIjvB,EAASivB,EAEb,IAAK,MAAMvwB,KAAO8vB,EAAYhd,MAAM,KAAM,KAAA0d,EACxClvB,EAAe,OAATkvB,EAAGlvB,QAAM,EAANkvB,EAASxwB,EAMpB,CAEA,OAAOsB,CAAM,EAGDivB,GACXA,EAAoBV,EAAkBC,eAIxCzsB,EAQH,MAAM,IAAImkB,MAGZ,IAAIvkB,EAAiC,CACnCI,GAAK,GAAEX,OAAOW,KACd0gB,aACAna,OAAQA,EACRpG,QACAuI,UAAW8jB,EACX1mB,QAAS,GACTsnB,eAAgBvvB,GACd,IAAM,EAAC,KACP,KAAM,IAAAmI,EACJ,MAAO,CACLpG,KACG,OAAHoG,EAAGpG,EAAOkG,cAAP,EAAAE,EAAgBqnB,SAAQrwB,GAAKA,EAAEowB,mBACnC,GAEH,CACEzwB,IAA8C,wBAC9CyB,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQ4b,YAAY,IAGrEmC,eAAgB/f,GACd,IAAM,CAAC8B,EAAMid,wBACbY,IAAgB,IAAA8P,EACd,GAAI,OAAJA,EAAI1tB,EAAOkG,UAAPwnB,EAAgB1vB,OAAQ,CAC1B,IAAIgE,EAAchC,EAAOkG,QAAQunB,SAAQztB,GACvCA,EAAOge,mBAGT,OAAOJ,EAAa5b,EACtB,CAEA,MAAO,CAAChC,EAAgC,GAE1C,CACEjD,IAA8C,wBAC9CyB,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQ4b,YAAY,KAUvE,OALA7b,EAASD,EAAMmB,UAAUgM,QAAO,CAACuP,EAAKtb,IAC7BC,OAAOC,OAAOob,EAAK,MAAAtb,EAAQsH,kBAAR,EAAAtH,EAAQsH,aAAezI,EAAQD,KACxDC,GAGIA,CACT,CNmJ2ByI,CAAa1I,EAAO+I,EAAWvI,EAAOoG,GAE/CgnB,EAAoB7kB,EAS1B,OAJA9I,EAAOkG,QAAUynB,EAAkBznB,QAC/BinB,EAAeQ,EAAkBznB,QAASlG,EAAQO,EAAQ,GAC1D,GAEGP,CAAM,KAIjB,OAAOmtB,EAAeD,EAAW,GAEnC,CACEnwB,KAAK4F,EACLnE,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQ4b,YAAY,IAIrEE,kBAAmB9d,GACjB,IAAM,CAAC8B,EAAM0B,mBACbM,GACSA,EAAW0rB,SAAQztB,GACjBA,EAAOwtB,oBAGlB,CACEzwB,KAAK4F,EACLnE,MAAOA,KAAA,IAAA2E,EAAA,OAA4B,OAA5BA,EAAMpD,EAAME,QAAQ4C,UAAQM,EAAIpD,EAAME,QAAQ4b,YAAY,IAIrE+R,uBAAwB3vB,GACtB,IAAM,CAAC8B,EAAMgc,uBACb8R,GACSA,EAAY3gB,QAAO,CAAC4gB,EAAK9tB,KAC9B8tB,EAAI9tB,EAAOI,IAAMJ,EACV8tB,IACN,CAAC,IAEN,CACE/wB,KAAK4F,EACLnE,MAAOA,KAAA,IAAA8E,EAAA,OAA4B,OAA5BA,EAAMvD,EAAME,QAAQ4C,UAAQS,EAAIvD,EAAME,QAAQ4b,YAAY,IAIrEG,kBAAmB/d,GACjB,IAAM,CAAC8B,EAAM0B,gBAAiB1B,EAAMid,wBACpC,CAACjb,EAAY6b,IAEJA,EADW7b,EAAW0rB,SAAQztB,GAAUA,EAAOge,qBAGxD,CACEjhB,KAAK4F,EACLnE,MAAOA,KAAA,IAAAkF,EAAA,OAA4B,OAA5BA,EAAM3D,EAAME,QAAQ4C,UAAQa,EAAI3D,EAAME,QAAQ4b,YAAY,IAIrEzR,UAAWhI,GACMrC,EAAM6tB,yBAAyBxrB,IAgBlD,OANAhB,OAAOC,OAAOtB,EAAO0rB,GAErB1rB,EAAMmB,UAAUtD,SAAQuD,GACfC,OAAOC,OAAOtB,EAA0B,MAAnBoB,EAAQI,iBAAW,EAAnBJ,EAAQI,YAAcxB,MAG7CA,CACT,OO1UamQ,EAAYA,CACvBnQ,EACAK,EACAwoB,EACAmF,EACAxtB,EACAuQ,EACAkd,KAEA,IAAI9e,EAAsB,CACxB9O,KACAtB,MAAOivB,EACPnF,WACAroB,QACAytB,WACAC,aAAc,CAAC,EACfC,mBAAoB,CAAC,EACrB1c,SAAUpP,IACR,GAAI8M,EAAI+e,aAAatF,eAAevmB,GAClC,OAAO8M,EAAI+e,aAAa7rB,GAG1B,MAAMpC,EAASD,EAAMqK,UAAUhI,GAE/B,OAAW,MAANpC,GAAAA,EAAQ8gB,YAIb5R,EAAI+e,aAAa7rB,GAAYpC,EAAO8gB,WAClC5R,EAAI0Z,SACJmF,GAGK7e,EAAI+e,aAAa7rB,SATxB,CASiC,EAEnC+rB,gBAAiB/rB,IACf,GAAI8M,EAAIgf,mBAAmBvF,eAAevmB,GACxC,OAAO8M,EAAIgf,mBAAmB9rB,GAGhC,MAAMpC,EAASD,EAAMqK,UAAUhI,GAE/B,OAAW,MAANpC,GAAAA,EAAQ8gB,WAIR9gB,EAAO8I,UAAUqlB,iBAKtBjf,EAAIgf,mBAAmB9rB,GAAYpC,EAAO8I,UAAUqlB,gBAClDjf,EAAI0Z,SACJmF,GAGK7e,EAAIgf,mBAAmB9rB,KAT5B8M,EAAIgf,mBAAmB9rB,GAAY,CAAC8M,EAAIsC,SAASpP,IAC1C8M,EAAIgf,mBAAmB9rB,SANhC,CAcuC,EAEzC4qB,YAAa5qB,IAAQ,IAAA+O,EAAA,OACG,OADHA,EACnBjC,EAAIsC,SAASpP,IAAS+O,EAAIpR,EAAME,QAAQmuB,mBAAmB,EAC7Dtd,QAAS,MAAAA,EAAAA,EAAW,GACpBud,YAAaA,IAAM/wB,EAAU4R,EAAI4B,SAAS1T,GAAKA,EAAE0T,UACjDwd,aAAcA,IAAOpf,EAAI8e,SAAWjuB,EAAM0Y,OAAOvJ,EAAI8e,eAAYpnB,EACjE2nB,cAAeA,KACb,IAAIC,EAA2B,GAC3BC,EAAavf,EACjB,OAAa,CACX,MAAMwf,EAAYD,EAAWH,eAC7B,IAAKI,EAAW,MAChBF,EAAW1wB,KAAK4wB,GAChBD,EAAaC,CACf,CACA,OAAOF,EAAW/qB,SAAS,EAE7B0X,YAAald,GACX,IAAM,CAAC8B,EAAMic,uBACbha,GACSA,EAAYhB,KAAIhB,GChFxB,SACLD,EACAmP,EACAlP,EACAoC,GAEA,MAGMiZ,EAAgC,CACpCjb,GAAK,GAAE8O,EAAI9O,MAAMJ,EAAOI,KACxB8O,MACAlP,SACAwR,SAAUA,IAAMtC,EAAIsC,SAASpP,GAC7B4qB,YARqB2B,KAAA,IAAAC,EAAA,OACN,OADMA,EACrBvT,EAAK7J,YAAUod,EAAI7uB,EAAME,QAAQmuB,mBAAmB,EAQpDntB,WAAYhD,GACV,IAAM,CAAC8B,EAAOC,EAAQkP,EAAKmM,KAC3B,CAACtb,EAAOC,EAAQkP,EAAKmM,KAAS,CAC5Btb,QACAC,SACAkP,MACAmM,KAAMA,EACN7J,SAAU6J,EAAK7J,SACfwb,YAAa3R,EAAK2R,eAEpB,CACEjwB,KAAK4F,EACLnE,MAAOA,IAAMuB,EAAME,QAAQ4C,YAiBjC,OAZA9C,EAAMmB,UAAUtD,SAAQuD,IACtBC,OAAOC,OACLga,EACA,MAAAla,EAAQ0nB,gBAAR,EAAA1nB,EAAQ0nB,WACNxN,EACArb,EACAkP,EACAnP,GAEH,GACA,CAAC,GAEGsb,CACT,CDoCiBwN,CAAW9oB,EAAOmP,EAAmBlP,EAAQA,EAAOI,OAG/D,CACErD,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQqb,SAAS,IAIlE8E,uBAAwBniB,GACtB,IAAM,CAACiR,EAAIiM,iBACX+D,GACSA,EAAShS,QAAO,CAAC4gB,EAAKzS,KAC3ByS,EAAIzS,EAAKrb,OAAOI,IAAMib,EACfyS,IACN,CAAC,IAEN,CACE/wB,IAC2C,4BAC3CyB,MAAOA,KAAA,IAAAwE,EAAA,OAA4B,OAA5BA,EAAMjD,EAAME,QAAQ4C,UAAQG,EAAIjD,EAAME,QAAQqb,SAAS,KAKpE,IAAK,IAAIuP,EAAI,EAAGA,EAAI9qB,EAAMmB,UAAUlD,OAAQ6sB,IAAK,CAC/C,MAAM1pB,EAAUpB,EAAMmB,UAAU2pB,GAChCzpB,OAAOC,OAAO6N,EAAY,MAAP/N,GAAA,MAAAA,EAAS+O,eAAF,EAAP/O,EAAS+O,UAAYhB,EAAKnP,GAC/C,CAEA,OAAOmP,CAAG,EEhIL,SAASoH,IAGd,OAAOvW,GACL9B,GACE,IAAM,CAAC8B,EAAME,QAAQ4uB,QAEnBA,IAMA,MAAMrY,EAA4B,CAChCL,KAAM,GACNlH,SAAU,GACVU,SAAU,CAAC,GAGPmf,EAAa,SACjBC,EACAxuB,EACAmuB,QADK,IAALnuB,IAAAA,EAAQ,GAGR,MAAM4V,EAAO,GAEb,IAAK,IAAI0U,EAAI,EAAGA,EAAIkE,EAAa/wB,OAAQ6sB,IAAK,CAS5C,MAAM3b,EAAMgB,EACVnQ,EACAA,EAAMusB,UAAUyC,EAAalE,GAAKA,EAAG6D,GACrCK,EAAalE,GACbA,EACAtqB,OACAqG,EACS,MAAT8nB,OAAS,EAATA,EAAWtuB,IAWiB,IAAA4uB,EAA9B,GAPAxY,EAASvH,SAASnR,KAAKoR,GAEvBsH,EAAS7G,SAAST,EAAI9O,IAAM8O,EAE5BiH,EAAKrY,KAAKoR,GAGNnP,EAAME,QAAQgvB,WAChB/f,EAAIggB,gBAAkBnvB,EAAME,QAAQgvB,WAClCF,EAAalE,GACbA,GAIE,OAAJmE,EAAI9f,EAAIggB,kBAAJF,EAAqBhxB,SACvBkR,EAAI4B,QAAUge,EAAW5f,EAAIggB,gBAAiB3uB,EAAQ,EAAG2O,GAG/D,CAEA,OAAOiH,GAKT,OAFAK,EAASL,KAAO2Y,EAAWD,GAEpBrY,CAAQ,GAEjB,CACEzZ,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQyW,UAAU,EAC/D1X,SAAUA,KACRe,EAAMqpB,qBAAqB,GAIrC,CCjFO,SAASrZ,IAGd,OAAOhQ,GACL9B,GACE,IAAM,CAAC8B,EAAM4B,WAAW6hB,QAASzjB,EAAM6mB,0BACvC,CAACpD,EAAShN,KACR,IAAKA,EAASL,KAAKnY,QAAkB,MAAPwlB,IAAAA,EAASxlB,OACrC,OAAOwY,EAGT,MAAM2Y,EAAepvB,EAAM4B,WAAW6hB,QAEhC4L,EAA+B,GAG/BC,EAAmBF,EAAa7sB,QAAOoS,IAAI,IAAA4a,EAAA,OAC/C,OAD+CA,EAC/CvvB,EAAMqK,UAAUsK,EAAKtU,UAArB,EAAAkvB,EAA0BvJ,YAAY,IAGlCwJ,EAOF,CAAC,EAELF,EAAiBzxB,SAAQ4xB,IACvB,MAAMxvB,EAASD,EAAMqK,UAAUolB,EAAUpvB,IACpCJ,IAELuvB,EAAeC,EAAUpvB,IAAM,CAC7BqvB,cAAezvB,EAAO8I,UAAU2mB,cAChCC,cAAe1vB,EAAO8I,UAAU4mB,cAChCjM,UAAWzjB,EAAOokB,gBACnB,IAGH,MAAMuL,EAAYxZ,IAGhB,MAAMyZ,EAAa,IAAIzZ,GA0DvB,OAxDAyZ,EAAWlb,MAAK,CAACuF,EAAMC,KACrB,IAAK,IAAI2Q,EAAI,EAAGA,EAAIwE,EAAiBrxB,OAAQ6sB,GAAK,EAAG,KAAAgF,EACnD,MAAML,EAAYH,EAAiBxE,GAC7BiF,EAAaP,EAAeC,EAAUpvB,IACtC2vB,EAAwB,OAAlBF,EAAY,MAATL,OAAS,EAATA,EAAW/K,OAAIoL,EAE9B,GAAIC,EAAWL,cAAe,CAC5B,MAGMO,EAA+B,qBAHtB/V,EAAKzI,SAASge,EAAUpvB,IAIjC6vB,EAA+B,qBAHtB/V,EAAK1I,SAASge,EAAUpvB,IAKvC,GAAI4vB,GAAcC,EAAY,CAC5B,IAAIC,EACFF,GAAcC,EACV,EACAD,EACAF,EAAWL,eACVK,EAAWL,cAMlB,OAJIM,GAA4B,IAAlBG,IACZA,IAAkB,GAGbA,CACT,CACF,CAGA,IAAIC,EAAUL,EAAWrM,UAAUxJ,EAAMC,EAAMsV,EAAUpvB,IAEzD,GAAgB,IAAZ+vB,EASF,OARIJ,IACFI,IAAY,GAGVL,EAAWJ,gBACbS,IAAY,GAGPA,CAEX,CAEA,OAAOlW,EAAKnb,MAAQob,EAAKpb,KAAK,IAIhC8wB,EAAWhyB,SAAQsR,IAAO,IAAAyB,EACxBye,EAAetxB,KAAKoR,GAChB,OAAJyB,EAAIzB,EAAI4B,UAAJH,EAAa3S,SACfkR,EAAI4B,QAAU6e,EAASzgB,EAAI4B,SAC7B,IAGK8e,CAAU,EAGnB,MAAO,CACLzZ,KAAMwZ,EAASnZ,EAASL,MACxBlH,SAAUmgB,EACVzf,SAAU6G,EAAS7G,SACpB,GAEH,CACE5S,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQyW,UAAU,EAC/D1X,SAAUA,KACRe,EAAMqpB,qBAAqB,GAIrC,CCvHO,SAASpZ,IAGd,OAAOjQ,GACL9B,GACE,IAAM,CACJ8B,EAAM4B,WAAWmM,SACjB/N,EAAM+P,yBACN/P,EAAME,QAAQ+N,wBAEhB,CAACF,EAAU0I,EAAUxI,KAEhBwI,EAASL,KAAKnY,SACD,IAAb8P,IAAsB1M,OAAOmO,KAAK,MAAAzB,EAAAA,EAAY,CAAC,GAAG9P,OAE5CwY,EAGJxI,EAKEoiB,EAAW5Z,GAHTA,GAKX,CACEzZ,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQyW,UAAU,GAGvE,CAEO,SAAS0Z,EAAkC5Z,GAChD,MAAM6Z,EAA6B,GAE7BC,EAAaphB,IAAoB,IAAAyB,EACrC0f,EAAavyB,KAAKoR,GAEd,OAAAyB,EAAAzB,EAAI4B,UAAJH,EAAa3S,QAAUkR,EAAIO,iBAC7BP,EAAI4B,QAAQlT,QAAQ0yB,EACtB,EAKF,OAFA9Z,EAASL,KAAKvY,QAAQ0yB,GAEf,CACLna,KAAMka,EACNphB,SAAUuH,EAASvH,SACnBU,SAAU6G,EAAS7G,SAEvB,CCjDO,SAASqH,EAA6C5Y,GAG3D,OAAO2B,GACL9B,GACE,IAAM,CACJ8B,EAAM4B,WAAWqnB,WACjBjpB,EAAMiP,2BACNjP,EAAME,QAAQ+N,0BACVpH,EACA7G,EAAM4B,WAAWmM,YAEvB,CAACkb,EAAYxS,KACX,IAAKA,EAASL,KAAKnY,OACjB,OAAOwY,EAGT,MAAM,SAAE0S,EAAQ,UAAED,GAAcD,EAChC,IAAI,KAAE7S,EAAI,SAAElH,EAAQ,SAAEU,GAAa6G,EACnC,MAAM+Z,EAAYrH,EAAWD,EACvBuH,EAAUD,EAAYrH,EAI5B,IAAIuH,EAFJta,EAAOA,EAAK4N,MAAMwM,EAAWC,GAW3BC,EAPG1wB,EAAME,QAAQ+N,qBAOG,CAClBmI,OACAlH,WACAU,YATkBygB,EAAW,CAC7Bja,OACAlH,WACAU,aAUJ8gB,EAAkBxhB,SAAW,GAE7B,MAAMqhB,EAAaphB,IACjBuhB,EAAkBxhB,SAASnR,KAAKoR,GAC5BA,EAAI4B,QAAQ9S,QACdkR,EAAI4B,QAAQlT,QAAQ0yB,EACtB,EAKF,OAFAG,EAAkBta,KAAKvY,QAAQ0yB,GAExBG,CAAiB,GAE1B,CACE1zB,KAAK4F,EACLnE,MAAOA,KAAA,IAAAoE,EAAA,OAA4B,OAA5BA,EAAM7C,EAAME,QAAQ4C,UAAQD,EAAI7C,EAAME,QAAQyW,UAAU,GAGvE,2GChDO,SAASga,EACdC,EACA1J,GAEA,OAAQ0J,EAiBV,SAA0BC,GACxB,MACuB,oBAAdA,GACP,MACE,MAAMC,EAAQzvB,OAAO0vB,eAAeF,GACpC,OAAOC,EAAM5M,WAAa4M,EAAM5M,UAAU8M,gBAC3C,EAHD,EAKJ,CAdIC,CAHFJ,EAR+CD,IAYxB,oBAAdC,GAeX,SAA2BA,GACzB,MACuB,kBAAdA,GACuB,kBAAvBA,EAAUK,UACjB,CAAC,aAAc,qBAAqBvuB,SAASkuB,EAAUK,SAASC,YAEpE,CApBIC,CAAkBP,GAZlBQ,EAAAA,cAACT,EAAS1J,GAEV0J,EAHa,KAOjB,IACEC,CAHF,CA8BO,SAASS,EACdpxB,GAGA,MAAMqxB,EAA+C,CACnD1pB,MAAO,CAAC,EACRykB,cAAeA,OACf+B,oBAAqB,QAClBnuB,IAIEsxB,GAAYH,EAAAA,UAAe,KAAM,CACtCI,SAASjwB,EAAAA,EAAAA,IAAmB+vB,QAIvB1pB,EAAO3K,GAAYm0B,EAAAA,UAAe,IAAMG,EAASC,QAAQ5kB,eAmBhE,OAfA2kB,EAASC,QAAQtF,YAAWuF,IAAQ,IAC/BA,KACAxxB,EACH2H,MAAO,IACFA,KACA3H,EAAQ2H,OAIbykB,cAAezvB,IACbK,EAASL,GACT,MAAAqD,EAAQosB,eAARpsB,EAAQosB,cAAgBzvB,EAAQ,MAI7B20B,EAASC,OAClB", "sources": ["../node_modules/@tanstack/table-core/src/utils.ts", "../node_modules/@tanstack/table-core/src/core/headers.ts", "../node_modules/@tanstack/table-core/src/features/ColumnSizing.ts", "../node_modules/@tanstack/table-core/src/features/Expanding.ts", "../node_modules/@tanstack/table-core/src/filterFns.ts", "../node_modules/@tanstack/table-core/src/features/Filters.ts", "../node_modules/@tanstack/table-core/src/aggregationFns.ts", "../node_modules/@tanstack/table-core/src/features/Ordering.ts", "../node_modules/@tanstack/table-core/src/features/RowSelection.ts", "../node_modules/@tanstack/table-core/src/sortingFns.ts", "../node_modules/@tanstack/table-core/src/core/table.ts", "../node_modules/@tanstack/table-core/src/features/Visibility.ts", "../node_modules/@tanstack/table-core/src/features/Grouping.ts", "../node_modules/@tanstack/table-core/src/features/Pinning.ts", "../node_modules/@tanstack/table-core/src/features/Sorting.ts", "../node_modules/@tanstack/table-core/src/features/Pagination.ts", "../node_modules/@tanstack/table-core/src/core/column.ts", "../node_modules/@tanstack/table-core/src/core/row.ts", "../node_modules/@tanstack/table-core/src/core/cell.ts", "../node_modules/@tanstack/table-core/src/utils/getCoreRowModel.ts", "../node_modules/@tanstack/table-core/src/utils/getSortedRowModel.ts", "../node_modules/@tanstack/table-core/src/utils/getExpandedRowModel.ts", "../node_modules/@tanstack/table-core/src/utils/getPaginationRowModel.ts", "../node_modules/@tanstack/react-table/src/index.tsx"], "sourcesContent": ["import { TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = []\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n  ? AllowedIndexes<Tail, Keys | Tail['length']>\n  : Keys\n\nexport type DeepKeys<T> = unknown extends T\n  ? keyof T\n  : object extends T\n  ? string\n  : T extends readonly any[] & IsTuple<T>\n  ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>>\n  : T extends any[]\n  ? never & 'Dynamic length array indexing is not supported'\n  : T extends Date\n  ? never\n  : T extends object\n  ? (keyof T & string) | DeepKeysPrefix<T, keyof T>\n  : never\n\ntype DeepKeysPrefix<T, TPrefix> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> = T extends Record<string | number, any>\n  ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n    ? DeepValue<T[TBranch], TDeepProp>\n    : T[TProp & string]\n  : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): () => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return () => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n", "import { <PERSON><PERSON><PERSON>, Column, Header, HeaderGroup, Table } from '../types'\nimport { memo } from '../utils'\nimport { TableFeature } from './table'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  id: string\n  depth: number\n  headers: Header<TData, unknown>[]\n}\n\nexport interface HeaderContext<TData, TValue> {\n  table: Table<TData>\n  header: Header<TData, TValue>\n  column: Column<TData, TValue>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  id: string\n  index: number\n  depth: number\n  column: Column<TData, TValue>\n  headerGroup: HeaderGroup<TData>\n  subHeaders: Header<TData, TValue>[]\n  colSpan: number\n  rowSpan: number\n  getLeafHeaders: () => Header<TData, unknown>[]\n  isPlaceholder: boolean\n  placeholderId?: string\n  getContext: () => HeaderContext<TData, TValue>\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  getFooterGroups: () => HeaderGroup<TData>[]\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  getFlatHeaders: () => Header<TData, unknown>[]\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  getLeafHeaders: () => Header<TData, unknown>[]\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    Object.assign(header, feature.createHeader?.(header, table))\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): HeadersInstance<TData> => {\n    return {\n      // Header Groups\n\n      getHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, leafColumns, left, right) => {\n          const leftColumns =\n            left\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          const rightColumns =\n            right\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          const centerColumns = leafColumns.filter(\n            column => !left?.includes(column.id) && !right?.includes(column.id)\n          )\n\n          const headerGroups = buildHeaderGroups(\n            allColumns,\n            [...leftColumns, ...centerColumns, ...rightColumns],\n            table\n          )\n\n          return headerGroups\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getCenterHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, leafColumns, left, right) => {\n          leafColumns = leafColumns.filter(\n            column => !left?.includes(column.id) && !right?.includes(column.id)\n          )\n          return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'development' && 'getCenterHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.left,\n        ],\n        (allColumns, leafColumns, left) => {\n          const orderedLeafColumns =\n            left\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          return buildHeaderGroups(\n            allColumns,\n            orderedLeafColumns,\n            table,\n            'left'\n          )\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, leafColumns, right) => {\n          const orderedLeafColumns =\n            right\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          return buildHeaderGroups(\n            allColumns,\n            orderedLeafColumns,\n            table,\n            'right'\n          )\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      // Footer Groups\n\n      getFooterGroups: memo(\n        () => [table.getHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftFooterGroups: memo(\n        () => [table.getLeftHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getCenterFooterGroups: memo(\n        () => [table.getCenterHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'development' && 'getCenterFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightFooterGroups: memo(\n        () => [table.getRightHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      // Flat Headers\n\n      getFlatHeaders: memo(\n        () => [table.getHeaderGroups()],\n        headerGroups => {\n          return headerGroups\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftFlatHeaders: memo(\n        () => [table.getLeftHeaderGroups()],\n        left => {\n          return left\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getCenterFlatHeaders: memo(\n        () => [table.getCenterHeaderGroups()],\n        left => {\n          return left\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getCenterFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightFlatHeaders: memo(\n        () => [table.getRightHeaderGroups()],\n        left => {\n          return left\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      // Leaf Headers\n\n      getCenterLeafHeaders: memo(\n        () => [table.getCenterFlatHeaders()],\n        flatHeaders => {\n          return flatHeaders.filter(header => !header.subHeaders?.length)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getCenterLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftLeafHeaders: memo(\n        () => [table.getLeftFlatHeaders()],\n        flatHeaders => {\n          return flatHeaders.filter(header => !header.subHeaders?.length)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightLeafHeaders: memo(\n        () => [table.getRightFlatHeaders()],\n        flatHeaders => {\n          return flatHeaders.filter(header => !header.subHeaders?.length)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeafHeaders: memo(\n        () => [\n          table.getLeftHeaderGroups(),\n          table.getCenterHeaderGroups(),\n          table.getRightHeaderGroups(),\n        ],\n        (left, center, right) => {\n          return [\n            ...(left[0]?.headers ?? []),\n            ...(center[0]?.headers ?? []),\n            ...(right[0]?.headers ?? []),\n          ]\n            .map(header => {\n              return header.getLeafHeaders()\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n    }\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { TableFeature } from '../core/table'\nimport { <PERSON><PERSON><PERSON>, Column, Header, OnChangeFn, Table, Updater } from '../types'\nimport { makeStateUpdater } from '../utils'\nimport { ColumnPinningPosition } from './Pinning'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  startOffset: null | number\n  startSize: null | number\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  columnSizingStart: [string, number][]\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport interface ColumnSizingOptions {\n  enableColumnResizing?: boolean\n  columnResizeMode?: ColumnResizeMode\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport interface ColumnSizingDefaultOptions {\n  columnResizeMode: ColumnResizeMode\n  onColumnSizingChange: OnChangeFn<ColumnSizingState>\n  onColumnSizingInfoChange: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport interface ColumnSizingInstance {\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n  resetColumnSizing: (defaultState?: boolean) => void\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  getTotalSize: () => number\n  getLeftTotalSize: () => number\n  getCenterTotalSize: () => number\n  getRightTotalSize: () => number\n}\n\nexport interface ColumnSizingColumnDef {\n  enableResizing?: boolean\n  size?: number\n  minSize?: number\n  maxSize?: number\n}\n\nexport interface ColumnSizingColumn {\n  getSize: () => number\n  getStart: (position?: ColumnPinningPosition) => number\n  getCanResize: () => boolean\n  getIsResizing: () => boolean\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  getSize: () => number\n  getStart: (position?: ColumnPinningPosition) => number\n  getResizeHandler: () => (event: unknown) => void\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): ColumnSizingColumn => {\n    return {\n      getSize: () => {\n        const columnSize = table.getState().columnSizing[column.id]\n\n        return Math.min(\n          Math.max(\n            column.columnDef.minSize ?? defaultColumnSizing.minSize,\n            columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n          ),\n          column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n        )\n      },\n      getStart: position => {\n        const columns = !position\n          ? table.getVisibleLeafColumns()\n          : position === 'left'\n          ? table.getLeftVisibleLeafColumns()\n          : table.getRightVisibleLeafColumns()\n\n        const index = columns.findIndex(d => d.id === column.id)\n\n        if (index > 0) {\n          const prevSiblingColumn = columns[index - 1]!\n\n          return (\n            prevSiblingColumn.getStart(position) + prevSiblingColumn.getSize()\n          )\n        }\n\n        return 0\n      },\n      resetSize: () => {\n        table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n          return rest\n        })\n      },\n      getCanResize: () => {\n        return (\n          (column.columnDef.enableResizing ?? true) &&\n          (table.options.enableColumnResizing ?? true)\n        )\n      },\n      getIsResizing: () => {\n        return table.getState().columnSizingInfo.isResizingColumn === column.id\n      },\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): ColumnSizingHeader => {\n    return {\n      getSize: () => {\n        let sum = 0\n\n        const recurse = (header: Header<TData, TValue>) => {\n          if (header.subHeaders.length) {\n            header.subHeaders.forEach(recurse)\n          } else {\n            sum += header.column.getSize() ?? 0\n          }\n        }\n\n        recurse(header)\n\n        return sum\n      },\n      getStart: () => {\n        if (header.index > 0) {\n          const prevSiblingHeader =\n            header.headerGroup.headers[header.index - 1]!\n          return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n        }\n\n        return 0\n      },\n      getResizeHandler: () => {\n        const column = table.getColumn(header.column.id)\n        const canResize = column?.getCanResize()\n\n        return (e: unknown) => {\n          if (!column || !canResize) {\n            return\n          }\n\n          ;(e as any).persist?.()\n\n          if (isTouchStartEvent(e)) {\n            // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n            if (e.touches && e.touches.length > 1) {\n              return\n            }\n          }\n\n          const startSize = header.getSize()\n\n          const columnSizingStart: [string, number][] = header\n            ? header\n                .getLeafHeaders()\n                .map(d => [d.column.id, d.column.getSize()])\n            : [[column.id, column.getSize()]]\n\n          const clientX = isTouchStartEvent(e)\n            ? Math.round(e.touches[0]!.clientX)\n            : (e as MouseEvent).clientX\n\n          const newColumnSizing: ColumnSizingState = {}\n\n          const updateOffset = (\n            eventType: 'move' | 'end',\n            clientXPos?: number\n          ) => {\n            if (typeof clientXPos !== 'number') {\n              return\n            }\n\n            table.setColumnSizingInfo(old => {\n              const deltaOffset = clientXPos - (old?.startOffset ?? 0)\n              const deltaPercentage = Math.max(\n                deltaOffset / (old?.startSize ?? 0),\n                -0.999999\n              )\n\n              old.columnSizingStart.forEach(([columnId, headerSize]) => {\n                newColumnSizing[columnId] =\n                  Math.round(\n                    Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                  ) / 100\n              })\n\n              return {\n                ...old,\n                deltaOffset,\n                deltaPercentage,\n              }\n            })\n\n            if (\n              table.options.columnResizeMode === 'onChange' ||\n              eventType === 'end'\n            ) {\n              table.setColumnSizing(old => ({\n                ...old,\n                ...newColumnSizing,\n              }))\n            }\n          }\n\n          const onMove = (clientXPos?: number) =>\n            updateOffset('move', clientXPos)\n\n          const onEnd = (clientXPos?: number) => {\n            updateOffset('end', clientXPos)\n\n            table.setColumnSizingInfo(old => ({\n              ...old,\n              isResizingColumn: false,\n              startOffset: null,\n              startSize: null,\n              deltaOffset: null,\n              deltaPercentage: null,\n              columnSizingStart: [],\n            }))\n          }\n\n          const mouseEvents = {\n            moveHandler: (e: MouseEvent) => onMove(e.clientX),\n            upHandler: (e: MouseEvent) => {\n              document.removeEventListener('mousemove', mouseEvents.moveHandler)\n              document.removeEventListener('mouseup', mouseEvents.upHandler)\n              onEnd(e.clientX)\n            },\n          }\n\n          const touchEvents = {\n            moveHandler: (e: TouchEvent) => {\n              if (e.cancelable) {\n                e.preventDefault()\n                e.stopPropagation()\n              }\n              onMove(e.touches[0]!.clientX)\n              return false\n            },\n            upHandler: (e: TouchEvent) => {\n              document.removeEventListener('touchmove', touchEvents.moveHandler)\n              document.removeEventListener('touchend', touchEvents.upHandler)\n              if (e.cancelable) {\n                e.preventDefault()\n                e.stopPropagation()\n              }\n              onEnd(e.touches[0]?.clientX)\n            },\n          }\n\n          const passiveIfSupported = passiveEventSupported()\n            ? { passive: false }\n            : false\n\n          if (isTouchStartEvent(e)) {\n            document.addEventListener(\n              'touchmove',\n              touchEvents.moveHandler,\n              passiveIfSupported\n            )\n            document.addEventListener(\n              'touchend',\n              touchEvents.upHandler,\n              passiveIfSupported\n            )\n          } else {\n            document.addEventListener(\n              'mousemove',\n              mouseEvents.moveHandler,\n              passiveIfSupported\n            )\n            document.addEventListener(\n              'mouseup',\n              mouseEvents.upHandler,\n              passiveIfSupported\n            )\n          }\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            startOffset: clientX,\n            startSize,\n            deltaOffset: 0,\n            deltaPercentage: 0,\n            columnSizingStart,\n            isResizingColumn: column.id,\n          }))\n        }\n      },\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingInstance => {\n    return {\n      setColumnSizing: updater => table.options.onColumnSizingChange?.(updater),\n      setColumnSizingInfo: updater =>\n        table.options.onColumnSizingInfoChange?.(updater),\n      resetColumnSizing: defaultState => {\n        table.setColumnSizing(\n          defaultState ? {} : table.initialState.columnSizing ?? {}\n        )\n      },\n      resetHeaderSizeInfo: defaultState => {\n        table.setColumnSizingInfo(\n          defaultState\n            ? getDefaultColumnSizingInfoState()\n            : table.initialState.columnSizingInfo ??\n                getDefaultColumnSizingInfoState()\n        )\n      },\n      getTotalSize: () =>\n        table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n      getLeftTotalSize: () =>\n        table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n      getCenterTotalSize: () =>\n        table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n      getRightTotalSize: () =>\n        table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n    }\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, Row, Updater, RowData } from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  toggleExpanded: (expanded?: boolean) => void\n  getIsExpanded: () => boolean\n  getCanExpand: () => boolean\n  getToggleExpandedHandler: () => () => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  manualExpanding?: boolean\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  autoResetExpanded?: boolean\n  enableExpanding?: boolean\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n  resetExpanded: (defaultState?: boolean) => void\n  getCanSomeRowsExpand: () => boolean\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  getIsSomeRowsExpanded: () => boolean\n  getIsAllRowsExpanded: () => boolean\n  getExpandedDepth: () => number\n  getExpandedRowModel: () => RowModel<TData>\n  _getExpandedRowModel?: () => RowModel<TData>\n  getPreExpandedRowModel: () => RowModel<TData>\n}\n\n//\n\nexport const Expanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedInstance<TData> => {\n    let registered = false\n    let queued = false\n\n    return {\n      _autoResetExpanded: () => {\n        if (!registered) {\n          table._queue(() => {\n            registered = true\n          })\n          return\n        }\n\n        if (\n          table.options.autoResetAll ??\n          table.options.autoResetExpanded ??\n          !table.options.manualExpanding\n        ) {\n          if (queued) return\n          queued = true\n          table._queue(() => {\n            table.resetExpanded()\n            queued = false\n          })\n        }\n      },\n      setExpanded: updater => table.options.onExpandedChange?.(updater),\n      toggleAllRowsExpanded: expanded => {\n        if (expanded ?? !table.getIsAllRowsExpanded()) {\n          table.setExpanded(true)\n        } else {\n          table.setExpanded({})\n        }\n      },\n      resetExpanded: defaultState => {\n        table.setExpanded(\n          defaultState ? {} : table.initialState?.expanded ?? {}\n        )\n      },\n      getCanSomeRowsExpand: () => {\n        return table\n          .getPrePaginationRowModel()\n          .flatRows.some(row => row.getCanExpand())\n      },\n      getToggleAllRowsExpandedHandler: () => {\n        return (e: unknown) => {\n          ;(e as any).persist?.()\n          table.toggleAllRowsExpanded()\n        }\n      },\n      getIsSomeRowsExpanded: () => {\n        const expanded = table.getState().expanded\n        return expanded === true || Object.values(expanded).some(Boolean)\n      },\n      getIsAllRowsExpanded: () => {\n        const expanded = table.getState().expanded\n\n        // If expanded is true, save some cycles and return true\n        if (typeof expanded === 'boolean') {\n          return expanded === true\n        }\n\n        if (!Object.keys(expanded).length) {\n          return false\n        }\n\n        // If any row is not expanded, return false\n        if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n          return false\n        }\n\n        // They must all be expanded :shrug:\n        return true\n      },\n      getExpandedDepth: () => {\n        let maxDepth = 0\n\n        const rowIds =\n          table.getState().expanded === true\n            ? Object.keys(table.getRowModel().rowsById)\n            : Object.keys(table.getState().expanded)\n\n        rowIds.forEach(id => {\n          const splitId = id.split('.')\n          maxDepth = Math.max(maxDepth, splitId.length)\n        })\n\n        return maxDepth\n      },\n      getPreExpandedRowModel: () => table.getSortedRowModel(),\n      getExpandedRowModel: () => {\n        if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n          table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n        }\n\n        if (table.options.manualExpanding || !table._getExpandedRowModel) {\n          return table.getPreExpandedRowModel()\n        }\n\n        return table._getExpandedRowModel()\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): ExpandedRow => {\n    return {\n      toggleExpanded: expanded => {\n        table.setExpanded(old => {\n          const exists = old === true ? true : !!old?.[row.id]\n\n          let oldExpanded: ExpandedStateList = {}\n\n          if (old === true) {\n            Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n              oldExpanded[rowId] = true\n            })\n          } else {\n            oldExpanded = old\n          }\n\n          expanded = expanded ?? !exists\n\n          if (!exists && expanded) {\n            return {\n              ...oldExpanded,\n              [row.id]: true,\n            }\n          }\n\n          if (exists && !expanded) {\n            const { [row.id]: _, ...rest } = oldExpanded\n            return rest\n          }\n\n          return old\n        })\n      },\n      getIsExpanded: () => {\n        const expanded = table.getState().expanded\n\n        return !!(\n          table.options.getIsRowExpanded?.(row) ??\n          (expanded === true || expanded?.[row.id])\n        )\n      },\n      getCanExpand: () => {\n        return (\n          table.options.getRowCanExpand?.(row) ??\n          ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n        )\n      },\n      getToggleExpandedHandler: () => {\n        const canExpand = row.getCanExpand()\n\n        return () => {\n          if (!canExpand) return\n          row.toggleExpanded()\n        }\n      },\n    }\n  },\n}\n", "import { FilterFn } from './features/Filters'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  FilterMeta,\n  FilterFns,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface FiltersTableState {\n  columnFilters: ColumnFiltersState\n  globalFilter: any\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  id: string\n  resolvedValue: unknown\n  filterFn: FilterFn<TData>\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n\n  resolveFilterValue?: TransformFilterValueFn<TData>\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface FiltersColumnDef<TData extends RowData> {\n  filterFn?: FilterFnOption<TData>\n  enableColumnFilter?: boolean\n  enableGlobalFilter?: boolean\n}\n\nexport interface FiltersColumn<TData extends RowData> {\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  getFilterFn: () => FilterFn<TData> | undefined\n  setFilterValue: (updater: Updater<any>) => void\n  getCanFilter: () => boolean\n  getCanGlobalFilter: () => boolean\n  getFacetedRowModel: () => RowModel<TData>\n  _getFacetedRowModel?: () => RowModel<TData>\n  getIsFiltered: () => boolean\n  getFilterValue: () => unknown\n  getFilterIndex: () => number\n  getFacetedUniqueValues: () => Map<any, number>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n}\n\nexport interface FiltersRow<TData extends RowData> {\n  columnFilters: Record<string, boolean>\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface FiltersOptionsBase<TData extends RowData> {\n  enableFilters?: boolean\n  manualFiltering?: boolean\n  filterFromLeafRows?: boolean\n  maxLeafRowFilterDepth?: number\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n\n  // Column\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n  enableColumnFilters?: boolean\n\n  // Global\n  globalFilterFn?: FilterFnOption<TData>\n  onGlobalFilterChange?: OnChangeFn<any>\n  enableGlobalFilter?: boolean\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n\n  // Faceting\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface FiltersOptions<TData extends RowData>\n  extends FiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface FiltersInstance<TData extends RowData> {\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n\n  resetColumnFilters: (defaultState?: boolean) => void\n\n  // Column Filters\n  getPreFilteredRowModel: () => RowModel<TData>\n  getFilteredRowModel: () => RowModel<TData>\n  _getFilteredRowModel?: () => RowModel<TData>\n\n  // Global Filters\n  setGlobalFilter: (updater: Updater<any>) => void\n  resetGlobalFilter: (defaultState?: boolean) => void\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n}\n\n//\n\nexport const Filters: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): FiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): FiltersTableState => {\n    return {\n      columnFilters: [],\n      globalFilter: undefined,\n      // filtersProgress: 1,\n      // facetProgress: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): FiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as FiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): FiltersColumn<TData> => {\n    return {\n      getAutoFilterFn: () => {\n        const firstRow = table.getCoreRowModel().flatRows[0]\n\n        const value = firstRow?.getValue(column.id)\n\n        if (typeof value === 'string') {\n          return filterFns.includesString\n        }\n\n        if (typeof value === 'number') {\n          return filterFns.inNumberRange\n        }\n\n        if (typeof value === 'boolean') {\n          return filterFns.equals\n        }\n\n        if (value !== null && typeof value === 'object') {\n          return filterFns.equals\n        }\n\n        if (Array.isArray(value)) {\n          return filterFns.arrIncludes\n        }\n\n        return filterFns.weakEquals\n      },\n      getFilterFn: () => {\n        return isFunction(column.columnDef.filterFn)\n          ? column.columnDef.filterFn\n          : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          // @ts-ignore \n          : table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n      },\n      getCanFilter: () => {\n        return (\n          (column.columnDef.enableColumnFilter ?? true) &&\n          (table.options.enableColumnFilters ?? true) &&\n          (table.options.enableFilters ?? true) &&\n          !!column.accessorFn\n        )\n      },\n\n      getCanGlobalFilter: () => {\n        return (\n          (column.columnDef.enableGlobalFilter ?? true) &&\n          (table.options.enableGlobalFilter ?? true) &&\n          (table.options.enableFilters ?? true) &&\n          (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n          !!column.accessorFn\n        )\n      },\n\n      getIsFiltered: () => column.getFilterIndex() > -1,\n\n      getFilterValue: () =>\n        table.getState().columnFilters?.find(d => d.id === column.id)?.value,\n\n      getFilterIndex: () =>\n        table.getState().columnFilters?.findIndex(d => d.id === column.id) ??\n        -1,\n\n      setFilterValue: value => {\n        table.setColumnFilters(old => {\n          const filterFn = column.getFilterFn()\n          const previousfilter = old?.find(d => d.id === column.id)\n\n          const newFilter = functionalUpdate(\n            value,\n            previousfilter ? previousfilter.value : undefined\n          )\n\n          //\n          if (\n            shouldAutoRemoveFilter(\n              filterFn as FilterFn<TData>,\n              newFilter,\n              column\n            )\n          ) {\n            return old?.filter(d => d.id !== column.id) ?? []\n          }\n\n          const newFilterObj = { id: column.id, value: newFilter }\n\n          if (previousfilter) {\n            return (\n              old?.map(d => {\n                if (d.id === column.id) {\n                  return newFilterObj\n                }\n                return d\n              }) ?? []\n            )\n          }\n\n          if (old?.length) {\n            return [...old, newFilterObj]\n          }\n\n          return [newFilterObj]\n        })\n      },\n      _getFacetedRowModel:\n        table.options.getFacetedRowModel &&\n        table.options.getFacetedRowModel(table, column.id),\n      getFacetedRowModel: () => {\n        if (!column._getFacetedRowModel) {\n          return table.getPreFilteredRowModel()\n        }\n\n        return column._getFacetedRowModel()\n      },\n      _getFacetedUniqueValues:\n        table.options.getFacetedUniqueValues &&\n        table.options.getFacetedUniqueValues(table, column.id),\n      getFacetedUniqueValues: () => {\n        if (!column._getFacetedUniqueValues) {\n          return new Map()\n        }\n\n        return column._getFacetedUniqueValues()\n      },\n      _getFacetedMinMaxValues:\n        table.options.getFacetedMinMaxValues &&\n        table.options.getFacetedMinMaxValues(table, column.id),\n      getFacetedMinMaxValues: () => {\n        if (!column._getFacetedMinMaxValues) {\n          return undefined\n        }\n\n        return column._getFacetedMinMaxValues()\n      },\n      // () => [column.getFacetedRowModel()],\n      // facetedRowModel => getRowModelMinMaxValues(facetedRowModel, column.id),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): FiltersRow<TData> => {\n    return {\n      columnFilters: {},\n      columnFiltersMeta: {},\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): FiltersInstance<TData> => {\n    return {\n      getGlobalAutoFilterFn: () => {\n        return filterFns.includesString\n      },\n\n      getGlobalFilterFn: () => {\n        const { globalFilterFn: globalFilterFn } = table.options\n\n        return isFunction(globalFilterFn)\n          ? globalFilterFn\n          : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          // @ts-ignore\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n      },\n\n      setColumnFilters: (updater: Updater<ColumnFiltersState>) => {\n        const leafColumns = table.getAllLeafColumns()\n\n        const updateFn = (old: ColumnFiltersState) => {\n          return functionalUpdate(updater, old)?.filter(filter => {\n            const column = leafColumns.find(d => d.id === filter.id)\n\n            if (column) {\n              const filterFn = column.getFilterFn()\n\n              if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n                return false\n              }\n            }\n\n            return true\n          })\n        }\n\n        table.options.onColumnFiltersChange?.(updateFn)\n      },\n\n      setGlobalFilter: updater => {\n        table.options.onGlobalFilterChange?.(updater)\n      },\n\n      resetGlobalFilter: defaultState => {\n        table.setGlobalFilter(\n          defaultState ? undefined : table.initialState.globalFilter\n        )\n      },\n\n      resetColumnFilters: defaultState => {\n        table.setColumnFilters(\n          defaultState ? [] : table.initialState?.columnFilters ?? []\n        )\n      },\n\n      getPreFilteredRowModel: () => table.getCoreRowModel(),\n      getFilteredRowModel: () => {\n        if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n          table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n        }\n\n        if (table.options.manualFiltering || !table._getFilteredRowModel) {\n          return table.getPreFilteredRowModel()\n        }\n\n        return table._getFilteredRowModel()\n      },\n\n      _getGlobalFacetedRowModel:\n        table.options.getFacetedRowModel &&\n        table.options.getFacetedRowModel(table, '__global__'),\n\n      getGlobalFacetedRowModel: () => {\n        if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n          return table.getPreFilteredRowModel()\n        }\n\n        return table._getGlobalFacetedRowModel()\n      },\n\n      _getGlobalFacetedUniqueValues:\n        table.options.getFacetedUniqueValues &&\n        table.options.getFacetedUniqueValues(table, '__global__'),\n      getGlobalFacetedUniqueValues: () => {\n        if (!table._getGlobalFacetedUniqueValues) {\n          return new Map()\n        }\n\n        return table._getGlobalFacetedUniqueValues()\n      },\n\n      _getGlobalFacetedMinMaxValues:\n        table.options.getFacetedMinMaxValues &&\n        table.options.getFacetedMinMaxValues(table, '__global__'),\n      getGlobalFacetedMinMaxValues: () => {\n        if (!table._getGlobalFacetedMinMaxValues) {\n          return\n        }\n\n        return table._getGlobalFacetedMinMaxValues()\n      },\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/Grouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { makeStateUpdater, memo } from '../utils'\n\nimport { Table, OnChangeFn, Updater, Column, RowData } from '../types'\n\nimport { orderColumns } from './Grouping'\nimport { TableFeature } from '../core/table'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n  resetColumnOrder: (defaultState?: boolean) => void\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n}\n\n//\n\nexport const Ordering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderInstance<TData> => {\n    return {\n      setColumnOrder: updater => table.options.onColumnOrderChange?.(updater),\n      resetColumnOrder: defaultState => {\n        table.setColumnOrder(\n          defaultState ? [] : table.initialState.columnOrder ?? []\n        )\n      },\n      _getOrderColumnsFn: memo(\n        () => [\n          table.getState().columnOrder,\n          table.getState().grouping,\n          table.options.groupedColumnMode,\n        ],\n        (columnOrder, grouping, groupedColumnMode) => columns => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getOrderColumnsFn',\n          // debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, Row, RowModel, Updater, RowData } from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  getIsSelected: () => boolean\n  getIsSomeSelected: () => boolean\n  getIsAllSubRowsSelected: () => boolean\n  getCanSelect: () => boolean\n  getCanMultiSelect: () => boolean\n  getCanSelectSubRows: () => boolean\n  toggleSelected: (value?: boolean) => void\n  getToggleSelectedHandler: () => (event: unknown) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  resetRowSelection: (defaultState?: boolean) => void\n  getIsAllRowsSelected: () => boolean\n  getIsAllPageRowsSelected: () => boolean\n  getIsSomeRowsSelected: () => boolean\n  getIsSomePageRowsSelected: () => boolean\n  toggleAllRowsSelected: (value?: boolean) => void\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  getPreSelectedRowModel: () => RowModel<TData>\n  getSelectedRowModel: () => RowModel<TData>\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  getGroupedSelectedRowModel: () => RowModel<TData>\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionInstance<TData> => {\n    return {\n      setRowSelection: updater => table.options.onRowSelectionChange?.(updater),\n      resetRowSelection: defaultState =>\n        table.setRowSelection(\n          defaultState ? {} : table.initialState.rowSelection ?? {}\n        ),\n      toggleAllRowsSelected: value => {\n        table.setRowSelection(old => {\n          value =\n            typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n          const rowSelection = { ...old }\n\n          const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n          // We don't use `mutateRowIsSelected` here for performance reasons.\n          // All of the rows are flat already, so it wouldn't be worth it\n          if (value) {\n            preGroupedFlatRows.forEach(row => {\n              if (!row.getCanSelect()) {\n                return\n              }\n              rowSelection[row.id] = true\n            })\n          } else {\n            preGroupedFlatRows.forEach(row => {\n              delete rowSelection[row.id]\n            })\n          }\n\n          return rowSelection\n        })\n      },\n      toggleAllPageRowsSelected: value =>\n        table.setRowSelection(old => {\n          const resolvedValue =\n            typeof value !== 'undefined'\n              ? value\n              : !table.getIsAllPageRowsSelected()\n\n          const rowSelection: RowSelectionState = { ...old }\n\n          table.getRowModel().rows.forEach(row => {\n            mutateRowIsSelected(rowSelection, row.id, resolvedValue, table)\n          })\n\n          return rowSelection\n        }),\n\n      // addRowSelectionRange: rowId => {\n      //   const {\n      //     rows,\n      //     rowsById,\n      //     options: { selectGroupingRows, selectSubRows },\n      //   } = table\n\n      //   const findSelectedRow = (rows: Row[]) => {\n      //     let found\n      //     rows.find(d => {\n      //       if (d.getIsSelected()) {\n      //         found = d\n      //         return true\n      //       }\n      //       const subFound = findSelectedRow(d.subRows || [])\n      //       if (subFound) {\n      //         found = subFound\n      //         return true\n      //       }\n      //       return false\n      //     })\n      //     return found\n      //   }\n\n      //   const firstRow = findSelectedRow(rows) || rows[0]\n      //   const lastRow = rowsById[rowId]\n\n      //   let include = false\n      //   const selectedRowIds = {}\n\n      //   const addRow = (row: Row) => {\n      //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n      //       rowsById,\n      //       selectGroupingRows: selectGroupingRows!,\n      //       selectSubRows: selectSubRows!,\n      //     })\n      //   }\n\n      //   table.rows.forEach(row => {\n      //     const isFirstRow = row.id === firstRow.id\n      //     const isLastRow = row.id === lastRow.id\n\n      //     if (isFirstRow || isLastRow) {\n      //       if (!include) {\n      //         include = true\n      //       } else if (include) {\n      //         addRow(row)\n      //         include = false\n      //       }\n      //     }\n\n      //     if (include) {\n      //       addRow(row)\n      //     }\n      //   })\n\n      //   table.setRowSelection(selectedRowIds)\n      // },\n      getPreSelectedRowModel: () => table.getCoreRowModel(),\n      getSelectedRowModel: memo(\n        () => [table.getState().rowSelection, table.getCoreRowModel()],\n        (rowSelection, rowModel) => {\n          if (!Object.keys(rowSelection).length) {\n            return {\n              rows: [],\n              flatRows: [],\n              rowsById: {},\n            }\n          }\n\n          return selectRowsFn(table, rowModel)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getSelectedRowModel',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      getFilteredSelectedRowModel: memo(\n        () => [table.getState().rowSelection, table.getFilteredRowModel()],\n        (rowSelection, rowModel) => {\n          if (!Object.keys(rowSelection).length) {\n            return {\n              rows: [],\n              flatRows: [],\n              rowsById: {},\n            }\n          }\n\n          return selectRowsFn(table, rowModel)\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' &&\n            'getFilteredSelectedRowModel',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      getGroupedSelectedRowModel: memo(\n        () => [table.getState().rowSelection, table.getSortedRowModel()],\n        (rowSelection, rowModel) => {\n          if (!Object.keys(rowSelection).length) {\n            return {\n              rows: [],\n              flatRows: [],\n              rowsById: {},\n            }\n          }\n\n          return selectRowsFn(table, rowModel)\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' &&\n            'getGroupedSelectedRowModel',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      ///\n\n      // getGroupingRowCanSelect: rowId => {\n      //   const row = table.getRow(rowId)\n\n      //   if (!row) {\n      //     throw new Error()\n      //   }\n\n      //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n      //     return table.options.enableGroupingRowSelection(row)\n      //   }\n\n      //   return table.options.enableGroupingRowSelection ?? false\n      // },\n\n      getIsAllRowsSelected: () => {\n        const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n        const { rowSelection } = table.getState()\n\n        let isAllRowsSelected = Boolean(\n          preGroupedFlatRows.length && Object.keys(rowSelection).length\n        )\n\n        if (isAllRowsSelected) {\n          if (\n            preGroupedFlatRows.some(\n              row => row.getCanSelect() && !rowSelection[row.id]\n            )\n          ) {\n            isAllRowsSelected = false\n          }\n        }\n\n        return isAllRowsSelected\n      },\n\n      getIsAllPageRowsSelected: () => {\n        const paginationFlatRows = table\n          .getPaginationRowModel()\n          .flatRows.filter(row => row.getCanSelect())\n        const { rowSelection } = table.getState()\n\n        let isAllPageRowsSelected = !!paginationFlatRows.length\n\n        if (\n          isAllPageRowsSelected &&\n          paginationFlatRows.some(row => !rowSelection[row.id])\n        ) {\n          isAllPageRowsSelected = false\n        }\n\n        return isAllPageRowsSelected\n      },\n\n      getIsSomeRowsSelected: () => {\n        const totalSelected = Object.keys(\n          table.getState().rowSelection ?? {}\n        ).length\n        return (\n          totalSelected > 0 &&\n          totalSelected < table.getFilteredRowModel().flatRows.length\n        )\n      },\n\n      getIsSomePageRowsSelected: () => {\n        const paginationFlatRows = table.getPaginationRowModel().flatRows\n        return table.getIsAllPageRowsSelected()\n          ? false\n          : paginationFlatRows\n              .filter(row => row.getCanSelect())\n              .some(d => d.getIsSelected() || d.getIsSomeSelected())\n      },\n\n      getToggleAllRowsSelectedHandler: () => {\n        return (e: unknown) => {\n          table.toggleAllRowsSelected(\n            ((e as MouseEvent).target as HTMLInputElement).checked\n          )\n        }\n      },\n\n      getToggleAllPageRowsSelectedHandler: () => {\n        return (e: unknown) => {\n          table.toggleAllPageRowsSelected(\n            ((e as MouseEvent).target as HTMLInputElement).checked\n          )\n        }\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): RowSelectionRow => {\n    return {\n      toggleSelected: value => {\n        const isSelected = row.getIsSelected()\n\n        table.setRowSelection(old => {\n          value = typeof value !== 'undefined' ? value : !isSelected\n\n          if (isSelected === value) {\n            return old\n          }\n\n          const selectedRowIds = { ...old }\n\n          mutateRowIsSelected(selectedRowIds, row.id, value, table)\n\n          return selectedRowIds\n        })\n      },\n      getIsSelected: () => {\n        const { rowSelection } = table.getState()\n        return isRowSelected(row, rowSelection)\n      },\n\n      getIsSomeSelected: () => {\n        const { rowSelection } = table.getState()\n        return isSubRowSelected(row, rowSelection, table) === 'some'\n      },\n\n      getIsAllSubRowsSelected: () => {\n        const { rowSelection } = table.getState()\n        return isSubRowSelected(row, rowSelection, table) === 'all'\n      },\n\n      getCanSelect: () => {\n        if (typeof table.options.enableRowSelection === 'function') {\n          return table.options.enableRowSelection(row)\n        }\n\n        return table.options.enableRowSelection ?? true\n      },\n\n      getCanSelectSubRows: () => {\n        if (typeof table.options.enableSubRowSelection === 'function') {\n          return table.options.enableSubRowSelection(row)\n        }\n\n        return table.options.enableSubRowSelection ?? true\n      },\n\n      getCanMultiSelect: () => {\n        if (typeof table.options.enableMultiRowSelection === 'function') {\n          return table.options.enableMultiRowSelection(row)\n        }\n\n        return table.options.enableMultiRowSelection ?? true\n      },\n      getToggleSelectedHandler: () => {\n        const canSelect = row.getCanSelect()\n\n        return (e: unknown) => {\n          if (!canSelect) return\n          row.toggleSelected(\n            ((e as MouseEvent).target as HTMLInputElement)?.checked\n          )\n        }\n      },\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (row.subRows && row.subRows.length) {\n    let allChildrenSelected = true\n    let someSelected = false\n\n    row.subRows.forEach(subRow => {\n      // Bail out early if we know both of these\n      if (someSelected && !allChildrenSelected) {\n        return\n      }\n\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    })\n\n    return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n  }\n\n  return false\n}\n", "import { SortingFn } from './features/Sorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { functionalUpdate, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { Expanding } from '../features/Expanding'\nimport { Filters } from '../features/Filters'\nimport { Grouping } from '../features/Grouping'\nimport { Ordering } from '../features/Ordering'\nimport { Pagination } from '../features/Pagination'\nimport { Pinning } from '../features/Pinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { Sorting } from '../features/Sorting'\nimport { Visibility } from '../features/Visibility'\n\nexport interface TableFeature {\n  getDefaultOptions?: (table: any) => any\n  getInitialState?: (initialState?: InitialTableState) => any\n  createTable?: (table: any) => any\n  getDefaultColumnDef?: () => any\n  createColumn?: (column: any, table: any) => any\n  createHeader?: (column: any, table: any) => any\n  createCell?: (cell: any, column: any, row: any, table: any) => any\n  createRow?: (row: any, table: any) => any\n}\n\nconst features = [\n  Headers,\n  Visibility,\n  Ordering,\n  Pinning,\n  Filters,\n  Sorting,\n  Grouping,\n  Expanding,\n  Pagination,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  data: TData[]\n  state: Partial<TableState>\n  onStateChange: (updater: Updater<TableState>) => void\n  debugAll?: boolean\n  debugTable?: boolean\n  debugHeaders?: boolean\n  debugColumns?: boolean\n  debugRows?: boolean\n  initialState?: InitialTableState\n  autoResetAll?: boolean\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  meta?: TableMeta<TData>\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  columns: ColumnDef<TData, any>[]\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  renderFallbackValue: any\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  initialState: TableState\n  reset: () => void\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  getState: () => TableState\n  setState: (updater: Updater<TableState>) => void\n  _features: readonly TableFeature[]\n  _queue: (cb: () => void) => void\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  getCoreRowModel: () => RowModel<TData>\n  _getCoreRowModel?: () => RowModel<TData>\n  getRowModel: () => RowModel<TData>\n  getRow: (id: string) => Row<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  getAllColumns: () => Column<TData, unknown>[]\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (options.debugAll || options.debugTable) {\n    console.info('Creating Table Instance...')\n  }\n\n  let table = { _features: features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = feature.getInitialState?.(initialState) ?? initialState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features: features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    getRow: (id: string) => {\n      const row = table.getRowModel().rowsById[id]\n\n      if (!row) {\n        if (process.env.NODE_ENV !== 'production') {\n          throw new Error(`getRow expected an ID, but got ${id}`)\n        }\n        throw new Error()\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      {\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n        key: process.env.NODE_ENV === 'development' && 'getDefaultColumnDef',\n      }\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllFlatColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce((acc, column) => {\n          acc[column.id] = column\n          return acc\n        }, {} as Record<string, Column<TData, unknown>>)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllFlatColumnsById',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  table._features.forEach(feature => {\n    return Object.assign(table, feature.createTable?.(table))\n  })\n\n  return table\n}\n", "import { TableFeature } from '../core/table'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n} from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n  enableHiding?: boolean\n}\n\nexport interface VisibilityDefaultOptions {\n  onColumnVisibilityChange: OnChangeFn<VisibilityState>\n}\n\nexport interface VisibilityInstance<TData extends RowData> {\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  resetColumnVisibility: (defaultState?: boolean) => void\n  toggleAllColumnsVisible: (value?: boolean) => void\n  getIsAllColumnsVisible: () => boolean\n  getIsSomeColumnsVisible: () => boolean\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  getCanHide: () => boolean\n  getIsVisible: () => boolean\n  toggleVisibility: (value?: boolean) => void\n  getToggleVisibilityHandler: () => (event: unknown) => void\n}\n\n//\n\nexport const Visibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): VisibilityColumn => {\n    return {\n      toggleVisibility: value => {\n        if (column.getCanHide()) {\n          table.setColumnVisibility(old => ({\n            ...old,\n            [column.id]: value ?? !column.getIsVisible(),\n          }))\n        }\n      },\n      getIsVisible: () => {\n        return table.getState().columnVisibility?.[column.id] ?? true\n      },\n\n      getCanHide: () => {\n        return (\n          (column.columnDef.enableHiding ?? true) &&\n          (table.options.enableHiding ?? true)\n        )\n      },\n      getToggleVisibilityHandler: () => {\n        return (e: unknown) => {\n          column.toggleVisibility?.(\n            ((e as MouseEvent).target as HTMLInputElement).checked\n          )\n        }\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): VisibilityRow<TData> => {\n    return {\n      _getAllVisibleCells: memo(\n        () => [row.getAllCells(), table.getState().columnVisibility],\n        cells => {\n          return cells.filter(cell => cell.column.getIsVisible())\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' && 'row._getAllVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n      getVisibleCells: memo(\n        () => [\n          row.getLeftVisibleCells(),\n          row.getCenterVisibleCells(),\n          row.getRightVisibleCells(),\n        ],\n        (left, center, right) => [...left, ...center, ...right],\n        {\n          key: process.env.NODE_ENV === 'development' && 'row.getVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityInstance<TData> => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        {\n          key,\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      )\n    }\n\n    return {\n      getVisibleFlatColumns: makeVisibleColumnsMethod(\n        'getVisibleFlatColumns',\n        () => table.getAllFlatColumns()\n      ),\n      getVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getVisibleLeafColumns',\n        () => table.getAllLeafColumns()\n      ),\n      getLeftVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getLeftVisibleLeafColumns',\n        () => table.getLeftLeafColumns()\n      ),\n      getRightVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getRightVisibleLeafColumns',\n        () => table.getRightLeafColumns()\n      ),\n      getCenterVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getCenterVisibleLeafColumns',\n        () => table.getCenterLeafColumns()\n      ),\n\n      setColumnVisibility: updater =>\n        table.options.onColumnVisibilityChange?.(updater),\n\n      resetColumnVisibility: defaultState => {\n        table.setColumnVisibility(\n          defaultState ? {} : table.initialState.columnVisibility ?? {}\n        )\n      },\n\n      toggleAllColumnsVisible: value => {\n        value = value ?? !table.getIsAllColumnsVisible()\n\n        table.setColumnVisibility(\n          table.getAllLeafColumns().reduce(\n            (obj, column) => ({\n              ...obj,\n              [column.id]: !value ? !column.getCanHide?.() : value,\n            }),\n            {}\n          )\n        )\n      },\n\n      getIsAllColumnsVisible: () =>\n        !table.getAllLeafColumns().some(column => !column.getIsVisible?.()),\n\n      getIsSomeColumnsVisible: () =>\n        table.getAllLeafColumns().some(column => column.getIsVisible?.()),\n\n      getToggleAllColumnsVisibilityHandler: () => {\n        return (e: unknown) => {\n          table.toggleAllColumnsVisible(\n            ((e as MouseEvent).target as HTMLInputElement)?.checked\n          )\n        }\n      },\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport { TableFeature } from '../core/table'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  ColumnDefTemplate,\n  RowData,\n  AggregationFns,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  aggregationFn?: AggregationFnOption<TData>\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  enableGrouping?: boolean\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  getCanGroup: () => boolean\n  getIsGrouped: () => boolean\n  getGroupedIndex: () => number\n  toggleGrouping: () => void\n  getToggleGroupingHandler: () => () => void\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  getAggregationFn: () => AggregationFn<TData> | undefined\n}\n\nexport interface GroupingRow {\n  groupingColumnId?: string\n  groupingValue?: unknown\n  getIsGrouped: () => boolean\n  getGroupingValue: (columnId: string) => unknown\n  _groupingValuesCache: Record<string, any>\n}\n\nexport interface GroupingCell {\n  getIsGrouped: () => boolean\n  getIsPlaceholder: () => boolean\n  getIsAggregated: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  // Column\n  onGroupingChange: OnChangeFn<GroupingState>\n  enableGrouping: boolean\n}\n\ninterface GroupingOptionsBase {\n  manualGrouping?: boolean\n  onGroupingChange?: OnChangeFn<GroupingState>\n  enableGrouping?: boolean\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  groupedColumnMode?: false | 'reorder' | 'remove'\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  setGrouping: (updater: Updater<GroupingState>) => void\n  resetGrouping: (defaultState?: boolean) => void\n  getPreGroupedRowModel: () => RowModel<TData>\n  getGroupedRowModel: () => RowModel<TData>\n  _getGroupedRowModel?: () => RowModel<TData>\n}\n\n//\n\nexport const Grouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): GroupingColumn<TData> => {\n    return {\n      toggleGrouping: () => {\n        table.setGrouping(old => {\n          // Find any existing grouping for this column\n          if (old?.includes(column.id)) {\n            return old.filter(d => d !== column.id)\n          }\n\n          return [...(old ?? []), column.id]\n        })\n      },\n\n      getCanGroup: () => {\n        return (\n          column.columnDef.enableGrouping ??\n          true ??\n          table.options.enableGrouping ??\n          true ??\n          !!column.accessorFn\n        )\n      },\n\n      getIsGrouped: () => {\n        return table.getState().grouping?.includes(column.id)\n      },\n\n      getGroupedIndex: () => table.getState().grouping?.indexOf(column.id),\n\n      getToggleGroupingHandler: () => {\n        const canGroup = column.getCanGroup()\n\n        return () => {\n          if (!canGroup) return\n          column.toggleGrouping()\n        }\n      },\n      getAutoAggregationFn: () => {\n        const firstRow = table.getCoreRowModel().flatRows[0]\n\n        const value = firstRow?.getValue(column.id)\n\n        if (typeof value === 'number') {\n          return aggregationFns.sum\n        }\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return aggregationFns.extent\n        }\n      },\n      getAggregationFn: () => {\n        if (!column) {\n          throw new Error()\n        }\n\n        return isFunction(column.columnDef.aggregationFn)\n          ? column.columnDef.aggregationFn\n          : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n      },\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingInstance<TData> => {\n    return {\n      setGrouping: updater => table.options.onGroupingChange?.(updater),\n\n      resetGrouping: defaultState => {\n        table.setGrouping(\n          defaultState ? [] : table.initialState?.grouping ?? []\n        )\n      },\n\n      getPreGroupedRowModel: () => table.getFilteredRowModel(),\n      getGroupedRowModel: () => {\n        if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n          table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n        }\n\n        if (table.options.manualGrouping || !table._getGroupedRowModel) {\n          return table.getPreGroupedRowModel()\n        }\n\n        return table._getGroupedRowModel()\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): GroupingRow => {\n    return {\n      getIsGrouped: () => !!row.groupingColumnId,\n      getGroupingValue: columnId => {\n        if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n          return row._groupingValuesCache[columnId]\n        }\n\n        const column = table.getColumn(columnId)\n\n        if (!column?.columnDef.getGroupingValue) {\n          return row.getValue(columnId)\n        }\n\n        row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n          row.original\n        )\n\n        return row._groupingValuesCache[columnId]\n      },\n      _groupingValuesCache: {},\n    }\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): GroupingCell => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    return {\n      getIsGrouped: () =>\n        column.getIsGrouped() && column.id === row.groupingColumnId,\n      getIsPlaceholder: () => !cell.getIsGrouped() && column.getIsGrouped(),\n      getIsAggregated: () =>\n        !cell.getIsGrouped() &&\n        !cell.getIsPlaceholder() &&\n        !!row.subRows?.length,\n    }\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { TableFeature } from '../core/table'\nimport {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n} from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  getCanPin: () => boolean\n  getPinnedIndex: () => number\n  getIsPinned: () => ColumnPinningPosition\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n  resetColumnPinning: (defaultState?: boolean) => void\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n}\n\n//\n\nconst getDefaultPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const Pinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): ColumnPinningColumn => {\n    return {\n      pin: position => {\n        const columnIds = column\n          .getLeafColumns()\n          .map(d => d.id)\n          .filter(Boolean) as string[]\n\n        table.setColumnPinning(old => {\n          if (position === 'right') {\n            return {\n              left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              right: [\n                ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n                ...columnIds,\n              ],\n            }\n          }\n\n          if (position === 'left') {\n            return {\n              left: [\n                ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n                ...columnIds,\n              ],\n              right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n            }\n          }\n\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        })\n      },\n\n      getCanPin: () => {\n        const leafColumns = column.getLeafColumns()\n\n        return leafColumns.some(\n          d =>\n            (d.columnDef.enablePinning ?? true) &&\n            (table.options.enablePinning ?? true)\n        )\n      },\n\n      getIsPinned: () => {\n        const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n        const { left, right } = table.getState().columnPinning\n\n        const isLeft = leafColumnIds.some(d => left?.includes(d))\n        const isRight = leafColumnIds.some(d => right?.includes(d))\n\n        return isLeft ? 'left' : isRight ? 'right' : false\n      },\n\n      getPinnedIndex: () => {\n        const position = column.getIsPinned()\n\n        return position\n          ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n          : 0\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): ColumnPinningRow<TData> => {\n    return {\n      getCenterVisibleCells: memo(\n        () => [\n          row._getAllVisibleCells(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allCells, left, right) => {\n          const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n          return allCells.filter(d => !leftAndRight.includes(d.column.id))\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' &&\n            'row.getCenterVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n      getLeftVisibleCells: memo(\n        () => [\n          row._getAllVisibleCells(),\n          table.getState().columnPinning.left,\n          ,\n        ],\n        (allCells, left) => {\n          const cells = (left ?? [])\n            .map(\n              columnId => allCells.find(cell => cell.column.id === columnId)!\n            )\n            .filter(Boolean)\n            .map(d => ({ ...d, position: 'left' } as Cell<TData, unknown>))\n\n          return cells\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' && 'row.getLeftVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n      getRightVisibleCells: memo(\n        () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n        (allCells, right) => {\n          const cells = (right ?? [])\n            .map(\n              columnId => allCells.find(cell => cell.column.id === columnId)!\n            )\n            .filter(Boolean)\n            .map(d => ({ ...d, position: 'right' } as Cell<TData, unknown>))\n\n          return cells\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' && 'row.getRightVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningInstance<TData> => {\n    return {\n      setColumnPinning: updater =>\n        table.options.onColumnPinningChange?.(updater),\n\n      resetColumnPinning: defaultState =>\n        table.setColumnPinning(\n          defaultState\n            ? getDefaultPinningState()\n            : table.initialState?.columnPinning ?? getDefaultPinningState()\n        ),\n\n      getIsSomeColumnsPinned: position => {\n        const pinningState = table.getState().columnPinning\n\n        if (!position) {\n          return Boolean(\n            pinningState.left?.length || pinningState.right?.length\n          )\n        }\n        return Boolean(pinningState[position]?.length)\n      },\n\n      getLeftLeafColumns: memo(\n        () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n        (allColumns, left) => {\n          return (left ?? [])\n            .map(columnId => allColumns.find(column => column.id === columnId)!)\n            .filter(Boolean)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftLeafColumns',\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      ),\n\n      getRightLeafColumns: memo(\n        () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n        (allColumns, right) => {\n          return (right ?? [])\n            .map(columnId => allColumns.find(column => column.id === columnId)!)\n            .filter(Boolean)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightLeafColumns',\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      ),\n\n      getCenterLeafColumns: memo(\n        () => [\n          table.getAllLeafColumns(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, left, right) => {\n          const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n          return allColumns.filter(d => !leftAndRight.includes(d.id))\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getCenterLeafColumns',\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      ),\n    }\n  },\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  id: string\n  desc: boolean\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  sortingFn?: SortingFnOption<TData>\n  sortDescFirst?: boolean\n  enableSorting?: boolean\n  enableMultiSort?: boolean\n  invertSorting?: boolean\n  sortUndefined?: false | -1 | 1\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  getAutoSortingFn: () => SortingFn<TData>\n  getAutoSortDir: () => SortDirection\n  getSortingFn: () => SortingFn<TData>\n  getFirstSortDir: () => SortDirection\n  getNextSortingOrder: () => SortDirection | false\n  getCanSort: () => boolean\n  getCanMultiSort: () => boolean\n  getSortIndex: () => number\n  getIsSorted: () => false | SortDirection\n  clearSorting: () => void\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n}\n\ninterface SortingOptionsBase {\n  manualSorting?: boolean\n  onSortingChange?: OnChangeFn<SortingState>\n  enableSorting?: boolean\n  enableSortingRemoval?: boolean\n  enableMultiRemove?: boolean\n  enableMultiSort?: boolean\n  sortDescFirst?: boolean\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  maxMultiSortColCount?: number\n  isMultiSortEvent?: (e: unknown) => boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  setSorting: (updater: Updater<SortingState>) => void\n  resetSorting: (defaultState?: boolean) => void\n  getPreSortedRowModel: () => RowModel<TData>\n  getSortedRowModel: () => RowModel<TData>\n  _getSortedRowModel?: () => RowModel<TData>\n}\n\n//\n\nexport const Sorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): SortingColumn<TData> => {\n    return {\n      getAutoSortingFn: () => {\n        const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n        let isString = false\n\n        for (const row of firstRows) {\n          const value = row?.getValue(column.id)\n\n          if (Object.prototype.toString.call(value) === '[object Date]') {\n            return sortingFns.datetime\n          }\n\n          if (typeof value === 'string') {\n            isString = true\n\n            if (value.split(reSplitAlphaNumeric).length > 1) {\n              return sortingFns.alphanumeric\n            }\n          }\n        }\n\n        if (isString) {\n          return sortingFns.text\n        }\n\n        return sortingFns.basic\n      },\n      getAutoSortDir: () => {\n        const firstRow = table.getFilteredRowModel().flatRows[0]\n\n        const value = firstRow?.getValue(column.id)\n\n        if (typeof value === 'string') {\n          return 'asc'\n        }\n\n        return 'desc'\n      },\n      getSortingFn: () => {\n        if (!column) {\n          throw new Error()\n        }\n\n        return isFunction(column.columnDef.sortingFn)\n          ? column.columnDef.sortingFn\n          : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n      },\n      toggleSorting: (desc, multi) => {\n        // if (column.columns.length) {\n        //   column.columns.forEach((c, i) => {\n        //     if (c.id) {\n        //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n        //     }\n        //   })\n        //   return\n        // }\n\n        // this needs to be outside of table.setSorting to be in sync with rerender\n        const nextSortingOrder = column.getNextSortingOrder()\n        const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n        table.setSorting(old => {\n          // Find any existing sorting for this column\n          const existingSorting = old?.find(d => d.id === column.id)\n          const existingIndex = old?.findIndex(d => d.id === column.id)\n\n          let newSorting: SortingState = []\n\n          // What should we do with this sort action?\n          let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n          let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n          // Multi-mode\n          if (old?.length && column.getCanMultiSort() && multi) {\n            if (existingSorting) {\n              sortAction = 'toggle'\n            } else {\n              sortAction = 'add'\n            }\n          } else {\n            // Normal mode\n            if (old?.length && existingIndex !== old.length - 1) {\n              sortAction = 'replace'\n            } else if (existingSorting) {\n              sortAction = 'toggle'\n            } else {\n              sortAction = 'replace'\n            }\n          }\n\n          // Handle toggle states that will remove the sorting\n          if (sortAction === 'toggle') {\n            // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n            if (!hasManualValue) {\n              // Is our intention to remove?\n              if (!nextSortingOrder) {\n                sortAction = 'remove'\n              }\n            }\n          }\n\n          if (sortAction === 'add') {\n            newSorting = [\n              ...old,\n              {\n                id: column.id,\n                desc: nextDesc,\n              },\n            ]\n            // Take latest n columns\n            newSorting.splice(\n              0,\n              newSorting.length -\n                (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n            )\n          } else if (sortAction === 'toggle') {\n            // This flips (or sets) the\n            newSorting = old.map(d => {\n              if (d.id === column.id) {\n                return {\n                  ...d,\n                  desc: nextDesc,\n                }\n              }\n              return d\n            })\n          } else if (sortAction === 'remove') {\n            newSorting = old.filter(d => d.id !== column.id)\n          } else {\n            newSorting = [\n              {\n                id: column.id,\n                desc: nextDesc,\n              },\n            ]\n          }\n\n          return newSorting\n        })\n      },\n\n      getFirstSortDir: () => {\n        const sortDescFirst =\n          column.columnDef.sortDescFirst ??\n          table.options.sortDescFirst ??\n          column.getAutoSortDir() === 'desc'\n        return sortDescFirst ? 'desc' : 'asc'\n      },\n\n      getNextSortingOrder: (multi?: boolean) => {\n        const firstSortDirection = column.getFirstSortDir()\n        const isSorted = column.getIsSorted()\n\n        if (!isSorted) {\n          return firstSortDirection\n        }\n\n        if (\n          isSorted !== firstSortDirection &&\n          (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n          (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n        ) {\n          return false\n        }\n        return isSorted === 'desc' ? 'asc' : 'desc'\n      },\n\n      getCanSort: () => {\n        return (\n          (column.columnDef.enableSorting ?? true) &&\n          (table.options.enableSorting ?? true) &&\n          !!column.accessorFn\n        )\n      },\n\n      getCanMultiSort: () => {\n        return (\n          column.columnDef.enableMultiSort ??\n          table.options.enableMultiSort ??\n          !!column.accessorFn\n        )\n      },\n\n      getIsSorted: () => {\n        const columnSort = table\n          .getState()\n          .sorting?.find(d => d.id === column.id)\n\n        return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n      },\n\n      getSortIndex: () =>\n        table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1,\n\n      clearSorting: () => {\n        //clear sorting for just 1 column\n        table.setSorting(old =>\n          old?.length ? old.filter(d => d.id !== column.id) : []\n        )\n      },\n\n      getToggleSortingHandler: () => {\n        const canSort = column.getCanSort()\n\n        return (e: unknown) => {\n          if (!canSort) return\n          ;(e as any).persist?.()\n          column.toggleSorting?.(\n            undefined,\n            column.getCanMultiSort()\n              ? table.options.isMultiSortEvent?.(e)\n              : false\n          )\n        }\n      },\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingInstance<TData> => {\n    return {\n      setSorting: updater => table.options.onSortingChange?.(updater),\n      resetSorting: defaultState => {\n        table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n      },\n      getPreSortedRowModel: () => table.getGroupedRowModel(),\n      getSortedRowModel: () => {\n        if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n          table._getSortedRowModel = table.options.getSortedRowModel(table)\n        }\n\n        if (table.options.manualSorting || !table._getSortedRowModel) {\n          return table.getPreSortedRowModel()\n        }\n\n        return table._getSortedRowModel()\n      },\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, RowModel, Updater, RowData } from '../types'\nimport { functionalUpdate, makeStateUpdater, memo } from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  pageCount?: number\n  manualPagination?: boolean\n  onPaginationChange?: OnChangeFn<PaginationState>\n  autoResetPageIndex?: boolean\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  setPagination: (updater: Updater<PaginationState>) => void\n  resetPagination: (defaultState?: boolean) => void\n  setPageIndex: (updater: Updater<number>) => void\n  resetPageIndex: (defaultState?: boolean) => void\n  setPageSize: (updater: Updater<number>) => void\n  resetPageSize: (defaultState?: boolean) => void\n  setPageCount: (updater: Updater<number>) => void\n  getPageOptions: () => number[]\n  getCanPreviousPage: () => boolean\n  getCanNextPage: () => boolean\n  previousPage: () => void\n  nextPage: () => void\n  getPrePaginationRowModel: () => RowModel<TData>\n  getPaginationRowModel: () => RowModel<TData>\n  _getPaginationRowModel?: () => RowModel<TData>\n  getPageCount: () => number\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const Pagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationInstance<TData> => {\n    let registered = false\n    let queued = false\n\n    return {\n      _autoResetPageIndex: () => {\n        if (!registered) {\n          table._queue(() => {\n            registered = true\n          })\n          return\n        }\n\n        if (\n          table.options.autoResetAll ??\n          table.options.autoResetPageIndex ??\n          !table.options.manualPagination\n        ) {\n          if (queued) return\n          queued = true\n          table._queue(() => {\n            table.resetPageIndex()\n            queued = false\n          })\n        }\n      },\n      setPagination: updater => {\n        const safeUpdater: Updater<PaginationState> = old => {\n          let newState = functionalUpdate(updater, old)\n\n          return newState\n        }\n\n        return table.options.onPaginationChange?.(safeUpdater)\n      },\n      resetPagination: defaultState => {\n        table.setPagination(\n          defaultState\n            ? getDefaultPaginationState()\n            : table.initialState.pagination ?? getDefaultPaginationState()\n        )\n      },\n      setPageIndex: updater => {\n        table.setPagination(old => {\n          let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n          const maxPageIndex =\n            typeof table.options.pageCount === 'undefined' ||\n            table.options.pageCount === -1\n              ? Number.MAX_SAFE_INTEGER\n              : table.options.pageCount - 1\n\n          pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n          return {\n            ...old,\n            pageIndex,\n          }\n        })\n      },\n      resetPageIndex: defaultState => {\n        table.setPageIndex(\n          defaultState\n            ? defaultPageIndex\n            : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n        )\n      },\n      resetPageSize: defaultState => {\n        table.setPageSize(\n          defaultState\n            ? defaultPageSize\n            : table.initialState?.pagination?.pageSize ?? defaultPageSize\n        )\n      },\n      setPageSize: updater => {\n        table.setPagination(old => {\n          const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n          const topRowIndex = old.pageSize * old.pageIndex!\n          const pageIndex = Math.floor(topRowIndex / pageSize)\n\n          return {\n            ...old,\n            pageIndex,\n            pageSize,\n          }\n        })\n      },\n      setPageCount: updater =>\n        table.setPagination(old => {\n          let newPageCount = functionalUpdate(\n            updater,\n            table.options.pageCount ?? -1\n          )\n\n          if (typeof newPageCount === 'number') {\n            newPageCount = Math.max(-1, newPageCount)\n          }\n\n          return {\n            ...old,\n            pageCount: newPageCount,\n          }\n        }),\n\n      getPageOptions: memo(\n        () => [table.getPageCount()],\n        pageCount => {\n          let pageOptions: number[] = []\n          if (pageCount && pageCount > 0) {\n            pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n          }\n          return pageOptions\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getPageOptions',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      getCanPreviousPage: () => table.getState().pagination.pageIndex > 0,\n\n      getCanNextPage: () => {\n        const { pageIndex } = table.getState().pagination\n\n        const pageCount = table.getPageCount()\n\n        if (pageCount === -1) {\n          return true\n        }\n\n        if (pageCount === 0) {\n          return false\n        }\n\n        return pageIndex < pageCount - 1\n      },\n\n      previousPage: () => {\n        return table.setPageIndex(old => old - 1)\n      },\n\n      nextPage: () => {\n        return table.setPageIndex(old => {\n          return old + 1\n        })\n      },\n\n      getPrePaginationRowModel: () => table.getExpandedRowModel(),\n      getPaginationRowModel: () => {\n        if (\n          !table._getPaginationRowModel &&\n          table.options.getPaginationRowModel\n        ) {\n          table._getPaginationRowModel =\n            table.options.getPaginationRowModel(table)\n        }\n\n        if (table.options.manualPagination || !table._getPaginationRowModel) {\n          return table.getPrePaginationRowModel()\n        }\n\n        return table._getPaginationRowModel()\n      },\n\n      getPageCount: () => {\n        return (\n          table.options.pageCount ??\n          Math.ceil(\n            table.getPrePaginationRowModel().rows.length /\n              table.getState().pagination.pageSize\n          )\n        )\n      },\n    }\n  },\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  id: string\n  depth: number\n  accessorFn?: AccessorFn<TData, TValue>\n  columnDef: ColumnDef<TData, TValue>\n  columns: Column<TData, TValue>[]\n  parent?: Column<TData, TValue>\n  getFlatColumns: () => Column<TData, TValue>[]\n  getLeafColumns: () => Column<TData, TValue>[]\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey ? accessorKey.replace('.', '_') : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'column.getFlatColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'column.getLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n  }\n\n  column = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.createColumn?.(column, table))\n  }, column)\n\n  // Yes, we have to convert table to uknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  id: string\n  index: number\n  original: TData\n  depth: number\n  parentId?: string\n  _valuesCache: Record<string, unknown>\n  _uniqueValuesCache: Record<string, unknown>\n  getValue: <TValue>(columnId: string) => TValue\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  renderValue: <TValue>(columnId: string) => TValue\n  subRows: Row<TData>[]\n  getLeafRows: () => Row<TData>[]\n  originalSubRows?: TData[]\n  getAllCells: () => Cell<TData, unknown>[]\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  getParentRow: () => Row<TData> | undefined\n  getParentRows: () => Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () => (row.parentId ? table.getRow(row.parentId) : undefined),\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'row.getAllCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce((acc, cell) => {\n          acc[cell.column.id] = cell\n          return acc\n        }, {} as Record<string, Cell<TData, unknown>>)\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'production' && 'row.getAllCellsByColumnId',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    Object.assign(row, feature?.createRow?.(row, table))\n  }\n\n  return row as Row<TData>\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  table: Table<TData>\n  column: Column<TData, TValue>\n  row: Row<TData>\n  cell: Cell<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  id: string\n  getValue: CellContext<TData, TValue>['getValue']\n  renderValue: CellContext<TData, TValue>['renderValue']\n  row: Row<TData>\n  column: Column<TData, TValue>\n  getContext: () => CellContext<TData, TValue>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      {\n        key: process.env.NODE_ENV === 'development' && 'cell.getContext',\n        debug: () => table.options.debugAll,\n      }\n    ),\n  }\n\n  table._features.forEach(feature => {\n    Object.assign(\n      cell,\n      feature.createCell?.(\n        cell as Cell<TData, TValue>,\n        column,\n        row as Row<TData>,\n        table\n      )\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/Sorting'\nimport { memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(sort =>\n          table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = [...rows]\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const isDesc = sortEntry?.desc ?? false\n\n              if (columnInfo.sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = typeof aValue === 'undefined'\n                const bUndefined = typeof bValue === 'undefined'\n\n                if (aUndefined || bUndefined) {\n                  let undefinedSort =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                      ? columnInfo.sortUndefined\n                      : -columnInfo.sortUndefined\n\n                  if (isDesc && undefinedSort !== 0) {\n                    undefinedSort *= -1\n                  }\n\n                  return undefinedSort\n                }\n              }\n\n              // This function should always return in ascending order\n              let sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getSortedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getExpandedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getPaginationRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n}\n", "import * as React from 'react'\nexport * from '@tanstack/table-core'\n\nimport {\n  TableOptions,\n  TableOptionsResolved,\n  RowData,\n  createTable,\n} from '@tanstack/table-core'\n\nexport type Renderable<TProps> = React.ReactNode | React.ComponentType<TProps>\n\n//\n\nexport function flexRender<TProps extends object>(\n  Comp: Renderable<TProps>,\n  props: TProps\n): React.ReactNode | JSX.Element {\n  return !Comp ? null : isReactComponent<TProps>(Comp) ? (\n    <Comp {...props} />\n  ) : (\n    Comp\n  )\n}\n\nfunction isReactComponent<TProps>(\n  component: unknown\n): component is React.ComponentType<TProps> {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component: any) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component: any) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n\nexport function useReactTable<TData extends RowData>(\n  options: TableOptions<TData>\n) {\n  // Compose in the generic options to the user options\n  const resolvedOptions: TableOptionsResolved<TData> = {\n    state: {}, // Dummy state\n    onStateChange: () => {}, // noop\n    renderFallbackValue: null,\n    ...options,\n  }\n\n  // Create a new table and store it in state\n  const [tableRef] = React.useState(() => ({\n    current: createTable<TData>(resolvedOptions),\n  }))\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = React.useState(() => tableRef.current.initialState)\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state,\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater)\n      options.onStateChange?.(updater)\n    },\n  }))\n\n  return tableRef.current\n}\n"], "names": ["functionalUpdate", "updater", "input", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "result", "deps", "depTime", "debug", "Date", "now", "newDeps", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "createHeader", "table", "column", "options", "_options$id", "header", "id", "isPlaceholder", "placeholderId", "depth", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "getContext", "_features", "feature", "Object", "assign", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "leafColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "columnId", "find", "filter", "Boolean", "rightColumns", "buildHeaderGroups", "includes", "process", "_table$options$debugA", "debugAll", "debugHeaders", "getCenterHeaderGroups", "_table$options$debugA2", "getLeftHeaderGroups", "_left$map$filter2", "_table$options$debugA3", "getRightHeaderGroups", "_right$map$filter2", "_table$options$debugA4", "getFooterGroups", "headerGroups", "reverse", "_table$options$debugA5", "getLeftFooterGroups", "_table$options$debugA6", "getCenterFooterGroups", "_table$options$debugA7", "getRightFooterGroups", "_table$options$debugA8", "getFlatHeaders", "headers", "_table$options$debugA9", "getLeftFlatHeaders", "_table$options$debugA10", "getCenterFlatHeaders", "_table$options$debugA11", "getRightFlatHeaders", "_table$options$debugA12", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "_table$options$debugA13", "getLeftLeafHeaders", "_header$subHeaders2", "_table$options$debugA14", "getRightLeafHeaders", "_header$subHeaders3", "_table$options$debugA15", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "_table$options$debugA16", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "columns", "getIsVisible", "_column$columns", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "parent", "undefined", "bottomHeaders", "recurseHeadersForSpans", "childRowSpans", "_ref", "childColSpan", "childRowSpan", "defaultColumnSizing", "size", "minSize", "maxSize", "Number", "MAX_SAFE_INTEGER", "ColumnSizing", "getDefaultColumnDef", "getInitialState", "state", "columnSizing", "columnSizingInfo", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "getDefaultOptions", "columnResizeMode", "onColumnSizingChange", "onColumnSizingInfoChange", "createColumn", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "columnDef", "getStart", "position", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "findIndex", "prevSiblingColumn", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "_column$columnDef$ena", "_table$options$enable", "enableResizing", "enableColumnResizing", "getIsResizing", "sum", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "getColumn", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "_ref3", "headerSize", "onMove", "onEnd", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "document", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveSupported", "supported", "passive", "noop", "window", "addEventListener", "err", "passiveEventSupported", "resetColumnSizing", "defaultState", "_table$initialState$c", "initialState", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "reduce", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "type", "Expanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "autoResetAll", "autoResetExpanded", "manualExpanding", "_queue", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "_table$initialState", "getCanSomeRowsExpand", "getPrePaginationRowModel", "flatRows", "row", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "values", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowsById", "splitId", "split", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "createRow", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "_row$subRows", "getRowCanExpand", "enableExpanding", "subRows", "getToggleExpandedHandler", "canExpand", "includesString", "filterValue", "_row$getValue", "_row$getValue$toStrin", "_row$getValue$toStrin2", "search", "toLowerCase", "getValue", "toString", "autoRemove", "val", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "_row$getValue2$toStri", "equalsString", "_row$getValue3", "_row$getValue3$toStri", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "isNaN", "Infinity", "temp", "filterFns", "shouldAutoRemoveFilter", "filterFn", "value", "aggregationFns", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "Array", "isArray", "every", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "_columnId", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "_table$initialState$r", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getPreGroupedRowModel", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "rows", "mutateRowIsSelected", "getPreSelectedRowModel", "getCoreRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "debugTable", "getFilteredSelectedRowModel", "getFilteredRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "getPaginationRowModel", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "target", "checked", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "selectedRowIds", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "_table$options$enable2", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "_target", "getRow", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "allChildrenSelected", "someSelected", "subRow", "reSplitAlphaNumeric", "compareBasic", "compareAlphanumeric", "aStr", "bStr", "aa", "shift", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "alphanumeric", "rowA", "rowB", "alphanumericCaseSensitive", "text", "textCaseSensitive", "datetime", "basic", "features", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "_table$getState$colum", "_table$getState$colum2", "enableHiding", "getToggleVisibilityHandler", "_getAllVisibleCells", "getAllCells", "cells", "cell", "debugRows", "getVisibleCells", "getLeftVisibleCells", "getCenterVisibleCells", "getRightVisibleCells", "makeVisibleColumnsMethod", "getColumns", "debugColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getAllLeafColumns", "getLeftLeafColumns", "getRightLeafColumns", "getCenterVisibleLeafColumns", "getCenterLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "columnOrder", "onColumnOrderChange", "setColumnOrder", "resetColumnOrder", "_getOrderColumnsFn", "grouping", "groupedColumnMode", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "foundIndex", "splice", "nonGroupingColumns", "col", "g", "orderColumns", "onColumnPinningChange", "pin", "columnIds", "getLeafColumns", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "_table$getState$colum3", "indexOf", "allCells", "leftAndRight", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "columnFilters", "globalFilter", "onColumnFiltersChange", "onGlobalFilterChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "_table$getCoreRowMode2", "_getAllCellsByColumnId", "getAutoFilterFn", "firstRow", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "enableColumnFilter", "enableColumnFilters", "enableFilters", "accessorFn", "getCanGlobalFilter", "_column$columnDef$ena2", "_table$options$enable4", "_table$options$getCol", "enableGlobalFilter", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum4", "setFilterValue", "setColumnFilters", "previousfilter", "newFilter", "_old$filter", "newFilterObj", "_old$map", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "columnFiltersMeta", "getGlobalAutoFilterFn", "getGlobalFilterFn", "_table$options$filter3", "_table$options$filter4", "_functionalUpdate", "setGlobalFilter", "resetGlobalFilter", "resetColumnFilters", "_getFilteredRowModel", "manualFiltering", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "sorting", "sortingFn", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "slice", "isString", "prototype", "call", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "Error", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "sortAction", "newSorting", "nextDesc", "_table$options$maxMul", "getCanMultiSort", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "getGroupedRowModel", "_getSortedRowModel", "manualSorting", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "onGroupingChange", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "getGroupingValue", "_groupingValuesCache", "hasOwnProperty", "original", "createCell", "getIsPlaceholder", "getIsAggregated", "pagination", "pageIndex", "pageSize", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "_table$initialState$p3", "resetPageSize", "_table$initialState$p4", "_table$initialState2", "_table$initialState2$", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "i", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "_options$initialState", "defaultOptions", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "mergeOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "_getDefaultColumnDef", "defaultColumn", "_defaultColumn", "resolvedColumnDef", "accessorKey", "_props$renderValue$to", "_props$renderValue", "renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "_resolvedColumnDef$id", "replace", "originalRow", "_result", "getFlatColumns", "flatMap", "_column$columns2", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "acc", "rowIndex", "parentId", "_valuesCache", "_uniqueValuesCache", "getUniqueValues", "renderFallbackValue", "getLeafRows", "getParentRow", "getParentRows", "parentRows", "currentRow", "parentRow", "getRenderValue", "_cell$getValue", "data", "accessRows", "originalRows", "_row$originalSubRows", "getSubRows", "originalSubRows", "sortingState", "sortedFlatRows", "availableSorting", "_table$getColumn", "columnInfoById", "sortEntry", "sortUndefined", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "aUndefined", "bUndefined", "undefinedSort", "sortInt", "expandRows", "expandedRows", "handleRow", "pageStart", "pageEnd", "paginatedRowModel", "flexRender", "Comp", "component", "proto", "getPrototypeOf", "isReactComponent", "isClassComponent", "$$typeof", "description", "isExoticComponent", "React", "useReactTable", "resolvedOptions", "tableRef", "current", "prev"], "sourceRoot": ""}