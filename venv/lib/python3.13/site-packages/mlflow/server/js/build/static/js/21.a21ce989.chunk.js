"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[21],{71265:function(e,t,s){s.r(t),s.d(t,{CompareModelVersionsPage:function(){return J},CompareModelVersionsPageImpl:function(){return W},default:function(){return $}});var n=s(31014),a=s(51079),r=s.n(a),o=s(10811),i=s(26809),l=s(7204),d=s(69708),c=s(53140),u=s(89555),h=s(93215),p=s(9133),m=s.n(p),g=s(88443),f=s(64912),b=s(48012),N=s(32599),Y=s(72877),v=(s(71832),s(51882)),I=s(7871),M=s(87234),A=s(58481),w=s(85017),y=s(24406),R=s(76010),k=s(55406),x=s(69869),T=s(65871),q=s(79085),S=s(50111);const{TabPane:C}=b.Y6f;function D(e){const{theme:t}=(0,N.u)();return(0,S.Y)("div",{css:(0,u.AH)({textAlign:"center",color:t.colors.textSecondary},""),...e})}function L(e){const{theme:t}=(0,N.u)();return(0,S.Y)("table",{className:"compare-table table",css:(0,u.AH)({"th.main-table-header":{backgroundColor:t.colors.white,padding:0},"td.highlight-data":{backgroundColor:t.colors.backgroundValidationWarning}},""),...e})}function V(e){const{theme:t}=(0,N.u)();return(0,S.Y)("button",{css:(0,u.AH)({textAlign:"left",display:"flex",alignItems:"center",border:"none",backgroundColor:t.colors.white,paddingLeft:0,cursor:"pointer"},""),...e})}class F extends n.Component{constructor(){super(...arguments),this.state={inputActive:!0,outputActive:!0,paramsToggle:!0,paramsActive:!0,schemaToggle:!0,compareByColumnNameToggle:!1,schemaActive:!0,metricToggle:!0,metricActive:!0},this.icons={plusIcon:(0,S.Y)("i",{className:"far fa-plus-square-o"}),minusIcon:(0,S.Y)("i",{className:"far fa-minus-square-o"}),downIcon:(0,S.Y)("i",{className:"fas fa-caret-down"}),rightIcon:(0,S.Y)("i",{className:"fas fa-caret-right"}),chartIcon:(0,S.Y)("i",{className:"fas fa-line-chart padding-left-text"})},this.onToggleClick=e=>{this.setState((t=>({[e]:!t[e]})))}}render(){const{inputsListByIndex:e,inputsListByName:t,modelName:s,outputsListByIndex:n,outputsListByName:a,runInfos:r,runUuids:o,runDisplayNames:i,paramLists:l,metricLists:d}=this.props,c=(0,S.Y)(g.A,{id:"6kSKRk",defaultMessage:"Comparing {numVersions} Versions",values:{numVersions:this.props.runInfos.length}}),p=[(0,S.Y)(h.N_,{to:x.fM.modelListPageRoute,children:(0,S.Y)(g.A,{id:"U82iv6",defaultMessage:"Registered Models"})}),(0,S.Y)(h.N_,{to:x.fM.getModelPageRoute(s),children:s})];return(0,S.FD)("div",{className:"CompareModelVersionsView",css:(0,u.AH)({...U.compareModelVersionsView,...U.wrapper(r.length)},""),children:[(0,S.Y)(q.z,{title:c,breadcrumbs:p}),(0,S.Y)("div",{className:"responsive-table-container",children:(0,S.FD)(L,{children:[this.renderTableHeader(),this.renderModelVersionInfo(),this.renderSectionHeader("paramsActive","paramsToggle",(0,S.Y)(g.A,{id:"bSx/er",defaultMessage:"Parameters"})),this.renderParams(),this.renderSectionHeader("schemaActive","schemaToggle",(0,S.Y)(g.A,{id:"cK6riy",defaultMessage:"Schema"}),!1,(0,S.Y)(b.dOG,{className:"toggle-switch",style:{marginLeft:"auto"},onChange:()=>this.onToggleClick("compareByColumnNameToggle")}),(0,S.Y)("div",{className:"padding-left-text padding-right-text",children:(0,S.Y)("span",{children:(0,S.Y)(g.A,{id:"sXqvoN",defaultMessage:"Ignore column ordering"})})})),this.renderSchemaSectionHeader("inputActive",(0,S.Y)(g.A,{id:"GJWVBA",defaultMessage:"Inputs"})),this.renderSchema("inputActive",(0,S.Y)(g.A,{id:"ThrXMh",defaultMessage:"Inputs"}),e,t),this.renderSchemaSectionHeader("outputActive",(0,S.Y)(g.A,{id:"aB6xFd",defaultMessage:"Outputs"})),this.renderSchema("outputActive",(0,S.Y)(g.A,{id:"bqn2bk",defaultMessage:"Outputs"}),n,a),this.renderSectionHeader("metricActive","metricToggle",(0,S.Y)(g.A,{id:"CpLnGS",defaultMessage:"Metrics"})),this.renderMetrics()]})}),(0,S.FD)(b.Y6f,{children:[(0,S.Y)(C,{tab:(0,S.Y)(g.A,{id:"QuU1sl",defaultMessage:"Parallel Coordinates Plot"}),children:(0,S.Y)(k.Ay,{runUuids:o})},"parallel-coordinates-plot"),(0,S.Y)(C,{tab:(0,S.Y)(g.A,{id:"4NEPlV",defaultMessage:"Scatter Plot"}),children:(0,S.Y)(v.J,{runUuids:o,runDisplayNames:i})},"scatter-plot"),(0,S.Y)(C,{tab:(0,S.Y)(g.A,{id:"iXb99e",defaultMessage:"Box Plot"}),children:(0,S.Y)(I.e,{runUuids:o,runInfos:r,paramLists:l,metricLists:d})},"box-plot"),(0,S.Y)(C,{tab:(0,S.Y)(g.A,{id:"dq8MJd",defaultMessage:"Contour Plot"}),children:(0,S.Y)(M.A,{runUuids:o,runDisplayNames:i})},"contour-plot")]})]})}renderTableHeader(){const{runInfos:e,runInfosValid:t}=this.props;return(0,S.Y)("thead",{children:(0,S.FD)("tr",{className:"table-row",children:[(0,S.Y)("th",{scope:"row",className:"row-header block-content",children:(0,S.Y)(g.A,{id:"bi26D5",defaultMessage:"Run ID:"})}),e.map(((e,s)=>{var n,a;return(0,S.Y)("th",{scope:"column",className:"data-value block-content",children:t[s]?(0,S.Y)(h.N_,{to:A.h.getRunPageRoute(null!==(n=e.experimentId)&&void 0!==n?n:"0",null!==(a=e.runUuid)&&void 0!==a?a:""),children:e.runUuid}):e.runUuid},e.runUuid)}))]})})}renderModelVersionInfo(){const{runInfos:e,runInfosValid:t,versionsToRuns:s,runNames:n,modelName:a}=this.props;return(0,S.FD)("tbody",{className:"scrollable-table",children:[(0,S.FD)("tr",{className:"table-row",children:[(0,S.Y)("th",{scope:"row",className:"data-value block-content",children:(0,S.Y)(g.A,{id:"xmPKKq",defaultMessage:"Model Version:"})}),Object.keys(s).map((e=>{const t=s[e];return(0,S.Y)("td",{className:"meta-info block-content",children:(0,S.Y)(h.N_,{to:x.fM.getModelVersionPageRoute(a,e),children:e})},t)}))]}),(0,S.FD)("tr",{className:"table-row",children:[(0,S.Y)("th",{scope:"row",className:"data-value block-content",children:(0,S.Y)(g.A,{id:"erqoIC",defaultMessage:"Run Name:"})}),n.map(((t,s)=>(0,S.Y)("td",{className:"meta-info block-content",children:(0,S.Y)("div",{className:"truncate-text single-line cell-content",children:t})},e[s].runUuid)))]}),(0,S.FD)("tr",{className:"table-row",children:[(0,S.Y)("th",{scope:"row",className:"data-value block-content",children:(0,S.Y)(g.A,{id:"GKiPV1",defaultMessage:"Start Time:"})}),e.map(((e,s)=>{const n=e.startTime&&t[s]?R.A.formatTimestamp(e.startTime,this.props.intl):"(unknown)";return(0,S.Y)("td",{className:"meta-info block-content",children:n},e.runUuid)}))]})]})}renderSectionHeader(e,t,s){let n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null;const{runInfos:o}=this.props,i=this.state[e],{downIcon:l,rightIcon:d}=this.icons;return(0,S.Y)("tbody",{children:(0,S.Y)("tr",{children:(0,S.Y)("th",{scope:"rowgroup",className:"block-content main-table-header",colSpan:o.length+1,children:(0,S.FD)("div",{className:"switch-button-container",children:[(0,S.FD)(V,{onClick:()=>this.onToggleClick(e),children:[i?l:d,(0,S.Y)("span",{className:"header",children:s})]}),a,r,(0,S.Y)(b.dOG,{defaultChecked:!0,className:"toggle-switch",style:n?{marginLeft:"auto"}:{},onChange:()=>this.onToggleClick(t)}),(0,S.Y)("div",{className:"padding-left-text",children:(0,S.Y)("span",{children:(0,S.Y)(g.A,{id:"H+4gFh",defaultMessage:"Show diff only"})})})]})})})})}renderParams(){return(0,S.Y)("tbody",{className:"scrollable-table",children:this.renderDataRows(this.props.paramLists,(0,S.Y)(g.A,{id:"EK5JxG",defaultMessage:"Parameters"}),this.state.paramsActive,this.state.paramsToggle)})}renderSchemaSectionHeader(e,t){const{runInfos:s}=this.props,{schemaActive:n}=this.state,a=this.state[e],{minusIcon:r,plusIcon:o}=this.icons;return(0,S.Y)("tbody",{children:(0,S.Y)("tr",{className:""+(n?"":"hidden-row"),children:(0,S.Y)("th",{scope:"rowgroup",className:"schema-table-header block-content",colSpan:s.length+1,children:(0,S.FD)("button",{className:"schema-collapse-button",onClick:()=>this.onToggleClick(e),children:[a?r:o,(0,S.Y)("strong",{style:{paddingLeft:4},children:t})]})})})})}renderSchema(e,t,s,n){const{schemaActive:a,compareByColumnNameToggle:r,schemaToggle:o}=this.state,i=this.state[e],l=a&&i,d=!r&&!m().isEmpty(s),c=r&&!m().isEmpty(n),u=e=>e,h=(0,S.Y)(g.A,{id:"5RWIet",defaultMessage:"Schema {sectionName}",values:{sectionName:t}});return(0,S.FD)("tbody",{className:"scrollable-table schema-scrollable-table",children:[this.renderDataRows(s,h,l&&d,o,((e,s)=>(0,S.FD)(S.FK,{children:[t," [",e,"]"]})),u),this.renderDataRows(n,h,l&&c,o,((e,t)=>e),u)]})}renderMetrics(){const{runInfos:e,metricLists:t}=this.props,{metricActive:s,metricToggle:n}=this.state,{chartIcon:a}=this.icons;return(0,S.Y)("tbody",{className:"scrollable-table",children:this.renderDataRows(t,(0,S.Y)(g.A,{id:"GDenB3",defaultMessage:"Metrics"}),s,n,((t,s)=>(0,S.FD)(h.N_,{to:A.h.getMetricPageRoute(e.map((e=>e.runUuid)).filter(((e,t)=>void 0!==s[t])),t,[e[0].experimentId]),target:"_blank",title:"Plot chart",children:[t,a]})),R.A.formatMetric)})}renderDataRows(e,t){let s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:(e,t)=>e,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e=>isNaN(e)?`"${e}"`:e;const o=y.A.getKeys(e),i={};if(o.forEach((e=>i[e]=[])),e.forEach(((e,t)=>{o.forEach((e=>i[e].push(void 0))),e.forEach((e=>i[e.key][t]=e.value))})),m().isEmpty(o)||m().isEmpty(e))return(0,S.Y)("tr",{className:"table-row "+(s?"":"hidden-row"),children:(0,S.Y)("th",{scope:"row",className:"rowHeader block-content",children:(0,S.Y)(D,{children:(0,S.Y)(g.A,{id:"47QsAK",defaultMessage:"{fieldName} are empty",values:{fieldName:t}})})})});m().every(o,(e=>!isNaN(e)))?o.sort(((e,t)=>parseInt(e,10)-parseInt(t,10))):o.sort();let l=!0;const d=o.map((e=>{const t=i[e].length>1&&m().uniq(i[e]).length>1;return l=!t&&l,(0,S.FD)("tr",{className:"table-row "+(n&&!t||!s?"hidden-row":""),children:[(0,S.Y)("th",{scope:"row",className:"rowHeader block-content",children:a(e,i[e])}),i[e].map(((e,s)=>(0,S.Y)("td",{className:"data-value block-content "+(t?"highlight-data":""),children:(0,S.Y)("span",{className:"truncate-text single-line cell-content",children:void 0===e?"-":r(e)})},this.props.runInfos[s].runUuid)))]},e)}));return l&&n?(0,S.Y)("tr",{className:"table-row "+(s?"":"hidden-row"),children:(0,S.Y)("th",{scope:"row",className:"rowHeader block-content",children:(0,S.Y)(D,{children:(0,S.Y)(g.A,{id:"NJ1bYP",defaultMessage:"{fieldName} are identical",values:{fieldName:t}})})})}):d}}const P=e=>{const t={};return e.forEach(((e,s)=>{const n=e.name?e.name:"",a=e.type?e.type:"";t[s]={key:s,value:""!==n&&""!==a?`${n}: ${a}`:`${n}${a}`}})),t},H=e=>{const t={};return e.forEach((e=>{const s=e.name?e.name:"-",n=e.type?e.type:"-";t[s]={key:s,value:n}})),t},U={wrapper:e=>({".compare-table":{minWidth:200*(e+1)}}),compareModelVersionsView:{"button:focus":{outline:"none",boxShadow:"none"},"td.block-content th.block-content":{whiteSpace:"nowrap",textOverflow:"ellipsis",tableLayout:"fixed",boxSizing:"content-box"},"th.schema-table-header":{height:28,padding:0},"tr.table-row":{display:"table",width:"100%",tableLayout:"fixed"},"tr.hidden-row":{display:"none"},"tbody.scrollable-table":{width:"100%",display:"block",border:"none",maxHeight:400,overflowY:"auto"},"tbody.schema-scrollable-table":{maxHeight:200},".switch-button-container":{display:"flex",paddingTop:16,paddingBottom:16},"button.schema-collapse-button":{textAlign:"left",display:"block",width:"100%",height:"100%",border:"none"},".collapse-button":{textAlign:"left",display:"flex",alignItems:"center",border:"none",backgroundColor:"white",paddingLeft:0},".cell-content":{maxWidth:"200px",minWidth:"100px"},".padding-left-text":{paddingLeft:8},".padding-right-text":{paddingRight:16},".toggle-switch":{marginTop:2},".header":{paddingLeft:8,fontSize:16}}},E=(0,o.Ng)(((e,t)=>{const s=[],n=[],a=[],r=[],o=[],i=[],l=[],{modelName:d,versionsToRuns:c}=t,u=[],h=[],p=[],m=[];for(const g in c)if(c&&g in c){const t=c[g],f=(0,Y.K4)(t,e);f?(s.push(f),n.push(!0),a.push(Object.values((0,w.d0)(t,e))),r.push(Object.values((0,Y.tI)(t,e))),o.push(R.A.getRunName(f)),i.push(R.A.getRunDisplayName(f,t)),l.push(t)):(t?s.push({runUuid:t}):s.push({runUuid:"None"}),n.push(!1),a.push([]),r.push([]),o.push("Invalid Run"));const b=(0,T.SE)(e,d,g);h.push(Object.values(P(b.inputs))),u.push(Object.values(H(b.inputs))),m.push(Object.values(P(b.outputs))),p.push(Object.values(H(b.outputs)))}return{runInfos:s,runInfosValid:n,metricLists:a,paramLists:r,runNames:o,runDisplayNames:i,runUuids:l,modelName:d,inputsListByName:u,inputsListByIndex:h,outputsListByName:p,outputsListByIndex:m}}))((0,f.Ay)(F));var O=s(48588),B=s(25869),_=s(20109),K=s(62448);class W extends n.Component{constructor(){super(...arguments),this.registeredModelRequestId=(0,l.yk)(),this.versionRequestId=(0,l.yk)(),this.runRequestId=(0,l.yk)(),this.getMlModelFileRequestId=(0,l.yk)(),this.state={requestIds:[this.registeredModelRequestId,this.runRequestId,this.versionRequestId,this.getMlModelFileRequestId],requestIdsWith404ErrorsToIgnore:[this.runRequestId,this.getMlModelFileRequestId]}}removeRunRequestId(){this.setState((e=>({requestIds:m().without(e.requestIds,this.runRequestId)})))}componentDidMount(){this.props.getRegisteredModelApi(this.props.modelName,this.registeredModelRequestId);for(const e in this.props.versionsToRuns)if({}.hasOwnProperty.call(this.props.versionsToRuns,e)){const t=this.props.versionsToRuns[e];t?this.props.getRunApi(t,this.runRequestId).catch((()=>{this.removeRunRequestId()})):this.removeRunRequestId();const{modelName:s}=this.props;this.props.getModelVersionApi(s,e,this.versionRequestId),this.props.getModelVersionArtifactApi(s,e).then((t=>this.props.parseMlModelFile(s,e,t.value,this.getMlModelFileRequestId))).catch((()=>{this.setState((e=>({requestIds:m().without(e.requestIds,this.getMlModelFileRequestId)})))}))}}render(){return(0,S.Y)(O.L,{children:(0,S.Y)(c.Ay,{requestIds:this.state.requestIds,requestIdsWith404sToIgnore:this.state.requestIdsWith404ErrorsToIgnore,children:(0,S.Y)(E,{modelName:this.props.modelName,versionsToRuns:this.props.versionsToRuns})})})}}const G={getRunApi:i.aO,getRegisteredModelApi:d.SY,getModelVersionApi:d.s5,getModelVersionArtifactApi:d.ax,parseMlModelFile:d.qI},j=(0,B.h)((0,o.Ng)(((e,t)=>{const{location:s}=t,n=r().parse(s.search);return{modelName:decodeURIComponent(JSON.parse(n["?name"])),versionsToRuns:JSON.parse(n.runs)}}),G)(W)),J=(0,_.X)(K.A.mlflowServices.MODEL_REGISTRY,j);var $=J}}]);
//# sourceMappingURL=21.a21ce989.chunk.js.map