"use strict";(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[427],{88466:(e,o,r)=>{r.d(o,{U:()=>l});var t=r(63434),n=r(98358),a={name:"g8zzui",styles:"cursor:progress"};function l(e){let{children:o,label:r}=e;if(!r)return(0,n.Y)(n.FK,{children:o});return(0,n.FD)("div",{css:a,children:[(0,n.Y)("span",{css:t.Q,children:r}),(0,n.Y)("div",{"aria-hidden":!0,children:o})]})}},27443:(e,o,r)=>{r.d(o,{$n:()=>C,CL:()=>y,oc:()=>x});var t=r(71218),n=r(63649),a=r(65848),l=r(76495),i=r(46792),c=r(8161),d=r(39659),s=r(80838),u=r(60973),g=r(8467),p=r(80842),h=r(69391),f=r(94167),m=r(58829),v=r(98358);let b=new WeakMap,y=e=>{let{theme:o,...r}=e,t=b.get(o)||new Map;b.has(o)||b.set(o,t);let n=JSON.stringify(r),a=t.get(n);if(a)return a;let l=x(e);return t.set(n,l),l};function w(e){return`${e.general.iconfontCssPrefix}-btn-end-icon`}let x=e=>{let{theme:o,classNamePrefix:r,loading:n,withIcon:a,onlyIcon:i,isAnchor:c,enableAnimation:s,size:u,type:g,useFocusPseudoClass:p,forceIconStyles:f,danger:m,useNewShadows:v,useNewBorderRadii:b}=e,y=`.${o.general.iconfontCssPrefix}`,x=`.${w(o)}`,C=`.${r}-btn-loading-icon`,k=`.${r}-btn-icon-only`,D=`.${r}-btn-primary`,S=`.${r}-btn-link`,B=`.${r}-btn-dangerous`,H={background:o.colors.actionTertiaryBackgroundDefault,color:o.colors.actionTertiaryTextDefault,...v&&{boxShadow:"none"},"&:hover":{background:o.colors.actionTertiaryBackgroundHover,color:o.colors.actionTertiaryTextHover},"&:active":{background:o.colors.actionTertiaryBackgroundPress,color:o.colors.actionTertiaryTextPress}},A={fontSize:o.general.iconFontSize,lineHeight:0,..."small"===u&&{lineHeight:o.typography.lineHeightSm,height:16,...(i||f)&&{fontSize:16}}},$={color:o.colors.textSecondary},T=`span > ${x} > ${y}`,R={lineHeight:o.typography.lineHeightBase,boxShadow:v?o.shadows.xs:"none",height:o.general.heightSm,display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",...b&&{borderRadius:o.borders.borderRadiusSm},...!i&&!f&&{"&&":{padding:"4px 12px",..."small"===u&&{padding:"0 8px"}}},...(i||f)&&{width:o.general.heightSm},..."small"===u&&{height:24,lineHeight:o.typography.lineHeightBase,...(i||f)&&{width:24,paddingTop:0,paddingBottom:0,verticalAlign:"middle"}},"&:focus-visible":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:(o.isDarkMode,o.colors.actionDefaultBorderFocus)},...(0,l.o8)(o,n),[`&${D}`]:{...(0,l.p_)(o)},[`&${S}`]:{...(0,l.pe)(o),..."link"===g&&{padding:"unset",height:"auto",border:"none",boxShadow:"none","&[disabled],&:hover":{background:"none"}}},[`&${B}${D}`]:{...(0,l.ap)(o)},[`&${B}`]:{...(0,l.om)(o)},"&[disabled]":{...(0,l.wy)(o,v)},[`&${S}:disabled`]:{...(0,l.lp)(o,v)},[`&${B}:disabled`]:{...(0,l.xc)(o,v)},[`&${D}:disabled`]:{...(0,l.Pp)(o,v)},[`&[disabled], &${B}:disabled`]:{...v&&{boxShadow:"none"},...(i||f)&&{backgroundColor:"transparent","&:hover":{backgroundColor:"transparent"},"&:active":{backgroundColor:"transparent"}}},[C]:{display:"none"},...n&&{"::before":{opacity:0},cursor:"default",[`${C}`]:{...i?{verticalAlign:"middle"}:{position:"absolute"},svg:{animationDuration:"1s !important"}},[`& > ${C} .anticon`]:{paddingRight:0},[`> :not(${C})`]:{opacity:0,visibility:"hidden",...a&&{paddingLeft:o.general.iconFontSize+o.spacing.xs}}},[`> ${y} + span, > span + ${y}`]:{marginRight:0,marginLeft:o.spacing.xs},[`> ${y}`]:A,[`> ${T}`]:{...A,marginLeft:"small"===u?o.spacing.xs:o.spacing.sm},...!g&&!m&&{[`&:enabled:not(:hover):not(:active) > ${y}`]:$},...!g&&!m&&{[`&:enabled:not(:hover):not(:active) > ${T}`]:$},[`&[${r}-click-animating-without-extra-node='true']::after`]:{display:"none"},[`&${k}`]:{border:"none",...v&&{boxShadow:"none"},[`&:enabled:not(${S})`]:{...H,color:o.colors.textSecondary,"&:hover > .anticon":{color:H["&:hover"].color,...m&&{color:o.colors.actionDangerDefaultTextHover}},"&:active > .anticon":{color:H["&:active"].color,...m&&{color:o.colors.actionDangerDefaultTextPress}},...n&&{"&&, &:hover, &:active":{backgroundColor:"transparent"}}},[`&:enabled:not(${S}) > .anticon`]:{color:o.colors.textSecondary,...m&&{color:o.colors.actionDangerDefaultTextDefault}},...c&&{lineHeight:`${o.general.heightSm}px`,...(0,l.pe)(o),"&:disabled":{color:o.colors.actionDisabledText}},...n&&{"&&, &:hover, &:active":{backgroundColor:"transparent"}},"&[disabled]:hover":{backgroundColor:"transparent"}},"&:focus":{...p&&{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:(o.isDarkMode,o.colors.actionDefaultBorderFocus)},[`${C}`]:{...i&&{left:0}}},...f&&{padding:"0 6px",lineHeight:o.typography.lineHeightSm,color:o.colors.textSecondary,...n&&{"&&, &:hover, &:active":{backgroundColor:"transparent",borderColor:o.colors.actionDefaultBorderDefault},"&[disabled], &[disabled]:hover, &[disabled]:active":{backgroundColor:"transparent",borderColor:"transparent"}},"& > span":{verticalAlign:-1,height:o.general.heightSm/2,width:o.general.heightSm/2},[`& > ${C} .anticon`]:{height:o.general.heightSm/2,width:o.general.heightSm/2,padding:0}},...(0,d.Cx)(s)},Y={..."tertiary"===g&&{[`&:enabled:not(${k})`]:H,[`&${S}[disabled]`]:{...(0,l.lp)(o,v)}}},P=(0,h.dg)(R),I=(0,h.dg)(Y);return(0,t.AH)(P,I,"","")},C=(()=>{let e=(0,a.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,children:l,size:h,type:b,loading:x,loadingDescription:C,endIcon:k,onClick:D,dangerouslySetForceIconStyles:S,dangerouslyUseFocusPseudoClass:B,dangerouslyAppendWrapperCss:H,componentId:A,analyticsEvents:$,shouldStartInteraction:T,...R}=e,Y=(0,m.W)("databricks.fe.observability.defaultButtonComponentView",!0),P=(0,i.xW)(),{theme:I,classNamePrefix:z}=(0,s.wn)(),{useNewShadows:V,useNewBorderRadii:M}=(0,g.p)(),L=(0,a.useMemo)(()=>null!=$?$:Y?[c.s7.OnClick,c.s7.OnView]:[c.s7.OnClick],[$,Y]),O=(0,c.ei)({componentType:c.v_.Button,componentId:A,analyticsEvents:L,shouldStartInteraction:T,isInteractionSubject:!("submit"===R.htmlType&&P.componentId)}),_=w(I),F=`${z}-btn-loading-icon`,{elementRef:W}=(0,p.z)({onView:O.onView});(0,a.useImperativeHandle)(o,()=>W.current);let E=null!=x?x:"submit"===R.htmlType&&P.isSubmitting;(0,a.useEffect)(()=>{W.current&&(E?(W.current.setAttribute("loading","true"),W.current.classList.add(`${z}-btn-loading`)):(W.current.setAttribute("loading","false"),W.current.classList.remove(`${z}-btn-loading`)))},[E,z,W]);let N=!!((R.icon||k)&&!l),G=(0,a.useCallback)(e=>{var o;if(E)return;O.onClick(e),"submit"===R.htmlType&&null!==(o=P.formRef)&&void 0!==o&&o.current&&(e.preventDefault(),P.formRef.current.requestSubmit()),null==D||D(e)},[E,R.htmlType,P.formRef,O,D]),U=(0,v.Y)(u.y,{className:F,animationDuration:8,inheritColor:!0,label:"loading","aria-label":"loading",loadingDescription:null!=C?C:A,css:(0,t.AH)({color:"inherit !important",pointerEvents:"none",...!N&&!S&&{".anticon":{verticalAlign:"-0.2em"}},'[aria-hidden="true"]':{display:"inline"}},"","")});return(0,v.Y)(d.wC,{children:(0,v.Y)(n.A,{...(0,f.VG)(),...R,css:y({theme:I,classNamePrefix:z,loading:!!E,withIcon:!!R.icon,onlyIcon:N,isAnchor:!!(R.href&&!b),danger:!!R.danger,enableAnimation:I.options.enableAnimation,size:h||"middle",type:b,forceIconStyles:!!S,useFocusPseudoClass:!!B,useNewShadows:V,useNewBorderRadii:M}),href:R.disabled?void 0:R.href,...r,onClick:G,icon:E?U:R.icon,ref:W,type:"tertiary"===b?"link":b,...O.dataComponentProps,children:l&&(0,v.FD)("span",{style:{visibility:E?"hidden":"visible",display:"inline-flex",alignItems:"center",...H},children:[l,k&&(0,v.Y)("span",{className:_,style:{display:"inline-flex",alignItems:"center"},children:k})]})})})});return e.__ANT_BUTTON=!0,e})()},76495:(e,o,r)=>{function t(e){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDefaultBorderDefault,color:e.colors.actionDefaultTextDefault,lineHeight:e.typography.lineHeightBase,textDecoration:"none","&:hover":{backgroundColor:e.colors.actionDefaultBackgroundHover,borderColor:e.colors.actionDefaultBorderHover,color:e.colors.actionDefaultTextHover},"&:active":{backgroundColor:o?e.colors.actionDefaultBackgroundDefault:e.colors.actionDefaultBackgroundPress,borderColor:e.colors.actionDefaultBorderPress,color:e.colors.actionDefaultTextPress}}}function n(e){return{backgroundColor:e.colors.actionPrimaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault,textShadow:"none","&:hover":{backgroundColor:e.colors.actionPrimaryBackgroundHover,borderColor:"transparent",color:e.colors.actionPrimaryTextHover},"&:active":{backgroundColor:e.colors.actionPrimaryBackgroundPress,borderColor:"transparent",color:e.colors.actionPrimaryTextPress}}}function a(e){return{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:e.colors.actionTertiaryBackgroundDefault,color:e.colors.actionTertiaryTextDefault,"&:hover":{backgroundColor:e.colors.actionTertiaryBackgroundHover,borderColor:"transparent",color:e.colors.actionTertiaryTextHover},"&:active":{backgroundColor:e.colors.actionTertiaryBackgroundPress,borderColor:"transparent",color:e.colors.actionTertiaryTextPress},"&[disabled]:hover":{background:"none",color:e.colors.actionDisabledText}}}function l(e){return{backgroundColor:e.colors.actionDangerPrimaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault,"&:hover":{backgroundColor:e.colors.actionDangerPrimaryBackgroundHover,borderColor:"transparent",color:e.colors.actionPrimaryTextHover},"&:active":{backgroundColor:e.colors.actionDangerPrimaryBackgroundPress,borderColor:"transparent",color:e.colors.actionPrimaryTextPress},"&:focus-visible":{outlineColor:e.colors.actionDangerPrimaryBackgroundDefault}}}function i(e){return{backgroundColor:e.colors.actionDangerDefaultBackgroundDefault,borderColor:e.colors.actionDangerDefaultBorderDefault,color:e.colors.actionDangerDefaultTextDefault,"&:hover":{backgroundColor:e.colors.actionDangerDefaultBackgroundHover,borderColor:e.colors.actionDangerDefaultBorderHover,color:e.colors.actionDangerDefaultTextHover},"&:active":{backgroundColor:e.colors.actionDangerDefaultBackgroundPress,borderColor:e.colors.actionDangerDefaultBorderPress,color:e.colors.actionDangerDefaultTextPress},"&:focus-visible":{outlineColor:e.colors.actionDangerPrimaryBackgroundDefault}}}function c(e,o){return{...{backgroundColor:"transparent",borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:"transparent",borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText},"&:active":{backgroundColor:"transparent",borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText}}}function d(e,o){return{...{backgroundColor:e.colors.actionDisabledBorder,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionDisabledBorder,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault},"&:active":{backgroundColor:e.colors.actionDisabledBorder,borderColor:"transparent",color:e.colors.actionPrimaryTextDefault}}}function s(e,o){return d(e,o)}function u(e,o){return{...{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionDisabledText,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionDisabledText},"&:active":{backgroundColor:e.colors.actionTertiaryBackgroundDefault,borderColor:"transparent",color:e.colors.actionDisabledText}}}function g(e,o){return{...{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText},"&:active":{backgroundColor:e.colors.actionDefaultBackgroundDefault,borderColor:e.colors.actionDisabledBorder,color:e.colors.actionDisabledText}}}function p(e,o){return{...{backgroundColor:e.colors.actionDisabledBorder,color:e.colors.actionPrimaryTextDefault,...o&&{boxShadow:"none"}},"&:hover":{backgroundColor:e.colors.actionDisabledBorder,color:e.colors.actionPrimaryTextDefault},"&:active":{backgroundColor:e.colors.actionDisabledBorder,color:e.colors.actionPrimaryTextDefault}}}r.d(o,{J5:()=>g,LM:()=>p,Pp:()=>d,ap:()=>l,lp:()=>u,o8:()=>t,om:()=>i,p_:()=>n,pe:()=>a,wy:()=>c,xc:()=>s})},16389:(e,o,r)=>{r.d(o,{B6:()=>C,Sc:()=>x,xl:()=>b});var t=r(71218),n=r(36292),a=r(88608),l=r.n(a),i=r(65848),c=r(8161),d=r(39659),s=r(80838),u=r(8467),g=r(80842),p=r(69391),h=r(94167),f=r(58829),m=r(98358);function v(e,o){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],t=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0,l=`.${e}-input`,i=`.${e}-inner`,c=`.${e}-indeterminate`,s=`.${e}-checked`,u=`.${e}-disabled`,g=`.${e}-wrapper-disabled`,p=`.${e}-group`,h=`.${e}-wrapper`,f=`${l} + ${i}`,m=`${l}:hover + ${i}`,v=`${l}:active + ${i}`,b=`.${e.replace("-checkbox","")}`;return{[`.${e}`]:{top:"unset",lineHeight:o.typography.lineHeightBase,alignSelf:"flex-start",display:"flex",alignItems:"center",height:o.typography.lineHeightBase},[`&${h}, ${h}`]:{alignItems:"center",lineHeight:o.typography.lineHeightBase},[i]:{borderColor:o.colors.actionDefaultBorderDefault,...a&&{borderRadius:o.borders.borderRadiusSm}},[`&> span:not(.${e})`]:{display:"inline-flex",alignItems:"center"},[`&${p}`]:{display:"flex",flexDirection:"column",rowGap:o.spacing.sm,columnGap:0,...n&&{[`& + ${b}-form-message`]:{marginTop:o.spacing.sm}}},...n&&{[`${b}-hint + &${p}`]:{marginTop:o.spacing.sm}},...r&&{[`&${p}`]:{display:"flex",flexDirection:"row",columnGap:o.spacing.sm,rowGap:0,[`& > ${p}-item`]:{marginRight:0}}},[`${l}:focus-visible + ${i}`]:{outlineWidth:"2px",outlineColor:o.colors.actionDefaultBorderFocus,outlineOffset:"4px",outlineStyle:"solid"},[m]:{backgroundColor:o.colors.actionDefaultBackgroundHover,borderColor:o.colors.actionPrimaryBackgroundHover},[v]:{backgroundColor:o.colors.actionDefaultBackgroundPress,borderColor:o.colors.actionPrimaryBackgroundPress},[s]:{...t&&{[i]:{boxShadow:o.shadows.xs}},"&::after":{border:"none"},[f]:{backgroundColor:o.colors.actionPrimaryBackgroundDefault,borderColor:"transparent"},[m]:{backgroundColor:o.colors.actionPrimaryBackgroundHover,borderColor:"transparent"},[v]:{backgroundColor:o.colors.actionPrimaryBackgroundPress,borderColor:"transparent"}},[c]:{[i]:{...t&&{boxShadow:o.shadows.xs},backgroundColor:o.colors.actionPrimaryBackgroundDefault,borderColor:o.colors.actionPrimaryBackgroundDefault,"&:after":{backgroundColor:o.colors.white,height:"3px",width:"8px",borderRadius:"4px"}},[m]:{backgroundColor:o.colors.actionPrimaryBackgroundHover,borderColor:"transparent"},[v]:{backgroundColor:o.colors.actionPrimaryBackgroundPress}},[`&${g}`]:{[u]:{[`&${s}`]:{[i]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder,"&:after":{borderColor:o.colors.actionDisabledText}},[m]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder}},[`&${c}`]:{[i]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder,"&:after":{borderColor:o.colors.actionDisabledText,backgroundColor:o.colors.actionDisabledText}},[m]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder}},[i]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder,"&:after":{borderColor:"transparent"}},[m]:{backgroundColor:o.colors.actionDisabledBackground,borderColor:o.colors.actionDisabledBorder},"& + span":{color:o.colors.actionDisabledText}}},...(0,d.Cx)(o.options.enableAnimation)}}let b=e=>{let{clsPrefix:o,theme:r,wrapperStyle:n={},useNewFormUISpacing:a}=e,l=a?`, && + .${o}-hint + .${o}-form-message`:"",i={height:r.typography.lineHeightBase,lineHeight:r.typography.lineHeightBase,[`&& + .${o}-hint, && + .${o}-form-message${l}`]:{paddingLeft:r.spacing.lg,marginTop:0},...n};return(0,t.AH)(i,"","")},y=(0,i.forwardRef)(function(e,o){let{isChecked:r,onChange:a,children:y,isDisabled:w=!1,style:x,wrapperStyle:C,dangerouslySetAntdProps:k,className:D,componentId:S,analyticsEvents:B,...H}=e,A=(0,f.W)("databricks.fe.observability.defaultComponentView.checkbox",!1),{theme:$,classNamePrefix:T,getPrefixedClassName:R}=(0,s.wn)(),{useNewShadows:Y,useNewFormUISpacing:P,useNewBorderRadii:I}=(0,u.p)(),z=R("checkbox"),V=(0,i.useMemo)(()=>null!=B?B:A?[c.s7.OnValueChange,c.s7.OnView]:[c.s7.OnValueChange],[B,A]),M=(0,c.ei)({componentType:c.v_.Checkbox,componentId:S,analyticsEvents:V,valueHasNoPii:!0}),{elementRef:L}=(0,g.z)({onView:M.onView,value:null!=r?r:H.defaultChecked});return(0,m.Y)(d.wC,{children:(0,m.Y)("div",{...(0,h.VG)(),className:l()(D,`${z}-container`),css:b({clsPrefix:T,theme:$,wrapperStyle:C,useNewFormUISpacing:P}),ref:L,children:(0,m.Y)(n.A,{checked:null===r?void 0:r,ref:o,onChange:e=>{M.onValueChange(e.target.checked),null==a||a(e.target.checked,e)},disabled:w,indeterminate:null===r,css:(0,t.AH)((0,p.dg)(v(z,$,!1,Y,P,I)),"",""),style:x,"aria-checked":null===r?"mixed":r,...H,...k,...M.dataComponentProps,children:(0,m.Y)(d.dg,{children:y})})})})}),w=(0,i.forwardRef)(function(e,o){let{children:r,layout:t="vertical",...a}=e,{theme:l,getPrefixedClassName:i}=(0,s.wn)(),c=i("checkbox"),{useNewShadows:g,useNewFormUISpacing:p,useNewBorderRadii:f}=(0,u.p)();return(0,m.Y)(d.wC,{children:(0,m.Y)(n.A.Group,{...(0,h.VG)(),ref:o,...a,css:v(c,l,"horizontal"===t,g,p,f),children:(0,m.Y)(d.dg,{children:r})})})}),x=Object.assign(y,{Group:w}),C=w},8161:(e,o,r)=>{r.d(o,{Ie:()=>s,NI:()=>d,PS:()=>function e(o,r){if(n().isValidElement(o))return n().cloneElement(o,r);if(Array.isArray(o))return o.map(o=>e(o,r));return o},WF:()=>m,ei:()=>v,s7:()=>c,v_:()=>i,z8:()=>f,zI:()=>b});var t=r(65848),n=r.n(t),a=r(47088),l=r(98358);let i=function(e){return e.Accordion="accordion",e.Alert="alert",e.Banner="banner",e.Button="button",e.Card="card",e.Checkbox="checkbox",e.ContextMenuCheckboxItem="context_menu_checkbox_item",e.ContextMenuItem="context_menu_item",e.ContextMenuRadioGroup="context_menu_radio_group",e.DialogCombobox="dialog_combobox",e.Drawer="drawer_content",e.DropdownMenuCheckboxItem="dropdown_menu_checkbox_item",e.DropdownMenuItem="dropdown_menu_item",e.DropdownMenuRadioGroup="dropdown_menu_radio_group",e.Form="form",e.Input="input",e.LegacySelect="legacy_select",e.Modal="modal",e.Notification="notification",e.Pagination="pagination",e.PillControl="pill_control",e.Popover="popover",e.PreviewCard="preview_card",e.Radio="radio",e.RadioGroup="radio_group",e.SegmentedControlGroup="segmented_control_group",e.SimpleSelect="simple_select",e.Switch="switch",e.TableHeader="table_header",e.Tabs="tabs",e.Tag="tag",e.TextArea="text_area",e.ToggleButton="toggle_button",e.Tooltip="tooltip",e.TypeaheadCombobox="typeahead_combobox",e.TypographyLink="typography_link",e}({}),c=function(e){return e.OnClick="onClick",e.OnSubmit="onSubmit",e.OnValueChange="onValueChange",e.OnView="onView",e}({}),d=function(e){return e.Success="success",e.Error="error",e.Warning="warning",e.Info="info",e.InfoLightPurple="info_light_purple",e.InfoDarkPurple="info_dark_purple",e}({}),s={success:d.Success,error:d.Error,warning:d.Warning,info:d.Info,info_light_purple:d.InfoLightPurple,info_dark_purple:d.InfoDarkPurple},u=(e,o)=>o.includes(e),g={callback:()=>{}},p=n().createContext(g),h=()=>(0,t.useContext)(p),f=(0,t.createContext)({dataComponentProps:{}}),m=e=>{let o=(0,t.useContext)(f);return e===o.dataComponentProps["data-component-type"]?o.dataComponentProps:{}},v=e=>{let{componentType:o,componentId:r,componentSubType:n,analyticsEvents:l,valueHasNoPii:i,shouldStartInteraction:d,isInteractionSubject:s}=e,p=h(),f=(0,a.dU)(),m=(0,t.useMemo)(()=>d||void 0===d&&!f.suppressAnalyticsStartInteraction,[f,d]);return(0,t.useMemo)(()=>{let e=r?{"data-component-id":r,"data-component-type":o}:{};if(p===g||void 0===r)return{onClick:()=>{},onSubmit:()=>{},onValueChange:()=>{},onView:()=>{},dataComponentProps:e};return{onClick:e=>{u(c.OnClick,l)&&p.callback({eventType:c.OnClick,componentType:o,componentId:r,componentSubType:n,value:void 0,shouldStartInteraction:m,event:e,isInteractionSubject:s})},onSubmit:e=>{let t=u(c.OnSubmit,l)?"default":"associate_event_only";p.callback({eventType:c.OnSubmit,componentType:o,componentId:r,componentSubType:n,value:void 0,shouldStartInteraction:m,event:e.event,mode:t,referrerComponent:e.referrerComponent,formPropertyValues:{initial:e.initialState,final:e.finalState}})},onValueChange:e=>{u(c.OnValueChange,l)&&p.callback({eventType:c.OnValueChange,componentType:o,componentId:r,componentSubType:n,value:i?e:void 0,shouldStartInteraction:!1})},onView:e=>{u(c.OnView,l)&&p.callback({eventType:c.OnView,componentType:o,componentId:r,componentSubType:n,value:i?e:void 0,shouldStartInteraction:!1})},dataComponentProps:e}},[p,r,l,o,n,m,s,i])};function b(e){let{children:o,callback:r}=e,n=(0,t.useMemo)(()=>({callback:r}),[r]);return(0,l.Y)(p.Provider,{value:n,children:o})}},47088:(e,o,r)=>{r.d(o,{dU:()=>i,pz:()=>a,vR:()=>l});var t=r(65848),n=r.n(t);let a={suppressAnalyticsStartInteraction:!0},l=n().createContext({suppressAnalyticsStartInteraction:!1}),i=()=>(0,t.useContext)(l)},83221:(e,o,r)=>{r.d(o,{Js:()=>u,_G:()=>n,aV:()=>s,ed:()=>c,kQ:()=>d,oS:()=>l,pY:()=>p,v8:()=>a,vu:()=>g,x9:()=>t});let t=(e,o)=>{let r=e.spacing.xs+e.spacing.sm,t=e.spacing.md,n=e.spacing.sm;if("string"==typeof o)return`calc(${o} - ${r+t+n}px)`;return o-r+t+n};function n(e){return e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")}function a(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"start",r=arguments.length>2?arguments[2]:void 0;if(n(e)){let t=l(e,"end"===o?"previous":"next");t&&c(t,r)}else c(e,r)}function l(e,o){let r="previous"===o?e.previousElementSibling:e.nextElementSibling;if((null==r?void 0:r.getAttribute("role"))==="option"){if(n(r))return l(r,o);return r}if(r){let e=r;for(;e&&("option"!==e.getAttribute("role")||n(e));)e="previous"===o?e.previousElementSibling:e.nextElementSibling;return e}return null}let i=e=>{let o=s(e);null==o||o.forEach(e=>{e.setAttribute("tabIndex","-1"),e.setAttribute("data-highlighted","false")})},c=function(e,o){var r;let t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];o&&(o.setAttribute("tabIndex","-1"),o.setAttribute("data-highlighted","false")),t&&e.focus(),e.setAttribute("tabIndex","0"),e.setAttribute("data-highlighted","true"),null===(r=e.scrollIntoView)||void 0===r||r.call(e,{block:"center"})},d=e=>{var o;return null!==(o=e.find(e=>"true"===e.getAttribute("data-highlighted")))&&void 0!==o?o:void 0},s=e=>{var o;let r=null===(o=e.closest('[data-combobox-option-list="true"]'))||void 0===o?void 0:o.querySelectorAll('[role="option"]');return r?Array.from(r):void 0},u=(e,o)=>{let{onKeyDown:r,onMouseEnter:t,onDefaultKeyDown:n,disableMouseOver:i,setDisableMouseOver:d}=o;return{onKeyDown:o=>{var t,i;switch(null==r||r(o),o.key){case"ArrowDown":o.preventDefault(),d(!0);let u=l(o.currentTarget,"next");if(u)c(u,o.currentTarget);else{let e=null===(t=s(o.currentTarget))||void 0===t?void 0:t[0];e&&a(e,"start",o.currentTarget)}break;case"ArrowUp":o.preventDefault(),d(!0);let g=l(o.currentTarget,"previous");if(g)c(g,o.currentTarget);else{let e=null===(i=s(o.currentTarget))||void 0===i?void 0:i.slice(-1)[0];e&&a(e,"end",o.currentTarget)}break;case"Enter":o.preventDefault(),e(o);break;default:null==n||n(o)}},onMouseMove:e=>{i&&d(!1)},onMouseEnter:e=>{i||(null==t||t(e),g(e.currentTarget))}}},g=e=>{i(e),e.setAttribute("tabIndex","0"),e.focus()},p=(e,o,r)=>{var t,n;if("Escape"===e.key||"Tab"===e.key||"Enter"===e.key)return;e.preventDefault();let a=Array.from(null!==(t=null===(n=e.currentTarget.parentElement)||void 0===n?void 0:n.children)&&void 0!==t?t:[]),l=a.findIndex(o=>{var t,n;return(null!==(t=null===(n=o.textContent)||void 0===n?void 0:n.toLowerCase())&&void 0!==t?t:"").startsWith(r+e.key)});if(-1!==l){let t=a[l];t.focus(),o&&o(r+e.key),g(t)}}},28219:(e,o,r)=>{r.r(o),r.d(o,{Arrow:()=>M,CheckboxItem:()=>I,Content:()=>B,Group:()=>W,HintColumn:()=>E,HintRow:()=>N,IconWrapper:()=>G,Item:()=>$,ItemIndicator:()=>V,Label:()=>T,RadioGroup:()=>z,RadioItem:()=>L,Root:()=>S,Separator:()=>R,Sub:()=>F,SubContent:()=>H,SubTrigger:()=>Y,Trigger:()=>A,TriggerItem:()=>P,dropdownContentStyles:()=>K,dropdownItemStyles:()=>j,dropdownSeparatorStyles:()=>J});var t=r(71218),n=r(81434),a=r(35012),l=r(65848),i=r.n(l),c=r(19362),d=r(46792),s=r(8161),u=r(80838),g=r(63908),p=r(17061),h=r(23240),f=r(49817),m=r(60054),v=r(71009),b=r(8467),y=r(69391),w=r(94167),x=r(58829),C=r(98358);let k=(0,l.createContext)({isOpen:!1,setIsOpen:e=>{}}),D=()=>i().useContext(k),S=e=>{let{children:o,itemHtmlType:r,...t}=e,[n,c]=i().useState(!!(t.defaultOpen||t.open)),d=(0,l.useRef)(void 0!==t.open||void 0!==t.onOpenChange).current;(0,l.useEffect)(()=>{d&&c(!!t.open)},[d,t.open]);let s=e=>{d||c(e),t.onOpenChange&&t.onOpenChange(e)};return(0,C.Y)(a.bL,{...t,...!d&&{open:n,onOpenChange:s},children:(0,C.Y)(k.Provider,{value:{isOpen:d?t.open:n,setIsOpen:d?t.onOpenChange:s,itemHtmlType:r},children:(0,C.Y)(v.C,{children:o})})})},B=(0,l.forwardRef)(function(e,o){let{children:r,minWidth:t=220,matchTriggerWidth:n,forceCloseOnEscape:l,onEscapeKeyDown:i,onKeyDown:d,...s}=e,{getPopupContainer:p}=(0,g.G)(),{theme:h}=(0,u.wn)(),{useNewShadows:m}=(0,b.p)(),{setIsOpen:v}=D(),{isInsideModal:y}=(0,f.k3)();return(0,C.Y)(a.ZL,{container:p&&p(),children:(0,C.Y)(a.UC,{...(0,w.VG)(),ref:o,loop:!0,css:[q(h,m),{minWidth:t},n?{width:"var(--radix-dropdown-menu-trigger-width)"}:{},"",""],sideOffset:4,align:"start",onKeyDown:e=>{"Escape"===e.key&&((y||l)&&(e.stopPropagation(),null==v||v(!1)),null==i||i(e.nativeEvent)),("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&(0,c.K)(e),null==d||d(e)},...s,onWheel:e=>{var o;e.stopPropagation(),null==s||null===(o=s.onWheel)||void 0===o||o.call(s,e)},onTouchMove:e=>{var o;e.stopPropagation(),null==s||null===(o=s.onTouchMove)||void 0===o||o.call(s,e)},children:r})})}),H=(0,l.forwardRef)(function(e,o){let{children:r,minWidth:t=220,onKeyDown:n,...d}=e,{getPopupContainer:s}=(0,g.G)(),{theme:p}=(0,u.wn)(),{useNewShadows:h}=(0,b.p)(),[f,m]=i().useState(!0),[v,y]=i().useState(null),{isOpen:x}=_(),k=(0,l.useRef)(null);(0,l.useImperativeHandle)(o,()=>k.current);let D=(0,l.useCallback)(()=>{if(k.current){let e=parseFloat(getComputedStyle(k.current).getPropertyValue("--radix-dropdown-menu-content-available-width")),o=k.current.offsetWidth,r=k.current.getAttribute("data-side");"left"===r||"right"===r?y(r):y(null),e<o?m(!1):m(!0)}},[]);(0,l.useEffect)(()=>(window.addEventListener("resize",D),D(),()=>{window.removeEventListener("resize",D)}),[D]),(0,l.useEffect)(()=>{x&&setTimeout(()=>{D()},25)},[x,D]);let S="calc(var(--radix-dropdown-menu-content-available-width) + var(--radix-dropdown-menu-trigger-width) * -1)";"left"===v&&(S="calc(var(--radix-dropdown-menu-trigger-width) - var(--radix-dropdown-menu-content-available-width))");let B=`
    transform-origin: var(--radix-dropdown-menu-content-transform-origin) !important;
    transform: translateX(${S}) !important;
`;return(0,C.Y)(a.ZL,{container:s&&s(),children:(0,C.Y)(a.G5,{...(0,w.VG)(),ref:k,loop:!0,css:[q(p,h),{minWidth:t},f?"":B,"",""],sideOffset:-2,alignOffset:-5,onKeyDown:e=>{("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&(e.stopPropagation(),(0,c.K)(e)),null==n||n(e)},...d,children:r})})}),A=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(a.l9,{...(0,w.VG)(),ref:o,...t,children:r})}),$=(0,l.forwardRef)(function(e,o){let{children:r,disabledReason:t,danger:i,onClick:c,componentId:u,analyticsEvents:g,...p}=e,h=(0,x.W)("databricks.fe.observability.defaultComponentView.dropdownMenu",!1),f=(0,d.xW)(),{itemHtmlType:b}=D(),y=(0,l.useRef)(null);(0,l.useImperativeHandle)(o,()=>y.current);let w=(0,l.useMemo)(()=>null!=g?g:h?[s.s7.OnClick,s.s7.OnView]:[s.s7.OnClick],[g,h]),k=(0,s.ei)({componentType:s.v_.DropdownMenuItem,componentId:u,analyticsEvents:w,isInteractionSubject:!("submit"===b&&f.componentId)}),{elementRef:S}=(0,v.Y)({onView:p.asChild?()=>{}:k.onView}),B=(0,n.SV)([y,S]);return(0,C.Y)(a.q7,{css:e=>[j,i&&X(e)],ref:B,onClick:e=>{if(p.disabled)e.preventDefault();else{var o;p.asChild||k.onClick(e),"submit"===b&&null!==(o=f.formRef)&&void 0!==o&&o.current&&(e.preventDefault(),f.formRef.current.requestSubmit()),null==c||c(e)}},onKeyDown:e=>{var o;("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&e.preventDefault(),null===(o=p.onKeyDown)||void 0===o||o.call(p,e)},...p,...k.dataComponentProps,children:(0,m.m)(r,p,t,y)})}),T=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(a.JU,{ref:o,css:[j,e=>({color:e.colors.textSecondary,"&:hover":{cursor:"default"}}),"",""],...t,children:r})}),R=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(a.wv,{ref:o,css:J,...t,children:r})}),Y=(0,l.forwardRef)(function(e,o){let{children:r,disabledReason:t,...n}=e,i=(0,l.useRef)(null);return(0,l.useImperativeHandle)(o,()=>i.current),(0,C.FD)(a.ZP,{ref:i,css:[j,e=>({'&[data-state="open"]':{backgroundColor:e.colors.actionTertiaryBackgroundHover}}),"",""],onKeyDown:e=>{var o;("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&e.preventDefault(),null===(o=n.onKeyDown)||void 0===o||o.call(n,e)},...n,children:[(0,m.m)(r,n,t,i),(0,C.Y)(E,{css:e=>({margin:U.subMenuIconMargin(e),display:"flex",alignSelf:"stretch",alignItems:"center"}),children:(0,C.Y)(p.A,{css:e=>({fontSize:U.subMenuIconSize(e)})})})]})}),P=Y,I=(0,l.forwardRef)(function(e,o){var r;let{children:t,disabledReason:i,componentId:c,analyticsEvents:d,onCheckedChange:u,...g}=e,p=(0,x.W)("databricks.fe.observability.defaultComponentView.dropdownMenu",!1),h=(0,l.useRef)(null);(0,l.useImperativeHandle)(o,()=>h.current);let f=(0,l.useMemo)(()=>null!=d?d:p?[s.s7.OnValueChange,s.s7.OnView]:[s.s7.OnValueChange],[d,p]),b=(0,s.ei)({componentType:s.v_.DropdownMenuCheckboxItem,componentId:c,analyticsEvents:f,valueHasNoPii:!0}),{elementRef:y}=(0,v.Y)({onView:b.onView,value:null!==(r=g.checked)&&void 0!==r?r:g.defaultChecked}),w=(0,n.SV)([h,y]),k=(0,l.useCallback)(e=>{b.onValueChange(e),null==u||u(e)},[b,u]);return(0,C.Y)(a.H_,{ref:w,css:e=>[j,Q(e)],onCheckedChange:k,onKeyDown:e=>{var o;("Tab"===e.key||"ArrowDown"===e.key||"ArrowUp"===e.key)&&e.preventDefault(),null===(o=g.onKeyDown)||void 0===o||o.call(g,e)},...g,...b.dataComponentProps,children:(0,m.m)(t,g,i,h)})}),z=(0,l.forwardRef)(function(e,o){var r;let{children:t,componentId:i,analyticsEvents:c,onValueChange:d,valueHasNoPii:u,...g}=e,p=(0,x.W)("databricks.fe.observability.defaultComponentView.dropdownMenu",!1),h=(0,l.useRef)(null);(0,l.useImperativeHandle)(o,()=>h.current);let f=(0,l.useMemo)(()=>null!=c?c:p?[s.s7.OnValueChange,s.s7.OnView]:[s.s7.OnValueChange],[c,p]),m=(0,s.ei)({componentType:s.v_.DropdownMenuRadioGroup,componentId:i,analyticsEvents:f,valueHasNoPii:u}),{elementRef:b}=(0,v.Y)({onView:m.onView,value:null!==(r=g.value)&&void 0!==r?r:g.defaultValue}),y=(0,n.SV)([h,b]),w=(0,l.useCallback)(e=>{m.onValueChange(e),null==d||d(e)},[m,d]);return(0,C.Y)(a.z6,{ref:y,onValueChange:w,...g,...m.dataComponentProps,children:t})}),V=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)(a.VF,{ref:o,css:e=>({marginLeft:-(U.checkboxIconWidth(e)+U.checkboxPaddingRight(e)),position:"absolute",fontSize:e.general.iconFontSize}),...t,children:null!=r?r:(0,C.Y)(h.A,{css:e=>({color:e.colors.textSecondary})})})}),M=(0,l.forwardRef)(function(e,o){let{children:r,...n}=e,{theme:l}=(0,u.wn)();return(0,C.Y)(a.i3,{css:(0,t.AH)({fill:l.colors.backgroundPrimary,stroke:l.colors.borderDecorative,strokeDashoffset:-U.arrowBottomLength(),strokeDasharray:U.arrowBottomLength()+2*U.arrowSide(),strokeWidth:U.arrowStrokeWidth(),position:"relative",top:-1},"",""),ref:o,width:12,height:6,...n,children:r})}),L=(0,l.forwardRef)(function(e,o){let{children:r,disabledReason:t,...n}=e,i=(0,l.useRef)(null);return(0,l.useImperativeHandle)(o,()=>i.current),(0,C.Y)(a.hN,{ref:i,css:e=>[j,Q(e)],...n,children:(0,m.m)(r,n,t,i)})}),O=(0,l.createContext)({isOpen:!1}),_=()=>i().useContext(O),F=e=>{var o;let{children:r,onOpenChange:t,...n}=e,[l,c]=i().useState(null!==(o=n.defaultOpen)&&void 0!==o&&o);return(0,C.Y)(a.Pb,{onOpenChange:e=>{null==t||t(e),c(e)},...n,children:(0,C.Y)(O.Provider,{value:{isOpen:l},children:r})})},W=a.YJ,E=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)("div",{ref:o,css:[Z,"margin-left:auto;",""],...t,children:r})}),N=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)("div",{ref:o,css:[Z,"min-width:100%;",""],...t,children:r})}),G=(0,l.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,C.Y)("div",{ref:o,css:e=>({fontSize:16,color:e.colors.textSecondary,paddingRight:e.spacing.sm}),...t,children:r})}),U={itemPaddingVertical:e=>.5*e.spacing.xs+.5*e.spacing.sm,itemPaddingHorizontal:e=>e.spacing.sm,checkboxIconWidth:e=>e.general.iconFontSize,checkboxPaddingLeft:e=>e.spacing.sm+e.spacing.xs,checkboxPaddingRight:e=>e.spacing.sm,subMenuIconMargin(e){let o=this.itemPaddingVertical(e)/2,r=-this.itemPaddingVertical(e)+1.5*e.spacing.sm;return`${-o}px ${-r}px ${-o}px auto`},subMenuIconSize:e=>e.spacing.lg,arrowBottomLength:()=>30,arrowHeight:()=>10,arrowSide(){return 2*(this.arrowHeight()**2*2)**.5},arrowStrokeWidth:()=>2},K=(e,o)=>({backgroundColor:e.colors.backgroundPrimary,color:e.colors.textPrimary,lineHeight:e.typography.lineHeightBase,border:`1px solid ${e.colors.borderDecorative}`,borderRadius:e.borders.borderRadiusSm,padding:`${e.spacing.xs}px 0`,boxShadow:o?e.shadows.lg:e.general.shadowLow,userSelect:"none",overflowY:"auto",maxHeight:"var(--radix-dropdown-menu-content-available-height)",...(0,y.WO)(e,o),zIndex:1e4,a:(0,y.dg)({color:e.colors.textPrimary,"&:hover, &:focus":{color:e.colors.textPrimary,textDecoration:"none"}})}),q=(e,o)=>({...K(e,o)}),j=e=>({padding:`${U.itemPaddingVertical(e)}px ${U.itemPaddingHorizontal(e)}px`,display:"flex",flexWrap:"wrap",alignItems:"center",outline:"unset","&:hover":{cursor:"pointer"},"&:focus":{backgroundColor:e.colors.actionTertiaryBackgroundHover,"&:not(:hover)":{outline:`2px auto ${e.colors.actionDefaultBorderFocus}`,outlineOffset:"-1px"}},"&[data-disabled]":{pointerEvents:"none",color:`${e.colors.actionDisabledText} !important`}}),X=e=>({color:e.colors.textValidationDanger,"&:hover, &:focus":{backgroundColor:e.colors.actionDangerDefaultBackgroundHover}}),Q=e=>({position:"relative",paddingLeft:U.checkboxIconWidth(e)+U.checkboxPaddingLeft(e)+U.checkboxPaddingRight(e)}),Z=e=>({color:e.colors.textSecondary,fontSize:e.typography.fontSizeSm,"[data-disabled] &":{color:e.colors.actionDisabledText}}),J=e=>({height:1,margin:`${e.spacing.xs}px ${e.spacing.sm}px`,backgroundColor:e.colors.borderDecorative})},19362:(e,o,r)=>{r.d(o,{K:()=>l});let t=e=>e.querySelectorAll('[role^="menuitem"]'),n=e=>{let o=e.currentTarget.closest('[role="menu"]');if(!o)return;let r=t(o),n=document.activeElement,a="ArrowUp"===e.key||"Tab"===e.key&&e.shiftKey,l=Array.from(r).findIndex(e=>e===n),i=a?l-1:l+1;(i<0||i>=r.length)&&(i=a?r.length-1:0);let c=r[i];if(c){if(c.hasAttribute("data-disabled")){let o=c.querySelector("[data-disabled-tooltip]");null==o||o.setAttribute("tabindex","0"),o&&(e.preventDefault(),o.focus())}else c.focus(),c.setAttribute("data-highlighted","true")}},a=e=>{let o=document.activeElement,r=o.closest('[role^="menuitem"]'),n=o.closest('[role="menu"]');if(!n)return;let a=t(n),l=Array.from(a).findIndex(e=>e===r),i="ArrowUp"===e.key||"Tab"===e.key&&e.shiftKey,c=i?l-1:l+1;(c<0||c>=a.length)&&(c=i?a.length-1:0);let d=a[c];if(d){if(o.removeAttribute("tabindex"),o.blur(),d.hasAttribute("data-disabled")){let o=d.querySelector("[data-disabled-tooltip]");null==o||o.setAttribute("tabindex","0"),o&&(e.preventDefault(),o.focus())}else d.focus()}},l=e=>{var o,r,t,l;let i=(null===(o=document.activeElement)||void 0===o?void 0:o.getAttribute("role"))==="menuitem"||(null===(r=document.activeElement)||void 0===r?void 0:r.getAttribute("role"))==="menuitemcheckbox"||(null===(t=document.activeElement)||void 0===t?void 0:t.getAttribute("role"))==="menuitemradio",c=null===(l=document.activeElement)||void 0===l?void 0:l.hasAttribute("data-disabled-tooltip");i||!c?n(e):a(e)}},70821:(e,o,r)=>{r.d(o,{S:()=>g});var t=r(71218);r(65848);var n=r(80838),a=r(86648),l=r(3078),i=r(94167),c=r(98358);let{Title:d,Paragraph:s}=l.o;var u={name:"zl1inp",styles:"display:flex;justify-content:center"};let g=e=>{let{theme:o,classNamePrefix:r}=(0,n.wn)(),{title:l,description:g,image:p=(0,c.Y)(a.A,{}),button:h,dangerouslyAppendEmotionCSS:f,...m}=e;return(0,c.Y)("div",{...m,...(0,i.VG)(),css:u,children:(0,c.FD)("div",{css:[function(e){let o={display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",maxWidth:600,wordBreak:"break-word",'> [role="img"]':{fontSize:64,color:e.colors.actionDisabledText,marginBottom:e.spacing.md}};return(0,t.AH)(o,"","")}(o),f,"",""],children:[p,l&&(0,c.Y)(d,{level:3,css:function(e,o){let r={[`&.${o}-typography`]:{color:e.colors.textSecondary,marginTop:0,marginBottom:0}};return(0,t.AH)(r,"","")}(o,r),children:l}),(0,c.Y)(s,{css:function(e,o){let r={[`&.${o}-typography`]:{color:e.colors.textSecondary,marginBottom:e.spacing.md}};return(0,t.AH)(r,"","")}(o,r),children:g}),h]})})}},63908:(e,o,r)=>{r.d(o,{G:()=>a});var t=r(65848),n=r(39659);function a(){return(0,t.useContext)(n.dC)}},96277:(e,o,r)=>{r.d(o,{I:()=>p});var t=r(71218),n=r(15232),a=r(65848),l=r(39659),i=r(80838),c=r(63434),d=r(94167),s=r(60499),u=r(98358);let g=(e,o)=>{switch(o){case"success":return{color:e.colors.textValidationSuccess};case"warning":return{color:e.colors.textValidationWarning};case"danger":return{color:e.colors.textValidationDanger};case"ai":return{"svg *":{fill:"var(--ai-icon-gradient)"}};default:return{color:o}}},p=(0,a.forwardRef)((e,o)=>{let{component:r,dangerouslySetAntdProps:p,color:h,style:f,...m}=e,{theme:v}=(0,i.wn)(),b=(0,s.Y)("ai-linear-gradient"),y=(0,a.useMemo)(()=>r?e=>{let{fill:o,...t}=e;return(0,u.FD)(u.FK,{children:[(0,u.Y)(r,{fill:"none",...t,style:"ai"===h?{"--ai-icon-gradient":`url(#${b})`,...t.style}:t.style}),"ai"===h&&(0,u.Y)("svg",{width:"0",height:"0",viewBox:"0 0 0 0",css:c.Q,children:(0,u.Y)("defs",{children:(0,u.FD)("linearGradient",{id:b,x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,u.Y)("stop",{offset:"20.5%",stopColor:v.colors.branded.ai.gradientStart}),(0,u.Y)("stop",{offset:"46.91%",stopColor:v.colors.branded.ai.gradientMid}),(0,u.Y)("stop",{offset:"79.5%",stopColor:v.colors.branded.ai.gradientEnd})]})})})]})}:void 0,[r,h,b,v]);return(0,u.Y)(l.wC,{children:(0,u.Y)(n.A,{...(0,d.VG)(),ref:o,"aria-hidden":"true",css:(0,t.AH)({fontSize:v.general.iconFontSize,...g(v,h)},"",""),component:y,style:{...f},...m,...p})})})},67630:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"M7 3.25H1v1.5h6zM15 11.25H1v1.5h14zM1 8.75h10v-1.5H1z"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="BarsAscendingVerticalIcon";let c=i},53127:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"m6.144 12.331.972-.972 1.06 1.06-.971.973a3.625 3.625 0 1 1-5.127-5.127l2.121-2.121A3.625 3.625 0 0 1 10.32 8H8.766a2.125 2.125 0 0 0-3.507-.795l-2.121 2.12a2.125 2.125 0 0 0 3.005 3.006"}),(0,a.Y)("path",{fill:"currentColor",d:"m9.856 3.669-.972.972-1.06-1.06.971-.973a3.625 3.625 0 1 1 5.127 5.127l-2.121 2.121A3.625 3.625 0 0 1 5.68 8h1.552a2.125 2.125 0 0 0 3.507.795l2.121-2.12a2.125 2.125 0 0 0-3.005-3.006"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ChainIcon";let c=i},23240:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"m15.06 3.56-9.53 9.531L1 8.561 2.06 7.5l3.47 3.47L14 2.5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="CheckIcon";let c=i},80061:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 8.917 10.947 6 12 7.042 8 11 4 7.042 5.053 6z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ChevronDownIcon";let c=i},16306:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.083 8 10 10.947 8.958 12 5 8l3.958-4L10 5.053z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ChevronLeftIcon";let c=i},17061:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8.917 8 6 5.053 7.042 4 11 8l-3.958 4L6 10.947z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ChevronRightIcon";let c=i},98022:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 7.083 5.053 10 4 8.958 8 5l4 3.958L10.947 10z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ChevronUpIcon";let c=i},4700:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.97 8.03 2 3.06 3.06 2l4.97 4.97L13 2l1.06 1.06-4.969 4.97 4.97 4.97L13 14.06 8.03 9.092l-4.97 4.97L2 13z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="CloseIcon";let c=i},26033:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.064 8 4 4.936 4.936 4 8 7.064 11.063 4l.937.936L8.937 8 12 11.063l-.937.937L8 8.937 4.936 12 4 11.063z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="CloseSmallIcon";let c=i},86571:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 17 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"M4.03 12.06 5.091 11l-2.97-2.97 2.97-2.97L4.031 4 0 8.03zM12.091 4l4.03 4.03-4.03 4.03-1.06-1.06L14 8.03l-2.97-2.97z"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="CodeIcon";let c=i},80982:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.78 3.97 5.03 1.22a.75.75 0 0 0-1.06 0L1.22 3.97a.75.75 0 0 0 0 1.06l2.75 2.75a.75.75 0 0 0 1.06 0l2.75-2.75a.75.75 0 0 0 0-1.06m-1.59.53L4.5 6.19 2.81 4.5 4.5 2.81zM15 11.75a3.25 3.25 0 1 0-6.5 0 3.25 3.25 0 0 0 6.5 0M11.75 10a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5",clipRule:"evenodd"}),(0,a.Y)("path",{fill:"currentColor",d:"M14.25 1H9v1.5h4.5V7H15V1.75a.75.75 0 0 0-.75-.75M1 9v5.25c0 .414.336.75.75.75H7v-1.5H2.5V9z"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ConnectIcon";let c=i},23859:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75H5v3.25c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-8.5a.75.75 0 0 0-.75-.75H11V1.75a.75.75 0 0 0-.75-.75zM9.5 5V2.5h-7v7H5V5.75A.75.75 0 0 1 5.75 5zm-3 8.5v-7h7v7z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="CopyIcon";let c=i},89481:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M7.248 10.748a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0M8.748 4.998v4h-1.5v-4z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"m11.533 15.776 4.243-4.243a.75.75 0 0 0 .22-.53v-6.01a.75.75 0 0 0-.22-.53L11.533.22a.75.75 0 0 0-.53-.22h-6.01a.75.75 0 0 0-.53.22L.22 4.462a.75.75 0 0 0-.22.53v6.011c0 .*************.53l4.242 4.243c.141.14.332.22.53.22h6.011a.75.75 0 0 0 .53-.22m2.963-10.473v5.39l-3.804 3.803H5.303L1.5 10.692V5.303L5.303 1.5h5.39z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="DangerIcon";let c=i},26769:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M2 1.75A.75.75 0 0 1 2.75 1h6a.75.75 0 0 1 .53.22l4.5 4.5c.141.14.22.331.22.53V10h-1.5V7H8.75A.75.75 0 0 1 8 6.25V2.5H3.5V16H2zm7.5 1.81 1.94 1.94H9.5z",clipRule:"evenodd"}),(0,a.Y)("path",{fill:"currentColor",d:"M5 11.5V13h9v-1.5zM14 16H5v-1.5h9z"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="FileDocumentIcon";let c=i},37370:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1.75A.75.75 0 0 1 1.75 1h12.5a.75.75 0 0 1 .75.75V4a.75.75 0 0 1-.22.53L10 9.31v4.94a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1-.75-.75V9.31L1.22 4.53A.75.75 0 0 1 1 4zm1.5.75v1.19l4.78 4.78c.141.14.22.331.22.53v4.5h1V9a.75.75 0 0 1 .22-.53l4.78-4.78V2.5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="FilterIcon";let c=i},72152:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("g",{clipPath:"url(#FunctionIcon_svg__a)",children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M9.93 2.988c-.774-.904-2.252-.492-2.448.682L7.094 6h2.005a2.75 2.75 0 0 1 2.585 1.81l.073.202 2.234-2.063 1.018 1.102-2.696 2.489.413 1.137c.18.494.65.823 1.175.823H15V13h-1.1a2.75 2.75 0 0 1-2.585-1.81l-.198-.547-2.61 2.408-1.017-1.102 3.07-2.834-.287-.792A1.25 1.25 0 0 0 9.099 7.5H6.844l-.846 5.076c-.405 2.43-3.464 3.283-5.067 1.412l1.139-.976c.774.904 2.252.492 2.448-.682l.805-4.83H3V6h2.573l.43-2.576C6.407.994 9.465.14 11.07 2.012z",clipRule:"evenodd"})}),(0,a.Y)("defs",{children:(0,a.Y)("clipPath",{children:(0,a.Y)("path",{fill:"#fff",d:"M16 0H0v16h16z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="FunctionIcon";let c=i},22073:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.FD)("g",{fill:"currentColor",fillRule:"evenodd",clipPath:"url(#GearIcon_svg__a)",clipRule:"evenodd",children:[(0,a.Y)("path",{d:"M7.984 5a3 3 0 1 0 0 6 3 3 0 0 0 0-6m-1.5 3a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0"}),(0,a.Y)("path",{d:"M7.966 0q-.51 0-1.005.063a.75.75 0 0 0-.62.51l-.639 1.946q-.315.13-.61.294L3.172 2.1a.75.75 0 0 0-.784.165c-.481.468-.903.996-1.255 1.572a.75.75 0 0 0 .013.802l1.123 1.713a6 6 0 0 0-.15.66L.363 8.07a.75.75 0 0 0-.36.716c.067.682.22 1.34.447 1.962a.75.75 0 0 0 .635.489l2.042.19q.195.276.422.529l-.27 2.032a.75.75 0 0 0 .336.728 8 8 0 0 0 1.812.874.75.75 0 0 0 .778-.192l1.422-1.478a6 6 0 0 0 .677 0l1.422 1.478a.75.75 0 0 0 .778.192 8 8 0 0 0 1.812-.874.75.75 0 0 0 .335-.728l-.269-2.032a6 6 0 0 0 .422-.529l2.043-.19a.75.75 0 0 0 .634-.49c.228-.621.38-1.279.447-1.961a.75.75 0 0 0-.36-.716l-1.756-1.056a6 6 0 0 0-.15-.661l1.123-1.713a.75.75 0 0 0 .013-.802 8 8 0 0 0-1.255-1.572.75.75 0 0 0-.784-.165l-1.92.713q-.295-.163-.61-.294L9.589.573a.75.75 0 0 0-.619-.51A8 8 0 0 0 7.965 0m-.95 3.328.597-1.819a7 7 0 0 1 .705 0l.597 1.819a.75.75 0 0 0 .472.476q.519.177.97.468a.75.75 0 0 0 .668.073l1.795-.668q.234.264.44.552l-1.05 1.6a.75.75 0 0 0-.078.667q.181.501.24 1.05a.75.75 0 0 0 .359.567l1.642.988q-.06.351-.156.687l-1.909.178a.75.75 0 0 0-.569.353q-.287.463-.672.843a.75.75 0 0 0-.219.633l.252 1.901a7 7 0 0 1-.635.306l-1.33-1.381a.75.75 0 0 0-.63-.225 4.5 4.5 0 0 1-1.08 0 .75.75 0 0 0-.63.225l-1.33 1.381a7 7 0 0 1-.634-.306l.252-1.9a.75.75 0 0 0-.219-.634 4.5 4.5 0 0 1-.672-.843.75.75 0 0 0-.569-.353l-1.909-.178a7 7 0 0 1-.156-.687L3.2 8.113a.75.75 0 0 0 .36-.567q.056-.549.239-1.05a.75.75 0 0 0-.078-.666L2.67 4.229q.206-.288.44-.552l1.795.668a.75.75 0 0 0 .667-.073c.3-.193.626-.351.97-.468a.75.75 0 0 0 .472-.476"})]}),(0,a.Y)("defs",{children:(0,a.Y)("clipPath",{children:(0,a.Y)("path",{fill:"#fff",d:"M0 0h16v16H0z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="GearIcon";let c=i},55444:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M7.25 10.5v-3h1.5v3zM8 5a.75.75 0 1 1 0 1.5A.75.75 0 0 1 8 5"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12m0-1.5a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="InfoIcon";let c=i},54198:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M12 8.75H7v-1.5h5zM7 5.5h5V4H7zM12 12H7v-1.5h5zM4.75 5.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5M5.5 8A.75.75 0 1 1 4 8a.75.75 0 0 1 1.5 0M4.75 12a.75.75 0 1 0 0-********* 0 0 0 0 1.5"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1.75A.75.75 0 0 1 1.75 1h12.5a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75zm1.5.75v11h11v-11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ListBorderIcon";let c=i},86648:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"M1.5 2.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0M3 2h13v1.5H3zM3 5.5h13V7H3zM3 9h13v1.5H3zM3 12.5h13V14H3zM.75 7a.75.75 0 1 0 0-********* 0 0 0 0 1.5M1.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0M.75 10.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ListIcon";let c=i},5844:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 24 24",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M23.212 12a.79.79 0 0 1-.789-.788 9.6 9.6 0 0 0-.757-3.751 9.66 9.66 0 0 0-5.129-5.129 9.6 9.6 0 0 0-3.749-.755.788.788 0 0 1 0-1.577c1.513 0 2.983.296 4.365.882a11.1 11.1 0 0 1 3.562 2.403 11.157 11.157 0 0 1 3.283 7.927.785.785 0 0 1-.786.788",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="LoadingIcon";let c=i},84437:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M7.25 9v4h1.5V9z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M12 6V4a4 4 0 0 0-8 0v2H2.75a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h10.5a.75.75 0 0 0 .75-.75v-8.5a.75.75 0 0 0-.75-.75zm.5 1.5v7h-9v-7zM5.5 4v2h5V4a2.5 2.5 0 0 0-5 0",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="LockIcon";let c=i},57477:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M6.42 5.415A.75.75 0 0 0 5 5.75V11h1.5V8.927l.83 1.658a.75.75 0 0 0 1.34 0l.83-1.658V11H11V5.75a.75.75 0 0 0-1.42-.335L8 8.573z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75V1.75a.75.75 0 0 0-.75-.75zm.75 12.5v-11h11v11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="MIcon";let c=i},69397:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M11.5 8.75h-7v-1.5h7z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75V1.75a.75.75 0 0 0-.75-.75zm.75 12.5v-11h11v11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="MinusSquareIcon";let c=i},92546:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("g",{clipPath:"url(#ModelsIcon_svg__a)",children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 4.75a2.75 2.75 0 0 1 5.145-1.353l4.372-.95a2.75 2.75 0 1 1 3.835 2.823l.282 2.257a2.75 2.75 0 1 1-2.517 4.46l-2.62 1.145.003.118a2.75 2.75 0 1 1-4.415-2.19L3.013 7.489A2.75 2.75 0 0 1 0 4.75M2.75 3.5a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5m2.715 1.688q.027-.164.033-.333l4.266-.928a2.75 2.75 0 0 0 2.102 1.546l.282 2.257c-.377.165-.71.412-.976.719zM4.828 6.55a2.8 2.8 0 0 1-.413.388l1.072 3.573q.13-.012.263-.012c.945 0 1.778.476 2.273 1.202l2.5-1.093a2.8 2.8 0 0 1 .012-.797zM12 10.25a1.25 1.25 0 1 1 2.5 0 1.25 1.25 0 0 1-2.5 0M5.75 12a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5M11 2.75a1.25 1.25 0 1 1 2.5 0 1.25 1.25 0 0 1-2.5 0",clipRule:"evenodd"})}),(0,a.Y)("defs",{children:(0,a.Y)("clipPath",{children:(0,a.Y)("path",{fill:"#fff",d:"M0 0h16v16H0z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="ModelsIcon";let c=i},10954:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M10 1h5v5h-1.5V3.56L8.53 8.53 7.47 7.47l4.97-4.97H10z"}),(0,a.Y)("path",{fill:"currentColor",d:"M1 2.75A.75.75 0 0 1 1.75 2H8v1.5H2.5v10h10V8H14v6.25a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75z"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="NewWindowIcon";let c=i},90374:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"M7.889 1A2.39 2.39 0 0 0 5.5 3.389H7c0-.491.398-.889.889-.889h.371a.74.74 0 0 1 .292 1.42l-1.43.613A2.68 2.68 0 0 0 5.5 6.992V8h5V6.5H7.108c.12-.26.331-.472.604-.588l1.43-.613A2.24 2.24 0 0 0 8.26 1zM2.75 6a1.5 1.5 0 0 1-1.5 1.5H1V9h.25c.546 0 1.059-.146 1.5-.401V11.5H1V13h5v-1.5H4.25V6zM10 12.85A2.15 2.15 0 0 0 12.15 15h.725a2.125 2.125 0 0 0 1.617-3.504 2.138 2.138 0 0 0-1.656-3.521l-.713.008A2.15 2.15 0 0 0 10 10.133v.284h1.5v-.284a.65.65 0 0 1 .642-.65l.712-.009a.638.638 0 1 1 .008 1.276H12v1.5h.875a.625.625 0 1 1 0 1.25h-.725a.65.65 0 0 1-.65-.65v-.267H10z"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="NumbersIcon";let c=i},6672:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M7.25 7.25V1h1.5v6.25H15v1.5H8.75V15h-1.5V8.75H1v-1.5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="PlusIcon";let c=i},40813:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M7.25 7.25V4.5h1.5v2.75h2.75v1.5H8.75v2.75h-1.5V8.75H4.5v-1.5z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1.75A.75.75 0 0 1 1.75 1h12.5a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75zm1.5.75v11h11v-11z",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="PlusSquareIcon";let c=i},72299:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M7.25 10.75a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0M10.079 7.111A2.25 2.25 0 1 0 5.75 6.25h1.5A.75.75 0 1 1 8 7a.75.75 0 0 0-.75.75V9h1.5v-.629a2.25 2.25 0 0 0 1.329-1.26"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="QuestionMarkIcon";let c=i},49716:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("g",{clipPath:"url(#SearchIcon_svg__a)",children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 1a7 7 0 1 0 4.39 12.453l2.55 2.55 1.06-1.06-2.55-2.55A7 7 0 0 0 8 1M2.5 8a5.5 5.5 0 1 1 11 0 5.5 5.5 0 0 1-11 0",clipRule:"evenodd"})}),(0,a.Y)("defs",{children:(0,a.Y)("clipPath",{children:(0,a.Y)("path",{fill:"#fff",d:"M0 0h16v16H0z"})})})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="SearchIcon";let c=i},16494:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"m11.5.94 4.03 4.03-1.06 1.06-2.22-2.22V10h-1.5V3.81L8.53 6.03 7.47 4.97zM1 4.5h4V6H1zM1 12.5h10V14H1zM8 8.5H1V10h7z"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="SortAscendingIcon";let c=i},62270:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 3.5h10V2H1zm0 8h4V10H1zm7-4H1V6h7zm3.5 7.56 4.03-4.03-1.06-1.06-2.22 2.22V6h-1.5v6.19L8.53 9.97l-1.06 1.06z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="SortDescendingIcon";let c=i},79670:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"M11.5.94 7.47 4.97l1.06 1.06 2.22-2.22v8.38L8.53 9.97l-1.06 1.06 4.03 4.03 4.03-4.03-1.06-1.06-2.22 2.22V3.81l2.22 2.22 1.06-1.06zM6 3.5H1V5h5zM6 11.5H1V13h5zM1 7.5h5V9H1z"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="SortUnsortedIcon";let c=i},88416:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M6 7a.75.75 0 1 1-1.5 0A.75.75 0 0 1 6 7M8 7.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5M10.75 7.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6 1a6 6 0 1 0 0 12v2.25a.75.75 0 0 0 1.28.53L10.061 13A6 6 0 0 0 10 1zM1.5 7A4.5 4.5 0 0 1 6 2.5h4a4.5 4.5 0 1 1 0 9h-.25a.75.75 0 0 0-.53.22L7.5 13.44v-1.19a.75.75 0 0 0-.75-.75H6A4.5 4.5 0 0 1 1.5 7",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="SpeechBubbleIcon";let c=i},17784:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.75 1a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75V1.75a.75.75 0 0 0-.75-.75zm.75 12.5v-11h11v11zM5 6h2.25v5.5h1.5V6H11V4.5H5z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="TextBoxIcon";let c=i},99145:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 1a3.25 3.25 0 1 0 0 6.5A3.25 3.25 0 0 0 8 1M6.25 4.25a1.75 1.75 0 1 1 3.5 0 1.75 1.75 0 0 1-3.5 0M8 9a8.74 8.74 0 0 0-6.836 3.287.75.75 0 0 0-.164.469v1.494c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-1.494a.75.75 0 0 0-.164-.469A8.74 8.74 0 0 0 8 9m-5.5 4.5v-.474A7.23 7.23 0 0 1 8 10.5c2.2 0 4.17.978 5.5 2.526v.474z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="UserIcon";let c=i},49393:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M8 1c.664 0 1.282.2 1.797.542l-.014.072-.062.357-.357.062c-.402.07-.765.245-1.06.493a1.75 1.75 0 1 0 0 3.447c.295.25.658.424 1.06.494l.357.062.062.357.014.072A3.25 3.25 0 1 1 8 1"}),(0,a.Y)("path",{fill:"currentColor",d:"M9.59 4.983A.75.75 0 0 1 9.62 3.51l.877-.152a.75.75 0 0 0 .61-.61l.153-.878a.75.75 0 0 1 1.478 0l.152.877a.75.75 0 0 0 .61.61l.878.153a.75.75 0 0 1 0 1.478l-.877.152a.75.75 0 0 0-.61.61l-.153.878a.75.75 0 0 1-1.478 0l-.152-.877a.75.75 0 0 0-.61-.61l-.878-.153z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1.164 12.287A8.74 8.74 0 0 1 8 9a8.74 8.74 0 0 1 6.836 3.287.75.75 0 0 1 .164.469v1.494a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75v-1.494a.75.75 0 0 1 .164-.469m1.336.74v.473h11v-.474A7.23 7.23 0 0 0 8 10.5c-2.2 0-4.17.978-5.5 2.526",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="UserSparkleIcon";let c=i},40631:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M14.367 3.29a.75.75 0 0 1 .547.443 5.001 5.001 0 0 1-6.072 6.736l-3.187 3.186a2.341 2.341 0 0 1-3.31-3.31L5.53 7.158a5.001 5.001 0 0 1 6.736-*********** 0 0 1 .237 1.22L10.5 4.312V5.5h1.19l2.003-2.004a.75.75 0 0 1 .674-.206m-.56 2.214L12.53 6.78A.75.75 0 0 1 12 7H9.75A.75.75 0 0 1 9 6.25V4a.75.75 0 0 1 .22-.53l1.275-1.276a3.501 3.501 0 0 0-3.407 4.865.75.75 0 0 1-.16.823l-3.523 3.523a.84.84 0 1 0 1.19 1.19L8.118 9.07a.75.75 0 0 1 .823-.16 3.5 3.5 0 0 0 4.865-3.407",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="WrenchIcon";let c=i},51211:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m1.97-4.97L8 9.06l-1.97 1.97-1.06-1.06L6.94 8 4.97 6.03l1.06-1.06L8 6.94l1.97-1.97 1.06 1.06L9.06 8l1.97 1.97z",clipRule:"evenodd"})})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="XCircleFillIcon";let c=i},84260:(e,o,r)=>{r.d(o,{A:()=>c});var t=r(65848),n=r(96277),a=r(98358);function l(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M6.94 8 4.97 6.03l1.06-1.06L8 6.94l1.97-1.97 1.06 1.06L9.06 8l1.97 1.97-1.06 1.06L8 9.06l-1.97 1.97-1.06-1.06z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8m8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13",clipRule:"evenodd"})]})}let i=(0,t.forwardRef)((e,o)=>(0,a.Y)(n.I,{ref:o,...e,component:l}));i.displayName="XCircleIcon";let c=i},48681:(e,o,r)=>{r.d(o,{p:()=>S,n:()=>x});var t=r(71218),n=r(22933),a=r(39659),l=r(80838),i=r(8467),c=r(94167),d=r(98358);let s=(e,o,r,n)=>{let a=`.${e}-input`,l=`.${e}-btn`;return(0,t.AH)({display:"inline-flex !important",width:"auto",[`& > ${a}`]:{flexGrow:1,...n&&{borderTopRightRadius:"0px !important",borderBottomRightRadius:"0px !important"},"&:disabled":{border:"none",background:o.colors.actionDisabledBackground,"&:hover":{borderRight:`1px solid ${o.colors.actionDisabledBorder} !important`}},"&[data-validation]":{marginRight:0}},...r&&{[`& > ${l}`]:{boxShadow:"none !important"}},...n&&{[`& > ${l}`]:{borderTopLeftRadius:"0px !important",borderBottomLeftRadius:"0px !important"}},[`& > ${l} > span`]:{verticalAlign:"middle"},[`& > ${l}:disabled, & > ${l}:disabled:hover`]:{borderLeft:`1px solid ${o.colors.actionDisabledBorder} !important`,backgroundColor:`${o.colors.actionDisabledBackground} !important`,color:`${o.colors.actionDisabledText} !important`}},"","")};var u=r(65848),g=r.n(u),p=r(46792),h=r(8161),f=r(84437),m=r(69391),v=r(58829),b=r(29121);let y=e=>{let o=(0,u.useRef)(!0),r=(0,u.useCallback)(()=>{o.current=!0},[]);return{callbackOnceUntilReset:(0,u.useCallback)(()=>{o.current&&(e(),o.current=!1)},[e]),reset:r}},w=e=>{let{handlers:o,stopOnDefaultPrevented:r}=e;return(0,u.useCallback)(e=>{for(let t of o){if(r&&e.defaultPrevented)return;null==t||t(e)}},[o,r])},x=(e,o,r,t)=>{let{validationState:n,type:l,hasValue:i,useNewShadows:c,useNewFormUISpacing:d,useNewBorderRadii:s,locked:u}=r,{useFocusWithin:g=!1}=t,p=`.${e}-input`,h=`.${e}-input-affix-wrapper`,f=`.${e}-input-affix-wrapper-disabled`,v=`.${e}-input-affix-wrapper-focused`,b=`.${e}-input-clear-icon`,y=`.${e}-input-prefix`,w=`.${e}-input-suffix`,x=(0,m.yu)(o,n),C=g?"focus-within":"focus";return{"&&":{lineHeight:o.typography.lineHeightBase,minHeight:o.general.heightSm,borderColor:o.colors.actionDefaultBorderDefault,...n&&{borderColor:x},"&:hover":{borderColor:n?x:o.colors.actionPrimaryBackgroundHover},[`&:${C}`]:{outlineColor:n?x:o.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",...!c&&{boxShadow:"none"},borderColor:"transparent"},"&:focus-visible":{outlineColor:n?x:o.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",...!c&&{boxShadow:"none"},borderColor:"transparent"},...d&&{[`& + .${e}-form-message`]:{marginTop:o.spacing.sm}}},[`&${p}, ${p}`]:{backgroundColor:"transparent",...s&&{borderRadius:o.borders.borderRadiusSm},"&:disabled":{backgroundColor:o.colors.actionDisabledBackground,color:o.colors.actionDisabledText,borderColor:o.colors.actionDisabledBorder},"&::placeholder":{color:o.colors.textPlaceholder}},[`&${h}`]:{backgroundColor:"transparent",lineHeight:o.typography.lineHeightBase,paddingTop:5,paddingBottom:5,minHeight:o.general.heightSm,"::before":{lineHeight:o.typography.lineHeightBase},"&:hover":{borderColor:n?x:o.colors.actionPrimaryBackgroundHover},[`input.${e}-input`]:{borderRadius:0}},[`&${f}`]:{backgroundColor:o.colors.actionDisabledBackground},[`&${v}`]:{boxShadow:"none",[`&&, &:${C}`]:{outlineColor:n?x:o.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",boxShadow:"none",borderColor:"transparent"}},[b]:{fontSize:o.typography.fontSizeSm},[y]:{marginRight:o.spacing.sm,color:o.colors.textSecondary},[w]:{marginLeft:o.spacing.sm,color:o.colors.textSecondary,...!i&&"number"===l&&{display:"none"}},...u?{[`&${h}`]:{backgroundColor:o.colors.backgroundSecondary},[`input.${e}-input`]:{backgroundColor:`${o.colors.backgroundSecondary} !important`,color:`${o.colors.textPrimary} !important`}}:{},...(0,a.Cx)(o.options.enableAnimation)}},C=(e,o,r)=>{let{validationState:n,type:a,hasValue:l,useNewShadows:i,useNewBorderRadii:c,locked:d}=r,s=x(e,o,{validationState:n,type:a,hasValue:l,useNewShadows:i,useNewBorderRadii:c,locked:d},{});return(0,t.AH)((0,m.dg)(s),"","")},k=(0,u.forwardRef)(function(e,o){let{validationState:r,autoComplete:t="off",dangerouslySetAntdProps:s,dangerouslyAppendEmotionCSS:m,onChange:x,onClear:k,onFocus:D,onPressEnter:S,onCompositionStart:B,onCompositionEnd:H,componentId:A,shouldPreventFormSubmission:$,analyticsEvents:T,readOnly:R,locked:Y,...P}=e,I=(0,v.W)("databricks.fe.observability.defaultComponentView.input",!1),z=(0,p.xW)(),{classNamePrefix:V,theme:M}=(0,l.wn)(),[L,O]=g().useState(void 0!==P.value&&null!==P.value&&""!==P.value),{useNewShadows:_,useNewBorderRadii:F}=(0,i.p)(),W=(0,u.useMemo)(()=>null!=T?T:I?[h.s7.OnValueChange,h.s7.OnView]:[h.s7.OnValueChange],[T,I]),E=(0,h.ei)({componentType:h.v_.Input,componentId:A,analyticsEvents:W,valueHasNoPii:!1}),N=(0,u.useRef)(!0);(0,u.useEffect)(()=>{N.current&&(E.onView(),N.current=!1)},[E]);let{callbackOnceUntilReset:G,reset:U}=y(E.onValueChange),K=(0,u.useCallback)(e=>{G(),!e.target.value&&e.nativeEvent instanceof InputEvent==!1&&k?(null==k||k(),O(!1)):(null==x||x(e),O(!!e.target.value))},[x,k,G]),q=(0,u.useCallback)(e=>{U(),null==D||D(e)},[D,U]),j=(0,u.useCallback)(e=>{var o;if(!(null!==(o=z.formRef)&&void 0!==o&&o.current))return;e.preventDefault(),z.formRef.current.requestSubmit()},[z.formRef]),X=(0,b.s)({callback:j,allowBasicEnter:!$,allowPlatformEnter:!$}),Q=w((0,u.useMemo)(()=>({handlers:[X.onKeyDown,S],stopOnDefaultPrevented:!1}),[X.onKeyDown,S])),Z=w((0,u.useMemo)(()=>({handlers:[X.onCompositionStart,B]}),[X.onCompositionStart,B])),J=w((0,u.useMemo)(()=>({handlers:[X.onCompositionEnd,H]}),[X.onCompositionEnd,H]));return(0,d.Y)(a.wC,{children:(0,d.Y)(n.A,{...(0,c.VG)(),autoComplete:t,"data-validation":r,ref:o,css:[C(V,M,{validationState:r,type:P.type,hasValue:L,useNewShadows:_,useNewBorderRadii:F,locked:Y}),m,"",""],onChange:K,onFocus:q,onPressEnter:Q,onCompositionStart:Z,onCompositionEnd:J,...P,readOnly:Y||R,suffix:Y?(0,d.Y)(f.A,{}):P.suffix,...s,...E.dataComponentProps})})}),D=(0,u.forwardRef)(function(e,o){let{validationState:r,autoComplete:t="off",dangerouslySetAntdProps:s,dangerouslyAppendEmotionCSS:u,...g}=e,{classNamePrefix:p,theme:h}=(0,l.wn)(),{useNewShadows:f,useNewBorderRadii:m}=(0,i.p)();return(0,d.Y)(a.wC,{children:(0,d.Y)(n.A.Password,{...(0,c.VG)(),visibilityToggle:!1,ref:o,autoComplete:t,css:[C(p,h,{validationState:r,useNewShadows:f,useNewBorderRadii:m}),u,"",""],...g,...s})})}),S=Object.assign(k,{TextArea:(0,u.forwardRef)(function(e,o){let{validationState:r,autoComplete:t="off",dangerouslySetAntdProps:s,dangerouslyAppendEmotionCSS:g,componentId:f,analyticsEvents:m,onChange:x,onFocus:k,onKeyDown:D,onCompositionStart:S,onCompositionEnd:B,allowFormSubmitOnEnter:H=!1,...A}=e,$=(0,v.W)("databricks.fe.observability.defaultComponentView.textArea",!1),T=(0,p.xW)(),{classNamePrefix:R,theme:Y}=(0,l.wn)(),{useNewShadows:P,useNewBorderRadii:I}=(0,i.p)(),z=(0,u.useMemo)(()=>null!=m?m:$?[h.s7.OnValueChange,h.s7.OnView]:[h.s7.OnValueChange],[m,$]),V=(0,h.ei)({componentType:h.v_.TextArea,componentId:f,analyticsEvents:z,valueHasNoPii:!1}),M=(0,u.useRef)(!0);(0,u.useEffect)(()=>{M.current&&(V.onView(),M.current=!1)},[V]);let{callbackOnceUntilReset:L,reset:O}=y(V.onValueChange),_=(0,u.useCallback)(e=>{L(),null==x||x(e)},[x,L]),F=(0,u.useCallback)(e=>{O(),null==k||k(e)},[k,O]),W=(0,u.useCallback)(e=>{var o;if(!(null!==(o=T.formRef)&&void 0!==o&&o.current))return;e.preventDefault(),T.formRef.current.requestSubmit()},[T.formRef]),E=(0,b.s)({callback:W,allowBasicEnter:H,allowPlatformEnter:!0}),N=w((0,u.useMemo)(()=>({handlers:[E.onKeyDown,D],stopOnDefaultPrevented:!0}),[D,E.onKeyDown])),G=w((0,u.useMemo)(()=>({handlers:[E.onCompositionStart,S]}),[E.onCompositionStart,S])),U=w((0,u.useMemo)(()=>({handlers:[E.onCompositionEnd,B]}),[E.onCompositionEnd,B]));return(0,d.Y)(a.wC,{children:(0,d.Y)(n.A.TextArea,{...(0,c.VG)(),ref:o,autoComplete:t,css:[C(R,Y,{validationState:r,useNewShadows:P,useNewBorderRadii:I}),g,"",""],onChange:_,onFocus:F,onKeyDown:N,onCompositionStart:G,onCompositionEnd:U,"data-component-type":h.v_.TextArea,"data-component-id":f,...A,...s})})}),Password:D,Group:e=>{let{dangerouslySetAntdProps:o,dangerouslyAppendEmotionCSS:r,compact:t=!0,...u}=e,{classNamePrefix:g,theme:p}=(0,l.wn)(),{useNewShadows:h,useNewBorderRadii:f}=(0,i.p)();return(0,d.Y)(a.wC,{children:(0,d.Y)(n.A.Group,{...(0,c.VG)(),css:[s(g,p,h,f),r,"",""],compact:t,...u,...o})})}})},13177:(e,o,r)=>{r.d(o,{AG:()=>S,VB:()=>C,_v:()=>B,c4:()=>D,r0:()=>k});var t=r(71218),n=r(48959),a=r(25656),l=r.n(a),i=r(65848),c=r(39659),d=r(80838),s=r(4700),u=r(51211),g=r(5844),p=r(80061),h=r(23240),f=r(14687),m=r(8467),v=r(69391),b=r(94167),y=r(98358);let w=(e,o)=>(0,v.dg)({fontSize:null!=o?o:e.general.iconFontSize});function x(e,o){let{children:r,validationState:a,loading:x,loadingDescription:C="Select",mode:D,options:S,notFoundContent:B,optionFilterProp:H,dangerouslySetAntdProps:A,virtual:$,dropdownClassName:T,id:R,onDropdownVisibleChange:Y,maxHeight:P,...I}=e,{theme:z,getPrefixedClassName:V}=(0,d.wn)(),{useNewShadows:M,useNewFormUISpacing:L}=(0,m.p)(),O=V("select"),[_,F]=(0,i.useState)(!1),[W,E]=(0,i.useState)(""),N=null!=P?P:8.5*z.general.heightSm;return(0,i.useEffect)(()=>{E(R||l().uniqueId("dubois-select-"))},[R]),(0,i.useEffect)(()=>{var e;null===(e=document.getElementById(W))||void 0===e||e.setAttribute("aria-expanded","false")},[W]),(0,y.Y)(t.Z2,{children:e=>{let{css:l}=e;return(0,y.FD)(c.wC,{children:[x&&(0,y.Y)(f.G,{description:C}),(0,y.Y)(n.A,{onDropdownVisibleChange:e=>{null==Y||Y(e),F(e)},..._?{}:{"aria-owns":void 0,"aria-controls":void 0,"aria-activedescendant":void 0},id:W,css:function(e){let{clsPrefix:o,theme:r,validationState:n,useNewFormUISpacing:a}=e,l=`.${o}-focused`,i=`.${o}-open`,d=`.${o}-single`,s=`.${o}-selector`,u=`.${o}-disabled`,g=`.${o}-multiple`,p=`.${o}-selection-item`,h=`.${o}-selection-overflow`,f=`.${o}-selection-overflow-item`,m=`.${o}-selection-overflow-item-suffix`,y=`.${o}-arrow`,w=`.${o}-arrow-loading`,x=`.${o}-selection-placeholder`,C=`.${o}-selection-item-remove`,k=`.${o}-selection-search`,D=`.${o}-show-search`,S=`.${o}-clear`,B=`.${o}-allow-clear`,H=`.${o}-selection-search-input`,A=`.${o.replace("-select","")}-form-message`,$=(0,v.yu)(r,n),T={...(0,b.De)(r),...a&&{[`& + ${A}`]:{marginTop:r.spacing.sm}},"&:hover":{[s]:{borderColor:r.colors.actionDefaultBorderHover}},[s]:{paddingLeft:12,paddingRight:0,color:r.colors.textPrimary,backgroundColor:"transparent",height:r.general.heightSm,"::after":{lineHeight:r.typography.lineHeightBase},"::before":{lineHeight:r.typography.lineHeightBase}},[d]:{[`&${s}`]:{height:r.general.heightSm}},[p]:{color:r.colors.textPrimary,paddingRight:32,lineHeight:r.typography.lineHeightBase,paddingTop:5,paddingBottom:5},[k]:{right:24,left:8,marginInlineStart:4,[H]:{color:r.colors.actionDefaultTextDefault,height:24}},[`&${d}`]:{[H]:{height:r.general.heightSm}},[`&${D}${i}${d}`]:{[p]:{color:r.colors.actionDisabledText}},[S]:{right:24,backgroundColor:"transparent"},[`&${l}`]:{[s]:{outlineColor:r.colors.actionDefaultBorderFocus,outlineWidth:2,outlineOffset:-2,outlineStyle:"solid",borderColor:"transparent",boxShadow:"none"}},[`&${u}`]:{[s]:{backgroundColor:r.colors.actionDisabledBackground,color:r.colors.actionDisabledText,border:"transparent"},[p]:{color:r.colors.actionDisabledText},[y]:{color:r.colors.actionDisabledText}},[y]:{height:r.general.iconFontSize,width:r.general.iconFontSize,top:(r.general.heightSm-r.general.iconFontSize)/2,marginTop:0,color:r.colors.textSecondary,fontSize:r.general.iconFontSize,".anticon":{pointerEvents:"none"},[`&${w}`]:{top:(r.general.heightSm-r.general.iconFontSize)/2,display:"flex",alignItems:"center",justifyContent:"center",fontSize:r.general.iconFontSize}},[x]:{color:r.colors.textPlaceholder,right:"auto",left:"auto",width:"100%",paddingRight:32,lineHeight:r.typography.lineHeightBase,alignSelf:"center"},[`&${g}`]:{[s]:{paddingTop:3,paddingBottom:3,paddingLeft:8,paddingRight:30,minHeight:r.general.heightSm,height:"auto","&::after":{margin:0}},[p]:{backgroundColor:r.colors.tagDefault,color:r.colors.textPrimary,border:"none",height:20,lineHeight:r.typography.lineHeightBase,fontSize:r.typography.fontSizeBase,marginInlineEnd:4,marginTop:2,marginBottom:2,paddingRight:0,paddingTop:0,paddingBottom:0},[h]:{minHeight:24},[f]:{alignSelf:"auto",height:24,lineHeight:r.typography.lineHeightBase},[k]:{marginTop:0,left:0,right:0},[`&${u}`]:{[p]:{paddingRight:2}},[y]:{top:(r.general.heightSm-r.general.iconFontSize)/2},[`&${B}`]:{[S]:{top:(r.general.heightSm-r.general.iconFontSize+4)/2}},[x]:{paddingLeft:4,color:r.colors.textPlaceholder},[`&:not(${l})`]:{[m]:{height:0}}},[`&${g}${u}`]:{[p]:{color:r.colors.actionDisabledText}},[`&${B}`]:{[p]:{paddingRight:0},[s]:{paddingRight:52},[S]:{top:(r.general.heightSm-r.general.iconFontSize+4)/2,opacity:100,width:r.general.iconFontSize,height:r.general.iconFontSize,marginTop:0}},[C]:{color:r.colors.textPrimary,borderTopRightRadius:r.legacyBorders.borderRadiusMd,borderBottomRightRadius:r.legacyBorders.borderRadiusMd,height:r.general.iconFontSize,width:r.general.iconFontSize,lineHeight:r.typography.lineHeightBase,paddingInlineEnd:0,marginInlineEnd:0,"& > .anticon":{height:r.general.iconFontSize-4,fontSize:r.general.iconFontSize-4},"&:hover":{color:r.colors.actionTertiaryTextHover,backgroundColor:r.colors.tagHover},"&:active":{color:r.colors.actionTertiaryTextPress,backgroundColor:r.colors.tagPress}},...n&&{[`& > ${s}`]:{borderColor:$,"&:hover":{borderColor:$}},[`&${l} > ${s}`]:{outlineColor:$,outlineOffset:-2}},...(0,c.Cx)(r.options.enableAnimation)},R=(0,v.dg)(T);return(0,t.AH)(R,"","")}({clsPrefix:O,theme:z,validationState:a,useNewFormUISpacing:L}),removeIcon:(0,y.Y)(s.A,{"aria-hidden":"false",css:w(z)}),clearIcon:(0,y.Y)(u.A,{"aria-hidden":"false",css:w(z,12),"aria-label":"close-circle"}),ref:o,suffixIcon:x&&"tags"===D?(0,y.Y)(g.A,{spin:!0,"aria-label":"loading","aria-hidden":"false",css:w(z,12)}):(0,y.Y)(p.A,{css:w(z)}),menuItemSelectedIcon:(0,y.Y)(h.A,{css:w(z)}),showArrow:!0,dropdownMatchSelectWidth:!0,notFoundContent:null!=B?B:(0,y.Y)("div",{css:(0,t.AH)({color:z.colors.textSecondary,textAlign:"center"},"",""),children:"No results found"}),dropdownClassName:l([function(e,o,r){let n=`.${e}-item-option`,a=`.${e}-item-option-active`,l=`.${e}-item-option-selected`,i=`.${e}-item-option-state`,d={borderColor:o.colors.borderDecorative,borderWidth:1,borderStyle:"solid",zIndex:o.options.zIndexBase+50,boxShadow:o.general.shadowLow,...(0,b.De)(o),[n]:{height:o.general.heightSm},[a]:{backgroundColor:o.colors.actionTertiaryBackgroundHover,height:o.general.heightSm,"&:hover":{backgroundColor:o.colors.actionTertiaryBackgroundHover}},[l]:{backgroundColor:o.colors.actionTertiaryBackgroundHover,fontWeight:"normal","&:hover":{backgroundColor:o.colors.actionTertiaryBackgroundHover}},[i]:{color:o.colors.textSecondary,"& > span":{verticalAlign:"middle"}},[`.${e}-loading-options`]:{pointerEvents:"none",margin:"0 auto",height:o.general.heightSm,display:"block"},...(0,c.Cx)(o.options.enableAnimation),...(0,v.WO)(o,r)},s=(0,v.dg)(d);return(0,t.AH)(s,"","")}(O,z,M),T]),listHeight:N,maxTagPlaceholder:e=>`+ ${e.length} more`,mode:D,options:S,loading:x,filterOption:!0,virtual:null!=$?$:r&&Array.isArray(r)&&8!==r.length||S&&8!==S.length||!r&&!S,optionFilterProp:null!=H?H:"children",...I,...A,children:x&&"tags"!==D?(0,y.FD)(y.FK,{children:[r,(0,y.Y)(k,{disabled:!0,value:"select-loading-options",className:`${O}-loading-options`,children:(0,y.Y)(g.A,{"aria-hidden":"false",spin:!0,css:(0,t.AH)({fontSize:20,color:z.colors.textSecondary,lineHeight:"20px"},"",""),"aria-label":"loading"})})]}):r})]})}})}let C=(0,i.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,...t}=e;return(0,y.Y)(n.A.Option,{...t,ref:o,...r})});C.isSelectOption=!0;let k=C,D=(()=>{let e=(0,i.forwardRef)(function(e,o){return(0,y.Y)(n.A.OptGroup,{...e,isSelectOptGroup:!0,ref:o})});return e.isSelectOptGroup=!0,e})(),S=D,B=(()=>{let e=(0,i.forwardRef)(x);return e.Option=C,e.OptGroup=D,e})()},80333:(e,o,r)=>{r.d(o,{p:()=>h});var t=r(71218),n=r(59859),a=r(25656),l=r(65848),i=r.n(l),c=r(39659),d=r(80838),s=r(8467),u=r(69391),g=r(60499),p=r(98358);let h=e=>{let{children:o,title:r,placement:h="top",dataTestId:f,dangerouslySetAntdProps:m,silenceScreenReader:v=!1,useAsLabel:b=!1,...y}=e,{theme:w}=(0,d.wn)(),{useNewShadows:x}=(0,s.p)(),C=(0,l.useRef)(null),k=(0,g.Y)("dubois-tooltip-component-"),D=null!=m&&m.id?null==m?void 0:m.id:k;if(!r)return(0,p.Y)(i().Fragment,{children:o});let S=v?{}:{"aria-live":"polite","aria-relevant":"additions"};f&&(S["data-testid"]=f);let B=r&&i().isValidElement(r)?i().cloneElement(r,S):(0,p.Y)("span",{...S,children:r}),H={"aria-hidden":!1},A=e=>{if(!C.current||e.currentTarget.hasAttribute("aria-describedby")||e.currentTarget.hasAttribute("aria-labelledby"))return;D&&(e.currentTarget.setAttribute("aria-live","polite"),b?e.currentTarget.setAttribute("aria-labelledby",D):e.currentTarget.setAttribute("aria-describedby",D))},$=e=>{if(!C||!e.currentTarget.hasAttribute("aria-describedby")&&!e.currentTarget.hasAttribute("aria-labelledby"))return;b?e.currentTarget.removeAttribute("aria-labelledby"):e.currentTarget.removeAttribute("aria-describedby"),e.currentTarget.removeAttribute("aria-live")},T={onMouseEnter:e=>{A(e)},onMouseLeave:e=>{$(e)},onFocus:e=>{A(e)},onBlur:e=>{$(e)}},R=i().isValidElement(o)?i().cloneElement(o,{...H,...T,...o.props}):(0,a.isNil)(o)?o:(0,p.Y)("span",{...H,...T,children:o}),{overlayInnerStyle:Y,overlayStyle:P,...I}=m||{};return(0,p.Y)(c.wC,{children:(0,p.Y)(n.A,{id:D,ref:C,title:B,placement:h,trigger:["hover","focus"],overlayInnerStyle:{backgroundColor:"#2F3941",lineHeight:"22px",padding:"4px 8px",boxShadow:w.general.shadowLow,...Y,...(0,u.WO)(w,x)},overlayStyle:{zIndex:w.options.zIndexBase+70,...P},css:(0,t.AH)({...(0,c.Cx)(w.options.enableAnimation)},"",""),...I,...y,children:R})})}},14687:(e,o,r)=>{r.d(o,{G:()=>i,B:()=>l});var t=r(65848);let n=Symbol("NOT_INITIALIZED"),a=0,l=(0,t.createContext)(null),i=e=>{let{description:o="Generic UI loading state"}=e,r=function(e){let o=(0,t.useRef)(n);if(o.current===n){let r=e();return o.current=r,r}return o.current}(()=>a++),i=(0,t.useContext)(l);return(0,t.useLayoutEffect)(()=>(i&&i.startLoading(r,o),()=>{i&&i.endLoading(r)}),[r,o,i]),null}},49817:(e,o,r)=>{r.d(o,{ZF:()=>$,aF:()=>S,k3:()=>w});var t=r(71218),n=r(87685),a=r(65848),l=r(27443),i=r(8161),c=r(47088),d=r(39659),s=r(80838),u=r(4700),g=r(89481),p=r(69391),h=r(8467),f=r(94167),m=r(58829),v=r(80842),b=r(98358);let y=(0,a.createContext)({isInsideModal:!0}),w=()=>(0,a.useContext)(y),x={normal:640,wide:880},C=e=>{let{theme:o,clsPrefix:r,hasFooter:n=!0,maxedOutHeight:a,useNewShadows:l,useNewBorderRadii:i}=e,c=`.${r}-modal-close`,s=`.${r}-modal-close-x`,u=`.${r}-modal-title`,g=`.${r}-modal-content`,h=`.${r}-modal-body`,m=`.${r}-modal-header`,v=`.${r}-modal-footer`,b=`.${r}-btn`,y=`.${r}-dropdown-button`,w=o.spacing.lg,x=o.general.heightSm,C="90vh",k=n?52:0,D=`calc(${C} - 64px - ${k}px - ${w}px)`;return(0,t.AH)({"&&":{...(0,f.De)(o)},[m]:{background:"transparent",paddingTop:o.spacing.md,paddingLeft:o.spacing.lg,paddingRight:o.spacing.md,paddingBottom:o.spacing.md},[v]:{height:k,paddingTop:o.spacing.lg-8,paddingLeft:w,paddingRight:w,marginTop:"auto",[`${b} + ${b}`]:{marginLeft:o.spacing.sm},[`${y} > ${b}:nth-of-type(2)`]:{marginLeft:-1}},[s]:{fontSize:o.general.iconSize,height:x,width:x,lineHeight:"normal",display:"flex",alignItems:"center",justifyContent:"center",color:o.colors.textSecondary},[c]:{height:x,width:x,margin:"16px 16px 0 0",borderRadius:i?o.borders.borderRadiusSm:o.legacyBorders.borderRadiusMd,backgroundColor:o.colors.actionDefaultBackgroundDefault,borderColor:o.colors.actionDefaultBackgroundDefault,color:o.colors.actionDefaultTextDefault,"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover,borderColor:o.colors.actionDefaultBackgroundHover,color:o.colors.actionDefaultTextHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress,borderColor:o.colors.actionDefaultBackgroundPress,color:o.colors.actionDefaultTextPress},"&:focus-visible":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:o.colors.actionDefaultBorderFocus}},[u]:{fontSize:o.typography.fontSizeXl,lineHeight:o.typography.lineHeightXl,fontWeight:o.typography.typographyBoldFontWeight,paddingRight:w,minHeight:32,display:"flex",alignItems:"center",overflowWrap:"anywhere"},[g]:{backgroundColor:o.colors.backgroundPrimary,maxHeight:C,height:a?C:"",overflow:"hidden",paddingBottom:w,display:"flex",flexDirection:"column",boxShadow:l?o.shadows.xl:o.general.shadowHigh,...i&&{borderRadius:o.borders.borderRadiusLg},...(0,p.WO)(o,l)},[h]:{overflowY:"auto",maxHeight:D,paddingLeft:w,paddingRight:w,paddingTop:8,paddingBottom:8,...(0,p.Ud)(o)},...(0,d.Cx)(o.options.enableAnimation)},"","")};function k(e){return e?`${e}.footer.cancel`:"codegen_design-system_src_design-system_modal_modal.tsx_260"}function D(e){let{componentId:o,onOk:r,onCancel:t,confirmLoading:n,okText:a,cancelText:i,okButtonProps:c,cancelButtonProps:d,autoFocusButton:s,shouldStartInteraction:u}=e;return(0,b.FD)(b.FK,{children:[i&&(0,b.Y)(l.$n,{componentId:k(o),onClick:e=>{null==t||t(e)},autoFocus:"cancel"===s,dangerouslyUseFocusPseudoClass:!0,shouldStartInteraction:u,...d,children:i}),a&&(0,b.Y)(l.$n,{componentId:o?`${o}.footer.ok`:"codegen_design-system_src_design-system_modal_modal.tsx_271",loading:n,onClick:e=>{null==r||r(e)},type:"primary",autoFocus:"ok"===s,dangerouslyUseFocusPseudoClass:!0,shouldStartInteraction:u,...c,children:a})]})}function S(e){return(0,b.Y)(c.vR.Provider,{value:c.pz,children:(0,b.Y)(B,{...e})})}function B(e){let{componentId:o,analyticsEvents:r=[i.s7.OnView],okButtonProps:l,cancelButtonProps:c,dangerouslySetAntdProps:g,children:p,title:w,footer:S,size:B="normal",verticalSizing:H="dynamic",autoFocusButton:A,truncateTitle:$,shouldStartInteraction:T,...R}=e,{theme:Y,classNamePrefix:P}=(0,s.wn)(),{useNewShadows:I,useNewBorderRadii:z}=(0,h.p)(),V=(0,a.useMemo)(()=>r,[r]),M=(0,i.ei)({componentType:i.v_.Modal,componentId:o,analyticsEvents:V,shouldStartInteraction:T}),{elementRef:L}=(0,v.z)({onView:M.onView}),O=(null==g?void 0:g.closable)===!1,_=(0,a.useRef)(!1);(0,a.useEffect)(()=>{O&&!_.current&&!0===R.visible&&(_.current=!0,M.onView())},[R.visible,O,M]);let F=(0,i.ei)({componentType:i.v_.Button,componentId:k(o),analyticsEvents:[i.s7.OnClick],shouldStartInteraction:T}),W=(0,m.W)("databricks.fe.observability.enableModalDataComponentProps",!1)?(0,i.PS)(p,M.dataComponentProps):p;return(0,b.Y)(d.wC,{children:(0,b.Y)(n.A,{...(0,f.VG)(),css:C({theme:Y,clsPrefix:P,hasFooter:null!==S,maxedOutHeight:"maxed_out"===H,useNewShadows:I,useNewBorderRadii:z}),title:(0,b.Y)(d.dg,{children:$?(0,b.Y)("div",{css:(0,t.AH)({textOverflow:"ellipsis",marginRight:Y.spacing.md,overflow:"hidden",whiteSpace:"nowrap"},"",""),title:"string"==typeof w?w:void 0,children:w}):w}),footer:null===S?null:(0,b.Y)(d.dg,{children:void 0===S?(0,b.Y)(D,{componentId:o,onOk:R.onOk,onCancel:R.onCancel,confirmLoading:R.confirmLoading,okText:R.okText,cancelText:R.cancelText,okButtonProps:l,cancelButtonProps:c,autoFocusButton:A,shouldStartInteraction:T}):S}),width:B?x[B]:void 0,closeIcon:(0,b.Y)(u.A,{ref:L}),centered:!0,zIndex:Y.options.zIndexBase,maskStyle:{backgroundColor:Y.colors.overlayOverlay},...R,onCancel:e=>{var o;F.onClick(e),null===(o=R.onCancel)||void 0===o||o.call(R,e)},...g,children:(0,b.Y)(d.dg,{children:(0,b.Y)(y.Provider,{value:{isInsideModal:!0},children:W})})})})}var H={name:"b9hrb",styles:"position:relative;display:inline-flex;align-items:center"},A={name:"1o6wc9k",styles:"padding-left:6px"};function $(e){let{theme:o}=(0,s.wn)(),{title:r,onCancel:n,onOk:a,cancelText:i,okText:c,okButtonProps:d,cancelButtonProps:u,...p}=e,h=(0,b.FD)("div",{css:H,children:[(0,b.Y)(g.A,{css:(0,t.AH)({color:o.colors.textValidationDanger,left:2,height:18,width:18,fontSize:18},"","")}),(0,b.Y)("div",{css:A,children:r})]});return(0,b.Y)(S,{shouldStartInteraction:e.shouldStartInteraction,title:h,footer:[(0,b.Y)(l.$n,{componentId:e.componentId?`${e.componentId}.danger.footer.cancel`:"codegen_design-system_src_design-system_modal_modal.tsx_386",onClick:n,shouldStartInteraction:e.shouldStartInteraction,...u,children:i||"Cancel"},"cancel"),(0,b.Y)(l.$n,{componentId:e.componentId?`${e.componentId}.danger.footer.ok`:"codegen_design-system_src_design-system_modal_modal.tsx_395",type:"primary",danger:!0,onClick:a,loading:e.confirmLoading,shouldStartInteraction:e.shouldStartInteraction,...d,children:c||"Delete"},"discard")],onOk:a,onCancel:n,...p})}},89815:(e,o,r)=>{r.d(o,{dK:()=>x,mB:()=>w,vI:()=>k});var t=r(71218),n=r(81434),a=r(62260),l=r(65848),i=r(27443),c=r(8161),d=r(39659),s=r(80838),u=r(16306),g=r(17061),p=r(13177),h=r(8467),f=r(80842),m=r(69391),v=r(94167),b=r(58829),y=r(98358);function w(e,o,r){let n=`.${e}-pagination`,a=`.${e}-pagination-item`,l=`.${e}-pagination-item-link`,i=`.${e}-pagination-item-active`,c=`.${e}-pagination-item-ellipsis`,d=`.${e}-pagination-next`,s=`.${e}-pagination-prev`,u=`.${e}-pagination-jump-next`,g=`.${e}-pagination-jump-prev`,p=`.${e}-pagination-options-size-changer`,h=`.${e}-pagination-options`,f=`.${e}-pagination-disabled`,v=`.${e}-select-selector`,b={"span[role=img]":{color:o.colors.textSecondary,"> *":{color:"inherit"}},[a]:{backgroundColor:"none",border:"none",color:o.colors.textSecondary,"&:focus-visible":{outline:"auto"},"> a":{color:o.colors.textSecondary,textDecoration:"none","&:hover":{color:o.colors.actionDefaultTextHover},"&:active":{color:o.colors.actionDefaultTextPress}},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress}},[i]:{backgroundColor:o.colors.actionDefaultBackgroundPress,color:o.colors.actionDefaultTextPress,border:"none","> a":{color:o.colors.actionDefaultTextPress},"&:focus-visible":{outline:"auto"},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundPress,color:o.colors.actionDefaultTextPress}},[l]:{border:"none",color:o.colors.textSecondary,"&[disabled]":{display:"none"},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress},"&:focus-visible":{outline:"auto"}},[c]:{color:"inherit"},[`${d}, ${s}, ${u}, ${g}`]:{color:o.colors.textSecondary,"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover},"&:active":{backgroundColor:o.colors.actionDefaultBackgroundPress},"&:focus-visible":{outline:"auto"},[`&${f}`]:{pointerEvents:"none"}},[`&${n}.mini, ${n}.mini`]:{[`${a}, ${d}, ${s}, ${u}, ${g}`]:{height:"32px",minWidth:"32px",width:"auto",lineHeight:"32px"},[p]:{marginLeft:4},[`input,  ${h}`]:{height:"32px"},...r&&{[`${v}`]:{boxShadow:o.shadows.xs}}}},y=(0,m.dg)(b);return(0,t.AH)(y,"","")}let x=function(e){let{currentPageIndex:o,pageSize:r=10,numTotal:t,onChange:i,style:u,hideOnSinglePage:g,dangerouslySetAntdProps:p,componentId:m,analyticsEvents:x}=e,C=(0,b.W)("databricks.fe.observability.defaultComponentView.pagination",!1),{classNamePrefix:k,theme:D}=(0,s.wn)(),{useNewShadows:S}=(0,h.p)(),{pageSizeSelectAriaLabel:B,pageQuickJumperAriaLabel:H,...A}=null!=p?p:{},$=(0,l.useRef)(null),T=(0,l.useMemo)(()=>null!=x?x:C?[c.s7.OnValueChange,c.s7.OnView]:[c.s7.OnValueChange],[x,C]),R=(0,c.ei)({componentType:c.v_.Pagination,componentId:m,analyticsEvents:T,valueHasNoPii:!0}),Y=(0,l.useCallback)((e,o)=>{R.onValueChange(e),i(e,o)},[R,i]),{elementRef:P}=(0,f.z)({onView:R.onView}),I=(0,n.SV)([$,P]);return(0,l.useEffect)(()=>{if($&&$.current){let e=$.current.querySelector(`.${k}-select-selection-search-input`);e&&e.setAttribute("aria-label",null!=B?B:"Select page size");let o=$.current.querySelector(`.${k}-pagination-options-quick-jumper > input`);o&&o.setAttribute("aria-label",null!=H?H:"Go to page")}},[H,B,k]),(0,y.Y)(d.wC,{children:(0,y.Y)("div",{ref:I,children:(0,y.Y)(a.A,{...(0,v.VG)(),css:w(k,D,S),current:o,pageSize:r,responsive:!1,total:t,onChange:Y,showSizeChanger:!1,showQuickJumper:!1,size:"small",style:u,hideOnSinglePage:g,...A,...R.dataComponentProps})})})};var C={name:"1u1zie3",styles:"width:120px"};let k=function(e){let{onNextPage:o,onPreviousPage:r,hasNextPage:n,hasPreviousPage:a,nextPageText:d="Next",previousPageText:h="Previous",pageSizeSelect:{options:f,default:m,getOptionText:v,onChange:b,ariaLabel:w="Select page size"}={},componentId:x="design_system.cursor_pagination",analyticsEvents:k=[c.s7.OnValueChange],valueHasNoPii:D}=e,{theme:S,classNamePrefix:B}=(0,s.wn)(),[H,A]=(0,l.useState)(m),$=(0,l.useMemo)(()=>k,[k]),T=`${x}.page_size`,R=(0,c.ei)({componentType:c.v_.LegacySelect,componentId:T,analyticsEvents:$,valueHasNoPii:D}),Y=e=>`${e} / page`;return(0,y.FD)("div",{css:(0,t.AH)({display:"flex",flexDirection:"row",gap:S.spacing.sm,[`.${B}-select-selector::after`]:{content:"none"}},"",""),...R.dataComponentProps,children:[(0,y.Y)(i.$n,{componentId:`${x}.previous_page`,icon:(0,y.Y)(u.A,{}),disabled:!a,onClick:r,type:"tertiary",children:h}),(0,y.Y)(i.$n,{componentId:`${x}.next_page`,endIcon:(0,y.Y)(g.A,{}),disabled:!n,onClick:o,type:"tertiary",children:d}),f&&(0,y.Y)(p._v,{"aria-label":w,value:String(H),css:C,onChange:e=>{let o=Number(e);null==b||b(o),A(o),R.onValueChange(e)},children:f.map(e=>(0,y.Y)(p._v.Option,{value:String(e),children:(v||Y)(e)},e))})]})}},95381:(e,o,r)=>{r.r(o),r.d(o,{Anchor:()=>p,Arrow:()=>b,Close:()=>v,Content:()=>f,Root:()=>h,Trigger:()=>m});var t=r(71218),n=r(88002),a=r(65848),l=r(8161),i=r(80838),c=r(63908),d=r(8467),s=r(69391),u=r(94167),g=r(98358);let p=n.Mz,h=e=>{let{children:o,onOpenChange:r,componentId:t,analyticsEvents:i=[l.s7.OnView],...c}=e,d=(0,a.useRef)(!0),s=(0,a.useMemo)(()=>i,[i]),u=(0,l.ei)({componentType:l.v_.Popover,componentId:null!=t?t:"design_system.popover",analyticsEvents:s});(0,a.useEffect)(()=>{c.open&&d.current&&(u.onView(),d.current=!1)},[u,c.open]);let p=(0,a.useCallback)(e=>{e&&d.current&&(u.onView(),d.current=!1),null==r||r(e)},[u,r]);return(0,g.Y)(n.bL,{...c,onOpenChange:p,children:(0,g.Y)(l.z8.Provider,{value:{dataComponentProps:u.dataComponentProps},children:o})})},f=(0,a.forwardRef)(function(e,o){let{children:r,minWidth:t=220,maxWidth:a,...s}=e,{getPopupContainer:p}=(0,c.G)(),{theme:h}=(0,i.wn)(),{useNewShadows:f}=(0,d.p)(),m=(0,l.WF)(l.v_.Popover);return(0,g.Y)(n.ZL,{container:p&&p(),children:(0,g.Y)(n.UC,{...(0,u.VG)(),ref:o,css:[x(h,f),{minWidth:t,maxWidth:a},"",""],sideOffset:4,"aria-label":"Popover content",...s,...m,children:r})})}),m=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,g.Y)(n.l9,{...(0,u.VG)(),ref:o,...t,children:r})}),v=(0,a.forwardRef)(function(e,o){let{children:r,...t}=e;return(0,g.Y)(n.bm,{ref:o,...t,children:r})}),b=(0,a.forwardRef)(function(e,o){let{children:r,...a}=e,{theme:l}=(0,i.wn)();return(0,g.Y)(n.i3,{css:(0,t.AH)({fill:l.colors.backgroundPrimary,stroke:l.colors.borderDecorative,strokeDashoffset:-y.arrowBottomLength(),strokeDasharray:y.arrowBottomLength()+2*y.arrowSide(),strokeWidth:y.arrowStrokeWidth(),position:"relative",top:-1},"",""),ref:o,width:12,height:6,...a,children:r})}),y={arrowBottomLength:()=>30,arrowHeight:()=>10,arrowSide(){return 2*(this.arrowHeight()**2*2)**.5},arrowStrokeWidth:()=>2},w=(e,o)=>({backgroundColor:e.colors.backgroundPrimary,color:e.colors.textPrimary,lineHeight:e.typography.lineHeightBase,border:`1px solid ${e.colors.borderDecorative}`,borderRadius:e.borders.borderRadiusSm,padding:`${e.spacing.sm}px`,boxShadow:o?e.shadows.lg:e.general.shadowLow,zIndex:e.options.zIndexBase+30,...(0,s.WO)(e,o),a:(0,s.dg)({color:e.colors.actionTertiaryTextDefault,"&:hover, &:focus":{color:e.colors.actionTertiaryTextHover}}),"&:focus-visible":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"1px",outlineColor:e.colors.actionDefaultBorderFocus}}),x=(e,o)=>({...w(e,o)})},17021:(e,o,r)=>{r.d(o,{E:()=>b,d:()=>y});var t=r(71218),n=r(81434),a=r(26976),l=r(65848),i=r.n(l),c=r(8161),d=r(39659),s=r(80838),u=r(8467),g=r(80842),p=r(69391),h=r(94167),f=r(58829),m=r(98358);let v=(0,l.createContext)({size:"middle",spaced:!1,dontTruncate:!1}),b=(0,l.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,...n}=e,{classNamePrefix:c,theme:g}=(0,s.wn)(),{size:h,spaced:b,dontTruncate:y}=(0,l.useContext)(v),w=(0,f.W)("databricks.fe.designsystem.truncateSegmentedControlText",!1)&&!y,{useNewShadows:x}=(0,u.p)(),C=(0,l.useRef)(null);(0,l.useImperativeHandle)(o,()=>C.current);let k=(0,l.useCallback)(()=>{let e="";return i().Children.map(n.children,o=>{"string"==typeof o&&(e+=o)}),e},[n.children]);return(0,l.useEffect)(()=>{if(C.current){let e=C.current.input.closest("label");e&&e.setAttribute("title",k())}},[C,k]),(0,m.Y)(d.wC,{children:(0,m.Y)(a.Ay.Button,{css:function(e,o,r){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=arguments.length>4?arguments[4]:void 0,l=arguments.length>5?arguments[5]:void 0,i=`.${e}-radio-button-wrapper-checked`,c=`.${e}-radio-button-wrapper`,s=`.${e}-radio-button-wrapper-disabled`,u=`.${e}-radio-button`,g={backgroundColor:o.colors.actionDefaultBackgroundDefault,borderColor:o.colors.actionDefaultBorderDefault,color:o.colors.actionDefaultTextDefault,...l&&{boxShadow:o.shadows.xs},"::before":{display:n?"none":"block",backgroundColor:o.colors.actionDefaultBorderDefault},"&:hover":{backgroundColor:o.colors.actionDefaultBackgroundHover,borderColor:o.colors.actionDefaultBorderHover,color:o.colors.actionDefaultTextHover,"::before":{backgroundColor:o.colors.actionDefaultBorderHover},[`& + ${c}::before`]:{backgroundColor:o.colors.actionDefaultBorderPress}},"&:active":{backgroundColor:o.colors.actionTertiaryBackgroundPress,borderColor:o.colors.actionDefaultBorderPress,color:o.colors.actionTertiaryTextPress},[`&${i}`]:{backgroundColor:o.colors.actionTertiaryBackgroundPress,borderColor:o.colors.actionDefaultBorderPress,color:o.colors.actionTertiaryTextPress,...!l&&{boxShadow:"none"},"::before":{backgroundColor:o.colors.actionDefaultBorderPress},[`& + ${c}::before`]:{backgroundColor:o.colors.actionDefaultBorderPress}},[`&${i}:focus-within`]:{"::before":{width:0}},[`&${c}`]:{padding:"middle"===r?"0 16px":"0 8px",display:"inline-flex",verticalAlign:"middle",...a&&{flexShrink:1,textOverflow:"ellipsis",whiteSpace:"nowrap",minWidth:"small"===r?58:68},"&:first-of-type":{borderTopLeftRadius:o.borders.borderRadiusSm,borderBottomLeftRadius:o.borders.borderRadiusSm},"&:last-of-type":{borderTopRightRadius:o.borders.borderRadiusSm,borderBottomRightRadius:o.borders.borderRadiusSm},...n?{borderWidth:1,borderRadius:o.borders.borderRadiusSm}:{},"&:focus-within":{outlineStyle:"solid",outlineWidth:"2px",outlineOffset:"-2px",outlineColor:o.colors.actionDefaultBorderFocus},...a&&{"span:last-of-type":{textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"}}},[`&${c}, ${u}`]:{height:"middle"===r?o.general.heightSm:24,lineHeight:o.typography.lineHeightBase,alignItems:"center"},[`&${s}, &${s} + ${s}`]:{color:o.colors.actionDisabledText,backgroundColor:"transparent",borderColor:o.colors.actionDisabledBorder,"&:hover":{color:o.colors.actionDisabledText,borderColor:o.colors.actionDisabledBorder,backgroundColor:"transparent"},"&:active":{color:o.colors.actionDisabledText,borderColor:o.colors.actionDisabledBorder,backgroundColor:"transparent"},"::before":{backgroundColor:o.colors.actionDisabledBorder},[`&${i}`]:{borderColor:o.colors.actionDefaultBorderPress,"::before":{backgroundColor:o.colors.actionDefaultBorderPress}},[`&${i} + ${c}`]:{"::before":{backgroundColor:o.colors.actionDefaultBorderPress}}},...(0,d.Cx)(o.options.enableAnimation)},h=(0,p.dg)(g);return(0,t.AH)(h,"","")}(c,g,h,b,w,x),...n,...r,ref:C})})}),y=(0,l.forwardRef)(function(e,o){var r;let{dangerouslySetAntdProps:i,size:u="middle",spaced:b=!1,onChange:y,componentId:w,analyticsEvents:x,valueHasNoPii:C,dontTruncate:k,...D}=e,S=(0,f.W)("databricks.fe.observability.defaultComponentView.segmentedControlGroup",!1),{classNamePrefix:B}=(0,s.wn)(),H=(0,f.W)("databricks.fe.designsystem.truncateSegmentedControlText",!1)&&!k,A=(0,l.useMemo)(()=>null!=x?x:S?[c.s7.OnValueChange,c.s7.OnView]:[c.s7.OnValueChange],[x,S]),$=(0,c.ei)({componentType:c.v_.SegmentedControlGroup,componentId:w,analyticsEvents:A,valueHasNoPii:C}),{elementRef:T}=(0,g.z)({onView:$.onView,value:null!==(r=D.value)&&void 0!==r?r:D.defaultValue}),R=(0,n.SV)([o,T]),Y=(0,l.useCallback)(e=>{$.onValueChange(e.target.value),null==y||y(e)},[$,y]);return(0,m.Y)(d.wC,{children:(0,m.Y)(v.Provider,{value:{size:u,spaced:b,dontTruncate:k},children:(0,m.Y)(a.Ay.Group,{...(0,h.VG)(),...D,css:function(e){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2?arguments[2]:void 0,n=`.${e}-radio-group`,a=`.${e}-radio-group-small`,l=`.${e}-radio-button-wrapper`,i={...r&&{display:"flex",maxWidth:"100%"},[`&${n}`]:o?{display:"flex",gap:8,flexWrap:"wrap"}:{},[`&${a} ${l}`]:{padding:"0 12px"}},c=(0,p.dg)(i);return(0,t.AH)(c,"","")}(B,b,H),onChange:Y,...i,ref:R,...$.dataComponentProps})})})})},29183:(e,o,r)=>{r.d(o,{B:()=>m,Q:()=>f});var t=r(65848),n=r(97835),a=r(80838),l=r(14687),i=r(66795),c=r(83487),d=r(67559),s=r(32501),u=r(63434),g=r(94167),p=r(98358);let h={container:{name:"6kz1wu",styles:"display:flex;flex-direction:column;align-items:flex-start"},cell:{name:"1t820zr",styles:"width:100%;height:8px;border-radius:4px;background:var(--table-skeleton-color);margin-top:var(--table-skeleton-row-vertical-margin);margin-bottom:var(--table-skeleton-row-vertical-margin)"}},f=e=>{let{lines:o=1,seed:r="",frameRate:l=60,style:c,label:d,...s}=e,{theme:f}=(0,a.wn)(),{size:m}=(0,t.useContext)(i.G),v=(0,n.PT)(r);return(0,p.FD)("div",{...s,...(0,g.VG)(),"aria-busy":!0,css:h.container,role:"status",style:{...c,"--table-skeleton-color":f.isDarkMode?"rgba(255, 255, 255, 0.1)":"rgba(31, 38, 45, 0.1)","--table-skeleton-row-vertical-margin":"small"===m?"4px":"6px"},children:[[...Array(o)].map((e,o)=>(0,p.Y)("div",{css:[h.cell,(0,n.q5)(f,l),{width:`calc(100% - ${v[o%v.length]}px)`},"",""]},o)),(0,p.Y)("span",{css:u.Q,children:d})]})},m=e=>{let{table:o,actionColumnIds:r=[],numRows:t=3,loading:n=!0,loadingDescription:i="Table skeleton rows"}=e,{theme:u}=(0,a.wn)();return(0,p.FD)(p.FK,{children:[n&&(0,p.Y)(l.G,{description:i}),[...Array(t).keys()].map(e=>(0,p.Y)(d.H,{children:o.getFlatHeaders().map(o=>{var t,n;let a=o.column.columnDef.meta;return r.includes(o.id)?(0,p.Y)(s.f,{children:(0,p.Y)(f,{style:{width:u.general.iconSize}})},`cell-${o.id}-${e}`):(0,p.Y)(c.n,{style:null!==(t=null==a?void 0:a.styles)&&void 0!==t?t:(null==a?void 0:a.width)!==void 0?{maxWidth:a.width}:{},children:(0,p.Y)(f,{seed:`skeleton-${o.id}-${e}`,lines:null!==(n=null==a?void 0:a.numSkeletonLines)&&void 0!==n?n:void 0})},`cell-${o.id}-${e}`)})},e))]})}},30109:(e,o,r)=>{r.d(o,{o:()=>u});var t=r(97835),n=r(80838),a=r(14687),l=r(63434),i=r(94167),c=r(98358);let d={name:"116rc6i",styles:"cursor:progress;width:100%;height:28px;display:flex;justify-content:flex-start;align-items:center"},s={name:"9fmdbb",styles:"border-radius:var(--border-radius);height:12px;width:100%"},u=e=>{let{label:o,frameRate:r=60,style:u,loading:g=!0,loadingDescription:p="TitleSkeleton",...h}=e,{theme:f}=(0,n.wn)();return(0,c.FD)("div",{...(0,i.VG)(),css:d,style:{...u,"--border-radius":`${f.general.borderRadiusBase}px`},...h,children:[g&&(0,c.Y)(a.G,{description:p}),(0,c.Y)("span",{css:l.Q,children:o}),(0,c.Y)("div",{"aria-hidden":!0,css:[s,(0,t.q5)(f,r),"",""]})]})}},97835:(e,o,r)=>{r.d(o,{PT:()=>n,q5:()=>l});var t=r(71218);function n(e){return function(e,o){for(let r=e.length-1;r>0;r--){let t=Math.floor((Math.sin((o+String(r)).split("").map(e=>e.charCodeAt(0)).reduce((e,o)=>e+o,0))/2+.5)*(r+1));[e[r],e[t]]=[e[t],e[r]]}return e}([48,24,0],e)}let a=(0,t.i7)({"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),l=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60,r=e.isDarkMode?"rgba(255, 255, 255, 0.1)":"rgba(31, 38, 45, 0.1)",n=e.isDarkMode?"rgba(99, 99, 99, 0.24)":"rgba(129, 129, 129, 0.24)";return(0,t.AH)({animationDuration:"1.4s",background:`linear-gradient(90deg, ${r} 25%, ${n} 37%, ${r} 63%)`,backgroundSize:"400% 100%",animationName:a,animationTimingFunction:`steps(${o}, end)`,animationIterationCount:14,"@media only percy":{animation:"none"}},"","")}},60973:(e,o,r)=>{r.d(o,{y:()=>p});var t=r(71218),n=r(88466),a=r(39659),l=r(80838),i=r(5844),c=r(14687),d=r(69391),s=r(98358);let u=(0,t.i7)({"0%":{transform:"rotate(0deg) translate3d(0, 0, 0)"},"100%":{transform:"rotate(360deg) translate3d(0, 0, 0)"}}),g=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],l={animation:`${u} ${n}s steps(${o}, end) infinite`,...a?{color:"inherit"}:{color:e.colors.textSecondary},animationDelay:`${r}s`,"@media only percy":{animation:"none"}};return(0,t.AH)((0,d.dg)(l),"","")},p=e=>{let{frameRate:o,size:r="default",delay:t,className:d,label:u,animationDuration:p,inheritColor:h,loading:f=!0,loadingDescription:m="Spinner",...v}=e,{classNamePrefix:b,theme:y}=(0,l.wn)(),w="small"===r?"-sm":"large"===r?"-lg":"",x=w?`${b}-spin${w}`:"",C=`${d||""} ${b}-spin ${x} ${b}-spin-spinning ${a.Nb}`.trim(),k=`${b}-spin-dot ${a.Nb}`.trim();return(0,s.FD)("div",{...v,className:C,children:[f&&(0,s.Y)(c.G,{description:m}),(0,s.Y)(n.U,{label:u,children:(0,s.Y)(i.A,{"aria-hidden":"false",css:g(y,o,t,p,h),className:k})})]})}},66795:(e,o,r)=>{r.d(o,{G:()=>g,X:()=>p});var t=r(71218),n=r(88608),a=r.n(n),l=r(65848),i=r(4987),c=r(47088),d=r(80838),s=r(94167),u=r(98358);let g=(0,l.createContext)({size:"default",grid:!1}),p=(0,l.forwardRef)(function(e,o){let{children:r,size:n="default",someRowsSelected:p,style:h,pagination:f,empty:m,className:v,scrollable:b=!1,grid:y=!1,...w}=e,{theme:x}=(0,d.wn)(),C=(0,l.useRef)(null);return(0,l.useImperativeHandle)(o,()=>C.current),(0,u.Y)(c.vR.Provider,{value:c.pz,children:(0,u.Y)(g.Provider,{value:(0,l.useMemo)(()=>({size:n,someRowsSelected:p,grid:y}),[n,p,y]),children:(0,u.FD)("div",{...(0,s.VG)(),...w,style:{...h,"--table-header-active-color":x.colors.actionDefaultTextPress,colorScheme:x.isDarkMode?"dark":void 0,"--table-header-background-color":x.colors.backgroundPrimary,"--table-header-focus-color":x.colors.actionDefaultTextHover,"--table-header-sort-icon-color":x.colors.textSecondary,"--table-header-text-color":x.colors.actionDefaultTextDefault,"--table-row-hover":x.colors.tableRowHover,"--table-separator-color":x.colors.borderDecorative,"--table-resize-handle-color":x.colors.borderDecorative,"--table-spacing-md":`${x.spacing.md}px`,"--table-spacing-sm":`${x.spacing.sm}px`,"--table-spacing-xs":`${x.spacing.xs}px`},css:[i.Ay.tableWrapper,(0,t.AH)({minHeight:!m&&f?150:100},"",""),"",""],className:a()({"table-isScrollable":b,"table-isGrid":y},v),children:[(0,u.FD)("div",{role:"table",ref:C,css:i.Ay.table,tabIndex:b?0:-1,children:[r,m&&(0,u.Y)("div",{css:(0,t.AH)({padding:x.spacing.lg},"",""),children:m})]}),!m&&f&&(0,u.Y)("div",{css:i.Ay.paginationContainer,children:f})]})})})})},83487:(e,o,r)=>{r.d(o,{n:()=>u});var t=r(88608),n=r.n(t),a=r(65848),l=r(66795),i=r(4987),c=r(3078),d=r(98358),s={name:"1hipw91",styles:"&:has(> button){overflow:visible;}"};let u=(0,a.forwardRef)(function(e,o){let{children:r,className:t,ellipsis:u=!1,multiline:g=!1,align:p="left",style:h,wrapContent:f=!0,...m}=e,{size:v,grid:b}=(0,a.useContext)(l.G),y="md";"small"===v&&(y="sm");let w=!0===f?(0,d.Y)(c.o.Text,{ellipsis:!g,size:y,title:!g&&"string"==typeof r&&r||void 0,css:s,children:r}):r;return(0,d.Y)("div",{...m,role:"cell",style:{textAlign:p,...h},ref:o,css:b?void 0:i.PV.cell,className:n()(b&&i.lH.cell,t),children:w})})},83308:(e,o,r)=>{r.d(o,{A:()=>D});var t=r(81434),n=r(88608),a=r.n(n),l=r(65848),i=r(66795),c=r(67559),d=r(4987),s=r(27443),u=r(8161),g=r(80838),p=r(16494),h=r(62270),f=r(79670),m=r(69397),v=r(40813),b=r(95381),y=r(3078),w=r(58829),x=r(80842),C=r(98358);let k=(0,l.forwardRef)(function(e,o){let{style:r,resizeHandler:t,increaseWidthHandler:n,decreaseWidthHandler:a,children:i,...u}=e,{isHeader:p}=(0,l.useContext)(c.g);if(!p)throw Error("`TableHeaderResizeHandle` must be used within a `TableRow` with `isHeader` set to true.");let[h,f]=(0,l.useState)(!1),w=(0,l.useRef)(null),x=(0,l.useRef)(null),k=(0,l.useRef)(!0),D=(0,l.useRef)(!1),{theme:S}=(0,g.wn)(),B=(0,l.useCallback)(e=>{if(!n||!a){null==t||t(e);return}if(h&&!k.current)return;k.current=!1,w.current={x:e.clientX,y:e.clientY},x.current=e,D.current=!1;let o=e=>{w.current&&Math.abs(e.clientX-w.current.x)>2&&x.current&&(D.current=!0,null==t||t(x.current),document.removeEventListener("pointermove",o))},r=()=>{w.current=null,document.removeEventListener("pointermove",o),document.removeEventListener("pointerup",r)};document.addEventListener("pointermove",o),document.addEventListener("pointerup",r)},[h,t,n,a]),H=(0,l.useCallback)(e=>{if(D.current){e.preventDefault(),e.stopPropagation(),D.current=!1;return}},[]),A=(0,C.Y)("div",{...u,ref:o,onPointerDown:B,onClick:H,css:d.Ay.resizeHandleContainer,style:r,role:"button","aria-label":"Resize Column",children:(0,C.Y)("div",{css:d.Ay.resizeHandle})});return n&&a?(0,C.FD)(b.Root,{componentId:"codegen_design-system_src_design-system_tableui_tableheader.tsx_114",onOpenChange:f,children:[(0,C.Y)(b.Trigger,{asChild:!0,children:A}),(0,C.FD)(b.Content,{side:"top",align:"center",sideOffset:0,minWidth:135,style:{padding:`${S.spacing.sm} ${S.spacing.md} ${S.spacing.md} ${S.spacing.sm}`},children:[(0,C.FD)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,C.Y)(y.o.Title,{style:{marginBottom:0,marginTop:0},children:"Resize Column"}),(0,C.FD)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center"},children:[(0,C.Y)(s.$n,{onClick:()=>{a()},size:"small",componentId:"design_system.adjustable_width_header.decrease_width_button",icon:(0,C.Y)(m.A,{}),style:{backgroundColor:S.colors.actionTertiaryBackgroundHover}}),(0,C.Y)(s.$n,{onClick:()=>{n()},size:"small",componentId:"design_system.adjustable_width_header.increase_width_button",icon:(0,C.Y)(v.A,{})})]})]}),(0,C.Y)(b.Arrow,{})]})]}):A}),D=(0,l.forwardRef)(function(e,o){let r,{children:n,ellipsis:s=!1,multiline:g=!1,sortable:m,sortDirection:v,onToggleSort:b,style:D,className:S,isResizing:B=!1,align:H="left",wrapContent:A=!0,column:$,header:T,setColumnSizing:R,componentId:Y,analyticsEvents:P,"aria-label":I,...z}=e,V=(0,w.W)("databricks.fe.observability.defaultComponentView.tableHeader",!1),M=(null==$?void 0:$.getCanResize())||z.resizable||!1,L=(null==T?void 0:T.getResizeHandler())||z.resizeHandler,O=$&&T&&R,{size:_,grid:F}=(0,l.useContext)(i.G),{isHeader:W}=(0,l.useContext)(c.g),[E,N]=(0,l.useState)(v),G=(0,l.useMemo)(()=>null!=P?P:V?[u.s7.OnValueChange,u.s7.OnView]:[u.s7.OnValueChange],[P,V]),U=(0,u.ei)({componentType:u.v_.TableHeader,componentId:Y,analyticsEvents:G,valueHasNoPii:!0}),{elementRef:K}=(0,x.z)({onView:U.onView,value:E}),q=(0,t.SV)([o,K]);if(!W)throw Error("`TableHeader` a must be used within a `TableRow` with `isHeader` set to true.");let j=(0,C.Y)(C.FK,{});m&&("asc"===v?(j=(0,C.Y)(p.A,{}),r="ascending"):"desc"===v?(j=(0,C.Y)(h.A,{}),r="descending"):"none"===v&&(j=(0,C.Y)(f.A,{}),r="none")),(0,l.useEffect)(()=>{v!==E&&(N(v),U.onValueChange(v))},[v,E,U]);let X="right"===H,Q="md";"small"===_&&(Q="sm");let Z=A?(0,C.Y)(y.o.Text,{className:"table-header-text",ellipsis:!g,size:Q,title:!g&&"string"==typeof n&&n||void 0,bold:!0,children:n}):n,J=(0,l.useCallback)(e=>()=>{$&&R&&R(o=>({...o,[$.id]:e}))},[$,R]),ee=(0,l.useCallback)(()=>{$&&R&&J($.getSize()+10)()},[$,R,J]),eo=(0,l.useCallback)(()=>{$&&R&&J($.getSize()-10)()},[$,R,J]),er=M&&L?(0,C.Y)(k,{style:{height:"default"===_?"20px":"16px"},resizeHandler:L,increaseWidthHandler:O?ee:void 0,decreaseWidthHandler:O?eo:void 0}):null,et=m&&!B;return(0,C.FD)("div",{...z,ref:q,css:F?void 0:[d.PV.cell,d.PV.header],className:a()(F&&d.lH.cell,F&&d.lH.header,{"table-header-isGrid":F},S),role:"columnheader","aria-sort":m&&r||void 0,style:{justifyContent:H,textAlign:H,...D},"aria-label":et?void 0:I,...U.dataComponentProps,children:[et?(0,C.FD)("div",{css:[d.Ay.headerButtonTarget,"",""],role:"button",tabIndex:0,onClick:b,onKeyDown:e=>{if(m&&("Enter"===e.key||" "===e.key))return e.preventDefault(),null==b?void 0:b(e)},"aria-label":et?I:void 0,children:[X?(0,C.Y)("span",{className:"table-header-icon-container",css:[d.Ay.sortHeaderIconOnLeft,"",""],children:j}):null,Z,X?null:(0,C.Y)("span",{className:"table-header-icon-container",css:[d.Ay.sortHeaderIconOnRight,"",""],children:j})]}):Z,er]})})},67559:(e,o,r)=>{r.d(o,{H:()=>u,g:()=>s});var t=r(88608),n=r.n(t),a=r(65848),l=r(66795),i=r(4987),c=r(80838),d=r(98358);let s=(0,a.createContext)({isHeader:!1}),u=(0,a.forwardRef)(function(e,o){let r,{children:t,className:u,style:g,isHeader:p=!1,verticalAlignment:h,...f}=e,{size:m,grid:v}=(0,a.useContext)(l.G),{theme:b}=(0,c.wn)();return r=p&&"default"===m?b.spacing.sm:"default"===m?6:b.spacing.xs,(0,d.Y)(s.Provider,{value:(0,a.useMemo)(()=>({isHeader:p}),[p]),children:(0,d.Y)("div",{...f,ref:o,role:"row",style:{...g,"--table-row-vertical-padding":`${r}px`},css:v?void 0:i.PV.row,className:n()(u,v&&i.lH.row,{"table-isHeader":p,"table-row-isGrid":v}),children:t})})})},32501:(e,o,r)=>{r.d(o,{R:()=>s,f:()=>d});var t=r(65848),n=r(66795),a=r(67559),l=r(80838),i=r(98358);let c={container:{name:"gk361n",styles:"width:32px;padding-top:var(--vertical-padding);padding-bottom:var(--vertical-padding);display:flex;align-items:start;justify-content:center"}},d=(0,t.forwardRef)(function(e,o){let{children:r,style:d,className:s,...u}=e,{size:g}=(0,t.useContext)(n.G),{isHeader:p}=(0,t.useContext)(a.g),{theme:h}=(0,l.wn)();return(0,i.Y)("div",{...u,ref:o,role:p?"columnheader":"cell",style:{...d,"--vertical-padding":"default"===g?`${h.spacing.xs}px`:0},css:c.container,className:s,children:r})}),s=d},4987:(e,o,r)=>{r.d(o,{Ay:()=>c,PV:()=>i,lH:()=>l});var t=r(71218),n=r(25656);function a(){return(0,n.times)(20,()=>(0,n.random)(35).toString(36)).join("")}let l={cell:`js--ds-table-cell-${a()}`,header:`js--ds-table-header-${a()}`,row:`js--ds-table-row-${a()}`},i={cell:{name:"fdi8dv",styles:"display:inline-grid;position:relative;flex:1;box-sizing:border-box;padding-left:var(--table-spacing-sm);padding-right:var(--table-spacing-sm);word-break:break-word;overflow:hidden;& .anticon{vertical-align:text-bottom;}"},header:{name:"ik7qgz",styles:'font-weight:bold;align-items:flex-end;display:flex;overflow:hidden;&[aria-sort]{cursor:pointer;user-select:none;}.table-header-text{color:var(--table-header-text-color);}.table-header-icon-container{color:var(--table-header-sort-icon-color);display:none;}&[aria-sort]:hover{.table-header-icon-container, .table-header-text{color:var(--table-header-focus-color);}}&[aria-sort]:active{.table-header-icon-container, .table-header-text{color:var(--table-header-active-color);}}&:hover, &[aria-sort="ascending"], &[aria-sort="descending"]{.table-header-icon-container{display:inline;}}'},row:{name:"ndcf6g",styles:'display:flex;&.table-isHeader{> *{background-color:var(--table-header-background-color);}.table-isScrollable &{position:sticky;top:0;z-index:1;}}.table-row-select-cell input[type="checkbox"] ~ *{opacity:var(--row-checkbox-opacity, 0);}&:not(.table-row-isGrid)&:hover{&:not(.table-isHeader){background-color:var(--table-row-hover);}.table-row-select-cell input[type="checkbox"] ~ *{opacity:1;}}.table-row-select-cell input[type="checkbox"]:focus ~ *{opacity:1;}> *{padding-top:var(--table-row-vertical-padding);padding-bottom:var(--table-row-vertical-padding);border-bottom:1px solid;border-color:var(--table-separator-color);}&.table-row-isGrid > *{border-right:1px solid;border-color:var(--table-separator-color);}&.table-row-isGrid > :first-of-type{border-left:1px solid;border-color:var(--table-separator-color);}&.table-row-isGrid.table-isHeader:first-of-type > *{border-top:1px solid;border-color:var(--table-separator-color);}'}},c={tableWrapper:(0,t.AH)({"&.table-isScrollable":{overflow:"auto"},display:"flex",flexDirection:"column",height:"100%",[`.${l.cell}`]:i.cell,[`.${l.header}`]:i.header,[`.${l.row}`]:i.row},"",""),table:{name:"oxmfz7",styles:".table-isScrollable &{flex:1;overflow:auto;}"},headerButtonTarget:{name:"sezlox",styles:"align-items:flex-end;display:flex;overflow:hidden;width:100%;justify-content:inherit;&:focus{.table-header-text{color:var(--table-header-focus-color);}.table-header-icon-container{color:var(--table-header-focus-color);display:inline;}}&:active{.table-header-icon-container, .table-header-text{color:var(--table-header-active-color);}}"},sortHeaderIconOnRight:{name:"1hdiaor",styles:"margin-left:var(--table-spacing-xs)"},sortHeaderIconOnLeft:{name:"d4plmt",styles:"margin-right:var(--table-spacing-xs)"},checkboxCell:{name:"4cdr0s",styles:"display:flex;align-items:center;flex:0;padding-left:var(--table-spacing-sm);padding-top:0;padding-bottom:0;min-width:var(--table-spacing-md);max-width:var(--table-spacing-md);box-sizing:content-box"},resizeHandleContainer:(0,t.AH)({position:"absolute",right:-3,top:"var(--table-spacing-sm)",bottom:"var(--table-spacing-sm)",width:"var(--table-spacing-sm)",display:"flex",justifyContent:"center",cursor:"col-resize",userSelect:"none",touchAction:"none",zIndex:1},"",""),resizeHandle:{name:"55zery",styles:"width:1px;background:var(--table-resize-handle-color)"},paginationContainer:{name:"ehlmid",styles:"display:flex;justify-content:flex-end;padding-top:var(--table-spacing-sm);padding-bottom:var(--table-spacing-sm)"}}},99347:(e,o,r)=>{r.r(o),r.d(o,{Content:()=>D,List:()=>C,Root:()=>x,Trigger:()=>k});var t=r(81434),n=r(41270),a=r(78545),l=r(25656),i=r(65848),c=r.n(i),d=r(8161),s=r(80842),u=r(27443),g=r(6672),p=r(26033),h=r(80838),f=r(69391),m=r(94363),v=r(58829),b=r(98358);let y=c().createContext({activeValue:void 0,dataComponentProps:{"data-component-id":"design_system.tabs.default_component_id","data-component-type":d.v_.Tabs}}),w=c().createContext({viewportRef:{current:null}}),x=c().forwardRef((e,o)=>{let{value:r,defaultValue:n,onValueChange:l,componentId:u,analyticsEvents:g,valueHasNoPii:p,...h}=e,f=(0,v.W)("databricks.fe.observability.defaultComponentView.tabs",!1),m=void 0!==r,[w,x]=c().useState(n),C=(0,i.useMemo)(()=>null!=g?g:f?[d.s7.OnValueChange,d.s7.OnView]:[d.s7.OnValueChange],[g,f]),k=(0,d.ei)({componentType:d.v_.Tabs,componentId:u,analyticsEvents:C,valueHasNoPii:p,shouldStartInteraction:!0}),{elementRef:D}=(0,s.z)({onView:k.onView,value:null!=r?r:n}),S=(0,t.SV)([o,D]);return(0,b.Y)(y.Provider,{value:{activeValue:m?r:w,dataComponentProps:k.dataComponentProps},children:(0,b.Y)(a.bL,{value:r,defaultValue:n,onValueChange:e=>{k.onValueChange(e),l&&l(e),m||x(e)},...h,ref:S})})}),C=c().forwardRef((e,o)=>{let{addButtonProps:r,scrollAreaViewportCss:t,tabListCss:l,children:i,dangerouslyAppendEmotionCSS:d,shadowScrollStylesBackgroundColor:s,scrollbarHeight:p,getScrollAreaViewportRef:h,...f}=e,m=c().useRef(null),{dataComponentProps:v}=c().useContext(y),x=S(s,p);return c().useEffect(()=>{h&&h(m.current)},[h]),(0,b.Y)(w.Provider,{value:{viewportRef:m},children:(0,b.FD)("div",{css:[x.container,d,"",""],children:[(0,b.FD)(n.bL,{type:"hover",css:[x.root,"",""],children:[(0,b.Y)(n.LM,{css:[x.viewport,t,"",""],ref:m,children:(0,b.Y)(a.B8,{css:[x.list,l,"",""],...f,ref:o,...v,children:i})}),(0,b.Y)(n.Ze,{orientation:"horizontal",css:x.scrollbar,children:(0,b.Y)(n.zi,{css:x.thumb})})]}),r&&(0,b.Y)("div",{css:[x.addButtonContainer,r.dangerouslyAppendEmotionCSS,"",""],children:(0,b.Y)(u.$n,{icon:(0,b.Y)(g.A,{}),size:"small","aria-label":"Add tab",css:x.addButton,onClick:r.onClick,componentId:`${v["data-component-id"]}.add_tab`,className:r.className})})]})})}),k=c().forwardRef((e,o)=>{let{onClose:r,value:n,disabled:i,children:s,...u}=e,g=c().useRef(null),h=(0,t.SV)([o,g]),{activeValue:f,dataComponentProps:m}=c().useContext(y),v=m["data-component-id"],{viewportRef:x}=c().useContext(w),C=void 0!==r&&!i,k=B(C),D=(0,d.ei)({componentType:d.v_.Button,componentId:`${v}.close_tab`,analyticsEvents:[d.s7.OnClick]}),S=c().useCallback(()=>{if(g.current&&x.current&&f===n){let e=x.current.getBoundingClientRect(),o=g.current.getBoundingClientRect();o.left<e.left?x.current.scrollLeft-=e.left-o.left:o.right>e.right&&(x.current.scrollLeft+=o.right-e.right)}},[x,f,n]),H=c().useMemo(()=>(0,l.debounce)(S,10),[S]);return c().useEffect(()=>{S()},[S]),c().useEffect(()=>{if(!x.current||!g.current)return;let e=new ResizeObserver(H);return e.observe(x.current),e.observe(g.current),()=>{e.disconnect(),H.cancel()}},[H,x]),(0,b.FD)(a.l9,{css:k.trigger,value:n,disabled:i,onKeyDown:e=>{C&&"Delete"===e.key&&(D.onClick(e),e.stopPropagation(),e.preventDefault(),r(n))},onMouseDown:e=>{C&&1===e.button&&(D.onClick(e),e.stopPropagation(),e.preventDefault(),r(n))},...u,ref:h,children:[s,C&&(0,b.Y)(p.A,{onMouseDown:e=>{i||0!==e.button||!1!==e.ctrlKey||(D.onClick(e),e.stopPropagation(),e.preventDefault(),r(n))},css:k.closeSmallIcon,"aria-hidden":"false","aria-label":"Press delete to close the tab"})]})}),D=c().forwardRef((e,o)=>{let{...r}=e,{theme:t}=(0,h.wn)(),n=H(t);return(0,b.Y)(a.UC,{css:n,...r,ref:o})}),S=(e,o)=>{let{theme:r}=(0,h.wn)();return{container:(0,m.c)(r),root:{overflow:"hidden"},viewport:{...(0,f.Ud)(r,{orientation:"horizontal",backgroundColor:e})},list:{display:"flex",alignItems:"center"},scrollbar:{display:"flex",flexDirection:"column",userSelect:"none",touchAction:"none",height:null!=o?o:3},thumb:{flex:1,background:r.isDarkMode?"rgba(255, 255, 255, 0.2)":"rgba(17, 23, 28, 0.2)","&:hover":{background:r.isDarkMode?"rgba(255, 255, 255, 0.3)":"rgba(17, 23, 28, 0.3)"},borderRadius:r.borders.borderRadiusSm,position:"relative"},addButtonContainer:{flex:1},addButton:{margin:"2px 0 6px 0"}}},B=e=>{let{theme:o}=(0,h.wn)();return{trigger:{...(0,m.s)(o),alignItems:"center",justifyContent:e?"space-between":"center",minWidth:e?o.spacing.lg+o.spacing.md:o.spacing.lg,color:o.colors.textSecondary,lineHeight:o.typography.lineHeightBase,whiteSpace:"nowrap",border:"none",padding:`${o.spacing.xs}px 0 ${o.spacing.sm}px 0`,"& > .anticon:last-of-type":{visibility:"hidden"},"&:hover":{cursor:"pointer",color:o.colors.actionDefaultTextHover,"& > .anticon:last-of-type":{visibility:"visible"}},"&:active":{color:o.colors.actionDefaultTextPress},outlineStyle:"none",outlineColor:o.colors.actionDefaultBorderFocus,"&:focus-visible":{outlineStyle:"auto"},'&[data-state="active"]':{color:o.colors.textPrimary,boxShadow:`inset 0 -4px 0 ${o.colors.actionPrimaryBackgroundDefault}`,"& > .anticon:last-of-type":{visibility:"visible"}},"&[data-disabled]":{color:o.colors.actionDisabledText,"&:hover":{cursor:"not-allowed"}}},closeSmallIcon:{marginLeft:o.spacing.xs,color:o.colors.textSecondary,"&:hover":{color:o.colors.actionDefaultTextHover},"&:active":{color:o.colors.actionDefaultTextPress}}}},H=e=>({color:e.colors.textPrimary,'&[data-state="inactive"]':{display:"none"}})},36173:(e,o,r)=>{r.d(o,{v:()=>h});var t=r(71218),n=r(81434),a=r(65848),l=r(8161),i=r(80838),c=r(4700),d=r(80842),s=r(94167),u=r(58829),g=r(98358);let p={default:"tagDefault",brown:"tagBrown",coral:"tagCoral",charcoal:"grey600",indigo:"tagIndigo",lemon:"tagLemon",lime:"tagLime",pink:"tagPink",purple:"tagPurple",teal:"tagTeal",turquoise:"tagTurquoise"},h=(0,a.forwardRef)((e,o)=>{let{theme:r}=(0,i.wn)(),{color:h,children:f,closable:m,onClose:v,role:b="status",closeButtonProps:y,analyticsEvents:w,componentId:x,icon:C,onClick:k,...D}=e,S=(0,u.W)("databricks.fe.observability.defaultComponentView.tag",!1),B=!!e.onClick,H=(0,a.useMemo)(()=>null!=w?w:S?[l.s7.OnClick,l.s7.OnView]:[l.s7.OnClick],[w,S]),A=(0,l.ei)({componentType:l.v_.Tag,componentId:x,analyticsEvents:H}),{elementRef:$}=(0,d.z)({onView:A.onView}),T=(0,n.SV)([$,o]),R=x?`${x}.close`:void 0,Y=(0,l.ei)({componentType:l.v_.Button,componentId:R,analyticsEvents:[l.s7.OnClick]}),P=(0,a.useCallback)(e=>{k&&(A.onClick(e),k(e))},[A,k]),I=(0,a.useCallback)(e=>{Y.onClick(e),e.stopPropagation(),v&&v()},[Y,v]),z=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],t=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=e.colors.tagText,a=e.colors[p[o]],l="",i=e.colors.actionDefaultBorderFocus,c=o.charAt(0).toUpperCase()+o.slice(1);n=e.DU_BOIS_INTERNAL_ONLY.colors[`tagText${c}`],a=e.DU_BOIS_INTERNAL_ONLY.colors[`tagBackground${c}`],l=e.DU_BOIS_INTERNAL_ONLY.colors[`tagIcon${c}`],"charcoal"===o&&(i=e.colors.white);let d=e.colors.tagIconHover,s=e.colors.tagIconPress;return{wrapper:{backgroundColor:a,display:"inline-flex",alignItems:"center",marginRight:e.spacing.sm,borderRadius:e.borders.borderRadiusSm},tag:{border:"none",color:n,padding:"",backgroundColor:"transparent",borderRadius:e.borders.borderRadiusSm,marginRight:e.spacing.sm,display:"inline-block",cursor:r?"pointer":"default",...t&&{borderTopRightRadius:0,borderBottomRightRadius:0},...r&&{"&:hover":{"& > div":{backgroundColor:e.colors.actionDefaultBackgroundHover}},"&:active":{"& > div":{backgroundColor:e.colors.actionDefaultBackgroundPress}}}},content:{display:"flex",alignItems:"center",minWidth:0,height:e.typography.lineHeightBase},close:{height:e.typography.lineHeightBase,width:e.typography.lineHeightBase,lineHeight:`${e.general.iconFontSize}px`,padding:0,color:n,fontSize:e.general.iconFontSize,borderTopRightRadius:e.borders.borderRadiusSm,borderBottomRightRadius:e.borders.borderRadiusSm,border:"none",background:"none",cursor:"pointer",marginLeft:e.spacing.xs,display:"flex",alignItems:"center",justifyContent:"center",margin:0,"&:hover":{backgroundColor:e.colors.actionDefaultBackgroundHover,color:d},"&:active":{backgroundColor:e.colors.actionDefaultBackgroundPress,color:s},"&:focus-visible":{outlineStyle:"solid",outlineWidth:1,outlineOffset:-2,outlineColor:i},".anticon":{verticalAlign:0,fontSize:12}},text:{padding:0,fontSize:e.typography.fontSizeBase,fontWeight:e.typography.typographyRegularFontWeight,lineHeight:e.typography.lineHeightSm,"& .anticon":{verticalAlign:"text-top"},whiteSpace:"nowrap"},icon:{color:l,paddingLeft:e.spacing.xs,height:e.typography.lineHeightBase,display:"inline-flex",alignItems:"center",borderTopLeftRadius:e.borders.borderRadiusSm,borderBottomLeftRadius:e.borders.borderRadiusSm,"& > span":{fontSize:12},"& + div":{borderTopLeftRadius:0,borderBottomLeftRadius:0,...t&&{borderTopRightRadius:0,borderBottomRightRadius:0}}},childrenWrapper:{paddingLeft:e.spacing.xs,paddingRight:e.spacing.xs,height:e.typography.lineHeightBase,display:"inline-flex",alignItems:"center",borderRadius:e.borders.borderRadiusSm,minWidth:0}}}(r,h,B,m);return(0,g.FD)("div",{ref:T,role:b,onClick:P,css:[z.wrapper,"",""],...D,...(0,s.VG)(),...A.dataComponentProps,tabIndex:B?0:-1,children:[(0,g.FD)("div",{css:[z.tag,z.content,z.text,"margin-right:0;",""],...A.dataComponentProps,children:[C&&(0,g.Y)("div",{css:[z.icon,"",""],children:C}),(0,g.Y)("div",{css:[z.childrenWrapper,"",""],children:f})]}),m&&(0,g.Y)("button",{css:z.close,tabIndex:0,onClick:I,onMouseDown:e=>{e.stopPropagation()},...y,...Y.dataComponentProps,children:(0,g.Y)(c.A,{css:(0,t.AH)({fontSize:r.general.iconFontSize-4},"","")})})]})})},46249:(e,o,r)=>{r.d(o,{I:()=>c});var t=r(71218),n=r(39234),a=r(80838),l=r(55444),i=r(98358);let c=e=>{let{content:o,iconTitle:r="More information",...c}=e,{theme:d}=(0,a.wn)();return(0,i.Y)(n.m,{content:o,...c,children:(0,i.Y)(l.A,{tabIndex:0,"aria-hidden":"false","aria-label":r,alt:r,css:(0,t.AH)({color:d.colors.textSecondary},"","")})})}},39234:(e,o,r)=>{r.d(o,{m:()=>p});var t=r(71218),n=r(80692),a=r(65848),l=r(8161),i=r(80838),c=r(63908),d=r(8467);let s=()=>"undefined"!=typeof jest;var u=r(98358);let g=e=>{let{maxWidth:o}=e,{theme:r,classNamePrefix:n}=(0,i.wn)(),{useNewShadows:a}=(0,d.p)(),l=`.${n}-typography`,{isDarkMode:c}=r,s=(0,t.i7)({from:{opacity:0,transform:"translateY(2px)"},to:{opacity:1,transform:"translateY(0)"}}),u=(0,t.i7)({from:{opacity:0,transform:"translateX(-2px)"},to:{opacity:1,transform:"translateX(0)"}}),g=(0,t.i7)({from:{opacity:0,transform:"translateY(-2px)"},to:{opacity:1,transform:"translateY(0)"}}),p=(0,t.i7)({from:{opacity:0,transform:"translateX(2px)"},to:{opacity:1,transform:"translateX(0)"}}),h=c?r.colors.blue600:r.colors.blue500,f=c?r.colors.blue800:r.colors.blue300,m=c?r.colors.blue700:r.colors.blue400;return{content:{backgroundColor:r.colors.tooltipBackgroundTooltip,color:r.colors.actionPrimaryTextDefault,borderRadius:r.borders.borderRadiusSm,fontSize:r.typography.fontSizeMd,padding:`${r.spacing.xs}px ${r.spacing.sm}px`,lineHeight:r.typography.lineHeightLg,fontWeight:r.typography.typographyRegularFontWeight,boxShadow:a?r.shadows.lg:r.general.shadowHigh,maxWidth:o,wordWrap:"break-word",whiteSpace:"normal",zIndex:r.options.zIndexBase+70,willChange:"transform, opacity","&[data-state='delayed-open'][data-side='top']":{animation:`${g} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},"&[data-state='delayed-open'][data-side='right']":{animation:`${p} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},"&[data-state='delayed-open'][data-side='bottom']":{animation:`${s} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},"&[data-state='delayed-open'][data-side='left']":{animation:`${u} 400ms cubic-bezier(0.16, 1, 0.3, 1)`},[`& a${l}`]:{"&, :focus":{color:h,".anticon":{color:h}},":active":{color:f,".anticon":{color:f}},":hover":{color:m,".anticon":{color:m}}}},arrow:{fill:r.colors.tooltipBackgroundTooltip,zIndex:r.options.zIndexBase+70,visibility:"visible"}}},p=e=>{let{children:o,content:r,defaultOpen:t=!1,delayDuration:d=350,side:p="top",sideOffset:h=4,align:f="center",maxWidth:m=250,componentId:v,analyticsEvents:b=[l.s7.OnView],zIndex:y,...w}=e,{theme:x}=(0,i.wn)(),{getPopupContainer:C}=(0,c.G)(),k=g({maxWidth:m}),D=(0,a.useMemo)(()=>b,[b]),S=(0,l.ei)({componentType:l.v_.Tooltip,componentId:v,analyticsEvents:D}),B=(0,a.useRef)(!0),H=(0,a.useCallback)(e=>{e&&B.current&&(S.onView(),B.current=!1)},[S,B]),A=s();return(0,u.FD)(n.bL,{defaultOpen:t,delayDuration:A?10:d,onOpenChange:H,children:[(0,u.Y)(n.l9,{asChild:!0,children:o}),r?(0,u.Y)(n.ZL,{container:C&&C(),children:(0,u.FD)(n.UC,{side:p,align:f,sideOffset:x.spacing.sm,arrowPadding:x.spacing.md,css:[k.content,y?{zIndex:y}:void 0,"",""],...w,...S.dataComponentProps,children:[r,(0,u.Y)(n.i3,{css:k.arrow})]})}):null]})}},99342:(e,o,r)=>{r.d(o,{h:()=>u});var t=r(71218),n=r(80646),a=r(39659),l=r(80838),i=r(69391),c=r(94167),d=r(98358);let{Title:s}=n.A;function u(e){let{dangerouslySetAntdProps:o,withoutMargins:r,color:n,elementLevel:u,...g}=e,{theme:p}=(0,l.wn)();return(0,d.Y)(a.wC,{children:(0,d.Y)(s,{...(0,c.VG)(),...g,level:null!=u?u:g.level,className:g.className,css:(0,t.AH)(function(e,o){switch(o.level){case 1:return(0,t.AH)({"&&":{fontSize:e.typography.fontSizeXxl,lineHeight:e.typography.lineHeightXxl,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightXxl}},"","");case 2:return(0,t.AH)({"&&":{fontSize:e.typography.fontSizeXl,lineHeight:e.typography.lineHeightXl,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightXl}},"","");case 3:return(0,t.AH)({"&&":{fontSize:e.typography.fontSizeLg,lineHeight:e.typography.lineHeightLg,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightLg}},"","");default:return(0,t.AH)({"&&":{fontSize:e.typography.fontSizeMd,lineHeight:e.typography.lineHeightMd,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightMd}},"","")}}(p,e),{"&&":{color:(0,i.X1)(p,e.color,p.colors.textPrimary)},"& > .anticon":{verticalAlign:"middle"}},e.withoutMargins&&{"&&":{marginTop:"0 !important",marginBottom:"0 !important"}},"",""),...o})})}},3078:(e,o,r)=>{r.d(o,{o:()=>Y});var t=r(80646),n=r(71218),a=r(39659),l=r(80838),i=r(94167),c=r(98358);let{Text:d}=t.A;function s(e){let{dangerouslySetAntdProps:o,bold:r,withoutMargins:t,color:s,...u}=e,{theme:g}=(0,l.wn)();return(0,c.Y)(a.wC,{children:(0,c.Y)(d,{...(0,i.VG)(),...u,css:(0,n.AH)({"&&":{display:"block",fontSize:g.typography.fontSizeSm,lineHeight:g.typography.lineHeightSm,color:g.colors.textSecondary,...e.withoutMargins&&{"&&":{marginTop:0,marginBottom:0}}}},"",""),...o})})}var u=r(81434),g=r(65848),p=r(8161),h=r(10954),f=r(80842),m=r(58829);let v=(e,o)=>{let r=`.${o}-typography`,t={[`&${r}, &${r}:focus`]:{color:e.colors.actionTertiaryTextDefault},[`&${r}:hover, &${r}:hover .anticon`]:{color:e.colors.actionTertiaryTextHover,textDecoration:"underline"},[`&${r}:active, &${r}:active .anticon`]:{color:e.colors.actionTertiaryTextPress,textDecoration:"underline"},[`&${r}:focus-visible`]:{textDecoration:"underline"},".anticon":{fontSize:12,verticalAlign:"baseline"},[`.${o}-tooltip-inner a&${r}`]:{"&, :focus":{color:e.colors.blue500,".anticon":{color:e.colors.blue500}},":active":{color:e.colors.blue500,".anticon":{color:e.colors.blue500}},":hover":{color:e.colors.blue400,".anticon":{color:e.colors.blue400}}}};return(0,n.AH)(t,"","")},b=()=>(0,n.AH)({paddingRight:"calc(2px + 1em)",position:"relative"},"",""),y=e=>{let o={marginLeft:4,color:e.colors.actionTertiaryTextDefault,position:"relative",top:"1px"};return(0,n.AH)(o,"","")},w=e=>{let o={position:"absolute",right:0,bottom:0,top:0,display:"flex",alignItems:"center",...e&&{fontSize:12}};return(0,n.AH)(o,"","")},x=(0,g.forwardRef)(function(e,o){let{dangerouslySetAntdProps:r,componentId:n,analyticsEvents:d,onClick:s,...x}=e,C=(0,m.W)("databricks.fe.observability.defaultComponentView.typographyLink",!1),{children:k,openInNewTab:D,...S}=x,{theme:B,classNamePrefix:H}=(0,l.wn)(),A=(0,g.useMemo)(()=>null!=d?d:C?[p.s7.OnClick,p.s7.OnView]:[p.s7.OnClick],[d,C]),$=(0,p.ei)({componentType:p.v_.TypographyLink,componentId:n,analyticsEvents:A,shouldStartInteraction:!1}),{elementRef:T}=(0,f.z)({onView:$.onView}),R=(0,u.SV)([o,T]),Y=(0,g.useCallback)(e=>{$.onClick(e),null==s||s(e)},[$,s]),P={rel:"noopener noreferrer",target:"_blank"},I=D?{...S,...P}:{...S},z=x.ellipsis&&D?[v(B,H),b()]:v(B,H),V=x.ellipsis?[y(B),w()]:y(B);return(0,c.Y)(a.wC,{children:(0,c.FD)(t.A.Link,{...(0,i.VG)(),"aria-disabled":I.disabled,css:z,ref:R,onClick:Y,...I,...r,...$.dataComponentProps,children:[k,D?(0,c.Y)(h.A,{css:V,...P}):null]})})});var C=r(69391);let{Paragraph:k}=t.A;function D(e){let{dangerouslySetAntdProps:o,withoutMargins:r,color:t,...d}=e,{theme:s,classNamePrefix:u}=(0,l.wn)();return(0,c.Y)(a.wC,{children:(0,c.Y)(k,{...(0,i.VG)(),...d,className:d.className,css:(0,n.AH)({"&&":{fontSize:s.typography.fontSizeBase,fontWeight:s.typography.typographyRegularFontWeight,lineHeight:s.typography.lineHeightBase,color:(0,C.X1)(s,e.color,s.colors.textPrimary)},"& .anticon":{verticalAlign:"text-bottom"},[`& .${u}-btn-link`]:{verticalAlign:"baseline !important"}},e.disabled&&{"&&":{color:s.colors.actionDisabledText}},e.withoutMargins&&{"&&":{marginTop:0,marginBottom:0}},"",""),...o})})}let{Text:S}=t.A;function B(e){let{dangerouslySetAntdProps:o,bold:r,hint:t,withoutMargins:d,color:s,...u}=e,{theme:g}=(0,l.wn)();return(0,c.Y)(a.wC,{children:(0,c.Y)(S,{...(0,i.VG)(),...u,className:u.className,css:(0,n.AH)({"&&":{fontSize:g.typography.fontSizeBase,fontWeight:g.typography.typographyRegularFontWeight,lineHeight:g.typography.lineHeightBase,color:(0,C.X1)(g,e.color,g.colors.textPrimary)}},e.disabled&&{"&&":{color:g.colors.actionDisabledText}},e.hint&&{"&&":{fontSize:g.typography.fontSizeSm,lineHeight:g.typography.lineHeightSm}},e.bold&&{"&&":{fontSize:g.typography.fontSizeBase,fontWeight:g.typography.typographyBoldFontWeight,lineHeight:g.typography.lineHeightBase}},e.code&&{"&& > code":{color:g.colors.textPrimary,fontSize:g.typography.fontSizeBase,lineHeight:g.typography.lineHeightBase,background:g.colors.typographyCodeBg,fontFamily:"monospace",borderRadius:g.borders.borderRadiusSm,padding:"2px 4px",border:"unset",margin:0}},e.size&&{"&&":(()=>{switch(e.size){case"xxl":return{fontSize:g.typography.fontSizeXxl,lineHeight:g.typography.lineHeightXxl,"& .anticon":{lineHeight:g.typography.lineHeightXxl,verticalAlign:"middle"}};case"xl":return{fontSize:g.typography.fontSizeXl,lineHeight:g.typography.lineHeightXl,"& .anticon":{lineHeight:g.typography.lineHeightXl,verticalAlign:"middle"}};case"lg":return{fontSize:g.typography.fontSizeLg,lineHeight:g.typography.lineHeightLg,"& .anticon":{lineHeight:g.typography.lineHeightLg,verticalAlign:"middle"}};case"sm":return{fontSize:g.typography.fontSizeSm,lineHeight:g.typography.lineHeightSm,"& .anticon":{verticalAlign:"-0.219em"}};default:return{}}})()},e.withoutMargins&&{"&&":{marginTop:0,marginBottom:0}},"",""),...o})})}var H=r(25656),A=r(64229);function $(e){var o,r;let{text:t,suffixLength:n=6,...a}=e,l=(0,g.useMemo)(()=>(0,H.uniqueId)("text-middle-elided-"),[]),{start:i,suffix:d}=function(e,o){if(e.length<=o)return{start:e,suffix:void 0};return{start:e.slice(0,e.length-o).trim(),suffix:e.slice(-o).trim()}}(t,n),s=(null===(o=A)||void 0===A||null===(o=o.env)||void 0===o?void 0:"production")==="test";return(0,g.useEffect)(()=>{var e;let o=e=>{var o;null==e||e.preventDefault(),null==e||null===(o=e.clipboardData)||void 0===o||o.setData("text/plain",t)},r=`.${l}`;return null===(e=document.querySelector(r))||void 0===e||e.addEventListener("copy",o),()=>{var e;null===(e=document.querySelector(r))||void 0===e||e.removeEventListener("copy",o)}},[l,t]),(0,c.Y)(B,{ellipsis:s?void 0:{suffix:d},...a,"aria-label":t,title:null!==(r=a.title)&&void 0!==r?r:t,className:l,children:s?t:i})}var T=r(99342);let R=e=>{let{children:o,lines:r=1,...t}=e,[a,l]=(0,g.useState)(""),i=(0,g.useRef)(null);return(0,g.useEffect)(()=>{i.current&&l(i.current.textContent||"")},[i,o]),(0,c.Y)("span",{ref:i,title:a,css:(0,n.AH)({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"normal",wordBreak:"break-word",display:"-webkit-box",webkitLineClamp:r,WebkitBoxOrient:"vertical",WebkitLineClamp:r},"",""),...t,children:o})},Y=(()=>{function e(e){let{dangerouslySetAntdProps:o,...r}=e;return(0,c.Y)(a.wC,{children:(0,c.Y)(t.A,{...(0,i.VG)(),...r,...o})})}return e.Text=B,e.Title=T.h,e.Paragraph=D,e.Link=x,e.Hint=s,e.Truncate=R,e.TextMiddleElide=$,e})()},37590:(e,o,r)=>{r.d(o,{DQ:()=>d,GE:()=>i,Kb:()=>l,RB:()=>c,UA:()=>g,WS:()=>u,eO:()=>s,rj:()=>p,y3:()=>h});var t=r(71218),n=r(83221),a=r(69391);let l=(e,o)=>{let{maxHeight:r="100vh",maxWidth:n="100vw",minHeight:l=0,minWidth:i=0,width:c,useNewShadows:d}=o;return(0,t.AH)({maxHeight:r,maxWidth:n,minHeight:l,minWidth:i,...c?{width:c}:{},background:e.colors.backgroundPrimary,color:e.colors.textPrimary,overflow:"auto",zIndex:e.options.zIndexBase+10,boxSizing:"border-box",border:`1px solid ${e.colors.border}`,boxShadow:d?e.shadows.lg:e.general.shadowLow,borderRadius:e.borders.borderRadiusSm,colorScheme:e.isDarkMode?"dark":"light",...(0,a.WO)(e,d)},"","")},i=[6,32,6,12],c=e=>(0,t.AH)((0,a.dg)({display:"flex",flexDirection:"row",alignItems:"flex-start",justifyContent:"flex-start",alignSelf:"stretch",padding:`${i.map(e=>`${e}px`).join(" ")}`,lineHeight:e.typography.lineHeightBase,boxSizing:"content-box",cursor:"pointer",userSelect:"none",'&[data-highlighted="true"]':{background:e.colors.actionTertiaryBackgroundHover},"&:focus":{background:e.colors.actionTertiaryBackgroundHover,outline:"none"},"&[disabled]":{pointerEvents:"none",color:e.colors.actionDisabledText,background:e.colors.backgroundPrimary}}),"",""),d=e=>{let{theme:o,dangerouslyHideCheck:r,textOverflowMode:a,contentWidth:l,hasHintColumn:i}=e;return(0,t.AH)({marginLeft:r?0:o.spacing.sm,fontSize:o.typography.fontSizeBase,fontStyle:"normal",fontWeight:400,cursor:"pointer",overflow:"hidden",wordBreak:"break-word",..."ellipsis"===a&&{textOverflow:"ellipsis",whiteSpace:"nowrap"},...l?{width:(0,n.x9)(o,l)}:{},...i&&{display:"flex"}},"","")},s=e=>(0,t.AH)({paddingLeft:e.spacing.xs,color:e.colors.textSecondary,pointerEvents:"all",cursor:"pointer",verticalAlign:"middle"},"",""),u=(e,o)=>(0,t.AH)({pointerEvents:"none",height:"unset",width:"100%","& > label":{display:"flex",width:"100%",fontSize:e.typography.fontSizeBase,fontStyle:"normal",fontWeight:400,cursor:"pointer","& > span:first-of-type":{alignSelf:"flex-start",display:"inline-flex",alignItems:"center",paddingTop:e.spacing.xs/2},"& > span:last-of-type, & > span:last-of-type > label":{paddingRight:0,width:"100%",overflow:"hidden",wordBreak:"break-word",..."ellipsis"===o?{textOverflow:"ellipsis",whiteSpace:"nowrap"}:{}}}},"",""),g=e=>(0,t.AH)({width:"100%",background:e.colors.backgroundPrimary,padding:`${e.spacing.sm}px ${e.spacing.lg/2}px ${e.spacing.sm}px ${e.spacing.lg/2}px`,position:"sticky",bottom:0,boxSizing:"border-box","&:has(> .combobox-footer-add-button)":{padding:`${e.spacing.sm}px 0 ${e.spacing.sm}px 0`,"& > :not(.combobox-footer-add-button)":{marginLeft:`${e.spacing.lg/2}px`,marginRight:`${e.spacing.lg/2}px`},"& > .combobox-footer-add-button":{justifyContent:"flex-start !important"}}},"",""),p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;return(0,t.AH)({flexGrow:1,display:"inline-grid",gridTemplateColumns:`${100-e}% ${e}%`},"","")},h=(e,o,r)=>(0,t.AH)({color:e.colors.textSecondary,fontSize:e.typography.fontSizeSm,textAlign:"right",...o&&{color:e.colors.actionDisabledText},..."ellipsis"===r&&{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}},"","")},60054:(e,o,r)=>{r.d(o,{m:()=>d});var t=r(65848),n=r(28219),a=r(55444),l=r(80333),i=r(98358);let c=e=>({display:"inline-flex",paddingLeft:e.spacing.xs,color:e.colors.textSecondary,pointerEvents:"all"}),d=(e,o,r,n)=>{let d=t.Children.count(e),u=(0,i.Y)(l.p,{title:r,placement:"right",dangerouslySetAntdProps:{getPopupContainer:()=>n.current||document.body},children:(0,i.Y)("span",{"data-disabled-tooltip":!0,css:e=>c(e),onClick:e=>{o.disabled&&e.stopPropagation()},children:(0,i.Y)(a.A,{role:"presentation",alt:"Disabled state reason","aria-hidden":"false"})})});if(1===d)return s(e,!!o.disabled,r,u,0,d);return t.Children.map(e,(e,t)=>s(e,!!o.disabled,r,u,t,d))},s=(e,o,r,t,a,l)=>{let c=(0,i.Y)(n.HintColumn,{}).type,d=!!(e&&"string"!=typeof e&&"number"!=typeof e&&"boolean"!=typeof e&&"type"in e&&(null==e?void 0:e.type)===c);if(o&&r&&e&&d)return(0,i.FD)(i.FK,{children:[t,e]});if(a===l-1&&o&&r)return(0,i.FD)(i.FK,{children:[e,t]});return e}},94363:(e,o,r)=>{r.d(o,{c:()=>t,s:()=>n});let t=e=>({display:"flex",borderBottom:`1px solid ${e.colors.border}`,marginBottom:e.spacing.md,height:e.general.heightSm,boxSizing:"border-box"}),n=e=>({display:"flex",fontWeight:e.typography.typographyBoldFontWeight,fontSize:e.typography.fontSizeMd,backgroundColor:"transparent",marginRight:e.spacing.md})},37402:(e,o,r)=>{r.d(o,{I:()=>n});let t=function(e){return e.ActionDangerDefaultBackgroundDefault="actionDangerDefaultBackgroundDefault",e.ActionDangerDefaultBackgroundHover="actionDangerDefaultBackgroundHover",e.ActionDangerDefaultBackgroundPress="actionDangerDefaultBackgroundPress",e.ActionDangerDefaultBorderDefault="actionDangerDefaultBorderDefault",e.ActionDangerDefaultBorderHover="actionDangerDefaultBorderHover",e.ActionDangerDefaultBorderPress="actionDangerDefaultBorderPress",e.ActionDangerDefaultTextDefault="actionDangerDefaultTextDefault",e.ActionDangerDefaultTextHover="actionDangerDefaultTextHover",e.ActionDangerDefaultTextPress="actionDangerDefaultTextPress",e.ActionDangerPrimaryBackgroundDefault="actionDangerPrimaryBackgroundDefault",e.ActionDangerPrimaryBackgroundHover="actionDangerPrimaryBackgroundHover",e.ActionDangerPrimaryBackgroundPress="actionDangerPrimaryBackgroundPress",e.ActionDangerPrimaryText="actionDangerPrimaryText",e.ActionDefaultBackgroundDefault="actionDefaultBackgroundDefault",e.ActionDefaultBackgroundHover="actionDefaultBackgroundHover",e.ActionDefaultBackgroundPress="actionDefaultBackgroundPress",e.ActionDefaultBorderDefault="actionDefaultBorderDefault",e.ActionDefaultBorderFocus="actionDefaultBorderFocus",e.ActionDefaultBorderHover="actionDefaultBorderHover",e.ActionDefaultBorderPress="actionDefaultBorderPress",e.ActionDefaultIconDefault="actionDefaultIconDefault",e.ActionDefaultIconHover="actionDefaultIconHover",e.ActionDefaultIconPress="actionDefaultIconPress",e.ActionDefaultTextDefault="actionDefaultTextDefault",e.ActionDefaultTextHover="actionDefaultTextHover",e.ActionDefaultTextPress="actionDefaultTextPress",e.ActionDisabledBackground="actionDisabledBackground",e.ActionDisabledBorder="actionDisabledBorder",e.ActionDisabledText="actionDisabledText",e.ActionIconBackgroundDefault="actionIconBackgroundDefault",e.ActionIconBackgroundHover="actionIconBackgroundHover",e.ActionIconBackgroundPress="actionIconBackgroundPress",e.ActionIconIconDefault="actionIconIconDefault",e.ActionIconIconHover="actionIconIconHover",e.ActionIconIconPress="actionIconIconPress",e.ActionLinkDefault="actionLinkDefault",e.ActionLinkHover="actionLinkHover",e.ActionLinkPress="actionLinkPress",e.ActionPrimaryBackgroundDefault="actionPrimaryBackgroundDefault",e.ActionPrimaryBackgroundHover="actionPrimaryBackgroundHover",e.ActionPrimaryBackgroundPress="actionPrimaryBackgroundPress",e.ActionPrimaryIcon="actionPrimaryIcon",e.ActionPrimaryTextDefault="actionPrimaryTextDefault",e.ActionPrimaryTextHover="actionPrimaryTextHover",e.ActionPrimaryTextPress="actionPrimaryTextPress",e.ActionTertiaryBackgroundDefault="actionTertiaryBackgroundDefault",e.ActionTertiaryBackgroundHover="actionTertiaryBackgroundHover",e.ActionTertiaryBackgroundPress="actionTertiaryBackgroundPress",e.ActionTertiaryIconDefault="actionTertiaryIconDefault",e.ActionTertiaryIconHover="actionTertiaryIconHover",e.ActionTertiaryIconPress="actionTertiaryIconPress",e.ActionTertiaryTextDefault="actionTertiaryTextDefault",e.ActionTertiaryTextHover="actionTertiaryTextHover",e.ActionTertiaryTextPress="actionTertiaryTextPress",e.BackgroundDanger="backgroundDanger",e.BackgroundPrimary="backgroundPrimary",e.BackgroundSecondary="backgroundSecondary",e.BackgroundSuccess="backgroundSuccess",e.BackgroundWarning="backgroundWarning",e.Border="border",e.BorderAccessible="borderAccessible",e.BorderDanger="borderDanger",e.BorderWarning="borderWarning",e.CodeBackground="codeBackground",e.OverlayOverlay="overlayOverlay",e.ProgressFill="progressFill",e.ProgressTrack="progressTrack",e.Skeleton="skeleton",e.TableBackgroundSelectedDefault="tableBackgroundSelectedDefault",e.TableBackgroundSelectedHover="tableBackgroundSelectedHover",e.TableBackgroundUnselectedDefault="tableBackgroundUnselectedDefault",e.TableBackgroundUnselectedHover="tableBackgroundUnselectedHover",e.TextPlaceholder="textPlaceholder",e.TextPrimary="textPrimary",e.TextSecondary="textSecondary",e.TextValidationDanger="textValidationDanger",e.TextValidationSuccess="textValidationSuccess",e.TextValidationWarning="textValidationWarning",e.TooltipBackgroundTooltip="tooltipBackgroundTooltip",e}({}),n={primary:t.TextPrimary,secondary:t.TextSecondary,info:t.TextSecondary,error:t.TextValidationDanger,success:t.TextValidationSuccess,warning:t.TextValidationWarning}},63434:(e,o,r)=>{r.d(o,{Q:()=>t});let t={"&:not(:focus):not(:active)":{clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",overflow:"hidden",position:"absolute",whiteSpace:"nowrap",width:"1px"}}},69391:(e,o,r)=>{r.d(o,{D7:()=>A,Ud:()=>S,WO:()=>u,X1:()=>d,dg:()=>function e(o){return a().mapValues(o,(o,r)=>{if(a().isString(o)||a().isNumber(o)||a().isBoolean(o)){if(a().isString(o)&&a().endsWith(o,"!important"))return o;if(a().isNumber(o)){if(t.A[r])return`${o}!important`;return`${o}px!important`}return`${o}!important`}if(a().isNil(o))return o;return e(o)})},hG:()=>H,yu:()=>s});var t=r(32646),n=r(25656),a=r.n(n),l=r(72646),i=r(37590),c=r(37402);function d(e,o,r){if(e&&o&&Object(e.colors).hasOwnProperty(c.I[o]))return e.colors[c.I[o]];return null!=r?r:e.colors.textPrimary}function s(e,o){let{errorColor:r,warningColor:t,successColor:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};switch(o){case"error":return r||e.colors.actionDangerPrimaryBackgroundDefault;case"warning":return t||e.colors.textValidationWarning;case"success":return n||e.colors.textValidationSuccess;default:return}}function u(e,o){if(!e||!e.isDarkMode)return{};return{border:`1px solid ${e.colors.borderDecorative}`,...o?{}:{boxShadow:"none"}}}let g=e=>`linear-gradient(${e} 30%, rgba(0, 0, 0, 0)) center top`,p=e=>`linear-gradient(rgba(0, 0, 0, 0), ${e} 70%) center bottom`,h=e=>`linear-gradient(to left, rgba(0, 0, 0, 0), ${e} 30%) left center`,f=e=>`linear-gradient(to left, ${e} 70%, rgba(0, 0, 0, 0)) right center`,m=e=>`radial-gradient(
  farthest-side at 50% 0,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) center top`,v=e=>`radial-gradient(
  farthest-side at 50% 100%,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) center bottom`,b=e=>`radial-gradient(
  farthest-side at 0 50%,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) left center`,y=e=>`radial-gradient(
  farthest-side at 100% 50%,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) right center`,w="100% 40px",x="40px 100%",C="100% 14px",k="14px 100%",D=(0,n.memoize)(function(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"vertical",t=null!=o?o:e.colors.backgroundPrimary,n=e.isDarkMode?l.Az:l.aH;if("horizontal"===r)return{background:`
            ${h(t)},
            ${f(t)},
            ${b(n)},
            ${y(n)}`,backgroundRepeat:"no-repeat",backgroundSize:`
            ${x},
            ${x},
            ${k},
            ${k}`,backgroundAttachment:"local, local, scroll, scroll"};return{background:`
          ${g(t)},
          ${p(t)},
          ${m(n)},
          ${v(n)}`,backgroundRepeat:"no-repeat",backgroundSize:`
          ${w},
          ${w},
          ${C},
          ${C}`,backgroundAttachment:"local, local, scroll, scroll"}},(e,o,r)=>`${e.isDarkMode}-${o}-${r}`),S=function(e){let{backgroundColor:o,orientation:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return D(e,o,r)},B=(0,n.memoize)(function(e,o){let r=null!=o?o:e.colors.backgroundPrimary;return{background:`
          ${p(r)},
          ${v(e.isDarkMode?l.Az:l.aH)}`,backgroundRepeat:"no-repeat",backgroundSize:`
          ${w},
          ${C}`,backgroundAttachment:"local, scroll"}}),H=function(e){let{backgroundColor:o}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return B(e,o)};function A(e){let o=i.GE[0]+i.GE[2],r=i.GE[1]+i.GE[3];return{position:"absolute",width:`calc(100% - ${r}px)`,height:`${e.size-o}px`,transform:`translateY(${e.start}px)`}}},94167:(e,o,r)=>{r.d(o,{De:()=>i,VG:()=>l});var t=r(71218),n=r(58829);let a=e=>(0,t.AH)({outline:`1px dashed ${(e.isDarkMode,e.colors.lime)}`,outlineOffset:"2px"},"","");function l(){return(0,n.W)("databricks.fe.designsystem.showDebugOutline",!1)?{"data-dubois-show-outline":!0}:{}}function i(e){return(0,n.W)("databricks.fe.designsystem.showDebugOutline",!1)?a(e):{}}},8467:(e,o,r)=>{r.d(o,{p:()=>n});var t=r(58829);let n=()=>({useNewShadows:(0,t.W)("databricks.fe.designsystem.useNewShadows",!1),useNewFormUISpacing:(0,t.W)("databricks.fe.designsystem.useNewFormUISpacing",!1),useNewBorderRadii:(0,t.W)("databricks.fe.designsystem.useNewBorderRadii",!1),useNewLargeAlertSizing:(0,t.W)("databricks.fe.designsystem.useNewLargeAlertSizing",!1)})},58829:(e,o,r)=>{r.d(o,{W:()=>t});let t=(e,o)=>{try{let r=window.__debug__safex;return r?r(e,o):o}catch(e){return o}}},29121:(e,o,r)=>{r.d(o,{s:()=>n});var t=r(65848);let n=e=>{let{callback:o,allowBasicEnter:r,allowPlatformEnter:n}=e,a=(0,t.useMemo)(()=>navigator.userAgent.includes("Mac"),[]),l=(0,t.useRef)(!1),i=(0,t.useCallback)(()=>{l.current=!0},[]),c=(0,t.useCallback)(()=>{l.current=!1},[]);return{onKeyDown:(0,t.useCallback)(e=>{if("Enter"!==e.key||l.current||!r&&!n)return;let t=r&&!e.metaKey&&!e.ctrlKey&&!e.shiftKey&&!e.altKey,i=n&&(a?e.metaKey:e.ctrlKey);(t||i)&&o(e)},[r,n,o,a]),onCompositionEnd:c,onCompositionStart:i}}},80842:(e,o,r)=>{r.d(o,{z:()=>i});var t=r(65848);let n=null,a=new WeakMap,l=()=>{n||(n=new IntersectionObserver(e=>{e.forEach(e=>{if(e.isIntersecting){let o=a.get(e.target);o&&(o(),a.delete(e.target))}})}))},i=e=>{let{onView:o,resetKey:r,value:i}=e,c=(0,t.useRef)(!1),d=(0,t.useRef)(null),s=(0,t.useRef)(r);return(0,t.useEffect)(()=>{s.current!==r&&(c.current=!1,s.current=r)},[r]),(0,t.useEffect)(()=>{let e=d.current;if(!e||c.current)return;let r=()=>{c.current=!0,o(i)};if(!window.IntersectionObserver){r();return}return function(e,o){var r;return l(),a.set(e,o),null===(r=n)||void 0===r||r.observe(e),()=>{if(e){var o;null===(o=n)||void 0===o||o.unobserve(e),a.delete(e)}}}(e,r)},[o,i]),{elementRef:d}}},71009:(e,o,r)=>{r.d(o,{C:()=>i,Y:()=>c});var t=r(65848),n=r(80842),a=r(98358);let l=(0,t.createContext)({isViewed:!1,emitOnFirstView:()=>{}}),i=e=>{let{children:o}=e,r=(0,t.useRef)(!1);return(0,a.Y)(l.Provider,{value:{isViewed:r.current,emitOnFirstView:()=>{r.current=!0}},children:o})},c=e=>{let{onView:o,value:r}=e,{isViewed:a,emitOnFirstView:i}=(0,t.useContext)(l),c=(0,t.useCallback)(()=>{a||(o(r),i())},[o,a,r,i]);return(0,n.z)({onView:c})}},60499:(e,o,r)=>{r.d(o,{Y:()=>a});var t=r(65848);let n=()=>new Date().getTime()+Array(16).fill("").map(()=>parseInt((10*Math.random()).toString())).join("");function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return(0,t.useState)(()=>`${e}-${n()}`)[0]}},46792:(e,o,r)=>{r.d(o,{xW:()=>l});var t=r(65848),n=r.n(t);let a=n().createContext({componentId:void 0,isSubmitting:!1,formRef:void 0}),l=()=>n().useContext(a)}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/427.e0f9eac2.chunk.js.map