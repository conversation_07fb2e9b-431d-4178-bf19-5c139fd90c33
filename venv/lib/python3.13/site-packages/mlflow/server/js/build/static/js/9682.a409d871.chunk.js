"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[9682],{1323:function(e,t,n){n.d(t,{m:function(){return a}});var o=n(48012),i=n(50361),s=n(50111);const a=e=>{let{sourceType:t,className:n}=e;return t===i.Y1.NOTEBOOK?(0,s.Y)(o.Trs,{className:n}):t===i.Y1.LOCAL?(0,s.Y)(o.bM2,{className:n}):t===i.Y1.PROJECT?(0,s.Y)(o.wd0,{className:n}):t===i.Y1.JOB?(0,s.Y)(o.mCV,{className:n}):null}},8986:function(e,t,n){n.d(t,{G:function(){return s}});var o=n(39416);function i(e){if(void 0!==e)return"string"===typeof e||e instanceof FormData||e instanceof Blob?e:JSON.stringify(e)}const s=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",n=arguments.length>2?arguments[2]:void 0;const s=await fetch(e,{method:t,body:i(n),headers:n?{"Content-Type":"application/json"}:{}});if(!s.ok){const e=(0,o.a$)(s);if(e){try{const t=(await s.json()).message;e.message=null!==t&&void 0!==t?t:e.message}catch{}throw e}}return s.json()}},14343:function(e,t,n){n.d(t,{k:function(){return r}});var o=n(89555),i=n(31014),s=n(32599),a=n(50111);const r=i.forwardRef(((e,t)=>{const{pressed:n,onClick:i,icon:r,onBlur:l,onFocus:c,onMouseEnter:d,onMouseLeave:u,componentId:g,analyticsEvents:m,type:p,...h}=e,{theme:f}=(0,s.u)(),v=(0,s.f)({componentType:s.h.Button,componentId:g,analyticsEvents:null!==m&&void 0!==m?m:[s.e.OnClick]});return(0,a.Y)("button",{onClick:e=>{v.onClick(e),null===i||void 0===i||i(e)},css:(0,o.AH)({cursor:"pointer",width:f.general.heightSm,height:f.general.heightSm,borderRadius:f.legacyBorders.borderRadiusMd,lineHeight:f.typography.lineHeightBase,padding:0,border:0,display:"flex",alignItems:"center",justifyContent:"center",background:n?f.colors.actionDefaultBackgroundPress:"transparent",color:n?f.colors.actionDefaultTextPress:f.colors.textSecondary,"&:hover":{background:f.colors.actionDefaultBackgroundHover,color:f.colors.actionDefaultTextHover}},""),ref:t,onBlur:l,onFocus:c,onMouseEnter:d,onMouseLeave:u,...h,children:r})}))},15164:function(e,t,n){n.d(t,{t:function(){return d}});var o=n(89555),i=n(31014),s=n(76010),a=n(32599),r=n(1323),l=n(50111);var c={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"};const d=i.memo((e=>{var t;let{value:n}=e;const{theme:i}=(0,a.u)();if(!n)return(0,l.Y)(l.FK,{children:"-"});const d=(null===(t=n[s.A.sourceTypeTag])||void 0===t?void 0:t.value)||"",u=s.A.renderSource(n||{},void 0,void 0);return u?(0,l.FD)("div",{css:(0,o.AH)({display:"flex",gap:i.spacing.xs,alignItems:"center"},""),children:[(0,l.Y)(r.m,{sourceType:d,css:(0,o.AH)({color:i.colors.textSecondary},"")}),(0,l.Y)("span",{css:c,children:u})]}):(0,l.Y)(l.FK,{children:"-"})}))},18476:function(e,t,n){n.d(t,{R:function(){return i},V:function(){return s}});var o=n(32378);const i=(e,t)=>e.digest===t.digest&&e.name===t.name&&e.context===t.context,s=e=>{const{dataset:t}=e,n=t.sourceType;try{if(n===o.Fv.HTTP){const{url:e}=JSON.parse(t.source);return e}if(n===o.Fv.S3){const{uri:e}=JSON.parse(t.source);return e}if(n===o.Fv.HUGGING_FACE){const{path:e}=JSON.parse(t.source);return`https://huggingface.co/datasets/${e}`}}catch{return null}return null}},21317:function(e,t,n){n.d(t,{h:function(){return u}});var o,i,s,a,r,l=n(31014);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},c.apply(null,arguments)}function d(e,t){let{title:n,titleId:d,...u}=e;return l.createElement("svg",c({width:19,height:16,viewBox:"0 0 19 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":d},u),n?l.createElement("title",{id:d},n):null,l.createElement("g",{clipPath:"url(#clip0_0_3)"},l.createElement("mask",{id:"mask0_0_3",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:0,y:0,width:16,height:16},o||(o=l.createElement("path",{d:"M16 0H0V16H16V0Z",fill:"white"}))),i||(i=l.createElement("g",{mask:"url(#mask0_0_3)"},l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.75 3.49999C2.05964 3.49999 1.5 4.05963 1.5 4.74999C1.5 5.44034 2.05964 5.99999 2.75 5.99999C3.44036 5.99999 4 5.44034 4 4.74999C4 4.05963 3.44036 3.49999 2.75 3.49999ZM0 4.74999C0 3.2312 1.23122 1.99999 2.75 1.99999C3.77682 1.99999 4.6722 2.56276 5.14452 3.39669L9.51655 2.44626C9.66772 1.0704 10.8338 0 12.25 0C13.7688 0 15 1.23122 15 2.75C15 3.87686 14.3222 4.84541 13.3521 5.27025L13.6341 7.52661C14.9711 7.71349 16 8.86158 16 10.25C16 11.7688 14.7687 13 13.25 13C12.3895 13 11.6214 12.6048 11.1172 11.9861L8.49749 13.1322C8.49913 13.1713 8.49997 13.2105 8.49997 13.25C8.49996 14.7688 7.26875 16 5.74997 16C4.23118 16 2.99997 14.7688 2.99996 13.25C2.99997 12.3569 3.42568 11.5633 4.08524 11.0609L3.01322 7.48755C2.92659 7.49578 2.83878 7.49999 2.75 7.49999C1.23122 7.49999 0 6.26877 0 4.74999ZM5.46534 5.18782C5.48277 5.07884 5.4938 4.96773 5.49804 4.85488L9.76409 3.92748C10.1528 4.74671 10.9346 5.34321 11.8658 5.47338L12.1478 7.72974C11.7709 7.89483 11.438 8.14204 11.1719 8.44873L5.46534 5.18782ZM4.82802 6.55126C4.70399 6.69422 4.56546 6.82424 4.41471 6.93906L5.48674 10.5124C5.57337 10.5042 5.66118 10.5 5.74997 10.5C6.69483 10.5 7.52839 10.9765 8.02345 11.7023L10.5231 10.6087C10.5079 10.4913 10.5 10.3716 10.5 10.25C10.5 10.101 10.5118 9.95475 10.5346 9.81218L4.82802 6.55126ZM12 10.25C12 9.55963 12.5596 8.99999 13.25 8.99999C13.9403 8.99999 14.5 9.55963 14.5 10.25C14.5 10.9403 13.9403 11.5 13.25 11.5C12.5596 11.5 12 10.9403 12 10.25ZM5.74997 12C5.05961 12 4.49997 12.5596 4.49997 13.25C4.49997 13.9403 5.05961 14.5 5.74997 14.5C6.44032 14.5 6.99997 13.9403 6.99997 13.25C6.99997 12.5596 6.44032 12 5.74997 12ZM11 2.75C11 2.05964 11.5596 1.5 12.25 1.5C12.9403 1.5 13.5 2.05964 13.5 2.75C13.5 3.44036 12.9403 4 12.25 4C11.5596 4 11 3.44036 11 2.75Z",fill:"currentColor"}))),s||(s=l.createElement("path",{d:"M13.5 14C15.433 14 17 12.433 17 10.5C17 8.567 15.433 7 13.5 7C11.567 7 10 8.567 10 10.5C10 12.433 11.567 14 13.5 14Z",fill:"white"})),a||(a=l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.125 11C11.125 9.41218 12.4122 8.125 14 8.125C15.5878 8.125 16.875 9.41218 16.875 11C16.875 12.5878 15.5878 13.875 14 13.875C12.4122 13.875 11.125 12.5878 11.125 11ZM14 6.625C11.5838 6.625 9.625 8.58375 9.625 11C9.625 13.4162 11.5838 15.375 14 15.375C16.4162 15.375 18.375 13.4162 18.375 11C18.375 8.58375 16.4162 6.625 14 6.625ZM14.0303 12.5303L16.0303 10.5303L14.9697 9.46967L13.5 10.9393L13.0303 10.4697L11.9697 11.5303L12.9697 12.5303L13.5 13.0607L14.0303 12.5303Z",fill:"#3CAA60"}))),r||(r=l.createElement("defs",null,l.createElement("clipPath",{id:"clip0_0_3"},l.createElement("rect",{width:19,height:16,fill:"white"})))))}const u=l.forwardRef(d);n.p},32614:function(e,t,n){n.d(t,{A:function(){return o}});class o{static getStoreForComponent(e,t){return new i([e,t].join("-"),"localStorage")}static getSessionScopedStoreForComponent(e,t){return new i([e,t].join("-"),"sessionStorage")}}o.version="1.1";class i{constructor(e,t){this.scope=void 0,this.storageObj=void 0,this.scope=e,this.storageObj="localStorage"===t?window.localStorage:window.sessionStorage}loadComponentState(){const e=this.getItem(i.reactComponentStateKey);return e?JSON.parse(e):{}}saveComponentState(e){const t="function"===typeof e.toJSON?e.toJSON():e;this.setItem(i.reactComponentStateKey,JSON.stringify(t))}withScopePrefix(e){return["MLflowLocalStorage",o.version,this.scope,e].join("-")}setItem(e,t){this.storageObj.setItem(this.withScopePrefix(e),t)}getItem(e){return this.storageObj.getItem(this.withScopePrefix(e))}}i.reactComponentStateKey="ReactComponentState"},38243:function(e,t,n){n.d(t,{E:function(){return c}});var o=n(89555),i=n(32599),s=n(48012),a=(n(31014),n(25866)),r=n(50111);var l={name:"1ykowef",styles:"margin-bottom:0"};const c=e=>{var t;let{datasetWithTags:n,displayTextAsLink:c,className:d}=e;const{dataset:u,tags:g}=n,{theme:m}=(0,i.u)(),p=null===g||void 0===g||null===(t=g.find((e=>{let{key:t}=e;return t===a.AS})))||void 0===t?void 0:t.value;return(0,r.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",alignItems:"center",marginTop:m.spacing.xs,marginBottom:m.spacing.xs},""),className:d,children:[(0,r.Y)(s.KbA,{css:(0,o.AH)({marginRight:m.spacing.xs,color:m.colors.textSecondary},"")}),c?(0,r.FD)("div",{children:[u.name," (",u.digest,")"]}):(0,r.FD)(i.T.Text,{size:"md",css:l,children:[u.name," (",u.digest,")"]}),p&&(0,r.Y)(s.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetwithcontext.tsx_41",css:(0,o.AH)({textTransform:"capitalize",marginLeft:m.spacing.xs,marginRight:m.spacing.xs},""),children:p})]})}},40555:function(e,t,n){n.d(t,{f:function(){return u},y:function(){return l}});var o=n(89555),i=n(31014),s=n(37616),a=n(9133),r=n(50111);const l=e=>{let{json:t,wrapperStyle:n,overlayStyle:i,codeSnippetStyle:a}=e;const{formattedJson:l,isJsonContent:d}=c(t);return(0,r.Y)("div",{style:{position:"relative",maxHeight:"calc(1.5em * 9)",overflow:"hidden",...n},children:d?(0,r.FD)(r.FK,{children:[(0,r.Y)(s.z7,{language:"json",style:{padding:"5px",overflowX:"hidden",...a},children:l}),(0,r.Y)("div",{css:(0,o.AH)({position:"absolute",bottom:0,right:0,left:6,height:"2em",background:"linear-gradient(transparent, white)",...i},"")})]}):(0,r.Y)(r.FK,{children:t})})};function c(e){return i.useMemo((()=>{try{const t=JSON.parse(e),n=(0,a.isObject)(t)&&"function"!==typeof t&&!(t instanceof Date);return{formattedJson:n?JSON.stringify(t,null,2):e,isJsonContent:n}}catch(t){return{formattedJson:e,isJsonContent:!1}}}),[e])}var d={name:"1089mxj",styles:"white-space:pre-wrap"};const u=e=>{let{json:t}=e;const{formattedJson:n,isJsonContent:o}=c(t);return(0,r.Y)("div",{css:d,children:o?(0,r.Y)(s.z7,{language:"json",wrapLongLines:!0,children:n}):(0,r.Y)("span",{children:t})})}},46536:function(e,t,n){n.d(t,{_:function(){return a.A},b:function(){return c}});var o=n(48012),i=n(32599),s=n(31014),a=n(10298),r=n(50111);const l={rotateLeft:(0,r.Y)(o.ejX,{}),rotateRight:(0,r.Y)(o.UfX,{}),zoomIn:(0,r.Y)(o.ADv,{}),zoomOut:(0,r.Y)(o.LoD,{}),close:(0,r.Y)(i.C,{}),left:(0,r.Y)(o.A60,{}),right:(0,r.Y)(o.flY,{})},c=e=>{let{children:t,visible:n,onVisibleChange:o}=e;const{getPopupContainer:c}=(0,s.useContext)(i.al);return(0,r.Y)(a.A.PreviewGroup,{icons:l,preview:{visible:n,getContainer:c,onVisibleChange:e=>o(e)},children:t})}},51455:function(e,t,n){n.d(t,{A:function(){return a},S:function(){return r}});var o=n(31014),i=n(50111);const s=(0,o.createContext)({}),a=e=>{let{children:t,introductionText:n,displayVersionWarnings:o}=e;return(0,i.Y)(s.Provider,{value:{introductionText:n,displayVersionWarnings:o},children:t})},r=()=>(0,o.useContext)(s)},54421:function(e,t,n){n.d(t,{Mc:function(){return u},Rn:function(){return p},TV:function(){return m},mQ:function(){return x}});var o=n(89555),i=n(32599),s=n(48012),a=n(88443),r=n(77484),l=n(31014),c=n(46536),d=n(50111);const u=200;var g={name:"49aokf",styles:"display:contents"};const m=e=>{let{imageUrl:t,compressedImageUrl:n,imageSize:s,maxImageSize:a}=e;const[r,u]=(0,l.useState)(!1),{theme:m}=(0,i.u)(),[p,h]=(0,l.useState)(!0);return(0,l.useEffect)((()=>{h(!0);const e=new window.Image;return e.onload=()=>h(!1),e.onerror=()=>h(!1),e.src=n,()=>{e.src=""}}),[n]),(0,d.Y)("div",{css:(0,o.AH)({width:s||"100%",height:s||"100%"},""),children:(0,d.Y)("div",{css:g,children:void 0===n||p?(0,d.Y)("div",{css:(0,o.AH)({width:"100%",backgroundColor:m.colors.backgroundSecondary,display:"flex",aspectRatio:"1",justifyContent:"center",alignItems:"center"},""),children:(0,d.Y)(i.S,{})}):(0,d.Y)("div",{css:(0,o.AH)({display:"flex",alignItems:"center",justifyContent:"center",width:s||"100%",aspectRatio:"1",maxWidth:a,maxHeight:a,backgroundColor:m.colors.backgroundSecondary,".rc-image":{cursor:"pointer"}},""),children:(0,d.Y)(c.b,{visible:r,onVisibleChange:u,children:(0,d.Y)(c._,{src:n,preview:{src:t},style:{maxWidth:a||"100%",maxHeight:a||"100%"}})})})})})},p=e=>{let{metadataByStep:t,imageSize:n,step:l,runUuid:c}=e;const{theme:u}=(0,i.u)();return void 0===t[l]?(0,d.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",width:n,backgroundColor:u.colors.backgroundSecondary,padding:u.spacing.md,aspectRatio:"1"},""),children:[(0,d.Y)(s.xfq,{}),(0,d.Y)(a.A,{id:"0XZ2zu",defaultMessage:"No image logged at this step"})]}):(0,d.Y)(m,{imageUrl:(0,r.To)(t[l].filepath,c),compressedImageUrl:(0,r.To)(t[l].compressed_filepath,c),imageSize:n})};var h={name:"1pf7ok1",styles:"display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%;width:100%;font-size:16px"},f={name:"yz1nei",styles:"margin-top:16px"},v={name:"18uqayh",styles:"margin-bottom:16px"};const x=()=>(0,d.FD)("div",{css:h,children:[(0,d.Y)(i.T.Title,{css:f,color:"secondary",level:3,children:"Compare logged images"}),(0,d.Y)(i.T.Text,{css:v,color:"secondary",children:"Use the image grid chart to compare logged images across runs."})]})},79432:function(e,t,n){n.d(t,{E:function(){return u}});var o=n(89555),i=n(9133),s=n(31014),a=n(98655),r=n(32599),l=n(50111);const c="@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)";var d={name:"13bnb9v",styles:"appearance:none;width:0;height:0;border:0;padding:0;position:absolute;bottom:0;visibility:hidden"};const u=e=>{var t;let{color:n,hidden:u,onChangeColor:g,...m}=e;const[p,h]=(0,s.useState)(void 0),f=(0,s.useMemo)((()=>g?(0,i.debounce)(g,300):()=>{}),[g]);return(0,l.FD)("label",{css:(0,o.AH)({width:12,height:12,borderRadius:6,flexShrink:0,border:"1px solid "+(u?"transparent":"rgba(0,0,0,0.1)"),[c]:{marginBottom:1},background:u?"repeating-linear-gradient(\n  135deg,\n  #959595 0,\n  #e7e7e7 1px,\n  #e7e7e7 2px,\n  #959595 3px,\n  #e7e7e7 4px,\n  #e7e7e7 5px,\n  #959595 6px,\n  #e7e7e7 7px,\n  #e7e7e7 8px,\n  #959595 9px,\n  #e7e7e7 10px,\n  #e7e7e7 11px,\n  #959595 12px,\n  #e7e7e7 13px,\n  #e7e7e7 14px\n)":void 0,cursor:g?"pointer":"default",position:"relative","&:hover":{opacity:g?.8:1}},""),style:{backgroundColor:null!==(t=null!==p&&void 0!==p?p:n)&&void 0!==t?t:"transparent"},...m,children:[(0,l.Y)("span",{css:[r.O,"user-select:none;"],children:n}),g&&(0,l.Y)("input",{disabled:u,type:"color",value:null!==p&&void 0!==p?p:n,onChange:e=>{let{target:t}=e;h(t.value),f(t.value)},list:a.U,css:d})]})}},80171:function(e,t,n){n.d(t,{Ab:function(){return x},CF:function(){return p},Db:function(){return m},Mq:function(){return u},Nh:function(){return s},YO:function(){return v},_H:function(){return h},cS:function(){return r},dY:function(){return g},h:function(){return d},oj:function(){return c},se:function(){return f},y:function(){return l}});var o=n(69526),i=n(9133);const s="mlflow.traceName",a=(e,t)=>{var n,o;return null===(n=e.request_metadata)||void 0===n||null===(o=n.find((e=>{let{key:n}=e;return n===t})))||void 0===o?void 0:o.value},r=e=>e.length>=250,l=e=>a(e,"mlflow.sourceRun"),c=e=>a(e,"total_tokens"),d=e=>{const t=a(e,"mlflow.traceInputs");if(!(0,i.isNil)(t))try{return JSON.stringify(JSON.parse(t))}catch(n){return t}},u=e=>{const t=a(e,"mlflow.traceOutputs");if(!(0,i.isNil)(t))try{return JSON.stringify(JSON.parse(t))}catch(n){return t}},g=(e,t)=>{var n,o,i;return Array.isArray(e.tags)?null===(o=e.tags)||void 0===o||null===(i=o.find((e=>{let{key:n}=e;return n===t})))||void 0===i?void 0:i.value:null===(n=e.tags)||void 0===n?void 0:n[t]},m=e=>g(e,s)||e.request_id,p=["timestamp_ms"],h="select";let f=function(e){return e.requestId="request_id",e.traceName="traceName",e.timestampMs="timestamp_ms",e.inputs="inputs",e.outputs="outputs",e.runName="runName",e.totalTokens="total_tokens",e.source="source",e.latency="latency",e.tags="tags",e.status="status",e}({});const v={[f.requestId]:(0,o.zR)({id:"0HqSXX",defaultMessage:"Request ID"}),[f.traceName]:(0,o.zR)({id:"60C/WF",defaultMessage:"Trace name"}),[f.timestampMs]:(0,o.zR)({id:"V3BImd",defaultMessage:"Time created"}),[f.status]:(0,o.zR)({id:"PBkS4s",defaultMessage:"Status"}),[f.inputs]:(0,o.zR)({id:"TQZ+X6",defaultMessage:"Request"}),[f.outputs]:(0,o.zR)({id:"pHAqL+",defaultMessage:"Response"}),[f.runName]:(0,o.zR)({id:"yltHdB",defaultMessage:"Run name"}),[f.totalTokens]:(0,o.zR)({id:"O5FKCr",defaultMessage:"Tokens"}),[f.source]:(0,o.zR)({id:"eM4+ab",defaultMessage:"Source"}),[f.latency]:(0,o.zR)({id:"6oYktw",defaultMessage:"Execution time"}),[f.tags]:(0,o.zR)({id:"4qVc2+",defaultMessage:"Tags"})},x={UNSET:null,IN_PROGRESS:(0,o.zR)({id:"y56Vz9",defaultMessage:"In progress"}),OK:(0,o.zR)({id:"Mn7Kss",defaultMessage:"OK"}),ERROR:(0,o.zR)({id:"igK5Su",defaultMessage:"Error"})}},81313:function(e,t,n){function o(e,t,n){if(void 0===t||null===t||"string"!==typeof t)return n;for(const o in e)if(e[o]===t)return e[o];return n}n.d(t,{wB:function(){return v},bw:function(){return x},u8:function(){return g},SK:function(){return o}});var i=n(89555),s=n(32599),a=n(48012),r=n(31014),l=n(88443);var c=function(e,t){const[n,o]=(0,r.useState)(null);return(0,r.useEffect)((()=>{if(e.current&&t){const n=()=>{if(!e.current)return;const n=e.current.offsetWidth,i=Object.keys(t).filter((e=>t[e]>=n)).sort(((e,n)=>t[e]-t[n]))[0];o(i)};n();const i=new ResizeObserver(n);return i.observe(e.current),()=>i.disconnect()}}),[e,t]),n},d=n(50111);const u={sm:316,lg:480},g=e=>{let{isLoading:t,secondarySections:n,children:o,isTabLayout:l=!0,sidebarSize:g="sm",verticalStackOrder:p}=e;const{theme:h}=(0,s.u)(),f=(0,r.useRef)(null),v="small"===c(f,{small:h.responsive.breakpoints.lg}),x="primary-first"===p,w=u[g],y=w-16,_=v?x?{width:"100%"}:{borderBottom:`1px solid ${h.colors.border}`,width:"100%"}:x?{width:y}:{paddingBottom:h.spacing.sm,width:y};return(0,d.FD)("div",{"data-testid":"entity-overview-container",ref:f,css:(0,i.AH)({display:"flex",flexDirection:v?x?"column":"column-reverse":"row",gap:h.spacing.lg},""),children:[(0,d.Y)("div",{css:(0,i.AH)({display:"flex",flexGrow:1,flexDirection:"column",gap:h.spacing.md,width:v?"100%":`calc(100% - ${w}px)`},""),children:t?(0,d.Y)(a.xUE,{}):o}),(0,d.Y)("div",{style:{display:"flex",...l&&{marginTop:-h.spacing.md}},children:(0,d.FD)("div",{css:(0,i.AH)({display:"flex",flexDirection:"column",gap:h.spacing.lg,..._},""),children:[t&&(0,d.Y)(a.xUE,{}),!t&&(0,d.Y)(m,{secondarySections:n})]})})]})},m=e=>{let{secondarySections:t}=e;return(0,d.Y)("div",{children:t.filter((e=>null!==e)).filter((e=>null!==(null===e||void 0===e?void 0:e.content))).map(((e,t)=>{let{title:n,isTitleLoading:o,content:i,id:s}=e;return(0,d.Y)(h,{title:n,isTitleLoading:o,content:i,index:t},s)}))})},p=e=>{let{children:t}=e;const{theme:n}=(0,s.u)();return(0,d.Y)(s.T.Title,{level:4,style:{whiteSpace:"nowrap",marginRight:n.spacing.lg,marginTop:0},children:t})},h=e=>{let{title:t,content:n,index:o,isTitleLoading:r=!1}=e;const{theme:c}=(0,s.u)(),u=r?(0,d.Y)(a.I_K,{label:(0,d.Y)(l.A,{id:"KJqjTs",defaultMessage:"Section title loading"})}):t?(0,d.Y)(p,{children:t}):null,g={padding:`${c.spacing.md}px 0 ${c.spacing.md}px 0`};return(0,d.FD)("div",{css:(0,i.AH)({...g,...0===o?{}:{borderTop:`1px solid ${c.colors.border}`}},""),children:[u,n]})};var f={name:"10x9q33",styles:"flex:1;align-self:start;overflow:hidden"};const v=e=>{let{keyValue:t,value:n,maxWidth:o}=e;const{theme:a}=(0,s.u)();return(0,d.FD)("div",{css:(0,i.AH)({display:"flex",alignItems:"center","&:has(+ div)":{marginBottom:a.spacing.xs},maxWidth:null!==o&&void 0!==o?o:450,wordBreak:"break-word",lineHeight:a.typography.lineHeightLg},""),children:[(0,d.Y)("div",{css:(0,i.AH)({color:a.colors.textSecondary,flex:.5,alignSelf:"start"},""),children:t}),(0,d.Y)("div",{css:f,children:n})]})},x=()=>(0,d.Y)(s.T.Text,{color:"secondary",children:(0,d.Y)(l.A,{id:"8EQFzg",defaultMessage:"None"})})},85343:function(e,t,n){n.d(t,{O:function(){return B}});var o=n(89555),i=n(31014),s=n(32599),a=n(48012),r=n(15579),l=n(25866),c=n(88443),d=n(50111);var u={name:"1d3w5wq",styles:"width:100%"};const g=e=>{let{schema:t,filter:n}=e;const o=t.filter(((e,t)=>{return o=e.name,i=e.type,""===n||(null===o||void 0===o?void 0:o.toLowerCase().includes(n.toLowerCase()))||(null===i||void 0===i?void 0:i.toLowerCase().includes(n.toLowerCase()));var o,i}));return(0,d.FD)(a.XIK,{scrollable:!0,css:u,children:[(0,d.FD)(a.Hjg,{isHeader:!0,children:[(0,d.Y)(a.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetschematable.tsx_57",children:(0,d.Y)(c.A,{id:"rCMgRJ",defaultMessage:"Name"})}),(0,d.Y)(a.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetschematable.tsx_58",children:(0,d.Y)(c.A,{id:"a0NgR8",defaultMessage:"Type"})})]}),(0,d.Y)("div",{onWheel:e=>e.stopPropagation(),children:0===o.length?(0,d.Y)(a.Hjg,{children:(0,d.Y)(a.nA6,{children:(0,d.Y)(c.A,{id:"dl3Yfr",defaultMessage:"No results match this search."})})}):o.map(((e,t)=>(0,d.FD)(a.Hjg,{children:[(0,d.Y)(a.nA6,{children:e.name}),(0,d.Y)(a.nA6,{children:e.type})]},`table-body-row-${t}`)))})]})};var m={name:"zyllbl",styles:"display:flex;flex-direction:column;height:100vh"},p={name:"1bnvdh6",styles:"height:100%;display:flex;flex-direction:column;justify-content:flex-start;align-content:center"},h={name:"84euy4",styles:"display:flex;flex-direction:column;overflow:hidden;height:100vh"},f={name:"1d3w5wq",styles:"width:100%"},v={name:"s1n8ed",styles:"height:100vh"},x={name:"p03wc2",styles:"display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%"},w={name:"1azakc",styles:"text-align:center"},y={name:"1r9xdqo",styles:"display:flex;flex-direction:column;justify-content:flex-start"},_={name:"1r9xdqo",styles:"display:flex;flex-direction:column;justify-content:flex-start"};const Y=e=>{let{datasetWithTags:t}=e;const{theme:n}=(0,s.u)(),{dataset:r}=t,[l,u]=(0,i.useState)("");if(null===r.schema||""===r.schema)return(0,d.Y)("div",{css:m,children:(0,d.Y)("div",{css:p,children:(0,d.Y)(a.Y9Y,{title:(0,d.Y)("div",{css:(0,o.AH)({color:n.colors.grey600},""),children:"No schema available"})})})});try{const e=JSON.parse(r.schema);return"mlflow_colspec"in e?(0,d.FD)("div",{css:h,children:[(0,d.Y)("div",{css:(0,o.AH)({marginTop:n.spacing.sm,form:{width:"100%"}},""),children:(0,d.Y)(a.z2z,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetschema.tsx_92",value:l,placeholder:"Search fields",onChange:e=>u(e.target.value),onClear:()=>{u("")},css:f,containerProps:{style:{width:"auto"}}})}),(0,d.Y)("div",{css:(0,o.AH)({marginTop:n.spacing.sm,overflow:"hidden"},""),children:(0,d.Y)(g,{schema:e.mlflow_colspec,filter:l})})]}):"mlflow_tensorspec"in e?(0,d.Y)("div",{css:v,children:(0,d.FD)("div",{css:x,children:[(0,d.Y)(a.KbA,{css:(0,o.AH)({fontSize:"56px",color:n.colors.grey600},"")}),(0,d.Y)(a.Y9Y,{title:(0,d.Y)("div",{css:(0,o.AH)({color:n.colors.grey600},""),children:"Array Datasource"})}),(0,d.Y)(s.T.Text,{color:n.colors.grey600,css:w,children:(0,d.Y)(c.A,{id:"sDqD2S",defaultMessage:"The dataset is an array. To see a preview of the dataset, view the dataset in the training notebook."})})]})}):(0,d.Y)("div",{css:(0,o.AH)({marginLeft:n.spacing.lg,marginTop:n.spacing.md,width:"100%"},""),children:(0,d.FD)("div",{css:y,children:[(0,d.Y)(a.Y9Y,{title:(0,d.Y)("div",{css:(0,o.AH)({color:n.colors.grey600},""),children:"Unrecognized Schema Format"})}),(0,d.FD)(s.T.Text,{color:n.colors.grey600,children:[(0,d.Y)(c.A,{id:"T/UYwm",defaultMessage:"Raw Schema JSON: "}),JSON.stringify(e)]})]})})}catch{return(0,d.Y)("div",{css:(0,o.AH)({marginLeft:n.spacing.lg,marginTop:n.spacing.md,width:"100%"},""),children:(0,d.Y)("div",{css:_,children:(0,d.Y)(a.Y9Y,{title:(0,d.Y)("div",{css:(0,o.AH)({color:n.colors.grey600},""),children:"No schema available"})})})})}};var b=n(32378),S=n(18476),A=n(56412);const C=e=>{let{datasetWithTags:t,runTags:n}=e;const{dataset:o}=t;if(o.sourceType===b.Fv.HTTP||o.sourceType===b.Fv.HUGGING_FACE){const e=(0,S.V)(t);if(e)return(0,d.Y)(s.B,{type:"primary",componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetlink.tsx_19_1",icon:(0,d.Y)(s.at,{}),href:e,target:"_blank",children:(0,d.Y)(c.A,{id:"Vvn8Cb",defaultMessage:"Open dataset"})})}if(o.sourceType===b.Fv.S3){const e=(0,S.V)(t);if(e)return(0,d.Y)(A.i,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetlink.tsx_19_2",icon:(0,d.Y)(a.TdU,{}),copyText:e,children:(0,d.Y)(c.A,{id:"SZj964",defaultMessage:"Copy S3 URI to clipboard"})})}return o.sourceType===b.Fv.EXTERNAL?(0,d.Y)(s.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetlink.tsx_19_3",icon:(0,d.Y)(s.at,{}),children:(0,d.Y)(c.A,{id:"tbAlJg",defaultMessage:"Go to external location"})}):null};var T=n(93215),k=n(58481),I=n(38243),M=n(79432);const R=e=>{let{datasetWithTags:t}=e;const{dataset:n}=t,o=n.sourceType,i=o===b.Fv.HTTP||o===b.Fv.EXTERNAL?(0,d.Y)(c.A,{id:"Aqq08o",defaultMessage:"HTTP"}):o===b.Fv.S3?(0,d.Y)(c.A,{id:"K5rmCE",defaultMessage:"S3"}):o===b.Fv.HUGGING_FACE?(0,d.Y)(c.A,{id:"JuHp/q",defaultMessage:"Hugging Face"}):null;return i?(0,d.Y)(s.T.Hint,{children:(0,d.Y)(c.A,{id:"g2tInh",defaultMessage:"Source type: {typeLabel}",values:{typeLabel:i}})}):null};var F={name:"andpxo",styles:"display:flex;overflow:hidden"},E={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"},N={name:"l8l8b8",styles:"white-space:nowrap;overflow:hidden;text-overflow:ellipsis"};const P=e=>{let{datasetWithTags:t}=e;const{dataset:n}=t,{theme:i}=(0,s.u)(),a=n.sourceType;if(a===b.Fv.HTTP||a===b.Fv.EXTERNAL||a===b.Fv.HUGGING_FACE){const e=(0,S.V)(t);if(e)return(0,d.FD)("div",{css:(0,o.AH)({whiteSpace:"nowrap",display:"flex",fontSize:i.typography.fontSizeSm,color:i.colors.textSecondary,columnGap:i.spacing.xs},""),title:e,children:["URL:"," ",(0,d.Y)(s.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetsourceurl.tsx_34",openInNewTab:!0,href:e,css:F,children:(0,d.Y)("span",{css:E,children:e})})]})}if(a===b.Fv.S3)try{const{uri:e}=JSON.parse(n.source);if(e)return(0,d.FD)(s.T.Hint,{title:e,css:N,children:["S3 URI: ",e]})}catch{return null}return null},D=e=>{let{datasetWithTags:t}=e;const{dataset:n}=t;return(0,d.Y)(s.T.Hint,{children:(0,d.Y)(c.A,{id:"WeAgwI",defaultMessage:"Digest: {digest}",values:{digest:(0,d.Y)("code",{children:n.digest})}})})};var L=n(70403);var H={name:"197dxto",styles:"display:flex;align-items:center;height:100%"},O={name:"10fhuyv",styles:"height:100%;display:flex;overflow:auto"},q={name:"1jf8xyb",styles:"display:flex;flex-direction:column;overflow:visible;flex:1"},z={name:"82a6rk",styles:"flex:1"},U={name:"fhxb3m",styles:"display:flex;flex-direction:row;align-items:center"},$={name:"1fu4rcv",styles:"margin-bottom:0;max-width:200px"};const W=e=>{var t;let{isOpen:n,setIsOpen:i,selectedDatasetWithRun:u,setSelectedDatasetWithRun:g}=e;const{theme:m}=(0,s.u)(),{datasetWithTags:p,runData:h}=u,f=u?null===p||void 0===p||null===(t=p.tags)||void 0===t?void 0:t.find((e=>e.key===l.AS)):void 0,v=p.dataset.profile&&"null"!==p.dataset.profile?p.dataset.profile:void 0,x=(0,L.LE)(),{experimentId:w="",tags:y={}}=h;return(0,d.Y)(a._s.Root,{open:n,onOpenChange:e=>{e||i(!1)},children:(0,d.Y)(a._s.Content,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetdrawer.tsx_81",title:(0,d.FD)("div",{css:H,children:[(0,d.Y)(s.T.Title,{level:4,css:(0,o.AH)({marginRight:m.spacing.sm,marginBottom:0},""),children:(0,d.Y)(c.A,{id:"obI80o",defaultMessage:"Data details for "})}),(0,d.FD)(T.N_,{to:k.h.getRunPageRoute(w,h.runUuid),css:K.runLink,children:[(0,d.Y)(M.E,{color:x(h.runUuid)}),(0,d.Y)("span",{css:K.runName,children:h.runName})]})]}),width:"800px",footer:(0,d.Y)(r.S,{size:"xs"}),children:(0,d.FD)("div",{css:(0,o.AH)({display:"flex",borderTop:`1px solid ${m.colors.border}`,height:"100%",marginLeft:-m.spacing.sm},""),children:[(0,d.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",width:"300px",borderRight:`1px solid ${m.colors.border}`,height:"100%"},""),children:[(0,d.FD)(s.T.Text,{color:"secondary",css:(0,o.AH)({marginBottom:m.spacing.sm,marginTop:m.spacing.sm,paddingLeft:m.spacing.sm},""),children:[h.datasets.length," ",(0,d.Y)(c.A,{id:"vK1v9d",defaultMessage:"datasets used"})]}),(0,d.Y)("div",{css:O,onWheel:e=>e.stopPropagation(),children:(0,d.Y)("div",{css:q,children:h.datasets.map((e=>{return(0,d.Y)(s.T.Link,{componentId:"mlflow.dataset_drawer.dataset_link","aria-label":`${e.dataset.name} (${e.dataset.digest})`,css:(0,o.AH)({display:"flex",whiteSpace:"nowrap",textDecoration:"none",cursor:"pointer",flexDirection:"column",justifyContent:"center",alignItems:"flex-start",backgroundColor:(t=e,n=p,t.dataset.digest===n.dataset.digest&&t.dataset.name===n.dataset.name?m.colors.actionTertiaryBackgroundPress:"transparent"),paddingBottom:m.spacing.sm,paddingTop:m.spacing.sm,paddingLeft:m.spacing.sm,border:0,borderTop:`1px solid ${m.colors.border}`,"&:hover":{backgroundColor:m.colors.actionTertiaryBackgroundHover}},""),onClick:()=>{g({datasetWithTags:e,runData:h}),i(!0)},children:(0,d.Y)(I.E,{datasetWithTags:e,displayTextAsLink:!1})},`${e.dataset.name}-${e.dataset.digest}`);var t,n}))})})]}),(0,d.FD)("div",{css:(0,o.AH)({overflow:"hidden",paddingLeft:m.spacing.md,paddingTop:m.spacing.md,display:"flex",flexDirection:"column",width:"100%"},""),children:[(0,d.FD)("div",{css:(0,o.AH)({display:"flex",gap:m.spacing.sm},""),children:[(0,d.FD)("div",{css:z,children:[(0,d.Y)(a.Y9Y,{title:(0,d.FD)("div",{css:U,children:[(0,d.Y)(a.KbA,{css:(0,o.AH)({marginRight:m.spacing.xs},"")}),(0,d.Y)(a.paO,{title:p.dataset.name,children:(0,d.Y)(s.T.Title,{ellipsis:!0,level:3,css:$,children:p.dataset.name})}),f&&(0,d.Y)(a.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetdrawer.tsx_206",css:(0,o.AH)({textTransform:"capitalize",marginLeft:m.spacing.xs,marginRight:m.spacing.xs},""),children:f.value})]})}),(0,d.Y)(s.T.Title,{level:4,color:"secondary",css:(0,o.AH)({marginBottom:m.spacing.xs,marginTop:m.spacing.xs},""),title:v,children:p.dataset.profile&&"null"!==p.dataset.profile?p.dataset.profile.length>80?`${p.dataset.profile.substring(0,80)} ...`:p.dataset.profile:(0,d.Y)(c.A,{id:"uq6CTI",defaultMessage:"No profile available"})})]}),(0,d.Y)(C,{datasetWithTags:p,runTags:y})]}),(0,d.FD)("div",{css:(0,o.AH)({flexShrink:0,display:"flex",flexDirection:"column",gap:m.spacing.xs},""),children:[(0,d.Y)(D,{datasetWithTags:p}),(0,d.Y)(R,{datasetWithTags:p}),(0,d.Y)(P,{datasetWithTags:p})]}),(0,d.Y)("div",{css:(0,o.AH)({marginTop:m.spacing.sm,marginBottom:m.spacing.xs,borderTop:`1px solid ${m.colors.border}`,opacity:.5},"")}),(0,d.Y)(Y,{datasetWithTags:p})]})]})})})},B=i.memo(W),K={runLink:{overflow:"hidden",display:"flex",gap:8,alignItems:"center"},runName:{overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"}}},91089:function(e,t,n){n.d(t,{O:function(){return Me}});var o=n(89555),i=n(32599),s=n(31014),a=n(63528),r=n(80171),l=n(50361),c=n(9133),d=n(91144);const u="request_metadata.`mlflow.sourceRun`",g="request_metadata.`mlflow.modelId`",m=e=>`run_id IN (${e.map((e=>`'${e}'`)).join(",")})`,p=e=>{let{experimentIds:t,sorting:n,filter:o="",runUuid:i,loggedModelId:p}=e;const[h,f]=(0,s.useState)([]),[v,x]=(0,s.useState)(!0),[w,y]=(0,s.useState)(void 0),_=(0,s.useMemo)((()=>{const e=(0,c.first)(n);return e&&r.CF.includes(e.id)?`${e.id} ${e.desc?"DESC":"ASC"}`:"timestamp_ms DESC"}),[n]),Y=(0,s.useMemo)((()=>i||p?(0,d.Dz)()&&p?o?`${o} AND ${g}='${p}'`:`${g}='${p}'`:o?`${o} AND ${u}='${i}'`:`${u}='${i}'`:o),[o,i,p]),[b,S]=(0,s.useState)({0:void 0}),[A,C]=(0,s.useState)(0),T=b[A],k=(0,s.useCallback)((async e=>{let{experimentIds:t,currentPage:n=0,pageToken:o,silent:i,orderByString:s="",filterString:d=""}=e;i||x(!0),y(void 0);try{const e=await a.x.getExperimentTraces(t,s,o,d);if(!e.traces)return void f([]);const i=await(async(e,t)=>{const n=t.reduce(((e,t)=>{const n=t.request_id,o=(0,r.y)(t);return n&&o?{...e,[n]:o}:e}),{}),o=(0,c.uniq)((0,c.values)(n));if(o.length<1)return{};const i=((await a.x.searchRuns({experiment_ids:e,filter:m(o),run_view_type:l.qi.ALL})).runs||[]).reduce(((e,t)=>({...e,[t.info.runUuid]:t.info.runName})),{});return t.reduce(((e,t)=>{const o=t.request_id;if(!o)return e;const s=n[o];return{...e,[o]:i[s]||s}}),{})})(t,e.traces),u=e.traces.map((e=>{const t=e.request_id;if(!t)return{...e};const n=i[t];return{...e,runName:n}}));f(u),S((t=>({...t,[n+1]:e.next_page_token})))}catch(u){y(u)}finally{x(!1)}}),[]),I=!v&&void 0!==b[A+1],M=!v&&(1===A||void 0!==b[A-1]);(0,s.useEffect)((()=>{k({experimentIds:t,filterString:Y,orderByString:_})}),[k,Y,t,_]);const R=(0,s.useCallback)((()=>{f([]),S({0:void 0}),C(0),k({experimentIds:t})}),[k,t]),F=(0,s.useCallback)((()=>{C((e=>e+1)),k({experimentIds:t,currentPage:A+1,pageToken:b[A+1],filterString:Y,orderByString:_})}),[t,A,k,b,Y,_]),E=(0,s.useCallback)((()=>{C((e=>e-1)),k({experimentIds:t,currentPage:A-1,pageToken:b[A-1],filterString:Y,orderByString:_})}),[t,A,k,b,Y,_]),N=(0,s.useCallback)((function(){return k({experimentIds:t,currentPage:A,pageToken:T,silent:arguments.length>0&&void 0!==arguments[0]&&arguments[0],filterString:Y,orderByString:_})}),[t,A,k,T,Y,_]);return{traces:h,loading:v,error:w,hasNextPage:I,hasPreviousPage:M,fetchNextPage:F,fetchPrevPage:E,refreshCurrentPage:N,reset:R}};var h=n(48012),f=n(70618),v=n(9856),x=n(76010),w=n(93215),y=n(52350),_=n(58481),Y=n(88464),b=n(88443),S=n(98590),A=n(98597),C=n(50111);var T={name:"1wcfv52",styles:"margin-right:0"},k={name:"14cnl6e",styles:"flex-shrink:0;opacity:0;[role=row]:hover &{opacity:1;}[role=row]:focus-within &{opacity:1;}"};const I=e=>{let{onAddEditTags:t,tags:n,baseComponentId:s}=e;const{theme:a}=(0,i.u)(),r=(null===n||void 0===n?void 0:n.filter((e=>{let{key:t}=e;return t&&!t.startsWith(S.nt)})))||[],l=r.length>0;return(0,C.FD)("div",{css:(0,o.AH)({display:"flex",alignItems:"center",flexWrap:"wrap",columnGap:a.spacing.xs,rowGap:a.spacing.xs},""),children:[r.map((e=>(0,C.Y)(A.t,{tag:e,css:T,charLimit:20,maxWidth:150,enableFullViewModal:!0},e.key)))," ",(0,C.Y)(i.B,{componentId:`${s}.traces_table.edit_tag`,size:"small",icon:l?(0,C.Y)(h.R2l,{}):void 0,onClick:t,children:l?void 0:(0,C.Y)(b.A,{id:"vHNP+J",defaultMessage:"Add tags"}),css:k,type:"tertiary"})]})};var M=n(41028);const R=(e,t)=>"IN_PROGRESS"===e?(0,C.Y)(M.C,{css:(0,o.AH)({color:t.colors.textValidationWarning},"")}):"OK"===e?(0,C.Y)(h.C1y,{css:(0,o.AH)({color:t.colors.textValidationSuccess},"")}):"ERROR"===e?(0,C.Y)(h.qhh,{css:(0,o.AH)({color:t.colors.textValidationDanger},"")}):null,F=e=>{let{row:{original:t}}=e;const{theme:n}=(0,i.u)(),s=(0,Y.A)(),a=r.Ab[t.status||"UNSET"];return(0,C.FD)("div",{css:(0,o.AH)({display:"flex",gap:n.spacing.xs,alignItems:"center"},""),children:[R(t.status,n),a?s.formatMessage(a):""]})};var E=n(37616);const N={name:"rthgne",styles:"display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical"};var P={name:"ozd7xs",styles:"flex-shrink:0"};const D=e=>{let{value:t,traceId:n,previewFieldName:l}=e;const{theme:d}=(0,i.u)(),[u,g]=(0,s.useState)(!1),[m,p]=(0,s.useState)(null),[f,v]=(0,s.useState)(!1),w=(0,s.useCallback)((async()=>{v(!0);try{const e=await a.x.getExperimentTraceData(n);if(l in e){const t=e[l],n=(0,c.isString)(t)?t:JSON.stringify(t);p(n)}}catch(e){const t=e instanceof y.s?e.getUserVisibleError():e.message;x.A.logErrorAndNotifyUser(`Error fetching response: ${t}`)}v(!1)}),[l,n]),_=(0,r.cS)(t),Y=(0,s.useCallback)((async()=>{!m&&_&&await w(),g(!0)}),[m,w,_]),b=(0,s.useCallback)((()=>{g(!1)}),[]);return(0,C.FD)("div",{css:(0,o.AH)({display:"flex",gap:d.spacing.xs},""),children:[(0,C.Y)(i.B,{componentId:"mlflow.traces.traces_table.expand_cell_preview",size:"small",icon:u?(0,C.Y)(h.D3D,{}):(0,C.Y)(i.q,{}),onClick:u?b:Y,css:P,loading:f,type:"primary"}),(0,C.Y)("div",{title:t,css:["overflow:hidden;text-overflow:ellipsis;",!u&&N,""],children:u?(0,C.Y)(L,{value:null!==m&&void 0!==m?m:t}):t})]})},L=e=>{let{value:t}=e;const{theme:n}=(0,i.u)(),a=(0,s.useMemo)((()=>{try{const e=JSON.parse(t);return JSON.stringify(e,null,2)}catch(e){return null}}),[t]);return(0,C.Y)("div",{css:(0,o.AH)({whiteSpace:"pre-wrap",wordBreak:"break-word",fontFamily:a?"monospace":void 0},""),children:(0,C.Y)(E.z7,{language:"json",wrapLongLines:!0,style:{padding:n.spacing.sm},theme:n.isDarkMode?"duotoneDark":"light",children:a||t})})},H=e=>{let{row:{original:t}}=e;return(0,C.Y)(D,{previewFieldName:"request",traceId:t.request_id||"",value:(0,r.h)(t)||""})},O=e=>{let{row:{original:t}}=e;return(0,C.Y)(D,{previewFieldName:"response",traceId:t.request_id||"",value:(0,r.Mq)(t)||""})};var q=n(15164);const z=e=>{let{row:{original:t}}=e;return(0,C.Y)(q.t,{value:(0,c.keyBy)(t.tags,"key")})},U=e=>`--header-${e}-size`,$=e=>`--col-${e}-size`,W=s.memo((e=>{let{row:t}=e;const{theme:n}=(0,i.u)();return(0,C.Y)("div",{role:"row","data-testid":"endpoints-list-table-rows",css:(0,o.AH)({minHeight:n.general.buttonHeight,display:"flex",flexDirection:"row",":hover":{backgroundColor:"var(--table-row-hover)"},paddingRight:"32px",borderBottom:"1px solid var(--table-separator-color)"},""),children:t.getAllCells().map((e=>{var t,o;const i=null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.multiline,s=e.column.id===r._H?n.spacing.sm:`${n.spacing.sm}px ${n.spacing.xs}px`;return(0,C.Y)("div",{role:"cell",css:[{"--table-row-vertical-padding":`${n.spacing.sm}px`,flex:`calc(var(${$(e.column.id)}) / 100)`,overflow:"hidden",whiteSpace:i?"pre-wrap":"nowrap",textOverflow:i?"ellipsis":void 0,padding:s},null===(o=e.column.columnDef.meta)||void 0===o?void 0:o.styles,""],children:(0,f.Kv)(e.column.columnDef.cell,e.getContext())},e.id)}))},t.id)}),((e,t)=>e.columns===t.columns&&e.selected===t.selected&&(0,c.isEqual)(e.row.original.tags,t.row.original.tags))),B=s.memo((e=>{let{row:{original:t}}=e;return t.timestamp_ms?(0,C.Y)(h.paO,{title:new Date(t.timestamp_ms).toLocaleString(navigator.language,{timeZoneName:"short"}),placement:"right",children:(0,C.Y)("span",{children:x.A.timeSinceStr(t.timestamp_ms)})}):null}),(()=>!0)),K=e=>{let{table:t}=e;const n=t.getIsAllRowsSelected()||!!t.getIsSomeRowsSelected()&&null;return(0,C.Y)(h.Sc0,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtableheadercheckbox.tsx_14","data-testid":"trace-table-header-checkbox",isChecked:n,wrapperStyle:{padding:0,margin:0},onChange:t.toggleAllRowsSelected})},j=e=>{let{row:t}=e;return(0,C.Y)(h.Sc0,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtablecellcheckbox.tsx_12","data-testid":`trace-table-cell-checkbox-${t.id}`,disabled:!t.getCanSelect(),isChecked:t.getIsSelected(),wrapperStyle:{padding:0,margin:0},onChange:()=>t.toggleSelected()})};var V=n(56412);const J={openai:{minVersion:"2.15.1",getContent:()=>(0,C.Y)(b.A,{id:"smtq2M",defaultMessage:"Automatically log traces for OpenAI API calls by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.openai.autolog()"})}}),getCodeSource:()=>'from openai import OpenAI\n\nmlflow.openai.autolog()\n\n# Ensure that the "OPENAI_API_KEY" environment variable is set\nclient = OpenAI()\n\nmessages = [\n  {"role": "system", "content": "You are a helpful assistant."},\n  {"role": "user", "content": "Hello!"}\n]\n\n# Inputs and outputs of the API request will be logged in a trace\nclient.chat.completions.create(model="gpt-4o-mini", messages=messages)'},langchain:{minVersion:"2.17.2",getContent:()=>(0,C.Y)(b.A,{id:"7/urtn",defaultMessage:"Automatically log traces for LangChain or LangGraph invocations by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.langchain.autolog()"})}}),getCodeSource:()=>'from langchain_openai import OpenAI\nfrom langchain_core.prompts import PromptTemplate\n\nmlflow.langchain.autolog()\n\n# Ensure that the "OPENAI_API_KEY" environment variable is set\nllm = OpenAI()\nprompt = PromptTemplate.from_template("Answer the following question: {question}")\nchain = prompt | llm\n\n# Invoking the chain will cause a trace to be logged\nchain.invoke("What is MLflow?")'},llama_index:{minVersion:"2.15.1",getContent:()=>(0,C.Y)(b.A,{id:"/v6KuF",defaultMessage:"Automatically log traces for LlamaIndex queries by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.llama_index.autolog()"})}}),getCodeSource:()=>'from llama_index.core import Document, VectorStoreIndex\n\nmlflow.llama_index.autolog()\n\n# Ensure that the "OPENAI_API_KEY" environment variable is set\nindex = VectorStoreIndex.from_documents([Document.example()])\nquery_engine = index.as_query_engine()\n\n# Querying the engine will cause a trace to be logged\nquery_engine.query("What is LlamaIndex?")'},dspy:{minVersion:"2.18.0",getContent:()=>(0,C.Y)(b.A,{id:"h1HQ14",defaultMessage:"Automatically log traces for DSPy executions by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.dspy.autolog()"})}}),getCodeSource:()=>'import dspy\n\nmlflow.dspy.autolog()\n\n# Configure the LLM to use. Please ensure that\n# the OPENAI_API_KEY environment variable is set\nlm = dspy.LM("openai/gpt-4o-mini")\ndspy.configure(lm=lm)\n\n# Define a simple chain-of-thought model and run it\nmath = dspy.ChainOfThought("question -> answer: float")\nquestion = "Two dice are tossed. What is the probability that the sum equals two?"\n\n# All intermediate outputs from the execution will be logged\nmath(question=question)'},crewai:{minVersion:"2.19.0",getContent:()=>(0,C.Y)(b.A,{id:"K8LdMX",defaultMessage:"Automatically log traces for CrewAI executions by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.crewai.autolog()"})}}),getCodeSource:()=>'from crewai import Agent, Crew, Process, Task\n\nmlflow.crewai.autolog()\n\ncity_selection_agent = Agent(\n    role="City selection expert",\n    goal="Select the best city based on weather, season, and prices",\n    backstory="An expert in analyzing travel data to pick ideal destinations",\n    allow_delegation=True,\n    verbose=True,\n)\n\nlocal_expert = Agent(\n    role="Local expert",\n    goal="Provide the best insights about the selected city",\n    backstory="A local guide with extensive information about the city",\n    verbose=True,\n)\n  \nplan_trip = Task(\n    name="Plan a trip",\n    description="""Plan a trip to a city based on weather, prices, and best local attractions. \n    Please consult with a local expert when researching things to do.""",\n    expected_output="A short summary of the trip destination and key things to do",\n    agent=city_selection_agent,\n)\n\ncrew = Crew(\n  agents=[\n    city_selection_agent,\n    local_expert,\n  ],\n  tasks=[plan_trip],\n  process=Process.sequential\n)\n\n# Ensure the "OPENAI_API_KEY" environment variable is set\n# before kicking off the crew. All intermediate agent outputs\n# will be logged in the resulting trace.\ncrew.kickoff()'},autogen:{minVersion:"2.16.2",getContent:()=>(0,C.Y)(b.A,{id:"i/pJvo",defaultMessage:"Automatically log traces for AutoGen conversations by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.autogen.autolog()"})}}),getCodeSource:()=>'import os\nfrom autogen import AssistantAgent, UserProxyAgent\n\nmlflow.autogen.autolog()\n\n# Ensure that the "OPENAI_API_KEY" environment variable is set\nllm_config = { "model": "gpt-4o-mini", "api_key": os.environ["OPENAI_API_KEY"] }\nassistant = AssistantAgent("assistant", llm_config = llm_config)\nuser_proxy = UserProxyAgent("user_proxy", code_execution_config = False)\n\n# All intermediate executions within the chat session will be logged\nuser_proxy.initiate_chat(assistant, message = "What is MLflow?", max_turns = 1)'},anthropic:{minVersion:"2.19.0",getContent:()=>(0,C.Y)(b.A,{id:"AzOnmT",defaultMessage:"Automatically log traces for Anthropic API calls by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.anthropic.autolog()"})}}),getCodeSource:()=>'import os\nimport anthropic\n\n# Enable auto-tracing for Anthropic\nmlflow.anthropic.autolog()\n\n# Configure your API key (please ensure that the "ANTHROPIC_API_KEY" environment variable is set)\nclient = anthropic.Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"])\n\n# Inputs and outputs of API calls will be logged as a trace\nmessage = client.messages.create(\n    model="claude-3-5-sonnet-20241022",\n    max_tokens=1024,\n    messages=[\n        {"role": "user", "content": "Hello, Claude"},\n    ],\n)'},bedrock:{minVersion:"2.20.0",getContent:()=>(0,C.Y)(b.A,{id:"6tKW1I",defaultMessage:"Automatically log traces for Bedrock conversations by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.bedrock.autolog()"})}}),getCodeSource:()=>'import boto3\n\nmlflow.bedrock.autolog()\n\n# Ensure that your boto3 client has the necessary auth information\nbedrock = boto3.client(\n    service_name="bedrock-runtime",\n    region_name="<REPLACE_WITH_YOUR_AWS_REGION>",\n)\n\nmodel = "anthropic.claude-3-5-sonnet-20241022-v2:0"\nmessages = [{ "role": "user", "content": [{"text": "Hello!"}]}]\n\n# All intermediate executions within the chat session will be logged\nbedrock.converse(modelId=model, messages=messages)'},litellm:{minVersion:"2.18.0",getContent:()=>(0,C.Y)(b.A,{id:"D7SSDK",defaultMessage:"Automatically log traces for LiteLLM API calls by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.litellm.autolog()"})}}),getCodeSource:()=>'import litellm\n\nmlflow.litellm.autolog()\n\n# Ensure that the "OPENAI_API_KEY" environment variable is set\nmessages = [{"role": "user", "content": "Hello!"}]\n\n# Inputs and outputs of the API request will be logged in a trace\nlitellm.completion(model="gpt-4o-mini", messages=messages)'},gemini:{minVersion:"2.18.0",getContent:()=>(0,C.Y)(b.A,{id:"IsIgE2",defaultMessage:"Automatically log traces for Gemini conversations by calling the {code} function. For example:",values:{code:(0,C.Y)("code",{children:"mlflow.gemini.autolog()"})}}),getCodeSource:()=>'import google.genai as genai\n\nmlflow.gemini.autolog()\n\n# Replace "GEMINI_API_KEY" with your API key\nclient = genai.Client(api_key="GEMINI_API_KEY")\n\n# Inputs and outputs of the API request will be logged in a trace\nclient.models.generate_content(model="gemini-1.5-flash", contents="Hello!")'},custom:{minVersion:"2.14.3",getContent:e=>(0,C.FD)(C.FK,{children:[(0,C.Y)(i.T.Paragraph,{children:(0,C.Y)(b.A,{id:"3Z6K+n",defaultMessage:"To manually instrument your own traces, the most convenient method is to use the {code} function decorator. This will cause the inputs and outputs of the function to be captured in the trace.",values:{code:(0,C.Y)("code",{children:"@mlflow.trace"})}})}),(0,C.Y)(i.T.Paragraph,{children:(0,C.Y)(b.A,{id:"WNz02j",defaultMessage:"For more complex use cases, MLflow also provides granular APIs that can be used to control tracing behavior. For more information, please visit the <a>official documentation</a> on fluent and client APIs for MLflow Tracing.",values:{a:t=>(0,C.Y)(i.T.Link,{title:"official documentation",componentId:`${e}.traces_table.custom_tracing_docs_link`,href:"https://mlflow.org/docs/latest/llms/tracing/index.html#tracing-fluent-apis",openInNewTab:!0,children:t})}})})]}),getCodeSource:()=>'@mlflow.trace\ndef foo(a):\nreturn a + bar(a)\n\n# Various attributes can be passed to the decorator\n# to modify the information contained in the span\<EMAIL>(name = "custom_name", attributes = { "key": "value" })\ndef bar(b):\nreturn b + 1\n\n# Invoking the traced function will cause a trace to be logged\nfoo(1)'}};var G=n(51455);var Z={name:"1hyob9y",styles:"position:relative;width:min-content"};const X=e=>{let{flavorName:t,baseComponentId:n}=e;const{theme:s}=(0,i.u)(),{getContent:a,getCodeSource:r,minVersion:l}=J[t],{displayVersionWarnings:c=!0}=(0,G.S)(),d=a(n),u=`import mlflow\nfrom packaging.version import Version\n\nassert Version(mlflow.__version__) >= Version("${l}"), (\n  "This feature requires MLflow version ${l} or newer. "\n  "Please run '%pip install -U mlflow' in a notebook cell, "\n  "and restart the kernel when the command finishes."\n)\n\n`+r(),g=(0,C.Y)(b.A,{id:"YfspJh",defaultMessage:"This example requires MLflow version {minVersion} or newer. Please run {installCommand} in a notebook cell if your MLflow version is older than this, and restart the kernel when the command finishes.",values:{minVersion:l,installCommand:(0,C.Y)(i.T.Text,{code:!0,children:"%pip install -U mlflow"})}});return(0,C.FD)("div",{children:[c&&(0,C.Y)(h.FcD,{componentId:`${n}.traces_table.${t}_quickstart_alert`,css:(0,o.AH)({marginBottom:s.spacing.md},""),closable:!1,message:(0,C.Y)(b.A,{id:"NoYMjZ",defaultMessage:"Requires MLflow >= {minVersion}",values:{minVersion:l}}),description:g,type:"info"}),(0,C.Y)(i.T.Text,{children:d}),(0,C.FD)("div",{css:Z,children:[(0,C.Y)(V.i,{componentId:`${n}.traces_table.${t}_quickstart_snippet_copy`,css:(0,o.AH)({zIndex:1,position:"absolute",top:s.spacing.xs,right:s.spacing.xs},""),showLabel:!1,copyText:u,icon:(0,C.Y)(h.TdU,{})}),(0,C.Y)(E.z7,{showLineNumbers:!0,theme:s.isDarkMode?"duotoneDark":"light",style:{padding:`${s.spacing.sm}px ${s.spacing.md}px`,marginTop:s.spacing.md},language:"python",children:u})]})]})},Q=e=>{let{baseComponentId:t,experimentIds:n,runUuid:s}=e;const{theme:a}=(0,i.u)(),{introductionText:r}=(0,G.S)();return(0,C.FD)("div",{css:(0,o.AH)({marginLeft:-a.spacing.md},""),children:[(0,C.Y)(h.Y9Y,{title:(0,C.Y)(b.A,{id:"6d5JTO",defaultMessage:"No traces recorded"}),titleElementLevel:3}),(0,C.Y)(i.T.Text,{css:(0,o.AH)({display:"block",marginTop:a.spacing.md,marginBottom:a.spacing.md},""),children:null!==r&&void 0!==r?r:(0,C.Y)(b.A,{id:"jLvmck",defaultMessage:"This tab displays all the traces logged to this {isRun, select, true {run} other {experiment}}. MLflow supports automatic tracing for many popular generative AI frameworks. Follow the steps below to log your first trace. For more information about MLflow Tracing, visit the <a>MLflow documentation</a>.",values:{isRun:!(0,c.isNil)(s),a:e=>(0,C.Y)(i.T.Link,{componentId:`${t}.traces_table.quickstart_docs_link`,href:"https://mlflow.org/docs/latest/llms/tracing/index.html",openInNewTab:!0,children:e})}})}),(0,C.FD)(h.tUM.Root,{componentId:`${t}.traces_table.quickstart`,defaultValue:"openai",children:[(0,C.FD)(h.tUM.List,{children:[(0,C.Y)(h.tUM.Trigger,{value:"openai",children:(0,C.Y)(b.A,{id:"n4m7x9",defaultMessage:"OpenAI"})}),(0,C.Y)(h.tUM.Trigger,{value:"langchain",children:(0,C.Y)(b.A,{id:"aseJdC",defaultMessage:"LangChain / LangGraph"})}),(0,C.Y)(h.tUM.Trigger,{value:"llama_index",children:(0,C.Y)(b.A,{id:"7UncLb",defaultMessage:"LlamaIndex"})}),(0,C.Y)(h.tUM.Trigger,{value:"dspy",children:(0,C.Y)(b.A,{id:"44f6Jl",defaultMessage:"DSPy"})}),(0,C.Y)(h.tUM.Trigger,{value:"crewai",children:(0,C.Y)(b.A,{id:"vUxxkd",defaultMessage:"CrewAI"})}),(0,C.Y)(h.tUM.Trigger,{value:"autogen",children:(0,C.Y)(b.A,{id:"nPqKse",defaultMessage:"AutoGen"})}),(0,C.Y)(h.tUM.Trigger,{value:"anthropic",children:(0,C.Y)(b.A,{id:"9DDsiU",defaultMessage:"Anthropic"})}),(0,C.Y)(h.tUM.Trigger,{value:"bedrock",children:(0,C.Y)(b.A,{id:"QqY6kD",defaultMessage:"Bedrock"})}),(0,C.Y)(h.tUM.Trigger,{value:"litellm",children:(0,C.Y)(b.A,{id:"ouTk0h",defaultMessage:"LiteLLM"})}),(0,C.Y)(h.tUM.Trigger,{value:"gemini",children:(0,C.Y)(b.A,{id:"Sviop4",defaultMessage:"Gemini"})}),(0,C.Y)(h.tUM.Trigger,{value:"custom",children:(0,C.Y)(b.A,{id:"+iY2p8",defaultMessage:"Custom"})})]}),(0,c.keys)(J).map((e=>(0,C.Y)(h.tUM.Content,{value:e,children:(0,C.Y)(X,{flavorName:e,baseComponentId:t})},e+"_content")))]})]})};var ee={name:"s09txq",styles:"max-width:100%;text-overflow:ellipsis"};const te=e=>{let{row:{original:t},table:{options:{meta:n}}}=e;const{baseComponentId:o,onTraceClicked:s}=n;return(0,C.Y)(i.T.Link,{componentId:`${o}.traces_table.request_id_link`,ellipsis:!0,css:ee,onClick:()=>{null===s||void 0===s||s(t)},children:t.request_id})};var ne={name:"s09txq",styles:"max-width:100%;text-overflow:ellipsis"};const oe=e=>{let{row:{original:t},table:{options:{meta:n}}}=e;const{baseComponentId:o,onTraceClicked:s}=n;return(0,C.Y)(i.T.Link,{componentId:`${o}.traces_table.trace_name_link`,ellipsis:!0,css:ne,onClick:()=>{null===s||void 0===s||s(t)},children:(0,r.dY)(t,r.Nh)})};var ie={name:"hgn0g7",styles:"max-width:100%;text-overflow:ellipsis;display:inline-block;overflow:hidden"};const se=e=>{let{row:{original:t}}=e;const n=(0,r.y)(t);if(!n||!t.experiment_id)return null;const o=t.runName||n;return(0,C.Y)(w.N_,{css:ie,to:_.h.getRunPageRoute(t.experiment_id,n),children:o})},ae=e=>{let{row:{original:t},table:{options:{meta:n}}}=e;const{onTraceTagsEdit:o,baseComponentId:i}=n;return(0,C.Y)(I,{tags:t.tags||[],onAddEditTags:()=>null===o||void 0===o?void 0:o(t),baseComponentId:i})};var re={name:"s09txq",styles:"max-width:100%;text-overflow:ellipsis"},le={name:"s09txq",styles:"max-width:100%;text-overflow:ellipsis"},ce={name:"hgn0g7",styles:"max-width:100%;text-overflow:ellipsis;display:inline-block;overflow:hidden"};const de=s.memo((e=>{let{experimentIds:t,runUuid:n,traces:o,loading:a,error:l,onTraceClicked:u,onTraceTagsEdit:g,hasNextPage:m,hasPreviousPage:p,onNextPage:S,onPreviousPage:A,usingFilters:T,onResetFilters:k,sorting:M,setSorting:R,rowSelection:E,setRowSelection:N,hiddenColumns:P=[],disableTokenColumn:D,baseComponentId:L,toggleHiddenColumn:q,disabledColumns:V=[]}=e;const J=(0,Y.A)(),{theme:G}=(0,i.u)(),Z=(0,d.rR)(),X=(0,s.useMemo)((()=>(0,c.entries)(r.YO).map((e=>{let[t,n]=e;return{key:t,label:J.formatMessage(n)}})).filter((e=>{let{key:t}=e;return!V.includes(t)}))),[J,V]),ee=(0,s.useMemo)((()=>{const e=[{id:r._H,header:K,enableResizing:!1,enableSorting:!1,cell:j,meta:{styles:{minWidth:32,maxWidth:32}}},{header:J.formatMessage(r.YO[r.se.requestId]),enableSorting:!1,enableResizing:!0,id:r.se.requestId,cell:Z?te:e=>{let{row:{original:t}}=e;return(0,C.Y)(i.T.Link,{componentId:`${L}.traces_table.request_id_link`,ellipsis:!0,css:re,onClick:()=>{null===u||void 0===u||u(t)},children:t.request_id})},meta:{styles:{minWidth:200}}},{header:J.formatMessage(r.YO[r.se.traceName]),enableSorting:!1,enableResizing:!0,id:r.se.traceName,cell:Z?oe:e=>{let{row:{original:t}}=e;return(0,C.Y)(i.T.Link,{componentId:`${L}.traces_table.trace_name_link`,ellipsis:!0,css:le,onClick:()=>{null===u||void 0===u||u(t)},children:(0,r.dY)(t,r.Nh)})},meta:{styles:{minWidth:150}}},{header:J.formatMessage(r.YO[r.se.timestampMs]),id:r.se.timestampMs,accessorFn:e=>e.timestamp_ms,enableSorting:!0,enableResizing:!0,cell:B,meta:{styles:{minWidth:100}}},{header:J.formatMessage(r.YO[r.se.status]),id:r.se.status,enableSorting:!1,enableResizing:!0,cell:F,meta:{styles:{minWidth:100}}},{header:J.formatMessage(r.YO[r.se.inputs]),id:r.se.inputs,enableSorting:!1,enableResizing:!0,cell:H,meta:{multiline:!0}},{header:J.formatMessage(r.YO[r.se.outputs]),enableSorting:!1,enableResizing:!0,id:r.se.outputs,cell:O,meta:{multiline:!0}},{header:J.formatMessage(r.YO[r.se.runName]),enableSorting:!1,enableResizing:!0,id:r.se.runName,cell:Z?se:e=>{let{row:{original:t}}=e;const n=(0,r.y)(t);if(!n||!t.experiment_id)return null;const o=t.runName||n;return(0,C.Y)(w.N_,{css:ce,to:_.h.getRunPageRoute(t.experiment_id,n),children:o})}},{header:J.formatMessage(r.YO[r.se.source]),enableSorting:!0,enableResizing:!0,id:r.se.source,cell:z,meta:{styles:{minWidth:100}}}];return D||e.push({header:J.formatMessage(r.YO[r.se.totalTokens]),enableSorting:!1,enableResizing:!0,id:r.se.totalTokens,accessorFn:e=>(0,r.oj)(e),meta:{styles:{minWidth:80,maxWidth:80}}}),e.push({header:J.formatMessage(r.YO[r.se.latency]),enableSorting:!1,enableResizing:!0,id:r.se.latency,accessorFn:e=>(0,c.isNil)(e.execution_time_ms)||!isFinite(e.execution_time_ms)?null:x.A.formatDuration(e.execution_time_ms),meta:{styles:{minWidth:100}}},{header:J.formatMessage(r.YO[r.se.tags]),enableSorting:!1,enableResizing:!0,id:r.se.tags,cell:Z?ae:e=>{let{row:{original:t}}=e;return(0,C.Y)(I,{tags:t.tags||[],onAddEditTags:()=>null===g||void 0===g?void 0:g(t),baseComponentId:L})}}),e.filter((e=>e.id&&!P.includes(e.id)))}),[J,u,g,D,P,L,Z]),ne=(0,f.N4)({columns:ee,data:o,state:{sorting:M,rowSelection:E},getCoreRowModel:(0,v.HT)(),getRowId:(e,t)=>e.request_id||t.toString(),getSortedRowModel:(0,v.h5)(),onSortingChange:R,onRowSelectionChange:N,enableColumnResizing:!0,enableRowSelection:!0,columnResizeMode:"onChange",meta:{baseComponentId:L,onTraceClicked:u,onTraceTagsEdit:g}}),ie=ne.getState().columnSizingInfo,de=s.useMemo((()=>{const e=ne.getFlatHeaders(),t={};return e.forEach((e=>{t[U(e.id)]=e.getSize(),t[$(e.column.id)]=e.column.getSize()})),t}),[ie,ee,ne]);return(0,C.FD)(h.XIK,{scrollable:!0,empty:(()=>{if(l){const e=l instanceof y.s?l.getMessageField():l.message;return(0,C.Y)(h.SvL,{image:(0,C.Y)(i.j,{}),description:e,title:(0,C.Y)(b.A,{id:"Cj2i02",defaultMessage:"Error"})})}return!a&&0===o.length&&T?(0,C.Y)(h.SvL,{description:(0,C.Y)(b.A,{id:"LTe7m4",defaultMessage:"No traces found with the current filter query. <button>Reset filters</button> to see all traces.",values:{button:e=>(0,C.Y)(i.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtable.tsx_289",onClick:k,children:e})}}),title:(0,C.Y)(b.A,{id:"yeEPjS",defaultMessage:"No traces found"})}):a||0!==o.length?null:(0,C.Y)(Q,{baseComponentId:L,experimentIds:t,runUuid:n})})(),style:de,pagination:(0,C.Y)(h.vIA,{componentId:`${L}.traces_table.pagination`,hasNextPage:m,hasPreviousPage:p,onNextPage:S,onPreviousPage:A}),children:[(0,C.FD)(h.Hjg,{isHeader:!0,children:[ne.getLeafHeaders().map((e=>{var t;return(0,C.Y)(h.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtable.tsx_365",css:null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.styles,sortable:e.column.getCanSort(),sortDirection:e.column.getIsSorted()||"none",onToggleSort:e.column.getToggleSortingHandler(),header:e,column:e.column,setColumnSizing:ne.setColumnSizing,isResizing:e.column.getIsResizing(),style:{flex:`calc(var(${U(e.id)}) / 100)`},children:(0,f.Kv)(e.column.columnDef.header,e.getContext())},e.id)})),(0,C.Y)(h.fWG,{children:(0,C.FD)(h.rId.Root,{children:[(0,C.Y)(h.rId.Trigger,{asChild:!0,children:(0,C.Y)(i.B,{componentId:`${L}.traces_table.column_selector_dropdown`,icon:(0,C.Y)(h.jng,{}),size:"small","aria-label":J.formatMessage({id:"zd5Jw4",defaultMessage:"Select columns"})})}),(0,C.Y)(h.rId.Content,{align:"end",children:X.map((e=>{let{key:t,label:n}=e;return(0,C.FD)(h.rId.CheckboxItem,{componentId:`${L}.traces_table.column_toggle_button`,checked:!P.includes(t),onClick:()=>q(t),children:[(0,C.Y)(h.rId.ItemIndicator,{}),n]},t)}))})]})})]}),a&&(0,C.Y)(h.BAM,{table:ne}),!a&&!l&&ne.getRowModel().rows.map((e=>(0,C.Y)(W,{row:e,columns:ee,selected:E[e.id]},e.id)))]})}));var ue=n(15579);var ge=n(47664);const me=e=>{let{modelTrace:t,height:n=700,useLatestVersion:i=!1}=e;const a=(0,s.useRef)(null),[r,l]=s.useState(!0);return(0,s.useEffect)((()=>{const e=e=>{var t;const n=null===(t=a.current)||void 0===t?void 0:t.contentWindow;if(n&&e.source===n&&e.data.type===pe.Ready)l(!1)};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}}),[t]),(0,s.useEffect)((()=>{var e;const n=null===(e=a.current)||void 0===e?void 0:e.contentWindow;n&&!r&&n.postMessage({type:he.UpdateTrace,traceData:t})}),[t,r]),(0,C.FD)("div",{css:(0,o.AH)({height:n},""),children:[r&&(0,C.FD)("div",{css:(0,o.AH)({position:"absolute",width:"100%",height:n},""),children:[(0,C.Y)(h.oud,{}),(0,C.Y)(h.QvX,{lines:5})]}),(0,C.Y)("iframe",{title:"Model Trace Explorer",src:`static-files/lib/ml-model-trace-renderer/index.html?version=${ge.Rx}`,ref:a,css:(0,o.AH)({border:"none",width:"100%",height:n},"")})]})};let pe=function(e){return e.Ready="READY",e}({}),he=function(e){return e.UpdateTrace="UPDATE_TRACE",e}({});const fe=e=>{let{requestId:t,traceInfo:n,loadingTraceInfo:l,onClose:c,selectedSpanId:d,onSelectSpan:u}=e;const{traceData:g,loading:m,error:p}=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const[n,o]=(0,s.useState)(void 0),[i,r]=(0,s.useState)(!1),[l,c]=(0,s.useState)(void 0),d=(0,s.useCallback)((async e=>{r(!0);try{const t=await a.x.getExperimentTraceData(e);Array.isArray(t.spans)?o(t):x.A.logErrorAndNotifyUser("Invalid trace data response: "+JSON.stringify(null===t||void 0===t?void 0:t.toString()))}catch(t){c(t)}r(!1)}),[]);return(0,s.useEffect)((()=>{e&&!t&&d(e)}),[d,e,t]),{traceData:n,loading:i,error:l}}(t,"IN_PROGRESS"===(null===n||void 0===n?void 0:n.status)),{theme:f}=(0,i.u)(),v=!l&&!n,{traceInfo:w,loading:y}=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const[n,o]=(0,s.useState)(void 0),[i,r]=(0,s.useState)(t),[l,c]=(0,s.useState)(void 0),d=(0,s.useCallback)((async()=>{if(t){c(void 0);try{const t=await a.x.getExperimentTraceInfo(e);if(!t.trace_info)return void o(void 0);o(t.trace_info)}catch(n){c(n)}finally{r(!1)}}}),[t,e]);return(0,s.useEffect)((()=>{d()}),[d]),{traceInfo:n,loading:i,error:l}}(t,v),_=n||w,Y=(0,s.useMemo)((()=>l||y?(0,C.Y)(h.oud,{}):_?(0,C.Y)(i.T.Title,{level:2,withoutMargins:!0,children:(0,r.Db)(_)}):t),[l,y,_,t]),S=(0,s.useMemo)((()=>g?{info:_||{},data:g}:void 0),[g,_]),A=((null===g||void 0===g?void 0:g.spans)||[]).length>0;return(0,C.Y)(h._s.Root,{modal:!0,open:!0,onOpenChange:e=>{e||c()},children:(0,C.Y)(h._s.Content,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracedatadrawer.tsx_222",width:"90vw",title:Y,expandContentToFullHeight:!0,children:m||l||y?(0,C.FD)(C.FK,{children:[(0,C.Y)(h.oud,{}),(0,C.Y)(h.QvX,{lines:5})]}):"IN_PROGRESS"===(null===n||void 0===n?void 0:n.status)?(0,C.FD)(C.FK,{children:[(0,C.Y)(ue.S,{size:"lg"}),(0,C.Y)(h.SvL,{image:(0,C.Y)(i.W,{}),description:(0,C.Y)(b.A,{id:"K+5g8S",defaultMessage:"Trace data is not available for in-progress traces. Please wait for the trace to complete."}),title:(0,C.Y)(b.A,{id:"p0O0uP",defaultMessage:"Trace data not available"})})]}):p?(0,C.FD)(C.FK,{children:[(0,C.Y)(ue.S,{size:"lg"}),(0,C.Y)(h.SvL,{image:(0,C.Y)(i.j,{}),description:(0,C.Y)(b.A,{id:"c4KA32",defaultMessage:"An error occurred while attempting to fetch the trace data. Please wait a moment and try again."}),title:(0,C.Y)(b.A,{id:"eavoW/",defaultMessage:"Error"})})]}):A?S?(0,C.Y)("div",{css:(0,o.AH)({height:`calc(100% - ${f.spacing.sm}px)`,marginLeft:-f.spacing.lg,marginRight:-f.spacing.lg,marginBottom:-f.spacing.lg},""),onWheel:e=>e.stopPropagation(),children:(0,C.Y)(me,{modelTrace:S,height:"100%"})}):null:(0,C.FD)(C.FK,{children:[(0,C.Y)(ue.S,{size:"lg"}),(0,C.Y)(h.SvL,{description:null,title:(0,C.Y)(b.A,{id:"EpFST9",defaultMessage:"No trace data recorded"})})]})})})};var ve=n(43683);const xe=e=>{let{experimentIds:t,visible:n,rowSelection:o,setRowSelection:r,handleClose:l,refreshTraces:d}=e;const u=(0,Y.A)(),[g,m]=(0,s.useState)(""),[p,h]=(0,s.useState)(!1),f=(0,c.keys)((0,c.pickBy)(o,(e=>e)));return(0,C.FD)(ue.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewdeletetracemodal.tsx_62",title:(0,C.Y)(b.A,{id:"gRz1nB",defaultMessage:"{count, plural, one {Delete Trace} other {Delete Traces}}",values:{count:f.length}}),visible:n,onCancel:l,okText:(0,C.Y)(b.A,{id:"g56Cuk",defaultMessage:"Delete {count, plural, one { # trace } other { # traces }}",values:{count:f.length}}),onOk:()=>{(async()=>{try{var e;await a.x.deleteTraces(null!==(e=t[0])&&void 0!==e?e:"",f),r({}),d(),l()}catch(n){m(u.formatMessage({id:"zhzZUu",defaultMessage:"An error occured while attempting to delete traces. Please refresh the page and try again."}))}h(!1)})(),h(!0)},okButtonProps:{loading:p,danger:!0},children:[g&&(0,C.Y)(i.T.Paragraph,{color:"error",children:g}),(0,C.Y)(i.T.Paragraph,{children:(0,C.Y)(i.T.Text,{bold:!0,children:(0,C.Y)(b.A,{id:"kmtWGf",defaultMessage:"{count, plural, one { # trace } other { # traces }} will be deleted.",values:{count:f.length}})})}),(0,C.Y)(i.T.Paragraph,{children:(0,C.Y)(b.A,{id:"VfGJFc",defaultMessage:"Deleted traces cannot be restored. Are you sure you want to proceed?"})})]})},we=e=>{let{experimentIds:t,rowSelection:n,setRowSelection:a,refreshTraces:r,baseComponentId:l}=e;const[c,d]=(0,s.useState)(!1),{theme:u}=(0,i.u)(),g=(0,s.useCallback)((()=>{d(!0)}),[d]),m=(0,s.useCallback)((()=>{d(!1)}),[d]);return(0,C.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"row",alignItems:"center",gap:u.spacing.sm},""),children:[(0,C.Y)(i.B,{componentId:`${l}.traces_table.delete_traces`,onClick:g,danger:!0,children:(0,C.Y)(b.A,{id:"QAyzA3",defaultMessage:"Delete"})}),(0,C.Y)(xe,{experimentIds:t,visible:c,rowSelection:n,handleClose:m,refreshTraces:r,setRowSelection:a})]})};const ye=e=>{let{baseComponentId:t}=e;const{theme:n}=(0,i.u)();return(0,C.FD)(i.av.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewcontrols.tsx_28",modal:!1,children:[(0,C.Y)(i.av.Trigger,{asChild:!0,children:(0,C.Y)(i.B,{size:"small",type:"link",icon:(0,C.Y)(ue.I,{css:(0,o.AH)({svg:{width:16,height:16,color:n.colors.textSecondary}},"")}),componentId:`${t}.traces_table.filter_tooltip`})}),(0,C.FD)(i.av.Content,{children:[(0,C.Y)(i.av.Arrow,{}),(0,C.Y)(i.T.Paragraph,{children:(0,C.Y)(b.A,{id:"2Ofya9",defaultMessage:"Search traces using a simplified version of the SQL {whereBold} clause.",values:{whereBold:(0,C.Y)("b",{children:"WHERE"})}})}),(0,C.Y)(b.A,{id:"U3btBc",defaultMessage:"Examples:"}),(0,C.Y)("ul",{children:(0,C.Y)("li",{children:(0,C.Y)("code",{children:'tags.some_tag = "abc"'})})})]})]})};var _e={name:"1ykowef",styles:"margin-bottom:0"},Ye={name:"ikeoj9",styles:"width:430px"};const be=e=>{let{experimentIds:t,filter:n,onChangeFilter:a,rowSelection:r,setRowSelection:l,refreshTraces:c,baseComponentId:d,runUuid:u,traces:g}=e;const m=(0,Y.A)(),{theme:p}=(0,i.u)(),[f,v]=(0,s.useState)(n||void 0),[x,w]=(0,s.useState)(!1),y=null!==f&&void 0!==f?f:n,_=Object.entries(r).filter((e=>{let[,t]=e;return t})).map((e=>{let[t]=e;return t})),b=_.length>0?(0,C.Y)(we,{experimentIds:t,rowSelection:r,setRowSelection:l,refreshTraces:c,baseComponentId:d}):(0,C.Y)(h.R9P,{css:_e,children:(0,C.Y)(M.I,{componentId:`${d}.traces_table.search_filter`,placeholder:m.formatMessage({id:"FPMrvd",defaultMessage:"Search traces"}),value:y,css:Ye,onChange:e=>v(e.target.value),prefix:(0,C.Y)(M.S,{}),suffix:(0,C.Y)(ye,{baseComponentId:d}),allowClear:!0,onClear:()=>{a(""),v(void 0)},onKeyDown:e=>{"Enter"===e.key&&(a(y),v(void 0))}})});return(0,C.Y)("div",{css:(0,o.AH)({display:"flex",gap:p.spacing.xs},""),children:b})};var Se=n(32614);const Ae={hiddenColumns:[r.se.traceName,r.se.source]},Ce=e=>{const t=(0,s.useMemo)((()=>{const t=JSON.stringify(e.slice().sort());return Se.A.getStoreForComponent("ExperimentViewTraces",t)}),[e]),[n,o]=(0,s.useState)((()=>(e=>{try{const t=e.getItem("uiState"),n=JSON.parse(t);return(0,c.isObject)(n)?n:Ae}catch(t){return Ae}})(t))),i=(0,s.useCallback)((e=>{o((t=>{const n=t.hiddenColumns||[];return{hiddenColumns:n.includes(e)?n.filter((t=>t!==e)):[...n,e]}}))}),[]);return(0,s.useEffect)((()=>{t.setItem("uiState",JSON.stringify(n))}),[t,n]),{uiState:n,toggleHiddenColumn:i}},Te="selectedTraceId",ke="selectedSpanId",Ie=[{id:r.se.timestampMs,desc:!0}],Me=e=>{let{experimentIds:t,runUuid:n,loggedModelId:l,disabledColumns:d,baseComponentId:u=(n?"mlflow.run.traces":"mlflow.experiment_page.traces")}=e;const g=(0,s.useRef)(void 0),[m,h]=(0,s.useState)(""),[f,v]=(0,s.useState)(Ie),[x,y]=(0,s.useState)({}),[_,Y]=(()=>{var e;const[t,n]=(0,w.ok)(),o=null!==(e=t.get(Te))&&void 0!==e?e:void 0,i=(0,s.useCallback)((e=>{n((t=>void 0===e?(t.delete(Te),t):(t.set(Te,e),t)))}),[n]);return[o,i]})(),[b,A]=(()=>{var e;const[t,n]=(0,w.ok)(),o=null!==(e=t.get(ke))&&void 0!==e?e:void 0,i=(0,s.useCallback)((e=>{n((t=>void 0===e?(t.delete(ke),t):(t.set(ke,e),t)),{replace:!0})}),[n]);return[o,i]})(),{traces:T,loading:k,error:I,hasNextPage:M,hasPreviousPage:R,fetchNextPage:F,fetchPrevPage:E,refreshCurrentPage:N}=p({experimentIds:t,sorting:f,filter:m,runUuid:n,loggedModelId:l}),P=(0,s.useCallback)((e=>{let{request_id:t}=e;return Y(t)}),[Y]),D=(0,s.useCallback)((()=>{F(),y({})}),[F]),L=(0,s.useCallback)((()=>{E(),y({})}),[E]);(0,s.useEffect)((()=>{window.clearTimeout(g.current);const e=async()=>{k||R||(await N(!0),window.clearTimeout(g.current),g.current=window.setTimeout(e,3e4))};return g.current=window.setTimeout(e,3e4),()=>window.clearTimeout(g.current)}),[N,k,R]);const{theme:H}=(0,i.u)(),O=(0,s.useMemo)((()=>{if(_)return T.find((e=>e.request_id===_))}),[_,T]),{uiState:q,toggleHiddenColumn:z}=Ce(t),U=(0,s.useMemo)((()=>(0,c.uniq)((0,c.compact)(T.flatMap((e=>{var t;return null===(t=e.tags)||void 0===t?void 0:t.map((e=>e.key))}))))),[T]),{showEditTagsModalForTrace:$,EditTagsModal:W}=(e=>{let{onSuccess:t,existingTagKeys:n=[],useV3Apis:o}=e;const{showEditTagsModal:i,EditTagsModal:r}=(0,ve.Q)({saveTagsHandler:async(e,t,n)=>{if(!e.traceRequestId)return;const i=e.traceRequestId,s=n.filter((e=>{let{key:n,value:o}=e;return!t.some((e=>{let{key:t,value:i}=e;return t===n&&o===i}))})),r=t.filter((e=>{let{key:t}=e;return!n.some((e=>{let{key:n}=e;return t===n}))})),l=Promise.all([...s.map((e=>{let{key:t,value:n}=e;return o?a.x.setExperimentTraceTagV3(i,t,n):a.x.setExperimentTraceTag(i,t,n)})),...r.map((e=>{let{key:t}=e;return o?a.x.deleteExperimentTraceTagV3(i,t):a.x.deleteExperimentTraceTag(i,t)}))]);return l},valueRequired:!0,allAvailableTags:n.filter((e=>e&&!e.startsWith(S.nt))),onSuccess:t});return{showEditTagsModalForTrace:(0,s.useCallback)((e=>{var t;if(!e.request_id)return;const n=(null===(t=e.tags)||void 0===t?void 0:t.filter((e=>{let{key:t}=e;return t&&!t.startsWith(S.nt)})))||[];i({traceRequestId:e.request_id,tags:n||[]})}),[i]),EditTagsModal:r}})({onSuccess:()=>N(!0),existingTagKeys:U}),B=""!==m,K=T.some((e=>!(0,c.isNil)((0,r.oj)(e)))),j=(0,s.useMemo)((()=>K?[]:[r.se.totalTokens]),[K]),V=(0,s.useMemo)((()=>[...null!==d&&void 0!==d?d:[],...j]),[d,j]),J=(0,s.useMemo)((()=>{var e;return[...null!==(e=q.hiddenColumns)&&void 0!==e?e:[],...V]}),[q,V]);return(0,C.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:H.spacing.sm,height:"100%",overflow:"hidden"},""),children:[(0,C.Y)(be,{experimentIds:t,filter:m,onChangeFilter:h,rowSelection:x,setRowSelection:y,refreshTraces:N,baseComponentId:u,runUuid:n,traces:T}),(0,C.Y)(de,{experimentIds:t,runUuid:n,traces:T,loading:k,error:I,onTraceClicked:P,onTraceTagsEdit:$,hasNextPage:M,hasPreviousPage:R,onPreviousPage:L,onNextPage:D,onTagsUpdated:N,usingFilters:B,onResetFilters:()=>h(""),hiddenColumns:J,disableTokenColumn:!K,disabledColumns:V,setSorting:e=>{if((0,c.isFunction)(e))return v((t=>{const n=e(t),o=t[0];return n&&0!==n.length||!o?n:[{id:o.id,desc:!o.desc}]}))},sorting:f,rowSelection:x,setRowSelection:y,baseComponentId:u,toggleHiddenColumn:z}),_&&(0,C.Y)(fe,{traceInfo:O,loadingTraceInfo:k,requestId:_,onClose:()=>Y(void 0),selectedSpanId:b,onSelectSpan:A}),W]})}}}]);
//# sourceMappingURL=9682.a409d871.chunk.js.map