"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[1107],{3159:function(e,t,s){s.d(t,{S9:function(){return O},PK:function(){return q},nl:function(){return X},I$:function(){return W},Ay:function(){return G}});var i=s(89555),n=s(31014),r=s(10811),a=s(76010),o=s(53140),l=s(26809),c=s(9133),d=s.n(c),u=s(82645),h=s(72877),p=s(89933),m=s(64912),g=s(48012),y=s(50111);function v(e){let{columns:t,values:s,styles:i={},testId:n,scroll:r}=e;return(0,y.Y)(g.qXK,{className:"html-table-view","data-test-id":n,dataSource:s,columns:t,scroll:r,size:"middle",pagination:!1,style:i})}var f=s(85017),x=s(93215),C=s(58481);class M extends n.Component{render(){const{runUuids:e}=this.props;return(0,y.Y)("div",{className:"metrics-summary",children:e.length>1?this.renderMetricTables():this.renderRunTable(e[0])})}renderRunTable(e){const{metricKeys:t,latestMetrics:s,minMetrics:i,maxMetrics:n,intl:r}=this.props,a=[{title:r.formatMessage({id:"YINJEO",defaultMessage:"Metric"}),dataIndex:"metricKey",sorter:(e,t)=>e.metricKey<t.metricKey?-1:e.metricKey>t.metricKey?1:0,width:350},...this.dataColumns()];return 0===t.length?null:(0,y.Y)(v,{columns:a,values:b(e,t,s,i,n,r),scroll:{y:300}})}renderMetricTables(){const{runExperimentIds:e,runUuids:t,runDisplayNames:s,metricKeys:i,latestMetrics:r,minMetrics:a,maxMetrics:o,intl:l}=this.props,c=[{title:l.formatMessage({id:"d1Ou4x",defaultMessage:"Run"}),dataIndex:"runLink",sorter:(e,t)=>e.runName<t.runName?-1:e.runName>t.runName?1:0,width:350},...this.dataColumns()];return i.map((i=>(0,y.FD)(n.Fragment,{children:[(0,y.Y)("h1",{children:i}),(0,y.Y)(v,{columns:c,values:A(i,e,t,s,r,a,o,l),scroll:{y:300}})]},i)))}dataColumns(){return[{title:this.props.intl.formatMessage({id:"umeTwG",defaultMessage:"Latest"}),dataIndex:"latestFormatted",sorter:(e,t)=>e.latestValue-t.latestValue,width:200,ellipsis:!0},{title:this.props.intl.formatMessage({id:"iQzwqe",defaultMessage:"Min"}),dataIndex:"minFormatted",sorter:(e,t)=>e.minValue-t.minValue,width:200,ellipsis:!0},{title:this.props.intl.formatMessage({id:"+hkg/y",defaultMessage:"Max"}),dataIndex:"maxFormatted",sorter:(e,t)=>e.maxValue-t.maxValue,width:200,ellipsis:!0}]}}const A=(e,t,s,i,n,r,a,o)=>s.map(((s,l)=>{const c=i[l];return{runName:c,runLink:(0,y.Y)(x.N_,{to:C.h.getRunPageRoute(t[s]||"",s),children:c}),key:s,...N(s,e,n,r,a,o)}})),b=(e,t,s,i,n,r)=>t.map((t=>({metricKey:t,key:t,...N(e,t,s,i,n,r)})));var w={name:"nkt64x",styles:"margin-right:10px"},S={name:"nkt64x",styles:"margin-right:10px"},L={name:"nkt64x",styles:"margin-right:10px"};const N=(e,t,s,i,n,r)=>{const a=k(s,e,t),o=k(i,e,t),l=k(n,e,t),c=Y(a),d=Y(o),u=Y(l);return{latestFormatted:(0,y.Y)("span",{title:c,css:w,children:I(a,r)}),minFormatted:(0,y.Y)("span",{title:d,css:S,children:I(o,r)}),maxFormatted:(0,y.Y)("span",{title:u,css:L,children:I(l,r)}),latestValue:c,minValue:d,maxValue:u}},k=(e,t,s)=>e[t]&&e[t][s],Y=e=>e&&e.value,I=(e,t)=>void 0===e?"":t.formatMessage({id:"eQgPEi",defaultMessage:"{value} (step={step})"},{value:e.value,step:e.step}),U=(0,m.Ay)(M);var _=(0,r.Ng)(((e,t)=>{const{runUuids:s}=t,i={},n={},r={},a={};return s.forEach((t=>{const s=(0,h.K4)(t,e);i[t]=s&&s.experimentId,n[t]=(0,f.d0)(t,e),r[t]=(0,f.qP)(t,e),a[t]=(0,f.KQ)(t,e)})),{runExperimentIds:i,latestMetrics:n,minMetrics:r,maxMetrics:a}}))(U),R=s(51079),D=s.n(R),K=s(25869),T=s(32599);const P=e=>{let{icon:t,className:s,style:i,onClick:n,...r}=e;return(0,y.Y)(T.B,{componentId:"codegen_mlflow_app_src_common_components_iconbutton.tsx_20",type:"link",className:s,style:{padding:0,...i},onClick:n,...r,children:t})};class E extends n.Component{constructor(){super(...arguments),this.renderContent=()=>{const{experimentId:e,runItems:t}=this.props;return(0,y.Y)("div",{children:t.map(((t,s)=>{let{name:i,runId:n,color:r,y:o}=t;const l=`${n}-${s}`,c=C.h.getRunPageRoute(e,n);return(0,y.Y)(x.N_,{to:c,children:(0,y.FD)("p",{style:{color:r},children:[(0,y.Y)("i",{className:"fas fa-external-link-o",style:{marginRight:5}}),`${i}, ${a.A.formatMetric(o)}`]})},l)}))})},this.renderTitle=()=>{const{handleClose:e}=this.props;return(0,y.FD)("div",{children:[(0,y.Y)("span",{children:"Jump to individual runs"}),(0,y.Y)(P,{icon:(0,y.Y)("i",{className:"fas fa-times"}),onClick:e,style:{float:"right",marginLeft:"7px"}})]})}}componentDidMount(){document.addEventListener("keydown",this.props.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.props.handleKeyDown)}render(){const{visible:e,x:t,y:s,handleVisibleChange:i}=this.props;return(0,y.Y)(g.Lac,{content:this.renderContent(),title:this.renderTitle(),placement:"left",visible:e,onVisibleChange:i,children:(0,y.Y)("div",{style:{left:t,top:s,position:"absolute"}})})}}var F=s(7204),V=s(44258),B=s(32039);const q="line",O="bar",X=1e4,H=1e5,W=e=>{const t=["run_id",...Object.keys(e[0].history[0])],s=e.flatMap((e=>{let{runUuid:t,history:s}=e;return s.map((e=>[t,...Object.values(e)]))}));return[t].concat(s).map((e=>e.join(","))).join("\n")};class j extends n.Component{constructor(e){super(e),this._isMounted=!1,this.displayPopover=void 0,this.intervalId=void 0,this.legendClickTimeout=null,this.prevLegendClickTime=Math.inf,this.lastClickedLegendCurveId=null,this.MAX_DOUBLE_CLICK_INTERVAL_MS=300,this.SINGLE_CLICK_EVENT_DELAY_MS=this.MAX_DOUBLE_CLICK_INTERVAL_MS+10,this.onFocus=()=>{this.setState({focused:!0})},this.onBlur=()=>{this.setState({focused:!1})},this.clearEventListeners=()=>{window.removeEventListener("focus",this.onFocus),window.removeEventListener("blur",this.onBlur)},this.clearInterval=()=>{clearInterval(this.intervalId),this.intervalId=null},this.allRunsCompleted=()=>this.props.completedRunUuids.length===this.props.runUuids.length,this.isHangingRunUuid=e=>{const t=this.props.latestMetricsByRunUuid[e];if(!t)return!1;const s=Object.values(t).map((e=>{let{timestamp:t}=e;return t})),i=Math.max(...s);return(new Date).getTime()-i>6048e5},this.getActiveRunUuids=()=>{const{completedRunUuids:e,runUuids:t}=this.props;return d().difference(t,e).filter(d().negate(this.isHangingRunUuid))},this.shouldPoll=()=>!(this.allRunsCompleted()||0===this.getActiveRunUuids().length),this.updateUrlState=e=>{const{runUuids:t,metricKey:s,location:i,navigate:n}=this.props,r=JSON.parse(D().parse(i.search).experiments),a={...this.getUrlState(),...e},{selectedXAxis:o,selectedMetricKeys:l,showPoint:c,yAxisLogScale:d,lineSmoothness:u,layout:h,deselectedCurves:p,lastLinearYAxisRange:m}=a;n(C.h.getMetricPageRoute(t,s,r,l,h,o,d,u,c,p,m),{replace:!0})},this.getNumTotalMetrics=()=>this.props.metricsWithRunInfoAndHistory.map((e=>{let{history:t}=e;return t.length})).reduce(((e,t)=>e+t),0),this.loadMetricHistory=(e,t)=>{if(this.getNumTotalMetrics()>=H)return a.A.logErrorAndNotifyUser("The total number of metric data points exceeded 100,000. Cannot fetch more metrics."),Promise.resolve([]);this.setState({loading:!0});const s=e.flatMap((e=>t.map((t=>({runUuid:e,metricKey:t}))))).filter((e=>{let{runUuid:t,metricKey:s}=e;return this.props.latestMetricsByRunUuid[t].hasOwnProperty(s)})).map((async e=>{let{runUuid:t,metricKey:s}=e;const i=[],n=(0,F.yk)();i.push(n);let r=(await this.props.getMetricHistoryApi(t,s,25e3,void 0,n)).value.next_page_token;for(;r;){if(this.getNumTotalMetrics()>=H)return{requestIds:i,success:!1};const e=(0,F.yk)();i.push(e);r=(await this.props.getMetricHistoryApi(t,s,25e3,r,e)).value.next_page_token}return{requestIds:i,success:!0}}));return Promise.all(s).then((e=>(this._isMounted&&this.setState({loading:!1}),e.every((e=>{let{success:t}=e;return t}))||a.A.logErrorAndNotifyUser("The total number of metric data points exceeded 100,000. Aborted fetching metrics."),e.flatMap((e=>{let{requestIds:t}=e;return t})))))},this.loadRuns=e=>{const t=[];return e.forEach((e=>{const s=(0,F.yk)();this.props.getRunApi(e),t.push(s)})),t},this.getMetrics=()=>{const e=this.getUrlState(),t=new Set(e.selectedMetricKeys),{selectedXAxis:s}=e,{metricsWithRunInfoAndHistory:i}=this.props,n=i.filter((e=>t.has(e.metricKey)));return n.forEach((e=>{const t=s===p.c1&&e.history[0]&&d().isNumber(e.history[0].step);e.history.sort(t?a.A.compareByStepAndTimestamp:a.A.compareByTimestamp)})),n},this.handleYAxisLogScaleChange=e=>{const t=this.getUrlState(),s=d().cloneDeep(t.layout),i=e?"log":"linear";if(!e&&t.lastLinearYAxisRange&&t.lastLinearYAxisRange.length>0)return s.yaxis={type:"linear",range:t.lastLinearYAxisRange},void this.updateUrlState({layout:s,lastLinearYAxisRange:[]});if(!t.layout.yaxis||!t.layout.yaxis.range)return s.yaxis={type:i,autorange:!0,..."log"===i?{exponentformat:"e"}:{}},void this.updateUrlState({layout:s,lastLinearYAxisRange:[]});let n=[];const r=t.layout.yaxis.range;e?r[0]<=0?(n=r,s.yaxis={type:"log",autorange:!0,exponentformat:"e"}):s.yaxis={type:"log",range:[Math.log(r[0])/Math.log(10),Math.log(r[1])/Math.log(10)],exponentformat:"e"}:s.yaxis={type:"linear",range:[Math.pow(10,r[0]),Math.pow(10,r[1])]},this.updateUrlState({layout:s,lastLinearYAxisRange:n})},this.handleXAxisChange=e=>{const t=this.getUrlState(),s={[p.up]:"date",[p.gW]:"linear",[p.c1]:"linear"}[e.target.value]||"linear",i={...t.layout,xaxis:{autorange:!0,type:s}};this.updateUrlState({selectedXAxis:e.target.value,layout:i})},this.handleLayoutChange=e=>{this.displayPopover=!1;const t=this.getUrlState(),{"xaxis.range[0]":s,"xaxis.range[1]":i,"yaxis.range[0]":n,"yaxis.range[1]":r,"xaxis.autorange":a,"yaxis.autorange":o,"yaxis.showspikes":l,"xaxis.showspikes":c,...d}=e;let u={...t.layout,...d},h=[...t.lastLinearYAxisRange];const p=u.xaxis||{};void 0!==s&&void 0!==i&&(p.range=[s,i],p.autorange=!1),c&&(p.showspikes=!0),a&&(p.autorange=!0);const m=u.yaxis||{};if(void 0!==n&&void 0!==r&&(m.range=[n,r],m.autorange=!1),l&&(m.showspikes=!0),o){h=[];const e=t.layout&&t.layout.yaxis&&"log"===t.layout.yaxis.type?"log":"linear";m.autorange=!0,m.type=e}"log"===m.type&&(m.exponentformat="e"),u={...u,xaxis:p,yaxis:m},this.updateUrlState({layout:u,lastLinearYAxisRange:h})},this.handleDownloadCsv=()=>{const e=W(this.props.metricsWithRunInfoAndHistory),t=new Blob([e],{type:"application/csv;charset=utf-8"});(0,V.saveAs)(t,"metrics.csv")},this.handleLegendClick=e=>{let{curveNumber:t,data:s}=e;const i=this.getUrlState(),n=Date.now();if(n-this.prevLegendClickTime<this.MAX_DOUBLE_CLICK_INTERVAL_MS&&t===this.lastClickedLegendCurveId)this.handleLegendDoubleClick({curveNumber:t,data:s}),this.prevLegendClickTime=Math.inf;else{const e=j.getCurveKey(s[t]);this.legendClickTimeout=window.setTimeout((()=>{const t=new Set(i.deselectedCurves);t.has(e)?t.delete(e):t.add(e),this.updateUrlState({deselectedCurves:Array.from(t)})}),this.SINGLE_CLICK_EVENT_DELAY_MS),this.prevLegendClickTime=n}return this.lastClickedLegendCurveId=t,!1},this.handleLegendDoubleClick=e=>{let{curveNumber:t,data:s}=e;window.clearTimeout(this.legendClickTimeout);const i=j.getCurveKey(s[t]),n=s.map((e=>j.getCurveKey(e))).filter((e=>e!==i));return this.updateUrlState({deselectedCurves:n}),!1},this.handleMetricsSelectChange=e=>{const t=this.getUrlState().selectedMetricKeys||[],s=e.filter((e=>!t.includes(e)));this.updateUrlState({selectedMetricKeys:e}),this.loadMetricHistory(this.props.runUuids,s).then((e=>{this.setState({loading:!1}),this.setState((t=>({historyRequestIds:[...t.historyRequestIds,...e]})))}))},this.handleShowPointChange=e=>this.updateUrlState({showPoint:e}),this.handleLineSmoothChange=e=>this.updateUrlState({lineSmoothness:e}),this.handleKeyDownOnPopover=e=>{let{key:t}=e;"Escape"===t&&this.setState({popoverVisible:!1})},this.updatePopover=e=>{this.displayPopover=!this.displayPopover,setTimeout((()=>{if(this.displayPopover){this.displayPopover=!1;const{popoverVisible:t,popoverX:s,popoverY:i}=this.state,{points:n,event:{clientX:r,clientY:a}}=e,o=s===r&&i===a,l=n.sort(((e,t)=>t.y-e.y)).map((e=>({runId:e.data.runId,name:e.data.name,color:e.fullData.marker.color,y:e.y})));this.setState({popoverVisible:!t||!o,popoverX:r,popoverY:a,popoverRunItems:l})}}),300)},this.state={historyRequestIds:[],popoverVisible:!1,popoverX:0,popoverY:0,popoverRunItems:[],focused:!0,loading:!1},this.displayPopover=!1,this.intervalId=null}hasMultipleExperiments(){return this.props.experimentIds&&this.props.experimentIds.length>1}componentDidMount(){this._isMounted=!0,this.loadMetricHistory(this.props.runUuids,this.getUrlState().selectedMetricKeys),this.shouldPoll()&&(window.addEventListener("blur",this.onBlur),window.addEventListener("focus",this.onFocus),this.intervalId=setInterval((()=>{if(this.state.focused){const e=this.getActiveRunUuids();this.loadMetricHistory(e,this.getUrlState().selectedMetricKeys),this.loadRuns(e),this.shouldPoll()||(this.clearEventListeners(),this.clearInterval())}}),X))}componentWillUnmount(){this._isMounted=!1,this.clearEventListeners(),this.clearInterval()}getUrlState(){return a.A.getMetricPlotStateFromUrl(this.props.location.search)}static predictChartType(e){return e&&e.length&&d().every(e,(e=>e.history&&1===e.history.length))?O:q}static isComparing(e){const t=D().parse(e),s=t&&t["?runs"];return!!s&&JSON.parse(s).length>1}getAxisType(){const e=this.getUrlState();return e.layout&&e.layout.yaxis&&"log"===e.layout.yaxis.type?"log":"linear"}static getCurveKey(e){return"bar"===e.type?e.runId:a.A.getCurveKey(e.runId,e.metricName)}render(){const{experimentIds:e,runUuids:t,runDisplayNames:s,distinctMetricKeys:n,location:r}=this.props,{popoverVisible:a,popoverX:l,popoverY:c,popoverRunItems:d,loading:h}=this.state,m=this.getUrlState(),{showPoint:g,selectedXAxis:v,selectedMetricKeys:f,lineSmoothness:x}=m,C="log"===this.getAxisType(),{historyRequestIds:M}=this.state,A=this.getMetrics(),b=j.predictChartType(A);return(0,y.FD)("div",{className:"metrics-plot-container",children:[(0,y.Y)(p.jC,{numRuns:this.props.runUuids.length,numCompletedRuns:this.props.completedRunUuids.length,distinctMetricKeys:n,selectedXAxis:v,selectedMetricKeys:f,handleXAxisChange:this.handleXAxisChange,handleMetricsSelectChange:this.handleMetricsSelectChange,handleShowPointChange:this.handleShowPointChange,handleYAxisLogScaleChange:this.handleYAxisLogScaleChange,handleLineSmoothChange:this.handleLineSmoothChange,chartType:b,lineSmoothness:x,yAxisLogScale:C,showPoint:g,handleDownloadCsv:this.handleDownloadCsv,disableSmoothnessControl:this.props.containsInfinities}),(0,y.Y)("div",{className:"metrics-plot-data",children:(0,y.FD)(o.Ay,{requestIds:M,shouldOptimisticallyRender:0===M.length,children:[this.hasMultipleExperiments()?null:(0,y.Y)(E,{experimentId:e[0],visible:a,x:l,y:c,runItems:d,handleKeyDown:this.handleKeyDownOnPopover,handleClose:()=>this.setState({popoverVisible:!1}),handleVisibleChange:e=>this.setState({popoverVisible:e})}),(0,y.Y)(T.S,{size:"large",css:(0,i.AH)({visibility:h?"visible":"hidden"},"")}),(0,y.Y)(u.gT,{runUuids:t,runDisplayNames:s,xAxis:v,metrics:this.getMetrics(),metricKeys:f,showPoint:g,chartType:b,isComparing:j.isComparing(r.search),lineSmoothness:x,extraLayout:m.layout,deselectedCurves:m.deselectedCurves,onLayoutChange:this.handleLayoutChange,onClick:this.updatePopover,onLegendClick:this.handleLegendClick,onLegendDoubleClick:this.handleLegendDoubleClick}),(0,y.Y)(_,{runUuids:t,runDisplayNames:s,metricKeys:f})]})})]})}}j.defaultProps={containsInfinities:!1};const z={getMetricHistoryApi:l.Ly,getRunApi:l.aO};var G=(0,K.h)((0,r.Ng)(((e,t)=>{const{runUuids:s}=t,i=s.filter((t=>"RUNNING"!==(0,h.K4)(t,e).status)),{latestMetricsByRunUuid:n,metricsByRunUuid:r}=e.entities,o=d().flatMap(s,(e=>{const t=n[e];return t?Object.keys(t):[]})),l=[...new Set(o)].sort(),c=[];let u=!1;const p=d().flatMap(s,(t=>{const s=a.A.getRunDisplayName((0,h.K4)(t,e),t);c.push(s);const i=r[t];return i?Object.keys(i).map((e=>{const n=i[e].map((e=>(0,B.rj)(e)));return n.some((e=>{let{value:t}=e;return"number"===typeof t&&!isNaN(t)&&!isFinite(t)}))&&(u=!0),{metricKey:e,history:n,runUuid:t,runDisplayName:s}})):[]}));return{runDisplayNames:c,latestMetricsByRunUuid:n,distinctMetricKeys:l,metricsWithRunInfoAndHistory:p,completedRunUuids:i,containsInfinities:u}}),z)(j))},20193:function(e,t,s){s.d(t,{o:function(){return p}});var i=s(89555),n=s(32599),r=s(48012),a=s(41028),o=s(9133),l=s(31014),c=s(50111);const d=(e,t,s)=>(0,o.keys)(e).reduce(((e,s)=>Math.abs(Number(s)-t)<Math.abs(e-t)?Number(s):Number(e)),s),u=(e,t,s)=>"down"===s?Math.max(...Object.keys(e).filter((e=>Number(e)<t)).map(Number)):Math.min(...Object.keys(e).filter((e=>Number(e)>t)).map(Number));var h={name:"b9l38d",styles:"width:min-content"};const p=e=>{let{max:t=1,min:s=0,step:p,marks:m,value:g,onChange:y,disabled:v,onAfterChange:f,componentId:x,className:C}=e;const{theme:M}=(0,n.u)(),A=!(0,o.isEmpty)(m),b=A&&Object.keys(m).length<10,[w,S]=(0,l.useState)(void 0);return(0,c.FD)("div",{css:(0,i.AH)({display:"flex",height:M.general.heightSm,gap:M.spacing.md,alignItems:"center"},""),children:[(0,c.FD)(r.Apm.Root,{disabled:v,css:(0,i.AH)({flex:1,position:"relative","span:last-child":{zIndex:2}},""),className:C,min:s,max:t,value:[null!==g&&void 0!==g?g:0],onValueCommit:e=>{let[t]=e;return null===f||void 0===f?void 0:f(t)},onKeyDown:e=>{if(A&&(e.preventDefault(),["ArrowLeft","ArrowRight","ArrowUp","ArrowDown"].includes(e.key))){const t=u(m,null!==g&&void 0!==g?g:0,"ArrowLeft"===e.key||"ArrowDown"===e.key?"down":"up");null===f||void 0===f||f(t),y(t)}},onValueChange:e=>{let[t]=e;y(A?d(m,t,null!==g&&void 0!==g?g:0):t)},step:null!==p&&void 0!==p?p:void 0,children:[b&&(0,c.Y)("div",{css:(0,i.AH)({position:"absolute",inset:0,marginRight:14},""),children:(0,o.keys)(m).map((e=>(0,c.Y)("div",{css:(0,i.AH)({position:"absolute",zIndex:1,top:0,right:0,bottom:0,marginLeft:-1.5,marginTop:6,pointerEvents:"none",borderRadius:"100%",backgroundColor:M.colors.actionPrimaryBackgroundDefault,height:8,width:8,opacity:.5},""),style:{left:Number(e)/(t-s)*100+"%"}},e)))}),(0,c.Y)(r.Apm.Track,{className:"TRACK",children:(0,c.Y)(r.Apm.Range,{})}),(0,c.Y)(r.Apm.Thumb,{css:(0,i.AH)({position:"relative",height:14,width:14},"")})]}),(0,c.Y)(a.I,{componentId:null!==x&&void 0!==x?x:"mlflow.experiment_tracking.common.line_smooth_slider",type:"number",disabled:v,min:s,max:t,css:h,step:p,value:null!==w&&void 0!==w?w:g,onBlur:()=>{(0,o.isUndefined)(w)||(A?(null===f||void 0===f||f(d(m,w,null!==g&&void 0!==g?g:0)),y(d(m,w,null!==g&&void 0!==g?g:0))):(null===f||void 0===f||f((0,o.clamp)(w,s,t)),y((0,o.clamp)(w,s,t))),S(void 0))},onChange:e=>{let{target:i,nativeEvent:n}=e;if(n instanceof InputEvent)S(Number(i.value));else if(A){const e=u(m,null!==g&&void 0!==g?g:0,Number(i.value)<Number(g)?"down":"up");y(e)}else y((0,o.clamp)(Number(i.value),s,t))}})]})}},79445:function(e,t,s){s.d(t,{W:function(){return l}});var i=s(31014),n=s(48012),r=s(27705),a=s(50111);const o=i.lazy((()=>s.e(4850).then(s.t.bind(s,84850,23)))),l=e=>{let{fallback:t,...s}=e;return(0,a.Y)(r.g,{children:(0,a.Y)(i.Suspense,{fallback:null!==t&&void 0!==t?t:(0,a.Y)(n.PLz,{active:!0}),children:(0,a.Y)(o,{...s})})})}},82645:function(e,t,s){s.d(t,{gT:function(){return C},mV:function(){return y}});var i=s(4473),n=s(68341),r=s(31014),a=s(76010),o=s(9133),l=s.n(o),c=s(44258),d=s(89933),u=s(3159),h=s(79445),p=s(32039),m=s(64912),g=s(50111);const y=(e,t)=>{if(t<=1||!e||e.length<=1||e.every((t=>t===e[0])))return e;const s=t/(d.W0+1),i=[];let n=0;for(let r=0;r<e.length;r++)if(isNaN(e[r]))i.push(e[r]);else{n=n*s+(1-s)*e[r];const t=n/(1-Math.pow(s,r+1));i.push(t)}return i},v={width:857.1,height:1e3,path:"m214-7h429v214h-429v-214z m500 0h72v500q0 8-6 21t-11 20l-157 156q-5 6-19 12t-22 5v-232q0-22-15-38t-38-16h-322q-22 0-37 16t-16 38v232h-72v-714h72v232q0 22 16 38t37 16h465q22 0 38-16t15-38v-232z m-214 518v178q0 8-5 13t-13 5h-107q-7 0-13-5t-5-13v-178q0-8 5-13t13-5h107q7 0 13 5t5 13z m357-18v-518q0-22-15-38t-38-16h-750q-23 0-38 16t-16 38v750q0 22 16 38t38 16h517q23 0 50-12t42-26l156-157q16-15 27-42t11-49z",transform:"matrix(1 0 0 -1 0 850)"};var f=(0,n.A)("annotationData");class x extends r.Component{constructor(){super(...arguments),this.regenerateInfinityAnnotations=()=>{var e;const{metrics:t,xAxis:s,extraLayout:n}=this.props,r="log"===(null===n||void 0===n||null===(e=n.yaxis)||void 0===e?void 0:e.type),a={};t.forEach((e=>{const{metricKey:t,history:i}=e;a[t]=(0,p.cX)({xValues:C.getXValuesForLineChart(i,s,this.props.intl),yValues:i.map((e=>"number"===typeof e.value?e.value:Number(e.value))),isLogScale:r,stringFormatter:e=>this.props.intl.formatMessage(e,{metricKey:t})})})),(0,i.A)(this,f)[f]=a},Object.defineProperty(this,f,{writable:!0,value:{}}),this.getPlotPropsForLineChart=()=>{const{metrics:e,xAxis:t,showPoint:s,lineSmoothness:n,isComparing:r,deselectedCurves:o}=this.props,l=new Set(o),c=[],d=[],u={data:e.map((e=>{const{metricKey:o,runDisplayName:u,history:h,runUuid:p}=e,m=h.map((e=>"number"===typeof e.value?e.value:Number(e.value))),g=m.filter((e=>!isNaN(e))).length<=1,v=!l.has(a.A.getCurveKey(p,o))||"legendonly";return(0,i.A)(this,f)[f]&&o in(0,i.A)(this,f)[f]&&!0===v&&(c.push(...(0,i.A)(this,f)[f][o].shapes),d.push(...(0,i.A)(this,f)[f][o].annotations)),{name:C.getLineLegend(o,u,r),x:C.getXValuesForLineChart(h,t),y:(g?m:y(m,n)).map((e=>isFinite(e)?e:NaN)),text:m.map((e=>isNaN(e)?e:e.toFixed(5))),type:"scattergl",mode:g?"markers":"lines+markers",marker:{opacity:g||s?1:0},hovertemplate:g||1===n?"%{y}":"Value: %{text}<br>Smoothed: %{y}",visible:v,runId:p,metricName:o}}))};return u.layout={...u.layout,...this.props.extraLayout,shapes:c,annotations:d},u},this.getPlotPropsForBarChart=()=>{const{runUuids:e,runDisplayNames:t,deselectedCurves:s}=this.props,i=this.props.metrics.reduce(((e,t)=>{const{runUuid:s,metricKey:i,history:n}=t,r=n[0]&&n[0].value;return e[i]?e[i][s]=r:e[i]={metricKey:i,[s]:r},e}),{}),n=l().sortBy(Object.values(i),"metricKey"),r=n.map((e=>e.metricKey)),o=new Set(s),c={data:e.map(((e,s)=>{const i=o.has(e)?{visible:"legendonly"}:{};return{name:a.A.truncateString(t[s],24),x:r,y:n.map((t=>t[e])),type:"bar",runId:e,...i}})),layout:{barmode:"group"}};return c.layout={...c.layout,...this.props.extraLayout},c}}static getXValuesForLineChart(e,t,s){if(0===e.length)return[];switch(t){case d.c1:return e.map((e=>{let{step:t}=e;return t}));case d.gW:{const{timestamp:t}=l().minBy(e,"timestamp");return e.map((e=>{let{timestamp:s}=e;return(s-t)/1e3}))}default:return e.map((e=>{let{timestamp:t}=e;return t}))}}componentDidMount(){this.regenerateInfinityAnnotations()}componentDidUpdate(){this.regenerateInfinityAnnotations()}render(){const{onLayoutChange:e,onClick:t,onLegendClick:s,onLegendDoubleClick:i}=this.props,n=this.props.chartType===u.S9?this.getPlotPropsForBarChart():this.getPlotPropsForLineChart();return(0,g.Y)("div",{className:"metrics-plot-view-container",children:(0,g.Y)(h.W,{...n,useResizeHandler:!0,onRelayout:e,onClick:t,onLegendClick:s,onLegendDoubleClick:i,style:{width:"100%",height:"100%"},layout:l().cloneDeep(n.layout),config:{displaylogo:!1,scrollZoom:!0,modeBarButtonsToRemove:["sendDataToCloud"],modeBarButtonsToAdd:[{name:"Download plot data as CSV",icon:v,click:()=>{const e=(0,u.I$)(this.props.metrics),t=new Blob([e],{type:"application/csv;charset=utf-8"});(0,c.saveAs)(t,"metrics.csv")}}]}})})}}x.getLineLegend=(e,t,s)=>{let i=e;return s&&(i+=`, ${a.A.truncateString(t,24)}`),i};const C=(0,m.Ay)(x)},89933:function(e,t,s){s.d(t,{W0:function(){return v},jC:function(){return M},gW:function(){return y},c1:function(){return g},up:function(){return m}});var i=s(31014),n=s(48012),r=s(32599),a=s(50111);const o=e=>(0,a.FD)("div",{css:l.wrapper,className:e.className,children:[(0,a.Y)("div",{css:l.track,children:(0,a.Y)("div",{css:l.progressTrack,style:{width:`${e.percent}%`}})}),e.format(e.percent)]}),l={wrapper:e=>({display:"flex",alignItems:"center",gap:e.spacing.sm}),track:e=>({backgroundColor:e.colors.backgroundSecondary,height:e.spacing.sm,flex:1,borderRadius:e.spacing.sm}),progressTrack:e=>({backgroundColor:e.colors.primary,height:e.spacing.sm,borderRadius:e.spacing.sm})};var c=s(3159),d=s(88443),u=s(64912),h=s(20193);const p=n.sxL.Group,m="wall",g="step",y="relative",v=100;var f={name:"1vuhrro",styles:"text-align:justify;text-align-last:left"};class x extends i.Component{constructor(){super(...arguments),this.handleMetricsSelectFilterChange=(e,t)=>t.props.title.toUpperCase().includes(e.toUpperCase()),this.getAllMetricKeys=()=>{const{distinctMetricKeys:e}=this.props;return e.map((e=>({title:e,value:e,key:e})))}}render(){const{chartType:e,yAxisLogScale:t,lineSmoothness:s,showPoint:i,numRuns:l,numCompletedRuns:u,disableSmoothnessControl:x}=this.props,M=(0,a.Y)(d.A,{id:"0gIYIc",defaultMessage:'Make the line between points "smoother" based on Exponential Moving Average. Smoothing can be useful for displaying the overall trend when the logging frequency is high.'}),A=(0,a.Y)(d.A,{id:"UEDu0c",defaultMessage:"MLflow UI automatically fetches metric histories for active runs and updates the metrics plot with a {interval} second interval.",values:{interval:Math.round(c.nl/1e3)}});return(0,a.FD)("div",{className:"plot-controls",css:[C.controlsWrapper,e===c.PK&&C.centeredControlsWrapper,""],children:[e===c.PK?(0,a.FD)("div",{children:[(0,a.Y)("div",{className:"inline-control",children:(0,a.FD)("div",{className:"control-label",children:[(0,a.Y)(d.A,{id:"nEoTsR",defaultMessage:"Completed Runs"})," ",(0,a.Y)(n.paO,{title:A,children:(0,a.Y)(n.JGH,{})}),(0,a.Y)(o,{percent:Math.round(100*u/l),format:()=>`${u}/${l}`})]})}),(0,a.FD)("div",{className:"inline-control",children:[(0,a.Y)("div",{className:"control-label",children:(0,a.Y)(d.A,{id:"yzxYIm",defaultMessage:"Points:"})}),(0,a.Y)(n.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_120","data-testid":"show-point-toggle",defaultChecked:i,onChange:this.props.handleShowPointChange})]}),!x&&(0,a.FD)("div",{className:"block-control",children:[(0,a.FD)("div",{className:"control-label",children:[(0,a.Y)(d.A,{id:"T4eipT",defaultMessage:"Line Smoothness"})," ",(0,a.Y)(n.paO,{title:M,children:(0,a.Y)(n.JGH,{})})]}),(0,a.Y)(h.o,{"data-testid":"smoothness-toggle",min:1,max:v,onChange:this.props.handleLineSmoothChange,value:s})]}),(0,a.FD)("div",{className:"block-control",children:[(0,a.Y)("div",{className:"control-label",children:(0,a.Y)(d.A,{id:"L26D19",defaultMessage:"X-axis:"})}),(0,a.FD)(p,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_154",name:"metrics-plot-x-axis-radio-group",css:C.xAxisControls,onChange:this.props.handleXAxisChange,value:this.props.selectedXAxis,children:[(0,a.Y)(n.sxL,{value:g,"data-testid":"x-axis-radio",children:(0,a.Y)(d.A,{id:"nQoUED",defaultMessage:"Step"})}),(0,a.Y)(n.sxL,{value:m,"data-testid":"x-axis-radio",children:(0,a.Y)(d.A,{id:"8flziy",defaultMessage:"Time (Wall)"})}),(0,a.Y)(n.sxL,{value:y,"data-testid":"x-axis-radio",children:(0,a.Y)(d.A,{id:"aNnPar",defaultMessage:"Time (Relative)"})})]})]})]}):null,(0,a.FD)("div",{className:"block-control",children:[(0,a.Y)("div",{className:"control-label",children:(0,a.Y)(d.A,{id:"QC5Vjq",defaultMessage:"Y-axis:"})}),(0,a.Y)(n._vn,{placeholder:this.props.intl.formatMessage({id:"aXw0NO",defaultMessage:"Please select metric"}),value:this.props.selectedMetricKeys,onChange:this.props.handleMetricsSelectChange,mode:"multiple",css:C.axisSelector,children:this.getAllMetricKeys().map((e=>(0,a.Y)(n._vn.Option,{value:e.value,children:e.title},e.key)))})]}),(0,a.FD)("div",{className:"inline-control",children:[(0,a.Y)("div",{className:"control-label",children:(0,a.Y)(d.A,{id:"HGG+BI",defaultMessage:"Y-axis Log Scale:"})}),(0,a.Y)(n.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_220",defaultChecked:t,onChange:this.props.handleYAxisLogScaleChange})]}),(0,a.Y)("div",{className:"inline-control",children:(0,a.FD)(r.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_222",css:f,onClick:this.props.handleDownloadCsv,children:[(0,a.Y)(d.A,{id:"3S3Ah4",defaultMessage:"Download data"}),(0,a.Y)("i",{className:"fas fa-download"})]})})]})}}x.defaultProps={disableSmoothnessControl:!1};const C={xAxisControls:e=>({label:{marginTop:e.spacing.xs,marginBottom:e.spacing.xs}}),controlsWrapper:{minWidth:"20%",maxWidth:"30%"},axisSelector:{width:"100%"},centeredControlsWrapper:{justifyContent:"center"}},M=(0,u.Ay)(x)}}]);
//# sourceMappingURL=1107.9610c3a0.chunk.js.map