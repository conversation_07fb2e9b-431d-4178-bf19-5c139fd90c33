(()=>{var e={37830:(e,r,t)=>{(()=>{if("undefined"!=typeof window&&"object"==typeof performance){let e=window.__dbModuleImportTracking=window.__dbModuleImportTracking||{},r=window.__dbModuleImportTrackingSeen=window.__dbModuleImportTrackingSeen||new Set;if(!r.has(40215)&&void 0===e[40215])return e[40215]={import:"./bootstrap",type:"lazy",loadTime:{start:window.performance.now()}},Promise.all([t.e(180),t.e(967)]).then(t.bind(t,40215)).finally(()=>{e[40215].loadTime.end=window.performance.now()}).then(e=>(r.add(40215),e))}return Promise.all([t.e(180),t.e(967)]).then(t.bind(t,40215))})()}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={id:n,loaded:!1,exports:{}},a={id:n,module:i,factory:e[n],require:t};return t.i.forEach(function(e){e(a)}),i=a.module,a.factory.call(i.exports,i,i.exports,a.require),i.loaded=!0,i.exports}t.m=e,t.i=[],t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},(()=>{var e,r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;t.t=function(n,o){if(1&o&&(n=this(n)),8&o||"object"==typeof n&&n&&(4&o&&n.__esModule||16&o&&"function"==typeof n.then))return n;var i=Object.create(null);t.r(i);var a={};e=e||[null,r({}),r([]),r(r)];for(var d=2&o&&n;"object"==typeof d&&!~e.indexOf(d);d=r(d))Object.getOwnPropertyNames(d).forEach(e=>a[e]=()=>n[e]);return a.default=()=>n,t.d(i,a),i}})(),t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.f={},t.e=e=>Promise.all(Object.keys(t.f).reduce((r,n)=>(t.f[n](e,r),r),[])),t.u=e=>{if(180===e)return"js/180.869b170a.chunk.js";if(967===e)return"js/967.92e18d95.chunk.js";if(939===e)return"js/939.91d5961d.chunk.js";if(487===e)return"js/487.9372e2e0.chunk.js";if(541===e)return"js/541.b181f44e.chunk.js";if(381===e)return"js/381.b0dd6b18.chunk.js";if(35===e)return"js/35.1623fe90.chunk.js";if(2===e)return"js/2.50231fdd.chunk.js";if(799===e)return"js/799.26151587.chunk.js";if(199===e)return"js/199.7e242aee.chunk.js";if(214===e)return"js/214.13558a5b.chunk.js"},t.miniCssF=e=>{if(967===e)return"css/967.b6918afe.chunk.css"},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),(()=>{var e={},r="databricks_mlModelTraceRenderer:";t.l=(n,o,i,a)=>{if(e[n]){e[n].push(o);return}if(void 0!==i)for(var d,u,c=document.getElementsByTagName("script"),l=0;l<c.length;l++){var s=c[l];if(s.getAttribute("src")==n||s.getAttribute("data-webpack")==r+i){d=s;break}}d||(u=!0,(d=document.createElement("script")).charset="utf-8",d.timeout=180,t.nc&&d.setAttribute("nonce",t.nc),d.setAttribute("data-webpack",r+i),d.src=n,0===d.src.indexOf(window.location.origin+"/")||(d.crossOrigin="anonymous")),e[n]=[o];var f=(r,t)=>{d.onerror=d.onload=null,clearTimeout(p);var o=e[n];if(delete e[n],d.parentNode&&d.parentNode.removeChild(d),o&&o.forEach(e=>e(t)),r)return r(t)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:d}),18e4);d.onerror=f.bind(null,d.onerror),d.onload=f.bind(null,d.onload),u&&document.head.appendChild(d)}})(),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.i.push(e=>{let r=e.factory;e.factory=function(...t){if("undefined"!=typeof window&&"object"==typeof performance&&void 0===window.__dbCurrentRootModuleFactoryId){let n=(window.__dbModuleImportTracking=window.__dbModuleImportTracking||{})[e.id],o=void 0!==n,i=window.performance.now();window.__dbCurrentRootModuleFactoryId=e.id,r.apply(this,t),window.__dbCurrentRootModuleFactoryId=void 0;let a=window.performance.now();o&&(n.initTime={start:i,end:a}),o||(window.__dbModuleInitTracking=window.__dbModuleInitTracking||[],window.__dbModuleInitTracking.push({id:e.id,start:i,end:a}))}else r.apply(this,t)}}),t.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{t.g.importScripts&&(e=t.g.location+"");var e,r=t.g.document;if(!e&&r&&(r.currentScript&&(e=r.currentScript.src),!e)){var n=r.getElementsByTagName("script");if(n.length)for(var o=n.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=n[o--].src}if(!e)throw Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),t.p=e+"../"})(),(()=>{var e=t.u,r=t.e,n={},o={};t.u=function(r){return e(r)+(n.hasOwnProperty(r)?"?"+n[r]:"")},t.e=function(i){return r(i).catch(function(r){var a=o.hasOwnProperty(i)?o[i]:3;if(a<1){var d=e(i);throw r.message="Loading chunk "+i+" failed after 3 retries.\n("+d+")",r.request=d,void 0!==window.recordTelemetry&&window.recordTelemetry("chunkLoadFailure"),r}return new Promise(function(e){var r=3-a+1;setTimeout(function(){var d="&retry-attempt="+r;n[i]="cache-bust=true"+d,o[i]=a-1,e(t.e(i))},(void 0!==window.recordTelemetry&&window.recordTelemetry("chunkLoadRetry",{retryCount:r}),1e3*Math.pow(2,r-1)))})})}})(),(()=>{if("undefined"==typeof document)return;var e=(e,r,n,o,i)=>{var a=document.createElement("link");return a.rel="stylesheet",a.type="text/css",t.nc&&(a.nonce=t.nc),a.onerror=a.onload=t=>{if(a.onerror=a.onload=null,"load"===t.type)o();else{var n=t&&t.type,d=t&&t.target&&t.target.href||r,u=Error("Loading CSS chunk "+e+" failed.\n("+n+": "+d+")");u.name="ChunkLoadError",u.code="CSS_CHUNK_LOAD_FAILED",u.type=n,u.request=d,a.parentNode&&a.parentNode.removeChild(a),i(u)}},a.href=r,0!==a.href.indexOf(window.location.origin+"/")&&(a.crossOrigin="anonymous"),n?n.parentNode.insertBefore(a,n.nextSibling):document.head.appendChild(a),a},r=(e,r)=>{for(var t=document.getElementsByTagName("link"),n=0;n<t.length;n++){var o=t[n],i=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(i===e||i===r))return o}for(var a=document.getElementsByTagName("style"),n=0;n<a.length;n++){var o=a[n],i=o.getAttribute("data-href");if(i===e||i===r)return o}},n=n=>new Promise((o,i)=>{var a=t.miniCssF(n),d=t.p+a;if(r(a,d))return o();e(n,d,null,o,i)}),o={442:0};t.f.miniCss=(e,r)=>{o[e]?r.push(o[e]):0!==o[e]&&({967:1})[e]&&r.push(o[e]=n(e).then(()=>{o[e]=0},r=>{throw delete o[e],r}))}})(),(()=>{var e={442:0};t.f.j=(r,n)=>{var o=t.o(e,r)?e[r]:void 0;if(0!==o){if(o)n.push(o[2]);else{var i=new Promise((t,n)=>o=e[r]=[t,n]);n.push(o[2]=i);var a=t.p+t.u(r),d=Error();t.l(a,n=>{if(t.o(e,r)&&(0!==(o=e[r])&&(e[r]=void 0),o)){var i=n&&("load"===n.type?"missing":n.type),a=n&&n.target&&n.target.src;d.message="Loading chunk "+r+" failed.\n("+i+": "+a+")",d.name="ChunkLoadError",d.type=i,d.request=a,o[1](d)}},"chunk-"+r,r)}}};var r=(r,n)=>{var o,i,a=n[0],d=n[1],u=n[2],c=0;if(a.some(r=>0!==e[r])){for(o in d)t.o(d,o)&&(t.m[o]=d[o]);u&&u(t)}for(r&&r(n);c<a.length;c++)i=a[c],t.o(e,i)&&e[i]&&e[i][0](),e[i]=0},n=self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[];n.forEach(r.bind(null,0)),n.push=r.bind(null,n.push.bind(n))})(),t.nc=void 0,t(37830)})();
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/ml-model-trace-renderer.417506a1.js.map