(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[541],{10160:e=>{e.exports=function(e){if(navigator.clipboard)return navigator.clipboard.writeText(e).catch(function(e){throw void 0!==e?e:new DOMException("The request is not allowed","NotAllowedError")});var t=document.createElement("span");t.textContent=e,t.style.whiteSpace="pre",t.style.webkitUserSelect="auto",t.style.userSelect="all",document.body.appendChild(t);var n=window.getSelection(),r=window.document.createRange();n.removeAllRanges(),r.selectNode(t),n.addRange(r);var u=!1;try{u=window.document.execCommand("copy")}catch(e){console.log("error",e)}return n.removeAllRanges(),window.document.body.removeChild(t),u?Promise.resolve():Promise.reject(new DOMException("The request is not allowed","NotAllowedError"))}},14112:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,u=Object.getOwnPropertyDescriptor,i=function(e){if("function"==typeof Array.isArray)return Array.isArray(e);return"[object Array]"===n.call(e)},l=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,u=t.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!u&&!i)return!1;for(r in e);return void 0===r||t.call(e,r)},o=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},a=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(u)return u(e,n).value}return e[n]};e.exports=function e(){var t,n,r,u,s,c,f=arguments[0],p=1,d=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<d;++p)if(t=arguments[p],null!=t)for(n in t)r=a(f,n),f!==(u=a(t,n))&&(h&&u&&(l(u)||(s=i(u)))?(s?(s=!1,c=r&&i(r)?r:[]):c=r&&l(r)?r:{},o(f,{name:n,newValue:e(h,c,u)})):void 0!==u&&o(f,{name:n,newValue:u}));return f}},99865:e=>{"use strict";e.exports=function(e,n){for(var r,u,i,l=e||"",o=n||"div",a={},s=0;s<l.length;)t.lastIndex=s,i=t.exec(l),(r=l.slice(s,i?i.index:l.length))&&(u?"#"===u?a.id=r:a.className?a.className.push(r):a.className=[r]:o=r,s+=r.length),i&&(u=i[0],s++);return{type:"element",tagName:o,properties:a,children:[]}};var t=/[#.]/g},61755:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,u=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,o=/^[;\s]*/,a=/^\s+|\s+$/g;function s(e){return e?e.replace(a,""):""}e.exports=function(e,a){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];a=a||{};var c=1,f=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");f=~r?e.length-r:f+e.length}function d(){var e={line:c,column:f};return function(t){return t.position=new h(e),F(r),t}}function h(e){this.start=e,this.end={line:c,column:f},this.source=a.source}h.prototype.content=e;var g=[];function m(t){var n=Error(a.source+":"+c+":"+f+": "+t);if(n.reason=t,n.filename=a.source,n.line=c,n.column=f,n.source=e,a.silent)g.push(n);else throw n}function F(t){var n=t.exec(e);if(!n)return;var r=n[0];return p(r),e=e.slice(r.length),n}function A(e){var t;for(e=e||[];t=y();)!1!==t&&e.push(t);return e}function y(){var t=d();if("/"!=e.charAt(0)||"*"!=e.charAt(1))return;for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return m("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}return F(r),function(){var e,n=[];for(A(n);e=function(){var e=d(),n=F(u);if(!n)return;if(y(),!F(i))return m("property missing ':'");var r=F(l),a=e({type:"declaration",property:s(n[0].replace(t,"")),value:r?s(r[0].replace(t,"")):""});return F(o),a}();)!1!==e&&(n.push(e),A(n));return n}()}},14533:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=122||t>=65&&t<=90}},3161:(e,t,n)=>{"use strict";var r=n(14533),u=n(33610);e.exports=function(e){return r(e)||u(e)}},33610:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=48&&t<=57}},37342:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=102||t>=65&&t<=70||t>=48&&t<=57}},84708:e=>{e.exports=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}},34233:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),a=Symbol.for("react.context"),s=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen"),m=Symbol.for("react.module.reference");function F(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case u:case l:case i:case f:case p:return e;default:switch(e=e&&e.$$typeof){case s:case a:case c:case h:case d:case o:return e;default:return t}}case r:return t}}}t.ContextConsumer=a,t.ContextProvider=o,t.Element=n,t.ForwardRef=c,t.Fragment=u,t.Lazy=h,t.Memo=d,t.Portal=r,t.Profiler=l,t.StrictMode=i,t.Suspense=f,t.SuspenseList=p,t.isAsyncMode=function(){return!1},t.isConcurrentMode=function(){return!1},t.isContextConsumer=function(e){return F(e)===a},t.isContextProvider=function(e){return F(e)===o},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return F(e)===c},t.isFragment=function(e){return F(e)===u},t.isLazy=function(e){return F(e)===h},t.isMemo=function(e){return F(e)===d},t.isPortal=function(e){return F(e)===r},t.isProfiler=function(e){return F(e)===l},t.isStrictMode=function(e){return F(e)===i},t.isSuspense=function(e){return F(e)===f},t.isSuspenseList=function(e){return F(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===u||e===l||e===i||e===f||e===p||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===d||e.$$typeof===o||e.$$typeof===a||e.$$typeof===c||e.$$typeof===m||void 0!==e.getModuleId)},t.typeOf=F},35381:(e,t,n)=>{"use strict";e.exports=n(34233)},8153:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(79617)).default;t.A=u},66164:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(45180)).default;t.A=u},22919:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(17487)).default;t.A=u},30136:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(21424)).default;t.A=u},70549:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(96861)).default;t.A=u},21733:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(35197)).default;t.A=u},60700:(e,t,n)=>{"use strict";var r=n(97926);t.A=void 0;var u=r(n(1828)).default;t.A=u},83214:(e,t,n)=>{"use strict";n.d(t,{A:()=>k});var r,u,i=n(44795),l=n(21444),o=n(17015);function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&r.push.apply(r,Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),r.forEach(function(t){(0,o.A)(e,t,n[t])})}return e}var s=n(65848),c=n.n(s),f=n(47148),p={},d=/\n/g;function h(e){var t,n,r,u,i=e.codeString,l=e.codeStyle,o=e.containerStyle,a=e.numberStyle,s=e.startingLineNumber;return c().createElement("code",{style:Object.assign({},l,void 0===o?{float:"left",paddingRight:"10px"}:o)},(n=(t={lines:i.replace(/\n$/,"").split("\n"),style:void 0===a?{}:a,startingLineNumber:s}).lines,r=t.startingLineNumber,u=t.style,n.map(function(e,t){var n=t+r;return c().createElement("span",{key:"line-".concat(t),className:"react-syntax-highlighter-line-number",style:"function"==typeof u?u(n):u},"".concat(n,"\n"))})))}function g(e,t){return{type:"element",tagName:"span",properties:{key:"line-number--".concat(e),className:["comment","linenumber","react-syntax-highlighter-line-number"],style:t},children:[{type:"text",value:e}]}}function m(e,t,n){return a({},{display:"inline-block",minWidth:"".concat(n.toString().length,".25em"),paddingRight:"1em",textAlign:"right",userSelect:"none"},"function"==typeof e?e(t):e)}function F(e){var t=e.children,n=e.lineNumber,r=e.lineNumberStyle,u=e.largestLineNumber,i=e.showInlineLineNumbers,l=e.lineProps,o=void 0===l?{}:l,s=e.className,c=e.showLineNumbers,f=e.wrapLongLines,p="function"==typeof o?o(n):o;if(p.className=void 0===s?[]:s,n&&i){var d=m(r,n,u);t.unshift(g(n,d))}return f&c&&(p.style=a({},p.style,{display:"flex"})),{type:"element",tagName:"span",properties:p,children:t}}function A(e){var t=e.rows,n=e.stylesheet,r=e.useInlineStyles;return t.map(function(e,t){return function e(t){var n=t.node,r=t.stylesheet,u=t.style,i=t.useInlineStyles,l=t.key,o=n.properties,s=n.type,d=n.tagName,h=n.value;if("text"===s)return h;if(d){var g,m,F=(g=0,function(t){return g+=1,t.map(function(t,n){return e({node:t,stylesheet:r,useInlineStyles:i,key:"code-segment-".concat(g,"-").concat(n)})})});if(i){var A=Object.keys(r).reduce(function(e,t){return t.split(".").forEach(function(t){e.includes(t)||e.push(t)}),e},[]),y=o.className&&o.className.includes("token")?["token"]:[],x=o.className&&y.concat(o.className.filter(function(e){return!A.includes(e)}));m=a({},o,{className:x.join(" ")||void 0,style:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;return(function(e){if(0===e.length||1===e.length)return e;var t=e.join(".");return p[t]||(p[t]=function(e){var t=e.length;if(0===t||1===t)return e;if(2===t)return[e[0],e[1],"".concat(e[0],".").concat(e[1]),"".concat(e[1],".").concat(e[0])];if(3===t)return[e[0],e[1],e[2],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0])];if(t>=4)return[e[0],e[1],e[2],e[3],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[2],".").concat(e[3]),"".concat(e[3],".").concat(e[0]),"".concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[0]),"".concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[3],".").concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[2],".").concat(e[1],".").concat(e[0])]}(e)),p[t]})(e.filter(function(e){return"token"!==e})).reduce(function(e,t){return a({},e,n[t])},t)}(o.className,Object.assign({},o.style,void 0===u?{}:u),r)})}else m=a({},o,{className:o.className.join(" ")});var b=F(n.children);return c().createElement(d,(0,f.A)({key:l},m),b)}}({node:e,stylesheet:n,useInlineStyles:r,key:"code-segement".concat(t)})})}function y(e){return e&&void 0!==e.highlightAuto}var x=n(88987),b=n.n(x),v=(r=b(),u={},function(e){var t=e.language,n=e.children,o=e.style,s=void 0===o?u:o,f=e.customStyle,p=void 0===f?{}:f,x=e.codeTagProps,b=void 0===x?{className:t?"language-".concat(t):void 0,style:a({},s['code[class*="language-"]'],s['code[class*="language-'.concat(t,'"]')])}:x,v=e.useInlineStyles,k=void 0===v||v,E=e.showLineNumbers,C=void 0!==E&&E,D=e.showInlineLineNumbers,w=void 0===D||D,S=e.startingLineNumber,B=void 0===S?1:S,L=e.lineNumberContainerStyle,T=e.lineNumberStyle,P=void 0===T?{}:T,O=e.wrapLines,I=e.wrapLongLines,N=void 0!==I&&I,z=e.lineProps,j=e.renderer,M=e.PreTag,R=void 0===M?"pre":M,_=e.CodeTag,U=void 0===_?"code":_,$=e.code,H=void 0===$?Array.isArray(n)?n[0]:n:$,V=e.astGenerator,q=(0,i.A)(e,["language","children","style","customStyle","codeTagProps","useInlineStyles","showLineNumbers","showInlineLineNumbers","startingLineNumber","lineNumberContainerStyle","lineNumberStyle","wrapLines","wrapLongLines","lineProps","renderer","PreTag","CodeTag","code","astGenerator"]);V=V||r;var Z=C?c().createElement(h,{containerStyle:L,codeStyle:b.style||{},numberStyle:P,startingLineNumber:B,codeString:H}):null,W=s.hljs||s['pre[class*="language-"]']||{backgroundColor:"#fff"},Q=y(V)?"hljs":"prismjs",Y=k?Object.assign({},q,{style:Object.assign({},W,p)}):Object.assign({},q,{className:q.className?"".concat(Q," ").concat(q.className):Q,style:Object.assign({},p)});if(!V)return c().createElement(R,Y,Z,c().createElement(U,b,H));(void 0===O&&j||N)&&(O=!0),j=j||A;var K=[{type:"text",value:H}],X=function(e){var t=e.astGenerator,n=e.language,r=e.code,u=e.defaultCodeValue;if(y(t)){var i=-1!==t.listLanguages().indexOf(n);if("text"===n)return{value:u,language:"text"};if(i)return t.highlight(n,r);return t.highlightAuto(r)}try{return n&&"text"!==n?{value:t.highlight(r,n)}:{value:u}}catch(e){return{value:u}}}({astGenerator:V,language:t,code:H,defaultCodeValue:K});null===X.language&&(X.value=K);var G=X.value.length+B,J=function(e,t,n,r,u,i,o,a,s){var c,f=function e(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],u=0;u<t.length;u++){var i=t[u];if("text"===i.type)r.push(F({children:[i],className:(0,l.A)(new Set(n))}));else if(i.children){var o=n.concat(i.properties.className);r=r.concat(e(i.children,o))}}return r}(e.value),p=[],h=-1,A=0;function y(e,i){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t||l.length>0?function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return F({children:e,lineNumber:t,lineNumberStyle:a,largestLineNumber:o,showInlineLineNumbers:u,lineProps:n,className:i,showLineNumbers:r,wrapLongLines:s})}(e,i,l):function(e,t){if(r&&t&&u){var n=m(a,t,o);e.unshift(g(t,n))}return e}(e,i)}for(;A<f.length;)!function(){var e=f[A],t=e.children[0].value;if(t.match(d)){var n=t.split("\n");n.forEach(function(t,u){var l=r&&p.length+i,o={type:"text",value:"".concat(t,"\n")};if(0===u){var a=y(f.slice(h+1,A).concat(F({children:[o],className:e.properties.className})),l);p.push(a)}else if(u===n.length-1){if(f[A+1]&&f[A+1].children&&f[A+1].children[0]){var s=F({children:[{type:"text",value:"".concat(t)}],className:e.properties.className});f.splice(A+1,0,s)}else{var c=y([o],l,e.properties.className);p.push(c)}}else{var d=y([o],l,e.properties.className);p.push(d)}}),h=A}A++}();if(h!==f.length-1){var x=f.slice(h+1,f.length);if(x&&x.length){var b=y(x,r&&p.length+i);p.push(b)}}return t?p:(c=[]).concat.apply(c,p)}(X,O,void 0===z?{}:z,C,w,B,G,P,N);return N?b.style=a({},b.style,{whiteSpace:"pre-wrap"}):b.style=a({},b.style,{whiteSpace:"pre"}),c().createElement(R,Y,c().createElement(U,b,!w&&Z,j({rows:J,stylesheet:s,useInlineStyles:k})))});v.registerLanguage=function(e,t){return b().register(t)};let k=v},88987:(e,t,n)=>{"use strict";var r="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof n.g?n.g:{},u=function(){var e="Prism"in r,t=e?r.Prism:void 0;return function(){e?r.Prism=t:delete r.Prism,e=void 0,t=void 0}}();r.Prism={manual:!0,disableWorkerMessageHandler:!0};var i=n(4329),l=n(18311),o=n(13398),a=n(3459),s=n(78164),c=n(71933),f=n(21424);u();var p={}.hasOwnProperty;function d(){}d.prototype=o;var h=new d;function g(e){if("function"!=typeof e||!e.displayName)throw Error("Expected `function` for `grammar`, got `"+e+"`");void 0===h.languages[e.displayName]&&e(h)}e.exports=h,h.highlight=function(e,t){var n,r=o.highlight;if("string"!=typeof e)throw Error("Expected `string` for `value`, got `"+e+"`");if("Object"===h.util.type(t))n=t,t=null;else{if("string"!=typeof t)throw Error("Expected `string` for `name`, got `"+t+"`");if(p.call(h.languages,t))n=h.languages[t];else throw Error("Unknown language: `"+t+"` is not registered")}return r.call(this,e,n,t)},h.register=g,h.alias=function(e,t){var n,r,u,i,l=h.languages,o=e;for(n in t&&((o={})[e]=t),o)for(u=(r="string"==typeof(r=o[n])?[r]:r).length,i=-1;++i<u;)l[r[i]]=l[n]},h.registered=function(e){if("string"!=typeof e)throw Error("Expected `string` for `language`, got `"+e+"`");return p.call(h.languages,e)},h.listLanguages=function(){var e,t=h.languages,n=[];for(e in t)p.call(t,e)&&"object"==typeof t[e]&&n.push(e);return n},g(a),g(s),g(c),g(f),h.util.encode=function(e){return e},h.Token.stringify=function(e,t,n){var r;if("string"==typeof e)return{type:"text",value:e};if("Array"===h.util.type(e))return function(e,t){for(var n,r=[],u=e.length,i=-1;++i<u;)""!==(n=e[i])&&null!=n&&r.push(n);for(i=-1,u=r.length;++i<u;)n=r[i],r[i]=h.Token.stringify(n,t,r);return r}(e,t);return r={type:e.type,content:h.Token.stringify(e.content,t,n),tag:"span",classes:["token",e.type],attributes:{},language:t,parent:n},e.alias&&(r.classes=r.classes.concat(e.alias)),h.hooks.run("wrap",r),i(r.tag+"."+r.classes.join("."),function(e){var t;for(t in e)e[t]=l(e[t]);return e}(r.attributes),r.content)}},71933:e=>{"use strict";function t(e){e.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|interface|extends|implements|trait|instanceof|new)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/}}e.exports=t,t.displayName="clike",t.aliases=[]},78164:e=>{"use strict";function t(e){var t,n;t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,(n=e.languages.markup)&&(n.tag.addInlined("style","css"),n.tag.addAttribute("style","css"))}e.exports=t,t.displayName="css",t.aliases=[]},79617:e=>{"use strict";function t(e){e.languages.go=e.languages.extend("clike",{string:{pattern:/(["'`])(?:\\[\s\S]|(?!\1)[^\\])*\1/,greedy:!0},keyword:/\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/,boolean:/\b(?:_|iota|nil|true|false)\b/,number:/(?:\b0x[a-f\d]+|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[-+]?\d+)?)i?/i,operator:/[*\/%^!=]=?|\+[=+]?|-[=-]?|\|[=|]?|&(?:=|&|\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\.\.\./,builtin:/\b(?:bool|byte|complex(?:64|128)|error|float(?:32|64)|rune|string|u?int(?:8|16|32|64)?|uintptr|append|cap|close|complex|copy|delete|imag|len|make|new|panic|print(?:ln)?|real|recover)\b/}),delete e.languages.go["class-name"]}e.exports=t,t.displayName="go",t.aliases=[]},45180:e=>{"use strict";function t(e){e.languages.hcl={comment:/(?:\/\/|#).*|\/\*[\s\S]*?(?:\*\/|$)/,heredoc:{pattern:/<<-?(\w+\b)[\s\S]*?^[ \t]*\1/m,greedy:!0,alias:"string"},keyword:[{pattern:/(?:resource|data)\s+(?:"(?:\\[\s\S]|[^\\"])*")(?=\s+"[\w-]+"\s+\{)/i,inside:{type:{pattern:/(resource|data|\s+)(?:"(?:\\[\s\S]|[^\\"])*")/i,lookbehind:!0,alias:"variable"}}},{pattern:/(?:provider|provisioner|variable|output|module|backend)\s+(?:[\w-]+|"(?:\\[\s\S]|[^\\"])*")\s+(?=\{)/i,inside:{type:{pattern:/(provider|provisioner|variable|output|module|backend)\s+(?:[\w-]+|"(?:\\[\s\S]|[^\\"])*")\s+/i,lookbehind:!0,alias:"variable"}}},/[\w-]+(?=\s+\{)/],property:[/[-\w\.]+(?=\s*=(?!=))/,/"(?:\\[\s\S]|[^\\"])+"(?=\s*[:=])/],string:{pattern:/"(?:[^\\$"]|\\[\s\S]|\$(?:(?=")|\$+(?!\$)|[^"${])|\$\{(?:[^{}"]|"(?:[^\\"]|\\[\s\S])*")*\})*"/,greedy:!0,inside:{interpolation:{pattern:/(^|[^$])\$\{(?:[^{}"]|"(?:[^\\"]|\\[\s\S])*")*\}/,lookbehind:!0,inside:{type:{pattern:/(\b(?:terraform|var|self|count|module|path|data|local)\b\.)[\w\*]+/i,lookbehind:!0,alias:"variable"},keyword:/\b(?:terraform|var|self|count|module|path|data|local)\b/i,function:/\w+(?=\()/,string:{pattern:/"(?:\\[\s\S]|[^\\"])*"/,greedy:!0},number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?(?:e[+-]?\d+)?/i,punctuation:/[!\$#%&'()*+,.\/;<=>@\[\\\]^`{|}~?:]/}}}},number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?(?:e[+-]?\d+)?/i,boolean:/\b(?:true|false)\b/i,punctuation:/[=\[\]{}]/}}e.exports=t,t.displayName="hcl",t.aliases=[]},17487:e=>{"use strict";function t(e){var t,n,r;t=/\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\b/,r={pattern:RegExp((n=/(^|[^\w.])(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source)+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}},punctuation:/\./}},e.languages.java=e.languages.extend("clike",{"class-name":[r,{pattern:RegExp(n+/[A-Z]\w*(?=\s+\w+\s*[;,=()])/.source),lookbehind:!0,inside:r.inside}],keyword:t,function:[e.languages.clike.function,{pattern:/(::\s*)[a-z_]\w*/,lookbehind:!0}],number:/\b0b[01][01_]*L?\b|\b0x(?:\.[\da-f_p+-]+|[\da-f_]+(?:\.[\da-f_p+-]+)?)\b|(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?\d[\d_]*)?[dfl]?/i,operator:{pattern:/(^|[^.])(?:<<=?|>>>?=?|->|--|\+\+|&&|\|\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,lookbehind:!0}}),e.languages.insertBefore("java","string",{"triple-quoted-string":{pattern:/"""[ \t]*[\r\n](?:(?:"|"")?(?:\\.|[^"\\]))*"""/,greedy:!0,alias:"string"}}),e.languages.insertBefore("java","class-name",{annotation:{pattern:/(^|[^.])@\w+(?:\s*\.\s*\w+)*/,lookbehind:!0,alias:"punctuation"},generics:{pattern:/<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&))*>)*>)*>)*>/,inside:{"class-name":r,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}},namespace:{pattern:RegExp(/(\b(?:exports|import(?:\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\s+)(?!<keyword>)[a-z]\w*(?:\.[a-z]\w*)*\.?/.source.replace(/<keyword>/g,function(){return t.source})),lookbehind:!0,inside:{punctuation:/\./}}})}e.exports=t,t.displayName="java",t.aliases=[]},21424:e=>{"use strict";function t(e){e.languages.javascript=e.languages.extend("clike",{"class-name":[e.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),e.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,e.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)\/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/,lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:e.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:e.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:e.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:e.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:e.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),e.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:e.languages.javascript}},string:/[\s\S]+/}}}),e.languages.markup&&(e.languages.markup.tag.addInlined("script","javascript"),e.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),e.languages.js=e.languages.javascript}e.exports=t,t.displayName="javascript",t.aliases=["js"]},96861:e=>{"use strict";function t(e){e.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:true|false)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},e.languages.webmanifest=e.languages.json}e.exports=t,t.displayName="json",t.aliases=["webmanifest"]},3459:e=>{"use strict";function t(e){e.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},e.languages.markup.tag.inside["attr-value"].inside.entity=e.languages.markup.entity,e.languages.markup.doctype.inside["internal-subset"].inside=e.languages.markup,e.hooks.add("wrap",function(e){"entity"===e.type&&(e.attributes.title=e.content.value.replace(/&amp;/,"&"))}),Object.defineProperty(e.languages.markup.tag,"addInlined",{value:function(t,n){var r={};r["language-"+n]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:e.languages[n]},r.cdata=/^<!\[CDATA\[|\]\]>$/i;var u={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:r}};u["language-"+n]={pattern:/[\s\S]+/,inside:e.languages[n]};var i={};i[t]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return t}),"i"),lookbehind:!0,greedy:!0,inside:u},e.languages.insertBefore("markup","cdata",i)}}),Object.defineProperty(e.languages.markup.tag,"addAttribute",{value:function(t,n){e.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+t+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[n,"language-"+n],inside:e.languages[n]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),e.languages.html=e.languages.markup,e.languages.mathml=e.languages.markup,e.languages.svg=e.languages.markup,e.languages.xml=e.languages.extend("markup",{}),e.languages.ssml=e.languages.xml,e.languages.atom=e.languages.xml,e.languages.rss=e.languages.xml}e.exports=t,t.displayName="markup",t.aliases=["html","mathml","svg","xml","ssml","atom","rss"]},35197:e=>{"use strict";function t(e){e.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0},"string-interpolation":{pattern:/(?:f|rf|fr)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|rb|br)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|rb|br)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/im,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:and|as|assert|async|await|break|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:True|False|None)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?\b/i,operator:/[-+%=]=?|!=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},e.languages.python["string-interpolation"].inside.interpolation.inside.rest=e.languages.python,e.languages.py=e.languages.python}e.exports=t,t.displayName="python",t.aliases=["py"]},1828:e=>{"use strict";function t(e){!function(e){var t=/[*&][^\s[\]{},]+/,n=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,r="(?:"+n.source+"(?:[ 	]+"+t.source+")?|"+t.source+"(?:[ 	]+"+n.source+")?)",u=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source}),i=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function l(e,t){return t=(t||"").replace(/m/g,"")+"m",RegExp(/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,function(){return r}).replace(/<<value>>/g,function(){return e}),t)}e.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,function(){return r})),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,function(){return r}).replace(/<<key>>/g,function(){return"(?:"+u+"|"+i+")"})),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:l(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:l(/true|false/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:l(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:l(i),lookbehind:!0,greedy:!0},number:{pattern:l(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:n,important:t,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},e.languages.yml=e.languages.yaml}(e)}e.exports=t,t.displayName="yaml",t.aliases=["yml"]},80912:(e,t)=>{"use strict";t.q=function(e){for(var t,n=[],r=String(e||""),u=r.indexOf(","),i=0,l=!1;!l;)-1===u&&(u=r.length,l=!0),((t=r.slice(i,u).trim())||!l)&&n.push(t),i=u+1,u=r.indexOf(",",i);return n}},47363:(e,t,n)=>{"use strict";var r=n(41285),u=n(69171),i=n(99865),l=n(42011).q,o=n(80912).q;e.exports=function(e,t,n){var u=n?function(e){for(var t,n=e.length,r=-1,u={};++r<n;)u[(t=e[r]).toLowerCase()]=t;return u}(n):null;return function(n,c){var f,p,d=i(n,t),h=Array.prototype.slice.call(arguments,2),g=d.tagName.toLowerCase();if(d.tagName=u&&a.call(u,g)?u[g]:g,c&&("string"==typeof(f=c)||"length"in f||function(e,t){var n=t.type;if("input"===e||!n||"string"!=typeof n)return!1;if("object"==typeof t.children&&"length"in t.children)return!0;if(n=n.toLowerCase(),"button"===e)return"menu"!==n&&"submit"!==n&&"reset"!==n&&"button"!==n;return"value"in t}(d.tagName,f))&&(h.unshift(c),c=null),c)for(p in c)!function(t,n,u){var i,a,c;if(null==u||u!=u)return;a=(i=r(e,n)).property,"string"==typeof(c=u)&&(i.spaceSeparated?c=l(c):i.commaSeparated?c=o(c):i.commaOrSpaceSeparated&&(c=l(o(c).join(" ")))),"style"===a&&"string"!=typeof u&&(c=function(e){var t,n=[];for(t in e)n.push([t,e[t]].join(": "));return n.join("; ")}(c)),"className"===a&&t.className&&(c=t.className.concat(c)),t[a]=function(e,t,n){var r,u,i;if("object"!=typeof n||!("length"in n))return s(e,t,n);for(u=n.length,r=-1,i=[];++r<u;)i[r]=s(e,t,n[r]);return i}(i,a,c)}(d.properties,p,c[p]);return function e(t,n){var r,u;if("string"==typeof n||"number"==typeof n){t.push({type:"text",value:String(n)});return}if("object"==typeof n&&"length"in n){for(r=-1,u=n.length;++r<u;)e(t,n[r]);return}if("object"!=typeof n||!("type"in n))throw Error("Expected node, nodes, or string, got `"+n+"`");t.push(n)}(d.children,h),"template"===d.tagName&&(d.content={type:"root",children:d.children},d.children=[]),d}};var a={}.hasOwnProperty;function s(e,t,n){var r=n;return e.number||e.positiveNumber?isNaN(r)||""===r||(r=Number(r)):(e.boolean||e.overloadedBoolean)&&"string"==typeof r&&(""===r||u(n)===u(t))&&(r=!0),r}},70764:(e,t,n)=>{"use strict";var r=n(34807),u=n(47363)(r,"div");u.displayName="html",e.exports=u},4329:(e,t,n)=>{"use strict";e.exports=n(70764)},56999:e=>{"use strict";var t;e.exports=function(e){var n,r="&"+e+";";if((t=t||document.createElement("i")).innerHTML=r,59===(n=t.textContent).charCodeAt(n.length-1)&&"semi"!==e)return!1;return n!==r&&n}},18311:(e,t,n)=>{"use strict";var r=n(80414),u=n(61492),i=n(33610),l=n(37342),o=n(3161),a=n(56999);e.exports=function(e,t){var n,i,l={};for(i in t||(t={}),p)n=t[i],l[i]=null==n?p[i]:n;return(l.position.indent||l.position.start)&&(l.indent=l.position.indent||[],l.position=l.position.start),function(e,t){var n,i,l,p,y,x,b,v,k,E,C,D,w,S,B,L,T,P,O,I,N,z=t.additional,j=t.nonTerminated,M=t.text,R=t.reference,_=t.warning,U=t.textContext,$=t.referenceContext,H=t.warningContext,V=t.position,q=t.indent||[],Z=e.length,W=0,Q=-1,Y=V.column||1,K=V.line||1,X="",G=[];for("string"==typeof z&&(z=z.charCodeAt(0)),P=J(),E=_?function(e,t){var n=J();n.column+=t,n.offset+=t,_.call(H,A[e],n,e)}:f,W--,Z++;++W<Z;)if(10===b&&(Y=q[Q]||1),38===(b=e.charCodeAt(W))){if(9===(k=e.charCodeAt(W+1))||10===k||12===k||32===k||38===k||60===k||k!=k||z&&k===z){X+=c(b),Y++;continue}for(S=B=W+1,N=B,35===k?(N=++S,88===(k=e.charCodeAt(N))||120===k?(L=h,N=++S):L=g):L=d,l="",w="",x="",T=F[L],N--;++N<Z&&T(k=e.charCodeAt(N));)x+=c(k),L===d&&s.call(r,x)&&(l=x,w=r[x]);(y=59===e.charCodeAt(N))&&(N++,(p=L===d&&a(x))&&(l=x,w=p)),I=1+N-B,(y||j)&&(x?L===d?(y&&!w?E(5,1):(l!==x&&(I=1+(N=S+l.length)-S,y=!1),y||(C=l?1:3,t.attribute?61===(k=e.charCodeAt(N))?(E(C,I),w=null):o(k)?w=null:E(C,I):E(C,I))),v=w):(y||E(2,I),(n=v=parseInt(x,m[L]))>=55296&&n<=57343||n>1114111?(E(7,I),v=c(65533)):v in u?(E(6,I),v=u[v]):(D="",((i=v)>=1&&i<=8||11===i||i>=13&&i<=31||i>=127&&i<=159||i>=64976&&i<=65007||(65535&i)==65535||(65535&i)==65534)&&E(6,I),v>65535&&(v-=65536,D+=c(v>>>10|55296),v=56320|1023&v),v=D+c(v))):L!==d&&E(4,I)),v?(ee(),P=J(),W=N-1,Y+=N-B+1,G.push(v),O=J(),O.offset++,R&&R.call($,v,{start:P,end:O},e.slice(B-1,N)),P=O):(x=e.slice(B-1,N),X+=x,Y+=x.length,W=N-1)}else 10===b&&(K++,Q++,Y=0),b==b?(X+=c(b),Y++):ee();return G.join("");function J(){return{line:K,column:Y,offset:W+(V.offset||0)}}function ee(){X&&(G.push(X),M&&M.call(U,X,{start:P,end:J()}),X="")}}(e,l)};var s={}.hasOwnProperty,c=String.fromCharCode,f=Function.prototype,p={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},d="named",h="hexadecimal",g="decimal",m={};m[h]=16,m[g]=10;var F={};F[d]=o,F[g]=i,F[h]=l;var A={};A[1]="Named character references must be terminated by a semicolon",A[2]="Numeric character references must be terminated by a semicolon",A[3]="Named character references cannot be empty",A[4]="Numeric character references cannot be empty",A[5]="Named character references must be known",A[6]="Numeric character references cannot be disallowed",A[7]="Numeric character references cannot be outside the permissible Unicode range"},13398:(e,t,n)=>{var r=function(e){var t=/\blang(?:uage)?-([\w-]+)\b/i,n=0,r={},u={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(t){if(t instanceof i)return new i(t.type,e(t.content),t.alias);if(Array.isArray(t))return t.map(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n}),e.__id},clone:function e(t,n){var r,i;switch(n=n||{},u.util.type(t)){case"Object":if(n[i=u.util.objId(t)])return n[i];for(var l in r={},n[i]=r,t)t.hasOwnProperty(l)&&(r[l]=e(t[l],n));return r;case"Array":if(n[i=u.util.objId(t)])return n[i];return r=[],n[i]=r,t.forEach(function(t,u){r[u]=e(t,n)}),r;default:return t}},getLanguage:function(e){for(;e&&!t.test(e.className);)e=e.parentElement;if(e)return(e.className.match(t)||[,"none"])[1].toLowerCase();return"none"},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw Error()}catch(r){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(r.stack)||[])[1];if(e){var t=document.getElementsByTagName("script");for(var n in t)if(t[n].src==e)return t[n]}return null}},isActive:function(e,t,n){for(var r="no-"+t;e;){var u=e.classList;if(u.contains(t))return!0;if(u.contains(r))return!1;e=e.parentElement}return!!n}},languages:{plain:r,plaintext:r,text:r,txt:r,extend:function(e,t){var n=u.util.clone(u.languages[e]);for(var r in t)n[r]=t[r];return n},insertBefore:function(e,t,n,r){var i=(r=r||u.languages)[e],l={};for(var o in i)if(i.hasOwnProperty(o)){if(o==t)for(var a in n)n.hasOwnProperty(a)&&(l[a]=n[a]);n.hasOwnProperty(o)||(l[o]=i[o])}var s=r[e];return r[e]=l,u.languages.DFS(u.languages,function(t,n){n===s&&t!=e&&(this[t]=l)}),l},DFS:function e(t,n,r,i){i=i||{};var l=u.util.objId;for(var o in t)if(t.hasOwnProperty(o)){n.call(t,o,t[o],r||o);var a=t[o],s=u.util.type(a);"Object"!==s||i[l(a)]?"Array"!==s||i[l(a)]||(i[l(a)]=!0,e(a,n,o,i)):(i[l(a)]=!0,e(a,n,null,i))}}},plugins:{},highlightAll:function(e,t){u.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};u.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),u.hooks.run("before-all-elements-highlight",r);for(var i,l=0;i=r.elements[l++];)u.highlightElement(i,!0===t,r.callback)},highlightElement:function(n,r,i){var l=u.util.getLanguage(n),o=u.languages[l];n.className=n.className.replace(t,"").replace(/\s+/g," ")+" language-"+l;var a=n.parentElement;a&&"pre"===a.nodeName.toLowerCase()&&(a.className=a.className.replace(t,"").replace(/\s+/g," ")+" language-"+l);var s=n.textContent,c={element:n,language:l,grammar:o,code:s};function f(e){c.highlightedCode=e,u.hooks.run("before-insert",c),c.element.innerHTML=c.highlightedCode,u.hooks.run("after-highlight",c),u.hooks.run("complete",c),i&&i.call(c.element)}if(u.hooks.run("before-sanity-check",c),(a=c.element.parentElement)&&"pre"===a.nodeName.toLowerCase()&&!a.hasAttribute("tabindex")&&a.setAttribute("tabindex","0"),!c.code){u.hooks.run("complete",c),i&&i.call(c.element);return}if(u.hooks.run("before-highlight",c),!c.grammar){f(u.util.encode(c.code));return}if(r&&e.Worker){var p=new Worker(u.filename);p.onmessage=function(e){f(e.data)},p.postMessage(JSON.stringify({language:c.language,code:c.code,immediateClose:!0}))}else f(u.highlight(c.code,c.grammar,c.language))},highlight:function(e,t,n){var r={code:e,grammar:t,language:n};return u.hooks.run("before-tokenize",r),r.tokens=u.tokenize(r.code,r.grammar),u.hooks.run("after-tokenize",r),i.stringify(u.util.encode(r.tokens),r.language)},tokenize:function(e,t){var n=t.rest;if(n){for(var r in n)t[r]=n[r];delete t.rest}var s=new o;return a(s,s.head,e),function e(t,n,r,o,s,c){for(var f in r)if(r.hasOwnProperty(f)&&r[f]){var p=r[f];p=Array.isArray(p)?p:[p];for(var d=0;d<p.length;++d){if(c&&c.cause==f+","+d)return;var h=p[d],g=h.inside,m=!!h.lookbehind,F=!!h.greedy,A=h.alias;if(F&&!h.pattern.global){var y=h.pattern.toString().match(/[imsuy]*$/)[0];h.pattern=RegExp(h.pattern.source,y+"g")}for(var x=h.pattern||h,b=o.next,v=s;b!==n.tail&&(!c||!(v>=c.reach));v+=b.value.length,b=b.next){var k,E=b.value;if(n.length>t.length)return;if(!(E instanceof i)){var C=1;if(F){if(!(k=l(x,v,t,m)))break;var D=k.index,w=k.index+k[0].length,S=v;for(S+=b.value.length;D>=S;)S+=(b=b.next).value.length;if(S-=b.value.length,v=S,b.value instanceof i)continue;for(var B=b;B!==n.tail&&(S<w||"string"==typeof B.value);B=B.next)C++,S+=B.value.length;C--,E=t.slice(v,S),k.index-=v}else if(!(k=l(x,0,E,m)))continue;var D=k.index,L=k[0],T=E.slice(0,D),P=E.slice(D+L.length),O=v+E.length;c&&O>c.reach&&(c.reach=O);var I=b.prev;if(T&&(I=a(n,I,T),v+=T.length),function(e,t,n){for(var r=t.next,u=0;u<n&&r!==e.tail;u++)r=r.next;t.next=r,r.prev=t,e.length-=u}(n,I,C),b=a(n,I,new i(f,g?u.tokenize(L,g):L,A,L)),P&&a(n,b,P),C>1){var N={cause:f+","+d,reach:O};e(t,n,r,b.prev,v,N),c&&N.reach>c.reach&&(c.reach=N.reach)}}}}}}(e,s,t,s.head,0),function(e){for(var t=[],n=e.head.next;n!==e.tail;)t.push(n.value),n=n.next;return t}(s)},hooks:{all:{},add:function(e,t){var n=u.hooks.all;n[e]=n[e]||[],n[e].push(t)},run:function(e,t){var n=u.hooks.all[e];if(!n||!n.length)return;for(var r,i=0;r=n[i++];)r(t)}},Token:i};function i(e,t,n,r){this.type=e,this.content=t,this.alias=n,this.length=0|(r||"").length}function l(e,t,n,r){e.lastIndex=t;var u=e.exec(n);if(u&&r&&u[1]){var i=u[1].length;u.index+=i,u[0]=u[0].slice(i)}return u}function o(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function a(e,t,n){var r=t.next,u={value:n,prev:t,next:r};return t.next=u,r.prev=u,e.length++,u}if(e.Prism=u,i.stringify=function e(t,n){if("string"==typeof t)return t;if(Array.isArray(t)){var r="";return t.forEach(function(t){r+=e(t,n)}),r}var i={type:t.type,content:e(t.content,n),tag:"span",classes:["token",t.type],attributes:{},language:n},l=t.alias;l&&(Array.isArray(l)?Array.prototype.push.apply(i.classes,l):i.classes.push(l)),u.hooks.run("wrap",i);var o="";for(var a in i.attributes)o+=" "+a+'="'+(i.attributes[a]||"").replace(/"/g,"&quot;")+'"';return"<"+i.tag+' class="'+i.classes.join(" ")+'"'+o+">"+i.content+"</"+i.tag+">"},!e.document){if(!e.addEventListener)return u;return u.disableWorkerMessageHandler||e.addEventListener("message",function(t){var n=JSON.parse(t.data),r=n.language,i=n.code,l=n.immediateClose;e.postMessage(u.highlight(i,u.languages[r],r)),l&&e.close()},!1),u}var s=u.util.currentScript();function c(){u.manual||u.highlightAll()}if(s&&(u.filename=s.src,s.hasAttribute("data-manual")&&(u.manual=!0)),!u.manual){var f=document.readyState;"loading"===f||"interactive"===f&&s&&s.defer?document.addEventListener("DOMContentLoaded",c):window.requestAnimationFrame?window.requestAnimationFrame(c):window.setTimeout(c,16)}return u}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});e.exports&&(e.exports=r),void 0!==n.g&&(n.g.Prism=r)},41285:(e,t,n)=>{"use strict";var r=n(69171),u=n(38905),i=n(88697),l="data";e.exports=function(e,t){var n,p=r(t),d=t,h=i;if(p in e.normal)return e.property[e.normal[p]];return p.length>4&&p.slice(0,4)===l&&o.test(t)&&("-"===t.charAt(4)?d=l+(n=t.slice(5).replace(a,f)).charAt(0).toUpperCase()+n.slice(1):t=function(e){var t=e.slice(4);if(a.test(t))return e;return"-"!==(t=t.replace(s,c)).charAt(0)&&(t="-"+t),l+t}(t),h=u),new h(d,t)};var o=/^data[-\w.:]+$/i,a=/-[a-z]/g,s=/[A-Z]/g;function c(e){return"-"+e.toLowerCase()}function f(e){return e.charAt(1).toUpperCase()}},34807:(e,t,n)=>{"use strict";var r=n(9865),u=n(62108),i=n(19181),l=n(31942),o=n(88883),a=n(82923);e.exports=r([i,u,l,o,a])},88883:(e,t,n)=>{"use strict";var r=n(33476),u=n(86301),i=r.booleanish,l=r.number,o=r.spaceSeparated;e.exports=u({transform:function(e,t){return"role"===t?t:"aria-"+t.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:i,ariaAutoComplete:null,ariaBusy:i,ariaChecked:i,ariaColCount:l,ariaColIndex:l,ariaColSpan:l,ariaControls:o,ariaCurrent:null,ariaDescribedBy:o,ariaDetails:null,ariaDisabled:i,ariaDropEffect:o,ariaErrorMessage:null,ariaExpanded:i,ariaFlowTo:o,ariaGrabbed:i,ariaHasPopup:null,ariaHidden:i,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:o,ariaLevel:l,ariaLive:null,ariaModal:i,ariaMultiLine:i,ariaMultiSelectable:i,ariaOrientation:null,ariaOwns:o,ariaPlaceholder:null,ariaPosInSet:l,ariaPressed:i,ariaReadOnly:i,ariaRelevant:null,ariaRequired:i,ariaRoleDescription:o,ariaRowCount:l,ariaRowIndex:l,ariaRowSpan:l,ariaSelected:i,ariaSetSize:l,ariaSort:null,ariaValueMax:l,ariaValueMin:l,ariaValueNow:l,ariaValueText:null,role:null}})},82923:(e,t,n)=>{"use strict";var r=n(33476),u=n(86301),i=n(7160),l=r.boolean,o=r.overloadedBoolean,a=r.booleanish,s=r.number,c=r.spaceSeparated,f=r.commaSeparated;e.exports=u({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:i,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:f,acceptCharset:c,accessKey:c,action:null,allow:null,allowFullScreen:l,allowPaymentRequest:l,allowUserMedia:l,alt:null,as:null,async:l,autoCapitalize:null,autoComplete:c,autoFocus:l,autoPlay:l,capture:l,charSet:null,checked:l,cite:null,className:c,cols:s,colSpan:null,content:null,contentEditable:a,controls:l,controlsList:c,coords:s|f,crossOrigin:null,data:null,dateTime:null,decoding:null,default:l,defer:l,dir:null,dirName:null,disabled:l,download:o,draggable:a,encType:null,enterKeyHint:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:l,formTarget:null,headers:c,height:s,hidden:l,high:s,href:null,hrefLang:null,htmlFor:c,httpEquiv:c,id:null,imageSizes:null,imageSrcSet:f,inputMode:null,integrity:null,is:null,isMap:l,itemId:null,itemProp:c,itemRef:c,itemScope:l,itemType:c,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:l,low:s,manifest:null,max:null,maxLength:s,media:null,method:null,min:null,minLength:s,multiple:l,muted:l,name:null,nonce:null,noModule:l,noValidate:l,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforePrint:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextMenu:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:l,optimum:s,pattern:null,ping:c,placeholder:null,playsInline:l,poster:null,preload:null,readOnly:l,referrerPolicy:null,rel:c,required:l,reversed:l,rows:s,rowSpan:s,sandbox:c,scope:null,scoped:l,seamless:l,selected:l,shape:null,size:s,sizes:null,slot:null,span:s,spellCheck:a,src:null,srcDoc:null,srcLang:null,srcSet:f,start:s,step:null,style:null,tabIndex:s,target:null,title:null,translate:null,type:null,typeMustMatch:l,useMap:null,value:a,width:s,wrap:null,align:null,aLink:null,archive:c,axis:null,background:null,bgColor:null,border:s,borderColor:null,bottomMargin:s,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:l,declare:l,event:null,face:null,frame:null,frameBorder:null,hSpace:s,leftMargin:s,link:null,longDesc:null,lowSrc:null,marginHeight:s,marginWidth:s,noResize:l,noHref:l,noShade:l,noWrap:l,object:null,profile:null,prompt:null,rev:null,rightMargin:s,rules:null,scheme:null,scrolling:a,standby:null,summary:null,text:null,topMargin:s,valueType:null,version:null,vAlign:null,vLink:null,vSpace:s,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:l,disableRemotePlayback:l,prefix:null,property:null,results:s,security:null,unselectable:null}})},7160:(e,t,n)=>{"use strict";var r=n(19457);e.exports=function(e,t){return r(e,t.toLowerCase())}},19457:e=>{"use strict";e.exports=function(e,t){return t in e?e[t]:t}},86301:(e,t,n)=>{"use strict";var r=n(69171),u=n(51434),i=n(38905);e.exports=function(e){var t,n,l=e.space,o=e.mustUseProperty||[],a=e.attributes||{},s=e.properties,c=e.transform,f={},p={};for(t in s)n=new i(t,c(a,t),s[t],l),-1!==o.indexOf(t)&&(n.mustUseProperty=!0),f[t]=n,p[r(t)]=t,p[r(n.attribute)]=t;return new u(f,p,l)}},38905:(e,t,n)=>{"use strict";var r=n(88697),u=n(33476);e.exports=o,o.prototype=new r,o.prototype.defined=!0;var i=["boolean","booleanish","overloadedBoolean","number","commaSeparated","spaceSeparated","commaOrSpaceSeparated"],l=i.length;function o(e,t,n,o){var a,s,c,f=-1;for(o&&(this.space=o),r.call(this,e,t);++f<l;)a=c=i[f],(s=(n&u[c])===u[c])&&(this[a]=s)}},88697:e=>{"use strict";e.exports=n;var t=n.prototype;function n(e,t){this.property=e,this.attribute=t}t.space=null,t.attribute=null,t.property=null,t.boolean=!1,t.booleanish=!1,t.overloadedBoolean=!1,t.number=!1,t.commaSeparated=!1,t.spaceSeparated=!1,t.commaOrSpaceSeparated=!1,t.mustUseProperty=!1,t.defined=!1},9865:(e,t,n)=>{"use strict";var r=n(82153),u=n(51434);e.exports=function(e){for(var t,n,i=e.length,l=[],o=[],a=-1;++a<i;)t=e[a],l.push(t.property),o.push(t.normal),n=t.space;return new u(r.apply(null,l),r.apply(null,o),n)}},51434:e=>{"use strict";e.exports=n;var t=n.prototype;function n(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}t.space=null,t.normal={},t.property={}},33476:(e,t)=>{"use strict";var n=0;function r(){return Math.pow(2,++n)}t.boolean=r(),t.booleanish=r(),t.overloadedBoolean=r(),t.number=r(),t.spaceSeparated=r(),t.commaSeparated=r(),t.commaOrSpaceSeparated=r()},62108:(e,t,n)=>{"use strict";var r=n(86301);e.exports=r({space:"xlink",transform:function(e,t){return"xlink:"+t.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}})},19181:(e,t,n)=>{"use strict";var r=n(86301);e.exports=r({space:"xml",transform:function(e,t){return"xml:"+t.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}})},31942:(e,t,n)=>{"use strict";var r=n(86301),u=n(7160);e.exports=r({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:u,properties:{xmlns:null,xmlnsXLink:null}})},69171:e=>{"use strict";e.exports=function(e){return e.toLowerCase()}},42011:(e,t)=>{"use strict";t.q=function(e){var t=String(e||"").trim();return""===t?[]:t.split(n)};var n=/[ \t\n\r\f]+/g},83223:(e,t,n)=>{var r=n(61755);e.exports=function(e,t){var n,u,i,l=null;if(!e||"string"!=typeof e)return l;for(var o=r(e),a="function"==typeof t,s=0,c=o.length;s<c;s++)u=(n=o[s]).property,i=n.value,a?t(u,i,n):i&&(l||(l={}),l[u]=i);return l}},33187:e=>{e.exports=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}},48091:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.useClipboard=void 0;var u=r(n(10160)),i=n(65848),l=n(95757);function o(e){return e&&("TEXTAREA"===e.nodeName||"INPUT"===e.nodeName)}t.useClipboard=function(e){void 0===e&&(e={});var t=l.useTimedToggle(!1),n=t[0],r=t[1],a=i.useRef(null),s=i.useRef(e);return s.current=e,{copied:n,copy:i.useCallback(function(e){var t=s.current,n=a.current;function i(){t.onSuccess&&t.onSuccess(),t.copiedTimeout&&r(t.copiedTimeout),t.selectOnCopy&&o(n)&&n.select()}function l(){t.onError&&t.onError(),!1!==t.selectOnError&&o(n)&&n.select()}function c(e){u.default(e).then(i).catch(l)}"string"==typeof e?c(e):n&&c(n.value)},[]),isSupported:function(){return!!navigator.clipboard||"function"==typeof document.execCommand&&"function"==typeof document.queryCommandSupported&&document.queryCommandSupported("copy")},target:a}}},95757:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useTimedToggle=void 0;var r=n(65848);t.useTimedToggle=function(e){var t=r.useState(!1),n=t[0],u=t[1],i=r.useRef(),l=r.useRef(e);return r.useEffect(function(){return function(){return clearTimeout(i.current)}},[]),[n,function(e){clearTimeout(i.current),u(!l.current),i.current=window.setTimeout(function(){return u(l.current)},e)}]}},27714:(e,t,n)=>{"use strict";n.d(t,{YQ:()=>u});var r=n(65848);function u(e,t,n){var u=this,i=(0,r.useRef)(null),l=(0,r.useRef)(0),o=(0,r.useRef)(null),a=(0,r.useRef)([]),s=(0,r.useRef)(),c=(0,r.useRef)(),f=(0,r.useRef)(e),p=(0,r.useRef)(!0);f.current=e;var d="undefined"!=typeof window,h=!t&&0!==t&&d;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var g=!!(n=n||{}).leading,m=!("trailing"in n)||!!n.trailing,F="maxWait"in n,A="debounceOnServer"in n&&!!n.debounceOnServer,y=F?Math.max(+n.maxWait||0,t):null;return(0,r.useEffect)(function(){return p.current=!0,function(){p.current=!1}},[]),(0,r.useMemo)(function(){var e=function(e){var t=a.current,n=s.current;return a.current=s.current=null,l.current=e,c.current=f.current.apply(n,t)},n=function(e,t){h&&cancelAnimationFrame(o.current),o.current=h?requestAnimationFrame(e):setTimeout(e,t)},r=function(e){if(!p.current)return!1;var n=e-i.current;return!i.current||n>=t||n<0||F&&e-l.current>=y},x=function(t){return o.current=null,m&&a.current?e(t):(a.current=s.current=null,c.current)},b=function e(){var u=Date.now();if(r(u))return x(u);if(p.current){var o=t-(u-i.current);n(e,F?Math.min(o,y-(u-l.current)):o)}},v=function(){if(d||A){var f=Date.now(),h=r(f);if(a.current=[].slice.call(arguments),s.current=u,i.current=f,h){if(!o.current&&p.current)return l.current=i.current,n(b,t),g?e(i.current):c.current;if(F)return n(b,t),e(i.current)}return o.current||n(b,t),c.current}};return v.cancel=function(){o.current&&(h?cancelAnimationFrame(o.current):clearTimeout(o.current)),l.current=0,a.current=i.current=s.current=o.current=null},v.isPending=function(){return!!o.current},v.flush=function(){return o.current?x(Date.now()):c.current},v},[g,F,t,y,m,h,d,A])}},82153:e=>{e.exports=function(){for(var e={},n=0;n<arguments.length;n++){var r=arguments[n];for(var u in r)t.call(r,u)&&(e[u]=r[u])}return e};var t=Object.prototype.hasOwnProperty},97926:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},89141:(e,t,n)=>{"use strict";n.d(t,{s:()=>u});let r=document.createElement("i");function u(e){let t="&"+e+";";r.innerHTML=t;let n=r.textContent;if(59===n.charCodeAt(n.length-1)&&"semi"!==e)return!1;return n!==t&&n}},88932:(e,t,n)=>{"use strict";function r(e){return -2===e||-1===e||32===e}function u(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function i(e,t,n,u){let i=u?u-1:Number.POSITIVE_INFINITY,l=0;return function(u){if(r(u))return e.enter(n),function u(o){if(r(o)&&l++<i)return e.consume(o),u;return e.exit(n),t(o)}(u);return t(u)}}n.d(t,{N:()=>i}),u(/[A-Za-z]/),u(/[\dA-Za-z]/),u(/[#-'*+\--9=?A-Z^-~]/),u(/\d/),u(/[\dA-Fa-f]/),u(/[!-/:-@[-`{-~]/),u(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),u(/\s/)},78002:(e,t,n)=>{"use strict";function r(e,t,n,r){let u;let i=e.length,l=0;if(t=t<0?-t>i?0:i+t:t>i?i:t,n=n>0?n:0,r.length<1e4)(u=Array.from(r)).unshift(t,n),[].splice.apply(e,u);else for(n&&[].splice.apply(e,[t,n]);l<r.length;)(u=r.slice(l,l+1e4)).unshift(t,0),[].splice.apply(e,u),l+=1e4,t+=1e4}function u(e,t){if(e.length>0)return r(e,e.length,0,t),e;return t}n.d(t,{V:()=>u,m:()=>r})},30626:(e,t,n)=>{"use strict";n.d(t,{S:()=>l}),i(/[A-Za-z]/),i(/[\dA-Za-z]/),i(/[#-'*+\--9=?A-Z^-~]/),i(/\d/),i(/[\dA-Fa-f]/),i(/[!-/:-@[-`{-~]/);let r=i(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),u=i(/\s/);function i(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function l(e){if(null===e||null!==e&&(e<0||32===e)||u(e))return 1;if(r(e))return 2}},80248:(e,t,n)=>{"use strict";function r(e,t){let n=Number.parseInt(e,t);if(n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111)return"�";return String.fromCharCode(n)}n.d(t,{C:()=>r})},22946:(e,t,n)=>{"use strict";n.d(t,{s:()=>l});var r=n(89141),u=n(80248);let i=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function l(e){return e.replace(i,o)}function o(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return(0,u.C)(n.slice(t?2:1),t?16:10)}return(0,r.s)(n)||e}},69603:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},25268:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],u=-1;for(;++u<e.length;){let i=e[u].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}n.d(t,{W:()=>r})},25650:(e,t,n)=>{"use strict";n.d(t,{$:()=>nv});var r={};n.r(r),n.d(r,{attentionMarkers:()=>tu,contentInitial:()=>e9,disable:()=>ti,document:()=>e4,flow:()=>te,flowInitial:()=>e8,insideSpan:()=>tr,string:()=>tt,text:()=>tn});var u={};n.r(u),n.d(u,{boolean:()=>tZ,booleanish:()=>tW,commaOrSpaceSeparated:()=>tG,commaSeparated:()=>tX,number:()=>tY,overloadedBoolean:()=>tQ,spaceSeparated:()=>tK});var i=n(65848),l=n(84708);function o(e){if(!e||"object"!=typeof e)return"";if("position"in e||"type"in e)return s(e.position);if("start"in e||"end"in e)return s(e);if("line"in e||"column"in e)return a(e);return""}function a(e){return c(e&&e.line)+":"+c(e&&e.column)}function s(e){return a(e&&e.start)+"-"+a(e&&e.end)}function c(e){return e&&"number"==typeof e?e:1}class f extends Error{constructor(e,t,n){let r=[null,null],u={start:{line:null,column:null},end:{line:null,column:null}};if(super(),"string"==typeof t&&(n=t,t=void 0),"string"==typeof n){let e=n.indexOf(":");-1===e?r[1]=n:(r[0]=n.slice(0,e),r[1]=n.slice(e+1))}t&&("type"in t||"position"in t?t.position&&(u=t.position):"start"in t||"end"in t?u=t:("line"in t||"column"in t)&&(u.start=t)),this.name=o(t)||"1:1",this.message="object"==typeof e?e.message:e,this.stack="","object"==typeof e&&e.stack&&(this.stack=e.stack),this.reason=this.message,this.fatal,this.line=u.start.line,this.column=u.start.column,this.position=u,this.source=r[0],this.ruleId=r[1],this.file,this.actual,this.expected,this.url,this.note}}f.prototype.file="",f.prototype.name="",f.prototype.reason="",f.prototype.message="",f.prototype.stack="",f.prototype.fatal=null,f.prototype.column=null,f.prototype.line=null,f.prototype.source=null,f.prototype.ruleId=null,f.prototype.position=null;let p={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');d(e);let r=0,u=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.charCodeAt(i)){if(n){r=i+1;break}}else u<0&&(n=!0,u=i+1);return u<0?"":e.slice(r,u)}if(t===e)return"";let l=-1,o=t.length-1;for(;i--;)if(47===e.charCodeAt(i)){if(n){r=i+1;break}}else l<0&&(n=!0,l=i+1),o>-1&&(e.charCodeAt(i)===t.charCodeAt(o--)?o<0&&(u=i):(o=-1,u=l));return r===u?u=l:u<0&&(u=e.length),e.slice(r,u)},dirname:function(e){let t;if(d(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.charCodeAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.charCodeAt(0)?"/":".":1===n&&47===e.charCodeAt(0)?"//":e.slice(0,n)},extname:function(e){let t;d(e);let n=e.length,r=-1,u=0,i=-1,l=0;for(;n--;){let o=e.charCodeAt(n);if(47===o){if(t){u=n+1;break}continue}r<0&&(t=!0,r=n+1),46===o?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1)}if(i<0||r<0||0===l||1===l&&i===r-1&&i===u+1)return"";return e.slice(i,r)},join:function(){let e;for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let u=-1;for(;++u<n.length;)d(n[u]),n[u]&&(e=void 0===e?n[u]:e+"/"+n[u]);return void 0===e?".":function(e){d(e);let t=47===e.charCodeAt(0),n=function(e,t){let n,r,u="",i=0,l=-1,o=0,a=-1;for(;++a<=e.length;){if(a<e.length)n=e.charCodeAt(a);else if(47===n)break;else n=47;if(47===n){if(l===a-1||1===o);else if(l!==a-1&&2===o){if(u.length<2||2!==i||46!==u.charCodeAt(u.length-1)||46!==u.charCodeAt(u.length-2)){if(u.length>2){if((r=u.lastIndexOf("/"))!==u.length-1){r<0?(u="",i=0):i=(u=u.slice(0,r)).length-1-u.lastIndexOf("/"),l=a,o=0;continue}}else if(u.length>0){u="",i=0,l=a,o=0;continue}}t&&(u=u.length>0?u+"/..":"..",i=2)}else u.length>0?u+="/"+e.slice(l+1,a):u=e.slice(l+1,a),i=a-l-1;l=a,o=0}else 46===n&&o>-1?o++:o=-1}return u}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.charCodeAt(e.length-1)&&(n+="/"),t?"/"+n:n}(e)},sep:"/"};function d(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let h={cwd:function(){return"/"}};function g(e){return null!==e&&"object"==typeof e&&e.href&&e.origin}let m=["history","path","basename","stem","extname","dirname"];class F{get path(){return this.history[this.history.length-1]}set path(e){g(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!g(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.charCodeAt(n)&&50===t.charCodeAt(n+1)){let e=t.charCodeAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),y(e,"path"),this.path!==e&&this.history.push(e)}get dirname(){return"string"==typeof this.path?p.dirname(this.path):void 0}set dirname(e){x(this.basename,"dirname"),this.path=p.join(e||"",this.basename)}get basename(){return"string"==typeof this.path?p.basename(this.path):void 0}set basename(e){y(e,"basename"),A(e,"basename"),this.path=p.join(this.dirname||"",e)}get extname(){return"string"==typeof this.path?p.extname(this.path):void 0}set extname(e){if(A(e,"extname"),x(this.dirname,"extname"),e){if(46!==e.charCodeAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=p.join(this.dirname,this.stem+(e||""))}get stem(){return"string"==typeof this.path?p.basename(this.path,this.extname):void 0}set stem(e){y(e,"stem"),A(e,"stem"),this.path=p.join(this.dirname||"",e+(this.extname||""))}toString(e){return(this.value||"").toString(e)}message(e,t,n){let r=new f(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=null,r}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}constructor(e){let t,n;t=e?"string"==typeof e||l(e)?{value:e}:g(e)?{path:e}:e:{},this.data={},this.messages=[],this.history=[],this.cwd=h.cwd(),this.value,this.stored,this.result,this.map;let r=-1;for(;++r<m.length;){let e=m[r];e in t&&void 0!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)m.includes(n)||(this[n]=t[n])}}function A(e,t){if(e&&e.includes(p.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+p.sep+"`")}function y(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function x(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}function b(e){if(e)throw e}var v=n(33187),k=n(14112);function E(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function C(e){return w(e&&e.line)+":"+w(e&&e.column)}function D(e){return C(e&&e.start)+"-"+C(e&&e.end)}function w(e){return e&&"number"==typeof e?e:1}class S extends Error{constructor(e,t,n){let r=[null,null],u={start:{line:null,column:null},end:{line:null,column:null}};if(super(),"string"==typeof t&&(n=t,t=void 0),"string"==typeof n){let e=n.indexOf(":");-1===e?r[1]=n:(r[0]=n.slice(0,e),r[1]=n.slice(e+1))}t&&("type"in t||"position"in t?t.position&&(u=t.position):"start"in t||"end"in t?u=t:("line"in t||"column"in t)&&(u.start=t)),this.name=function(e){if(!e||"object"!=typeof e)return"";if("position"in e||"type"in e)return D(e.position);if("start"in e||"end"in e)return D(e);if("line"in e||"column"in e)return C(e);return""}(t)||"1:1",this.message="object"==typeof e?e.message:e,this.stack="","object"==typeof e&&e.stack&&(this.stack=e.stack),this.reason=this.message,this.fatal,this.line=u.start.line,this.column=u.start.column,this.position=u,this.source=r[0],this.ruleId=r[1],this.file,this.actual,this.expected,this.url,this.note}}S.prototype.file="",S.prototype.name="",S.prototype.reason="",S.prototype.message="",S.prototype.stack="",S.prototype.fatal=null,S.prototype.column=null,S.prototype.line=null,S.prototype.source=null,S.prototype.ruleId=null,S.prototype.position=null;let B={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');L(e);let r=0,u=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.charCodeAt(i)){if(n){r=i+1;break}}else u<0&&(n=!0,u=i+1);return u<0?"":e.slice(r,u)}if(t===e)return"";let l=-1,o=t.length-1;for(;i--;)if(47===e.charCodeAt(i)){if(n){r=i+1;break}}else l<0&&(n=!0,l=i+1),o>-1&&(e.charCodeAt(i)===t.charCodeAt(o--)?o<0&&(u=i):(o=-1,u=l));return r===u?u=l:u<0&&(u=e.length),e.slice(r,u)},dirname:function(e){let t;if(L(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.charCodeAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.charCodeAt(0)?"/":".":1===n&&47===e.charCodeAt(0)?"//":e.slice(0,n)},extname:function(e){let t;L(e);let n=e.length,r=-1,u=0,i=-1,l=0;for(;n--;){let o=e.charCodeAt(n);if(47===o){if(t){u=n+1;break}continue}r<0&&(t=!0,r=n+1),46===o?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1)}if(i<0||r<0||0===l||1===l&&i===r-1&&i===u+1)return"";return e.slice(i,r)},join:function(){let e;for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let u=-1;for(;++u<n.length;)L(n[u]),n[u]&&(e=void 0===e?n[u]:e+"/"+n[u]);return void 0===e?".":function(e){L(e);let t=47===e.charCodeAt(0),n=function(e,t){let n,r,u="",i=0,l=-1,o=0,a=-1;for(;++a<=e.length;){if(a<e.length)n=e.charCodeAt(a);else if(47===n)break;else n=47;if(47===n){if(l===a-1||1===o);else if(l!==a-1&&2===o){if(u.length<2||2!==i||46!==u.charCodeAt(u.length-1)||46!==u.charCodeAt(u.length-2)){if(u.length>2){if((r=u.lastIndexOf("/"))!==u.length-1){r<0?(u="",i=0):i=(u=u.slice(0,r)).length-1-u.lastIndexOf("/"),l=a,o=0;continue}}else if(u.length>0){u="",i=0,l=a,o=0;continue}}t&&(u=u.length>0?u+"/..":"..",i=2)}else u.length>0?u+="/"+e.slice(l+1,a):u=e.slice(l+1,a),i=a-l-1;l=a,o=0}else 46===n&&o>-1?o++:o=-1}return u}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.charCodeAt(e.length-1)&&(n+="/"),t?"/"+n:n}(e)},sep:"/"};function L(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let T={cwd:function(){return"/"}};function P(e){return null!==e&&"object"==typeof e&&e.href&&e.origin}let O=["history","path","basename","stem","extname","dirname"];class I{get path(){return this.history[this.history.length-1]}set path(e){P(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!P(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.charCodeAt(n)&&50===t.charCodeAt(n+1)){let e=t.charCodeAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),z(e,"path"),this.path!==e&&this.history.push(e)}get dirname(){return"string"==typeof this.path?B.dirname(this.path):void 0}set dirname(e){j(this.basename,"dirname"),this.path=B.join(e||"",this.basename)}get basename(){return"string"==typeof this.path?B.basename(this.path):void 0}set basename(e){z(e,"basename"),N(e,"basename"),this.path=B.join(this.dirname||"",e)}get extname(){return"string"==typeof this.path?B.extname(this.path):void 0}set extname(e){if(N(e,"extname"),j(this.dirname,"extname"),e){if(46!==e.charCodeAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=B.join(this.dirname,this.stem+(e||""))}get stem(){return"string"==typeof this.path?B.basename(this.path,this.extname):void 0}set stem(e){z(e,"stem"),N(e,"stem"),this.path=B.join(this.dirname||"",e+(this.extname||""))}toString(e){return(this.value||"").toString(e)}message(e,t,n){let r=new S(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=null,r}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}constructor(e){let t,n;t=e?"string"==typeof e||v(e)?{value:e}:P(e)?{path:e}:e:{},this.data={},this.messages=[],this.history=[],this.cwd=T.cwd(),this.value,this.stored,this.result,this.map;let r=-1;for(;++r<O.length;){let e=O[r];e in t&&void 0!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)O.includes(n)||(this[n]=t[n])}}function N(e,t){if(e&&e.includes(B.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+B.sep+"`")}function z(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function j(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let M=(function e(){let t;let n=function(){let e=[],t={run:function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let u=-1,i=n.pop();if("function"!=typeof i)throw TypeError("Expected function as last argument, not "+i);!function t(r){for(var l=arguments.length,o=Array(l>1?l-1:0),a=1;a<l;a++)o[a-1]=arguments[a];let s=e[++u],c=-1;if(r){i(r);return}for(;++c<n.length;)(null===o[c]||void 0===o[c])&&(o[c]=n[c]);n=o,s?(function(e,t){let n;return function(){let t;for(var i=arguments.length,l=Array(i),o=0;o<i;o++)l[o]=arguments[o];let a=e.length>l.length;a&&l.push(r);try{t=e.apply(this,l)}catch(e){if(a&&n)throw e;return r(e)}a||(t instanceof Promise?t.then(u,r):t instanceof Error?r(t):u(t))};function r(e){for(var r=arguments.length,u=Array(r>1?r-1:0),i=1;i<r;i++)u[i-1]=arguments[i];n||(n=!0,t(e,...u))}function u(e){r(null,e)}})(s,t)(...o):i(null,...o)}(null,...n)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}(),r=[],u={},i=-1;return l.data=function(e,n){if("string"==typeof e){if(2==arguments.length)return H("data",t),u[e]=n,l;return R.call(u,e)&&u[e]||null}if(e)return H("data",t),u=e,l;return u},l.Parser=void 0,l.Compiler=void 0,l.freeze=function(){if(t)return l;for(;++i<r.length;){let[e,...t]=r[i];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let u=e.call(l,...t);"function"==typeof u&&n.use(u)}return t=!0,i=Number.POSITIVE_INFINITY,l},l.attachers=r,l.use=function(e){let n;for(var i=arguments.length,o=Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];if(H("use",t),null==e);else if("function"==typeof e)f(e,...o);else if("object"==typeof e)Array.isArray(e)?c(e):s(e);else throw TypeError("Expected usable value, not `"+e+"`");return n&&(u.settings=Object.assign(u.settings||{},n)),l;function s(e){c(e.plugins),e.settings&&(n=Object.assign(n||{},e.settings))}function c(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;)!function(e){if("function"==typeof e)f(e);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;f(t,...n)}else s(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(e[t]);else throw TypeError("Expected a list of plugins, not `"+e+"`")}function f(e,t){let n,u=-1;for(;++u<r.length;)if(r[u][0]===e){n=r[u];break}n?(E(n[1])&&E(t)&&(t=k(!0,n[1],t)),n[1]=t):r.push([...arguments])}},l.parse=function(e){l.freeze();let t=Z(e),n=l.Parser;if(U("parse",n),_(n,"parse"))return new n(String(t),t).parse();return n(String(t),t)},l.stringify=function(e,t){l.freeze();let n=Z(t),r=l.Compiler;if($("stringify",r),V(e),_(r,"compile"))return new r(e,n).compile();return r(e,n)},l.run=function(e,t,r){if(V(e),l.freeze(),r||"function"!=typeof t||(r=t,t=void 0),!r)return new Promise(u);function u(u,i){n.run(e,Z(t),function(t,n,l){n=n||e,t?i(t):u?u(n):r(null,n,l)})}u(null,r)},l.runSync=function(e,t){let n,r;return l.run(e,t,function(e,t){b(e),n=t,r=!0}),q("runSync","run",r),n},l.process=function(e,t){if(l.freeze(),U("process",l.Parser),$("process",l.Compiler),!t)return new Promise(n);function n(n,r){let u=Z(e);function i(e,u){e||!u?r(e):n?n(u):t(null,u)}l.run(l.parse(u),u,(e,t,n)=>{if(!e&&t&&n){let r=l.stringify(t,n);null==r||("string"==typeof r||v(r)?n.value=r:n.result=r),i(e,n)}else i(e)})}n(null,t)},l.processSync=function(e){let t;l.freeze(),U("processSync",l.Parser),$("processSync",l.Compiler);let n=Z(e);return l.process(n,function(e){t=!0,b(e)}),q("processSync","process",t),n},l;function l(){let t=e(),n=-1;for(;++n<r.length;)t.use(...r[n]);return t.data(k(!0,{},u)),t}})().freeze(),R={}.hasOwnProperty;function _(e,t){return"function"==typeof e&&e.prototype&&(function(e){let t;for(t in e)if(R.call(e,t))return!0;return!1}(e.prototype)||t in e.prototype)}function U(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `Parser`")}function $(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `Compiler`")}function H(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function V(e){if(!E(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function q(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function Z(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new I(e)}function W(e,t){return e&&"object"==typeof e&&(e.value||(t?e.alt:"")||"children"in e&&Q(e.children,t)||Array.isArray(e)&&Q(e,t))||""}function Q(e,t){for(var n=[],r=-1;++r<e.length;)n[r]=W(e[r],t);return n.join("")}var Y=n(78002);let K={}.hasOwnProperty;var X=n(88932);let G=eo(/[A-Za-z]/),J=eo(/[\dA-Za-z]/),ee=eo(/[#-'*+\--9=?A-Z^-~]/),et=eo(/\d/),en=eo(/[\dA-Fa-f]/),er=eo(/[!-/:-@[-`{-~]/);function eu(e){return null!==e&&e<-2}function ei(e){return null!==e&&(e<0||32===e)}function el(e){return -2===e||-1===e||32===e}function eo(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}eo(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),eo(/\s/);let ea={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,X.N)(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function n(r){let u=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=u),t=u,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}if(eu(r))return e.consume(r),e.exit("chunkText"),n;return e.consume(r),t}(r)}(n)});return n}},es={tokenize:function(e){let t,n,r;let u=this,i=[],l=0;return o;function o(t){if(l<i.length){let n=i[l];return u.containerState=n[1],e.attempt(n[0].continuation,a,s)(t)}return s(t)}function a(e){if(l++,u.containerState._closeFlow){let n;u.containerState._closeFlow=void 0,t&&F();let r=u.events.length,i=r;for(;i--;)if("exit"===u.events[i][0]&&"chunkFlow"===u.events[i][1].type){n=u.events[i][1].end;break}m(l);let o=r;for(;o<u.events.length;)u.events[o][1].end=Object.assign({},n),o++;return(0,Y.m)(u.events,i+1,0,u.events.slice(r)),u.events.length=o,s(e)}return o(e)}function s(n){if(l===i.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return h(n);u.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return u.containerState={},e.check(ec,c,f)(n)}function c(e){return t&&F(),m(l),p(e)}function f(e){return u.parser.lazy[u.now().line]=l!==i.length,r=u.now().offset,h(e)}function p(t){return u.containerState={},e.attempt(ec,d,h)(t)}function d(e){return l++,i.push([u.currentConstruct,u.containerState]),p(e)}function h(r){if(null===r){t&&F(),m(0),e.consume(r);return}return t=t||u.parser.flow(u.now()),e.enter("chunkFlow",{contentType:"flow",previous:n,_tokenizer:t}),function t(n){if(null===n){g(e.exit("chunkFlow"),!0),m(0),e.consume(n);return}if(eu(n))return e.consume(n),g(e.exit("chunkFlow")),l=0,u.interrupt=void 0,o;return e.consume(n),t}(r)}function g(e,i){let o=u.sliceStream(e);if(i&&o.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(o),u.parser.lazy[e.start.line]){let e,n,i=t.events.length;for(;i--;)if(t.events[i][1].start.offset<r&&(!t.events[i][1].end||t.events[i][1].end.offset>r))return;let o=u.events.length,a=o;for(;a--;)if("exit"===u.events[a][0]&&"chunkFlow"===u.events[a][1].type){if(e){n=u.events[a][1].end;break}e=!0}for(m(l),i=o;i<u.events.length;)u.events[i][1].end=Object.assign({},n),i++;(0,Y.m)(u.events,a+1,0,u.events.slice(o)),u.events.length=i}}function m(t){let n=i.length;for(;n-- >t;){let t=i[n];u.containerState=t[1],t[0].exit.call(u,e)}i.length=t}function F(){t.write([null]),n=void 0,t=void 0,u.containerState._closeFlow=void 0}}},ec={tokenize:function(e,t,n){return(0,X.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},ef={tokenize:function(e,t,n){return(0,X.N)(e,function(e){return null===e||eu(e)?t(e):n(e)},"linePrefix")},partial:!0};function ep(e){let t,n,r,u,i,l,o;let a={},s=-1;for(;++s<e.length;){for(;s in a;)s=a[s];if(t=e[s],s&&"chunkFlow"===t[1].type&&"listItemPrefix"===e[s-1][1].type&&((r=0)<(l=t[1]._tokenizer.events).length&&"lineEndingBlank"===l[r][1].type&&(r+=2),r<l.length&&"content"===l[r][1].type))for(;++r<l.length&&"content"!==l[r][1].type;)"chunkText"===l[r][1].type&&(l[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(a,function(e,t){let n,r;let u=e[t][1],i=e[t][2],l=t-1,o=[],a=u._tokenizer||i.parser[u.contentType](u.start),s=a.events,c=[],f={},p=-1,d=u,h=0,g=0,m=[0];for(;d;){for(;e[++l][1]!==d;);o.push(l),!d._tokenizer&&(n=i.sliceStream(d),d.next||n.push(null),r&&a.defineSkip(d.start),d._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(n),d._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),r=d,d=d.next}for(d=u;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(g=p+1,m.push(g),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(a.events=[],d?(d._tokenizer=void 0,d.previous=void 0):m.pop(),p=m.length;p--;){let t=s.slice(m[p],m[p+1]),n=o.pop();c.unshift([n,n+t.length-1]),(0,Y.m)(e,n,2,t)}for(p=-1;++p<c.length;)f[h+c[p][0]]=h+c[p][1],h+=c[p][1]-c[p][0]-1;return f}(e,s)),s=a[s],o=!0);else if(t[1]._container){for(r=s,n=void 0;r--;)if("lineEnding"===(u=e[r])[1].type||"lineEndingBlank"===u[1].type)"enter"===u[0]&&(n&&(e[n][1].type="lineEndingBlank"),u[1].type="lineEnding",n=r);else break;n&&(t[1].end=Object.assign({},e[n][1].start),(i=e.slice(n,s)).unshift(t),(0,Y.m)(e,n,s-n+1,i))}}return!o}let ed={tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){if(null===t)return u(t);if(eu(t))return e.check(eh,i,u)(t);return e.consume(t),r}function u(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}},resolve:function(e){return ep(e),e}},eh={tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,X.N)(e,u,"linePrefix")};function u(u){if(null===u||eu(u))return n(u);let i=r.events[r.events.length-1];if(!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4)return t(u);return e.interrupt(r.parser.constructs.flow,n,t)(u)}},partial:!0},eg={tokenize:function(e){let t=this,n=e.attempt(ef,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,(0,X.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(ed,r)),"linePrefix")));return n;function r(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},em={resolveAll:ex()},eF=ey("string"),eA=ey("text");function ey(e){return{tokenize:function(t){let n=this,r=this.parser.constructs[e],u=t.attempt(r,i,l);return i;function i(e){return a(e)?u(e):l(e)}function l(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),o}function o(e){if(a(e))return t.exit("data"),u(e);return t.consume(e),o}function a(e){if(null===e)return!0;let t=r[e],u=-1;if(t)for(;++u<t.length;){let e=t[u];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}},resolveAll:ex("text"===e?eb:void 0)}}function ex(e){return function(t,n){let r,u=-1;for(;++u<=t.length;)void 0===r?t[u]&&"data"===t[u][1].type&&(r=u,u++):t[u]&&"data"===t[u][1].type||(u!==r+2&&(t[r][1].end=t[u-1][1].end,t.splice(r+2,u-r-2),u=r+2),r=void 0);return e?e(t,n):t}}function eb(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let u=e[n-1][1],i=t.sliceStream(u),l=i.length,o=-1,a=0;for(;l--;){let e=i[l];if("string"==typeof e){for(o=e.length;32===e.charCodeAt(o-1);)a++,o--;if(o)break;o=-1}else if(-2===e)r=!0,a++;else if(-1===e);else{l++;break}}if(a){let i={type:n===e.length||r||a<2?"lineSuffix":"hardBreakTrailing",start:{line:u.end.line,column:u.end.column-a,offset:u.end.offset-a,_index:u.start._index+l,_bufferIndex:l?o:u.start._bufferIndex+o},end:Object.assign({},u.end)};u.end=Object.assign({},i.start),u.start.offset===u.end.offset?Object.assign(u,i):(e.splice(n,0,["enter",i,t],["exit",i,t]),n+=2)}n++}return e}var ev=n(25268);let ek={name:"thematicBreak",tokenize:function(e,t,n){let r,u=0;return function(i){return e.enter("thematicBreak"),r=i,function i(l){if(l===r)return e.enter("thematicBreakSequence"),function t(n){if(n===r)return e.consume(n),u++,t;return e.exit("thematicBreakSequence"),i(n)}(l);if(el(l))return(0,X.N)(e,i,"whitespace")(l);if(u<3||null!==l&&!eu(l))return n(l);return e.exit("thematicBreak"),t(l)}(i)}}},eE={name:"list",tokenize:function(e,t,n){let r=this,u=r.events[r.events.length-1],i=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,l=0;return function(t){let u=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===u?!r.containerState.marker||t===r.containerState.marker:et(t)){if(r.containerState.type||(r.containerState.type=u,e.enter(u,{_container:!0})),"listUnordered"===u)return e.enter("listItemPrefix"),42===t||45===t?e.check(ek,n,o)(t):o(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(u){if(et(u)&&++l<10)return e.consume(u),t;if((!r.interrupt||l<2)&&(r.containerState.marker?u===r.containerState.marker:41===u||46===u))return e.exit("listItemValue"),o(u);return n(u)}(t)}return n(t)};function o(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(ef,r.interrupt?n:a,e.attempt(eC,c,s))}function a(e){return r.containerState.initialBlankLine=!0,i++,c(e)}function s(t){if(el(t))return e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c;return n(t)}function c(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}},continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(ef,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,X.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){if(r.containerState.furtherBlankLines||!el(n))return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,u(n);return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eD,t,u)(n)});function u(u){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,X.N)(e,e.attempt(eE,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}},exit:function(e){e.exit(this.containerState.type)}},eC={tokenize:function(e,t,n){let r=this;return(0,X.N)(e,function(e){let u=r.events[r.events.length-1];return!el(e)&&u&&"listItemPrefixWhitespace"===u[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)},partial:!0},eD={tokenize:function(e,t,n){let r=this;return(0,X.N)(e,function(e){let u=r.events[r.events.length-1];return u&&"listItemIndent"===u[1].type&&u[2].sliceSerialize(u[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)},partial:!0},ew={name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),u}return n(t)};function u(n){if(el(n))return e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t;return e.exit("blockQuotePrefix"),t(n)}},continuation:{tokenize:function(e,t,n){return(0,X.N)(e,e.attempt(ew,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},exit:function(e){e.exit("blockQuote")}};function eS(e){return null!==e&&(e<32||127===e)}function eB(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function eL(e,t,n,r,u,i,l,o,a){let s=a||Number.POSITIVE_INFINITY,c=0;return function(t){if(60===t)return e.enter(r),e.enter(u),e.enter(i),e.consume(t),e.exit(i),f;if(null===t||41===t||eS(t))return n(t);return e.enter(r),e.enter(l),e.enter(o),e.enter("chunkString",{contentType:"string"}),h(t)};function f(n){if(62===n)return e.enter(i),e.consume(n),e.exit(i),e.exit(u),e.exit(r),t;return e.enter(o),e.enter("chunkString",{contentType:"string"}),p(n)}function p(t){if(62===t)return e.exit("chunkString"),e.exit(o),f(t);if(null===t||60===t||null!==t&&t<-2)return n(t);return e.consume(t),92===t?d:p}function d(t){if(60===t||62===t||92===t)return e.consume(t),p;return p(t)}function h(u){if(40===u){if(++c>s)return n(u);return e.consume(u),h}if(41===u){if(!c--)return e.exit("chunkString"),e.exit(o),e.exit(l),e.exit(r),t(u);return e.consume(u),h}if(null===u||null!==u&&(u<0||32===u)){if(c)return n(u);return e.exit("chunkString"),e.exit(o),e.exit(l),e.exit(r),t(u)}if(eS(u))return n(u);return e.consume(u),92===u?g:h}function g(t){if(40===t||41===t||92===t)return e.consume(t),h;return h(t)}}function eT(e){return null!==e&&e<-2}function eP(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function eO(e,t,n,r,u,i){let l;let o=this,a=0;return function(t){return e.enter(r),e.enter(u),e.consume(t),e.exit(u),e.enter(i),s};function s(f){if(null===f||91===f||93===f&&!l||94===f&&!a&&"_hiddenFootnoteSupport"in o.parser.constructs||a>999)return n(f);if(93===f)return e.exit(i),e.enter(u),e.consume(f),e.exit(u),e.exit(r),t;if(eT(f))return e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),s;return e.enter("chunkString",{contentType:"string"}),c(f)}function c(t){if(null===t||91===t||93===t||eT(t)||a++>999)return e.exit("chunkString"),s(t);return e.consume(t),l=l||!(-2===t||-1===t||32===t),92===t?f:c}function f(t){if(91===t||92===t||93===t)return e.consume(t),a++,c;return c(t)}}function eI(e){return null!==e&&e<-2}function eN(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function ez(e,t,n,r,u,i){let l;return function(t){return e.enter(r),e.enter(u),e.consume(t),e.exit(u),l=40===t?41:t,o};function o(n){if(n===l)return e.enter(u),e.consume(n),e.exit(u),e.exit(r),t;return e.enter(i),a(n)}function a(t){if(t===l)return e.exit(i),o(l);if(null===t)return n(t);if(eI(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,X.N)(e,a,"linePrefix");return e.enter("chunkString",{contentType:"string"}),s(t)}function s(t){if(t===l||null===t||eI(t))return e.exit("chunkString"),a(t);return e.consume(t),92===t?c:s}function c(t){if(t===l||92===t)return e.consume(t),s;return s(t)}}function ej(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function eM(e,t){let n;return function r(u){if(null!==u&&u<-2)return e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),n=!0,r;if(-2===u||-1===u||32===u)return(0,X.N)(e,r,n?"linePrefix":"lineSuffix")(u);return t(u)}}eB(/[A-Za-z]/),eB(/[\dA-Za-z]/),eB(/[#-'*+\--9=?A-Z^-~]/),eB(/\d/),eB(/[\dA-Fa-f]/),eB(/[!-/:-@[-`{-~]/),eB(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),eB(/\s/),eP(/[A-Za-z]/),eP(/[\dA-Za-z]/),eP(/[#-'*+\--9=?A-Z^-~]/),eP(/\d/),eP(/[\dA-Fa-f]/),eP(/[!-/:-@[-`{-~]/),eP(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),eP(/\s/),eN(/[A-Za-z]/),eN(/[\dA-Za-z]/),eN(/[#-'*+\--9=?A-Z^-~]/),eN(/\d/),eN(/[\dA-Fa-f]/),eN(/[!-/:-@[-`{-~]/),eN(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),eN(/\s/),ej(/[A-Za-z]/),ej(/[\dA-Za-z]/),ej(/[#-'*+\--9=?A-Z^-~]/),ej(/\d/),ej(/[\dA-Fa-f]/),ej(/[!-/:-@[-`{-~]/),ej(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),ej(/\s/);var eR=n(69603);let e_={tokenize:function(e,t,n){return function(t){return ei(t)?eM(e,r)(t):n(t)};function r(t){if(34===t||39===t||40===t)return ez(e,(0,X.N)(e,u,"whitespace"),n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t);return n(t)}function u(e){return null===e||eu(e)?t(e):n(e)}},partial:!0},eU={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,X.N)(e,u,"linePrefix",5)(t)};function u(t){let u=r.events[r.events.length-1];return u&&"linePrefix"===u[1].type&&u[2].sliceSerialize(u[1],!0).length>=4?function t(n){if(null===n)return i(n);if(eu(n))return e.attempt(e$,t,i)(n);return e.enter("codeFlowValue"),function n(r){if(null===r||eu(r))return e.exit("codeFlowValue"),t(r);return e.consume(r),n}(n)}(t):n(t)}function i(n){return e.exit("codeIndented"),t(n)}}},e$={tokenize:function(e,t,n){let r=this;return u;function u(t){if(r.parser.lazy[r.now().line])return n(t);if(eu(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),u;return(0,X.N)(e,i,"linePrefix",5)(t)}function i(e){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):eu(e)?u(e):n(e)}},partial:!0},eH={name:"setextUnderline",tokenize:function(e,t,n){let r,u;let i=this,l=i.events.length;for(;l--;)if("lineEnding"!==i.events[l][1].type&&"linePrefix"!==i.events[l][1].type&&"content"!==i.events[l][1].type){u="paragraph"===i.events[l][1].type;break}return function(t){if(!i.parser.lazy[i.now().line]&&(i.interrupt||u))return e.enter("setextHeadingLine"),e.enter("setextHeadingLineSequence"),r=t,function t(n){if(n===r)return e.consume(n),t;return e.exit("setextHeadingLineSequence"),(0,X.N)(e,o,"lineSuffix")(n)}(t);return n(t)};function o(r){if(null===r||eu(r))return e.exit("setextHeadingLine"),t(r);return n(r)}},resolveTo:function(e,t){let n,r,u,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),u||"definition"!==e[i][1].type||(u=i);let l={type:"setextHeading",start:Object.assign({},e[r][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[r][1].type="setextHeadingText",u?(e.splice(r,0,["enter",l,t]),e.splice(u+1,0,["exit",e[n][1],t]),e[n][1].end=Object.assign({},e[u][1].end)):e[n][1]=l,e.push(["exit",l,t]),e}},eV=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],eq=["pre","script","style","textarea"],eZ={tokenize:function(e,t,n){return function(r){return e.exit("htmlFlowData"),e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),e.attempt(ef,t,n)}},partial:!0},eW={name:"codeFenced",tokenize:function(e,t,n){let r;let u=this,i={tokenize:function(e,t,n){let u=0;return(0,X.N)(e,function(t){return e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(l){if(l===r)return e.consume(l),u++,t;if(u<s)return n(l);return e.exit("codeFencedFenceSequence"),(0,X.N)(e,i,"whitespace")(l)}(t)},"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4);function i(r){if(null===r||eu(r))return e.exit("codeFencedFence"),t(r);return n(r)}},partial:!0},l={tokenize:function(e,t,n){let r=this;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),u};function u(e){return r.parser.lazy[r.now().line]?n(e):t(e)}},partial:!0},o=this.events[this.events.length-1],a=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,s=0;return function(t){return e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),r=t,function t(u){if(u===r)return e.consume(u),s++,t;return e.exit("codeFencedFenceSequence"),s<3?n(u):(0,X.N)(e,c,"whitespace")(u)}(t)};function c(t){if(null===t||eu(t))return p(t);return e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(u){if(null===u||ei(u))return e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,X.N)(e,f,"whitespace")(u);if(96===u&&u===r)return n(u);return e.consume(u),t}(t)}function f(t){if(null===t||eu(t))return p(t);return e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(u){if(null===u||eu(u))return e.exit("chunkString"),e.exit("codeFencedFenceMeta"),p(u);if(96===u&&u===r)return n(u);return e.consume(u),t}(t)}function p(n){return e.exit("codeFencedFence"),u.interrupt?t(n):function t(n){if(null===n)return d(n);if(eu(n))return e.attempt(l,e.attempt(i,d,a?(0,X.N)(e,t,"linePrefix",a+1):t),d)(n);return e.enter("codeFlowValue"),function n(r){if(null===r||eu(r))return e.exit("codeFlowValue"),t(r);return e.consume(r),n}(n)}(n)}function d(n){return e.exit("codeFenced"),t(n)}},concrete:!0};var eQ=n(89141);let eY={name:"characterReference",tokenize:function(e,t,n){let r,u;let i=this,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),o};function o(t){if(35===t)return e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),a;return e.enter("characterReferenceValue"),r=31,u=J,s(t)}function a(t){if(88===t||120===t)return e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,u=en,s;return e.enter("characterReferenceValue"),r=7,u=et,s(t)}function s(o){let a;if(59===o&&l){if(a=e.exit("characterReferenceValue"),u===J&&!(0,eQ.s)(i.sliceSerialize(a)))return n(o);return e.enter("characterReferenceMarker"),e.consume(o),e.exit("characterReferenceMarker"),e.exit("characterReference"),t}if(u(o)&&l++<r)return e.consume(o),s;return n(o)}}},eK={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){if(er(r))return e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t;return n(r)}}},eX={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,X.N)(e,t,"linePrefix")}}},eG={name:"labelEnd",tokenize:function(e,t,n){let r,u;let i=this,l=i.events.length;for(;l--;)if(("labelImage"===i.events[l][1].type||"labelLink"===i.events[l][1].type)&&!i.events[l][1]._balanced){r=i.events[l][1];break}return function(t){if(!r)return n(t);if(r._inactive)return a(t);return u=i.parser.defined.includes((0,eR.B)(i.sliceSerialize({start:r.end,end:i.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),o};function o(n){if(40===n)return e.attempt(eJ,t,u?t:a)(n);if(91===n)return e.attempt(e0,t,u?e.attempt(e1,t,a):a)(n);return u?t(n):a(n)}function a(e){return r._balanced=!0,n(e)}},resolveTo:function(e,t){let n,r,u,i,l=e.length,o=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(u){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){o=2;break}}else"labelEnd"===n.type&&(u=l);let a={type:"labelLink"===e[r][1].type?"link":"image",start:Object.assign({},e[r][1].start),end:Object.assign({},e[e.length-1][1].end)},s={type:"label",start:Object.assign({},e[r][1].start),end:Object.assign({},e[u][1].end)},c={type:"labelText",start:Object.assign({},e[r+o+2][1].end),end:Object.assign({},e[u-2][1].start)};return i=[["enter",a,t],["enter",s,t]],i=(0,Y.V)(i,e.slice(r+1,r+o+3)),i=(0,Y.V)(i,[["enter",c,t]]),i=(0,Y.V)(i,(0,ev.W)(t.parser.constructs.insideSpan.null,e.slice(r+o+4,u-3),t)),i=(0,Y.V)(i,[["exit",c,t],e[u-2],e[u-1],["exit",s,t]]),i=(0,Y.V)(i,e.slice(u+1)),i=(0,Y.V)(i,[["exit",a,t]]),(0,Y.m)(e,r,e.length,i),e},resolveAll:function(e){let t,n=-1;for(;++n<e.length;)("labelImage"===(t=e[n][1]).type||"labelLink"===t.type||"labelEnd"===t.type)&&(e.splice(n+1,"labelImage"===t.type?4:2),t.type="data",n++);return e}},eJ={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),eM(e,r)};function r(t){if(41===t)return l(t);return eL(e,u,n,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function u(t){return ei(t)?eM(e,i)(t):l(t)}function i(t){if(34===t||39===t||40===t)return ez(e,eM(e,l),n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t);return l(t)}function l(r){if(41===r)return e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t;return n(r)}}},e0={tokenize:function(e,t,n){let r=this;return function(t){return eO.call(r,e,u,n,"reference","referenceMarker","referenceString")(t)};function u(e){return r.parser.defined.includes((0,eR.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}}},e1={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){if(93===r)return e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t;return n(r)}}},e2={name:"labelStartImage",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),u};function u(t){if(91===t)return e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i;return n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}},resolveAll:eG.resolveAll};var e3=n(30626);let e6={name:"attention",tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,u=this.previous,i=(0,e3.S)(u);return function(l){return e.enter("attentionSequence"),n=l,function l(o){if(o===n)return e.consume(o),l;let a=e.exit("attentionSequence"),s=(0,e3.S)(o),c=!s||2===s&&i||r.includes(o),f=!i||2===i&&s||r.includes(u);return a._open=!!(42===n?c:c&&(i||!f)),a._close=!!(42===n?f:f&&(s||!c)),t(o)}(l)}},resolveAll:function(e,t){let n,r,u,i,l,o,a,s,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;o=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let f=Object.assign({},e[n][1].end),p=Object.assign({},e[c][1].start);e5(f,-o),e5(p,o),i={type:o>1?"strongSequence":"emphasisSequence",start:f,end:Object.assign({},e[n][1].end)},l={type:o>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[c][1].start),end:p},u={type:o>1?"strongText":"emphasisText",start:Object.assign({},e[n][1].end),end:Object.assign({},e[c][1].start)},r={type:o>1?"strong":"emphasis",start:Object.assign({},i.start),end:Object.assign({},l.end)},e[n][1].end=Object.assign({},i.start),e[c][1].start=Object.assign({},l.end),a=[],e[n][1].end.offset-e[n][1].start.offset&&(a=(0,Y.V)(a,[["enter",e[n][1],t],["exit",e[n][1],t]])),a=(0,Y.V)(a,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",u,t]]),a=(0,Y.V)(a,(0,ev.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),a=(0,Y.V)(a,[["exit",u,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(s=2,a=(0,Y.V)(a,[["enter",e[c][1],t],["exit",e[c][1],t]])):s=0,(0,Y.m)(e,n-1,c-n+3,a),c=n+a.length-s-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e}};function e5(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let e7={name:"labelStartLink",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),u};function u(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}},resolveAll:eG.resolveAll},e4={42:eE,43:eE,45:eE,48:eE,49:eE,50:eE,51:eE,52:eE,53:eE,54:eE,55:eE,56:eE,57:eE,62:ew},e9={91:{name:"definition",tokenize:function(e,t,n){let r;let u=this;return function(t){return e.enter("definition"),eO.call(u,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function i(t){if(r=(0,eR.B)(u.sliceSerialize(u.events[u.events.length-1][1]).slice(1,-1)),58===t)return e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),eM(e,eL(e,e.attempt(e_,(0,X.N)(e,l,"whitespace"),(0,X.N)(e,l,"whitespace")),n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString"));return n(t)}function l(i){if(null===i||eu(i))return e.exit("definition"),u.parser.defined.includes(r)||u.parser.defined.push(r),t(i);return n(i)}}}},e8={[-2]:eU,[-1]:eU,32:eU},te={35:{name:"headingAtx",tokenize:function(e,t,n){let r=this,u=0;return function(i){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function i(l){if(35===l&&u++<6)return e.consume(l),i;if(null===l||ei(l))return e.exit("atxHeadingSequence"),r.interrupt?t(l):function n(r){if(35===r)return e.enter("atxHeadingSequence"),function t(r){if(35===r)return e.consume(r),t;return e.exit("atxHeadingSequence"),n(r)}(r);if(null===r||eu(r))return e.exit("atxHeading"),t(r);if(el(r))return(0,X.N)(e,n,"whitespace")(r);return e.enter("atxHeadingText"),function t(r){if(null===r||35===r||ei(r))return e.exit("atxHeadingText"),n(r);return e.consume(r),t}(r)}(l);return n(l)}(i)}},resolve:function(e,t){let n,r,u=e.length-2,i=3;return"whitespace"===e[3][1].type&&(i+=2),u-2>i&&"whitespace"===e[u][1].type&&(u-=2),"atxHeadingSequence"===e[u][1].type&&(i===u-1||u-4>i&&"whitespace"===e[u-2][1].type)&&(u-=i+1===u?2:4),u>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[u][1].end},r={type:"chunkText",start:e[i][1].start,end:e[u][1].end,contentType:"text"},(0,Y.m)(e,i,u-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e}},42:ek,45:[eH,ek],60:{name:"htmlFlow",tokenize:function(e,t,n){let r,u,i,l,o;let a=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),s};function s(l){if(33===l)return e.consume(l),c;if(47===l)return e.consume(l),d;if(63===l)return e.consume(l),r=3,a.interrupt?t:T;if(G(l))return e.consume(l),i=String.fromCharCode(l),u=!0,h;return n(l)}function c(u){if(45===u)return e.consume(u),r=2,f;if(91===u)return e.consume(u),r=5,i="CDATA[",l=0,p;if(G(u))return e.consume(u),r=4,a.interrupt?t:T;return n(u)}function f(r){if(45===r)return e.consume(r),a.interrupt?t:T;return n(r)}function p(r){if(r===i.charCodeAt(l++))return e.consume(r),l===i.length?a.interrupt?t:E:p;return n(r)}function d(t){if(G(t))return e.consume(t),i=String.fromCharCode(t),h;return n(t)}function h(l){if(null===l||47===l||62===l||ei(l)){if(47!==l&&u&&eq.includes(i.toLowerCase()))return r=1,a.interrupt?t(l):E(l);if(eV.includes(i.toLowerCase())){if(r=6,47===l)return e.consume(l),g;return a.interrupt?t(l):E(l)}return r=7,a.interrupt&&!a.parser.lazy[a.now().line]?n(l):u?m(l):function t(n){if(el(n))return e.consume(n),t;return v(n)}(l)}if(45===l||J(l))return e.consume(l),i+=String.fromCharCode(l),h;return n(l)}function g(r){if(62===r)return e.consume(r),a.interrupt?t:E;return n(r)}function m(t){if(47===t)return e.consume(t),v;if(58===t||95===t||G(t))return e.consume(t),F;if(el(t))return e.consume(t),m;return v(t)}function F(t){if(45===t||46===t||58===t||95===t||J(t))return e.consume(t),F;return A(t)}function A(t){if(61===t)return e.consume(t),y;if(el(t))return e.consume(t),A;return m(t)}function y(t){if(null===t||60===t||61===t||62===t||96===t)return n(t);if(34===t||39===t)return e.consume(t),o=t,x;if(el(t))return e.consume(t),y;return o=null,function t(n){if(null===n||34===n||39===n||60===n||61===n||62===n||96===n||ei(n))return A(n);return e.consume(n),t}(t)}function x(t){if(null===t||eu(t))return n(t);if(t===o)return e.consume(t),b;return e.consume(t),x}function b(e){if(47===e||62===e||el(e))return m(e);return n(e)}function v(t){if(62===t)return e.consume(t),k;return n(t)}function k(t){if(el(t))return e.consume(t),k;return null===t||eu(t)?E(t):n(t)}function E(t){if(45===t&&2===r)return e.consume(t),w;if(60===t&&1===r)return e.consume(t),S;if(62===t&&4===r)return e.consume(t),P;if(63===t&&3===r)return e.consume(t),T;if(93===t&&5===r)return e.consume(t),L;if(eu(t)&&(6===r||7===r))return e.check(eZ,P,C)(t);if(null===t||eu(t))return C(t);return e.consume(t),E}function C(t){return e.exit("htmlFlowData"),function t(n){if(null===n)return O(n);if(eu(n))return e.attempt({tokenize:D,partial:!0},t,O)(n);return e.enter("htmlFlowData"),E(n)}(t)}function D(e,t,n){return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),r};function r(e){return a.parser.lazy[a.now().line]?n(e):t(e)}}function w(t){if(45===t)return e.consume(t),T;return E(t)}function S(t){if(47===t)return e.consume(t),i="",B;return E(t)}function B(t){if(62===t&&eq.includes(i.toLowerCase()))return e.consume(t),P;if(G(t)&&i.length<8)return e.consume(t),i+=String.fromCharCode(t),B;return E(t)}function L(t){if(93===t)return e.consume(t),T;return E(t)}function T(t){if(62===t)return e.consume(t),P;if(45===t&&2===r)return e.consume(t),T;return E(t)}function P(t){if(null===t||eu(t))return e.exit("htmlFlowData"),O(t);return e.consume(t),P}function O(n){return e.exit("htmlFlow"),t(n)}},resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},concrete:!0},61:eH,95:ek,96:eW,126:eW},tt={38:eY,92:eK},tn={[-5]:eX,[-4]:eX,[-3]:eX,33:e2,38:eY,42:e6,60:[{name:"autolink",tokenize:function(e,t,n){let r=1;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),u};function u(t){if(G(t))return e.consume(t),i;return ee(t)?o(t):n(t)}function i(t){return 43===t||45===t||46===t||J(t)?function t(n){if(58===n)return e.consume(n),l;if((43===n||45===n||46===n||J(n))&&r++<32)return e.consume(n),t;return o(n)}(t):o(t)}function l(t){if(62===t)return e.exit("autolinkProtocol"),s(t);if(null===t||32===t||60===t||null!==t&&(t<32||127===t))return n(t);return e.consume(t),l}function o(t){if(64===t)return e.consume(t),r=0,a;if(ee(t))return e.consume(t),o;return n(t)}function a(t){return J(t)?function t(u){if(46===u)return e.consume(u),r=0,a;if(62===u)return e.exit("autolinkProtocol").type="autolinkEmail",s(u);return function u(i){if((45===i||J(i))&&r++<63)return e.consume(i),45===i?u:t;return n(i)}(u)}(t):n(t)}function s(n){return e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t}}},{name:"htmlText",tokenize:function(e,t,n){let r,u,i,l;let o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){if(33===t)return e.consume(t),s;if(47===t)return e.consume(t),v;if(63===t)return e.consume(t),x;if(G(t))return e.consume(t),E;return n(t)}function s(t){if(45===t)return e.consume(t),c;if(91===t)return e.consume(t),u="CDATA[",i=0,g;if(G(t))return e.consume(t),y;return n(t)}function c(t){if(45===t)return e.consume(t),f;return n(t)}function f(t){if(null===t||62===t)return n(t);if(45===t)return e.consume(t),p;return d(t)}function p(e){if(null===e||62===e)return n(e);return d(e)}function d(t){if(null===t)return n(t);if(45===t)return e.consume(t),h;if(eu(t))return l=d,T(t);return e.consume(t),d}function h(t){if(45===t)return e.consume(t),O;return d(t)}function g(t){if(t===u.charCodeAt(i++))return e.consume(t),i===u.length?m:g;return n(t)}function m(t){if(null===t)return n(t);if(93===t)return e.consume(t),F;if(eu(t))return l=m,T(t);return e.consume(t),m}function F(t){if(93===t)return e.consume(t),A;return m(t)}function A(t){if(62===t)return O(t);if(93===t)return e.consume(t),A;return m(t)}function y(t){if(null===t||62===t)return O(t);if(eu(t))return l=y,T(t);return e.consume(t),y}function x(t){if(null===t)return n(t);if(63===t)return e.consume(t),b;if(eu(t))return l=x,T(t);return e.consume(t),x}function b(e){return 62===e?O(e):x(e)}function v(t){if(G(t))return e.consume(t),k;return n(t)}function k(t){if(45===t||J(t))return e.consume(t),k;return function t(n){if(eu(n))return l=t,T(n);if(el(n))return e.consume(n),t;return O(n)}(t)}function E(t){if(45===t||J(t))return e.consume(t),E;if(47===t||62===t||ei(t))return C(t);return n(t)}function C(t){if(47===t)return e.consume(t),O;if(58===t||95===t||G(t))return e.consume(t),D;if(eu(t))return l=C,T(t);if(el(t))return e.consume(t),C;return O(t)}function D(t){if(45===t||46===t||58===t||95===t||J(t))return e.consume(t),D;return function t(n){if(61===n)return e.consume(n),w;if(eu(n))return l=t,T(n);if(el(n))return e.consume(n),t;return C(n)}(t)}function w(t){if(null===t||60===t||61===t||62===t||96===t)return n(t);if(34===t||39===t)return e.consume(t),r=t,S;if(eu(t))return l=w,T(t);if(el(t))return e.consume(t),w;return e.consume(t),r=void 0,L}function S(t){if(t===r)return e.consume(t),B;if(null===t)return n(t);if(eu(t))return l=S,T(t);return e.consume(t),S}function B(e){if(62===e||47===e||ei(e))return C(e);return n(e)}function L(t){if(null===t||34===t||39===t||60===t||61===t||96===t)return n(t);if(62===t||ei(t))return C(t);return e.consume(t),L}function T(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,X.N)(e,P,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function P(t){return e.enter("htmlTextData"),l(t)}function O(r){if(62===r)return e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t;return n(r)}}}],91:e7,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.enter("escapeMarker"),e.consume(t),r};function r(r){if(eu(r))return e.exit("escapeMarker"),e.exit("hardBreakEscape"),t(r);return n(r)}}},eK],93:eG,95:e6,96:{name:"codeText",tokenize:function(e,t,n){let r,u,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){if(96===n)return e.consume(n),i++,t;return e.exit("codeTextSequence"),l(n)}(t)};function l(a){if(null===a)return n(a);if(96===a)return u=e.enter("codeTextSequence"),r=0,function n(l){if(96===l)return e.consume(l),r++,n;if(r===i)return e.exit("codeTextSequence"),e.exit("codeText"),t(l);return u.type="codeTextData",o(l)}(a);if(32===a)return e.enter("space"),e.consume(a),e.exit("space"),l;if(eu(a))return e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),l;return e.enter("codeTextData"),o(a)}function o(t){if(null===t||32===t||96===t||eu(t))return e.exit("codeTextData"),l(t);return e.consume(t),o}},resolve:function(e){let t,n,r=e.length-4,u=3;if(("lineEnding"===e[3][1].type||"space"===e[u][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=u;++t<r;)if("codeTextData"===e[t][1].type){e[u][1].type="codeTextPadding",e[r][1].type="codeTextPadding",u+=2,r-=2;break}}for(t=u-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type}}},tr={null:[e6,em]},tu={null:[42,95]},ti={null:[]},tl=/[\0\t\n\r]/g;var to=n(80248),ta=n(22946);let ts={}.hasOwnProperty,tc=function(e,t,n){let u,i,l,a;return"string"!=typeof t&&(n=t,t=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(x),autolinkProtocol:p,autolinkEmail:p,atxHeading:i(F),blockQuote:i(function(){return{type:"blockquote",children:[]}}),characterEscape:p,characterReference:p,codeFenced:i(m),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:i(m,l),codeText:i(function(){return{type:"inlineCode",value:""}},l),codeTextData:p,data:p,codeFlowValue:p,definition:i(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:i(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:i(A),hardBreakTrailing:i(A),htmlFlow:i(y,l),htmlFlowData:p,htmlText:i(y,l),htmlTextData:p,image:i(function(){return{type:"image",title:null,url:"",alt:null}}),label:l,link:i(x),listItem:i(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){n.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),n.expectingFirstListItemValue=void 0)},listOrdered:i(b,function(){n.expectingFirstListItemValue=!0}),listUnordered:i(b),paragraph:i(function(){return{type:"paragraph",children:[]}}),reference:function(){n.referenceType="collapsed"},referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:i(F),strong:i(function(){return{type:"strong",children:[]}}),thematicBreak:i(function(){return{type:"thematicBreak"}})},exit:{atxHeading:s(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:s(),autolinkEmail:function(e){d.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){d.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:s(),characterEscapeValue:d,characterReferenceMarkerHexadecimal:g,characterReferenceMarkerNumeric:g,characterReferenceValue:function(e){let t;let r=this.sliceSerialize(e),u=n.characterReferenceType;u?(t=(0,to.C)(r,"characterReferenceMarkerNumeric"===u?10:16),n.characterReferenceType=void 0):t=(0,eQ.s)(r);let i=this.stack.pop();i.value+=t,i.position.end=tf(e.end)},codeFenced:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),n.flowCodeInside=void 0}),codeFencedFence:function(){if(n.flowCodeInside)return;this.buffer(),n.flowCodeInside=!0},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:d,codeIndented:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:d,data:d,definition:s(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eR.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:s(),hardBreakEscape:s(h),hardBreakTrailing:s(h),htmlFlow:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:d,htmlText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:d,image:s(function(){let e=this.stack[this.stack.length-1];if(n.inReference){let t=n.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;n.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),r=this.stack[this.stack.length-1];if(n.inReference=!0,"link"===r.type){let t=e.children;r.children=t}else r.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=(0,ta.s)(t),n.identifier=(0,eR.B)(t).toLowerCase()},lineEnding:function(e){let r=this.stack[this.stack.length-1];if(n.atHardBreak){r.children[r.children.length-1].position.end=tf(e.end),n.atHardBreak=void 0;return}!n.setextHeadingSlurpLineEnding&&t.canContainEols.includes(r.type)&&(p.call(this,e),d.call(this,e))},link:s(function(){let e=this.stack[this.stack.length-1];if(n.inReference){let t=n.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;n.referenceType=void 0}),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:function(e){let t=this.resume(),r=this.stack[this.stack.length-1];r.label=t,r.identifier=(0,eR.B)(this.sliceSerialize(e)).toLowerCase(),n.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){n.inReference=void 0},setextHeading:s(function(){n.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).charCodeAt(0)?1:2},setextHeadingText:function(){n.setextHeadingSlurpLineEnding=!0},strong:s(),thematicBreak:s()}};!function e(t,n){let r=-1;for(;++r<n.length;){let u=n[r];Array.isArray(u)?e(t,u):function(e,t){let n;for(n in t)if(ts.call(t,n)){if("canContainEols"===n){let r=t[n];r&&e[n].push(...r)}else if("transforms"===n){let r=t[n];r&&e[n].push(...r)}else if("enter"===n||"exit"===n){let r=t[n];r&&Object.assign(e[n],r)}}}(t,u)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let n={type:"root",children:[]},i={stack:[n],tokenStack:[],config:t,enter:a,exit:c,buffer:l,resume:f,setData:r,getData:u},o=[],s=-1;for(;++s<e.length;)("listOrdered"===e[s][1].type||"listUnordered"===e[s][1].type)&&("enter"===e[s][0]?o.push(s):s=function(e,t,n){let r,u,i,l,o=t-1,a=-1,s=!1;for(;++o<=n;){let t=e[o];if("listUnordered"===t[1].type||"listOrdered"===t[1].type||"blockQuote"===t[1].type?("enter"===t[0]?a++:a--,l=void 0):"lineEndingBlank"===t[1].type?"enter"===t[0]&&(!r||l||a||i||(i=o),l=void 0):"linePrefix"===t[1].type||"listItemValue"===t[1].type||"listItemMarker"===t[1].type||"listItemPrefix"===t[1].type||"listItemPrefixWhitespace"===t[1].type||(l=void 0),!a&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===a&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=o;for(u=void 0;l--;){let t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;u&&(e[u][1].type="lineEndingBlank",s=!0),t[1].type="lineEnding",u=l}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}i&&(!u||i<u)&&(r._spread=!0),r.end=Object.assign({},u?e[u][1].start:t[1].end),e.splice(u||o,0,["exit",r,t[2]]),o++,n++}"listItemPrefix"===t[1].type&&(r={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0},e.splice(o,0,["enter",r,t[2]]),o++,n++,i=void 0,l=!0)}}return e[t][1]._spread=s,n}(e,o.pop(),s));for(s=-1;++s<e.length;){let n=t[e[s][0]];ts.call(n,e[s][1].type)&&n[e[s][1].type].call(Object.assign({sliceSerialize:e[s][2].sliceSerialize},i),e[s][1])}if(i.tokenStack.length>0){let e=i.tokenStack[i.tokenStack.length-1];(e[1]||tp).call(i,void 0,e[0])}for(n.position={start:tf(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tf(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},s=-1;++s<t.transforms.length;)n=t.transforms[s](n)||n;return n};function r(e,t){n[e]=t}function u(e){return n[e]}function i(e,t){return function(n){a.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function a(e,t,n){return this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n]),e.position={start:tf(t.start)},e}function s(e){return function(t){e&&e.call(this,t),c.call(this,t)}}function c(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tp).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+o({start:e.start,end:e.end})+"): it’s not open");return n.position.end=tf(e.end),n}function f(){return function(e,t){var{includeImageAlt:n=!0}={};return W(e,n)}(this.stack.pop())}function p(e){let t=this.stack[this.stack.length-1],n=t.children[t.children.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tf(e.start)},t.children.push(n)),this.stack.push(n)}function d(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tf(e.end)}function h(){n.atHardBreak=!0}function g(e){var t;t=e.type,n.characterReferenceType=t}function m(){return{type:"code",lang:null,meta:null,value:""}}function F(){return{type:"heading",depth:void 0,children:[]}}function A(){return{type:"break"}}function y(){return{type:"html",value:""}}function x(){return{type:"link",title:null,url:"",children:[]}}function b(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(n)(function(e){for(;!ep(e););return e}((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={defined:[],lazy:{},constructs:function(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let r;let u=(K.call(e,n)?e[n]:void 0)||(e[n]={}),i=t[n];for(r in i){K.call(u,r)||(u[r]=[]);let e=i[r];!function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);(0,Y.m)(e,0,0,r)}(u[r],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}([r].concat(e.extensions||[])),content:n(ea),document:n(es),flow:n(eg),string:n(eF),text:n(eA)};return t;function n(e){return function(n){return function(e,t,n){let r=Object.assign(n?Object.assign({},n):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1}),u={},i=[],l=[],o=[],a={consume:function(e){eu(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,m()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++)),s.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),s.events.push(["enter",n,s]),o.push(n),n},exit:function(e){let t=o.pop();return t.end=p(),s.events.push(["exit",t,s]),t},attempt:h(function(e,t){g(e,t.from)}),check:h(d),interrupt:h(d,{interrupt:!0})},s={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:f,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,u=[];for(;++r<e.length;){let i;let l=e[r];if("string"==typeof l)i=l;else switch(l){case -5:i="\r";break;case -4:i="\n";break;case -3:i="\r\n";break;case -2:i=t?" ":"	";break;case -1:if(!t&&n)continue;i=" ";break;default:i=String.fromCharCode(l)}n=-2===l,u.push(i)}return u.join("")}(f(e),t)},now:p,defineSkip:function(e){u[e.line]=e.column,m()},write:function(e){if(l=(0,Y.V)(l,e),function(){let e;for(;r._index<l.length;){let n=l[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==l[l.length-1])return[];return g(t,0),s.events=(0,ev.W)(i,s.events,s),s.events}},c=t.tokenize.call(s,a);return t.resolveAll&&i.push(t),s;function f(e){return function(e,t){let n;let r=t.start._index,u=t.start._bufferIndex,i=t.end._index,l=t.end._bufferIndex;return r===i?n=[e[r].slice(u,l)]:(n=e.slice(r,i),u>-1&&(n[0]=n[0].slice(u)),l>0&&n.push(e[i].slice(0,l))),n}(l,e)}function p(){return Object.assign({},r)}function d(e,t){t.restore()}function h(e,t){return function(n,u,i){let l,c,f,d;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null;return h([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(e)};function h(e){if(l=e,c=0,0===e.length)return i;return g(e[c])}function g(e){return function(n){if(d=function(){let e=p(),t=s.previous,n=s.currentConstruct,u=s.events.length,i=Array.from(o);return{restore:function(){r=e,s.previous=t,s.currentConstruct=n,s.events.length=u,o=i,m()},from:u}}(),f=e,e.partial||(s.currentConstruct=e),e.name&&s.parser.constructs.disable.null.includes(e.name))return A(n);return e.tokenize.call(t?Object.assign(Object.create(s),t):s,a,F,A)(n)}}function F(t){return e(f,d),u}function A(e){if(d.restore(),++c<l.length)return g(l[c]);return i}}}function g(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&(0,Y.m)(s.events,t,s.events.length-t,e.resolve(s.events.slice(t),s)),e.resolveTo&&(s.events=e.resolveTo(s.events,s))}function m(){r.line in u&&r.column<2&&(r.column=u[r.line],r.offset+=u[r.line]-1)}}(t,e,n)}}})(n).document().write((i=1,l="",a=!0,function(e,t,n){let r,o,s,c,f;let p=[];for(e=l+e.toString(t),s=0,l="",a&&(65279===e.charCodeAt(0)&&s++,a=void 0);s<e.length;){if(tl.lastIndex=s,c=(r=tl.exec(e))&&void 0!==r.index?r.index:e.length,f=e.charCodeAt(c),!r){l=e.slice(s);break}if(10===f&&s===c&&u)p.push(-3),u=void 0;else switch(u&&(p.push(-5),u=void 0),s<c&&(p.push(e.slice(s,c)),i+=c-s),f){case 0:p.push(65533),i++;break;case 9:for(o=4*Math.ceil(i/4),p.push(-2);i++<o;)p.push(-1);break;case 10:p.push(-4),i=1;break;default:u=!0,i=1}s=c+1}return n&&(u&&p.push(-5),l&&p.push(l),p.push(null)),p})(e,t,!0))))};function tf(e){return{line:e.line,column:e.column,offset:e.offset}}function tp(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+o({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+o({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+o({start:t.start,end:t.end})+") is still open")}let td=function(e){Object.assign(this,{Parser:t=>tc(t,Object.assign({},this.data("settings"),e,{extensions:this.data("micromarkExtensions")||[],mdastExtensions:this.data("fromMarkdownExtensions")||[]}))})};var th=function(e,t,n){var r={type:String(e)};return null==n&&("string"==typeof t||Array.isArray(t))?n=t:Object.assign(r,t),Array.isArray(n)?r.children=n:null!=n&&(r.value=String(n)),r},tg=n(88814);let tm=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null);let u=(0,tg.C)(t),i=r?-1:1;(function e(l,o,a){let s=l&&"object"==typeof l?l:{};if("string"==typeof s.type){let e="string"==typeof s.tagName?s.tagName:"string"==typeof s.name?s.name:void 0;Object.defineProperty(c,"name",{value:"node ("+l.type+(e?"<"+e+">":"")+")"})}return c;function c(){let s,c,f,p=[];if((!t||u(l,o,a[a.length-1]||null))&&!1===(p=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[!0,e];return[e]}(n(l,a)))[0])return p;if(l.children&&"skip"!==p[0])for(c=(r?l.children.length:-1)+i,f=a.concat(l);c>-1&&c<l.children.length;){if(!1===(s=e(l.children[c],c,f)())[0])return s;c="number"==typeof s[1]?s[1]:c+i}return p}})(e,void 0,[])()},tF=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null),tm(e,t,function(e,t){let r=t[t.length-1];return n(e,r?r.children.indexOf(e):null,r)},r)},tA=tx("start"),ty=tx("end");function tx(e){return function(t){let n=t&&t.position&&t.position[e]||{};return{line:n.line||null,column:n.column||null,offset:n.offset>-1?n.offset:null}}}let tb={}.hasOwnProperty;function tv(e){return String(e||"").toUpperCase()}let tk={}.hasOwnProperty;function tE(e,t,n){let r;let u=t&&t.type;if(!u)throw Error("Expected node, got `"+t+"`");return("function"==typeof(r=tk.call(e.handlers,u)?e.handlers[u]:e.passThrough&&e.passThrough.includes(u)?tC:e.unknownHandler)?r:function(e,t){let n=t.data||{};if("value"in t&&!(tk.call(n,"hName")||tk.call(n,"hProperties")||tk.call(n,"hChildren")))return e.augment(t,th("text",t.value));return e(t,"div",tD(e,t))})(e,t,n)}function tC(e,t){return"children"in t?{...t,children:tD(e,t)}:t}function tD(e,t){let n=[];if("children"in t){let r=t.children,u=-1;for(;++u<r.length;){let i=tE(e,r[u],t);if(i){if(u&&"break"===r[u-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=i.value.replace(/^\s+/,"")),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=e.value.replace(/^\s+/,""))}Array.isArray(i)?n.push(...i):n.push(i)}}}return n}tS(/[A-Za-z]/);let tw=tS(/[\dA-Za-z]/);function tS(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}function tB(e){let t=[],n=-1,r=0,u=0;for(;++n<e.length;){let i=e.charCodeAt(n),l="";if(37===i&&tw(e.charCodeAt(n+1))&&tw(e.charCodeAt(n+2)))u=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){let t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(l=String.fromCharCode(i,t),u=1):l="�"}else l=String.fromCharCode(i);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+u+1,l=""),u&&(n+=u,u=0)}return t.join("")+e.slice(r)}function tL(e,t){let n=[],r=-1;for(t&&n.push(th("text","\n"));++r<e.length;)r&&n.push(th("text","\n")),n.push(e[r]);return t&&e.length>0&&n.push(th("text","\n")),n}function tT(e,t){let n;let r=String(t.identifier),u=tB(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);-1===i?(e.footnoteOrder.push(r),e.footnoteCounts[r]=1,n=e.footnoteOrder.length):(e.footnoteCounts[r]++,n=i+1);let l=e.footnoteCounts[r];return e(t,"sup",[e(t.position,"a",{href:"#"+e.clobberPrefix+"fn-"+u,id:e.clobberPrefix+"fnref-"+u+(l>1?"-"+l:""),dataFootnoteRef:!0,ariaDescribedBy:"footnote-label"},[th("text",String(n))])])}function tP(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return th("text","!["+t.alt+r);let u=tD(e,t),i=u[0];i&&"text"===i.type?i.value="["+i.value:u.unshift(th("text","["));let l=u[u.length-1];return l&&"text"===l.type?l.value+=r:u.push(th("text",r)),u}function tO(e){let t=e.spread;return null==t?e.children.length>1:t}function tI(e,t,n){let r=0,u=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(u-1);for(;9===t||32===t;)u--,t=e.codePointAt(u-1)}return u>r?e.slice(r,u):""}tS(/[#-'*+\--9=?A-Z^-~]/),tS(/\d/),tS(/[\dA-Fa-f]/),tS(/[!-/:-@[-`{-~]/),tS(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),tS(/\s/);let tN={blockquote:function(e,t){return e(t,"blockquote",tL(tD(e,t),!0))},break:function(e,t){return[e(t,"br"),th("text","\n")]},code:function(e,t){let n=t.value?t.value+"\n":"",r=t.lang&&t.lang.match(/^[^ \t]+(?=[ \t]|$)/),u={};r&&(u.className=["language-"+r]);let i=e(t,"code",u,[th("text",n)]);return t.meta&&(i.data={meta:t.meta}),e(t.position,"pre",[i])},delete:function(e,t){return e(t,"del",tD(e,t))},emphasis:function(e,t){return e(t,"em",tD(e,t))},footnoteReference:tT,footnote:function(e,t){let n=e.footnoteById,r=1;for(;r in n;)r++;let u=String(r);return n[u]={type:"footnoteDefinition",identifier:u,children:[{type:"paragraph",children:t.children}],position:t.position},tT(e,{type:"footnoteReference",identifier:u,position:t.position})},heading:function(e,t){return e(t,"h"+t.depth,tD(e,t))},html:function(e,t){return e.dangerous?e.augment(t,th("raw",t.value)):null},imageReference:function(e,t){let n=e.definition(t.identifier);if(!n)return tP(e,t);let r={src:tB(n.url||""),alt:t.alt};return null!==n.title&&void 0!==n.title&&(r.title=n.title),e(t,"img",r)},image:function(e,t){let n={src:tB(t.url),alt:t.alt};return null!==t.title&&void 0!==t.title&&(n.title=t.title),e(t,"img",n)},inlineCode:function(e,t){return e(t,"code",[th("text",t.value.replace(/\r?\n|\r/g," "))])},linkReference:function(e,t){let n=e.definition(t.identifier);if(!n)return tP(e,t);let r={href:tB(n.url||"")};return null!==n.title&&void 0!==n.title&&(r.title=n.title),e(t,"a",r,tD(e,t))},link:function(e,t){let n={href:tB(t.url)};return null!==t.title&&void 0!==t.title&&(n.title=t.title),e(t,"a",n,tD(e,t))},listItem:function(e,t,n){let r=tD(e,t),u=n?function(e){let t=e.spread,n=e.children,r=-1;for(;!t&&++r<n.length;)t=tO(n[r]);return!!t}(n):tO(t),i={},l=[];if("boolean"==typeof t.checked){let n;r[0]&&"element"===r[0].type&&"p"===r[0].tagName?n=r[0]:(n=e(null,"p",[]),r.unshift(n)),n.children.length>0&&n.children.unshift(th("text"," ")),n.children.unshift(e(null,"input",{type:"checkbox",checked:t.checked,disabled:!0})),i.className=["task-list-item"]}let o=-1;for(;++o<r.length;){let e=r[o];(u||0!==o||"element"!==e.type||"p"!==e.tagName)&&l.push(th("text","\n")),"element"!==e.type||"p"!==e.tagName||u?l.push(e):l.push(...e.children)}let a=r[r.length-1];return!a||!u&&"tagName"in a&&"p"===a.tagName||l.push(th("text","\n")),e(t,"li",i,l)},list:function(e,t){let n={},r=t.ordered?"ol":"ul",u=tD(e,t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<u.length;){let e=u[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}return e(t,r,n,tL(u,!0))},paragraph:function(e,t){return e(t,"p",tD(e,t))},root:function(e,t){return e.augment(t,th("root",tL(tD(e,t))))},strong:function(e,t){return e(t,"strong",tD(e,t))},table:function(e,t){let n=t.children,r=-1,u=t.align||[],i=[];for(;++r<n.length;){let l=n[r].children,o=0===r?"th":"td",a=[],s=-1,c=t.align?u.length:l.length;for(;++s<c;){let t=l[s];a.push(e(t,o,{align:u[s]},t?tD(e,t):[]))}i[r]=e(n[r],"tr",tL(a,!0))}return e(t,"table",tL([e(i[0].position,"thead",tL([i[0]],!0))].concat(i[1]?e({start:tA(i[1]),end:ty(i[i.length-1])},"tbody",tL(i.slice(1),!0)):[]),!0))},text:function(e,t){return e.augment(t,th("text",function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),u=0,i=[];for(;r;)i.push(tI(t.slice(u,r.index),u>0,!0),r[0]),u=r.index+r[0].length,r=n.exec(t);return i.push(tI(t.slice(u),u>0,!1)),i.join("")}(String(t.value))))},thematicBreak:function(e,t){return e(t,"hr")},toml:tz,yaml:tz,definition:tz,footnoteDefinition:tz};function tz(){return null}let tj={}.hasOwnProperty;function tM(e,t){let n=function(e,t){let n=t||{},r=n.allowDangerousHtml||!1,u={};return l.dangerous=r,l.clobberPrefix=void 0===n.clobberPrefix||null===n.clobberPrefix?"user-content-":n.clobberPrefix,l.footnoteLabel=n.footnoteLabel||"Footnotes",l.footnoteLabelTagName=n.footnoteLabelTagName||"h2",l.footnoteLabelProperties=n.footnoteLabelProperties||{className:["sr-only"]},l.footnoteBackLabel=n.footnoteBackLabel||"Back to content",l.definition=function(e){let t=Object.create(null);if(!e||!e.type)throw Error("mdast-util-definitions expected node");return tF(e,"definition",e=>{let n=tv(e.identifier);n&&!tb.call(t,n)&&(t[n]=e)}),function(e){let n=tv(e);return n&&tb.call(t,n)?t[n]:null}}(e),l.footnoteById=u,l.footnoteOrder=[],l.footnoteCounts={},l.augment=i,l.handlers={...tN,...n.handlers},l.unknownHandler=n.unknownHandler,l.passThrough=n.passThrough,tF(e,"footnoteDefinition",e=>{let t=String(e.identifier).toUpperCase();tj.call(u,t)||(u[t]=e)}),l;function i(e,t){if(e&&"data"in e&&e.data){let n=e.data;n.hName&&("element"!==t.type&&(t={type:"element",tagName:"",properties:{},children:[]}),t.tagName=n.hName),"element"===t.type&&n.hProperties&&(t.properties={...t.properties,...n.hProperties}),"children"in t&&t.children&&n.hChildren&&(t.children=n.hChildren)}if(e){let n="type"in e?e:{position:e};!n||!n.position||!n.position.start||!n.position.start.line||!n.position.start.column||!n.position.end||!n.position.end.line||!n.position.end.column||(t.position={start:tA(n),end:ty(n)})}return t}function l(e,t,n,r){return Array.isArray(n)&&(r=n,n={}),i(e,{type:"element",tagName:t,properties:n||{},children:r||[]})}}(e,t),r=tE(n,e,null),u=function(e){let t=-1,n=[];for(;++t<e.footnoteOrder.length;){let r=e.footnoteById[e.footnoteOrder[t].toUpperCase()];if(!r)continue;let u=tD(e,r),i=String(r.identifier),l=tB(i.toLowerCase()),o=0,a=[];for(;++o<=e.footnoteCounts[i];){let t={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fnref-"+l+(o>1?"-"+o:""),dataFootnoteBackref:!0,className:["data-footnote-backref"],ariaLabel:e.footnoteBackLabel},children:[{type:"text",value:"↩"}]};o>1&&t.children.push({type:"element",tagName:"sup",children:[{type:"text",value:String(o)}]}),a.length>0&&a.push({type:"text",value:" "}),a.push(t)}let s=u[u.length-1];if(s&&"element"===s.type&&"p"===s.tagName){let e=s.children[s.children.length-1];e&&"text"===e.type?e.value+=" ":s.children.push({type:"text",value:" "}),s.children.push(...a)}else u.push(...a);let c={type:"element",tagName:"li",properties:{id:e.clobberPrefix+"fn-"+l},children:tL(u,!0)};r.position&&(c.position=r.position),n.push(c)}if(0===n.length)return null;return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:e.footnoteLabelTagName,properties:{...JSON.parse(JSON.stringify(e.footnoteLabelProperties)),id:"footnote-label"},children:[th("text",e.footnoteLabel)]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:tL(n,!0)},{type:"text",value:"\n"}]}}(n);return u&&r.children.push(th("text","\n"),u),Array.isArray(r)?{type:"root",children:r}:r}let tR=function(e,t){var n;return e&&"run"in e?(n,r,u)=>{e.run(tM(n,t),r,e=>{u(e)})}:(n=e||t,e=>tM(e,n))};var t_=n(15325);class tU{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}}function t$(e,t){let n={},r={},u=-1;for(;++u<e.length;)Object.assign(n,e[u].property),Object.assign(r,e[u].normal);return new tU(n,r,t)}function tH(e){return e.toLowerCase()}tU.prototype.property={},tU.prototype.normal={},tU.prototype.space=null;class tV{constructor(e,t){this.property=e,this.attribute=t}}tV.prototype.space=null,tV.prototype.boolean=!1,tV.prototype.booleanish=!1,tV.prototype.overloadedBoolean=!1,tV.prototype.number=!1,tV.prototype.commaSeparated=!1,tV.prototype.spaceSeparated=!1,tV.prototype.commaOrSpaceSeparated=!1,tV.prototype.mustUseProperty=!1,tV.prototype.defined=!1;let tq=0,tZ=tJ(),tW=tJ(),tQ=tJ(),tY=tJ(),tK=tJ(),tX=tJ(),tG=tJ();function tJ(){return 2**++tq}let t0=Object.keys(u);class t1 extends tV{constructor(e,t,n,r){let i=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++i<t0.length;){let e=t0[i];!function(e,t,n){n&&(e[t]=n)}(this,t0[i],(n&u[e])===u[e])}}}t1.prototype.defined=!0;let t2={}.hasOwnProperty;function t3(e){let t;let n={},r={};for(t in e.properties)if(t2.call(e.properties,t)){let u=e.properties[t],i=new t1(t,e.transform(e.attributes||{},t),u,e.space);e.mustUseProperty&&e.mustUseProperty.includes(t)&&(i.mustUseProperty=!0),n[t]=i,r[tH(t)]=t,r[tH(i.attribute)]=t}return new tU(n,r,e.space)}let t6=t3({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),t5=t3({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function t7(e,t){return t in e?e[t]:t}function t4(e,t){return t7(e,t.toLowerCase())}let t9=t3({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:t4,properties:{xmlns:null,xmlnsXLink:null}}),t8=t3({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:tW,ariaAutoComplete:null,ariaBusy:tW,ariaChecked:tW,ariaColCount:tY,ariaColIndex:tY,ariaColSpan:tY,ariaControls:tK,ariaCurrent:null,ariaDescribedBy:tK,ariaDetails:null,ariaDisabled:tW,ariaDropEffect:tK,ariaErrorMessage:null,ariaExpanded:tW,ariaFlowTo:tK,ariaGrabbed:tW,ariaHasPopup:null,ariaHidden:tW,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:tK,ariaLevel:tY,ariaLive:null,ariaModal:tW,ariaMultiLine:tW,ariaMultiSelectable:tW,ariaOrientation:null,ariaOwns:tK,ariaPlaceholder:null,ariaPosInSet:tY,ariaPressed:tW,ariaReadOnly:tW,ariaRelevant:null,ariaRequired:tW,ariaRoleDescription:tK,ariaRowCount:tY,ariaRowIndex:tY,ariaRowSpan:tY,ariaSelected:tW,ariaSetSize:tY,ariaSort:null,ariaValueMax:tY,ariaValueMin:tY,ariaValueNow:tY,ariaValueText:null,role:null}}),ne=t3({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:t4,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:tX,acceptCharset:tK,accessKey:tK,action:null,allow:null,allowFullScreen:tZ,allowPaymentRequest:tZ,allowUserMedia:tZ,alt:null,as:null,async:tZ,autoCapitalize:null,autoComplete:tK,autoFocus:tZ,autoPlay:tZ,capture:tZ,charSet:null,checked:tZ,cite:null,className:tK,cols:tY,colSpan:null,content:null,contentEditable:tW,controls:tZ,controlsList:tK,coords:tY|tX,crossOrigin:null,data:null,dateTime:null,decoding:null,default:tZ,defer:tZ,dir:null,dirName:null,disabled:tZ,download:tQ,draggable:tW,encType:null,enterKeyHint:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:tZ,formTarget:null,headers:tK,height:tY,hidden:tZ,high:tY,href:null,hrefLang:null,htmlFor:tK,httpEquiv:tK,id:null,imageSizes:null,imageSrcSet:null,inputMode:null,integrity:null,is:null,isMap:tZ,itemId:null,itemProp:tK,itemRef:tK,itemScope:tZ,itemType:tK,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:tZ,low:tY,manifest:null,max:null,maxLength:tY,media:null,method:null,min:null,minLength:tY,multiple:tZ,muted:tZ,name:null,nonce:null,noModule:tZ,noValidate:tZ,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:tZ,optimum:tY,pattern:null,ping:tK,placeholder:null,playsInline:tZ,poster:null,preload:null,readOnly:tZ,referrerPolicy:null,rel:tK,required:tZ,reversed:tZ,rows:tY,rowSpan:tY,sandbox:tK,scope:null,scoped:tZ,seamless:tZ,selected:tZ,shape:null,size:tY,sizes:null,slot:null,span:tY,spellCheck:tW,src:null,srcDoc:null,srcLang:null,srcSet:null,start:tY,step:null,style:null,tabIndex:tY,target:null,title:null,translate:null,type:null,typeMustMatch:tZ,useMap:null,value:tW,width:tY,wrap:null,align:null,aLink:null,archive:tK,axis:null,background:null,bgColor:null,border:tY,borderColor:null,bottomMargin:tY,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:tZ,declare:tZ,event:null,face:null,frame:null,frameBorder:null,hSpace:tY,leftMargin:tY,link:null,longDesc:null,lowSrc:null,marginHeight:tY,marginWidth:tY,noResize:tZ,noHref:tZ,noShade:tZ,noWrap:tZ,object:null,profile:null,prompt:null,rev:null,rightMargin:tY,rules:null,scheme:null,scrolling:tW,standby:null,summary:null,text:null,topMargin:tY,valueType:null,version:null,vAlign:null,vLink:null,vSpace:tY,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:tZ,disableRemotePlayback:tZ,prefix:null,property:null,results:tY,security:null,unselectable:null}}),nt=t3({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:t7,properties:{about:tG,accentHeight:tY,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:tY,amplitude:tY,arabicForm:null,ascent:tY,attributeName:null,attributeType:null,azimuth:tY,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:tY,by:null,calcMode:null,capHeight:tY,className:tK,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:tY,diffuseConstant:tY,direction:null,display:null,dur:null,divisor:tY,dominantBaseline:null,download:tZ,dx:null,dy:null,edgeMode:null,editable:null,elevation:tY,enableBackground:null,end:null,event:null,exponent:tY,externalResourcesRequired:null,fill:null,fillOpacity:tY,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:tX,g2:tX,glyphName:tX,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:tY,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:tY,horizOriginX:tY,horizOriginY:tY,id:null,ideographic:tY,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:tY,k:tY,k1:tY,k2:tY,k3:tY,k4:tY,kernelMatrix:tG,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:tY,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:tY,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:tY,overlineThickness:tY,paintOrder:null,panose1:null,path:null,pathLength:tY,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:tK,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:tY,pointsAtY:tY,pointsAtZ:tY,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:tG,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:tG,rev:tG,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:tG,requiredFeatures:tG,requiredFonts:tG,requiredFormats:tG,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:tY,specularExponent:tY,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:tY,strikethroughThickness:tY,string:null,stroke:null,strokeDashArray:tG,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:tY,strokeOpacity:tY,strokeWidth:null,style:null,surfaceScale:tY,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:tG,tabIndex:tY,tableValues:null,target:null,targetX:tY,targetY:tY,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:tG,to:null,transform:null,u1:null,u2:null,underlinePosition:tY,underlineThickness:tY,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:tY,values:null,vAlphabetic:tY,vMathematical:tY,vectorEffect:null,vHanging:tY,vIdeographic:tY,version:null,vertAdvY:tY,vertOriginX:tY,vertOriginY:tY,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:tY,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),nn=t$([t5,t6,t9,t8,ne],"html"),nr=t$([t5,t6,t9,t8,nt],"svg"),nu=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null);let u=(0,tg.C)(t),i=r?-1:1;(function e(l,o,a){let s=l&&"object"==typeof l?l:{};if("string"==typeof s.type){let e="string"==typeof s.tagName?s.tagName:"string"==typeof s.name?s.name:void 0;Object.defineProperty(c,"name",{value:"node ("+l.type+(e?"<"+e+">":"")+")"})}return c;function c(){let s,c,f,p=[];if((!t||u(l,o,a[a.length-1]||null))&&!1===(p=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[!0,e];return[e]}(n(l,a)))[0])return p;if(l.children&&"skip"!==p[0])for(c=(r?l.children.length:-1)+i,f=a.concat(l);c>-1&&c<l.children.length;){if(!1===(s=e(l.children[c],c,f)())[0])return s;c="number"==typeof s[1]?s[1]:c+i}return p}})(e,void 0,[])()},ni=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null),nu(e,t,function(e,t){let r=t[t.length-1];return n(e,r?r.children.indexOf(e):null,r)},r)};function nl(e){if(e.allowedElements&&e.disallowedElements)throw TypeError("Only one of `allowedElements` and `disallowedElements` should be defined");if(e.allowedElements||e.disallowedElements||e.allowElement)return t=>{ni(t,"element",(t,n,r)=>{let u;if(e.allowedElements?u=!e.allowedElements.includes(t.tagName):e.disallowedElements&&(u=e.disallowedElements.includes(t.tagName)),!u&&e.allowElement&&"number"==typeof n&&(u=!e.allowElement(t,n,r)),u&&"number"==typeof n)return e.unwrapDisallowed&&t.children?r.children.splice(n,1,...t.children):r.children.splice(n,1),n})}}let no=["http","https","mailto","tel"];var na=n(35381);let ns=/^data[-\w.:]+$/i,nc=/-[a-z]/g,nf=/[A-Z]/g;function np(e){return"-"+e.toLowerCase()}function nd(e){return e.charAt(1).toUpperCase()}let nh={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var ng=n(83223);let nm={}.hasOwnProperty,nF=new Set(["table","thead","tbody","tfoot","tr"]);function nA(e,t){let n=-1,r=0;for(;++n<e.children.length&&e.children[n]!==t;)"element"===e.children[n].type&&r++;return r}function ny(e,t){return t.toUpperCase()}let nx={}.hasOwnProperty,nb={plugins:{to:"plugins",id:"change-plugins-to-remarkplugins"},renderers:{to:"components",id:"change-renderers-to-components"},astPlugins:{id:"remove-buggy-html-in-markdown-parser"},allowDangerousHtml:{id:"remove-buggy-html-in-markdown-parser"},escapeHtml:{id:"remove-buggy-html-in-markdown-parser"},source:{to:"children",id:"change-source-to-children"},allowNode:{to:"allowElement",id:"replace-allownode-allowedtypes-and-disallowedtypes"},allowedTypes:{to:"allowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},disallowedTypes:{to:"disallowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},includeNodeIndex:{to:"includeElementIndex",id:"change-includenodeindex-to-includeelementindex"}};function nv(e){for(let t in nb)if(nx.call(nb,t)&&nx.call(e,t)){let e=nb[t];console.warn(`[react-markdown] Warning: please ${e.to?`use \`${e.to}\` instead of`:"remove"} \`${t}\` (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#${e.id}> for more info)`),delete nb[t]}let t=M().use(td).use(e.remarkPlugins||[]).use(tR,{...e.remarkRehypeOptions,allowDangerousHtml:!0}).use(e.rehypePlugins||[]).use(nl,e),n=new F;"string"==typeof e.children?n.value=e.children:void 0!==e.children&&null!==e.children&&console.warn(`[react-markdown] Warning: please pass a string as \`children\` (not: \`${e.children}\`)`);let r=t.runSync(t.parse(n),n);if("root"!==r.type)throw TypeError("Expected a `root` node");let u=i.createElement(i.Fragment,{},function e(t,n){let r;let u=[],l=-1;for(;++l<n.children.length;)"element"===(r=n.children[l]).type?u.push(function(t,n,r,u){let l;let o=t.options,a=t.schema,s=n.tagName,c={},f=a;if("html"===a.space&&"svg"===s&&(f=nr,t.schema=f),n.properties)for(l in n.properties)nm.call(n.properties,l)&&function(e,t,n,r){let u=function(e,t){let n=tH(t),r=t,u=tV;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&ns.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(nc,nd);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!nc.test(e)){let n=e.replace(nf,np);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}u=t1}return new u(r,t)}(r.schema,t),i=n;if(null==i||i!=i)return;Array.isArray(i)&&(i=u.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(i):i.join(" ").trim()),"style"===u.property&&"string"==typeof i&&(i=function(e){let t={};try{ng(e,function(e,n){t[("-ms-"===e.slice(0,4)?`ms-${e.slice(4)}`:e).replace(/-([a-z])/g,ny)]=n})}catch{}return t}(i)),u.space&&u.property?e[nm.call(nh,u.property)?nh[u.property]:u.property]=i:u.attribute&&(e[u.attribute]=i)}(c,l,n.properties[l],t);("ol"===s||"ul"===s)&&t.listDepth++;let p=e(t,n);("ol"===s||"ul"===s)&&t.listDepth--,t.schema=a;let d=n.position||{start:{line:null,column:null,offset:null},end:{line:null,column:null,offset:null}},h=o.components&&nm.call(o.components,s)?o.components[s]:s,g="string"==typeof h||h===i.Fragment;if(!na.isValidElementType(h))throw TypeError(`Component for name \`${s}\` not defined or is not renderable`);if(c.key=[s,d.start.line,d.start.column,r].join("-"),"a"===s&&o.linkTarget&&(c.target="function"==typeof o.linkTarget?o.linkTarget(String(c.href||""),n.children,"string"==typeof c.title?c.title:null):o.linkTarget),"a"===s&&o.transformLinkUri&&(c.href=o.transformLinkUri(String(c.href||""),n.children,"string"==typeof c.title?c.title:null)),g||"code"!==s||"element"!==u.type||"pre"===u.tagName||(c.inline=!0),g||"h1"!==s&&"h2"!==s&&"h3"!==s&&"h4"!==s&&"h5"!==s&&"h6"!==s||(c.level=Number.parseInt(s.charAt(1),10)),"img"===s&&o.transformImageUri&&(c.src=o.transformImageUri(String(c.src||""),String(c.alt||""),"string"==typeof c.title?c.title:null)),!g&&"li"===s&&"element"===u.type){let e=function(e){let t=-1;for(;++t<e.children.length;){let n=e.children[t];if("element"===n.type&&"input"===n.tagName)return n}return null}(n);c.checked=e&&e.properties?!!e.properties.checked:null,c.index=nA(u,n),c.ordered="ol"===u.tagName}return g||"ol"!==s&&"ul"!==s||(c.ordered="ol"===s,c.depth=t.listDepth),"td"!==s&&"th"!==s||(c.align&&(c.style||(c.style={}),c.style.textAlign=c.align,delete c.align),g||(c.isHeader="th"===s)),g||"tr"!==s||"element"!==u.type||(c.isHeader="thead"===u.tagName),o.sourcePos&&(c["data-sourcepos"]=[d.start.line,":",d.start.column,"-",d.end.line,":",d.end.column].map(String).join("")),!g&&o.rawSourcePos&&(c.sourcePosition=n.position),!g&&o.includeElementIndex&&(c.index=nA(u,n),c.siblingCount=nA(u)),g||(c.node=n),p.length>0?i.createElement(h,c,p):i.createElement(h,c)}(t,r,l,n)):"text"===r.type?"element"===n.type&&nF.has(n.tagName)&&function(e){var t=e&&"object"==typeof e&&"text"===e.type?e.value||"":e;return"string"==typeof t&&""===t.replace(/[ \t\n\f\r]/g,"")}(r)||u.push(r.value):"raw"!==r.type||t.options.skipHtml||u.push(r.value);return u}({options:e,schema:nn,listDepth:0},r));return e.className&&(u=i.createElement("div",{className:e.className},u)),u}nv.defaultProps={transformLinkUri:function(e){let t=(e||"").trim(),n=t.charAt(0);if("#"===n||"/"===n)return t;let r=t.indexOf(":");if(-1===r)return t;let u=-1;for(;++u<no.length;){let e=no[u];if(r===e.length&&t.slice(0,e.length).toLowerCase()===e)return t}if(-1!==(u=t.indexOf("?"))&&r>u||-1!==(u=t.indexOf("#"))&&r>u)return t;return"javascript:void(0)"}},nv.propTypes={children:t_.string,className:t_.string,allowElement:t_.func,allowedElements:t_.arrayOf(t_.string),disallowedElements:t_.arrayOf(t_.string),unwrapDisallowed:t_.bool,remarkPlugins:t_.arrayOf(t_.oneOfType([t_.object,t_.func,t_.arrayOf(t_.oneOfType([t_.bool,t_.string,t_.object,t_.func,t_.arrayOf(t_.any)]))])),rehypePlugins:t_.arrayOf(t_.oneOfType([t_.object,t_.func,t_.arrayOf(t_.oneOfType([t_.bool,t_.string,t_.object,t_.func,t_.arrayOf(t_.any)]))])),sourcePos:t_.bool,rawSourcePos:t_.bool,skipHtml:t_.bool,includeElementIndex:t_.bool,transformLinkUri:t_.oneOfType([t_.func,t_.bool]),linkTarget:t_.oneOfType([t_.func,t_.string]),transformImageUri:t_.func,components:t_.object}},29417:(e,t,n)=>{"use strict";n.d(t,{A:()=>e_});var r=n(78002);let u={}.hasOwnProperty,i=p(/[A-Za-z]/),l=p(/[\dA-Za-z]/);function o(e){return null!==e&&(e<32||127===e)}p(/[#-'*+\--9=?A-Z^-~]/);let a=p(/\d/);function s(e){return null!==e&&(e<0||32===e)}p(/[\dA-Fa-f]/),p(/[!-/:-@[-`{-~]/);let c=p(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),f=p(/\s/);function p(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}let d={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(t){if(87===t||119===t)return e.consume(t),u;return n(t)}function u(t){if(87===t||119===t)return e.consume(t),i;return n(t)}function i(t){if(46===t)return e.consume(t),l;return n(t)}function l(e){return null===e||null!==e&&e<-2?n(e):t(e)}},partial:!0},h={tokenize:function(e,t,n){let r,u;return i;function i(t){if(38===t)return e.check(F,a,l)(t);if(46===t||95===t)return e.check(m,a,l)(t);if(null===t||o(t)||f(t)||45!==t&&c(t))return a(t);return e.consume(t),i}function l(t){if(46===t)return u=r,r=void 0,e.consume(t),i;return 95===t&&(r=!0),e.consume(t),i}function a(e){if(!u&&!r)return t(e);return n(e)}},partial:!0},g={tokenize:function(e,t){let n=0;return r;function r(l){if(38===l)return e.check(F,t,u)(l);if(40===l&&n++,41===l)return e.check(m,i,u)(l);if(C(l))return t(l);if(E(l))return e.check(m,t,u)(l);return e.consume(l),r}function u(t){return e.consume(t),r}function i(e){return--n<0?t(e):u(e)}},partial:!0},m={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(u){if(E(u))return e.consume(u),r;return C(u)?t(u):n(u)}},partial:!0},F={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(t){if(i(t))return e.consume(t),r;if(59===t)return e.consume(t),u;return n(t)}function u(e){return C(e)?t(e):n(e)}},partial:!0},A={tokenize:function(e,t,n){let r=this;return function(t){if(87!==t&&119!==t||!w(r.previous)||L(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(d,e.attempt(h,e.attempt(g,u),n),n)(t)};function u(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:w},y={tokenize:function(e,t,n){let r=this;return function(t){if(72!==t&&104!==t||!S(r.previous)||L(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),e.consume(t),u};function u(t){if(84===t||116===t)return e.consume(t),i;return n(t)}function i(t){if(84===t||116===t)return e.consume(t),l;return n(t)}function l(t){if(80===t||112===t)return e.consume(t),a;return n(t)}function a(t){if(83===t||115===t)return e.consume(t),s;return s(t)}function s(t){if(58===t)return e.consume(t),p;return n(t)}function p(t){if(47===t)return e.consume(t),d;return n(t)}function d(t){if(47===t)return e.consume(t),m;return n(t)}function m(t){return null===t||o(t)||f(t)||c(t)?n(t):e.attempt(h,e.attempt(g,F),n)(t)}function F(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:S},x={tokenize:function(e,t,n){let r,u;let i=this;return function(t){if(!D(t)||!B(i.previous)||L(i.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){if(D(r))return e.consume(r),t;if(64===r)return e.consume(r),o;return n(r)}(t)};function o(t){if(46===t)return e.check(m,p,s)(t);if(45===t||95===t)return e.check(m,n,c)(t);if(l(t))return!u&&a(t)&&(u=!0),e.consume(t),o;return p(t)}function s(t){return e.consume(t),r=!0,u=void 0,o}function c(t){return e.consume(t),f}function f(t){if(46===t)return e.check(m,n,s)(t);return o(t)}function p(i){if(r&&!u)return e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(i);return n(i)}},previous:B},b={},v={text:b},k=48;for(;k<123;)b[k]=x,58==++k?k=65:91===k&&(k=97);function E(e){return 33===e||34===e||39===e||41===e||42===e||44===e||46===e||58===e||59===e||60===e||63===e||95===e||126===e}function C(e){return null===e||60===e||s(e)}function D(e){return 43===e||45===e||46===e||95===e||l(e)}function w(e){return null===e||40===e||42===e||95===e||126===e||s(e)}function S(e){return null===e||!i(e)}function B(e){return 47!==e&&S(e)}function L(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}b[43]=x,b[45]=x,b[46]=x,b[95]=x,b[72]=[x,y],b[104]=[x,y],b[87]=[x,A],b[119]=[x,A];var T=n(88932);function P(e){return null!==e&&e<-2}function O(e){return null!==e&&(e<0||32===e)}function I(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}I(/[A-Za-z]/),I(/[\dA-Za-z]/),I(/[#-'*+\--9=?A-Z^-~]/),I(/\d/),I(/[\dA-Fa-f]/),I(/[!-/:-@[-`{-~]/),I(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),I(/\s/);let N={tokenize:function(e,t,n){return(0,T.N)(e,function(e){return null===e||P(e)?t(e):n(e)},"linePrefix")},partial:!0};var z=n(69603);let j={tokenize:function(e,t,n){let r=this;return(0,T.N)(e,function(e){let u=r.events[r.events.length-1];return u&&"gfmFootnoteDefinitionIndent"===u[1].type&&4===u[2].sliceSerialize(u[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function M(e,t,n){let r;let u=this,i=u.events.length,l=u.parser.gfmFootnotes||(u.parser.gfmFootnotes=[]);for(;i--;){let e=u.events[i][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(i){if(!r||!r._balanced)return n(i);let o=(0,z.B)(u.sliceSerialize({start:r.end,end:u.now()}));if(94!==o.charCodeAt(0)||!l.includes(o.slice(1)))return n(i);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(i),e.exit("gfmFootnoteCallLabelMarker"),t(i)}}function R(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},u={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};u.end.column++,u.end.offset++,u.end._bufferIndex++;let i={type:"gfmFootnoteCallString",start:Object.assign({},u.end),end:Object.assign({},e[e.length-1][1].start)},l={type:"chunkString",contentType:"string",start:Object.assign({},i.start),end:Object.assign({},i.end)},o=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",u,t],["exit",u,t],["enter",i,t],["enter",l,t],["exit",l,t],["exit",i,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...o),e}function _(e,t,n){let r;let u=this,i=u.parser.gfmFootnotes||(u.parser.gfmFootnotes=[]),l=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),o};function o(t){if(94!==t)return n(t);return e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",a}function a(o){let c;if(null===o||91===o||l++>999)return n(o);if(93===o){if(!r)return n(o);return e.exit("chunkString"),c=e.exit("gfmFootnoteCallString"),i.includes((0,z.B)(u.sliceSerialize(c)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(o)}return e.consume(o),O(o)||(r=!0),92===o?s:a}function s(t){if(91===t||92===t||93===t)return e.consume(t),l++,a;return a(t)}}function U(e,t,n){let r,u;let i=this,l=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),o=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),a};function a(t){if(94===t)return e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),s;return n(t)}function s(t){let l;if(null===t||91===t||o>999)return n(t);if(93===t){if(!u)return n(t);return l=e.exit("gfmFootnoteDefinitionLabelString"),r=(0,z.B)(i.sliceSerialize(l)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}if(P(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o++,s;return e.enter("chunkString").contentType="string",c(t)}function c(t){if(null===t||P(t)||91===t||93===t||o>999)return e.exit("chunkString"),s(t);return O(t)||(u=!0),o++,e.consume(t),92===t?f:c}function f(t){if(91===t||92===t||93===t)return e.consume(t),o++,c;return c(t)}function p(t){if(58===t)return e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),(0,T.N)(e,d,"gfmFootnoteDefinitionWhitespace");return n(t)}function d(e){return l.includes(r)||l.push(r),t(e)}}function $(e,t,n){return e.check(N,t,e.attempt(j,t,n))}function H(e){e.exit("gfmFootnoteDefinition")}var V=n(30626),q=n(25268);function Z(e){return null!==e&&e<-2}function W(e){return null!==e&&(e<0||32===e)}function Q(e){return -2===e||-1===e||32===e}function Y(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}Y(/[A-Za-z]/),Y(/[\dA-Za-z]/),Y(/[#-'*+\--9=?A-Z^-~]/),Y(/\d/),Y(/[\dA-Fa-f]/),Y(/[!-/:-@[-`{-~]/),Y(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),Y(/\s/);let K={flow:{null:{tokenize:function(e,t,n){let r,u;let i=this,l=[],o=0;return function(t){if(e.enter("table")._align=l,e.enter("tableHead"),e.enter("tableRow"),124===t)return a(t);return o++,e.enter("temporaryTableCellContent"),f(t)};function a(t){return e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,s}function s(t){if(null===t||Z(t))return function(t){if(null===t)return n(t);e.exit("tableRow"),e.exit("tableHead");let r=i.interrupt;return i.interrupt=!0,e.attempt({tokenize:S,partial:!0},function(t){return i.interrupt=r,e.enter("tableDelimiterRow"),d(t)},function(e){return i.interrupt=r,n(e)})(t)}(t);if(Q(t))return e.enter("whitespace"),e.consume(t),c;if(r&&(r=void 0,o++),124===t)return a(t);return e.enter("temporaryTableCellContent"),f(t)}function c(t){if(Q(t))return e.consume(t),c;return e.exit("whitespace"),s(t)}function f(t){if(null===t||124===t||W(t))return e.exit("temporaryTableCellContent"),s(t);return e.consume(t),92===t?p:f}function p(t){if(92===t||124===t)return e.consume(t),f;return f(t)}function d(t){if(null===t||Z(t))return A(t);if(Q(t))return e.enter("whitespace"),e.consume(t),h;if(45===t)return e.enter("tableDelimiterFiller"),e.consume(t),u=!0,l.push("none"),g;if(58===t)return e.enter("tableDelimiterAlignment"),e.consume(t),e.exit("tableDelimiterAlignment"),l.push("left"),m;if(124===t)return e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),d;return n(t)}function h(t){if(Q(t))return e.consume(t),h;return e.exit("whitespace"),d(t)}function g(t){if(45===t)return e.consume(t),g;if(e.exit("tableDelimiterFiller"),58===t)return e.enter("tableDelimiterAlignment"),e.consume(t),e.exit("tableDelimiterAlignment"),l[l.length-1]="left"===l[l.length-1]?"center":"right",F;return d(t)}function m(t){if(45===t)return e.enter("tableDelimiterFiller"),e.consume(t),u=!0,g;return n(t)}function F(t){if(null===t||Z(t))return A(t);if(Q(t))return e.enter("whitespace"),e.consume(t),h;if(124===t)return e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),d;return n(t)}function A(t){if(e.exit("tableDelimiterRow"),!u||o!==l.length)return n(t);if(null===t)return y(t);return e.check(X,y,e.attempt({tokenize:S,partial:!0},(0,T.N)(e,x,"linePrefix",4),y))(t)}function y(n){return e.exit("table"),t(n)}function x(t){return e.enter("tableBody"),b(t)}function b(t){if(e.enter("tableRow"),124===t)return v(t);return e.enter("temporaryTableCellContent"),C(t)}function v(t){return e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),k}function k(t){if(null===t||Z(t))return function(t){if(e.exit("tableRow"),null===t)return w(t);return e.check(X,w,e.attempt({tokenize:S,partial:!0},(0,T.N)(e,b,"linePrefix",4),w))(t)}(t);if(Q(t))return e.enter("whitespace"),e.consume(t),E;if(124===t)return v(t);return e.enter("temporaryTableCellContent"),C(t)}function E(t){if(Q(t))return e.consume(t),E;return e.exit("whitespace"),k(t)}function C(t){if(null===t||124===t||W(t))return e.exit("temporaryTableCellContent"),k(t);return e.consume(t),92===t?D:C}function D(t){if(92===t||124===t)return e.consume(t),C;return C(t)}function w(t){return e.exit("tableBody"),y(t)}function S(e,t,n){return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,T.N)(e,r,"linePrefix")};function r(r){if(i.parser.lazy[i.now().line]||null===r||Z(r))return n(r);let u=i.events[i.events.length-1];if(!i.parser.constructs.disable.null.includes("codeIndented")&&u&&"linePrefix"===u[1].type&&u[2].sliceSerialize(u[1],!0).length>=4)return n(r);return i._gfmTableDynamicInterruptHack=!0,e.check(i.parser.constructs.flow,function(e){return i._gfmTableDynamicInterruptHack=!1,n(e)},function(e){return i._gfmTableDynamicInterruptHack=!1,t(e)})(r)}}},resolve:function(e,t){let n,r,u,i,l,o,a,s=-1;for(;++s<e.length;){let c=e[s][1];if(u&&("temporaryTableCellContent"===c.type&&(i=i||s,l=s),("tableCellDivider"===c.type||"tableRow"===c.type)&&l)){let n={type:"tableContent",start:e[i][1].start,end:e[l][1].end},r={type:"chunkText",start:n.start,end:n.end,contentType:"text"};e.splice(i,l-i+1,["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]),s-=l-i-3,i=void 0,l=void 0}if("exit"===e[s][0]&&void 0!==o&&o+(a?0:1)<s&&("tableCellDivider"===c.type||"tableRow"===c.type&&(o+3<s||"whitespace"!==e[o][1].type))){let u={type:r?"tableDelimiter":n?"tableHeader":"tableData",start:e[o][1].start,end:e[s][1].end};e.splice(s+("tableCellDivider"===c.type?1:0),0,["exit",u,t]),e.splice(o,0,["enter",u,t]),s+=2,o=s+1,a=!0}"tableRow"===c.type&&(u="enter"===e[s][0])&&(o=s+1,a=!1),"tableDelimiterRow"===c.type&&(r="enter"===e[s][0])&&(o=s+1,a=!1),"tableHead"===c.type&&(n="enter"===e[s][0])}return e}}}},X={tokenize:function(e,t,n){let r=0;return function(t){return e.enter("check"),e.consume(t),u};function u(i){if(-1===i||32===i)return e.consume(i),4==++r?t:u;if(null===i||W(i))return t(i);return n(i)}},partial:!0};function G(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}G(/[A-Za-z]/),G(/[\dA-Za-z]/),G(/[#-'*+\--9=?A-Z^-~]/),G(/\d/),G(/[\dA-Fa-f]/),G(/[!-/:-@[-`{-~]/),G(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),G(/\s/);let J={text:{91:{tokenize:function(e,t,n){let r=this;return function(t){if(null!==r.previous||!r._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),u};function u(t){if(null!==t&&(t<0||32===t))return e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i;if(88===t||120===t)return e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i;return n(t)}function i(r){if(93===r)return e.enter("taskListCheckMarker"),e.consume(r),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),e.check({tokenize:ee},t,n);return n(r)}}}}};function ee(e,t,n){let r=this;return(0,T.N)(e,function(e){let u=r.events[r.events.length-1];return(u&&"whitespace"===u[1].type||null!==e&&e<-2)&&null!==e?t(e):n(e)},"whitespace")}function et(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,u=n.indexOf(t);for(;-1!==u;)r++,u=n.indexOf(t,u+t.length);return r}var en=n(88814);let er=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null);let u=(0,en.C)(t),i=r?-1:1;(function e(l,o,a){let s=l&&"object"==typeof l?l:{};if("string"==typeof s.type){let e="string"==typeof s.tagName?s.tagName:"string"==typeof s.name?s.name:void 0;Object.defineProperty(c,"name",{value:"node ("+l.type+(e?"<"+e+">":"")+")"})}return c;function c(){let s,c,f,p=[];if((!t||u(l,o,a[a.length-1]||null))&&!1===(p=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[!0,e];return[e]}(n(l,a)))[0])return p;if(l.children&&"skip"!==p[0])for(c=(r?l.children.length:-1)+i,f=a.concat(l);c>-1&&c<l.children.length;){if(!1===(s=e(l.children[c],c,f)())[0])return s;c="number"==typeof s[1]?s[1]:c+i}return p}})(e,void 0,[])()},eu={}.hasOwnProperty,ei=function(e,t,n,r){let u,i;"string"==typeof t||t instanceof RegExp?(i=[[t,n]],u=r):(i=t,u=n),u||(u={});let l=(0,en.C)(u.ignore||[]),o=function(e){let t=[];if("object"!=typeof e)throw TypeError("Expected array or object as schema");if(Array.isArray(e)){let n=-1;for(;++n<e.length;)t.push([el(e[n][0]),eo(e[n][1])])}else{let n;for(n in e)eu.call(e,n)&&t.push([el(n),eo(e[n])])}return t}(i),a=-1;for(;++a<o.length;)er(e,"text",s);return e;function s(e,t){let n,r=-1;for(;++r<t.length;){let e=t[r];if(l(e,n?n.children.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n;let r=t[t.length-1],u=o[a][0],i=o[a][1],l=0,s=r.children.indexOf(e),c=!1,f=[];u.lastIndex=0;let p=u.exec(e.value);for(;p;){n=p.index;let r={index:p.index,input:p.input,stack:[...t,e]},o=i(...p,r);if("string"==typeof o&&(o=o.length>0?{type:"text",value:o}:void 0),!1!==o&&(l!==n&&f.push({type:"text",value:e.value.slice(l,n)}),Array.isArray(o)?f.push(...o):o&&f.push(o),l=n+p[0].length,c=!0),!u.global)break;p=u.exec(e.value)}return c?(l<e.value.length&&f.push({type:"text",value:e.value.slice(l)}),r.children.splice(s,1,...f)):f=[e],s+f.length}(e,t)}};function el(e){return"string"==typeof e?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function eo(e){return"function"==typeof e?e:()=>e}ec(/[A-Za-z]/),ec(/[\dA-Za-z]/),ec(/[#-'*+\--9=?A-Z^-~]/),ec(/\d/),ec(/[\dA-Fa-f]/),ec(/[!-/:-@[-`{-~]/);let ea=ec(/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/),es=ec(/\s/);function ec(e){return function(t){return null!==t&&e.test(String.fromCharCode(t))}}let ef="phrasing",ep=["autolink","link","image","label"],ed={transforms:[function(e){ei(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,em],[/([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/g,eF]],{ignore:["link","linkReference"]})}],enter:{literalAutolink:function(e){this.enter({type:"link",title:null,url:"",children:[]},e)},literalAutolinkEmail:eg,literalAutolinkHttp:eg,literalAutolinkWww:eg},exit:{literalAutolink:function(e){this.exit(e)},literalAutolinkEmail:function(e){this.config.exit.autolinkEmail.call(this,e)},literalAutolinkHttp:function(e){this.config.exit.autolinkProtocol.call(this,e)},literalAutolinkWww:function(e){this.config.exit.data.call(this,e),this.stack[this.stack.length-1].url="http://"+this.sliceSerialize(e)}}},eh={unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:ef,notInConstruct:ep},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:ef,notInConstruct:ep},{character:":",before:"[ps]",after:"\\/",inConstruct:ef,notInConstruct:ep}]};function eg(e){this.config.enter.autolinkProtocol.call(this,e)}function em(e,t,n,r,u){let i="";if(!eA(u)||(/^w/i.test(t)&&(n=t+n,t="",i="http://"),!function(e){let t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n)))return!1;let l=function(e){let t,n,r,u;let i=/[!"&'),.:;<>?\]}]+$/.exec(e);if(i)for(e=e.slice(0,i.index),t=(u=i[0]).indexOf(")"),n=et(e,"("),r=et(e,")");-1!==t&&n>r;)e+=u.slice(0,t+1),t=(u=u.slice(t+1)).indexOf(")"),r++;return[e,u]}(n+r);if(!l[0])return!1;let o={type:"link",title:null,url:i+t+l[0],children:[{type:"text",value:t+l[0]}]};if(l[1])return[o,{type:"text",value:l[1]}];return o}function eF(e,t,n,r){if(!eA(r,!0)||/[_-\d]$/.test(n))return!1;return{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function eA(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||es(n)||ea(n))&&(!t||47!==n)}var ey=n(22946);function ex(e){if(e.label||!e.identifier)return e.label||"";return(0,ey.s)(e.identifier)}let eb=/\r?\n|\r/g;function ev(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function ek(e,t,n){let r=(n.before||"")+(t||"")+(n.after||""),u=[],i=[],l={},o=-1;for(;++o<e.unsafe.length;){var a;let t;let n=e.unsafe[o];if(!ev(a=e.stack,n.inConstruct,!0)||ev(a,n.notInConstruct,!1))continue;let i=function(e){if(!e._compiled){let t=(e.atBreak?"[\\r\\n][\\t ]*":"")+(e.before?"(?:"+e.before+")":"");e._compiled=RegExp((t?"("+t+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(e.after?"(?:"+e.after+")":""),"g")}return e._compiled}(n);for(;t=i.exec(r);){let e="before"in n||!!n.atBreak,r="after"in n,i=t.index+(e?t[1].length:0);u.includes(i)?(l[i].before&&!e&&(l[i].before=!1),l[i].after&&!r&&(l[i].after=!1)):(u.push(i),l[i]={before:e,after:r})}}u.sort(eE);let s=n.before?n.before.length:0,c=r.length-(n.after?n.after.length:0);for(o=-1;++o<u.length;){let e=u[o];!(e<s)&&!(e>=c)&&(!(e+1<c)||u[o+1]!==e+1||!l[e].after||l[e+1].before||l[e+1].after)&&(u[o-1]!==e-1||!l[e].before||l[e-1].before||l[e-1].after)&&(s!==e&&i.push(eC(r.slice(s,e),"\\")),s=e,!/[!-/:-@[-`{-~]/.test(r.charAt(e))||n.encode&&n.encode.includes(r.charAt(e))?(i.push("&#x"+r.charCodeAt(e).toString(16).toUpperCase()+";"),s++):i.push("\\"))}return i.push(eC(r.slice(s,c),n.after)),i.join("")}function eE(e,t){return e-t}function eC(e,t){let n;let r=/\\(?=[!-/:-@[-`{-~])/g,u=[],i=[],l=e+t,o=-1,a=0;for(;n=r.exec(l);)u.push(n.index);for(;++o<u.length;)a!==u[o]&&i.push(e.slice(a,u[o])),i.push("\\"),a=u[o];return i.push(e.slice(a)),i.join("")}function eD(e){let t=e||{},n=t.now||{},r=t.lineShift||0,u=n.line||1,i=n.column||1;return{move:function(e){let t=e||"",n=t.split(/\r?\n|\r/g),l=n[n.length-1];return u+=n.length-1,i=1===n.length?i+l.length:1+l.length+r,t},current:function(){return{now:{line:u,column:i},lineShift:r}},shift:function(e){r+=e}}}let ew={canContainEols:["delete"],enter:{strikethrough:function(e){this.enter({type:"delete",children:[]},e)}},exit:{strikethrough:function(e){this.exit(e)}}},eS={unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"]}],handlers:{delete:eB}};function eB(e,t,n,r){let u=function(e){let t=e||{},n=t.now||{},r=t.lineShift||0,u=n.line||1,i=n.column||1;return{move:function(e){let t=e||"",n=t.split(/\r?\n|\r/g),l=n[n.length-1];return u+=n.length-1,i=1===n.length?i+l.length:1+l.length+r,t},current:function(){return{now:{line:u,column:i},lineShift:r}},shift:function(e){r+=e}}}(r),i=n.enter("emphasis"),l=u.move("~~");return l+=function(e,t,n){let r=t.indexStack,u=e.children||[],i=[],l=-1,o=n.before;r.push(-1);let a=t.createTracker(n);for(;++l<u.length;){let s;let c=u[l];if(r[r.length-1]=l,l+1<u.length){let n=t.handle.handlers[u[l+1].type];n&&n.peek&&(n=n.peek),s=n?n(u[l+1],e,t,{before:"",after:"",...a.current()}).charAt(0):""}else s=n.after;i.length>0&&("\r"===o||"\n"===o)&&"html"===c.type&&(i[i.length-1]=i[i.length-1].replace(/(\r?\n|\r)$/," "),o=" ",(a=t.createTracker(n)).move(i.join(""))),i.push(a.move(t.handle(c,e,t,{...a.current(),before:o,after:s}))),o=i[i.length-1].slice(-1)}return r.pop(),i.join("")}(e,n,{...u.current(),before:l,after:"~"}),l+=u.move("~~"),i(),l}function eL(e,t,n){let r=e.value||"",u="`",i=-1;for(;RegExp("(^|[^`])"+u+"([^`]|$)").test(r);)u+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){let e;let t=n.unsafe[i],u=function(e){if(!e._compiled){let t=(e.atBreak?"[\\r\\n][\\t ]*":"")+(e.before?"(?:"+e.before+")":"");e._compiled=RegExp((t?"("+t+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(e.after?"(?:"+e.after+")":""),"g")}return e._compiled}(t);if(t.atBreak)for(;e=u.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return u+r+u}function eT(e){return e.length}function eP(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}eB.peek=function(){return"~"},eL.peek=function(){return"`"};let eO={enter:{table:function(e){let t=e._align;this.enter({type:"table",align:t.map(e=>"none"===e?null:e),children:[]},e),this.setData("inTable",!0)},tableData:eN,tableHeader:eN,tableRow:function(e){this.enter({type:"tableRow",children:[]},e)}},exit:{codeText:function(e){let t=this.resume();this.getData("inTable")&&(t=t.replace(/\\([\\|])/g,ez)),this.stack[this.stack.length-1].value=t,this.exit(e)},table:function(e){this.exit(e),this.setData("inTable")},tableData:eI,tableHeader:eI,tableRow:eI}};function eI(e){this.exit(e)}function eN(e){this.enter({type:"tableCell",children:[]},e)}function ez(e,t){return"|"===t?t:e}let ej={exit:{taskListCheckValueChecked:eR,taskListCheckValueUnchecked:eR,paragraph:function(e){let t;let n=this.stack[this.stack.length-2],r=this.stack[this.stack.length-1],u=n.children,i=r.children[0],l=-1;if(n&&"listItem"===n.type&&"boolean"==typeof n.checked&&i&&"text"===i.type){for(;++l<u.length;){let e=u[l];if("paragraph"===e.type){t=e;break}}t===r&&(i.value=i.value.slice(1),0===i.value.length?r.children.shift():r.position&&i.position&&"number"==typeof i.position.start.offset&&(i.position.start.column++,i.position.start.offset++,r.position.start=Object.assign({},i.position.start)))}this.exit(e)}}},eM={unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:function(e,t,n,r){let u=e.children[0],i="boolean"==typeof e.checked&&u&&"paragraph"===u.type,l="["+(e.checked?"x":" ")+"] ",o=function(e){let t=e||{},n=t.now||{},r=t.lineShift||0,u=n.line||1,i=n.column||1;return{move:function(e){let t=e||"",n=t.split(/\r?\n|\r/g),l=n[n.length-1];return u+=n.length-1,i=1===n.length?i+l.length:1+l.length+r,t},current:function(){return{now:{line:u,column:i},lineShift:r}},shift:function(e){r+=e}}}(r);i&&o.move(l);let a=function(e,t,n,r){let u=function(e){let t=e.options.listItemIndent||"tab";if(1===t||"1"===t)return"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),i=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let l=i.length+1;("tab"===u||"mixed"===u&&(t&&"list"===t.type&&t.spread||e.spread))&&(l=4*Math.ceil(l/4));let o=n.createTracker(r);o.move(i+" ".repeat(l-i.length)),o.shift(l);let a=n.enter("listItem"),s=n.indentLines(n.containerFlow(e,o.current()),function(e,t,n){if(t)return(n?"":" ".repeat(l))+e;return(n?i:i+" ".repeat(l-i.length))+e});return a(),s}(e,t,n,{...r,...o.current()});return i&&(a=a.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+l})),a}}};function eR(e){this.stack[this.stack.length-2].checked="taskListCheckValueChecked"===e.type}function e_(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.data();function n(e,n){(t[e]?t[e]:t[e]=[]).push(n)}n("micromarkExtensions",function(e){let t={},n=-1;for(;++n<e.length;)(function(e,t){let n;for(n in t){let i;let l=(u.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];for(i in o){u.call(l,i)||(l[i]=[]);let e=o[i];(function(e,t){let n=-1,u=[];for(;++n<t.length;)("after"===t[n].add?e:u).push(t[n]);(0,r.m)(e,0,0,u)})(l[i],Array.isArray(e)?e:e?[e]:[])}}})(t,e[n]);return t}([v,{document:{91:{tokenize:U,continuation:{tokenize:$},exit:H}},text:{91:{tokenize:_},93:{add:"after",tokenize:M,resolveTo:R}}},function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.singleTilde,n={tokenize:function(e,n,r){let u=this.previous,i=this.events,l=0;return function(o){if(126===u&&"characterEscape"!==i[i.length-1][1].type)return r(o);return e.enter("strikethroughSequenceTemporary"),function i(o){let a=(0,V.S)(u);if(126===o){if(l>1)return r(o);return e.consume(o),l++,i}if(l<2&&!t)return r(o);let s=e.exit("strikethroughSequenceTemporary"),c=(0,V.S)(o);return s._open=!c||2===c&&!!a,s._close=!a||2===a&&!!c,n(o)}(o)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let u=n;for(;u--;)if("exit"===e[u][0]&&"strikethroughSequenceTemporary"===e[u][1].type&&e[u][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[u][1].end.offset-e[u][1].start.offset){e[n][1].type="strikethroughSequence",e[u][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[u][1].start),end:Object.assign({},e[n][1].end)},l={type:"strikethroughText",start:Object.assign({},e[u][1].end),end:Object.assign({},e[n][1].start)},o=[["enter",i,t],["enter",e[u][1],t],["exit",e[u][1],t],["enter",l,t]];(0,r.m)(o,o.length,0,(0,q.W)(t.parser.constructs.insideSpan.null,e.slice(u+1,n),t)),(0,r.m)(o,o.length,0,[["exit",l,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,r.m)(e,u-1,n-u+3,o),n=u+o.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(e),K,J])),n("fromMarkdownExtensions",[ed,{enter:{gfmFootnoteDefinition:function(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)},gfmFootnoteDefinitionLabelString:function(){this.buffer()},gfmFootnoteCall:function(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)},gfmFootnoteCallString:function(){this.buffer()}},exit:{gfmFootnoteDefinition:function(e){this.exit(e)},gfmFootnoteDefinitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,z.B)(this.sliceSerialize(e)).toLowerCase()},gfmFootnoteCall:function(e){this.exit(e)},gfmFootnoteCallString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,z.B)(this.sliceSerialize(e)).toLowerCase()}}},ew,eO,ej]),n("toMarkdownExtensions",{extensions:[eh,function(){return e.peek=function(){return"["},{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]}],handlers:{footnoteDefinition:function(e,t,n,r){let u=eD(r),i=u.move("[^"),l=n.enter("footnoteDefinition"),o=n.enter("label");return i+=u.move(ek(n,ex(e),{...u.current(),before:i,after:"]"})),o(),i+=u.move("]:"+(e.children&&e.children.length>0?" ":"")),u.shift(4),i+=u.move(function(e,t){let n;let r=[],u=0,i=0;for(;n=eb.exec(e);)l(e.slice(u,n.index)),r.push(n[0]),u=n.index+n[0].length,i++;return l(e.slice(u)),r.join("");function l(e){r.push(t(e,i,!e))}}(function(e,t,n){let r=t.indexStack,u=e.children||[],i=t.createTracker(n),l=[],o=-1;for(r.push(-1);++o<u.length;){let n=u[o];r[r.length-1]=o,l.push(i.move(t.handle(n,e,t,{before:"\n",after:"\n",...i.current()}))),"list"!==n.type&&(t.bulletLastUsed=void 0),o<u.length-1&&l.push(i.move(function(e,t,n,r){let u=r.join.length;for(;u--;){let i=r.join[u](e,t,n,r);if(!0===i||1===i)break;if("number"==typeof i)return"\n".repeat(1+i);if(!1===i)return"\n\n\x3c!----\x3e\n\n"}return"\n\n"}(n,u[o+1],e,t)))}return r.pop(),l.join("")}(e,n,u.current()),function(e,t,n){if(t)return(n?"":"    ")+e;return e})),l(),i},footnoteReference:e}};function e(e,t,n,r){let u=eD(r),i=u.move("[^"),l=n.enter("footnoteReference"),o=n.enter("reference");return i+=u.move(ek(n,ex(e),{...u.current(),before:i,after:"]"})),o(),l(),i+=u.move("]")}}(),eS,function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,u=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{table:function(e,t,n,r){return o(function(e,t,n){let r=e.children,u=-1,i=[],l=t.enter("table");for(;++u<r.length;)i[u]=a(r[u],t,n);return l(),i}(e,n,r),e.align)},tableRow:function(e,t,n,r){let u=o([a(e,n,r)]);return u.slice(0,u.indexOf("\n"))},tableCell:l,inlineCode:function(e,t,n){let r=eL(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r}}};function l(e,t,n,r){let u=n.enter("tableCell"),l=n.enter("phrasing"),o=function(e,t,n){let r=t.indexStack,u=e.children||[],i=[],l=-1,o=n.before;r.push(-1);let a=t.createTracker(n);for(;++l<u.length;){let s;let c=u[l];if(r[r.length-1]=l,l+1<u.length){let n=t.handle.handlers[u[l+1].type];n&&n.peek&&(n=n.peek),s=n?n(u[l+1],e,t,{before:"",after:"",...a.current()}).charAt(0):""}else s=n.after;i.length>0&&("\r"===o||"\n"===o)&&"html"===c.type&&(i[i.length-1]=i[i.length-1].replace(/(\r?\n|\r)$/," "),o=" ",(a=t.createTracker(n)).move(i.join(""))),i.push(a.move(t.handle(c,e,t,{...a.current(),before:o,after:s}))),o=i[i.length-1].slice(-1)}return r.pop(),i.join("")}(e,n,{...r,before:i,after:i});return l(),u(),o}function o(e,t){return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(t.align||[]).concat(),r=t.stringLength||eT,u=[],i=[],l=[],o=[],a=0,s=-1;for(;++s<e.length;){let n=[],u=[],f=-1;for(e[s].length>a&&(a=e[s].length);++f<e[s].length;){var c;let i=null==(c=e[s][f])?"":String(c);if(!1!==t.alignDelimiters){let e=r(i);u[f]=e,(void 0===o[f]||e>o[f])&&(o[f]=e)}n.push(i)}i[s]=n,l[s]=u}let f=-1;if("object"==typeof n&&"length"in n)for(;++f<a;)u[f]=eP(n[f]);else{let e=eP(n);for(;++f<a;)u[f]=e}f=-1;let p=[],d=[];for(;++f<a;){let e=u[f],n="",r="";99===e?(n=":",r=":"):108===e?n=":":114===e&&(r=":");let i=!1===t.alignDelimiters?1:Math.max(1,o[f]-n.length-r.length),l=n+"-".repeat(i)+r;!1!==t.alignDelimiters&&((i=n.length+i+r.length)>o[f]&&(o[f]=i),d[f]=i),p[f]=l}i.splice(1,0,p),l.splice(1,0,d),s=-1;let h=[];for(;++s<i.length;){let e=i[s],n=l[s];f=-1;let r=[];for(;++f<a;){let i=e[f]||"",l="",s="";if(!1!==t.alignDelimiters){let e=o[f]-(n[f]||0),t=u[f];114===t?l=" ".repeat(e):99===t?e%2?(l=" ".repeat(e/2+.5),s=" ".repeat(e/2-.5)):s=l=" ".repeat(e/2):s=" ".repeat(e)}!1===t.delimiterStart||f||r.push("|"),!1!==t.padding&&!(!1===t.alignDelimiters&&""===i)&&(!1!==t.delimiterStart||f)&&r.push(" "),!1!==t.alignDelimiters&&r.push(l),r.push(i),!1!==t.alignDelimiters&&r.push(s),!1!==t.padding&&r.push(" "),(!1!==t.delimiterEnd||f!==a-1)&&r.push("|")}h.push(!1===t.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return h.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:u})}function a(e,t,n){let r=e.children,u=-1,i=[],o=t.enter("tableRow");for(;++u<r.length;)i[u]=l(r[u],e,t,n);return o(),i}}(e),eM]})}},88814:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){if(null==e)return i;if("string"==typeof e)return u(function(t){return t&&t.type===e});if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return u(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let u=-1;for(;++u<t.length;)if(t[u].call(this,...n))return!0;return!1})}(e):u(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("function"==typeof e)return u(e);throw Error("Expected function, string, or object as test")};function u(e){return function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return!!e.call(this,...n)}}function i(){return!0}},80414:e=>{"use strict";e.exports=JSON.parse('{"AElig":"\xc6","AMP":"&","Aacute":"\xc1","Acirc":"\xc2","Agrave":"\xc0","Aring":"\xc5","Atilde":"\xc3","Auml":"\xc4","COPY":"\xa9","Ccedil":"\xc7","ETH":"\xd0","Eacute":"\xc9","Ecirc":"\xca","Egrave":"\xc8","Euml":"\xcb","GT":">","Iacute":"\xcd","Icirc":"\xce","Igrave":"\xcc","Iuml":"\xcf","LT":"<","Ntilde":"\xd1","Oacute":"\xd3","Ocirc":"\xd4","Ograve":"\xd2","Oslash":"\xd8","Otilde":"\xd5","Ouml":"\xd6","QUOT":"\\"","REG":"\xae","THORN":"\xde","Uacute":"\xda","Ucirc":"\xdb","Ugrave":"\xd9","Uuml":"\xdc","Yacute":"\xdd","aacute":"\xe1","acirc":"\xe2","acute":"\xb4","aelig":"\xe6","agrave":"\xe0","amp":"&","aring":"\xe5","atilde":"\xe3","auml":"\xe4","brvbar":"\xa6","ccedil":"\xe7","cedil":"\xb8","cent":"\xa2","copy":"\xa9","curren":"\xa4","deg":"\xb0","divide":"\xf7","eacute":"\xe9","ecirc":"\xea","egrave":"\xe8","eth":"\xf0","euml":"\xeb","frac12":"\xbd","frac14":"\xbc","frac34":"\xbe","gt":">","iacute":"\xed","icirc":"\xee","iexcl":"\xa1","igrave":"\xec","iquest":"\xbf","iuml":"\xef","laquo":"\xab","lt":"<","macr":"\xaf","micro":"\xb5","middot":"\xb7","nbsp":"\xa0","not":"\xac","ntilde":"\xf1","oacute":"\xf3","ocirc":"\xf4","ograve":"\xf2","ordf":"\xaa","ordm":"\xba","oslash":"\xf8","otilde":"\xf5","ouml":"\xf6","para":"\xb6","plusmn":"\xb1","pound":"\xa3","quot":"\\"","raquo":"\xbb","reg":"\xae","sect":"\xa7","shy":"\xad","sup1":"\xb9","sup2":"\xb2","sup3":"\xb3","szlig":"\xdf","thorn":"\xfe","times":"\xd7","uacute":"\xfa","ucirc":"\xfb","ugrave":"\xf9","uml":"\xa8","uuml":"\xfc","yacute":"\xfd","yen":"\xa5","yuml":"\xff"}')},61492:e=>{"use strict";e.exports=JSON.parse('{"0":"�","128":"€","130":"‚","131":"ƒ","132":"„","133":"…","134":"†","135":"‡","136":"ˆ","137":"‰","138":"Š","139":"‹","140":"Œ","142":"Ž","145":"‘","146":"’","147":"“","148":"”","149":"•","150":"–","151":"—","152":"˜","153":"™","154":"š","155":"›","156":"œ","158":"ž","159":"Ÿ"}')}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/541.b181f44e.chunk.js.map