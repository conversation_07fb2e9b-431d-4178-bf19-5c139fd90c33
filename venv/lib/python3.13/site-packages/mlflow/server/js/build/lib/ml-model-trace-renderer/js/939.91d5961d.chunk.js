(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[939],{15232:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(63639),o=n(17015),i=n(44795),a=n(65848),s=n(88608),u=n.n(s),l=n(94610),c=n(58813),f=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],p=a.forwardRef(function(e,t){var n=e.className,s=e.component,p=e.viewBox,d=e.spin,h=e.rotate,v=e.tabIndex,m=e.onClick,g=e.children,y=(0,i.A)(e,f);(0,c.$e)(!!(s||g),"Should have `component` prop or `children`."),(0,c.lf)();var b=a.useContext(l.A).prefixCls,A=void 0===b?"anticon":b,w=u()(A,n),E=u()((0,o.A)({},"".concat(A,"-spin"),!!d)),x=(0,r.A)((0,r.A)({},c.yf),{},{className:E,style:h?{msTransform:"rotate(".concat(h,"deg)"),transform:"rotate(".concat(h,"deg)")}:void 0,viewBox:p});p||delete x.viewBox;var C=v;return void 0===C&&m&&(C=-1),a.createElement("span",(0,r.A)((0,r.A)({role:"img"},y),{},{ref:t,tabIndex:C,onClick:m,className:w}),function(){if(s)return a.createElement(s,(0,r.A)({},x),g);if(g)return(0,c.$e)(!!p||1===a.Children.count(g)&&a.isValidElement(g)&&"use"===a.Children.only(g).type,"Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),a.createElement("svg",(0,r.A)((0,r.A)({},x),{},{viewBox:p}),g);return null}())});p.displayName="AntdIcon";let d=p},14232:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(63639),o=n(65848);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"};var a=n(16637),s=function(e,t){return o.createElement(a.A,(0,r.A)((0,r.A)({},e),{},{ref:t,icon:i}))};s.displayName="CheckOutlined";let u=o.forwardRef(s)},71218:(e,t,n)=>{"use strict";n.d(t,{AH:()=>c,Z2:()=>h,i7:()=>f,mL:()=>l,n:()=>u});var r=n(14664),o=n(65848),i=n(1266),a=n(59028),s=n(82974);n(61769),n(35107);var u=function(e,t){var n=arguments;if(null==t||!r.h.call(t,"css"))return o.createElement.apply(void 0,n);var i=n.length,a=Array(i);a[0]=r.E,a[1]=(0,r.c)(e,t);for(var s=2;s<i;s++)a[s]=n[s];return o.createElement.apply(null,a)},l=(0,r.w)(function(e,t){var n=e.styles,u=(0,s.J)([n],void 0,o.useContext(r.T));if(!r.i){for(var l,c=u.name,f=u.styles,p=u.next;void 0!==p;)c+=" "+p.name,f+=p.styles,p=p.next;var d=!0===t.compat,h=t.insert("",{name:c,styles:f},t.sheet,d);if(d)return null;return o.createElement("style",((l={})["data-emotion"]=t.key+"-global "+c,l.dangerouslySetInnerHTML={__html:h},l.nonce=t.sheet.nonce,l))}var v=o.useRef();return(0,a.i)(function(){var e=t.key+"-global",n=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),r=!1,o=document.querySelector('style[data-emotion="'+e+" "+u.name+'"]');return t.sheet.tags.length&&(n.before=t.sheet.tags[0]),null!==o&&(r=!0,o.setAttribute("data-emotion",e),n.hydrate([o])),v.current=[n,r],function(){n.flush()}},[t]),(0,a.i)(function(){var e=v.current,n=e[0];if(e[1]){e[1]=!1;return}if(void 0!==u.next&&(0,i.sk)(t,u.next,!0),n.tags.length){var r=n.tags[n.tags.length-1].nextElementSibling;n.before=r,n.flush()}t.insert("",u,n,!1)},[t,u.name]),null});function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.J)(t)}var f=function(){var e=c.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}},p=function e(t){for(var n=t.length,r=0,o="";r<n;r++){var i=t[r];if(null!=i){var a=void 0;switch(typeof i){case"boolean":break;case"object":if(Array.isArray(i))a=e(i);else for(var s in a="",i)i[s]&&s&&(a&&(a+=" "),a+=s);break;default:a=i}a&&(o&&(o+=" "),o+=a)}}return o},d=function(e){var t=e.cache,n=e.serializedArr;return(0,a.s)(function(){for(var e=0;e<n.length;e++)(0,i.sk)(t,n[e],!1)}),null},h=(0,r.w)(function(e,t){var n=[],a=function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];var a=(0,s.J)(r,t.registered);return n.push(a),(0,i.SF)(t,a,!1),t.key+"-"+a.name},u={css:a,cx:function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return function(e,t,n){var r=[],o=(0,i.Rk)(e,r,n);if(r.length<2)return n;return o+t(r)}(t.registered,a,p(n))},theme:o.useContext(r.T)},l=e.children(u);return o.createElement(o.Fragment,null,o.createElement(d,{cache:t,serializedArr:n}),l)})},33806:(e,t,n)=>{"use strict";var r=n(78675);e.exports=function(e,t){var n,o,i,a,s,u,l,c,f=!1;t||(t={}),i=t.debug||!1;try{if(s=r(),u=document.createRange(),l=document.getSelection(),(c=document.createElement("span")).textContent=e,c.style.all="unset",c.style.position="fixed",c.style.top=0,c.style.clip="rect(0, 0, 0, 0)",c.style.whiteSpace="pre",c.style.webkitUserSelect="text",c.style.MozUserSelect="text",c.style.msUserSelect="text",c.style.userSelect="text",c.addEventListener("copy",function(n){n.stopPropagation(),t.format&&(n.preventDefault(),n.clipboardData.clearData(),n.clipboardData.setData(t.format,e))}),document.body.appendChild(c),u.selectNodeContents(c),l.addRange(u),!document.execCommand("copy"))throw Error("copy command was unsuccessful");f=!0}catch(r){i&&console.error("unable to copy using execCommand: ",r),i&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),f=!0}catch(r){i&&console.error("unable to copy using clipboardData: ",r),i&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",o=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",a=n.replace(/#{\s*key\s*}/g,o),window.prompt(a,e)}}finally{l&&("function"==typeof l.removeRange?l.removeRange(u):l.removeAllRanges()),c&&document.body.removeChild(c),s()}return f}},60148:(e,t,n)=>{"use strict";n.d(t,{A:()=>v});var r=n(63639),o=n(63257),i=n(92401),a=n(30756),s=n(93254),u=n(65848),l=n(72401),c=n(48973),f=n(64011),p=n(48550),d=n(26420),h=function(e){(0,a.A)(n,e);var t=(0,s.A)(n);function n(){var e;return(0,o.A)(this,n),e=t.apply(this,arguments),e.resizeObserver=null,e.childNode=null,e.currentElement=null,e.state={width:0,height:0,offsetHeight:0,offsetWidth:0},e.onResize=function(t){var n=e.props.onResize,o=t[0].target,i=o.getBoundingClientRect(),a=i.width,s=i.height,u=o.offsetWidth,l=o.offsetHeight,c=Math.floor(a),f=Math.floor(s);if(e.state.width!==c||e.state.height!==f||e.state.offsetWidth!==u||e.state.offsetHeight!==l){var p={width:c,height:f,offsetWidth:u,offsetHeight:l};e.setState(p),n&&Promise.resolve().then(function(){n((0,r.A)((0,r.A)({},p),{},{offsetWidth:u,offsetHeight:l}),o)})}},e.setChildNode=function(t){e.childNode=t},e}return(0,i.A)(n,[{key:"componentDidMount",value:function(){this.onComponentUpdated()}},{key:"componentDidUpdate",value:function(){this.onComponentUpdated()}},{key:"componentWillUnmount",value:function(){this.destroyObserver()}},{key:"onComponentUpdated",value:function(){if(this.props.disabled){this.destroyObserver();return}var e=(0,l.A)(this.childNode||this);e!==this.currentElement&&(this.destroyObserver(),this.currentElement=e),!this.resizeObserver&&e&&(this.resizeObserver=new d.A(this.onResize),this.resizeObserver.observe(e))}},{key:"destroyObserver",value:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)}},{key:"render",value:function(){var e=this.props.children,t=(0,c.A)(e);if(t.length>1)(0,f.Ay)(!1,"Find more than one child node with `children` in ResizeObserver. Will only observe first one.");else if(0===t.length)return(0,f.Ay)(!1,"`children` of ResizeObserver is empty. Nothing is in observe."),null;var n=t[0];if(u.isValidElement(n)&&(0,p.f3)(n)){var r=n.ref;t[0]=u.cloneElement(n,{ref:(0,p.K4)(r,this.setChildNode)})}return 1===t.length?t[0]:t.map(function(e,t){if(!u.isValidElement(e)||"key"in e&&null!==e.key)return e;return u.cloneElement(e,{key:"".concat("rc-observer-key","-").concat(t)})})}}]),n}(u.Component);h.displayName="ResizeObserver";let v=h},13845:(e,t,n)=>{"use strict";n.d(t,{A:()=>eJ});var r,o,i=n(63639),a=n(47148),s=n(63257),u=n(92401),l=n(23781),c=n(30756),f=n(93254),p=n(17015),d=n(65848),h=n.n(d),v=n(8155),m=n.n(v),g=function(e){return+setTimeout(e,16)},y=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(g=function(e){return window.requestAnimationFrame(e)},y=function(e){return window.cancelAnimationFrame(e)});var b=0,A=new Map;function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=b+=1;return!function t(r){if(0===r)A.delete(n),e();else{var o=g(function(){t(r-1)});A.set(n,o)}}(t),n}function E(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}w.cancel=function(e){var t=A.get(e);return A.delete(t),y(t)};var x=n(90240),C=n(85822);function O(e,t,n,r){var o=m().unstable_batchedUpdates?function(e){m().unstable_batchedUpdates(n,e)}:n;return e.addEventListener&&e.addEventListener(t,o,r),{remove:function(){e.removeEventListener&&e.removeEventListener(t,o,r)}}}function T(){return!!("undefined"!=typeof window&&window.document&&window.document.createElement)}var S=(0,d.forwardRef)(function(e,t){var n=e.didUpdate,r=e.getContainer,o=e.children,i=(0,d.useRef)(),a=(0,d.useRef)();(0,d.useImperativeHandle)(t,function(){return{}});var s=(0,d.useRef)(!1);return!s.current&&T()&&(a.current=r(),i.current=a.current.parentNode,s.current=!0),(0,d.useEffect)(function(){null==n||n(e)}),(0,d.useEffect)(function(){return null===a.current.parentNode&&null!==i.current&&i.current.appendChild(a.current),function(){var e,t;null===(e=a.current)||void 0===e||null===(t=e.parentNode)||void 0===t||t.removeChild(a.current)}},[]),a.current?m().createPortal(o,a.current):null}),N=n(88608),k=n.n(N),P=n(19721),R=n(44795),M=n(98697);function _(e){var t=e.prefixCls,n=e.motion,r=e.animation,o=e.transitionName;if(n)return n;if(r)return{motionName:"".concat(t,"-").concat(r)};if(o)return{motionName:o};return null}function D(e){var t=e.prefixCls,n=e.visible,r=e.zIndex,o=e.mask,s=e.maskMotion,u=e.maskAnimation,l=e.maskTransitionName;if(!o)return null;var c={};return(s||l||u)&&(c=(0,i.A)({motionAppear:!0},_({motion:s,prefixCls:t,transitionName:l,animation:u}))),d.createElement(M.A,(0,a.A)({},c,{visible:n,removeOnLeave:!0}),function(e){var n=e.className;return d.createElement("div",{style:{zIndex:r},className:k()("".concat(t,"-mask"),n)})})}var I=n(48550),L=n(82854),z={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-",O:"-o-"};function H(){if(void 0!==r)return r;r="";var e=document.createElement("p").style;for(var t in z)t+"Transform"in e&&(r=t);return r}function j(){return H()?"".concat(H(),"TransitionProperty"):"transitionProperty"}function V(){return H()?"".concat(H(),"Transform"):"transform"}function F(e,t){var n=j();n&&(e.style[n]=t,"transitionProperty"!==n&&(e.style.transitionProperty=t))}function U(e,t){var n=V();n&&(e.style[n]=t,"transform"!==n&&(e.style.transform=t))}var W=/matrix\((.*)\)/,B=/matrix3d\((.*)\)/;function K(e){return(K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var G=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source;function Y(e){var t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t}function X(e,t,n){var r=n;if("object"===K(t)){for(var i in t)t.hasOwnProperty(i)&&X(e,i,t[i]);return}if(void 0!==r){"number"==typeof r&&(r="".concat(r,"px")),e.style[t]=r;return}return o(e,t)}function q(e,t){var n=e["page".concat(t?"Y":"X","Offset")],r="scroll".concat(t?"Top":"Left");if("number"!=typeof n){var o=e.document;"number"!=typeof(n=o.documentElement[r])&&(n=o.body[r])}return n}function Q(e){var t,n,r,o,i,a,s=(i=(o=e.ownerDocument).body,a=o&&o.documentElement,n=(t=e.getBoundingClientRect()).left,r=t.top,{left:n-=a.clientLeft||i.clientLeft||0,top:r-=a.clientTop||i.clientTop||0}),u=e.ownerDocument,l=u.defaultView||u.parentWindow;return s.left+=q(l),s.top+=q(l,!0),s}function Z(e){return null!=e&&e==e.window}function $(e){if(Z(e))return e.document;if(9===e.nodeType)return e;return e.ownerDocument}var J=RegExp("^(".concat(G,")(?!px)[a-z%]+$"),"i"),ee=/^(top|right|bottom|left)$/,et="currentStyle",en="runtimeStyle",er="left";function eo(e,t){if("left"===e)return t.useCssRight?"right":e;return t.useCssBottom?"bottom":e}function ei(e){if("left"===e)return"right";if("right"===e)return"left";if("top"===e)return"bottom";if("bottom"===e)return"top"}function ea(e,t,n){"static"===X(e,"position")&&(e.style.position="relative");var r=-999,o=-999,i=eo("left",n),a=eo("top",n),s=ei(i),u=ei(a);"left"!==i&&(r=999),"top"!==a&&(o=999);var l="",c=Q(e);("left"in t||"top"in t)&&(l=e.style.transitionProperty||e.style[j()]||"",F(e,"none")),"left"in t&&(e.style[s]="",e.style[i]="".concat(r,"px")),"top"in t&&(e.style[u]="",e.style[a]="".concat(o,"px")),Y(e);var f=Q(e),p={};for(var d in t)if(t.hasOwnProperty(d)){var h=eo(d,n),v="left"===d?r:o,m=c[d]-f[d];h===d?p[h]=v+m:p[h]=v-m}X(e,p),Y(e),("left"in t||"top"in t)&&F(e,l);var g={};for(var y in t)if(t.hasOwnProperty(y)){var b=eo(y,n),A=t[y]-c[y];y===b?g[b]=p[b]+A:g[b]=p[b]-A}X(e,g)}function es(e,t){for(var n=0;n<e.length;n++)t(e[n])}function eu(e){return"border-box"===o(e,"boxSizing")}"undefined"!=typeof window&&(o=window.getComputedStyle?function(e,t,n){var r=n,o="",i=$(e);return(r=r||i.defaultView.getComputedStyle(e,null))&&(o=r.getPropertyValue(t)||r[t]),o}:function(e,t){var n=e[et]&&e[et][t];if(J.test(n)&&!ee.test(t)){var r=e.style,o=r[er],i=e[en][er];e[en][er]=e[et][er],r[er]="fontSize"===t?"1em":n||0,n=r.pixelLeft+"px",r[er]=o,e[en][er]=i}return""===n?"auto":n});var el=["margin","border","padding"];function ec(e,t,n){var r,i,a,s=0;for(i=0;i<t.length;i++)if(r=t[i])for(a=0;a<n.length;a++){var u=void 0;u="border"===r?"".concat(r).concat(n[a],"Width"):r+n[a],s+=parseFloat(o(e,u))||0}return s}var ef={getParent:function(e){var t=e;do t=11===t.nodeType&&t.host?t.host:t.parentNode;while(t&&1!==t.nodeType&&9!==t.nodeType);return t}};function ep(e,t,n){var r=n;if(Z(e))return"width"===t?ef.viewportWidth(e):ef.viewportHeight(e);if(9===e.nodeType)return"width"===t?ef.docWidth(e):ef.docHeight(e);var i="width"===t?["Left","Right"]:["Top","Bottom"],a="width"===t?e.getBoundingClientRect().width:e.getBoundingClientRect().height;o(e);var s=eu(e),u=0;(null==a||a<=0)&&(a=void 0,(null==(u=o(e,t))||0>Number(u))&&(u=e.style[t]||0),u=parseFloat(u)||0),void 0===r&&(r=s?1:-1);var l=void 0!==a||s,c=a||u;if(-1===r){if(l)return c-ec(e,["border","padding"],i);return u}if(l){if(1===r)return c;return c+(2===r?-ec(e,["border"],i):ec(e,["margin"],i))}return u+ec(e,el.slice(r),i)}es(["Width","Height"],function(e){ef["doc".concat(e)]=function(t){var n=t.document;return Math.max(n.documentElement["scroll".concat(e)],n.body["scroll".concat(e)],ef["viewport".concat(e)](n))},ef["viewport".concat(e)]=function(t){var n="client".concat(e),r=t.document,o=r.body,i=r.documentElement[n];return"CSS1Compat"===r.compatMode&&i||o&&o[n]||i}});var ed={position:"absolute",visibility:"hidden",display:"block"};function eh(){for(var e,t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=n[0];return 0!==o.offsetWidth?e=ep.apply(void 0,n):function(e,t,n){var r,o={},i=e.style;for(r in t)t.hasOwnProperty(r)&&(o[r]=i[r],i[r]=t[r]);for(r in n.call(e),t)t.hasOwnProperty(r)&&(i[r]=o[r])}(o,ed,function(){e=ep.apply(void 0,n)}),e}function ev(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}es(["width","height"],function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);ef["outer".concat(t)]=function(t,n){return t&&eh(t,e,n?0:1)};var n="width"===e?["Left","Right"]:["Top","Bottom"];ef[e]=function(t,r){var i=r;if(void 0!==i){if(t)return o(t),eu(t)&&(i+=ec(t,["padding","border"],n)),X(t,e,i);return}return t&&eh(t,e,-1)}});var em={getWindow:function(e){if(e&&e.document&&e.setTimeout)return e;var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},getDocument:$,offset:function(e,t,n){if(void 0===t)return Q(e);!function(e,t,n){if(n.ignoreShake){var r,o,i,a=Q(e),s=a.left.toFixed(0),u=a.top.toFixed(0),l=t.left.toFixed(0),c=t.top.toFixed(0);if(s===l&&u===c)return}n.useCssRight||n.useCssBottom?ea(e,t,n):n.useCssTransform&&V()in document.body.style?(r=Q(e),i={x:(o=function(e){var t=window.getComputedStyle(e,null),n=t.getPropertyValue("transform")||t.getPropertyValue(V());if(n&&"none"!==n){var r=n.replace(/[^0-9\-.,]/g,"").split(",");return{x:parseFloat(r[12]||r[4],0),y:parseFloat(r[13]||r[5],0)}}return{x:0,y:0}}(e)).x,y:o.y},"left"in t&&(i.x=o.x+t.left-r.left),"top"in t&&(i.y=o.y+t.top-r.top),function(e,t){var n=window.getComputedStyle(e,null),r=n.getPropertyValue("transform")||n.getPropertyValue(V());if(r&&"none"!==r){var o,i=r.match(W);i?((o=(i=i[1]).split(",").map(function(e){return parseFloat(e,10)}))[4]=t.x,o[5]=t.y,U(e,"matrix(".concat(o.join(","),")"))):((o=r.match(B)[1].split(",").map(function(e){return parseFloat(e,10)}))[12]=t.x,o[13]=t.y,U(e,"matrix3d(".concat(o.join(","),")")))}else U(e,"translateX(".concat(t.x,"px) translateY(").concat(t.y,"px) translateZ(0)"))}(e,i)):ea(e,t,n)}(e,t,n||{})},isWindow:Z,each:es,css:X,clone:function(e){var t,n={};for(t in e)e.hasOwnProperty(t)&&(n[t]=e[t]);if(e.overflow)for(t in e)e.hasOwnProperty(t)&&(n.overflow[t]=e.overflow[t]);return n},mix:ev,getWindowScrollLeft:function(e){return q(e)},getWindowScrollTop:function(e){return q(e,!0)},merge:function(){for(var e={},t=0;t<arguments.length;t++)em.mix(e,t<0||arguments.length<=t?void 0:arguments[t]);return e},viewportWidth:0,viewportHeight:0};ev(em,ef);var eg=em.getParent;function ey(e){if(em.isWindow(e)||9===e.nodeType)return null;var t,n=em.getDocument(e).body,r=em.css(e,"position");if(!("fixed"===r||"absolute"===r))return"html"===e.nodeName.toLowerCase()?null:eg(e);for(t=eg(e);t&&t!==n;t=eg(t))if("static"!==(r=em.css(t,"position")))return t;return null}var eb=em.getParent;function eA(e){for(var t={left:0,right:1/0,top:0,bottom:1/0},n=ey(e),r=em.getDocument(e),o=r.defaultView||r.parentWindow,i=r.body,a=r.documentElement;n;){if((-1===navigator.userAgent.indexOf("MSIE")||0!==n.clientWidth)&&n!==i&&n!==a&&"visible"!==em.css(n,"overflow")){var s=em.offset(n);s.left+=n.clientLeft,s.top+=n.clientTop,t.top=Math.max(t.top,s.top),t.right=Math.min(t.right,s.left+n.clientWidth),t.bottom=Math.min(t.bottom,s.top+n.clientHeight),t.left=Math.max(t.left,s.left)}else if(n===i||n===a)break;n=ey(n)}var u=null;em.isWindow(e)||9===e.nodeType||(u=e.style.position,"absolute"!==em.css(e,"position")||(e.style.position="fixed"));var l=em.getWindowScrollLeft(o),c=em.getWindowScrollTop(o),f=em.viewportWidth(o),p=em.viewportHeight(o),d=a.scrollWidth,h=a.scrollHeight,v=window.getComputedStyle(i);if("hidden"===v.overflowX&&(d=o.innerWidth),"hidden"===v.overflowY&&(h=o.innerHeight),e.style&&(e.style.position=u),function(e){if(em.isWindow(e)||9===e.nodeType)return!1;var t=em.getDocument(e).body,n=null;for(n=eb(e);n&&n!==t;n=eb(n))if("fixed"===em.css(n,"position"))return!0;return!1}(e))t.left=Math.max(t.left,l),t.top=Math.max(t.top,c),t.right=Math.min(t.right,l+f),t.bottom=Math.min(t.bottom,c+p);else{var m=Math.max(d,l+f);t.right=Math.min(t.right,m);var g=Math.max(h,c+p);t.bottom=Math.min(t.bottom,g)}return t.top>=0&&t.left>=0&&t.bottom>t.top&&t.right>t.left?t:null}function ew(e){if(em.isWindow(e)||9===e.nodeType){var t,n,r,o=em.getWindow(e);t={left:em.getWindowScrollLeft(o),top:em.getWindowScrollTop(o)},n=em.viewportWidth(o),r=em.viewportHeight(o)}else t=em.offset(e),n=em.outerWidth(e),r=em.outerHeight(e);return t.width=n,t.height=r,t}function eE(e,t){var n=t.charAt(0),r=t.charAt(1),o=e.width,i=e.height,a=e.left,s=e.top;return"c"===n?s+=i/2:"b"===n&&(s+=i),"c"===r?a+=o/2:"r"===r&&(a+=o),{left:a,top:s}}function ex(e,t,n,r,o){var i=eE(t,n[1]),a=eE(e,n[0]),s=[a.left-i.left,a.top-i.top];return{left:Math.round(e.left-s[0]+r[0]-o[0]),top:Math.round(e.top-s[1]+r[1]-o[1])}}function eC(e,t,n){return e.left<n.left||e.left+t.width>n.right}function eO(e,t,n){return e.top<n.top||e.top+t.height>n.bottom}function eT(e,t,n){var r=[];return em.each(e,function(e){r.push(e.replace(t,function(e){return n[e]}))}),r}function eS(e,t){return e[t]=-e[t],e}function eN(e,t){return(/%$/.test(e)?parseInt(e.substring(0,e.length-1),10)/100*t:parseInt(e,10))||0}function ek(e,t){e[0]=eN(e[0],t.width),e[1]=eN(e[1],t.height)}function eP(e,t,n,r){var o=n.points,i=n.offset||[0,0],a=n.targetOffset||[0,0],s=n.overflow,u=n.source||e;i=[].concat(i),a=[].concat(a),s=s||{};var l={},c=0,f=eA(u),p=ew(u);ek(i,p),ek(a,t);var d=ex(p,t,o,i,a),h=em.merge(p,d);if(f&&(s.adjustX||s.adjustY)&&r){if(s.adjustX&&eC(d,p,f)){var v,m,g,y,b=eT(o,/[lr]/gi,{l:"r",r:"l"}),A=eS(i,0),w=eS(a,0);(y=ex(p,t,b,A,w)).left>f.right||y.left+p.width<f.left||(c=1,o=b,i=A,a=w)}if(s.adjustY&&eO(d,p,f)){var E,x=eT(o,/[tb]/gi,{t:"b",b:"t"}),C=eS(i,1),O=eS(a,1);(E=ex(p,t,x,C,O)).top>f.bottom||E.top+p.height<f.top||(c=1,o=x,i=C,a=O)}c&&(d=ex(p,t,o,i,a),em.mix(h,d));var T=eC(d,p,f),S=eO(d,p,f);(T||S)&&(o=n.points,i=n.offset||[0,0],a=n.targetOffset||[0,0]),l.adjustX=s.adjustX&&T,l.adjustY=s.adjustY&&S,(l.adjustX||l.adjustY)&&(v=d,m=em.clone(v),g={width:p.width,height:p.height},l.adjustX&&m.left<f.left&&(m.left=f.left),l.resizeWidth&&m.left>=f.left&&m.left+g.width>f.right&&(g.width-=m.left+g.width-f.right),l.adjustX&&m.left+g.width>f.right&&(m.left=Math.max(f.right-g.width,f.left)),l.adjustY&&m.top<f.top&&(m.top=f.top),l.resizeHeight&&m.top>=f.top&&m.top+g.height>f.bottom&&(g.height-=m.top+g.height-f.bottom),l.adjustY&&m.top+g.height>f.bottom&&(m.top=Math.max(f.bottom-g.height,f.top)),h=em.mix(m,g))}return h.width!==p.width&&em.css(u,"width",em.width(u)+h.width-p.width),h.height!==p.height&&em.css(u,"height",em.height(u)+h.height-p.height),em.offset(u,{left:h.left,top:h.top},{useCssRight:n.useCssRight,useCssBottom:n.useCssBottom,useCssTransform:n.useCssTransform,ignoreShake:n.ignoreShake}),{points:o,offset:i,targetOffset:a,overflow:l}}function eR(e,t,n){var r,o,i=n.target||t;return eP(e,ew(i),n,(r=eA(i),o=ew(i),!!r&&!(o.left+o.width<=r.left)&&!(o.top+o.height<=r.top)&&!(o.left>=r.right)&&!(o.top>=r.bottom)))}function eM(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}eR.__getOffsetParent=ey,eR.__getVisibleRectForElement=eA;var e_=n(4303),eD=n(26420),eI=n(22755);function eL(e,t){var n=null,r=null,o=new eD.A(function(e){var o=(0,P.A)(e,1)[0].target;if(!document.documentElement.contains(o))return;var i=o.getBoundingClientRect(),a=i.width,s=i.height,u=Math.floor(a),l=Math.floor(s);(n!==u||r!==l)&&Promise.resolve().then(function(){t({width:u,height:l})}),n=u,r=l});return e&&o.observe(e),function(){o.disconnect()}}function ez(e){if("function"!=typeof e)return null;return e()}function eH(e){if("object"!==(0,x.A)(e)||!e)return null;return e}var ej=h().forwardRef(function(e,t){var n=e.children,r=e.disabled,o=e.target,i=e.align,a=e.onAlign,s=e.monitorWindowResize,u=e.monitorBufferTime,l=h().useRef({}),c=h().useRef(),f=h().Children.only(n),p=h().useRef({});p.current.disabled=r,p.current.target=o,p.current.onAlign=a;var d=function(e,t){var n=h().useRef(!1),r=h().useRef(null);function o(){window.clearTimeout(r.current)}return[function i(a){if(n.current&&!0!==a)o(),r.current=window.setTimeout(function(){n.current=!1,i()},t);else{if(!1===e())return;n.current=!0,o(),r.current=window.setTimeout(function(){n.current=!1},t)}},function(){n.current=!1,o()}]}(function(){var e=p.current,t=e.disabled,n=e.target,r=e.onAlign;if(!t&&n){var o,a,s,u,f,d,h,v,m,g,y,b,A=c.current,w=ez(n),E=eH(n);l.current.element=w,l.current.point=E;var x=document.activeElement;return w&&(0,L.A)(w)?b=eR(A,w,i):E&&(u=(s=em.getDocument(A)).defaultView||s.parentWindow,f=em.getWindowScrollLeft(u),d=em.getWindowScrollTop(u),h=em.viewportWidth(u),v=em.viewportHeight(u),m={left:o="pageX"in E?E.pageX:f+E.clientX,top:a="pageY"in E?E.pageY:d+E.clientY,width:0,height:0},g=o>=0&&o<=f+h&&a>=0&&a<=d+v,y=[i.points[0],"cc"],b=eP(A,m,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?eM(n,!0).forEach(function(t){var r;r=n[t],t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):eM(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({},i,{points:y}),g)),x!==document.activeElement&&(0,eI.A)(A,x)&&"function"==typeof x.focus&&x.focus(),r&&b&&r(A,b),!0}return!1},void 0===u?0:u),v=(0,P.A)(d,2),m=v[0],g=v[1],y=h().useRef({cancel:function(){}}),b=h().useRef({cancel:function(){}});h().useEffect(function(){var e=ez(o),t=eH(o);c.current!==b.current.element&&(b.current.cancel(),b.current.element=c.current,b.current.cancel=eL(c.current,m)),(l.current.element!==e||!function(e,t){if(e===t)return!0;if(!e||!t)return!1;if("pageX"in t&&"pageY"in t)return e.pageX===t.pageX&&e.pageY===t.pageY;if("clientX"in t&&"clientY"in t)return e.clientX===t.clientX&&e.clientY===t.clientY;return!1}(l.current.point,t))&&(m(),y.current.element!==e&&(y.current.cancel(),y.current.element=e,y.current.cancel=eL(e,m)))}),h().useEffect(function(){r?g():m()},[r]);var A=h().useRef(null);return h().useEffect(function(){s?A.current||(A.current=(0,e_.A)(window,"resize",m)):A.current&&(A.current.remove(),A.current=null)},[s]),h().useEffect(function(){return function(){y.current.cancel(),b.current.cancel(),A.current&&A.current.remove(),g()}},[]),h().useImperativeHandle(t,function(){return{forceAlign:function(){return m(!0)}}}),h().isValidElement(f)&&(f=h().cloneElement(f,{ref:(0,I.K4)(f.ref,c)})),f});ej.displayName="Align";var eV=T()?d.useLayoutEffect:d.useEffect;function eF(){eF=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function c(t,n,r,i){var a,s,u=Object.create((n&&n.prototype instanceof m?n:m).prototype);return o(u,"_invoke",{value:(a=new N(i||[]),s=p,function(n,o){if(s===d)throw Error("Generator is already running");if(s===h){if("throw"===n)throw o;return{value:e,done:!0}}for(a.method=n,a.arg=o;;){var i=a.delegate;if(i){var u=function t(n,r){var o=r.method,i=n.iterator[o];if(i===e)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=e,t(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),v;var a=f(i,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var s=a.arg;return s?s.done?(r[n.resultName]=s.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):s:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,v)}(i,a);if(u){if(u===v)continue;return u}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(s===p)throw s=h,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=d;var l=f(t,r,a);if("normal"===l.type){if(s=a.done?h:"suspendedYield",l.arg===v)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(s=h,a.method="throw",a.arg=l.arg)}})}),u}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=c;var p="suspendedStart",d="executing",h="completed",v={};function m(){}function g(){}function y(){}var b={};l(b,a,function(){return this});var A=Object.getPrototypeOf,w=A&&A(A(k([])));w&&w!==n&&r.call(w,a)&&(b=w);var E=y.prototype=m.prototype=Object.create(b);function C(e){["next","throw","return"].forEach(function(t){l(e,t,function(e){return this._invoke(t,e)})})}function O(e,t){var n;o(this,"_invoke",{value:function(o,i){function a(){return new t(function(n,a){!function n(o,i,a,s){var u=f(e[o],e,i);if("throw"!==u.type){var l=u.arg,c=l.value;return c&&"object"==(0,x.A)(c)&&r.call(c,"__await")?t.resolve(c.__await).then(function(e){n("next",e,a,s)},function(e){n("throw",e,a,s)}):t.resolve(c).then(function(e){l.value=e,a(l)},function(e){return n("throw",e,a,s)})}s(u.arg)}(o,i,n,a)})}return n=n?n.then(a,a):a()}})}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function k(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw TypeError((0,x.A)(t)+" is not iterable")}return g.prototype=y,o(E,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:g,configurable:!0}),g.displayName=l(y,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,l(e,u,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},C(O.prototype),l(O.prototype,s,function(){return this}),t.AsyncIterator=O,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new O(c(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then(function(e){return e.done?e.value:a.next()})},C(E),l(E,u,"Generator"),l(E,a,function(){return this}),l(E,"toString",function(){return"[object Generator]"}),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=k,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return s.type="throw",s.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(u&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:k(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}var eU=n(48591),eW=["measure","alignPre","align",null,"motion"],eB=d.forwardRef(function(e,t){var n,r,o,s,u=e.visible,l=e.prefixCls,c=e.className,f=e.style,p=e.children,h=e.zIndex,v=e.stretch,m=e.destroyPopupOnHide,g=e.forceRender,y=e.align,b=e.point,A=e.getRootDomNode,E=e.getClassNameFromAlign,x=e.onAlign,C=e.onMouseEnter,O=e.onMouseLeave,T=e.onMouseDown,S=e.onTouchStart,N=e.onClick,R=(0,d.useRef)(),D=(0,d.useRef)(),I=(0,d.useState)(),L=(0,P.A)(I,2),z=L[0],H=L[1],j=(n=d.useState({width:0,height:0}),o=(r=(0,P.A)(n,2))[0],s=r[1],[d.useMemo(function(){var e={};if(v){var t=o.width,n=o.height;-1!==v.indexOf("height")&&n?e.height=n:-1!==v.indexOf("minHeight")&&n&&(e.minHeight=n),-1!==v.indexOf("width")&&t?e.width=t:-1!==v.indexOf("minWidth")&&t&&(e.minWidth=t)}return e},[v,o]),function(e){var t=e.offsetWidth,n=e.offsetHeight,r=e.getBoundingClientRect(),o=r.width,i=r.height;1>Math.abs(t-o)&&1>Math.abs(n-i)&&(t=o,n=i),s({width:t,height:n})}]),V=(0,P.A)(j,2),F=V[0],U=V[1],W=function(e,t){var n,r,o,i,a,s=(n=d.useRef(!1),r=d.useState(null),i=(o=(0,P.A)(r,2))[0],a=o[1],d.useEffect(function(){return n.current=!1,function(){n.current=!0}},[]),[i,function(e,t){if(t&&n.current)return;a(e)}]),u=(0,P.A)(s,2),l=u[0],c=u[1],f=(0,d.useRef)();function p(){w.cancel(f.current)}return(0,d.useEffect)(function(){c("measure",!0)},[e]),(0,d.useEffect)(function(){"measure"===l&&t(),l&&(f.current=w((0,eU.A)(eF().mark(function e(){var t,n;return eF().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=eW.indexOf(l),(n=eW[t+1])&&-1!==t&&c(n,!0);case 3:case"end":return e.stop()}},e)}))))},[l]),(0,d.useEffect)(function(){return function(){p()}},[]),[l,function(e){p(),f.current=w(function(){c(function(e){switch(l){case"align":return"motion";case"motion":return"stable"}return e},!0),null==e||e()})}]}(u,function(){v&&U(A())}),B=(0,P.A)(W,2),K=B[0],G=B[1],Y=(0,d.useState)(0),X=(0,P.A)(Y,2),q=X[0],Q=X[1],Z=(0,d.useRef)();function $(){var e;null===(e=R.current)||void 0===e||e.forceAlign()}function J(e,t){var n=E(t);z!==n&&H(n),Q(function(e){return e+1}),"align"===K&&(null==x||x(e,t))}eV(function(){"alignPre"===K&&Q(0)},[K]),eV(function(){"align"===K&&(q<3?$():G(function(){var e;null===(e=Z.current)||void 0===e||e.call(Z)}))},[q]);var ee=(0,i.A)({},_(e));function et(){return new Promise(function(e){Z.current=e})}["onAppearEnd","onEnterEnd","onLeaveEnd"].forEach(function(e){var t=ee[e];ee[e]=function(e,n){return G(),null==t?void 0:t(e,n)}}),d.useEffect(function(){ee.motionName||"motion"!==K||G()},[ee.motionName,K]),d.useImperativeHandle(t,function(){return{forceAlign:$,getElement:function(){return D.current}}});var en=(0,i.A)((0,i.A)({},F),{},{zIndex:h,opacity:"motion"!==K&&"stable"!==K&&u?0:void 0,pointerEvents:u||"stable"===K?void 0:"none"},f),er=!0;null!=y&&y.points&&("align"===K||"stable"===K)&&(er=!1);var eo=p;return d.Children.count(p)>1&&(eo=d.createElement("div",{className:"".concat(l,"-content")},p)),d.createElement(M.A,(0,a.A)({visible:u,ref:D,leavedClassName:"".concat(l,"-hidden")},ee,{onAppearPrepare:et,onEnterPrepare:et,removeOnLeave:m,forceRender:g}),function(e,t){var n=e.className,r=e.style,o=k()(l,c,z,n);return d.createElement(ej,{target:function(){if(b)return b;return A}(),key:"popup",ref:R,monitorWindowResize:!0,disabled:er,align:y,onAlign:J},d.createElement("div",{ref:t,className:o,onMouseEnter:C,onMouseLeave:O,onMouseDownCapture:T,onTouchStartCapture:S,onClick:N,style:(0,i.A)((0,i.A)({},r),en)},eo))})});eB.displayName="PopupInner";var eK=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.visible,o=e.zIndex,s=e.children,u=e.mobile,l=(u=void 0===u?{}:u).popupClassName,c=u.popupStyle,f=u.popupMotion,p=u.popupRender,h=e.onClick,v=d.useRef();d.useImperativeHandle(t,function(){return{forceAlign:function(){},getElement:function(){return v.current}}});var m=(0,i.A)({zIndex:o},c),g=s;return d.Children.count(s)>1&&(g=d.createElement("div",{className:"".concat(n,"-content")},s)),p&&(g=p(g)),d.createElement(M.A,(0,a.A)({visible:r,ref:v,removeOnLeave:!0},void 0===f?{}:f),function(e,t){var r=e.className,o=e.style,a=k()(n,l,r);return d.createElement("div",{ref:t,className:a,onClick:h,style:(0,i.A)((0,i.A)({},o),m)},g)})});eK.displayName="MobilePopupInner";var eG=["visible","mobile"],eY=d.forwardRef(function(e,t){var n=e.visible,r=e.mobile,o=(0,R.A)(e,eG),s=(0,d.useState)(n),u=(0,P.A)(s,2),l=u[0],c=u[1],f=(0,d.useState)(!1),p=(0,P.A)(f,2),h=p[0],v=p[1],m=(0,i.A)((0,i.A)({},o),{},{visible:l});(0,d.useEffect)(function(){c(n),n&&r&&v(function(){if("undefined"==typeof navigator||"undefined"==typeof window)return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(null==e?void 0:e.substr(0,4))}())},[n,r]);var g=h?d.createElement(eK,(0,a.A)({},m,{mobile:r,ref:t})):d.createElement(eB,(0,a.A)({},m,{ref:t}));return d.createElement("div",null,d.createElement(D,m),g)});eY.displayName="Popup";var eX=d.createContext(null);function eq(){}function eQ(){return""}function eZ(e){if(e)return e.ownerDocument;return window.document}var e$=["onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur","onContextMenu"];let eJ=function(e){var t=function(t){(0,c.A)(r,t);var n=(0,f.A)(r);function r(e){var t,o;return(0,s.A)(this,r),t=n.call(this,e),(0,p.A)((0,l.A)(t),"popupRef",d.createRef()),(0,p.A)((0,l.A)(t),"triggerRef",d.createRef()),(0,p.A)((0,l.A)(t),"portalContainer",void 0),(0,p.A)((0,l.A)(t),"attachId",void 0),(0,p.A)((0,l.A)(t),"clickOutsideHandler",void 0),(0,p.A)((0,l.A)(t),"touchOutsideHandler",void 0),(0,p.A)((0,l.A)(t),"contextMenuOutsideHandler1",void 0),(0,p.A)((0,l.A)(t),"contextMenuOutsideHandler2",void 0),(0,p.A)((0,l.A)(t),"mouseDownTimeout",void 0),(0,p.A)((0,l.A)(t),"focusTime",void 0),(0,p.A)((0,l.A)(t),"preClickTime",void 0),(0,p.A)((0,l.A)(t),"preTouchTime",void 0),(0,p.A)((0,l.A)(t),"delayTimer",void 0),(0,p.A)((0,l.A)(t),"hasPopupMouseDown",void 0),(0,p.A)((0,l.A)(t),"onMouseEnter",function(e){var n=t.props.mouseEnterDelay;t.fireEvents("onMouseEnter",e),t.delaySetPopupVisible(!0,n,n?null:e)}),(0,p.A)((0,l.A)(t),"onMouseMove",function(e){t.fireEvents("onMouseMove",e),t.setPoint(e)}),(0,p.A)((0,l.A)(t),"onMouseLeave",function(e){t.fireEvents("onMouseLeave",e),t.delaySetPopupVisible(!1,t.props.mouseLeaveDelay)}),(0,p.A)((0,l.A)(t),"onPopupMouseEnter",function(){t.clearDelayTimer()}),(0,p.A)((0,l.A)(t),"onPopupMouseLeave",function(e){var n;if(e.relatedTarget&&!e.relatedTarget.setTimeout&&E(null===(n=t.popupRef.current)||void 0===n?void 0:n.getElement(),e.relatedTarget))return;t.delaySetPopupVisible(!1,t.props.mouseLeaveDelay)}),(0,p.A)((0,l.A)(t),"onFocus",function(e){t.fireEvents("onFocus",e),t.clearDelayTimer(),t.isFocusToShow()&&(t.focusTime=Date.now(),t.delaySetPopupVisible(!0,t.props.focusDelay))}),(0,p.A)((0,l.A)(t),"onMouseDown",function(e){t.fireEvents("onMouseDown",e),t.preClickTime=Date.now()}),(0,p.A)((0,l.A)(t),"onTouchStart",function(e){t.fireEvents("onTouchStart",e),t.preTouchTime=Date.now()}),(0,p.A)((0,l.A)(t),"onBlur",function(e){t.fireEvents("onBlur",e),t.clearDelayTimer(),t.isBlurToHide()&&t.delaySetPopupVisible(!1,t.props.blurDelay)}),(0,p.A)((0,l.A)(t),"onContextMenu",function(e){e.preventDefault(),t.fireEvents("onContextMenu",e),t.setPopupVisible(!0,e)}),(0,p.A)((0,l.A)(t),"onContextMenuClose",function(){t.isContextMenuToShow()&&t.close()}),(0,p.A)((0,l.A)(t),"onClick",function(e){if(t.fireEvents("onClick",e),t.focusTime){var n;if(t.preClickTime&&t.preTouchTime?n=Math.min(t.preClickTime,t.preTouchTime):t.preClickTime?n=t.preClickTime:t.preTouchTime&&(n=t.preTouchTime),20>Math.abs(n-t.focusTime))return;t.focusTime=0}t.preClickTime=0,t.preTouchTime=0,t.isClickToShow()&&(t.isClickToHide()||t.isBlurToHide())&&e&&e.preventDefault&&e.preventDefault();var r=!t.state.popupVisible;(t.isClickToHide()&&!r||r&&t.isClickToShow())&&t.setPopupVisible(!t.state.popupVisible,e)}),(0,p.A)((0,l.A)(t),"onPopupMouseDown",function(){if(t.hasPopupMouseDown=!0,clearTimeout(t.mouseDownTimeout),t.mouseDownTimeout=window.setTimeout(function(){t.hasPopupMouseDown=!1},0),t.context){var e;(e=t.context).onPopupMouseDown.apply(e,arguments)}}),(0,p.A)((0,l.A)(t),"onDocumentClick",function(e){if(t.props.mask&&!t.props.maskClosable)return;var n=e.target,r=t.getRootDomNode(),o=t.getPopupDomNode();(!E(r,n)||t.isContextMenuOnly())&&!E(o,n)&&!t.hasPopupMouseDown&&t.close()}),(0,p.A)((0,l.A)(t),"getRootDomNode",function(){var e=t.props.getTriggerDOMNode;if(e)return e(t.triggerRef.current);try{var n=function(e){if(e instanceof HTMLElement)return e;return m().findDOMNode(e)}(t.triggerRef.current);if(n)return n}catch(e){}return m().findDOMNode((0,l.A)(t))}),(0,p.A)((0,l.A)(t),"getPopupClassNameFromAlign",function(e){var n=[],r=t.props,o=r.popupPlacement,i=r.builtinPlacements,a=r.prefixCls,s=r.alignPoint,u=r.getPopupClassNameFromAlign;return o&&i&&n.push(function(e,t,n,r){for(var o=n.points,i=Object.keys(e),a=0;a<i.length;a+=1){var s=i[a];if(function(e,t,n){if(n)return e[0]===t[0];return e[0]===t[0]&&e[1]===t[1]}(e[s].points,o,r))return"".concat(t,"-placement-").concat(s)}return""}(i,a,e,s)),u&&n.push(u(e)),n.join(" ")}),(0,p.A)((0,l.A)(t),"getComponent",function(){var e=t.props,n=e.prefixCls,r=e.destroyPopupOnHide,o=e.popupClassName,i=e.onPopupAlign,s=e.popupMotion,u=e.popupAnimation,l=e.popupTransitionName,c=e.popupStyle,f=e.mask,p=e.maskAnimation,h=e.maskTransitionName,v=e.maskMotion,m=e.zIndex,g=e.popup,y=e.stretch,b=e.alignPoint,A=e.mobile,w=e.forceRender,E=e.onPopupClick,x=t.state,C=x.popupVisible,O=x.point,T=t.getPopupAlign(),S={};return t.isMouseEnterToShow()&&(S.onMouseEnter=t.onPopupMouseEnter),t.isMouseLeaveToHide()&&(S.onMouseLeave=t.onPopupMouseLeave),S.onMouseDown=t.onPopupMouseDown,S.onTouchStart=t.onPopupMouseDown,d.createElement(eY,(0,a.A)({prefixCls:n,destroyPopupOnHide:r,visible:C,point:b&&O,className:o,align:T,onAlign:i,animation:u,getClassNameFromAlign:t.getPopupClassNameFromAlign},S,{stretch:y,getRootDomNode:t.getRootDomNode,style:c,mask:f,zIndex:m,transitionName:l,maskAnimation:p,maskTransitionName:h,maskMotion:v,ref:t.popupRef,motion:s,mobile:A,forceRender:w,onClick:E}),"function"==typeof g?g():g)}),(0,p.A)((0,l.A)(t),"attachParent",function(e){w.cancel(t.attachId);var n,r=t.props,o=r.getPopupContainer,i=r.getDocument,a=t.getRootDomNode();o?(a||0===o.length)&&(n=o(a)):n=i(t.getRootDomNode()).body,n?n.appendChild(e):t.attachId=w(function(){t.attachParent(e)})}),(0,p.A)((0,l.A)(t),"getContainer",function(){if(!t.portalContainer){var e=(0,t.props.getDocument)(t.getRootDomNode()).createElement("div");e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.width="100%",t.portalContainer=e}return t.attachParent(t.portalContainer),t.portalContainer}),(0,p.A)((0,l.A)(t),"setPoint",function(e){if(!t.props.alignPoint||!e)return;t.setState({point:{pageX:e.pageX,pageY:e.pageY}})}),(0,p.A)((0,l.A)(t),"handlePortalUpdate",function(){t.state.prevPopupVisible!==t.state.popupVisible&&t.props.afterPopupVisibleChange(t.state.popupVisible)}),(0,p.A)((0,l.A)(t),"triggerContextValue",{onPopupMouseDown:t.onPopupMouseDown}),o="popupVisible"in e?!!e.popupVisible:!!e.defaultPopupVisible,t.state={prevPopupVisible:o,popupVisible:o},e$.forEach(function(e){t["fire".concat(e)]=function(n){t.fireEvents(e,n)}}),t}return(0,u.A)(r,[{key:"componentDidMount",value:function(){this.componentDidUpdate()}},{key:"componentDidUpdate",value:function(){var e,t=this.props;if(this.state.popupVisible){!this.clickOutsideHandler&&(this.isClickToHide()||this.isContextMenuToShow())&&(e=t.getDocument(this.getRootDomNode()),this.clickOutsideHandler=O(e,"mousedown",this.onDocumentClick)),this.touchOutsideHandler||(e=e||t.getDocument(this.getRootDomNode()),this.touchOutsideHandler=O(e,"touchstart",this.onDocumentClick)),!this.contextMenuOutsideHandler1&&this.isContextMenuToShow()&&(e=e||t.getDocument(this.getRootDomNode()),this.contextMenuOutsideHandler1=O(e,"scroll",this.onContextMenuClose)),!this.contextMenuOutsideHandler2&&this.isContextMenuToShow()&&(this.contextMenuOutsideHandler2=O(window,"blur",this.onContextMenuClose));return}this.clearOutsideHandler()}},{key:"componentWillUnmount",value:function(){this.clearDelayTimer(),this.clearOutsideHandler(),clearTimeout(this.mouseDownTimeout),w.cancel(this.attachId)}},{key:"getPopupDomNode",value:function(){var e;return(null===(e=this.popupRef.current)||void 0===e?void 0:e.getElement())||null}},{key:"getPopupAlign",value:function(){var e,t=this.props,n=t.popupPlacement,r=t.popupAlign,o=t.builtinPlacements;if(n&&o)return e=o[n]||{},(0,i.A)((0,i.A)({},e),r);return r}},{key:"setPopupVisible",value:function(e,t){var n=this.props.alignPoint,r=this.state.popupVisible;this.clearDelayTimer(),r!==e&&("popupVisible"in this.props||this.setState({popupVisible:e,prevPopupVisible:r}),this.props.onPopupVisibleChange(e)),n&&t&&e&&this.setPoint(t)}},{key:"delaySetPopupVisible",value:function(e,t,n){var r=this,o=1e3*t;if(this.clearDelayTimer(),o){var i=n?{pageX:n.pageX,pageY:n.pageY}:null;this.delayTimer=window.setTimeout(function(){r.setPopupVisible(e,i),r.clearDelayTimer()},o)}else this.setPopupVisible(e,n)}},{key:"clearDelayTimer",value:function(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)}},{key:"clearOutsideHandler",value:function(){this.clickOutsideHandler&&(this.clickOutsideHandler.remove(),this.clickOutsideHandler=null),this.contextMenuOutsideHandler1&&(this.contextMenuOutsideHandler1.remove(),this.contextMenuOutsideHandler1=null),this.contextMenuOutsideHandler2&&(this.contextMenuOutsideHandler2.remove(),this.contextMenuOutsideHandler2=null),this.touchOutsideHandler&&(this.touchOutsideHandler.remove(),this.touchOutsideHandler=null)}},{key:"createTwoChains",value:function(e){var t=this.props.children.props,n=this.props;if(t[e]&&n[e])return this["fire".concat(e)];return t[e]||n[e]}},{key:"isClickToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return -1!==t.indexOf("click")||-1!==n.indexOf("click")}},{key:"isContextMenuOnly",value:function(){var e=this.props.action;return"contextMenu"===e||1===e.length&&"contextMenu"===e[0]}},{key:"isContextMenuToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return -1!==t.indexOf("contextMenu")||-1!==n.indexOf("contextMenu")}},{key:"isClickToHide",value:function(){var e=this.props,t=e.action,n=e.hideAction;return -1!==t.indexOf("click")||-1!==n.indexOf("click")}},{key:"isMouseEnterToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return -1!==t.indexOf("hover")||-1!==n.indexOf("mouseEnter")}},{key:"isMouseLeaveToHide",value:function(){var e=this.props,t=e.action,n=e.hideAction;return -1!==t.indexOf("hover")||-1!==n.indexOf("mouseLeave")}},{key:"isFocusToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return -1!==t.indexOf("focus")||-1!==n.indexOf("focus")}},{key:"isBlurToHide",value:function(){var e=this.props,t=e.action,n=e.hideAction;return -1!==t.indexOf("focus")||-1!==n.indexOf("blur")}},{key:"forcePopupAlign",value:function(){if(this.state.popupVisible){var e;null===(e=this.popupRef.current)||void 0===e||e.forceAlign()}}},{key:"fireEvents",value:function(e,t){var n=this.props.children.props[e];n&&n(t);var r=this.props[e];r&&r(t)}},{key:"close",value:function(){this.setPopupVisible(!1)}},{key:"render",value:function(){var t,n=this.state.popupVisible,r=this.props,o=r.children,a=r.forceRender,s=r.alignPoint,u=r.className,l=r.autoDestroy,c=d.Children.only(o),f={key:"trigger"};this.isContextMenuToShow()?f.onContextMenu=this.onContextMenu:f.onContextMenu=this.createTwoChains("onContextMenu"),this.isClickToHide()||this.isClickToShow()?(f.onClick=this.onClick,f.onMouseDown=this.onMouseDown,f.onTouchStart=this.onTouchStart):(f.onClick=this.createTwoChains("onClick"),f.onMouseDown=this.createTwoChains("onMouseDown"),f.onTouchStart=this.createTwoChains("onTouchStart")),this.isMouseEnterToShow()?(f.onMouseEnter=this.onMouseEnter,s&&(f.onMouseMove=this.onMouseMove)):f.onMouseEnter=this.createTwoChains("onMouseEnter"),this.isMouseLeaveToHide()?f.onMouseLeave=this.onMouseLeave:f.onMouseLeave=this.createTwoChains("onMouseLeave"),this.isFocusToShow()||this.isBlurToHide()?(f.onFocus=this.onFocus,f.onBlur=this.onBlur):(f.onFocus=this.createTwoChains("onFocus"),f.onBlur=this.createTwoChains("onBlur"));var p=k()(c&&c.props&&c.props.className,u);p&&(f.className=p);var h=(0,i.A)({},f);(function(e){var t,n,r=(0,C.isMemo)(e)?e.type.type:e.type;if("function"==typeof r&&!(null===(t=r.prototype)||void 0===t?void 0:t.render)||"function"==typeof e&&!(null===(n=e.prototype)||void 0===n?void 0:n.render))return!1;return!0})(c)&&(h.ref=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.filter(function(e){return e});if(r.length<=1)return r[0];return function(e){t.forEach(function(t){"function"==typeof t?t(e):"object"===(0,x.A)(t)&&t&&"current"in t&&(t.current=e)})}}(this.triggerRef,c.ref));var v=d.cloneElement(c,h);return(n||this.popupRef.current||a)&&(t=d.createElement(e,{key:"portal",getContainer:this.getContainer,didUpdate:this.handlePortalUpdate},this.getComponent())),!n&&l&&(t=null),d.createElement(eX.Provider,{value:this.triggerContextValue},v,t)}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.popupVisible,r={};return void 0!==n&&t.popupVisible!==n&&(r.popupVisible=n,r.prevPopupVisible=t.popupVisible),r}}]),r}(d.Component);return(0,p.A)(t,"contextType",eX),(0,p.A)(t,"defaultProps",{prefixCls:"rc-trigger-popup",getPopupClassNameFromAlign:eQ,getDocument:eZ,onPopupVisibleChange:eq,afterPopupVisibleChange:eq,onPopupAlign:eq,popupClassName:"",mouseEnterDelay:0,mouseLeaveDelay:.1,focusDelay:0,blurDelay:.15,popupStyle:{},destroyPopupOnHide:!1,popupAlign:{},defaultPopupVisible:!1,mask:!1,maskClosable:!0,action:[],showAction:[],hideAction:[],autoDestroy:!1}),t}(S)},4303:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(8155),o=n.n(r);function i(e,t,n,r){var i=o().unstable_batchedUpdates?function(e){o().unstable_batchedUpdates(n,e)}:n;return e.addEventListener&&e.addEventListener(t,i,r),{remove:function(){e.removeEventListener&&e.removeEventListener(t,i,r)}}}},82854:(e,t,n)=>{"use strict";function r(e){if(!e)return!1;if(e instanceof HTMLElement&&e.offsetParent)return!0;if(e instanceof SVGGraphicsElement&&e.getBBox){var t=e.getBBox(),n=t.width,r=t.height;if(n||r)return!0}if(e instanceof HTMLElement&&e.getBoundingClientRect){var o=e.getBoundingClientRect(),i=o.width,a=o.height;if(i||a)return!0}return!1}n.d(t,{A:()=>r})},72161:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(e){var t=e.keyCode;if(e.altKey&&!e.ctrlKey||e.metaKey||t>=r.F1&&t<=r.F12)return!1;switch(t){case r.ALT:case r.CAPS_LOCK:case r.CONTEXT_MENU:case r.CTRL:case r.DOWN:case r.END:case r.ESC:case r.HOME:case r.INSERT:case r.LEFT:case r.MAC_FF_META:case r.META:case r.NUMLOCK:case r.NUM_CENTER:case r.PAGE_DOWN:case r.PAGE_UP:case r.PAUSE:case r.PRINT_SCREEN:case r.RIGHT:case r.SHIFT:case r.UP:case r.WIN_KEY:case r.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(e){if(e>=r.ZERO&&e<=r.NINE||e>=r.NUM_ZERO&&e<=r.NUM_MULTIPLY||e>=r.A&&e<=r.Z||-1!==window.navigator.userAgent.indexOf("WebKit")&&0===e)return!0;switch(e){case r.SPACE:case r.QUESTION_MARK:case r.NUM_PLUS:case r.NUM_MINUS:case r.NUM_PERIOD:case r.NUM_DIVISION:case r.SEMICOLON:case r.DASH:case r.EQUALS:case r.COMMA:case r.PERIOD:case r.SLASH:case r.APOSTROPHE:case r.SINGLE_QUOTE:case r.OPEN_SQUARE_BRACKET:case r.BACKSLASH:case r.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}};let o=r},45434:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var r,o=n(19721),i=n(65848);function a(e){var t=i.useRef();return t.current=e,i.useCallback(function(){for(var e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))},[])}var s=(0,n(59339).A)()?i.useLayoutEffect:i.useEffect,u=function(e,t){var n=i.useRef(!0);s(function(){if(!n.current)return e()},t),s(function(){return n.current=!1,function(){n.current=!0}},[])};function l(e){return void 0!==e}function c(e,t){var n,c,f,p,d,h,v=t||{},m=v.defaultValue,g=v.value,y=v.onChange,b=v.postState,A=(n=function(){var t,n=void 0;return l(g)?(n=g,t=r.PROP):l(m)?(n="function"==typeof m?m():m,t=r.PROP):(n="function"==typeof e?e():e,t=r.INNER),[n,t,n]},c=i.useRef(!1),f=i.useState(n),d=(p=(0,o.A)(f,2))[0],h=p[1],i.useEffect(function(){return c.current=!1,function(){c.current=!0}},[]),[d,function(e,t){if(t&&c.current)return;h(e)}]),w=(0,o.A)(A,2),E=w[0],x=w[1],C=l(g)?g:E[0],O=b?b(C):C;u(function(){x(function(e){var t=(0,o.A)(e,1)[0];return[g,r.PROP,t]})},[g]);var T=i.useRef(),S=a(function(e,t){x(function(t){var n=(0,o.A)(t,3),i=n[0],a=n[1],s=n[2],u="function"==typeof e?e(i):e;if(u===i)return t;var l=a===r.INNER&&T.current!==s?s:i;return[u,r.INNER,l]},t)}),N=a(y);return s(function(){var e=(0,o.A)(E,3),t=e[0],n=e[1],i=e[2];t!==i&&n===r.INNER&&(N(t,i),T.current=i)},[E]),[O,S]}!function(e){e[e.INNER=0]="INNER",e[e.PROP=1]="PROP"}(r||(r={}))},84790:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(63639);function o(e,t){var n=(0,r.A)({},e);return Array.isArray(t)&&t.forEach(function(e){delete n[e]}),n}},26420:(e,t,n)=>{"use strict";n.d(t,{A:()=>E});var r=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some(function(e,r){if(e[0]===t)return n=r,!0;return!1}),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];e.call(t,o[1],o[0])}},t}()}(),o="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,i=function(){if(void 0!==n.g&&n.g.Math===Math)return n.g;if("undefined"!=typeof self&&self.Math===Math)return self;if("undefined"!=typeof window&&window.Math===Math)return window;return Function("return this")()}(),a=function(){if("function"==typeof requestAnimationFrame)return requestAnimationFrame.bind(i);return function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),s=["top","right","bottom","left","width","height","size","weight"],u="undefined"!=typeof MutationObserver,l=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,r=!1,o=0;function i(){n&&(n=!1,e()),r&&u()}function s(){a(i)}function u(){var e=Date.now();if(n){if(e-o<2)return;r=!0}else n=!0,r=!1,setTimeout(s,20);o=e}return u}(this.refresh.bind(this),0)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){if(!o||this.connected_)return;document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),u?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0},e.prototype.disconnect_=function(){if(!o||!this.connected_)return;document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;s.some(function(e){return!!~n.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),c=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},f=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||i},p=m(0,0,0,0);function d(e){return parseFloat(e)||0}function h(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(t,n){return t+d(e["border-"+n+"-width"])},0)}var v=function(){if("undefined"!=typeof SVGGraphicsElement)return function(e){return e instanceof f(e).SVGGraphicsElement};return function(e){return e instanceof f(e).SVGElement&&"function"==typeof e.getBBox}}();function m(e,t,n,r){return{x:e,y:t,width:n,height:r}}var g=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=m(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=function(e){if(!o)return p;if(v(e)){var t;return m(0,0,(t=e.getBBox()).width,t.height)}return function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return p;var r=f(e).getComputedStyle(e),o=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var o=r[n],i=e["padding-"+o];t[o]=d(i)}return t}(r),i=o.left+o.right,a=o.top+o.bottom,s=d(r.width),u=d(r.height);if("border-box"===r.boxSizing&&(Math.round(s+i)!==t&&(s-=h(r,"left","right")+i),Math.round(u+a)!==n&&(u-=h(r,"top","bottom")+a)),e!==f(e).document.documentElement){var l=Math.round(s+i)-t,c=Math.round(u+a)-n;1!==Math.abs(l)&&(s-=l),1!==Math.abs(c)&&(u-=c)}return m(o.left,o.top,s,u)}(e)}(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),y=function(e,t){var n,r,o,i,a,s=(n=t.x,r=t.y,o=t.width,i=t.height,c(a=Object.create(("undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object).prototype),{x:n,y:r,width:o,height:i,top:r,right:n+o,bottom:i+r,left:n}),a);c(this,{target:e,contentRect:s})},b=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new r,"function"!=typeof e)throw TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"==typeof Element||!(Element instanceof Object))return;if(!(e instanceof f(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;if(t.has(e))return;t.set(e,new g(e)),this.controller_.addObserver(this),this.controller_.refresh()},e.prototype.unobserve=function(e){if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");if("undefined"==typeof Element||!(Element instanceof Object))return;if(!(e instanceof f(e).Element))throw TypeError('parameter 1 is not of type "Element".');var t=this.observations_;if(!t.has(e))return;t.delete(e),t.size||this.controller_.removeObserver(this)},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(!this.hasActive())return;var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new y(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),A="undefined"!=typeof WeakMap?new WeakMap:new r,w=function e(t){if(!(this instanceof e))throw TypeError("Cannot call a class as a function.");if(!arguments.length)throw TypeError("1 argument required, but only 0 present.");var n=new b(t,l.getInstance(),this);A.set(this,n)};["observe","unobserve","disconnect"].forEach(function(e){w.prototype[e]=function(){var t;return(t=A.get(this))[e].apply(t,arguments)}});let E=function(){if(void 0!==i.ResizeObserver)return i.ResizeObserver;return w}()},78675:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},90916:(e,t,n)=>{"use strict";n.d(t,{C:()=>o,w:()=>i});var r=n(85852),o=(0,r.P)("success","processing","error","default","warning"),i=(0,r.P)("pink","red","yellow","orange","cyan","green","blue","purple","geekblue","magenta","volcano","gold","lime")},48406:(e,t,n)=>{"use strict";n.d(t,{A:()=>s,b:()=>a});var r=function(){return{height:0,opacity:0}},o=function(e){return{height:e.scrollHeight,opacity:1}},i=function(e,t){return(null==t?void 0:t.deadline)===!0||"height"===t.propertyName},a=function(e,t,n){if(void 0!==n)return n;return"".concat(e,"-").concat(t)};let s={motionName:"ant-motion-collapse",onAppearStart:r,onEnterStart:r,onAppearActive:o,onEnterActive:o,onLeaveStart:function(e){return{height:e.offsetHeight}},onLeaveActive:r,onAppearEnd:i,onEnterEnd:i,onLeaveEnd:i,motionDeadline:500}},15003:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5730),o=0,i={};function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=o++,a=t;return i[n]=(0,r.A)(function t(){(a-=1)<=0?(e(),delete i[n]):i[n]=(0,r.A)(t)}),n}a.cancel=function(e){if(void 0===e)return;r.A.cancel(i[e]),delete i[e]},a.ids=i},21689:(e,t,n)=>{"use strict";n.d(t,{Ob:()=>a,fx:()=>i,zO:()=>o});var r=n(65848),o=r.isValidElement;function i(e,t,n){if(!o(e))return t;return r.cloneElement(e,"function"==typeof n?n(e.props||{}):n)}function a(e,t){return i(e,e,t)}},93900:(e,t,n)=>{"use strict";n.d(t,{Fq:()=>a,Pu:()=>s,qz:()=>i});var r,o=n(59339),i=function(){return(0,o.A)()&&window.document.documentElement},a=function(e){if(i()){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some(function(e){return e in n.style})}return!1},s=function(){if(!i())return!1;if(void 0!==r)return r;var e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="column",e.style.rowGap="1px",e.appendChild(document.createElement("div")),e.appendChild(document.createElement("div")),document.body.appendChild(e),r=1===e.scrollHeight,document.body.removeChild(e),r}},85852:(e,t,n)=>{"use strict";n.d(t,{P:()=>r,p:()=>o});var r=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t},o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t}},81923:(e,t,n)=>{"use strict";n.d(t,{A:()=>y,O:()=>m});var r=n(17015),o=n(63257),i=n(92401),a=n(30756),s=n(93254),u=n(65848),l=n(88608),c=n.n(l),f=n(22292),p=n(85852),d=n(6895),h=n(21689),v=(0,p.P)("text","input");function m(e){return!!(e.prefix||e.suffix||e.allowClear)}function g(e){return!!(e.addonBefore||e.addonAfter)}let y=function(e){(0,a.A)(n,e);var t=(0,s.A)(n);function n(){var e;return(0,o.A)(this,n),e=t.apply(this,arguments),e.containerRef=u.createRef(),e.onInputMouseUp=function(t){var n;if(null===(n=e.containerRef.current)||void 0===n?void 0:n.contains(t.target)){var r=e.props.triggerFocus;null==r||r()}},e}return(0,i.A)(n,[{key:"renderClearIcon",value:function(e){var t=this.props,n=t.allowClear,o=t.value,i=t.disabled,a=t.readOnly,s=t.handleReset;if(!n)return null;var l="".concat(e,"-clear-icon");return u.createElement(f.A,{onClick:s,onMouseDown:function(e){return e.preventDefault()},className:c()((0,r.A)({},"".concat(l,"-hidden"),!(!i&&!a&&o)),l),role:"button"})}},{key:"renderSuffix",value:function(e){var t=this.props,n=t.suffix,r=t.allowClear;if(n||r)return u.createElement("span",{className:"".concat(e,"-suffix")},this.renderClearIcon(e),n);return null}},{key:"renderLabeledIcon",value:function(e,t){var n,o=this.props,i=o.focused,a=o.value,s=o.prefix,l=o.className,f=o.size,p=o.suffix,v=o.disabled,y=o.allowClear,b=o.direction,A=o.style,w=o.readOnly,E=o.bordered,x=this.renderSuffix(e);if(!m(this.props))return(0,h.Ob)(t,{value:a});var C=s?u.createElement("span",{className:"".concat(e,"-prefix")},s):null,O=c()("".concat(e,"-affix-wrapper"),(n={},(0,r.A)(n,"".concat(e,"-affix-wrapper-focused"),i),(0,r.A)(n,"".concat(e,"-affix-wrapper-disabled"),v),(0,r.A)(n,"".concat(e,"-affix-wrapper-sm"),"small"===f),(0,r.A)(n,"".concat(e,"-affix-wrapper-lg"),"large"===f),(0,r.A)(n,"".concat(e,"-affix-wrapper-input-with-clear-btn"),p&&y&&a),(0,r.A)(n,"".concat(e,"-affix-wrapper-rtl"),"rtl"===b),(0,r.A)(n,"".concat(e,"-affix-wrapper-readonly"),w),(0,r.A)(n,"".concat(e,"-affix-wrapper-borderless"),!E),(0,r.A)(n,"".concat(l),!g(this.props)&&l),n));return u.createElement("span",{ref:this.containerRef,className:O,style:A,onMouseUp:this.onInputMouseUp},C,(0,h.Ob)(t,{style:null,value:a,className:(0,d.KO)(e,E,f,v)}),x)}},{key:"renderInputWithLabel",value:function(e,t){var n,o=this.props,i=o.addonBefore,a=o.addonAfter,s=o.style,l=o.size,f=o.className,p=o.direction;if(!g(this.props))return t;var d="".concat(e,"-group"),v="".concat(d,"-addon"),m=i?u.createElement("span",{className:v},i):null,y=a?u.createElement("span",{className:v},a):null,b=c()("".concat(e,"-wrapper"),d,(0,r.A)({},"".concat(d,"-rtl"),"rtl"===p)),A=c()("".concat(e,"-group-wrapper"),(n={},(0,r.A)(n,"".concat(e,"-group-wrapper-sm"),"small"===l),(0,r.A)(n,"".concat(e,"-group-wrapper-lg"),"large"===l),(0,r.A)(n,"".concat(e,"-group-wrapper-rtl"),"rtl"===p),n),f);return u.createElement("span",{className:A,style:s},u.createElement("span",{className:b},m,(0,h.Ob)(t,{style:null}),y))}},{key:"renderTextAreaWithClearIcon",value:function(e,t){var n,o=this.props,i=o.value,a=o.allowClear,s=o.className,l=o.style,f=o.direction,p=o.bordered;if(!a)return(0,h.Ob)(t,{value:i});var d=c()("".concat(e,"-affix-wrapper"),"".concat(e,"-affix-wrapper-textarea-with-clear-btn"),(n={},(0,r.A)(n,"".concat(e,"-affix-wrapper-rtl"),"rtl"===f),(0,r.A)(n,"".concat(e,"-affix-wrapper-borderless"),!p),(0,r.A)(n,"".concat(s),!g(this.props)&&s),n));return u.createElement("span",{className:d,style:l},(0,h.Ob)(t,{style:null,value:i}),this.renderClearIcon(e))}},{key:"render",value:function(){var e=this.props,t=e.prefixCls,n=e.inputType,r=e.element;if(n===v[0])return this.renderTextAreaWithClearIcon(t,r);return this.renderInputWithLabel(t,this.renderLabeledIcon(t,r))}}]),n}(u.Component)},6895:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>E,F4:()=>A,KO:()=>b,gS:()=>y,pt:()=>g});var r=n(47148),o=n(63257),i=n(92401),a=n(30756),s=n(93254),u=n(17015),l=n(65848),c=n(88608),f=n.n(c),p=n(84790),d=n(81923),h=n(75046),v=n(74571),m=n(39717);function g(e){if(null==e)return"";return e}function y(e,t,n,r){if(!n)return;var o=t,i=e.value;if("click"===t.type){(o=Object.create(t)).target=e,o.currentTarget=e,e.value="",n(o),e.value=i;return}if(void 0!==r){(o=Object.create(t)).target=e,o.currentTarget=e,e.value=r,n(o);return}n(o)}function b(e,t,n,r,o){var i;return f()(e,(i={},(0,u.A)(i,"".concat(e,"-sm"),"small"===n),(0,u.A)(i,"".concat(e,"-lg"),"large"===n),(0,u.A)(i,"".concat(e,"-disabled"),r),(0,u.A)(i,"".concat(e,"-rtl"),"rtl"===o),(0,u.A)(i,"".concat(e,"-borderless"),!t),i))}function A(e,t){if(!e)return;e.focus(t);var n=(t||{}).cursor;if(n){var r=e.value.length;switch(n){case"start":e.setSelectionRange(0,0);break;case"end":e.setSelectionRange(r,r);break;default:e.setSelectionRange(0,r)}}}var w=function(e){(0,a.A)(n,e);var t=(0,s.A)(n);function n(e){(0,o.A)(this,n),(i=t.call(this,e)).direction="ltr",i.focus=function(e){A(i.input,e)},i.saveClearableInput=function(e){i.clearableInput=e},i.saveInput=function(e){i.input=e},i.onFocus=function(e){var t=i.props.onFocus;i.setState({focused:!0},i.clearPasswordValueAttribute),null==t||t(e)},i.onBlur=function(e){var t=i.props.onBlur;i.setState({focused:!1},i.clearPasswordValueAttribute),null==t||t(e)},i.handleReset=function(e){i.setValue("",function(){i.focus()}),y(i.input,e,i.props.onChange)},i.renderInput=function(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=i.props,s=a.className,c=a.addonBefore,d=a.addonAfter,h=a.size,v=a.disabled,m=(0,p.A)(i.props,["prefixCls","onPressEnter","addonBefore","addonAfter","prefix","suffix","allowClear","defaultValue","size","inputType","bordered"]);return l.createElement("input",(0,r.A)({autoComplete:o.autoComplete},m,{onChange:i.handleChange,onFocus:i.onFocus,onBlur:i.onBlur,onKeyDown:i.handleKeyDown,className:f()(b(e,n,h||t,v,i.direction),(0,u.A)({},s,s&&!c&&!d)),ref:i.saveInput}))},i.clearPasswordValueAttribute=function(){i.removePasswordTimeout=setTimeout(function(){i.input&&"password"===i.input.getAttribute("type")&&i.input.hasAttribute("value")&&i.input.removeAttribute("value")})},i.handleChange=function(e){i.setValue(e.target.value,i.clearPasswordValueAttribute),y(i.input,e,i.props.onChange)},i.handleKeyDown=function(e){var t=i.props,n=t.onPressEnter,r=t.onKeyDown;n&&13===e.keyCode&&n(e),null==r||r(e)},i.renderComponent=function(e){var t=e.getPrefixCls,n=e.direction,o=e.input,a=i.state,s=a.value,u=a.focused,c=i.props,f=c.prefixCls,p=c.bordered,h=void 0===p||p,m=t("input",f);return i.direction=n,l.createElement(v.A.Consumer,null,function(e){return l.createElement(d.A,(0,r.A)({size:e},i.props,{prefixCls:m,inputType:"input",value:g(s),element:i.renderInput(m,e,h,o),handleReset:i.handleReset,ref:i.saveClearableInput,direction:n,focused:u,triggerFocus:i.focus,bordered:h}))})};var i,a=void 0===e.value?e.defaultValue:e.value;return i.state={value:a,focused:!1,prevValue:e.value},i}return(0,i.A)(n,[{key:"componentDidMount",value:function(){this.clearPasswordValueAttribute()}},{key:"componentDidUpdate",value:function(){}},{key:"getSnapshotBeforeUpdate",value:function(e){return(0,d.O)(e)!==(0,d.O)(this.props)&&(0,m.A)(this.input!==document.activeElement,"Input","When Input is focused, dynamic add or remove prefix / suffix will make it lose focus caused by dom structure change. Read more: https://ant.design/components/input/#FAQ"),null}},{key:"componentWillUnmount",value:function(){this.removePasswordTimeout&&clearTimeout(this.removePasswordTimeout)}},{key:"blur",value:function(){this.input.blur()}},{key:"setSelectionRange",value:function(e,t,n){this.input.setSelectionRange(e,t,n)}},{key:"select",value:function(){this.input.select()}},{key:"setValue",value:function(e,t){void 0===this.props.value?this.setState({value:e},t):null==t||t()}},{key:"render",value:function(){return l.createElement(h.TG,null,this.renderComponent)}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevValue,r={prevValue:e.value};return(void 0!==e.value||n!==e.value)&&(r.value=e.value),r}}]),n}(l.Component);w.defaultProps={type:"text"};let E=w},71217:(e,t,n)=>{"use strict";n.d(t,{A:()=>F});var r,o,i=n(90240),a=n(47148),s=n(17015),u=n(19721),l=n(21444),c=n(65848),f=n(63257),p=n(92401),d=n(30756),h=n(83802),v=n(13814),m=n(63639),g=n(93254),y=n(72401),b=n(48973),A=n(64011),w=n(48550),E=n(26420),x=function(e){(0,d.A)(n,e);var t=(0,g.A)(n);function n(){var e;return(0,f.A)(this,n),e=t.apply(this,arguments),e.resizeObserver=null,e.childNode=null,e.currentElement=null,e.state={width:0,height:0,offsetHeight:0,offsetWidth:0},e.onResize=function(t){var n=e.props.onResize,r=t[0].target,o=r.getBoundingClientRect(),i=o.width,a=o.height,s=r.offsetWidth,u=r.offsetHeight,l=Math.floor(i),c=Math.floor(a);if(e.state.width!==l||e.state.height!==c||e.state.offsetWidth!==s||e.state.offsetHeight!==u){var f={width:l,height:c,offsetWidth:s,offsetHeight:u};e.setState(f),n&&Promise.resolve().then(function(){n((0,m.A)((0,m.A)({},f),{},{offsetWidth:s,offsetHeight:u}))})}},e.setChildNode=function(t){e.childNode=t},e}return(0,p.A)(n,[{key:"componentDidMount",value:function(){this.onComponentUpdated()}},{key:"componentDidUpdate",value:function(){this.onComponentUpdated()}},{key:"componentWillUnmount",value:function(){this.destroyObserver()}},{key:"onComponentUpdated",value:function(){if(this.props.disabled){this.destroyObserver();return}var e=(0,y.A)(this.childNode||this);e!==this.currentElement&&(this.destroyObserver(),this.currentElement=e),!this.resizeObserver&&e&&(this.resizeObserver=new E.A(this.onResize),this.resizeObserver.observe(e))}},{key:"destroyObserver",value:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)}},{key:"render",value:function(){var e=this.props.children,t=(0,b.A)(e);if(t.length>1)(0,A.Ay)(!1,"Find more than one child node with `children` in ResizeObserver. Will only observe first one.");else if(0===t.length)return(0,A.Ay)(!1,"`children` of ResizeObserver is empty. Nothing is in observe."),null;var n=t[0];if(c.isValidElement(n)&&(0,w.f3)(n)){var r=n.ref;t[0]=c.cloneElement(n,{ref:(0,w.K4)(r,this.setChildNode)})}return 1===t.length?t[0]:t.map(function(e,t){if(!c.isValidElement(e)||"key"in e&&null!==e.key)return e;return c.cloneElement(e,{key:"".concat("rc-observer-key","-").concat(t)})})}}]),n}(c.Component);x.displayName="ResizeObserver";let C=function(e,t){for(var n=Object.assign({},e),r=0;r<t.length;r+=1){var o=t[r];delete n[o]}return n};var O=n(88608),T=n.n(O),S=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"],N={};function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach(function(t){(0,s.A)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}!function(e){e[e.NONE=0]="NONE",e[e.RESIZING=1]="RESIZING",e[e.RESIZED=2]="RESIZED"}(o||(o={}));var R=function(e){(0,d.A)(i,e);var t,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}(),function(){var e,n=(0,v.A)(i);return e=t?Reflect.construct(n,arguments,(0,v.A)(this).constructor):n.apply(this,arguments),(0,h.A)(this,e)});function i(e){var t;return(0,f.A)(this,i),(t=n.call(this,e)).saveTextArea=function(e){t.textArea=e},t.handleResize=function(e){var n=t.state.resizeStatus,r=t.props,i=r.autoSize,a=r.onResize;if(n!==o.NONE)return;"function"==typeof a&&a(e),i&&t.resizeOnNextFrame()},t.resizeOnNextFrame=function(){cancelAnimationFrame(t.nextFrameActionId),t.nextFrameActionId=requestAnimationFrame(t.resizeTextarea)},t.resizeTextarea=function(){var e=t.props.autoSize;if(!e||!t.textArea)return;var n=e.minRows,i=e.maxRows,a=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;r||((r=document.createElement("textarea")).setAttribute("tab-index","-1"),r.setAttribute("aria-hidden","true"),document.body.appendChild(r)),e.getAttribute("wrap")?r.setAttribute("wrap",e.getAttribute("wrap")):r.removeAttribute("wrap");var a=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&N[n])return N[n];var r=window.getComputedStyle(e),o=r.getPropertyValue("box-sizing")||r.getPropertyValue("-moz-box-sizing")||r.getPropertyValue("-webkit-box-sizing"),i=parseFloat(r.getPropertyValue("padding-bottom"))+parseFloat(r.getPropertyValue("padding-top")),a=parseFloat(r.getPropertyValue("border-bottom-width"))+parseFloat(r.getPropertyValue("border-top-width")),s={sizingStyle:S.map(function(e){return"".concat(e,":").concat(r.getPropertyValue(e))}).join(";"),paddingSize:i,borderSize:a,boxSizing:o};return t&&n&&(N[n]=s),s}(e,n),s=a.paddingSize,u=a.borderSize,l=a.boxSizing,c=a.sizingStyle;r.setAttribute("style","".concat(c,";").concat("\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important\n")),r.value=e.value||e.placeholder||"";var f=Number.MIN_SAFE_INTEGER,p=Number.MAX_SAFE_INTEGER,d=r.scrollHeight;if("border-box"===l?d+=u:"content-box"===l&&(d-=s),null!==o||null!==i){r.value=" ";var h=r.scrollHeight-s;null!==o&&(f=h*o,"border-box"===l&&(f=f+s+u),d=Math.max(f,d)),null!==i&&(p=h*i,"border-box"===l&&(p=p+s+u),t=d>p?"":"hidden",d=Math.min(p,d))}return{height:d,minHeight:f,maxHeight:p,overflowY:t}}(t.textArea,!1,n,i);t.setState({textareaStyles:a,resizeStatus:o.RESIZING},function(){cancelAnimationFrame(t.resizeFrameId),t.resizeFrameId=requestAnimationFrame(function(){t.setState({resizeStatus:o.RESIZED},function(){t.resizeFrameId=requestAnimationFrame(function(){t.setState({resizeStatus:o.NONE}),t.fixFirefoxAutoScroll()})})})})},t.renderTextArea=function(){var e=t.props,n=e.prefixCls,r=void 0===n?"rc-textarea":n,i=e.autoSize,a=e.onResize,u=e.className,l=e.disabled,f=t.state,p=f.textareaStyles,d=f.resizeStatus,h=C(t.props,["prefixCls","onPressEnter","autoSize","defaultValue","onResize"]),v=T()(r,u,(0,s.A)({},"".concat(r,"-disabled"),l));"value"in h&&(h.value=h.value||"");var m=P(P(P({},t.props.style),p),d===o.RESIZING?{overflowX:"hidden",overflowY:"hidden"}:null);return c.createElement(x,{onResize:t.handleResize,disabled:!(i||a)},c.createElement("textarea",Object.assign({},h,{className:v,style:m,ref:t.saveTextArea})))},t.state={textareaStyles:{},resizeStatus:o.NONE},t}return(0,p.A)(i,[{key:"componentDidMount",value:function(){this.resizeTextarea()}},{key:"componentDidUpdate",value:function(e){e.value!==this.props.value&&this.resizeTextarea()}},{key:"componentWillUnmount",value:function(){cancelAnimationFrame(this.nextFrameActionId),cancelAnimationFrame(this.resizeFrameId)}},{key:"fixFirefoxAutoScroll",value:function(){try{if(document.activeElement===this.textArea){var e=this.textArea.selectionStart,t=this.textArea.selectionEnd;this.textArea.setSelectionRange(e,t)}}catch(e){}}},{key:"render",value:function(){return this.renderTextArea()}}]),i}(c.Component),M=function(e){(0,d.A)(r,e);var t,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}(),function(){var e,n=(0,v.A)(r);return e=t?Reflect.construct(n,arguments,(0,v.A)(this).constructor):n.apply(this,arguments),(0,h.A)(this,e)});function r(e){(0,f.A)(this,r),(t=n.call(this,e)).focus=function(){t.resizableTextArea.textArea.focus()},t.saveTextArea=function(e){t.resizableTextArea=e},t.handleChange=function(e){var n=t.props.onChange;t.setValue(e.target.value,function(){t.resizableTextArea.resizeTextarea()}),n&&n(e)},t.handleKeyDown=function(e){var n=t.props,r=n.onPressEnter,o=n.onKeyDown;13===e.keyCode&&r&&r(e),o&&o(e)};var t,o=void 0===e.value||null===e.value?e.defaultValue:e.value;return t.state={value:o},t}return(0,p.A)(r,[{key:"setValue",value:function(e,t){"value"in this.props||this.setState({value:e},t)}},{key:"blur",value:function(){this.resizableTextArea.textArea.blur()}},{key:"render",value:function(){return c.createElement(R,Object.assign({},this.props,{value:this.state.value,onKeyDown:this.handleKeyDown,onChange:this.handleChange,ref:this.saveTextArea}))}}],[{key:"getDerivedStateFromProps",value:function(e){if("value"in e)return{value:e.value};return null}}]),r}(c.Component),_=n(84790),D=n(45434),I=n(81923),L=n(75046),z=n(6895),H=n(74571),j=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function V(e,t){return(0,l.A)(e||"").slice(0,t).join("")}let F=c.forwardRef(function(e,t){var n,r=e.prefixCls,o=e.bordered,f=void 0===o||o,p=e.showCount,d=void 0!==p&&p,h=e.maxLength,v=e.className,m=e.style,g=e.size,y=e.onCompositionStart,b=e.onCompositionEnd,A=e.onChange,w=j(e,["prefixCls","bordered","showCount","maxLength","className","style","size","onCompositionStart","onCompositionEnd","onChange"]),E=c.useContext(L.QO),x=E.getPrefixCls,C=E.direction,O=c.useContext(H.A),S=c.useRef(null),N=c.useRef(null),k=c.useState(!1),P=(0,u.A)(k,2),R=P[0],F=P[1],U=(0,D.A)(w.defaultValue,{value:w.value}),W=(0,u.A)(U,2),B=W[0],K=W[1],G=function(e,t){void 0===w.value&&(K(e),null==t||t())},Y=Number(h)>0,X=x("input",r);c.useImperativeHandle(t,function(){var e;return{resizableTextArea:null===(e=S.current)||void 0===e?void 0:e.resizableTextArea,focus:function(e){var t,n;(0,z.F4)(null===(n=null===(t=S.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e)},blur:function(){var e;return null===(e=S.current)||void 0===e?void 0:e.blur()}}});var q=c.createElement(M,(0,a.A)({},(0,_.A)(w,["allowClear"]),{className:T()((n={},(0,s.A)(n,"".concat(X,"-borderless"),!f),(0,s.A)(n,v,v&&!d),(0,s.A)(n,"".concat(X,"-sm"),"small"===O||"small"===g),(0,s.A)(n,"".concat(X,"-lg"),"large"===O||"large"===g),n)),style:d?void 0:m,prefixCls:X,onCompositionStart:function(e){F(!0),null==y||y(e)},onChange:function(e){var t=e.target.value;!R&&Y&&(t=V(t,h)),G(t),(0,z.gS)(e.currentTarget,e,A,t)},onCompositionEnd:function(e){F(!1);var t=e.currentTarget.value;Y&&(t=V(t,h)),t!==B&&(G(t),(0,z.gS)(e.currentTarget,e,A,t)),null==b||b(e)},ref:S})),Q=(0,z.pt)(B);!R&&Y&&(null===w.value||void 0===w.value)&&(Q=V(Q,h));var Z=c.createElement(I.A,(0,a.A)({},w,{prefixCls:X,direction:C,inputType:"text",value:Q,element:q,handleReset:function(e){var t,n;G("",function(){var e;null===(e=S.current)||void 0===e||e.focus()}),(0,z.gS)(null===(n=null===(t=S.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e,A)},ref:N,bordered:f,style:d?void 0:m}));if(d){var $=(0,l.A)(Q).length,J="";return J="object"===(0,i.A)(d)?d.formatter({count:$,maxLength:h}):"".concat($).concat(Y?" / ".concat(h):""),c.createElement("div",{className:T()("".concat(X,"-textarea"),(0,s.A)({},"".concat(X,"-textarea-rtl"),"rtl"===C),"".concat(X,"-textarea-show-count"),v),style:m,"data-count":J},Z)}return Z})},59859:(e,t,n)=>{"use strict";n.d(t,{A:()=>R});var r=n(17015),o=n(19721),i=n(47148),a=n(65848),s=n(90240),u=n(63639),l=n(44795),c=n(13845),f={adjustX:1,adjustY:1},p=[0,0],d={left:{points:["cr","cl"],overflow:f,offset:[-4,0],targetOffset:p},right:{points:["cl","cr"],overflow:f,offset:[4,0],targetOffset:p},top:{points:["bc","tc"],overflow:f,offset:[0,-4],targetOffset:p},bottom:{points:["tc","bc"],overflow:f,offset:[0,4],targetOffset:p},topLeft:{points:["bl","tl"],overflow:f,offset:[0,-4],targetOffset:p},leftTop:{points:["tr","tl"],overflow:f,offset:[-4,0],targetOffset:p},topRight:{points:["br","tr"],overflow:f,offset:[0,-4],targetOffset:p},rightTop:{points:["tl","tr"],overflow:f,offset:[4,0],targetOffset:p},bottomRight:{points:["tr","br"],overflow:f,offset:[0,4],targetOffset:p},rightBottom:{points:["bl","br"],overflow:f,offset:[4,0],targetOffset:p},bottomLeft:{points:["tl","bl"],overflow:f,offset:[0,4],targetOffset:p},leftBottom:{points:["br","bl"],overflow:f,offset:[-4,0],targetOffset:p}};let h=function(e){var t=e.overlay,n=e.prefixCls,r=e.id,o=e.overlayInnerStyle;return a.createElement("div",{className:"".concat(n,"-inner"),id:r,role:"tooltip",style:o},"function"==typeof t?t():t)},v=(0,a.forwardRef)(function(e,t){var n=e.overlayClassName,r=e.trigger,o=e.mouseEnterDelay,f=e.mouseLeaveDelay,p=e.overlayStyle,v=e.prefixCls,m=void 0===v?"rc-tooltip":v,g=e.children,y=e.onVisibleChange,b=e.afterVisibleChange,A=e.transitionName,w=e.animation,E=e.motion,x=e.placement,C=e.align,O=e.destroyTooltipOnHide,T=void 0!==O&&O,S=e.defaultVisible,N=e.getTooltipContainer,k=e.overlayInnerStyle,P=(0,l.A)(e,["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle"]),R=(0,a.useRef)(null);(0,a.useImperativeHandle)(t,function(){return R.current});var M=(0,u.A)({},P);"visible"in e&&(M.popupVisible=e.visible);var _=!1,D=!1;if("boolean"==typeof T)_=T;else if(T&&"object"===(0,s.A)(T)){var I=T.keepParent;_=!0===I,D=!1===I}return a.createElement(c.A,(0,i.A)({popupClassName:n,prefixCls:m,popup:function(){var t=e.arrowContent,n=e.overlay,r=e.id;return[a.createElement("div",{className:"".concat(m,"-arrow"),key:"arrow"},void 0===t?null:t),a.createElement(h,{key:"content",prefixCls:m,id:r,overlay:n,overlayInnerStyle:k})]},action:void 0===r?["hover"]:r,builtinPlacements:d,popupPlacement:void 0===x?"right":x,ref:R,popupAlign:void 0===C?{}:C,getPopupContainer:N,onPopupVisibleChange:y,afterPopupVisibleChange:b,popupTransitionName:A,popupAnimation:w,popupMotion:E,defaultPopupVisible:S,destroyPopupOnHide:_,autoDestroy:D,mouseLeaveDelay:void 0===f?.1:f,popupStyle:p,mouseEnterDelay:void 0===o?0:o},M),g)});var m=n(45434),g=n(88608),y=n.n(g),b={adjustX:1,adjustY:1},A={adjustX:0,adjustY:0},w=[0,0];function E(e){if("boolean"==typeof e)return e?b:A;return(0,i.A)((0,i.A)({},A),e)}var x=n(21689),C=n(75046),O=n(90916),T=n(48406),S=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},N=function(e,t){var n={},r=(0,i.A)({},e);return t.forEach(function(t){e&&t in e&&(n[t]=e[t],delete r[t])}),{picked:n,omitted:r}},k=new RegExp("^(".concat(O.w.join("|"),")(-inverse)?$")),P=a.forwardRef(function(e,t){var n,s,u=a.useContext(C.QO),l=u.getPopupContainer,c=u.getPrefixCls,f=u.direction,p=(0,m.A)(!1,{value:e.visible,defaultValue:e.defaultVisible}),h=(0,o.A)(p,2),g=h[0],b=h[1],A=function(){var t=e.title,n=e.overlay;return!t&&!n&&0!==t},O=function(){var t,n,r,o,a,s,u,l,c,f=e.builtinPlacements,p=e.arrowPointAtCenter,h=e.autoAdjustOverflow;return f||(r=void 0===(n=(t={arrowPointAtCenter:p,autoAdjustOverflow:h}).arrowWidth)?4:n,a=void 0===(o=t.horizontalArrowShift)?16:o,u=void 0===(s=t.verticalArrowShift)?8:s,l=t.autoAdjustOverflow,Object.keys(c={left:{points:["cr","cl"],offset:[-4,0]},right:{points:["cl","cr"],offset:[4,0]},top:{points:["bc","tc"],offset:[0,-4]},bottom:{points:["tc","bc"],offset:[0,4]},topLeft:{points:["bl","tc"],offset:[-(a+r),-4]},leftTop:{points:["tr","cl"],offset:[-4,-(u+r)]},topRight:{points:["br","tc"],offset:[a+r,-4]},rightTop:{points:["tl","cr"],offset:[4,-(u+r)]},bottomRight:{points:["tr","bc"],offset:[a+r,4]},rightBottom:{points:["bl","cr"],offset:[4,u+r]},bottomLeft:{points:["tl","bc"],offset:[-(a+r),4]},leftBottom:{points:["br","cl"],offset:[-4,u+r]}}).forEach(function(e){c[e]=t.arrowPointAtCenter?(0,i.A)((0,i.A)({},c[e]),{overflow:E(l),targetOffset:w}):(0,i.A)((0,i.A)({},d[e]),{overflow:E(l)}),c[e].ignoreShake=!0}),c)},P=e.getPopupContainer,R=S(e,["getPopupContainer"]),M=e.prefixCls,_=e.openClassName,D=e.getTooltipContainer,I=e.overlayClassName,L=e.color,z=e.overlayInnerStyle,H=e.children,j=c("tooltip",M),V=c(),F=g;!("visible"in e)&&A()&&(F=!1);var U=function(e,t){var n=e.type;if((!0===n.__ANT_BUTTON||!0===n.__ANT_SWITCH||!0===n.__ANT_CHECKBOX||"button"===e.type)&&e.props.disabled){var r=N(e.props.style,["position","left","right","top","bottom","float","display","zIndex"]),o=r.picked,s=r.omitted,u=(0,i.A)((0,i.A)({display:"inline-block"},o),{cursor:"not-allowed",width:e.props.block?"100%":null}),l=(0,i.A)((0,i.A)({},s),{pointerEvents:"none"}),c=(0,x.Ob)(e,{style:l,className:null});return a.createElement("span",{style:u,className:y()(e.props.className,"".concat(t,"-disabled-compatible-wrapper"))},c)}return e}((0,x.zO)(H)?H:a.createElement("span",null,H),j),W=U.props,B=y()(W.className,(0,r.A)({},_||"".concat(j,"-open"),!0)),K=y()(I,(n={},(0,r.A)(n,"".concat(j,"-rtl"),"rtl"===f),(0,r.A)(n,"".concat(j,"-").concat(L),L&&k.test(L)),n)),G=z;return L&&!k.test(L)&&(G=(0,i.A)((0,i.A)({},z),{background:L}),s={background:L}),a.createElement(v,(0,i.A)({},R,{prefixCls:j,overlayClassName:K,getTooltipContainer:P||D||l,ref:t,builtinPlacements:O(),overlay:function(){var t=e.title,n=e.overlay;if(0===t)return t;return n||t||""}(),visible:F,onVisibleChange:function(t){var n;b(!A()&&t),A()||null===(n=e.onVisibleChange)||void 0===n||n.call(e,t)},onPopupAlign:function(e,t){var n=O(),r=Object.keys(n).filter(function(e){return n[e].points[0]===t.points[0]&&n[e].points[1]===t.points[1]})[0];if(!r)return;var o=e.getBoundingClientRect(),i={top:"50%",left:"50%"};r.indexOf("top")>=0||r.indexOf("Bottom")>=0?i.top="".concat(o.height-t.offset[1],"px"):(r.indexOf("Top")>=0||r.indexOf("bottom")>=0)&&(i.top="".concat(-t.offset[1],"px")),r.indexOf("left")>=0||r.indexOf("Right")>=0?i.left="".concat(o.width-t.offset[0],"px"):(r.indexOf("right")>=0||r.indexOf("Left")>=0)&&(i.left="".concat(-t.offset[0],"px")),e.style.transformOrigin="".concat(i.left," ").concat(i.top)},overlayInnerStyle:G,arrowContent:a.createElement("span",{className:"".concat(j,"-arrow-content"),style:s}),motion:{motionName:(0,T.b)(V,"zoom-big-fast",e.transitionName),motionDeadline:1e3}}),F?(0,x.Ob)(U,{className:B}):U)});P.displayName="Tooltip",P.defaultProps={placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0};let R=P},80646:(e,t,n)=>{"use strict";n.d(t,{A:()=>ec});var r,o=n(47148),i=n(17015),a=n(65848),s=n(88608),u=n.n(s),l=n(48550),c=n(75046),f=n(39717),p=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},d=a.forwardRef(function(e,t){var n=e.prefixCls,r=e.component,s=void 0===r?"article":r,d=e.className,h=e["aria-label"],v=e.setContentRef,m=e.children,g=p(e,["prefixCls","component","className","aria-label","setContentRef","children"]),y=t;return v&&((0,f.A)(!1,"Typography","`setContentRef` is deprecated. Please use `ref` instead."),y=(0,l.K4)(t,v)),a.createElement(c.TG,null,function(e){var t=e.getPrefixCls,r=e.direction,l=t("typography",n),c=u()(l,(0,i.A)({},"".concat(l,"-rtl"),"rtl"===r),d);return a.createElement(s,(0,o.A)({className:c,"aria-label":h,ref:y},g),m)})});d.displayName="Typography";var h=n(90240),v=n(84790),m=n(21444),g=n(63257),y=n(92401),b=n(30756),A=n(93254),w=n(48973),E=n(33806),x=n.n(E),C=n(63639);let O={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var T=n(16637),S=function(e,t){return a.createElement(T.A,(0,C.A)((0,C.A)({},e),{},{ref:t,icon:O}))};S.displayName="EditOutlined";let N=a.forwardRef(S);var k=n(14232);let P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var R=function(e,t){return a.createElement(T.A,(0,C.A)((0,C.A)({},e),{},{ref:t,icon:P}))};R.displayName="CopyOutlined";let M=a.forwardRef(R);var _=n(60148),D=n(52118),I=n(64399),L=n(72161),z=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},H={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-block"},j=a.forwardRef(function(e,t){var n=e.style,r=e.noStyle,i=e.disabled,s=z(e,["style","noStyle","disabled"]),u={};return r||(u=(0,o.A)({},H)),i&&(u.pointerEvents="none"),u=(0,o.A)((0,o.A)({},u),n),a.createElement("div",(0,o.A)({role:"button",tabIndex:0,ref:t},s,{onKeyDown:function(e){e.keyCode===L.A.ENTER&&e.preventDefault()},onKeyUp:function(t){var n=t.keyCode,r=e.onClick;n===L.A.ENTER&&r&&r()},style:u}))}),V=n(15003),F=n(93900),U=n(59859),W=n(19721);let B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var K=function(e,t){return a.createElement(T.A,(0,C.A)((0,C.A)({},e),{},{ref:t,icon:B}))};K.displayName="EnterOutlined";let G=a.forwardRef(K);var Y=n(71217);let X=function(e){var t=e.prefixCls,n=e["aria-label"],r=e.className,o=e.style,s=e.direction,l=e.maxLength,c=e.autoSize,f=e.value,p=e.onSave,d=e.onCancel,h=e.onEnd,v=a.useRef(),m=a.useRef(!1),g=a.useRef(),y=a.useState(f),b=(0,W.A)(y,2),A=b[0],w=b[1];a.useEffect(function(){w(f)},[f]),a.useEffect(function(){if(v.current&&v.current.resizableTextArea){var e=v.current.resizableTextArea.textArea;e.focus();var t=e.value.length;e.setSelectionRange(t,t)}},[]);var E=function(){p(A.trim())},x=u()(t,"".concat(t,"-edit-content"),(0,i.A)({},"".concat(t,"-rtl"),"rtl"===s),r);return a.createElement("div",{className:x,style:o},a.createElement(Y.A,{ref:v,maxLength:l,value:A,onChange:function(e){w(e.target.value.replace(/[\n\r]/g,""))},onKeyDown:function(e){var t=e.keyCode;if(m.current)return;g.current=t},onKeyUp:function(e){var t=e.keyCode,n=e.ctrlKey,r=e.altKey,o=e.metaKey,i=e.shiftKey;g.current!==t||m.current||n||r||o||i||(t===L.A.ENTER?(E(),null==h||h()):t===L.A.ESC&&d())},onCompositionStart:function(){m.current=!0},onCompositionEnd:function(){m.current=!1},onBlur:function(){E()},"aria-label":n,autoSize:void 0===c||c}),a.createElement(G,{className:"".concat(t,"-edit-content-confirm")}))};var q=n(8155),Q={padding:0,margin:0,display:"inline",lineHeight:"inherit"};function Z(e){if(!e)return 0;var t=e.match(/^\d*(\.\d*)?/);return t?Number(t[0]):0}let $=function(e,t,n,o,i){r||(r=document.createElement("div")).setAttribute("aria-hidden","true"),r.parentNode||document.body.appendChild(r);var s,u,l=t.rows,c=t.suffix,f=void 0===c?"":c,p=window.getComputedStyle(e),d=Array.prototype.slice.apply(p).map(function(e){return"".concat(e,": ").concat(p.getPropertyValue(e),";")}).join(""),h=Math.floor(Z(p.lineHeight))*(l+1)+Z(p.paddingTop)+Z(p.paddingBottom);r.setAttribute("style",d),r.style.position="fixed",r.style.left="0",r.style.height="auto",r.style.minHeight="auto",r.style.maxHeight="auto",r.style.top="-999999px",r.style.zIndex="-1000",r.style.textOverflow="clip",r.style.whiteSpace="normal",r.style.webkitLineClamp="none";var v=(s=(0,w.A)(n),u=[],s.forEach(function(e){var t=u[u.length-1];"string"==typeof e&&"string"==typeof t?u[u.length-1]+=e:u.push(e)}),u);function m(){return r.offsetHeight<h}if((0,q.render)(a.createElement("div",{style:Q},a.createElement("span",{style:Q},v,f),a.createElement("span",{style:Q},o)),r),m())return(0,q.unmountComponentAtNode)(r),{content:n,text:r.innerHTML,ellipsis:!1};var g=Array.prototype.slice.apply(r.childNodes[0].childNodes[0].cloneNode(!0).childNodes).filter(function(e){return 8!==e.nodeType}),y=Array.prototype.slice.apply(r.childNodes[0].childNodes[1].cloneNode(!0).childNodes);(0,q.unmountComponentAtNode)(r);var b=[];r.innerHTML="";var A=document.createElement("span");r.appendChild(A);var E=document.createTextNode(i+f);function x(e){A.insertBefore(e,E)}return A.appendChild(E),y.forEach(function(e){r.appendChild(e)}),g.some(function(e,t){var n=function(e,t){var n=e.nodeType;if(1===n){if(x(e),m())return{finished:!1,reactNode:v[t]};return A.removeChild(e),{finished:!0,reactNode:null}}if(3===n){var r=e.textContent||"",o=document.createTextNode(r);return x(o),function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n.length,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=Math.floor((r+o)/2),s=n.slice(0,a);if(t.textContent=s,r>=o-1)for(var u=o;u>=r;u-=1){var l=n.slice(0,u);if(t.textContent=l,m()||!l)return u===n.length?{finished:!1,reactNode:n}:{finished:!0,reactNode:l}}if(m())return e(t,n,a,o,a);return e(t,n,r,a,i)}(o,r)}return{finished:!1,reactNode:null}}(e,t),r=n.finished,o=n.reactNode;return o&&b.push(o),r}),{content:b,text:r.innerHTML,ellipsis:!0}};var J=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},ee=(0,F.Fq)("webkitLineClamp"),et=(0,F.Fq)("textOverflow");function en(e,t,n){if(!0===e||void 0===e)return t;return e||n&&t}var er=function(e){(0,b.A)(n,e);var t=(0,A.A)(n);function n(){var e;return(0,g.A)(this,n),e=t.apply(this,arguments),e.contentRef=a.createRef(),e.state={edit:!1,copied:!1,ellipsisText:"",ellipsisContent:null,isEllipsis:!1,expanded:!1,clientRendered:!1},e.getPrefixCls=function(){var t=e.props.prefixCls;return(0,e.context.getPrefixCls)("typography",t)},e.onExpandClick=function(t){var n=e.getEllipsis().onExpand;e.setState({expanded:!0}),null==n||n(t)},e.onEditClick=function(t){t.preventDefault(),e.triggerEdit(!0)},e.onEditChange=function(t){var n=e.getEditable().onChange;null==n||n(t),e.triggerEdit(!1)},e.onEditCancel=function(){var t,n;null===(n=(t=e.getEditable()).onCancel)||void 0===n||n.call(t),e.triggerEdit(!1)},e.onCopyClick=function(t){t.preventDefault();var n=e.props,r=n.children,i=n.copyable,a=(0,o.A)({},"object"===(0,h.A)(i)?i:null);void 0===a.text&&(a.text=String(r)),x()(a.text||""),e.setState({copied:!0},function(){a.onCopy&&a.onCopy(),e.copyId=window.setTimeout(function(){e.setState({copied:!1})},3e3)})},e.setEditRef=function(t){e.editIcon=t},e.triggerEdit=function(t){var n=e.getEditable().onStart;t&&n&&n(),e.setState({edit:t},function(){!t&&e.editIcon&&e.editIcon.focus()})},e.resizeOnNextFrame=function(){V.A.cancel(e.rafId),e.rafId=(0,V.A)(function(){e.syncEllipsis()})},e}return(0,y.A)(n,[{key:"componentDidMount",value:function(){this.setState({clientRendered:!0}),this.resizeOnNextFrame()}},{key:"componentDidUpdate",value:function(e){var t=this.props.children,n=this.getEllipsis(),r=this.getEllipsis(e);(t!==e.children||n.rows!==r.rows)&&this.resizeOnNextFrame()}},{key:"componentWillUnmount",value:function(){window.clearTimeout(this.copyId),V.A.cancel(this.rafId)}},{key:"getEditable",value:function(e){var t=this.state.edit,n=(e||this.props).editable;if(!n)return{editing:t};return(0,o.A)({editing:t},"object"===(0,h.A)(n)?n:null)}},{key:"getEllipsis",value:function(e){var t=(e||this.props).ellipsis;if(!t)return{};return(0,o.A)({rows:1,expandable:!1},"object"===(0,h.A)(t)?t:null)}},{key:"canUseCSSEllipsis",value:function(){var e=this.state.clientRendered,t=this.props,n=t.editable,r=t.copyable,o=this.getEllipsis(),i=o.rows,a=o.expandable,s=o.suffix,u=o.onEllipsis,l=o.tooltip;if(s||l||n||r||a||!e||u)return!1;if(1===i)return et;return ee}},{key:"syncEllipsis",value:function(){var e=this.state,t=e.ellipsisText,n=e.isEllipsis,r=e.expanded,o=this.getEllipsis(),i=o.rows,a=o.suffix,s=o.onEllipsis,u=this.props.children;if(!i||i<0||!this.contentRef.current||r||this.canUseCSSEllipsis())return;(0,f.A)((0,w.A)(u).every(function(e){return"string"==typeof e}),"Typography","`ellipsis` should use string as children only.");var l=$(this.contentRef.current,{rows:i,suffix:a},u,this.renderOperations(!0),"..."),c=l.content,p=l.text,d=l.ellipsis;(t!==p||n!==d)&&(this.setState({ellipsisText:p,ellipsisContent:c,isEllipsis:d}),n!==d&&s&&s(d))}},{key:"renderExpand",value:function(e){var t,n=this.getEllipsis(),r=n.expandable,o=n.symbol,i=this.state,s=i.expanded,u=i.isEllipsis;if(!r||!e&&(s||!u))return null;return t=o||this.expandStr,a.createElement("a",{key:"expand",className:"".concat(this.getPrefixCls(),"-expand"),onClick:this.onExpandClick,"aria-label":this.expandStr},t)}},{key:"renderEdit",value:function(){var e=this.props.editable;if(!e)return;var t=e.icon,n=e.tooltip,r=(0,w.A)(n)[0]||this.editStr,o="string"==typeof r?r:"";return a.createElement(U.A,{key:"edit",title:!1===n?"":r},a.createElement(j,{ref:this.setEditRef,className:"".concat(this.getPrefixCls(),"-edit"),onClick:this.onEditClick,"aria-label":o},t||a.createElement(N,{role:"button"})))}},{key:"renderCopy",value:function(){var e=this.state.copied,t=this.props.copyable;if(!t)return;var n=this.getPrefixCls(),r=t.tooltips,o=t.icon,i=Array.isArray(r)?r:[r],s=Array.isArray(o)?o:[o],l=e?en(i[1],this.copiedStr):en(i[0],this.copyStr),c=e?this.copiedStr:this.copyStr,f="string"==typeof l?l:c;return a.createElement(U.A,{key:"copy",title:l},a.createElement(j,{className:u()("".concat(n,"-copy"),e&&"".concat(n,"-copy-success")),onClick:this.onCopyClick,"aria-label":f},e?en(s[1],a.createElement(k.A,null),!0):en(s[0],a.createElement(M,null),!0)))}},{key:"renderEditInput",value:function(){var e=this.props,t=e.children,n=e.className,r=e.style,o=this.context.direction,i=this.getEditable(),s=i.maxLength,u=i.autoSize,l=i.onEnd;return a.createElement(X,{value:"string"==typeof t?t:"",onSave:this.onEditChange,onCancel:this.onEditCancel,onEnd:l,prefixCls:this.getPrefixCls(),className:n,style:r,direction:o,maxLength:s,autoSize:u})}},{key:"renderOperations",value:function(e){return[this.renderExpand(e),this.renderEdit(),this.renderCopy()].filter(function(e){return e})}},{key:"renderContent",value:function(){var e=this,t=this.state,n=t.ellipsisContent,r=t.isEllipsis,s=t.expanded,l=this.props,c=l.component,f=l.children,p=l.className,h=l.type,g=l.disabled,y=l.style,b=J(l,["component","children","className","type","disabled","style"]),A=this.context.direction,w=this.getEllipsis(),E=w.rows,x=w.suffix,C=w.tooltip,O=this.getPrefixCls(),T=(0,v.A)(b,["prefixCls","editable","copyable","ellipsis","mark","code","delete","underline","strong","keyboard","italic"].concat((0,m.A)(D.Vh))),S=this.canUseCSSEllipsis(),N=1===E&&S,k=E&&E>1&&S,P=f;if(E&&r&&!s&&!S){var R=b.title,M=R||"";R||"string"!=typeof f&&"number"!=typeof f||(M=String(f)),M=M.slice(String(n||"").length),P=a.createElement(a.Fragment,null,n,a.createElement("span",{title:M,"aria-hidden":"true"},"..."),x),C&&(P=a.createElement(U.A,{title:!0===C?f:C},a.createElement("span",null,P)))}else P=a.createElement(a.Fragment,null,f,x);return P=function(e,t){var n=e.mark,r=e.code,o=e.underline,i=e.delete,s=e.strong,u=e.keyboard,l=e.italic,c=t;function f(e,t){if(!e)return;c=a.createElement(t,{},c)}return f(s,"strong"),f(o,"u"),f(i,"del"),f(r,"code"),f(n,"mark"),f(u,"kbd"),f(l,"i"),c}(this.props,P),a.createElement(I.A,{componentName:"Text"},function(t){var n,r=t.edit,s=t.copy,l=t.copied,f=t.expand;return e.editStr=r,e.copyStr=s,e.copiedStr=l,e.expandStr=f,a.createElement(_.A,{onResize:e.resizeOnNextFrame,disabled:S},a.createElement(d,(0,o.A)({className:u()((n={},(0,i.A)(n,"".concat(O,"-").concat(h),h),(0,i.A)(n,"".concat(O,"-disabled"),g),(0,i.A)(n,"".concat(O,"-ellipsis"),E),(0,i.A)(n,"".concat(O,"-single-line"),1===E),(0,i.A)(n,"".concat(O,"-ellipsis-single-line"),N),(0,i.A)(n,"".concat(O,"-ellipsis-multiple-line"),k),n),p),style:(0,o.A)((0,o.A)({},y),{WebkitLineClamp:k?E:void 0}),component:c,ref:e.contentRef,direction:A},T),P,e.renderOperations()))})}},{key:"render",value:function(){if(this.getEditable().editing)return this.renderEditInput();return this.renderContent()}}],[{key:"getDerivedStateFromProps",value:function(e){var t=e.children,n=e.editable;return(0,f.A)(!n||"string"==typeof t,"Typography","When `editable` is enabled, the `children` should use string."),{}}}]),n}(a.Component);er.contextType=c.QO,er.defaultProps={children:""};var eo=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},ei=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let ea=a.forwardRef(function(e,t){var n=e.ellipsis,r=e.rel,i=ei(e,["ellipsis","rel"]);(0,f.A)("object"!==(0,h.A)(n),"Typography.Link","`ellipsis` only supports boolean value.");var s=a.useRef(null);a.useImperativeHandle(t,function(){var e;return null===(e=s.current)||void 0===e?void 0:e.contentRef.current});var u=(0,o.A)((0,o.A)({},i),{rel:void 0===r&&"_blank"===i.target?"noopener noreferrer":r});return delete u.navigate,a.createElement(er,(0,o.A)({},u,{ref:s,ellipsis:!!n,component:"a"}))});var es=n(85852),eu=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},el=(0,es.p)(1,2,3,4,5);d.Text=function(e){var t=e.ellipsis,n=eo(e,["ellipsis"]),r=a.useMemo(function(){if(t&&"object"===(0,h.A)(t))return(0,v.A)(t,["expandable","rows"]);return t},[t]);return(0,f.A)("object"!==(0,h.A)(t)||!t||!("expandable"in t)&&!("rows"in t),"Typography.Text","`ellipsis` do not support `expandable` or `rows` props."),a.createElement(er,(0,o.A)({},n,{ellipsis:r,component:"span"}))},d.Link=ea,d.Title=function(e){var t,n=e.level,r=void 0===n?1:n,i=eu(e,["level"]);return -1!==el.indexOf(r)?t="h".concat(r):((0,f.A)(!1,"Typography.Title","Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version."),t="h1"),a.createElement(er,(0,o.A)({},i,{component:t}))},d.Paragraph=function(e){return a.createElement(er,(0,o.A)({},e,{component:"div"}))};let ec=d},81434:(e,t,n)=>{"use strict";n.d(t,{SV:()=>s,we:()=>E});var r,o=n(65848),i=n(90692);n(8155);var a=n(51736);function s(e){return o.useMemo(()=>{if(e.every(e=>null==e))return null;return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}},e)}let u={...r||(r=n.t(o,2))},l=u.useInsertionEffect||(e=>e());var c="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;let f=!1,p=0,d=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+p++,h=u.useId||function(){let[e,t]=o.useState(()=>f?d():void 0);return c(()=>{null==e&&t(d())},[]),o.useEffect(()=>{f=!0},[]),e},v=o.createContext(null),m=o.createContext(null),g=()=>{var e;return(null==(e=o.useContext(v))?void 0:e.id)||null},y=()=>o.useContext(m),b=()=>{},A=e=>e&&(e.host||A(e.parentNode)),w=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function E(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:r}=e,i=h(),a=o.useRef({}),[s]=o.useState(()=>(function(){let e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}})()),u=null!=g(),[c,f]=o.useState(r.reference),p=function(e){let t=o.useRef(()=>{});return l(()=>{t.current=e}),o.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}((e,t,r)=>{a.current.openEvent=e?t:void 0,s.emit("openchange",{open:e,event:t,reason:r,nested:u}),null==n||n(e,t,r)}),d=o.useMemo(()=>({setPositionReference:f}),[]),v=o.useMemo(()=>({reference:c||r.reference||null,floating:r.floating||null,domReference:r.reference}),[c,r.reference,r.floating]);return o.useMemo(()=>({dataRef:a,open:t,onOpenChange:p,elements:v,events:s,floatingId:i,refs:d}),[t,p,v,s,i,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,s=r.elements,[u,f]=o.useState(null),[p,d]=o.useState(null),v=(null==s?void 0:s.reference)||u,m=o.useRef(null),b=y();c(()=>{v&&(m.current=v)},[v]);let A=(0,a.we)({...e,elements:{...s,...p&&{reference:p}}}),w=o.useCallback(e=>{let t=(0,i.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;d(t),A.refs.setReference(t)},[A.refs]),E=o.useCallback(e=>{((0,i.vq)(e)||null===e)&&(m.current=e,f(e)),((0,i.vq)(A.refs.reference.current)||null===A.refs.reference.current||null!==e&&!(0,i.vq)(e))&&A.refs.setReference(e)},[A.refs]),x=o.useMemo(()=>({...A.refs,setReference:E,setPositionReference:w,domReference:m}),[A.refs,E,w]),C=o.useMemo(()=>({...A.elements,domReference:v}),[A.elements,v]),O=o.useMemo(()=>({...A,...r,refs:x,elements:C,nodeId:t}),[A,x,C,t,r]);return c(()=>{r.dataRef.current.floatingContext=O;let e=null==b?void 0:b.nodesRef.current.find(e=>e.id===t);e&&(e.context=O)}),o.useMemo(()=>({...A,context:O,refs:x,elements:C}),[A,x,C,O])}}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/939.91d5961d.chunk.js.map