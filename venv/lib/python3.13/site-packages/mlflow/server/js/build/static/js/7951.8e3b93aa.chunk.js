"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[2402,7951],{22402:function(e,t,l){l.r(t),l.d(t,{default:function(){return s}});var i=l(45605),o=l(14943),a=(l(44181),l(80672),l(31014)),n=l(50111);const r=()=>{const e=(0,a.useRef)(null);return(0,a.useEffect)((()=>{if(e.current&&e.current.getRootNode()!==document){const e=document.createElement("style");return e.className="ag-grid-snapshot-base-css",e.appendChild(document.createTextNode('@font-face { font-family: "agGridBalham"; src: url("data:application/font-woff;charset=utf-8;base64,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") format("woff"); font-weight: normal; font-style: normal; }')),document.head.appendChild(e),()=>e.remove()}return()=>{}}),[]),(0,n.Y)("span",{ref:e})};var s=e=>(0,n.FD)(n.FK,{children:[(0,n.Y)(r,{}),(0,n.Y)(o.AgGridReact,{modules:[i.Q],...e})]})},30706:function(e,t,l){l.d(t,{$:function(){return p}});var i=l(48012),o=l(32599),a=l(88464),n=l(88443),r=l(9133),s=l(31014),d=l(92677),u=l(50111);const c="all_metrics",m=[d.t7.Attributes,c];var g={name:"1a6l0ym",styles:"max-height:500px;padding-right:32px"};const p=e=>{let{onUpdateColumns:t,columnVisibility:l={},columnDefs:p,disabled:f,customTrigger:A}=e;const v=(0,a.A)(),{leafColumnIds:h=[],treeData:y=[]}=(0,s.useMemo)((()=>{var e;if(!p)return{};const t=[],l=p.find((e=>e.groupId===d.t7.Attributes));var i;l&&t.push({...l,children:null===(i=l.children)||void 0===i?void 0:i.filter((e=>{let{colId:t}=e;return t&&!d.R3.includes(t)}))});const o=p.filter((e=>{var t;return null===(t=e.groupId)||void 0===t?void 0:t.startsWith(d.D2)})).map((e=>({...e,headerName:e.headerName?`Dataset: ${e.headerName}`:v.formatMessage({id:"1b3c+B",defaultMessage:"No dataset"})})));o.length>0&&t.push({groupId:c,headerName:v.formatMessage({id:"h3GmoO",defaultMessage:"Metrics"}),children:o});const a=p.find((e=>e.groupId===d.t7.Params));a&&t.push(a);const n=[],s=e=>{var t,l,i,o,a;return e.colId&&n.push(e.colId),{key:null!==(t=null!==(l=e.groupId)&&void 0!==l?l:e.colId)&&void 0!==t?t:"",title:null!==(i=e.headerName)&&void 0!==i?i:"",children:(0,r.compact)(null!==(o=null===(a=e.children)||void 0===a?void 0:a.map(s))&&void 0!==o?o:[])}},u=(0,r.compact)(null!==(e=null===t||void 0===t?void 0:t.map((e=>s(e))))&&void 0!==e?e:[]);return{leafColumnIds:n,treeData:u}}),[p,v]);return(0,u.FD)(i.rId.Root,{children:[(0,u.Y)(i.rId.Trigger,{asChild:!0,disabled:f,children:null!==A&&void 0!==A?A:(0,u.Y)(o.B,{componentId:"mlflow.logged_model.list.columns",icon:(0,u.Y)(i.jng,{}),disabled:f,children:(0,u.Y)(n.A,{id:"EN0lUA",defaultMessage:"Columns"})})}),(0,u.Y)(i.rId.Content,{css:g,children:(0,u.Y)(i.PH6,{treeData:y,mode:"checkable",showLine:!0,defaultExpandedKeys:m,defaultCheckedKeys:h.filter((e=>!1!==l[e])),onCheck:e=>{const l="checked"in e?e.checked:e,i={};for(const t of h)l.includes(t)||(i[t]=!1);t(i)}})})]})}},31798:function(e,t,l){l.d(t,{P:function(){return d},k:function(){return u}});var i=l(9133),o=l(31014),a=l(58898),n=l(88421),r=l(92677),s=l(82638);const d=[r.I7.CreationTime],u=()=>{const[e,t]=(0,o.useReducer)(((e,t)=>{if("SET_ORDER_BY"===t.type)return{...e,orderByColumn:t.orderByColumn,orderByAsc:t.orderByAsc};if("SET_COLUMN_VISIBILITY"===t.type)return{...e,columnVisibility:t.columnVisibility};if("CLEAR_DATASETS"===t.type)return{...e,selectedFilterDatasets:[]};var l,o,a;if("TOGGLE_DATASET"===t.type)return{...e,selectedFilterDatasets:null!==(l=e.selectedFilterDatasets)&&void 0!==l&&l.some((e=>(0,i.isEqual)(e,t.dataset)))?null===(o=e.selectedFilterDatasets)||void 0===o?void 0:o.filter((e=>!(0,i.isEqual)(e,t.dataset))):[...null!==(a=e.selectedFilterDatasets)&&void 0!==a?a:[],t.dataset]};if("SET_RUN_VISIBILITY"===t.type){if(t.visibilityMode)return{...e,rowVisibilityMode:t.visibilityMode,rowVisibilityMap:{}};if(t.rowUuid&&void 0!==t.rowIndex){var r;const l=(0,n.cP)(e.rowVisibilityMode,t.rowUuid,t.rowIndex,null!==(r=e.rowVisibilityMap)&&void 0!==r?r:{});return{...e,rowVisibilityMap:{...e.rowVisibilityMap,[t.rowUuid]:l}}}}return e}),{orderByColumn:(0,i.first)(d),orderByAsc:!1,columnVisibility:{},rowVisibilityMode:a.oy.FIRST_10_RUNS}),l=(0,o.useCallback)(((e,l)=>t({type:"SET_ORDER_BY",orderByColumn:e,orderByAsc:l})),[]),r=(0,o.useCallback)((e=>t({type:"SET_COLUMN_VISIBILITY",columnVisibility:e})),[]),u=(0,o.useCallback)((e=>t({type:"SET_RUN_VISIBILITY",visibilityMode:e})),[]),c=(0,o.useCallback)(((e,l)=>t({type:"SET_RUN_VISIBILITY",rowUuid:e,rowIndex:l})),[]),m=(0,o.useCallback)((e=>t({type:"TOGGLE_DATASET",dataset:e})),[]),g=(0,o.useCallback)((()=>t({type:"CLEAR_DATASETS"})),[]),p=(0,s.t)(e),[f,A]=(0,o.useState)("");return{state:p,isFilteringActive:Boolean(f||!(0,i.isEmpty)(e.selectedFilterDatasets)),searchQuery:f,setOrderBy:l,setColumnVisibility:r,setRowVisibilityMode:u,toggleRowVisibility:c,updateSearchQuery:A,toggleDataset:m,clearSelectedDatasets:g}}},46398:function(e,t,l){l.d(t,{_:function(){return R}});var i=l(89555),o=l(32599),a=l(48012),n=l(22402),r=l(28940),s=l(31014),d=l(50111);const u=(0,s.createContext)({}),c=e=>{let{loadMoreResults:t,moreResultsAvailable:l,isLoadingMore:i,children:o}=e;const a=(0,s.useMemo)((()=>({moreResultsAvailable:l,loadMoreResults:t,isLoadingMore:i})),[l,t,i]);return(0,d.Y)(u.Provider,{value:a,children:o})};var m=l(31798),g=l(88443),p=l(28239),f=l(15579),A=l(37616),v=l(25866);const h='\nimport pandas as pd\nfrom sklearn.linear_model import ElasticNet\nfrom sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\nfrom sklearn.datasets import load_iris\nfrom sklearn.model_selection import train_test_split\n\nimport mlflow\nimport mlflow.sklearn\nfrom mlflow.entities import Dataset\n\n# Helper function to compute metrics\ndef compute_metrics(actual, predicted):\n    rmse = mean_squared_error(actual, predicted) \n    mae = mean_absolute_error(actual, predicted)\n    r2 = r2_score(actual, predicted)\n    return rmse, mae, r2\n\n# Load Iris dataset and prepare the DataFrame\niris = load_iris()\niris_df = pd.DataFrame(data=iris.data, columns=iris.feature_names)\niris_df[\'quality\'] = (iris.target == 2).astype(int)  # Create a binary target for simplicity\n\n# Split into training and testing datasets\ntrain_df, test_df = train_test_split(iris_df, test_size=0.2, random_state=42)\n\n# Start a run to represent the training job\nwith mlflow.start_run() as training_run:\n    # Load the training dataset with MLflow. We will link training metrics to this dataset.\n    train_dataset: Dataset = mlflow.data.from_pandas(train_df, name="train")\n    train_x = train_dataset.df.drop(["quality"], axis=1)\n    train_y = train_dataset.df[["quality"]]\n\n    # Fit a model to the training dataset\n    lr = ElasticNet(alpha=0.5, l1_ratio=0.5, random_state=42)\n    lr.fit(train_x, train_y)\n\n    # Log the model, specifying its ElasticNet parameters (alpha, l1_ratio)\n    # As a new feature, the LoggedModel entity is linked to its name and params\n    model_info = mlflow.sklearn.log_model(\n        sk_model=lr,\n        name="elasticnet",\n        params={\n            "alpha": 0.5,\n            "l1_ratio": 0.5,\n        },\n        input_example = train_x\n    )\n\n    # Inspect the LoggedModel and its properties\n    logged_model = mlflow.get_logged_model(model_info.model_id)\n    print(logged_model.model_id, logged_model.params)\n\n    # Evaluate the model on the training dataset and log metrics\n    # These metrics are now linked to the LoggedModel entity\n    predictions = lr.predict(train_x)\n    (rmse, mae, r2) = compute_metrics(train_y, predictions)\n    mlflow.log_metrics(\n        metrics={\n            "rmse": rmse,\n            "r2": r2,\n            "mae": mae,\n        },\n        model_id=logged_model.model_id,\n        dataset=train_dataset\n    )\n\n    # Inspect the LoggedModel, now with metrics\n    logged_model = mlflow.get_logged_model(model_info.model_id)\n    print(logged_model.model_id, logged_model.metrics)'.trim(),y=e=>{let{displayShowExampleButton:t=!0,isFilteringActive:l=!1,badRequestError:n}=e;const{theme:r}=(0,o.u)(),[u,c]=(0,s.useState)(!1);return(0,d.FD)("div",{css:(0,i.AH)({inset:0,top:r.general.heightBase+r.spacing.lg,position:"absolute",display:"flex",justifyContent:"center",alignItems:"center",minHeight:160},""),children:[(0,d.Y)(a.SvL,{title:n?(0,d.Y)(g.A,{id:"Xm5xxu",defaultMessage:"Request error"}):l?(0,d.Y)(g.A,{id:"X6P8tX",defaultMessage:"No models found"}):(0,d.Y)(g.A,{id:"dDX+7q",defaultMessage:"No models logged"}),description:n?n.message:l?(0,d.Y)(g.A,{id:"2deqxk",defaultMessage:"We couldn't find any models matching your search criteria. Try changing your search filters."}):(0,d.Y)(g.A,{id:"SzapEm",defaultMessage:"Your models will appear here once you log them using newest version of MLflow. <link>Learn more</link>.",values:{link:e=>(0,d.Y)(o.T.Link,{componentId:"mlflow.logged_models.list.no_results_learn_more",openInNewTab:!0,href:(0,v.Gg)(),children:e})}}),image:n?(0,d.Y)(o.j,{}):void 0,button:!t||l||n?null:(0,d.Y)(o.B,{type:"primary",componentId:"mlflow.logged_models.list.show_example_code",onClick:()=>c(!u),children:(0,d.Y)(g.A,{id:"Y3wSZx",defaultMessage:"Show example code"})})}),(0,d.FD)(f.d,{size:"wide",visible:u,onCancel:()=>c(!1),title:(0,d.Y)(g.A,{id:"plSWbC",defaultMessage:"Example code"}),componentId:"mlflow.logged_models.list.example_code_modal",okText:(0,d.Y)(g.A,{id:"n24ICT",defaultMessage:"Close"}),onOk:()=>c(!1),children:[(0,d.Y)(o.T.Text,{children:(0,d.Y)(g.A,{id:"r4a52n",defaultMessage:"Install <code>mlflow</code> from <code>mlflow-3</code> branch:",values:{code:e=>(0,d.Y)("code",{children:e})}})}),(0,d.Y)(A.z7,{language:"text",children:"pip install git+https://github.com/mlflow/mlflow@mlflow-3"}),(0,d.Y)(f.S,{size:"sm"}),(0,d.Y)(g.A,{id:"80LQWB",defaultMessage:"Run example training code:"}),(0,d.Y)(A.z7,{language:"python",children:h})]})]})};var b=l(92677);const M=Symbol("LoadMoreRow"),I=e=>{var t,l;let{data:i}=e;return null!==(t=null===i||void 0===i||null===(l=i.info)||void 0===l?void 0:l.model_id)&&void 0!==t?t:""},w=e=>{let{loggedModels:t,isLoading:l,isLoadingMore:u,badRequestError:f,onLoadMore:A,orderByColumn:v,orderByAsc:h,moreResultsAvailable:w,onOrderByChange:R,columnDefs:D=[],columnVisibility:_,relatedRunsData:k,className:x,disableLoadMore:S,displayShowExampleButton:N=!0,isFilteringActive:C=!0}=e;const{theme:F}=(0,o.u)(),T=(0,r.T)({usingCustomHeaderComponent:!1}),O=(0,s.useRef)(null),z=(0,s.useMemo)((()=>t&&k?t.map((e=>{const t=k.find((t=>{var l,i;return(null===t||void 0===t||null===(l=t.info)||void 0===l?void 0:l.runUuid)===(null===e||void 0===e||null===(i=e.info)||void 0===i?void 0:i.source_run_id)}));return{...e,sourceRun:t}})):t),[t,k]),B=(0,s.useMemo)((()=>{if(!l)return S||!z||0===z.length?z:[...z,M]}),[z,l,S]),E=(0,s.useCallback)((e=>{const t=e.columnApi.getColumnState().find((e=>e.sort));null!==t&&void 0!==t&&t.colId&&(m.P.includes(t.colId)||t.colId.startsWith(b.D2))&&(null===R||void 0===R||R(null===t||void 0===t?void 0:t.colId,"asc"===t.sort))}),[R]),L=(0,s.useCallback)(((e,t)=>{var l;const i=null===(l=O.current)||void 0===l?void 0:l.getColumn(e);if(i){var o,a;const e=null===(o=O.current)||void 0===o||null===(a=o.getColumnState().find((e=>e.sort)))||void 0===a?void 0:a.colId;var n,r;if(e!==i.getColId())null===(n=O.current)||void 0===n||null===(r=n.getColumn(e))||void 0===r||r.setSort(null);i.setSort(t?"asc":"desc")}}),[]),H=(0,s.useCallback)((e=>{for(const o of null!==(t=null===O||void 0===O||null===(l=O.current)||void 0===l?void 0:l.getAllColumns())&&void 0!==t?t:[]){var t,l,i;null===(i=O.current)||void 0===i||i.setColumnVisible(o,!1!==(null===e||void 0===e?void 0:e[o.getColId()]))}}),[]);(0,s.useEffect)((()=>L(v,h)),[L,v,h]),(0,s.useEffect)((()=>H(_)),[H,_]);const W=(0,s.useMemo)((()=>D.some((e=>"children"in e))),[D]),V=(0,s.useRef)(null),{cellMouseOverHandler:q,cellMouseOutHandler:Z}=(0,p._)(V,void 0,!0,I);return(0,d.Y)(c,{loadMoreResults:A,moreResultsAvailable:w,isLoadingMore:u,children:(0,d.FD)("div",{css:(0,i.AH)({overflow:"hidden",flex:1,...T,".ag-cell":{alignItems:"center"},borderTop:`1px solid ${F.colors.border}`,".ag-header-cell.is-checkbox-header-cell":{paddingLeft:F.spacing.sm},"&& .ag-root-wrapper":{border:0}},""),className:["ag-theme-balham",x].join(" "),ref:V,children:[(0,d.Y)(n.default,{columnDefs:D,rowData:B,rowHeight:36,rowSelection:"multiple",suppressRowClickSelection:!0,suppressMovableColumns:!0,getRowId:I,suppressLoadingOverlay:!0,suppressNoRowsOverlay:!0,suppressColumnMoveAnimation:!0,isFullWidthRow:e=>{let{rowNode:t}=e;return t.data===M},fullWidthCellRenderer:Y,onSortChanged:E,onGridReady:e=>{let{columnApi:t}=e;O.current=t,L(v,h),H(_)},onCellMouseOver:q,onCellMouseOut:Z}),l&&(0,d.Y)("div",{css:(0,i.AH)({inset:0,top:(W?F.general.heightBase:0)+F.spacing.lg,position:"absolute",paddingTop:F.spacing.md,paddingRight:F.spacing.md},""),children:(0,d.Y)(a.QvX,{lines:8,label:(0,d.Y)(g.A,{id:"/hP8AG",defaultMessage:"Models loading"})})}),!l&&0===(null===t||void 0===t?void 0:t.length)&&(0,d.Y)(y,{displayShowExampleButton:N,badRequestError:f,isFilteringActive:C})]})})},Y=()=>{const{theme:e}=(0,o.u)(),{moreResultsAvailable:t,loadMoreResults:l,isLoadingMore:a}=(0,s.useContext)(u);return t?(0,d.Y)("div",{css:(0,i.AH)({pointerEvents:"all",userSelect:"all",padding:e.spacing.sm,display:"flex",justifyContent:"center"},""),children:(0,d.Y)(o.B,{componentId:"mlflow.logged_models.list.load_more",type:"primary",size:"small",onClick:l,loading:a,children:(0,d.Y)(g.A,{id:"FWtUH2",defaultMessage:"Load more"})})}):null},R=s.memo(w)},88421:function(e,t,l){l.d(t,{Ap:function(){return d},GP:function(){return u},cP:function(){return s}});var i=l(31014),o=l(58898),a=l(9133),n=l(50111);const r=(0,i.createContext)({isRowHidden:()=>!1,setRowVisibilityMode:()=>{},toggleRowVisibility:()=>{},visibilityMode:o.oy.FIRST_10_RUNS,usingCustomVisibility:!1}),s=(e,t,l,i)=>(0,a.isUndefined)(i[t])?e===o.oy.HIDEALL||(e===o.oy.FIRST_10_RUNS?l>=10:e===o.oy.FIRST_20_RUNS&&l>=20):!i[t],d=e=>{let{children:t,visibilityMap:l={},visibilityMode:o,setRowVisibilityMode:a,toggleRowVisibility:d}=e;const u=(0,i.useCallback)(((e,t)=>s(o,e,t,l)),[l,o]),c=(0,i.useMemo)((()=>Object.keys(l).length>0),[l]),m=(0,i.useMemo)((()=>({isRowHidden:u,setRowVisibilityMode:a,toggleRowVisibility:d,visibilityMode:o,usingCustomVisibility:c})),[u,a,d,o,c]);return(0,n.Y)(r.Provider,{value:m,children:t})},u=()=>(0,i.useContext)(r)},92677:function(e,t,l){l.d(t,{t7:function(){return z},I7:function(){return B},R3:function(){return L},D2:function(){return E},TQ:function(){return W},ih:function(){return q}});var i=l(31014),o=l(88464),a=l(89555),n=l(32599),r=l(93215),s=l(58481),d=l(23734),u=l(79432),c=l(50111);const m=e=>{var t,l;let{data:i}=e;const{theme:o}=(0,n.u)();var m;return null!==(t=i.info)&&void 0!==t&&t.experiment_id&&null!==(l=i.info)&&void 0!==l&&l.model_id?(0,c.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:o.spacing.sm},""),children:[(0,c.Y)(u.E,{color:(0,d.T6)(i.info.model_id)}),(0,c.Y)(r.N_,{to:s.h.getExperimentLoggedModelDetailsPageRoute(i.info.experiment_id,i.info.model_id),children:i.info.name})]}):(0,c.Y)(c.FK,{children:null===(m=i.info)||void 0===m?void 0:m.name})};var g=l(58710),p=l(83858),f=l(48012),A=l(28684);const v=e=>{let{data:t}=e;const l=(0,i.useMemo)((()=>{var e,l;return(null!==(e=null===t||void 0===t||null===(l=t.data)||void 0===l?void 0:l.metrics)&&void 0!==e?e:[]).reduce(((e,t)=>{let{dataset_digest:l,dataset_name:i,run_id:o}=t;return i&&l&&!e.find((e=>e.dataset_name===i&&e.dataset_digest===l))&&e.push({dataset_name:i,dataset_digest:l,run_id:o}),e}),[])}),[t]);return l.length?(0,c.Y)(f.nEg,{children:l.map((e=>{let{dataset_digest:t,dataset_name:l,run_id:i}=e;return(0,c.Y)(A.f,{datasetName:l,datasetDigest:t,runId:null!==i&&void 0!==i?i:null},[l,t].join("."))}))}):(0,c.Y)(c.FK,{children:"-"})};var h=l(9133);const y=e=>{var t,l,i;let{data:o}=e;var a,n,d,u,m,g;return null!==(t=o.info)&&void 0!==t&&t.experiment_id&&null!==(l=o.info)&&void 0!==l&&l.source_run_id?(0,c.Y)(r.N_,{to:s.h.getRunPageRoute(null===(a=o.info)||void 0===a?void 0:a.experiment_id,null===(n=o.info)||void 0===n?void 0:n.source_run_id),target:"_blank",children:null!==(d=null===(u=o.sourceRun)||void 0===u||null===(m=u.info)||void 0===m?void 0:m.runName)&&void 0!==d?d:null===(g=o.info)||void 0===g?void 0:g.source_run_id}):(null===(i=o.info)||void 0===i?void 0:i.source_run_id)||(0,c.Y)(c.FK,{children:"-"})};var b=l(88421),M=l(1670),I=l(88443),w=l(58898),Y=l(81313);const R=e=>{var t,l;let{data:i,rowIndex:o}=e;const{isRowHidden:r,toggleRowVisibility:s}=(0,b.GP)(),d=r(null!==(t=null===(l=i.info)||void 0===l?void 0:l.model_id)&&void 0!==t?t:"",o),{theme:u}=(0,n.u)();return(0,c.Y)(n.B,{componentId:"mlflow.logged_model.list_page.row_visibility_toggle",type:"link",onClick:()=>{var e,t;return s(null!==(e=null===(t=i.info)||void 0===t?void 0:t.model_id)&&void 0!==e?e:"",o)},icon:d?(0,c.Y)(f.DAb,{css:(0,a.AH)({color:u.colors.textSecondary},"")}):(0,c.Y)(M.h,{css:(0,a.AH)({color:u.colors.textSecondary},"")})})},D=()=>{const e=(0,o.A)(),{visibilityMode:t,usingCustomVisibility:l,setRowVisibilityMode:i}=(0,b.GP)(),{theme:r}=(0,n.u)();return(0,c.FD)(f.rId.Root,{modal:!1,children:[(0,c.Y)(f.rId.Trigger,{asChild:!0,children:(0,c.Y)(n.B,{componentId:"mlflow.logged_model.list_page.global_row_visibility_toggle",type:"link","data-testid":"experiment-view-runs-visibility-column-header","aria-label":e.formatMessage({id:"BTAzqb",defaultMessage:"Toggle visibility of rows"}),children:t===w.oy.HIDEALL?(0,c.Y)(f.DAb,{css:(0,a.AH)({color:r.colors.textSecondary},"")}):(0,c.Y)(M.h,{css:(0,a.AH)({color:r.colors.textSecondary},"")})})}),(0,c.Y)(f.rId.Content,{children:(0,c.FD)(f.rId.RadioGroup,{componentId:"mlflow.logged_model.list_page.global_row_visibility_toggle.options",value:t,onValueChange:e=>i((0,Y.SK)(w.oy,e,w.oy.FIRST_10_RUNS)),children:[(0,c.FD)(f.rId.RadioItem,{value:w.oy.FIRST_10_RUNS,children:[(0,c.Y)(f.rId.ItemIndicator,{children:l?(0,c.Y)(f.YTx,{}):null}),(0,c.Y)(I.A,{id:"w39cZ4",defaultMessage:"Show first 10"})]}),(0,c.FD)(f.rId.RadioItem,{value:w.oy.FIRST_20_RUNS,children:[(0,c.Y)(f.rId.ItemIndicator,{children:l?(0,c.Y)(f.YTx,{}):null}),(0,c.Y)(I.A,{id:"ONIiUE",defaultMessage:"Show first 20"})]}),(0,c.FD)(f.rId.RadioItem,{value:w.oy.SHOWALL,children:[(0,c.Y)(f.rId.ItemIndicator,{children:l?(0,c.Y)(f.YTx,{}):null}),(0,c.Y)(I.A,{id:"4yjF8O",defaultMessage:"Show all runs"})]}),(0,c.FD)(f.rId.RadioItem,{value:w.oy.HIDEALL,children:[(0,c.Y)(f.rId.ItemIndicator,{children:l?(0,c.Y)(f.YTx,{}):null}),(0,c.Y)(I.A,{id:"Qi6Etm",defaultMessage:"Hide all runs"})]})]})})]})};var _=l(31179),k=l(21317);var x={name:"1p2zdak",styles:"margin-right:0;vertical-align:middle"};const S=e=>{let{data:t}=e;const{theme:l}=(0,n.u)(),o=(0,_.b)({loggedModels:[t]});return(0,h.isEmpty)(o)?"-":(0,c.Y)(f.nEg,{children:o.map((e=>(0,c.Y)(i.Fragment,{children:(0,c.FD)(r.N_,{to:e.link,css:(0,a.AH)({display:"flex",alignItems:"center",gap:l.spacing.xs},""),children:[(0,c.Y)(k.h,{}),e.displayedName,(0,c.FD)(f.vwO,{componentId:"mlflow.logged_model.list.registered_model_cell_version_tag",css:x,children:["v",e.version]})]})},e.link)))})};var N=l(36506);const C=(e,t,l)=>`metrics.${JSON.stringify([e,t,l])}`,F=e=>{let{columnGroup:t}=e;const{onDatasetClicked:l}=(0,N.s7)(),{theme:o}=(0,n.u)(),[r,s]=(0,i.useState)(!1),d=(0,i.useMemo)((()=>{try{const e=t.getGroupId();return e?(e=>{try{const t=e.match(/metrics\.(.+)/);if(!t)return null;const l=t[1],[i,o,a]=JSON.parse(l);return i&&o?{datasetName:i,datasetDigest:o,runId:a}:null}catch{return null}})(e):null}catch{return null}}),[t]);return d?(0,c.FD)("span",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:o.spacing.xs},""),children:["Dataset:"," ",(0,c.FD)(n.T.Link,{css:(0,a.AH)({".anticon":{fontSize:o.general.iconFontSize},fontSize:o.typography.fontSizeBase,fontWeight:"normal",display:"flex",alignItems:"center",gap:o.spacing.xs},""),role:"button",componentId:"mlflow.logged_model.list.metric_by_dataset_column_header",onClick:async()=>{s(!0);try{await l({datasetName:d.datasetName,datasetDigest:d.datasetDigest,runId:d.runId})}finally{s(!1)}},children:[r?(0,c.Y)(n.S,{size:"small"}):(0,c.Y)(f.KbA,{}),d.datasetName," (#",d.datasetDigest,")"]})]}):(0,c.Y)(I.A,{id:"tkc+sv",defaultMessage:"No dataset"})};var T=l(43102);const O=e=>{let{data:t}=e;return(0,c.Y)(T.J,{loggedModel:t})};let z=function(e){return e.Attributes="attributes",e.Params="params",e}({}),B=function(e){return e.RelationshipType="relationship_type",e.Step="step",e.Select="select",e.Name="name",e.Status="status",e.CreationTime="creation_time",e.Source="source",e.SourceRun="source_run_id",e.RegisteredModels="registered_models",e.Dataset="dataset",e}({});const E="metrics.",L=[B.Select,B.Name,B.CreationTime],H=(e,t)=>e&&t?JSON.stringify([e,t]):"",W=e=>{const t=e.match(/metrics\.(.*?)(?:\.(.*))?$/);try{if(t){const[,e,l]=t;if(!l)return{datasetName:void 0,datasetDigest:void 0,metricKey:e};const[i,o]=JSON.parse(e);return{datasetName:i,datasetDigest:o,metricKey:l}}}catch(l){console.error("Failed to parse metric column ID",l)}return{datasetName:void 0,datasetDigest:void 0,metricKey:e}},V=[B.Name,B.Status,B.CreationTime,B.Source,B.SourceRun,B.RegisteredModels,B.Dataset],q=e=>{let{columnVisibility:t={},supportedAttributeColumnKeys:l=V,loggedModels:a=[],disablePinnedColumns:n=!1,disableOrderBy:r=!1,enableSortingByMetrics:s,orderByColumn:d,orderByAsc:u,isLoading:c}=e;const f=(0,i.useMemo)((()=>(e=>{const t={};for(const o of(0,h.orderBy)(e,(e=>{var t;return null===(t=e.info)||void 0===t?void 0:t.model_id})))for(const e of null!==(l=null===o||void 0===o||null===(i=o.data)||void 0===i?void 0:i.metrics)&&void 0!==l?l:[]){var l,i;if(!e.key)continue;const o=e.dataset_name&&e.dataset_digest?H(e.dataset_name,e.dataset_digest):"";t[o]||(t[o]={datasetName:e.dataset_name,datasetDigest:e.dataset_digest,runId:e.run_id,metrics:[]}),t[o]&&!t[o].metrics.includes(e.key)&&t[o].metrics.push(e.key)}return t})(a)),[a]),A=(0,i.useMemo)((()=>(0,h.compact)((0,h.uniq)(a.map((e=>{var t,l;return null===e||void 0===e||null===(t=e.data)||void 0===t||null===(l=t.params)||void 0===l?void 0:l.map((e=>e.key))})).flat()))),[a]),b=(0,o.A)();return((e,t,l)=>{const o=(0,i.useRef)();return o.current&&((0,h.isEqual)(t,o.current.deps)||l)||(o.current={deps:t,value:e()}),o.current.value})((()=>{const e=[{colId:B.RelationshipType,headerName:"Type",sortable:!1,valueGetter:e=>{let{data:t}=e;return"input"===t.direction?b.formatMessage({id:"wusz4l",defaultMessage:"Input"}):b.formatMessage({id:"jULSKd",defaultMessage:"Output"})},pinned:n?void 0:"left",resizable:!1,width:100},{colId:B.Step,headerName:b.formatMessage({id:"jOyo3+",defaultMessage:"Step"}),field:"step",pinned:n?void 0:"left",resizable:!1,width:60},{headerName:b.formatMessage({id:"y2oQyU",defaultMessage:"Model name"}),colId:B.Name,cellRenderer:m,resizable:!0,pinned:n?void 0:"left",minWidth:140,flex:1},{headerName:b.formatMessage({id:"atcZM5",defaultMessage:"Status"}),cellRenderer:p.a,colId:B.Status,pinned:n?void 0:"left",width:140,resizable:!1},{headerName:b.formatMessage({id:"xIcbik",defaultMessage:"Created"}),field:"info.creation_timestamp_ms",colId:B.CreationTime,cellRenderer:g.P,resizable:!0,pinned:n?void 0:"left",sortable:!r,sortingOrder:["desc","asc"],comparator:()=>0},{headerName:b.formatMessage({id:"bOGBCO",defaultMessage:"Logged from"}),colId:B.Source,cellRenderer:O,resizable:!0},{headerName:b.formatMessage({id:"5xPlEu",defaultMessage:"Source run"}),colId:B.SourceRun,cellRenderer:y,resizable:!0},{headerName:b.formatMessage({id:"UtUq/x",defaultMessage:"Registered models"}),colId:B.RegisteredModels,cellRenderer:S,resizable:!0},{headerName:b.formatMessage({id:"rpqN8U",defaultMessage:"Dataset"}),colId:B.Dataset,cellRenderer:v,resizable:!0}],i=[{groupId:"attributes",headerName:b.formatMessage({id:"ZoBXpz",defaultMessage:"Model attributes"}),children:e.filter((e=>!e.colId||l.includes(e.colId)))}];(0,h.orderBy)((0,h.values)(f),(e=>null===e||void 0===e?void 0:e.datasetName)).forEach((e=>{var l;let{datasetDigest:o,datasetName:a,runId:n,metrics:c}=e;const m=!a||!o?"":`${a} (#${o})`;i.push({headerName:m,groupId:C(a,o,n),headerGroupComponent:F,children:null!==(l=null===c||void 0===c?void 0:c.map((e=>{const l=((e,t,l)=>t&&l?`${E}${H(t,l)}.${e}`:`${E}${e}`)(e,a,o);return{headerName:e,hide:!1===t[l],colId:l,valueGetter:t=>{let{data:l}=t;for(const n of null!==(i=null===(o=l.data)||void 0===o?void 0:o.metrics)&&void 0!==i?i:[]){var i,o;if(n.key===e&&(n.dataset_name===a||!a&&!n.dataset_name))return n.value}},resizable:!0,sortable:s&&!r,sortingOrder:["desc","asc"],comparator:()=>0,sort:s&&l===d?u?"asc":"desc":null}})))&&void 0!==l?l:[]})})),A.length>0&&i.push({headerName:b.formatMessage({id:"mmiHAX",defaultMessage:"Parameters"}),groupId:"params",children:A.map((e=>({headerName:e,colId:`params.${e}`,hide:!1===t[`params.${e}`],valueGetter:t=>{let{data:l}=t;for(const a of null!==(i=null===(o=l.data)||void 0===o?void 0:o.params)&&void 0!==i?i:[]){var i,o;if(a.key===e)return a.value}},resizable:!0})))});const o=[{headerCheckboxSelection:!1,checkboxSelection:!1,width:40,maxWidth:40,resizable:!1,colId:B.Select,cellRenderer:R,headerComponent:D,flex:void 0},{headerName:b.formatMessage({id:"y2oQyU",defaultMessage:"Model name"}),colId:B.Name,cellRenderer:m,resizable:!0,flex:1}];return{columnDefs:i,compactColumnDefs:o}}),[f,A,l],c)}}}]);
//# sourceMappingURL=7951.8e3b93aa.chunk.js.map