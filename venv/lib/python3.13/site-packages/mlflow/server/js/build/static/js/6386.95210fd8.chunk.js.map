{"version": 3, "file": "static/js/6386.95210fd8.chunk.js", "mappings": "kHAEAA,EAAOC,QAAU,CACf,EAAQ,kBACR,EAAQ,aACR,EAAQ,GACR,KAAQ,aACR,KAAQ,eACR,KAAQ,aACR,KAAQ,sBACR,KAAQ,eACR,KAAQ,uB,iCCiBVD,EAAOC,QAzBP,WAEEC,KAAKC,MAAQ,KACbD,KAAKE,QAAU,EAEfF,KAAKG,SAAW,EAEhBH,KAAKI,SAAW,EAEhBJ,KAAKK,OAAS,KACdL,KAAKM,SAAW,EAEhBN,KAAKO,UAAY,EAEjBP,KAAKQ,UAAY,EAEjBR,KAAKS,IAAM,GAEXT,KAAKU,MAAQ,KAEbV,KAAKW,UAAY,EAEjBX,KAAKY,MAAQ,CACf,C,iCCKAd,EAAOC,QAzBP,SAAiBa,EAAOC,EAAKC,EAAKC,GAKhC,IAJA,IAAIC,EAAc,MAARJ,EACNK,EAAOL,IAAU,GAAM,MACvBM,EAAI,EAEO,IAARJ,GAAW,CAKhBA,GADAI,EAAIJ,EAAM,IAAO,IAAOA,EAGxB,GAEEG,EAAMA,GADND,EAAMA,EAAKH,EAAIE,KAAS,GACR,UACPG,GAEXF,GAAM,MACNC,GAAM,KACR,CAEA,OAAQD,EAAMC,GAAM,EACtB,C,iCCLA,IAAIE,EAfJ,WAGE,IAFA,IAAIC,EAAGC,EAAQ,GAENH,EAAG,EAAGA,EAAI,IAAKA,IAAK,CAC3BE,EAAIF,EACJ,IAAK,IAAII,EAAG,EAAGA,EAAI,EAAGA,IACpBF,EAAQ,EAAFA,EAAQ,WAAcA,IAAM,EAAOA,IAAM,EAEjDC,EAAMH,GAAKE,CACb,CAEA,OAAOC,CACT,CAGeE,GAiBfzB,EAAOC,QAdP,SAAeyB,EAAKX,EAAKC,EAAKC,GAC5B,IAAIU,EAAIN,EACJO,EAAMX,EAAMD,EAEhBU,GAAMA,EAEN,IAAK,IAAIG,EAAIZ,EAAKY,EAAID,EAAKC,IACzBH,EAAOA,IAAQ,EAAKC,EAAmB,KAAhBD,EAAMX,EAAIc,KAGnC,OAAQH,CACV,C,mCClCA,IAAII,EAAmC,qBAAfC,YACgB,qBAAhBC,aACe,qBAAfC,WAGxBhC,EAAQiC,OAAS,SAAUC,GAEzB,IADA,IAAIC,EAAUC,MAAMC,UAAUC,MAAMC,KAAKC,UAAW,GAC7CL,EAAQM,QAAQ,CACrB,IAAIC,EAASP,EAAQQ,QACrB,GAAKD,EAAL,CAEA,GAAsB,kBAAXA,EACT,MAAM,IAAIE,UAAUF,EAAS,sBAG/B,IAAK,IAAIG,KAAKH,EACRA,EAAOI,eAAeD,KACxBX,EAAIW,GAAKH,EAAOG,GARK,CAW3B,CAEA,OAAOX,CACT,EAIAlC,EAAQ+C,UAAY,SAAUjC,EAAKkC,GACjC,OAAIlC,EAAI2B,SAAWO,EAAelC,EAC9BA,EAAImC,SAAmBnC,EAAImC,SAAS,EAAGD,IAC3ClC,EAAI2B,OAASO,EACNlC,EACT,EAGA,IAAIoC,EAAU,CACZC,SAAU,SAAUC,EAAMC,EAAKC,EAAUvC,EAAKwC,GAC5C,GAAIF,EAAIJ,UAAYG,EAAKH,SACvBG,EAAKI,IAAIH,EAAIJ,SAASK,EAAUA,EAASvC,GAAMwC,QAIjD,IAAK,IAAI3B,EAAE,EAAGA,EAAEb,EAAKa,IACnBwB,EAAKG,EAAY3B,GAAKyB,EAAIC,EAAW1B,EAEzC,EAEA6B,cAAe,SAASC,GACtB,IAAI9B,EAAG+B,EAAG5C,EAAKC,EAAK4C,EAAOC,EAI3B,IADA9C,EAAM,EACDa,EAAE,EAAG+B,EAAED,EAAOjB,OAAQb,EAAE+B,EAAG/B,IAC9Bb,GAAO2C,EAAO9B,GAAGa,OAMnB,IAFAoB,EAAS,IAAI/B,WAAWf,GACxBC,EAAM,EACDY,EAAE,EAAG+B,EAAED,EAAOjB,OAAQb,EAAE+B,EAAG/B,IAC9BgC,EAAQF,EAAO9B,GACfiC,EAAOL,IAAII,EAAO5C,GAClBA,GAAO4C,EAAMnB,OAGf,OAAOoB,CACT,GAGEC,EAAY,CACdX,SAAU,SAAUC,EAAMC,EAAKC,EAAUvC,EAAKwC,GAC5C,IAAK,IAAI3B,EAAE,EAAGA,EAAEb,EAAKa,IACnBwB,EAAKG,EAAY3B,GAAKyB,EAAIC,EAAW1B,EAEzC,EAEA6B,cAAe,SAASC,GACtB,MAAO,GAAGK,OAAOC,MAAM,GAAIN,EAC7B,GAMF1D,EAAQiE,SAAW,SAAUC,GACvBA,GACFlE,EAAQmE,KAAQrC,WAChB9B,EAAQoE,MAAQrC,YAChB/B,EAAQqE,MAAQrC,WAChBhC,EAAQiC,OAAOjC,EAASkD,KAExBlD,EAAQmE,KAAQ/B,MAChBpC,EAAQoE,MAAQhC,MAChBpC,EAAQqE,MAAQjC,MAChBpC,EAAQiC,OAAOjC,EAAS8D,GAE5B,EAEA9D,EAAQiE,SAASpC,E,qCClGjB,IAAIyC,EAAeC,EAAQ,OACvBC,EAAQD,EAAQ,OAChBE,EAAUF,EAAQ,OAClBlD,EAAIkD,EAAQ,OACZ7D,EAAM6D,EAAQ,MACdG,EAAUH,EAAQ,OAClBI,EAAWJ,EAAQ,OAEnBK,EAAWC,OAAOxC,UAAUuC,SAgF5BE,EAAU,SAASC,GAErB9E,KAAK8E,QAAUP,EAAMvC,OAAO,CAC1B+C,UAAW,MACXC,WAAY,EACZC,GAAI,IACHH,GAAW,CAAC,GAEf,IAAII,EAAMlF,KAAK8E,QAIXI,EAAIC,KAAQD,EAAIF,YAAc,GAAOE,EAAIF,WAAa,KACxDE,EAAIF,YAAcE,EAAIF,WACC,IAAnBE,EAAIF,aAAoBE,EAAIF,YAAc,OAI3CE,EAAIF,YAAc,GAAOE,EAAIF,WAAa,KACzCF,GAAWA,EAAQE,aACvBE,EAAIF,YAAc,IAKfE,EAAIF,WAAa,IAAQE,EAAIF,WAAa,IAGf,KAAR,GAAjBE,EAAIF,cACPE,EAAIF,YAAc,IAItBhF,KAAKoF,IAAS,EACdpF,KAAKS,IAAS,GACdT,KAAKqF,OAAS,EACdrF,KAAKyD,OAAS,GAEdzD,KAAKsF,KAAS,IAAIb,EAClBzE,KAAKsF,KAAK/E,UAAY,EAEtB,IAAIgF,EAAUlB,EAAamB,aACzBxF,KAAKsF,KACLJ,EAAIF,YAGN,GAAIO,IAAWnE,EAAEqE,KACf,MAAM,IAAIC,MAAMjF,EAAI8E,IAGtBvF,KAAK2F,OAAS,IAAIjB,EAElBL,EAAauB,iBAAiB5F,KAAKsF,KAAMtF,KAAK2F,OAChD,EAkMA,SAASE,EAAQ5F,EAAO6E,GACtB,IAAIgB,EAAW,IAAIjB,EAAQC,GAK3B,GAHAgB,EAASC,KAAK9F,GAAO,GAGjB6F,EAASV,IAAO,MAAMU,EAASrF,IAEnC,OAAOqF,EAASlC,MAClB,CA7KAiB,EAAQzC,UAAU2D,KAAO,SAASC,EAAMC,GACtC,IAEIV,EAAQW,EACRC,EAAeC,EAAMC,EAHrBf,EAAOtF,KAAKsF,KACZP,EAAY/E,KAAK8E,QAAQC,UAI7B,GAAI/E,KAAKqF,MAAS,OAAO,EACzBa,EAASD,MAAWA,EAAQA,GAAkB,IAATA,EAAiB7E,EAAEkF,SAAWlF,EAAEmF,WAGjD,kBAATP,EAETV,EAAKrF,MAAQuE,EAAQgC,cAAcR,GACF,yBAAxBrB,EAASrC,KAAK0D,GACvBV,EAAKrF,MAAQ,IAAI4B,WAAWmE,GAE5BV,EAAKrF,MAAQ+F,EAGfV,EAAKpF,QAAU,EACfoF,EAAKnF,SAAWmF,EAAKrF,MAAMuC,OAE3B,EAAG,CASD,GARuB,IAAnB8C,EAAK/E,YACP+E,EAAKjF,OAAS,IAAIkE,EAAML,KAAKa,GAC7BO,EAAKhF,SAAW,EAChBgF,EAAK/E,UAAYwE,IAGnBQ,EAASlB,EAAawB,QAAQP,EAAMlE,EAAEmF,eAEvBnF,EAAEqF,cAAgBlB,IAAWnE,EAAEqE,KAG5C,OAFAzF,KAAK0G,MAAMnB,GACXvF,KAAKqF,OAAQ,GACN,EAGLC,EAAKhF,WACgB,IAAnBgF,EAAK/E,WAAmBgF,IAAWnE,EAAEqF,eAAmC,IAAlBnB,EAAKnF,UAAmB+F,IAAU9E,EAAEkF,UAAYJ,IAAU9E,EAAEuF,gBAE5F,WAApB3G,KAAK8E,QAAQG,IAEfkB,EAAgB3B,EAAQoC,WAAWtB,EAAKjF,OAAQiF,EAAKhF,UAErD8F,EAAOd,EAAKhF,SAAW6F,EACvBE,EAAU7B,EAAQqC,WAAWvB,EAAKjF,OAAQ8F,GAG1Cb,EAAKhF,SAAW8F,EAChBd,EAAK/E,UAAYwE,EAAYqB,EACzBA,GAAQ7B,EAAMrB,SAASoC,EAAKjF,OAAQiF,EAAKjF,OAAQ8F,EAAeC,EAAM,GAE1EpG,KAAK8G,OAAOT,IAGZrG,KAAK8G,OAAOvC,EAAMzB,UAAUwC,EAAKjF,OAAQiF,EAAKhF,YAItD,OAAUgF,EAAKnF,SAAW,GAAMoF,IAAWnE,EAAEqF,cAO7C,OALIlB,IAAWnE,EAAEqF,eACfP,EAAQ9E,EAAEkF,UAIRJ,IAAU9E,EAAEkF,UACdf,EAASlB,EAAa0C,WAAW/G,KAAKsF,MACtCtF,KAAK0G,MAAMnB,GACXvF,KAAKqF,OAAQ,EACNE,IAAWnE,EAAEqE,MAIlBS,IAAU9E,EAAEuF,eACd3G,KAAK0G,MAAMtF,EAAEqE,MACbH,EAAK/E,UAAY,GACV,EAIX,EAYAsE,EAAQzC,UAAU0E,OAAS,SAASnD,GAClC3D,KAAKyD,OAAOsC,KAAKpC,EACnB,EAaAkB,EAAQzC,UAAUsE,MAAQ,SAASnB,GAE7BA,IAAWnE,EAAEqE,OACS,WAApBzF,KAAK8E,QAAQG,GAGfjF,KAAK4D,OAAS5D,KAAKyD,OAAOuD,KAAK,IAE/BhH,KAAK4D,OAASW,EAAMf,cAAcxD,KAAKyD,SAG3CzD,KAAKyD,OAAS,GACdzD,KAAKoF,IAAMG,EACXvF,KAAKS,IAAMT,KAAKsF,KAAK7E,GACvB,EA+EAV,EAAQ8E,QAAUA,EAClB9E,EAAQ8F,QAAUA,EAClB9F,EAAQkH,WAnBR,SAAoBhH,EAAO6E,GAGzB,OAFAA,EAAUA,GAAW,CAAC,GACdK,KAAM,EACPU,EAAQ5F,EAAO6E,EACxB,EAgBA/E,EAAQmH,OAAUrB,C,qCCvXlB,IAAIsB,EAAe7C,EAAQ,OACvBC,EAAQD,EAAQ,OAChBE,EAAUF,EAAQ,OAClB7D,EAAM6D,EAAQ,MACdG,EAAUH,EAAQ,OAElBK,EAAWC,OAAOxC,UAAUuC,SA6G5ByC,EAAU,SAAStC,GAErB9E,KAAK8E,QAAUP,EAAMvC,OAAO,CAC1BqF,OApGwB,EAqGxBC,OAjGc,EAkGdvC,UAAW,MACXC,WAAY,GACZuC,SAAU,EACVC,SAvGwB,EAwGxBvC,GAAI,IACHH,GAAW,CAAC,GAEf,IAAII,EAAMlF,KAAK8E,QAEXI,EAAIC,KAAQD,EAAIF,WAAa,EAC/BE,EAAIF,YAAcE,EAAIF,WAGfE,EAAIuC,MAASvC,EAAIF,WAAa,GAAOE,EAAIF,WAAa,KAC7DE,EAAIF,YAAc,IAGpBhF,KAAKoF,IAAS,EACdpF,KAAKS,IAAS,GACdT,KAAKqF,OAAS,EACdrF,KAAKyD,OAAS,GAEdzD,KAAKsF,KAAO,IAAIb,EAChBzE,KAAKsF,KAAK/E,UAAY,EAEtB,IAAIgF,EAAS4B,EAAaO,aACxB1H,KAAKsF,KACLJ,EAAImC,MACJnC,EAAIoC,OACJpC,EAAIF,WACJE,EAAIqC,SACJrC,EAAIsC,UAGN,GA5IoB,IA4IhBjC,EACF,MAAM,IAAIG,MAAMjF,EAAI8E,IAGlBL,EAAIS,QACNwB,EAAaQ,iBAAiB3H,KAAKsF,KAAMJ,EAAIS,OAEjD,EAsKA,SAASiC,EAAQ3H,EAAO6E,GACtB,IAAI+C,EAAW,IAAIT,EAAQtC,GAK3B,GAHA+C,EAAS9B,KAAK9F,GAAO,GAGjB4H,EAASzC,IAAO,MAAMyC,EAASpH,IAEnC,OAAOoH,EAASjE,MAClB,CAhJAwD,EAAQhF,UAAU2D,KAAO,SAASC,EAAMC,GACtC,IAEIV,EAAQW,EAFRZ,EAAOtF,KAAKsF,KACZP,EAAY/E,KAAK8E,QAAQC,UAG7B,GAAI/E,KAAKqF,MAAS,OAAO,EAEzBa,EAASD,MAAWA,EAAQA,GAAkB,IAATA,EA3LjB,EADA,EA+LA,kBAATD,EAETV,EAAKrF,MAAQuE,EAAQsD,WAAW9B,GACC,yBAAxBrB,EAASrC,KAAK0D,GACvBV,EAAKrF,MAAQ,IAAI4B,WAAWmE,GAE5BV,EAAKrF,MAAQ+F,EAGfV,EAAKpF,QAAU,EACfoF,EAAKnF,SAAWmF,EAAKrF,MAAMuC,OAE3B,EAAG,CAQD,GAPuB,IAAnB8C,EAAK/E,YACP+E,EAAKjF,OAAS,IAAIkE,EAAML,KAAKa,GAC7BO,EAAKhF,SAAW,EAChBgF,EAAK/E,UAAYwE,GA3MD,KA6MlBQ,EAAS4B,EAAaS,QAAQtC,EAAMY,KA9MlB,IAgNaX,EAG7B,OAFAvF,KAAK0G,MAAMnB,GACXvF,KAAKqF,OAAQ,GACN,EAEc,IAAnBC,EAAK/E,YAAsC,IAAlB+E,EAAKnF,UAvNhB,IAuNmC+F,GAnNnC,IAmNyDA,KACjD,WAApBlG,KAAK8E,QAAQG,GACfjF,KAAK8G,OAAOtC,EAAQuD,cAAcxD,EAAMzB,UAAUwC,EAAKjF,OAAQiF,EAAKhF,YAEpEN,KAAK8G,OAAOvC,EAAMzB,UAAUwC,EAAKjF,OAAQiF,EAAKhF,WAGpD,QAAUgF,EAAKnF,SAAW,GAAwB,IAAnBmF,EAAK/E,YA3NhB,IA2NoCgF,GAGxD,OAjOoB,IAiOhBW,GACFX,EAAS4B,EAAaa,WAAWhI,KAAKsF,MACtCtF,KAAK0G,MAAMnB,GACXvF,KAAKqF,OAAQ,EAlOK,IAmOXE,GAjOW,IAqOhBW,IACFlG,KAAK0G,MAxOa,GAyOlBpB,EAAK/E,UAAY,GACV,EAIX,EAYA6G,EAAQhF,UAAU0E,OAAS,SAASnD,GAClC3D,KAAKyD,OAAOsC,KAAKpC,EACnB,EAaAyD,EAAQhF,UAAUsE,MAAQ,SAASnB,GAzQb,IA2QhBA,IACsB,WAApBvF,KAAK8E,QAAQG,GACfjF,KAAK4D,OAAS5D,KAAKyD,OAAOuD,KAAK,IAE/BhH,KAAK4D,OAASW,EAAMf,cAAcxD,KAAKyD,SAG3CzD,KAAKyD,OAAS,GACdzD,KAAKoF,IAAMG,EACXvF,KAAKS,IAAMT,KAAKsF,KAAK7E,GACvB,EA8EAV,EAAQqH,QAAUA,EAClBrH,EAAQ6H,QAAUA,EAClB7H,EAAQkI,WAxBR,SAAoBhI,EAAO6E,GAGzB,OAFAA,EAAUA,GAAW,CAAC,GACdK,KAAM,EACPyC,EAAQ3H,EAAO6E,EACxB,EAqBA/E,EAAQ0H,KAVR,SAAcxH,EAAO6E,GAGnB,OAFAA,EAAUA,GAAW,CAAC,GACd2C,MAAO,EACRG,EAAQ3H,EAAO6E,EACxB,C,qCC/WA,IAAIP,EAAUD,EAAQ,OAClB4D,EAAU5D,EAAQ,OAClB6D,EAAU7D,EAAQ,OAClB8D,EAAU9D,EAAQ,OAClB7D,EAAQ6D,EAAQ,MAuBhB+D,GAAmB,EAwDnBC,EAAY,IACZC,EAAiBD,IAQjBE,EAAa,IACbC,EAAa,IACbC,EAAe,IASnB,SAAStD,EAAIE,EAAMqD,GAEjB,OADArD,EAAK7E,IAAMA,EAAIkI,GACRA,CACT,CAEA,SAASC,EAAKC,GACZ,OAASA,GAAM,IAAOA,EAAK,EAAI,EAAI,EACrC,CAEA,SAASC,EAAKjI,GAA6B,IAAtB,IAAIC,EAAMD,EAAI2B,SAAiB1B,GAAO,GAAKD,EAAIC,GAAO,CAAK,CAShF,SAASiI,EAAczD,GACrB,IAAI0D,EAAI1D,EAAK5E,MAGTI,EAAMkI,EAAEC,QACRnI,EAAMwE,EAAK/E,YACbO,EAAMwE,EAAK/E,WAED,IAARO,IAEJyD,EAAMrB,SAASoC,EAAKjF,OAAQ2I,EAAEE,YAAaF,EAAEG,YAAarI,EAAKwE,EAAKhF,UACpEgF,EAAKhF,UAAYQ,EACjBkI,EAAEG,aAAerI,EACjBwE,EAAK9E,WAAaM,EAClBwE,EAAK/E,WAAaO,EAClBkI,EAAEC,SAAWnI,EACK,IAAdkI,EAAEC,UACJD,EAAEG,YAAc,GAEpB,CAGA,SAASC,EAAkBJ,EAAGK,GAC5BnB,EAAMoB,gBAAgBN,EAAIA,EAAEO,aAAe,EAAIP,EAAEO,aAAe,EAAIP,EAAEQ,SAAWR,EAAEO,YAAaF,GAChGL,EAAEO,YAAcP,EAAEQ,SAClBT,EAAcC,EAAE1D,KAClB,CAGA,SAASmE,EAAST,EAAGU,GACnBV,EAAEE,YAAYF,EAAEC,WAAaS,CAC/B,CAQA,SAASC,EAAYX,EAAGU,GAGtBV,EAAEE,YAAYF,EAAEC,WAAcS,IAAM,EAAK,IACzCV,EAAEE,YAAYF,EAAEC,WAAiB,IAAJS,CAC/B,CAUA,SAASE,EAAStE,EAAMzE,EAAKgJ,EAAO9G,GAClC,IAAIjC,EAAMwE,EAAKnF,SAGf,OADIW,EAAMiC,IAAQjC,EAAMiC,GACZ,IAARjC,EAAoB,GAExBwE,EAAKnF,UAAYW,EAEjByD,EAAMrB,SAASrC,EAAKyE,EAAKrF,MAAOqF,EAAKpF,QAASY,EAAK+I,GAC3B,IAApBvE,EAAK5E,MAAMoJ,KACbxE,EAAK1E,MAAQuH,EAAQ7C,EAAK1E,MAAOC,EAAKC,EAAK+I,GAGhB,IAApBvE,EAAK5E,MAAMoJ,OAClBxE,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOC,EAAKC,EAAK+I,IAG3CvE,EAAKpF,SAAWY,EAChBwE,EAAKlF,UAAYU,EAEVA,EACT,CAYA,SAASiJ,EAAcf,EAAGgB,GACxB,IAEIC,EACAnJ,EAHAoJ,EAAelB,EAAEmB,iBACjBC,EAAOpB,EAAEQ,SAGTa,EAAWrB,EAAEsB,YACbC,EAAavB,EAAEuB,WACfC,EAASxB,EAAEQ,SAAYR,EAAEyB,OAASlC,EAClCS,EAAEQ,UAAYR,EAAEyB,OAASlC,GAAiB,EAE1CmC,EAAO1B,EAAE2B,OAETC,EAAQ5B,EAAE6B,OACVC,EAAQ9B,EAAE8B,KAMVC,EAAS/B,EAAEQ,SAAWlB,EACtB0C,EAAaN,EAAKN,EAAOC,EAAW,GACpCY,EAAaP,EAAKN,EAAOC,GAQzBrB,EAAEsB,aAAetB,EAAEkC,aACrBhB,IAAiB,GAKfK,EAAavB,EAAEmC,YAAaZ,EAAavB,EAAEmC,WAI/C,GAaE,GAAIT,GAXJT,EAAQD,GAWSK,KAAkBY,GAC/BP,EAAKT,EAAQI,EAAW,KAAOW,GAC/BN,EAAKT,KAA0BS,EAAKN,IACpCM,IAAOT,KAAwBS,EAAKN,EAAO,GAH/C,CAaAA,GAAQ,EACRH,IAMA,UAESS,IAAON,KAAUM,IAAOT,IAAUS,IAAON,KAAUM,IAAOT,IAC1DS,IAAON,KAAUM,IAAOT,IAAUS,IAAON,KAAUM,IAAOT,IAC1DS,IAAON,KAAUM,IAAOT,IAAUS,IAAON,KAAUM,IAAOT,IAC1DS,IAAON,KAAUM,IAAOT,IAAUS,IAAON,KAAUM,IAAOT,IAC1DG,EAAOW,GAOhB,GAHAjK,EAAMwH,GAAayC,EAASX,GAC5BA,EAAOW,EAASzC,EAEZxH,EAAMuJ,EAAU,CAGlB,GAFArB,EAAEoC,YAAcpB,EAChBK,EAAWvJ,EACPA,GAAOyJ,EACT,MAEFS,EAAaN,EAAKN,EAAOC,EAAW,GACpCY,EAAaP,EAAKN,EAAOC,EAC3B,CApCA,SAqCQL,EAAYc,EAAKd,EAAYY,IAAUJ,GAA4B,MAAjBN,GAE5D,OAAIG,GAAYrB,EAAEmC,UACTd,EAEFrB,EAAEmC,SACX,CAaA,SAASE,EAAYrC,GACnB,IACIpG,EAAG1B,EAAGoK,EAAGC,EAAMC,EADfC,EAAUzC,EAAEyB,OAKhB,EAAG,CAqBD,GApBAc,EAAOvC,EAAE0C,YAAc1C,EAAEmC,UAAYnC,EAAEQ,SAoBnCR,EAAEQ,UAAYiC,GAAWA,EAAUlD,GAAgB,CAErDhE,EAAMrB,SAAS8F,EAAE2B,OAAQ3B,EAAE2B,OAAQc,EAASA,EAAS,GACrDzC,EAAEoC,aAAeK,EACjBzC,EAAEQ,UAAYiC,EAEdzC,EAAEO,aAAekC,EAUjB7I,EADA1B,EAAI8H,EAAE2C,UAEN,GACEL,EAAItC,EAAE4C,OAAOhJ,GACboG,EAAE4C,KAAKhJ,GAAM0I,GAAKG,EAAUH,EAAIG,EAAU,UACjCvK,GAGX0B,EADA1B,EAAIuK,EAEJ,GACEH,EAAItC,EAAE8B,OAAOlI,GACboG,EAAE8B,KAAKlI,GAAM0I,GAAKG,EAAUH,EAAIG,EAAU,UAIjCvK,GAEXqK,GAAQE,CACV,CACA,GAAwB,IAApBzC,EAAE1D,KAAKnF,SACT,MAmBF,GAJAe,EAAI0I,EAASZ,EAAE1D,KAAM0D,EAAE2B,OAAQ3B,EAAEQ,SAAWR,EAAEmC,UAAWI,GACzDvC,EAAEmC,WAAajK,EAGX8H,EAAEmC,UAAYnC,EAAE6C,QAhUR,EAyUV,IARAL,EAAMxC,EAAEQ,SAAWR,EAAE6C,OACrB7C,EAAE8C,MAAQ9C,EAAE2B,OAAOa,GAGnBxC,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAOa,EAAM,IAAMxC,EAAEgD,UAIvDhD,EAAE6C,SAEP7C,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAOa,EA3UxC,EA2UwD,IAAMxC,EAAEgD,UAExEhD,EAAE8B,KAAKU,EAAMxC,EAAE6B,QAAU7B,EAAE4C,KAAK5C,EAAE8C,OAClC9C,EAAE4C,KAAK5C,EAAE8C,OAASN,EAClBA,IACAxC,EAAE6C,WACE7C,EAAEmC,UAAYnC,EAAE6C,OAjVZ,MA0Vd,OAAS7C,EAAEmC,UAAY5C,GAAqC,IAApBS,EAAE1D,KAAKnF,SAsCjD,CA6GA,SAAS8L,EAAajD,EAAGkD,GAIvB,IAHA,IAAIC,EACAC,IAEK,CAMP,GAAIpD,EAAEmC,UAAY5C,EAAe,CAE/B,GADA8C,EAAYrC,GACRA,EAAEmC,UAAY5C,GAhkBF,IAgkBmB2D,EACjC,OA5egB,EA8elB,GAAoB,IAAhBlD,EAAEmC,UACJ,KAEJ,CAyBA,GApBAgB,EAAY,EACRnD,EAAEmC,WArgBM,IAugBVnC,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAO3B,EAAEQ,SAvgBxC,EAugB+D,IAAMR,EAAEgD,UACjFG,EAAYnD,EAAE8B,KAAK9B,EAAEQ,SAAWR,EAAE6B,QAAU7B,EAAE4C,KAAK5C,EAAE8C,OACrD9C,EAAE4C,KAAK5C,EAAE8C,OAAS9C,EAAEQ,UAOJ,IAAd2C,GAA4BnD,EAAEQ,SAAW2C,GAAenD,EAAEyB,OAASlC,IAKrES,EAAEqD,aAAetC,EAAcf,EAAGmD,IAGhCnD,EAAEqD,cAxhBM,EAoiBV,GAPAD,EAASlE,EAAMoE,UAAUtD,EAAGA,EAAEQ,SAAWR,EAAEoC,YAAapC,EAAEqD,aA7hBhD,GA+hBVrD,EAAEmC,WAAanC,EAAEqD,aAKbrD,EAAEqD,cAAgBrD,EAAEuD,gBAAuCvD,EAAEmC,WApiBvD,EAoiB+E,CACvFnC,EAAEqD,eACF,GACErD,EAAEQ,WAEFR,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAO3B,EAAEQ,SAziB5C,EAyiBmE,IAAMR,EAAEgD,UACjFG,EAAYnD,EAAE8B,KAAK9B,EAAEQ,SAAWR,EAAE6B,QAAU7B,EAAE4C,KAAK5C,EAAE8C,OACrD9C,EAAE4C,KAAK5C,EAAE8C,OAAS9C,EAAEQ,eAKQ,MAAnBR,EAAEqD,cACbrD,EAAEQ,UACJ,MAEER,EAAEQ,UAAYR,EAAEqD,aAChBrD,EAAEqD,aAAe,EACjBrD,EAAE8C,MAAQ9C,EAAE2B,OAAO3B,EAAEQ,UAErBR,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAO3B,EAAEQ,SAAW,IAAMR,EAAEgD,eAavEI,EAASlE,EAAMoE,UAAUtD,EAAG,EAAGA,EAAE2B,OAAO3B,EAAEQ,WAE1CR,EAAEmC,YACFnC,EAAEQ,WAEJ,GAAI4C,IAEFhD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WACT,OAhkBgB,CAokBtB,CAEA,OADAyI,EAAE6C,OAAW7C,EAAEQ,SAAYgD,EAAgBxD,EAAEQ,SAAWgD,EAtpBpC,IAupBhBN,GAEF9C,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,UAvkBS,EACA,GA4kBlByI,EAAEyD,WAEJrD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WAllBS,EACA,CAulBxB,CAOA,SAASmM,EAAa1D,EAAGkD,GAOvB,IANA,IAAIC,EACAC,EAEAO,IAGK,CAMP,GAAI3D,EAAEmC,UAAY5C,EAAe,CAE/B,GADA8C,EAAYrC,GACRA,EAAEmC,UAAY5C,GAnsBF,IAmsBmB2D,EACjC,OA/mBgB,EAinBlB,GAAoB,IAAhBlD,EAAEmC,UAAmB,KAC3B,CAyCA,GApCAgB,EAAY,EACRnD,EAAEmC,WAtoBM,IAwoBVnC,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAO3B,EAAEQ,SAxoBxC,EAwoB+D,IAAMR,EAAEgD,UACjFG,EAAYnD,EAAE8B,KAAK9B,EAAEQ,SAAWR,EAAE6B,QAAU7B,EAAE4C,KAAK5C,EAAE8C,OACrD9C,EAAE4C,KAAK5C,EAAE8C,OAAS9C,EAAEQ,UAMtBR,EAAEsB,YAActB,EAAEqD,aAClBrD,EAAE4D,WAAa5D,EAAEoC,YACjBpC,EAAEqD,aAAeG,EAEC,IAAdL,GAA0BnD,EAAEsB,YAActB,EAAEuD,gBAC5CvD,EAAEQ,SAAW2C,GAAcnD,EAAEyB,OAAOlC,IAKtCS,EAAEqD,aAAetC,EAAcf,EAAGmD,GAG9BnD,EAAEqD,cAAgB,IAtsBA,IAusBlBrD,EAAExB,UA9pBI,IA8pBwBwB,EAAEqD,cAA8BrD,EAAEQ,SAAWR,EAAEoC,YAAc,QAK7FpC,EAAEqD,aAAeG,IAMjBxD,EAAEsB,aAzqBM,GAyqBsBtB,EAAEqD,cAAgBrD,EAAEsB,YAAa,CACjEqC,EAAa3D,EAAEQ,SAAWR,EAAEmC,UA1qBlB,EAirBViB,EAASlE,EAAMoE,UAAUtD,EAAGA,EAAEQ,SAAW,EAAGR,EAAE4D,WAAY5D,EAAEsB,YAjrBlD,GAurBVtB,EAAEmC,WAAanC,EAAEsB,YAAY,EAC7BtB,EAAEsB,aAAe,EACjB,KACQtB,EAAEQ,UAAYmD,IAElB3D,EAAE8C,OAAU9C,EAAE8C,OAAS9C,EAAE+C,WAAc/C,EAAE2B,OAAO3B,EAAEQ,SA5rB5C,EA4rBmE,IAAMR,EAAEgD,UACjFG,EAAYnD,EAAE8B,KAAK9B,EAAEQ,SAAWR,EAAE6B,QAAU7B,EAAE4C,KAAK5C,EAAE8C,OACrD9C,EAAE4C,KAAK5C,EAAE8C,OAAS9C,EAAEQ,gBAGK,MAAlBR,EAAEsB,aAKb,GAJAtB,EAAE6D,gBAAkB,EACpB7D,EAAEqD,aAAeG,EACjBxD,EAAEQ,WAEE4C,IAEFhD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WACT,OA5rBc,CAisBpB,MAAO,GAAIyI,EAAE6D,iBAgBX,IATAT,EAASlE,EAAMoE,UAAUtD,EAAG,EAAGA,EAAE2B,OAAO3B,EAAEQ,SAAS,MAIjDJ,EAAiBJ,GAAG,GAGtBA,EAAEQ,WACFR,EAAEmC,YACuB,IAArBnC,EAAE1D,KAAK/E,UACT,OAltBgB,OAwtBlByI,EAAE6D,gBAAkB,EACpB7D,EAAEQ,WACFR,EAAEmC,WAEN,CAUA,OARInC,EAAE6D,kBAGJT,EAASlE,EAAMoE,UAAUtD,EAAG,EAAGA,EAAE2B,OAAO3B,EAAEQ,SAAS,IAEnDR,EAAE6D,gBAAkB,GAEtB7D,EAAE6C,OAAS7C,EAAEQ,SAAWgD,EAAcxD,EAAEQ,SAAWgD,EAtzB/B,IAuzBhBN,GAEF9C,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,UAvuBS,EACA,GA4uBlByI,EAAEyD,WAEJrD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WAlvBS,EACA,CAwvBxB,CAgKA,IAQIuM,EARAC,EAAS,SAAUC,EAAaC,EAAUC,EAAaC,EAAWC,GACpEpN,KAAKgN,YAAcA,EACnBhN,KAAKiN,SAAWA,EAChBjN,KAAKkN,YAAcA,EACnBlN,KAAKmN,UAAYA,EACjBnN,KAAKoN,KAAOA,CACd,EA8CA,SAASC,IACPrN,KAAKsF,KAAO,KACZtF,KAAKuF,OAAS,EACdvF,KAAKkJ,YAAc,KACnBlJ,KAAKsN,iBAAmB,EACxBtN,KAAKmJ,YAAc,EACnBnJ,KAAKiJ,QAAU,EACfjJ,KAAK8J,KAAO,EACZ9J,KAAKuN,OAAS,KACdvN,KAAKwN,QAAU,EACfxN,KAAKsH,OAhgCW,EAigChBtH,KAAKyN,YAAc,EAEnBzN,KAAKyK,OAAS,EACdzK,KAAK0N,OAAS,EACd1N,KAAK6K,OAAS,EAEd7K,KAAK2K,OAAS,KAQd3K,KAAK0L,YAAc,EAKnB1L,KAAK8K,KAAO,KAMZ9K,KAAK4L,KAAO,KAEZ5L,KAAK8L,MAAQ,EACb9L,KAAK2L,UAAY,EACjB3L,KAAK2N,UAAY,EACjB3N,KAAKgM,UAAY,EAEjBhM,KAAK+L,WAAa,EAOlB/L,KAAKuJ,YAAc,EAKnBvJ,KAAKqM,aAAe,EACpBrM,KAAK4M,WAAa,EAClB5M,KAAK6M,gBAAkB,EACvB7M,KAAKwJ,SAAW,EAChBxJ,KAAKoL,YAAc,EACnBpL,KAAKmL,UAAY,EAEjBnL,KAAKsK,YAAc,EAKnBtK,KAAKmK,iBAAmB,EAMxBnK,KAAKuM,eAAiB,EAYtBvM,KAAKqH,MAAQ,EACbrH,KAAKwH,SAAW,EAEhBxH,KAAKkL,WAAa,EAGlBlL,KAAKuK,WAAa,EAYlBvK,KAAK4N,UAAa,IAAIrJ,EAAMJ,MAAM0J,MAClC7N,KAAK8N,UAAa,IAAIvJ,EAAMJ,MAAM,KAClCnE,KAAK+N,QAAa,IAAIxJ,EAAMJ,MAAM,IAClC2E,EAAK9I,KAAK4N,WACV9E,EAAK9I,KAAK8N,WACVhF,EAAK9I,KAAK+N,SAEV/N,KAAKgO,OAAW,KAChBhO,KAAKiO,OAAW,KAChBjO,KAAKkO,QAAW,KAGhBlO,KAAKmO,SAAW,IAAI5J,EAAMJ,MAAMiK,IAIhCpO,KAAKqO,KAAO,IAAI9J,EAAMJ,MAAM,KAC5B2E,EAAK9I,KAAKqO,MAEVrO,KAAKsO,SAAW,EAChBtO,KAAKuO,SAAW,EAKhBvO,KAAKwO,MAAQ,IAAIjK,EAAMJ,MAAM,KAC7B2E,EAAK9I,KAAKwO,OAIVxO,KAAKyO,MAAQ,EAEbzO,KAAK0O,YAAc,EAoBnB1O,KAAKyM,SAAW,EAEhBzM,KAAK2O,MAAQ,EAMb3O,KAAK4O,QAAU,EACf5O,KAAK6O,WAAa,EAClB7O,KAAK8O,QAAU,EACf9O,KAAK6L,OAAS,EAGd7L,KAAK+O,OAAS,EAId/O,KAAKgP,SAAW,CAalB,CAGA,SAASC,EAAiB3J,GACxB,IAAI0D,EAEJ,OAAK1D,GAASA,EAAK5E,OAInB4E,EAAKlF,SAAWkF,EAAK9E,UAAY,EACjC8E,EAAK3E,UA/rCqB,GAisC1BqI,EAAI1D,EAAK5E,OACPuI,QAAU,EACZD,EAAEG,YAAc,EAEZH,EAAEc,KAAO,IACXd,EAAEc,MAAQd,EAAEc,MAGdd,EAAEzD,OAAUyD,EAAEc,KApqCC,GAoqCmBrB,EAClCnD,EAAK1E,MAAoB,IAAXoI,EAAEc,KACd,EAEA,EACFd,EAAEyE,WAtvCkB,EAuvCpBvF,EAAMgH,SAASlG,GA3uCK,GAutCX5D,EAAIE,EAAM+C,EAsBrB,CAGA,SAAS8G,EAAa7J,GACpB,IAlPe0D,EAkPXoG,EAAMH,EAAiB3J,GAI3B,OArvCoB,IAkvChB8J,KAnPWpG,EAoPL1D,EAAK5E,OAnPbgL,YAAc,EAAI1C,EAAEyB,OAGtB3B,EAAKE,EAAE4C,MAIP5C,EAAEuD,eAAiBO,EAAoB9D,EAAE3B,OAAO4F,SAChDjE,EAAEkC,WAAa4B,EAAoB9D,EAAE3B,OAAO2F,YAC5ChE,EAAEuB,WAAauC,EAAoB9D,EAAE3B,OAAO6F,YAC5ClE,EAAEmB,iBAAmB2C,EAAoB9D,EAAE3B,OAAO8F,UAElDnE,EAAEQ,SAAW,EACbR,EAAEO,YAAc,EAChBP,EAAEmC,UAAY,EACdnC,EAAE6C,OAAS,EACX7C,EAAEqD,aAAerD,EAAEsB,YAAckC,EACjCxD,EAAE6D,gBAAkB,EACpB7D,EAAE8C,MAAQ,GAmOHsD,CACT,CAWA,SAAS1H,EAAapC,EAAM+B,EAAOC,EAAQtC,EAAYuC,EAAUC,GAC/D,IAAKlC,EACH,OAAO+C,EAET,IAAIyB,EAAO,EAiBX,IAvwC0B,IAwvCtBzC,IACFA,EAAQ,GAGNrC,EAAa,GACf8E,EAAO,EACP9E,GAAcA,GAGPA,EAAa,KACpB8E,EAAO,EACP9E,GAAc,IAIZuC,EAAW,GAAKA,EAjvCF,GALF,IAsvCgCD,GAC9CtC,EAAa,GAAKA,EAAa,IAAMqC,EAAQ,GAAKA,EAAQ,GAC1DG,EAAW,GAAKA,EAnwCQ,EAowCxB,OAAOpC,EAAIE,EAAM+C,GAIA,IAAfrD,IACFA,EAAa,GAIf,IAAIgE,EAAI,IAAIqE,EAmCZ,OAjCA/H,EAAK5E,MAAQsI,EACbA,EAAE1D,KAAOA,EAET0D,EAAEc,KAAOA,EACTd,EAAEuE,OAAS,KACXvE,EAAE0E,OAAS1I,EACXgE,EAAEyB,OAAS,GAAKzB,EAAE0E,OAClB1E,EAAE6B,OAAS7B,EAAEyB,OAAS,EAEtBzB,EAAE2E,UAAYpG,EAAW,EACzByB,EAAE2C,UAAY,GAAK3C,EAAE2E,UACrB3E,EAAEgD,UAAYhD,EAAE2C,UAAY,EAC5B3C,EAAE+C,eAAiB/C,EAAE2E,UArvCP,EAqvC+B,GArvC/B,GAuvCd3E,EAAE2B,OAAS,IAAIpG,EAAML,KAAgB,EAAX8E,EAAEyB,QAC5BzB,EAAE4C,KAAO,IAAIrH,EAAMJ,MAAM6E,EAAE2C,WAC3B3C,EAAE8B,KAAO,IAAIvG,EAAMJ,MAAM6E,EAAEyB,QAK3BzB,EAAE0F,YAAc,GAAMnH,EAAW,EAEjCyB,EAAEsE,iBAAmC,EAAhBtE,EAAE0F,YACvB1F,EAAEE,YAAc,IAAI3E,EAAML,KAAK8E,EAAEsE,kBAEjCtE,EAAE2F,MAAQ3F,EAAE0F,aAAe,EAC3B1F,EAAEyF,MAAQ,EAAUzF,EAAE0F,YAEtB1F,EAAE3B,MAAQA,EACV2B,EAAExB,SAAWA,EACbwB,EAAE1B,OAASA,EAEJ6H,EAAa7J,EACtB,CA1VAwH,EAAsB,CAEpB,IAAIC,EAAO,EAAG,EAAG,EAAG,GAxiBtB,SAAwB/D,EAAGkD,GAIzB,IAAImD,EAAiB,MAOrB,IALIA,EAAiBrG,EAAEsE,iBAAmB,IACxC+B,EAAiBrG,EAAEsE,iBAAmB,KAI/B,CAEP,GAAItE,EAAEmC,WAAa,EAAG,CAUpB,GADAE,EAAYrC,GACQ,IAAhBA,EAAEmC,WAzeU,IAyeSe,EACvB,OArZgB,EAwZlB,GAAoB,IAAhBlD,EAAEmC,UACJ,KAGJ,CAIAnC,EAAEQ,UAAYR,EAAEmC,UAChBnC,EAAEmC,UAAY,EAGd,IAAImE,EAAYtG,EAAEO,YAAc8F,EAEhC,IAAmB,IAAfrG,EAAEQ,UAAkBR,EAAEQ,UAAY8F,KAEpCtG,EAAEmC,UAAYnC,EAAEQ,SAAW8F,EAC3BtG,EAAEQ,SAAW8F,EAEblG,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WACT,OA7agB,EAsbpB,GAAIyI,EAAEQ,SAAWR,EAAEO,aAAgBP,EAAEyB,OAASlC,IAE5Ca,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WACT,OA1bgB,CA8btB,CAIA,OAFAyI,EAAE6C,OAAS,EAjhBS,IAmhBhBK,GAEF9C,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,UAncS,EACA,IAyclByI,EAAEQ,SAAWR,EAAEO,cAEjBH,EAAiBJ,GAAG,GAChBA,EAAE1D,KAAK/E,WA/cS,EAsdxB,IAgdE,IAAIwM,EAAO,EAAG,EAAG,EAAG,EAAGd,GACvB,IAAIc,EAAO,EAAG,EAAG,GAAI,EAAGd,GACxB,IAAIc,EAAO,EAAG,EAAG,GAAI,GAAId,GAEzB,IAAIc,EAAO,EAAG,EAAG,GAAI,GAAIL,GACzB,IAAIK,EAAO,EAAG,GAAI,GAAI,GAAIL,GAC1B,IAAIK,EAAO,EAAG,GAAI,IAAK,IAAKL,GAC5B,IAAIK,EAAO,EAAG,GAAI,IAAK,IAAKL,GAC5B,IAAIK,EAAO,GAAI,IAAK,IAAK,KAAML,GAC/B,IAAIK,EAAO,GAAI,IAAK,IAAK,KAAML,IAksBjC3M,EAAQwP,YAlXR,SAAqBjK,EAAM+B,GACzB,OAAOK,EAAapC,EAAM+B,EAzyCV,EAOF,GAEI,EAnBQ,EAozC5B,EAiXAtH,EAAQ2H,aAAeA,EACvB3H,EAAQoP,aAAeA,EACvBpP,EAAQkP,iBAAmBA,EAC3BlP,EAAQ4H,iBArcR,SAA0BrC,EAAMsG,GAC9B,OAAKtG,GAASA,EAAK5E,MACK,IAApB4E,EAAK5E,MAAMoJ,KAAqBzB,GACpC/C,EAAK5E,MAAM6M,OAAS3B,EA5vCA,GA0vCevD,CAIrC,EAicAtI,EAAQ6H,QAlXR,SAAiBtC,EAAM4G,GACrB,IAAIsD,EAAWxG,EACXyG,EAAKC,EAET,IAAKpK,IAASA,EAAK5E,OACjBwL,EAz1CkB,GAy1CCA,EAAQ,EAC3B,OAAO5G,EAAOF,EAAIE,EAAM+C,GAAkBA,EAK5C,GAFAW,EAAI1D,EAAK5E,OAEJ4E,EAAKjF,SACJiF,EAAKrF,OAA2B,IAAlBqF,EAAKnF,UACpB6I,EAAEzD,SAAWmD,GAl2CE,IAk2CcwD,EAChC,OAAO9G,EAAIE,EAA0B,IAAnBA,EAAK/E,WAp1CL,EAo1CsC8H,GAQ1D,GALAW,EAAE1D,KAAOA,EACTkK,EAAYxG,EAAEyE,WACdzE,EAAEyE,WAAavB,EA/xCA,KAkyCXlD,EAAEzD,OAEJ,GAAe,IAAXyD,EAAEc,KACJxE,EAAK1E,MAAQ,EACb6I,EAAST,EAAG,IACZS,EAAST,EAAG,KACZS,EAAST,EAAG,GACPA,EAAEuE,QAaL9D,EAAST,GAAIA,EAAEuE,OAAOoC,KAAO,EAAI,IACpB3G,EAAEuE,OAAOqC,KAAO,EAAI,IACnB5G,EAAEuE,OAAOsC,MAAY,EAAJ,IACjB7G,EAAEuE,OAAOuC,KAAW,EAAJ,IAChB9G,EAAEuE,OAAOwC,QAAc,GAAJ,IAEjCtG,EAAST,EAAmB,IAAhBA,EAAEuE,OAAOyC,MACrBvG,EAAST,EAAIA,EAAEuE,OAAOyC,MAAQ,EAAK,KACnCvG,EAAST,EAAIA,EAAEuE,OAAOyC,MAAQ,GAAM,KACpCvG,EAAST,EAAIA,EAAEuE,OAAOyC,MAAQ,GAAM,KACpCvG,EAAST,EAAe,IAAZA,EAAE3B,MAAc,EACf2B,EAAExB,UA/2CK,GA+2CyBwB,EAAE3B,MAAQ,EAC1C,EAAI,GACjBoC,EAAST,EAAiB,IAAdA,EAAEuE,OAAO0C,IACjBjH,EAAEuE,OAAOsC,OAAS7G,EAAEuE,OAAOsC,MAAMrN,SACnCiH,EAAST,EAA2B,IAAxBA,EAAEuE,OAAOsC,MAAMrN,QAC3BiH,EAAST,EAAIA,EAAEuE,OAAOsC,MAAMrN,QAAU,EAAK,MAEzCwG,EAAEuE,OAAOqC,OACXtK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAS,IAE3DD,EAAEwE,QAAU,EACZxE,EAAEzD,OA30CQ,KAyyCVkE,EAAST,EAAG,GACZS,EAAST,EAAG,GACZS,EAAST,EAAG,GACZS,EAAST,EAAG,GACZS,EAAST,EAAG,GACZS,EAAST,EAAe,IAAZA,EAAE3B,MAAc,EACf2B,EAAExB,UA91CK,GA81CyBwB,EAAE3B,MAAQ,EAC1C,EAAI,GACjBoC,EAAST,EAryCH,GAsyCNA,EAAEzD,OAASkD,OA6Bf,CACE,IAAI9C,EAl3CQ,GAk3CiBqD,EAAE0E,OAAS,GAAM,IAAO,EAYrD/H,IATIqD,EAAExB,UAl4CgB,GAk4CcwB,EAAE3B,MAAQ,EAC9B,EACL2B,EAAE3B,MAAQ,EACL,EACO,IAAZ2B,EAAE3B,MACG,EAEA,IAEU,EACP,IAAf2B,EAAEQ,WAAkB7D,GAh2CZ,IAi2CZA,GAAU,GAAMA,EAAS,GAEzBqD,EAAEzD,OAASkD,EACXkB,EAAYX,EAAGrD,GAGI,IAAfqD,EAAEQ,WACJG,EAAYX,EAAG1D,EAAK1E,QAAU,IAC9B+I,EAAYX,EAAgB,MAAb1D,EAAK1E,QAEtB0E,EAAK1E,MAAQ,CACf,CAIF,GA72CgB,KA62CZoI,EAAEzD,OACJ,GAAIyD,EAAEuE,OAAOsC,MAAqB,CAGhC,IAFAJ,EAAMzG,EAAEC,QAEDD,EAAEwE,SAAmC,MAAxBxE,EAAEuE,OAAOsC,MAAMrN,UAC7BwG,EAAEC,UAAYD,EAAEsE,mBACdtE,EAAEuE,OAAOqC,MAAQ5G,EAAEC,QAAUwG,IAC/BnK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAUwG,EAAKA,IAEjE1G,EAAczD,GACdmK,EAAMzG,EAAEC,QACJD,EAAEC,UAAYD,EAAEsE,oBAItB7D,EAAST,EAA+B,IAA5BA,EAAEuE,OAAOsC,MAAM7G,EAAEwE,UAC7BxE,EAAEwE,UAEAxE,EAAEuE,OAAOqC,MAAQ5G,EAAEC,QAAUwG,IAC/BnK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAUwG,EAAKA,IAE7DzG,EAAEwE,UAAYxE,EAAEuE,OAAOsC,MAAMrN,SAC/BwG,EAAEwE,QAAU,EACZxE,EAAEzD,OAn4CO,GAq4Cb,MAEEyD,EAAEzD,OAv4CS,GA04Cf,GA14Ce,KA04CXyD,EAAEzD,OACJ,GAAIyD,EAAEuE,OAAOuC,KAAoB,CAC/BL,EAAMzG,EAAEC,QAGR,EAAG,CACD,GAAID,EAAEC,UAAYD,EAAEsE,mBACdtE,EAAEuE,OAAOqC,MAAQ5G,EAAEC,QAAUwG,IAC/BnK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAUwG,EAAKA,IAEjE1G,EAAczD,GACdmK,EAAMzG,EAAEC,QACJD,EAAEC,UAAYD,EAAEsE,kBAAkB,CACpCoC,EAAM,EACN,KACF,CAIAA,EADE1G,EAAEwE,QAAUxE,EAAEuE,OAAOuC,KAAKtN,OACkB,IAAxCwG,EAAEuE,OAAOuC,KAAKI,WAAWlH,EAAEwE,WAE3B,EAER/D,EAAST,EAAG0G,EACd,OAAiB,IAARA,GAEL1G,EAAEuE,OAAOqC,MAAQ5G,EAAEC,QAAUwG,IAC/BnK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAUwG,EAAKA,IAErD,IAARC,IACF1G,EAAEwE,QAAU,EACZxE,EAAEzD,OAx6CU,GA06ChB,MAEEyD,EAAEzD,OA56CY,GA+6ClB,GA/6CkB,KA+6CdyD,EAAEzD,OACJ,GAAIyD,EAAEuE,OAAOwC,QAAuB,CAClCN,EAAMzG,EAAEC,QAGR,EAAG,CACD,GAAID,EAAEC,UAAYD,EAAEsE,mBACdtE,EAAEuE,OAAOqC,MAAQ5G,EAAEC,QAAUwG,IAC/BnK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAUwG,EAAKA,IAEjE1G,EAAczD,GACdmK,EAAMzG,EAAEC,QACJD,EAAEC,UAAYD,EAAEsE,kBAAkB,CACpCoC,EAAM,EACN,KACF,CAIAA,EADE1G,EAAEwE,QAAUxE,EAAEuE,OAAOwC,QAAQvN,OACkB,IAA3CwG,EAAEuE,OAAOwC,QAAQG,WAAWlH,EAAEwE,WAE9B,EAER/D,EAAST,EAAG0G,EACd,OAAiB,IAARA,GAEL1G,EAAEuE,OAAOqC,MAAQ5G,EAAEC,QAAUwG,IAC/BnK,EAAK1E,MAAQwH,EAAM9C,EAAK1E,MAAOoI,EAAEE,YAAaF,EAAEC,QAAUwG,EAAKA,IAErD,IAARC,IACF1G,EAAEzD,OAASiD,EAEf,MAEEQ,EAAEzD,OAASiD,EAsBf,GAnBIQ,EAAEzD,SAAWiD,IACXQ,EAAEuE,OAAOqC,MACP5G,EAAEC,QAAU,EAAID,EAAEsE,kBACpBvE,EAAczD,GAEZ0D,EAAEC,QAAU,GAAKD,EAAEsE,mBACrB7D,EAAST,EAAgB,IAAb1D,EAAK1E,OACjB6I,EAAST,EAAI1D,EAAK1E,OAAS,EAAK,KAChC0E,EAAK1E,MAAQ,EACboI,EAAEzD,OAASkD,IAIbO,EAAEzD,OAASkD,GAMG,IAAdO,EAAEC,SAEJ,GADAF,EAAczD,GACS,IAAnBA,EAAK/E,UAQP,OADAyI,EAAEyE,YAAc,EApjDA,OA4jDb,GAAsB,IAAlBnI,EAAKnF,UAAkByI,EAAKsD,IAAUtD,EAAK4G,IApkDlC,IAqkDlBtD,EACA,OAAO9G,EAAIE,GAvjDO,GA2jDpB,GAAI0D,EAAEzD,SAAWmD,GAAkC,IAAlBpD,EAAKnF,SACpC,OAAOiF,EAAIE,GA5jDO,GAikDpB,GAAsB,IAAlBA,EAAKnF,UAAkC,IAAhB6I,EAAEmC,WAplDT,IAqlDjBe,GAAwBlD,EAAEzD,SAAWmD,EAAe,CACrD,IAAIyH,EAvjDoB,IAujDVnH,EAAExB,SAjqBpB,SAAsBwB,EAAGkD,GAGvB,IAFA,IAAIE,IAEK,CAEP,GAAoB,IAAhBpD,EAAEmC,YACJE,EAAYrC,GACQ,IAAhBA,EAAEmC,WAAiB,CACrB,GA77Bc,IA67BVe,EACF,OAz2Bc,EA22BhB,KACF,CAUF,GANAlD,EAAEqD,aAAe,EAGjBD,EAASlE,EAAMoE,UAAUtD,EAAG,EAAGA,EAAE2B,OAAO3B,EAAEQ,WAC1CR,EAAEmC,YACFnC,EAAEQ,WACE4C,IAEFhD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WACT,OA13BgB,CA83BtB,CAEA,OADAyI,EAAE6C,OAAS,EAh9BS,IAi9BhBK,GAEF9C,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,UAj4BS,EACA,GAs4BlByI,EAAEyD,WAEJrD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WA54BS,EACA,CAi5BxB,CA+mBmD6P,CAAapH,EAAGkD,GAtjDvC,IAujDrBlD,EAAExB,SAjwBT,SAAqBwB,EAAGkD,GAOtB,IANA,IAAIE,EACAtB,EACAV,EAAMW,EAENL,EAAO1B,EAAE2B,SAEJ,CAKP,GAAI3B,EAAEmC,WAAa7C,EAAW,CAE5B,GADA+C,EAAYrC,GACRA,EAAEmC,WAAa7C,GAp2BH,IAo2BgB4D,EAC9B,OAhxBgB,EAkxBlB,GAAoB,IAAhBlD,EAAEmC,UAAmB,KAC3B,CAIA,GADAnC,EAAEqD,aAAe,EACbrD,EAAEmC,WAryBM,GAqyBoBnC,EAAEQ,SAAW,IAE3CsB,EAAOJ,EADPN,EAAOpB,EAAEQ,SAAW,MAEPkB,IAAON,IAASU,IAASJ,IAAON,IAASU,IAASJ,IAAON,GAAO,CAC3EW,EAAS/B,EAAEQ,SAAWlB,EACtB,UAESwC,IAASJ,IAAON,IAASU,IAASJ,IAAON,IACzCU,IAASJ,IAAON,IAASU,IAASJ,IAAON,IACzCU,IAASJ,IAAON,IAASU,IAASJ,IAAON,IACzCU,IAASJ,IAAON,IAASU,IAASJ,IAAON,IACzCA,EAAOW,GAChB/B,EAAEqD,aAAe/D,GAAayC,EAASX,GACnCpB,EAAEqD,aAAerD,EAAEmC,YACrBnC,EAAEqD,aAAerD,EAAEmC,UAEvB,CAuBF,GAlBInC,EAAEqD,cA1zBM,GA8zBVD,EAASlE,EAAMoE,UAAUtD,EAAG,EAAGA,EAAEqD,aA9zBvB,GAg0BVrD,EAAEmC,WAAanC,EAAEqD,aACjBrD,EAAEQ,UAAYR,EAAEqD,aAChBrD,EAAEqD,aAAe,IAKjBD,EAASlE,EAAMoE,UAAUtD,EAAG,EAAGA,EAAE2B,OAAO3B,EAAEQ,WAE1CR,EAAEmC,YACFnC,EAAEQ,YAEA4C,IAEFhD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WACT,OAl0BgB,CAs0BtB,CAEA,OADAyI,EAAE6C,OAAS,EAx5BS,IAy5BhBK,GAEF9C,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,UAz0BS,EACA,GA80BlByI,EAAEyD,WAEJrD,EAAiBJ,GAAG,GACK,IAArBA,EAAE1D,KAAK/E,WAp1BS,EACA,CAy1BxB,CAwqB8B8P,CAAYrH,EAAGkD,GACrCY,EAAoB9D,EAAE3B,OAAO+F,KAAKpE,EAAGkD,GAKzC,GAtgDoB,IAmgDhBiE,GAlgDgB,IAkgDgBA,IAClCnH,EAAEzD,OAASmD,GAtgDO,IAwgDhByH,GAtgDgB,IAsgDWA,EAK7B,OAJuB,IAAnB7K,EAAK/E,YACPyI,EAAEyE,YAAc,GAnlDF,EA+lDlB,GArhDoB,IAqhDhB0C,IA1mDc,IA2mDZjE,EACFhE,EAAMoI,UAAUtH,GAxmDF,IA0mDPkD,IAEPhE,EAAMqI,iBAAiBvH,EAAG,EAAG,GAAG,GA9mDlB,IAknDVkD,IAEFpD,EAAKE,EAAE4C,MAEa,IAAhB5C,EAAEmC,YACJnC,EAAEQ,SAAW,EACbR,EAAEO,YAAc,EAChBP,EAAE6C,OAAS,KAIjB9C,EAAczD,GACS,IAAnBA,EAAK/E,WAEP,OADAyI,EAAEyE,YAAc,EAtnDF,CA0nDpB,CAIA,OAtoDoB,IAsoDhBvB,EA9nDgB,EA+nDhBlD,EAAEc,MAAQ,EA9nDM,GAioDL,IAAXd,EAAEc,MACJL,EAAST,EAAgB,IAAb1D,EAAK1E,OACjB6I,EAAST,EAAI1D,EAAK1E,OAAS,EAAK,KAChC6I,EAAST,EAAI1D,EAAK1E,OAAS,GAAM,KACjC6I,EAAST,EAAI1D,EAAK1E,OAAS,GAAM,KACjC6I,EAAST,EAAmB,IAAhB1D,EAAKlF,UACjBqJ,EAAST,EAAI1D,EAAKlF,UAAY,EAAK,KACnCqJ,EAAST,EAAI1D,EAAKlF,UAAY,GAAM,KACpCqJ,EAAST,EAAI1D,EAAKlF,UAAY,GAAM,OAIpCuJ,EAAYX,EAAG1D,EAAK1E,QAAU,IAC9B+I,EAAYX,EAAgB,MAAb1D,EAAK1E,QAGtBmI,EAAczD,GAIV0D,EAAEc,KAAO,IAAKd,EAAEc,MAAQd,EAAEc,MAET,IAAdd,EAAEC,QAxpDW,EACA,EAwpDtB,EAuCAlJ,EAAQiI,WArCR,SAAoB1C,GAClB,IAAIC,EAEJ,OAAKD,GAAsBA,EAAK5E,MA7lDjB,MAimDf6E,EAASD,EAAK5E,MAAM6E,SAhmDJ,KAkmDdA,GAjmDa,KAkmDbA,GAjmDgB,KAkmDhBA,GACAA,IAAWiD,GACXjD,IAAWkD,GACXlD,IAAWmD,EAEJtD,EAAIE,EAAM+C,IAGnB/C,EAAK5E,MAAQ,KAEN6E,IAAWkD,EAAarD,EAAIE,GA3qDf,GALA,GA+pDX+C,CAkBX,EAgBAtI,EAAQyQ,YAAc,oC,iCCjrDtB1Q,EAAOC,QAAU,SAAsBuF,EAAMuE,GAC3C,IAAInJ,EACA+P,EACApH,EACAqH,EACAjB,EACA/N,EAEAiP,EAEAC,EACAC,EACAC,EACAnG,EACAoG,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEAxQ,EACAyQ,EACAC,EACAC,EAGAxR,EAAOI,EAGXK,EAAQ4E,EAAK5E,MAEb+P,EAAMnL,EAAKpF,QACXD,EAAQqF,EAAKrF,MACboJ,EAAOoH,GAAOnL,EAAKnF,SAAW,GAC9BuQ,EAAOpL,EAAKhF,SACZD,EAASiF,EAAKjF,OACdoP,EAAMiB,GAAQ7G,EAAQvE,EAAK/E,WAC3BmB,EAAMgP,GAAQpL,EAAK/E,UAAY,KAE/BoQ,EAAOjQ,EAAMiQ,KAEbC,EAAQlQ,EAAMkQ,MACdC,EAAQnQ,EAAMmQ,MACdC,EAAQpQ,EAAMoQ,MACdnG,EAASjK,EAAMiK,OACfoG,EAAOrQ,EAAMqQ,KACbC,EAAOtQ,EAAMsQ,KACbC,EAAQvQ,EAAMgR,QACdR,EAAQxQ,EAAMiR,SACdR,GAAS,GAAKzQ,EAAMkR,SAAW,EAC/BR,GAAS,GAAK1Q,EAAMmR,UAAY,EAMhCC,EACA,EAAG,CACGd,EAAO,KACTD,GAAQ9Q,EAAMwQ,MAAUO,EACxBA,GAAQ,EACRD,GAAQ9Q,EAAMwQ,MAAUO,EACxBA,GAAQ,GAGVK,EAAOJ,EAAMF,EAAOI,GAEpBY,EACA,OAAS,CAKP,GAHAhB,KADAO,EAAKD,IAAS,GAEdL,GAAQM,EAEG,KADXA,EAAMD,IAAS,GAAM,KAKnBhR,EAAOqQ,KAAiB,MAAPW,MAEd,MAAS,GAALC,GAwKJ,IAAkB,KAAR,GAALA,GAAgB,CACxBD,EAAOJ,GAAc,MAAPI,IAA8BN,GAAS,GAAKO,GAAM,IAChE,SAASS,CACX,CACK,GAAS,GAALT,EAAS,CAEhB5Q,EAAMuF,KArSH,GAsSH,MAAM6L,CACR,CAEExM,EAAK7E,IAAM,8BACXC,EAAMuF,KA3SJ,GA4SF,MAAM6L,CACR,CApLEhR,EAAa,MAAPuQ,GACNC,GAAM,MAEAN,EAAOM,IACTP,GAAQ9Q,EAAMwQ,MAAUO,EACxBA,GAAQ,GAEVlQ,GAAOiQ,GAAS,GAAKO,GAAM,EAC3BP,KAAUO,EACVN,GAAQM,GAGNN,EAAO,KACTD,GAAQ9Q,EAAMwQ,MAAUO,EACxBA,GAAQ,EACRD,GAAQ9Q,EAAMwQ,MAAUO,EACxBA,GAAQ,GAEVK,EAAOH,EAAMH,EAAOK,GAEpBY,EACA,OAAS,CAMP,GAJAjB,KADAO,EAAKD,IAAS,GAEdL,GAAQM,IAGC,IAFTA,EAAMD,IAAS,GAAM,MAiIhB,IAAkB,KAAR,GAALC,GAAgB,CACxBD,EAAOH,GAAc,MAAPG,IAA8BN,GAAS,GAAKO,GAAM,IAChE,SAASU,CACX,CAEE1M,EAAK7E,IAAM,wBACXC,EAAMuF,KAzRR,GA0RE,MAAM6L,CACR,CA1HE,GAZAP,EAAc,MAAPF,EAEHL,GADJM,GAAM,MAEJP,GAAQ9Q,EAAMwQ,MAAUO,GACxBA,GAAQ,GACGM,IACTP,GAAQ9Q,EAAMwQ,MAAUO,EACxBA,GAAQ,KAGZO,GAAQR,GAAS,GAAKO,GAAM,GAEjBX,EAAM,CACfrL,EAAK7E,IAAM,gCACXC,EAAMuF,KAnKV,GAoKI,MAAM6L,CACR,CAMA,GAJAf,KAAUO,EACVN,GAAQM,EAGJC,GADJD,EAAKZ,EAAOjB,GACG,CAEb,IADA6B,EAAKC,EAAOD,GACHT,GACHnQ,EAAMuR,KAAM,CACd3M,EAAK7E,IAAM,gCACXC,EAAMuF,KAhLd,GAiLQ,MAAM6L,CACR,CA0BF,GAFAN,EAAO,EACPC,EAAc9G,EACA,IAAVmG,GAEF,GADAU,GAAQZ,EAAQU,EACZA,EAAKxQ,EAAK,CACZA,GAAOwQ,EACP,GACEjR,EAAOqQ,KAAU/F,EAAO6G,aACfF,GACXE,EAAOd,EAAOa,EACdE,EAAcpR,CAChB,OAEG,GAAIyQ,EAAQQ,GAGf,GAFAE,GAAQZ,EAAQE,EAAQQ,GACxBA,GAAMR,GACGhQ,EAAK,CACZA,GAAOwQ,EACP,GACEjR,EAAOqQ,KAAU/F,EAAO6G,aACfF,GAEX,GADAE,EAAO,EACHV,EAAQhQ,EAAK,CAEfA,GADAwQ,EAAKR,EAEL,GACEzQ,EAAOqQ,KAAU/F,EAAO6G,aACfF,GACXE,EAAOd,EAAOa,EACdE,EAAcpR,CAChB,CACF,OAIA,GADAmR,GAAQV,EAAQQ,EACZA,EAAKxQ,EAAK,CACZA,GAAOwQ,EACP,GACEjR,EAAOqQ,KAAU/F,EAAO6G,aACfF,GACXE,EAAOd,EAAOa,EACdE,EAAcpR,CAChB,CAEF,KAAOS,EAAM,GACXT,EAAOqQ,KAAUe,EAAYD,KAC7BnR,EAAOqQ,KAAUe,EAAYD,KAC7BnR,EAAOqQ,KAAUe,EAAYD,KAC7B1Q,GAAO,EAELA,IACFT,EAAOqQ,KAAUe,EAAYD,KACzB1Q,EAAM,IACRT,EAAOqQ,KAAUe,EAAYD,MAGnC,KACK,CACHA,EAAOd,EAAOa,EACd,GACElR,EAAOqQ,KAAUrQ,EAAOmR,KACxBnR,EAAOqQ,KAAUrQ,EAAOmR,KACxBnR,EAAOqQ,KAAUrQ,EAAOmR,KACxB1Q,GAAO,QACAA,EAAM,GACXA,IACFT,EAAOqQ,KAAUrQ,EAAOmR,KACpB1Q,EAAM,IACRT,EAAOqQ,KAAUrQ,EAAOmR,MAG9B,CAYF,KACF,CAeF,CAEA,KACF,CACF,OAASf,EAAMpH,GAAQqH,EAAOhP,GAI9B+O,GADA3P,EAAMkQ,GAAQ,EAGdD,IAAS,IADTC,GAAQlQ,GAAO,IACO,EAGtBwE,EAAKpF,QAAUuQ,EACfnL,EAAKhF,SAAWoQ,EAChBpL,EAAKnF,SAAYsQ,EAAMpH,EAAYA,EAAOoH,EAAZ,EAAmB,GAAKA,EAAMpH,GAC5D/D,EAAK/E,UAAamQ,EAAOhP,EAAaA,EAAMgP,EAAb,IAAqB,KAAOA,EAAOhP,GAClEhB,EAAMqQ,KAAOA,EACbrQ,EAAMsQ,KAAOA,CAEf,C,qCCjUA,IAAIzM,EAAQD,EAAQ,OAChB6D,EAAU7D,EAAQ,OAClB8D,EAAU9D,EAAQ,OAClB4N,EAAe5N,EAAQ,OACvB6N,EAAgB7N,EAAQ,OA2BxB+D,GAAmB,EAyBZ+J,EAAO,GAkBXC,EAAM,GAiBb,SAASC,EAAQC,GACf,OAAWA,IAAM,GAAM,MACbA,IAAM,EAAK,SACP,MAAJA,IAAe,KACX,IAAJA,IAAa,GACzB,CAGA,SAASC,IACPxS,KAAKiG,KAAO,EACZjG,KAAKqJ,MAAO,EACZrJ,KAAK8J,KAAO,EACZ9J,KAAKyS,UAAW,EAChBzS,KAAK0S,MAAQ,EACb1S,KAAK2Q,KAAO,EACZ3Q,KAAK2S,MAAQ,EACb3S,KAAK4S,MAAQ,EAEb5S,KAAK4L,KAAO,KAGZ5L,KAAK6S,MAAQ,EACb7S,KAAK4Q,MAAQ,EACb5Q,KAAK6Q,MAAQ,EACb7Q,KAAK8Q,MAAQ,EACb9Q,KAAK2K,OAAS,KAGd3K,KAAK+Q,KAAO,EACZ/Q,KAAKgR,KAAO,EAGZhR,KAAKwC,OAAS,EACdxC,KAAK8S,OAAS,EAGd9S,KAAK6P,MAAQ,EAGb7P,KAAK0R,QAAU,KACf1R,KAAK2R,SAAW,KAChB3R,KAAK4R,QAAU,EACf5R,KAAK6R,SAAW,EAGhB7R,KAAK+S,MAAQ,EACb/S,KAAKgT,KAAO,EACZhT,KAAKiT,MAAQ,EACbjT,KAAKkT,KAAO,EACZlT,KAAKmT,KAAO,KAEZnT,KAAKoT,KAAO,IAAI7O,EAAMJ,MAAM,KAC5BnE,KAAKqT,KAAO,IAAI9O,EAAMJ,MAAM,KAO5BnE,KAAKsT,OAAS,KACdtT,KAAKuT,QAAU,KACfvT,KAAKiS,KAAO,EACZjS,KAAKwT,KAAO,EACZxT,KAAKyT,IAAM,CACb,CAEA,SAASC,EAAiBpO,GACxB,IAAI5E,EAEJ,OAAK4E,GAASA,EAAK5E,OACnBA,EAAQ4E,EAAK5E,MACb4E,EAAKlF,SAAWkF,EAAK9E,UAAYE,EAAMkS,MAAQ,EAC/CtN,EAAK7E,IAAM,GACPC,EAAMoJ,OACRxE,EAAK1E,MAAqB,EAAbF,EAAMoJ,MAErBpJ,EAAMuF,KA1HM,EA2HZvF,EAAM2I,KAAO,EACb3I,EAAM+R,SAAW,EACjB/R,EAAMiQ,KAAO,MACbjQ,EAAMkL,KAAO,KACblL,EAAMqQ,KAAO,EACbrQ,EAAMsQ,KAAO,EAEbtQ,EAAMgR,QAAUhR,EAAM4S,OAAS,IAAI/O,EAAMH,MA7FzB,KA8FhB1D,EAAMiR,SAAWjR,EAAM6S,QAAU,IAAIhP,EAAMH,MA7F1B,KA+FjB1D,EAAMuR,KAAO,EACbvR,EAAM8S,MAAQ,EAxJM,GAqIenL,CAsBrC,CAEA,SAASsL,EAAarO,GACpB,IAAI5E,EAEJ,OAAK4E,GAASA,EAAK5E,QACnBA,EAAQ4E,EAAK5E,OACPkQ,MAAQ,EACdlQ,EAAMmQ,MAAQ,EACdnQ,EAAMoQ,MAAQ,EACP4C,EAAiBpO,IALW+C,CAOrC,CAEA,SAASuL,EAActO,EAAMN,GAC3B,IAAI8E,EACApJ,EAGJ,OAAK4E,GAASA,EAAK5E,OACnBA,EAAQ4E,EAAK5E,MAGTsE,EAAa,GACf8E,EAAO,EACP9E,GAAcA,IAGd8E,EAA2B,GAAnB9E,GAAc,GAClBA,EAAa,KACfA,GAAc,KAKdA,IAAeA,EAAa,GAAKA,EAAa,IACzCqD,GAEY,OAAjB3H,EAAMiK,QAAmBjK,EAAMmS,QAAU7N,IAC3CtE,EAAMiK,OAAS,MAIjBjK,EAAMoJ,KAAOA,EACbpJ,EAAMmS,MAAQ7N,EACP2O,EAAarO,KA1Be+C,CA2BrC,CAEA,SAAS7C,EAAaF,EAAMN,GAC1B,IAAIoK,EACA1O,EAEJ,OAAK4E,GAGL5E,EAAQ,IAAI8R,EAIZlN,EAAK5E,MAAQA,EACbA,EAAMiK,OAAS,KAvNK,KAwNpByE,EAAMwE,EAActO,EAAMN,MAExBM,EAAK5E,MAAQ,MAER0O,GAba/G,CActB,CAiBA,IAEIwL,EAAQC,EAFRC,GAAS,EAIb,SAASC,EAAYtT,GAEnB,GAAIqT,EAAQ,CACV,IAAIE,EAOJ,IALAJ,EAAS,IAAItP,EAAMH,MAAM,KACzB0P,EAAU,IAAIvP,EAAMH,MAAM,IAG1B6P,EAAM,EACCA,EAAM,KAAOvT,EAAM0S,KAAKa,KAAS,EACxC,KAAOA,EAAM,KAAOvT,EAAM0S,KAAKa,KAAS,EACxC,KAAOA,EAAM,KAAOvT,EAAM0S,KAAKa,KAAS,EACxC,KAAOA,EAAM,KAAOvT,EAAM0S,KAAKa,KAAS,EAMxC,IAJA9B,EArRO,EAqRczR,EAAM0S,KAAM,EAAG,IAAKS,EAAU,EAAGnT,EAAM2S,KAAM,CAACrC,KAAM,IAGzEiD,EAAM,EACCA,EAAM,IAAMvT,EAAM0S,KAAKa,KAAS,EAEvC9B,EA1RQ,EA0RazR,EAAM0S,KAAM,EAAG,GAAMU,EAAS,EAAGpT,EAAM2S,KAAM,CAACrC,KAAM,IAGzE+C,GAAS,CACX,CAEArT,EAAMgR,QAAUmC,EAChBnT,EAAMkR,QAAU,EAChBlR,EAAMiR,SAAWmC,EACjBpT,EAAMmR,SAAW,CACnB,CA4pCA9R,EAAQ4T,aAAeA,EACvB5T,EAAQ6T,cAAgBA,EACxB7T,EAAQ2T,iBAAmBA,EAC3B3T,EAAQmU,YAjtCR,SAAqB5O,GACnB,OAAOE,EAAaF,EArKN,GAsKhB,EAgtCAvF,EAAQyF,aAAeA,EACvBzF,EAAQ8F,QAtmCR,SAAiBP,EAAM4G,GACrB,IAAIxL,EACAT,EAAOI,EACP8S,EACAgB,EACAjB,EAAMkB,EACNrD,EACAC,EACAP,EAAKC,EACL2D,EACA7C,EACAC,EAEA6C,EAAWC,EAASC,EAEpBC,EAAWC,EAASC,EACpB7T,EACAsO,EAEAwF,EAEA1T,EATAmQ,EAAO,EAMPwD,EAAO,IAAItQ,EAAML,KAAK,GAKtB4Q,EACF,CAAC,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,IAGjE,IAAKxP,IAASA,EAAK5E,QAAU4E,EAAKjF,SAC5BiF,EAAKrF,OAA2B,IAAlBqF,EAAKnF,SACvB,OAAOkI,GAGT3H,EAAQ4E,EAAK5E,OACHuF,OAASmM,IAAQ1R,EAAMuF,KA/Uf,IAmVlBkO,EAAM7O,EAAKhF,SACXD,EAASiF,EAAKjF,OACd+T,EAAO9O,EAAK/E,UACZ4S,EAAO7N,EAAKpF,QACZD,EAAQqF,EAAKrF,MACbiT,EAAO5N,EAAKnF,SACZ4Q,EAAOrQ,EAAMqQ,KACbC,EAAOtQ,EAAMsQ,KAGbP,EAAMyC,EACNxC,EAAO0D,EACPhF,EA7XoB,EA+XpB2F,EACA,OACE,OAAQrU,EAAMuF,MACd,KAhXU,EAiXR,GAAmB,IAAfvF,EAAMoJ,KAAY,CACpBpJ,EAAMuF,KAtWM,GAuWZ,KACF,CAEA,KAAO+K,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEA,GAAkB,EAAbtQ,EAAMoJ,MAAsB,QAATiH,EAAiB,CACvCrQ,EAAMiS,MAAQ,EAEdkC,EAAK,GAAY,IAAP9D,EACV8D,EAAK,GAAM9D,IAAS,EAAK,IACzBrQ,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAOkC,EAAM,EAAG,GAI1C9D,EAAO,EACPC,EAAO,EAEPtQ,EAAMuF,KAxYC,EAyYP,KACF,CAKA,GAJAvF,EAAMgS,MAAQ,EACVhS,EAAMkL,OACRlL,EAAMkL,KAAKoJ,MAAO,KAED,EAAbtU,EAAMoJ,UACA,IAAPiH,IAA2B,IAAMA,GAAQ,IAAM,GAAI,CACtDzL,EAAK7E,IAAM,yBACXC,EAAMuF,KAAOoM,EACb,KACF,CACA,GA7ZY,KA6ZA,GAAPtB,GAAwC,CAC3CzL,EAAK7E,IAAM,6BACXC,EAAMuF,KAAOoM,EACb,KACF,CAMA,GAHArB,GAAQ,EAERlQ,EAAiC,GAAnB,IAHdiQ,KAAU,IAIU,IAAhBrQ,EAAMmS,MACRnS,EAAMmS,MAAQ/R,OAEX,GAAIA,EAAMJ,EAAMmS,MAAO,CAC1BvN,EAAK7E,IAAM,sBACXC,EAAMuF,KAAOoM,EACb,KACF,CACA3R,EAAMiQ,KAAO,GAAK7P,EAElBwE,EAAK1E,MAAQF,EAAMiS,MAAQ,EAC3BjS,EAAMuF,KAAc,IAAP8K,EAlaH,GAka2BqB,EAErCrB,EAAO,EACPC,EAAO,EAEP,MACF,KAhbW,EAkbT,KAAOA,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAGA,GADAtQ,EAAMgS,MAAQ3B,EAjcF,KAkcO,IAAdrQ,EAAMgS,OAA8B,CACvCpN,EAAK7E,IAAM,6BACXC,EAAMuF,KAAOoM,EACb,KACF,CACA,GAAkB,MAAd3R,EAAMgS,MAAgB,CACxBpN,EAAK7E,IAAM,2BACXC,EAAMuF,KAAOoM,EACb,KACF,CACI3R,EAAMkL,OACRlL,EAAMkL,KAAK+D,KAASoB,GAAQ,EAAK,GAEjB,IAAdrQ,EAAMgS,QAERmC,EAAK,GAAY,IAAP9D,EACV8D,EAAK,GAAM9D,IAAS,EAAK,IACzBrQ,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAOkC,EAAM,EAAG,IAI5C9D,EAAO,EACPC,EAAO,EAEPtQ,EAAMuF,KAjdE,EAmdV,KAndU,EAqdR,KAAO+K,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEItQ,EAAMkL,OACRlL,EAAMkL,KAAKoE,KAAOe,GAEF,IAAdrQ,EAAMgS,QAERmC,EAAK,GAAY,IAAP9D,EACV8D,EAAK,GAAM9D,IAAS,EAAK,IACzB8D,EAAK,GAAM9D,IAAS,GAAM,IAC1B8D,EAAK,GAAM9D,IAAS,GAAM,IAC1BrQ,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAOkC,EAAM,EAAG,IAI5C9D,EAAO,EACPC,EAAO,EAEPtQ,EAAMuF,KA3eA,EA6eR,KA7eQ,EA+eN,KAAO+K,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEItQ,EAAMkL,OACRlL,EAAMkL,KAAKqJ,OAAiB,IAAPlE,EACrBrQ,EAAMkL,KAAKqE,GAAMc,GAAQ,GAET,IAAdrQ,EAAMgS,QAERmC,EAAK,GAAY,IAAP9D,EACV8D,EAAK,GAAM9D,IAAS,EAAK,IACzBrQ,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAOkC,EAAM,EAAG,IAI5C9D,EAAO,EACPC,EAAO,EAEPtQ,EAAMuF,KApgBG,EAsgBX,KAtgBW,EAugBT,GAAkB,KAAdvF,EAAMgS,MAAgB,CAExB,KAAO1B,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEAtQ,EAAM8B,OAASuO,EACXrQ,EAAMkL,OACRlL,EAAMkL,KAAKsJ,UAAYnE,GAEP,IAAdrQ,EAAMgS,QAERmC,EAAK,GAAY,IAAP9D,EACV8D,EAAK,GAAM9D,IAAS,EAAK,IACzBrQ,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAOkC,EAAM,EAAG,IAI5C9D,EAAO,EACPC,EAAO,CAET,MACStQ,EAAMkL,OACblL,EAAMkL,KAAKiE,MAAQ,MAErBnP,EAAMuF,KAliBG,EAoiBX,KApiBW,EAqiBT,GAAkB,KAAdvF,EAAMgS,SACR2B,EAAO3T,EAAM8B,QACF0Q,IAAQmB,EAAOnB,GACtBmB,IACE3T,EAAMkL,OACR9K,EAAMJ,EAAMkL,KAAKsJ,UAAYxU,EAAM8B,OAC9B9B,EAAMkL,KAAKiE,QAEdnP,EAAMkL,KAAKiE,MAAQ,IAAI1N,MAAMzB,EAAMkL,KAAKsJ,YAE1C3Q,EAAMrB,SACJxC,EAAMkL,KAAKiE,MACX5P,EACAkT,EAGAkB,EAEAvT,IAMc,IAAdJ,EAAMgS,QACRhS,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAO1S,EAAOoU,EAAMlB,IAEhDD,GAAQmB,EACRlB,GAAQkB,EACR3T,EAAM8B,QAAU6R,GAEd3T,EAAM8B,QAAU,MAAMuS,EAE5BrU,EAAM8B,OAAS,EACf9B,EAAMuF,KAtkBE,EAwkBV,KAxkBU,EAykBR,GAAkB,KAAdvF,EAAMgS,MAAgB,CACxB,GAAa,IAATQ,EAAc,MAAM6B,EACxBV,EAAO,EACP,GAEEvT,EAAMb,EAAMkT,EAAOkB,KAEf3T,EAAMkL,MAAQ9K,GACbJ,EAAM8B,OAAS,QAClB9B,EAAMkL,KAAKkE,MAAQqF,OAAOC,aAAatU,UAElCA,GAAOuT,EAAOnB,GAOvB,GALkB,IAAdxS,EAAMgS,QACRhS,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAO1S,EAAOoU,EAAMlB,IAEhDD,GAAQmB,EACRlB,GAAQkB,EACJvT,EAAO,MAAMiU,CACnB,MACSrU,EAAMkL,OACblL,EAAMkL,KAAKkE,KAAO,MAEpBpP,EAAM8B,OAAS,EACf9B,EAAMuF,KAhmBK,EAkmBb,KAlmBa,EAmmBX,GAAkB,KAAdvF,EAAMgS,MAAgB,CACxB,GAAa,IAATQ,EAAc,MAAM6B,EACxBV,EAAO,EACP,GACEvT,EAAMb,EAAMkT,EAAOkB,KAEf3T,EAAMkL,MAAQ9K,GACbJ,EAAM8B,OAAS,QAClB9B,EAAMkL,KAAKmE,SAAWoF,OAAOC,aAAatU,UAErCA,GAAOuT,EAAOnB,GAMvB,GALkB,IAAdxS,EAAMgS,QACRhS,EAAMiS,MAAQvK,EAAM1H,EAAMiS,MAAO1S,EAAOoU,EAAMlB,IAEhDD,GAAQmB,EACRlB,GAAQkB,EACJvT,EAAO,MAAMiU,CACnB,MACSrU,EAAMkL,OACblL,EAAMkL,KAAKmE,QAAU,MAEvBrP,EAAMuF,KAvnBE,EAynBV,KAznBU,EA0nBR,GAAkB,IAAdvF,EAAMgS,MAAgB,CAExB,KAAO1B,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEA,GAAID,KAAwB,MAAdrQ,EAAMiS,OAAiB,CACnCrN,EAAK7E,IAAM,sBACXC,EAAMuF,KAAOoM,EACb,KACF,CAEAtB,EAAO,EACPC,EAAO,CAET,CACItQ,EAAMkL,OACRlL,EAAMkL,KAAKgE,KAASlP,EAAMgS,OAAS,EAAK,EACxChS,EAAMkL,KAAKoJ,MAAO,GAEpB1P,EAAK1E,MAAQF,EAAMiS,MAAQ,EAC3BjS,EAAMuF,KAAOmM,EACb,MACF,KAnpBY,GAqpBV,KAAOpB,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEA1L,EAAK1E,MAAQF,EAAMiS,MAAQL,EAAQvB,GAEnCA,EAAO,EACPC,EAAO,EAEPtQ,EAAMuF,KAhqBE,GAkqBV,KAlqBU,GAmqBR,GAAuB,IAAnBvF,EAAM+R,SASR,OAPAnN,EAAKhF,SAAW6T,EAChB7O,EAAK/E,UAAY6T,EACjB9O,EAAKpF,QAAUiT,EACf7N,EAAKnF,SAAW+S,EAChBxS,EAAMqQ,KAAOA,EACbrQ,EAAMsQ,KAAOA,EApsBC,EAwsBhB1L,EAAK1E,MAAQF,EAAMiS,MAAQ,EAC3BjS,EAAMuF,KAAOmM,EAEf,KAAKA,EACH,GArtBgB,IAqtBZlG,GAptBY,IAotBSA,EAAqB,MAAM6I,EAEtD,KAlrBgB,GAmrBd,GAAIrU,EAAM2I,KAAM,CAEd0H,KAAiB,EAAPC,EACVA,GAAe,EAAPA,EAERtQ,EAAMuF,KA1qBC,GA2qBP,KACF,CAEA,KAAO+K,EAAO,GAAG,CACf,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAQA,OANAtQ,EAAM2I,KAAe,EAAP0H,EAGdC,GAAQ,EAGQ,GAJhBD,KAAU,IAKV,KAAK,EAGHrQ,EAAMuF,KA5sBM,GA6sBZ,MACF,KAAK,EAKH,GAJA+N,EAAYtT,GAGZA,EAAMuF,KA5sBQ,GA3CA,IAwvBViG,EAAmB,CAErB6E,KAAU,EACVC,GAAQ,EAER,MAAM+D,CACR,CACA,MACF,KAAK,EAGHrU,EAAMuF,KA3tBK,GA4tBX,MACF,KAAK,EACHX,EAAK7E,IAAM,qBACXC,EAAMuF,KAAOoM,EAGftB,KAAU,EACVC,GAAQ,EAER,MACF,KAzuBgB,GA+uBd,IAJAD,KAAiB,EAAPC,EACVA,GAAe,EAAPA,EAGDA,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEA,IAAY,MAAPD,MAAqBA,IAAS,GAAM,OAAS,CAChDzL,EAAK7E,IAAM,+BACXC,EAAMuF,KAAOoM,EACb,KACF,CASA,GARA3R,EAAM8B,OAAgB,MAAPuO,EAIfA,EAAO,EACPC,EAAO,EAEPtQ,EAAMuF,KAjwBO,GAtCG,IAwyBZiG,EAAqB,MAAM6I,EAEjC,KApwBe,GAqwBbrU,EAAMuF,KApwBM,GAswBd,KAtwBc,GAwwBZ,GADAoO,EAAO3T,EAAM8B,OACH,CAGR,GAFI6R,EAAOnB,IAAQmB,EAAOnB,GACtBmB,EAAOD,IAAQC,EAAOD,GACb,IAATC,EAAc,MAAMU,EAExBxQ,EAAMrB,SAAS7C,EAAQJ,EAAOkT,EAAMkB,EAAMF,GAE1CjB,GAAQmB,EACRlB,GAAQkB,EACRD,GAAQC,EACRF,GAAOE,EACP3T,EAAM8B,QAAU6R,EAChB,KACF,CAEA3T,EAAMuF,KAAOmM,EACb,MACF,KAxxBe,GA0xBb,KAAOpB,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAkBA,GAhBAtQ,EAAMsS,KAAkC,KAAnB,GAAPjC,GAEdA,KAAU,EACVC,GAAQ,EAERtQ,EAAMuS,MAAmC,GAAnB,GAAPlC,GAEfA,KAAU,EACVC,GAAQ,EAERtQ,EAAMqS,MAAmC,GAAnB,GAAPhC,GAEfA,KAAU,EACVC,GAAQ,EAGJtQ,EAAMsS,KAAO,KAAOtS,EAAMuS,MAAQ,GAAI,CACxC3N,EAAK7E,IAAM,sCACXC,EAAMuF,KAAOoM,EACb,KACF,CAGA3R,EAAMwS,KAAO,EACbxS,EAAMuF,KAxzBS,GA0zBjB,KA1zBiB,GA2zBf,KAAOvF,EAAMwS,KAAOxS,EAAMqS,OAAO,CAE/B,KAAO/B,EAAO,GAAG,CACf,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEAtQ,EAAM0S,KAAK0B,EAAMpU,EAAMwS,SAAmB,EAAPnC,EAEnCA,KAAU,EACVC,GAAQ,CAEV,CACA,KAAOtQ,EAAMwS,KAAO,IAClBxS,EAAM0S,KAAK0B,EAAMpU,EAAMwS,SAAW,EAapC,GAPAxS,EAAMgR,QAAUhR,EAAM4S,OACtB5S,EAAMkR,QAAU,EAEhBgD,EAAO,CAAC5D,KAAMtQ,EAAMkR,SACpBxC,EAAM+C,EA74BA,EA64BqBzR,EAAM0S,KAAM,EAAG,GAAI1S,EAAMgR,QAAS,EAAGhR,EAAM2S,KAAMuB,GAC5ElU,EAAMkR,QAAUgD,EAAK5D,KAEjB5B,EAAK,CACP9J,EAAK7E,IAAM,2BACXC,EAAMuF,KAAOoM,EACb,KACF,CAEA3R,EAAMwS,KAAO,EACbxS,EAAMuF,KA91BU,GAg2BlB,KAh2BkB,GAi2BhB,KAAOvF,EAAMwS,KAAOxS,EAAMsS,KAAOtS,EAAMuS,OAAO,CAC5C,KAGEsB,GAFAlD,EAAO3Q,EAAMgR,QAAQX,GAAS,GAAKrQ,EAAMkR,SAAW,MAEhC,GAAM,IAC1B4C,EAAkB,MAAPnD,KAFXiD,EAAYjD,IAAS,KAIFL,IANZ,CAQP,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CAEV,CACA,GAAIwD,EAAW,GAEbzD,KAAUuD,EACVtD,GAAQsD,EAER5T,EAAM0S,KAAK1S,EAAMwS,QAAUsB,MAExB,CACH,GAAiB,KAAbA,EAAiB,CAGnB,IADAtT,EAAIoT,EAAY,EACTtD,EAAO9P,GAAG,CACf,GAAa,IAATgS,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAMA,GAHAD,KAAUuD,EACVtD,GAAQsD,EAEW,IAAf5T,EAAMwS,KAAY,CACpB5N,EAAK7E,IAAM,4BACXC,EAAMuF,KAAOoM,EACb,KACF,CACAvR,EAAMJ,EAAM0S,KAAK1S,EAAMwS,KAAO,GAC9BmB,EAAO,GAAY,EAAPtD,GAEZA,KAAU,EACVC,GAAQ,CAEV,MACK,GAAiB,KAAbwD,EAAiB,CAGxB,IADAtT,EAAIoT,EAAY,EACTtD,EAAO9P,GAAG,CACf,GAAa,IAATgS,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAIAA,GAAQsD,EAERxT,EAAM,EACNuT,EAAO,GAAY,GAJnBtD,KAAUuD,IAMVvD,KAAU,EACVC,GAAQ,CAEV,KACK,CAGH,IADA9P,EAAIoT,EAAY,EACTtD,EAAO9P,GAAG,CACf,GAAa,IAATgS,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAIAA,GAAQsD,EAERxT,EAAM,EACNuT,EAAO,IAAa,KAJpBtD,KAAUuD,IAMVvD,KAAU,EACVC,GAAQ,CAEV,CACA,GAAItQ,EAAMwS,KAAOmB,EAAO3T,EAAMsS,KAAOtS,EAAMuS,MAAO,CAChD3N,EAAK7E,IAAM,4BACXC,EAAMuF,KAAOoM,EACb,KACF,CACA,KAAOgC,KACL3T,EAAM0S,KAAK1S,EAAMwS,QAAUpS,CAE/B,CACF,CAGA,GAAIJ,EAAMuF,OAASoM,EAAO,MAG1B,GAAwB,IAApB3R,EAAM0S,KAAK,KAAY,CACzB9N,EAAK7E,IAAM,uCACXC,EAAMuF,KAAOoM,EACb,KACF,CAcA,GATA3R,EAAMkR,QAAU,EAEhBgD,EAAO,CAAC5D,KAAMtQ,EAAMkR,SACpBxC,EAAM+C,EA/gCD,EA+gCqBzR,EAAM0S,KAAM,EAAG1S,EAAMsS,KAAMtS,EAAMgR,QAAS,EAAGhR,EAAM2S,KAAMuB,GAGnFlU,EAAMkR,QAAUgD,EAAK5D,KAGjB5B,EAAK,CACP9J,EAAK7E,IAAM,8BACXC,EAAMuF,KAAOoM,EACb,KACF,CAaA,GAXA3R,EAAMmR,SAAW,EAGjBnR,EAAMiR,SAAWjR,EAAM6S,QACvBqB,EAAO,CAAC5D,KAAMtQ,EAAMmR,UACpBzC,EAAM+C,EA/hCA,EA+hCqBzR,EAAM0S,KAAM1S,EAAMsS,KAAMtS,EAAMuS,MAAOvS,EAAMiR,SAAU,EAAGjR,EAAM2S,KAAMuB,GAG/FlU,EAAMmR,SAAW+C,EAAK5D,KAGlB5B,EAAK,CACP9J,EAAK7E,IAAM,wBACXC,EAAMuF,KAAOoM,EACb,KACF,CAGA,GADA3R,EAAMuF,KAn/BU,GA3CA,IA+hCZiG,EAAqB,MAAM6I,EAEjC,KAt/BkB,GAu/BhBrU,EAAMuF,KAt/BS,GAw/BjB,KAx/BiB,GAy/Bf,GAAIiN,GAAQ,GAAKkB,GAAQ,IAAK,CAE5B9O,EAAKhF,SAAW6T,EAChB7O,EAAK/E,UAAY6T,EACjB9O,EAAKpF,QAAUiT,EACf7N,EAAKnF,SAAW+S,EAChBxS,EAAMqQ,KAAOA,EACbrQ,EAAMsQ,KAAOA,EAEbkB,EAAa5M,EAAMoL,GAEnByD,EAAM7O,EAAKhF,SACXD,EAASiF,EAAKjF,OACd+T,EAAO9O,EAAK/E,UACZ4S,EAAO7N,EAAKpF,QACZD,EAAQqF,EAAKrF,MACbiT,EAAO5N,EAAKnF,SACZ4Q,EAAOrQ,EAAMqQ,KACbC,EAAOtQ,EAAMsQ,KAGTtQ,EAAMuF,OAASmM,IACjB1R,EAAM8S,MAAQ,GAEhB,KACF,CAEA,IADA9S,EAAM8S,KAAO,EAIXe,GAFAlD,EAAO3Q,EAAMgR,QAAQX,GAAS,GAAKrQ,EAAMkR,SAAU,MAE/B,GAAM,IAC1B4C,EAAkB,MAAPnD,KAFXiD,EAAYjD,IAAS,KAIJL,IANV,CAQP,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CAEV,CACA,GAAIuD,GAAgC,KAAV,IAAVA,GAAuB,CAIrC,IAHAE,EAAYH,EACZI,EAAUH,EACVI,EAAWH,EAKTD,GAHAlD,EAAO3Q,EAAMgR,QAAQiD,IACX5D,GAAS,GAAM0D,EAAYC,GAAW,IAAoCD,OAEhE,GAAM,IAC1BD,EAAkB,MAAPnD,IAENoD,GAJLH,EAAYjD,IAAS,KAIUL,IAPxB,CASP,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CAEV,CAEAD,KAAU0D,EACVzD,GAAQyD,EAER/T,EAAM8S,MAAQiB,CAChB,CAOA,GALA1D,KAAUuD,EACVtD,GAAQsD,EAER5T,EAAM8S,MAAQc,EACd5T,EAAM8B,OAASgS,EACC,IAAZD,EAAe,CAIjB7T,EAAMuF,KAhkCO,GAikCb,KACF,CACA,GAAc,GAAVsO,EAAc,CAEhB7T,EAAM8S,MAAQ,EACd9S,EAAMuF,KAAOmM,EACb,KACF,CACA,GAAc,GAAVmC,EAAc,CAChBjP,EAAK7E,IAAM,8BACXC,EAAMuF,KAAOoM,EACb,KACF,CACA3R,EAAMmP,MAAkB,GAAV0E,EACd7T,EAAMuF,KAnlCY,GAqlCpB,KArlCoB,GAslClB,GAAIvF,EAAMmP,MAAO,CAGf,IADA3O,EAAIR,EAAMmP,MACHmB,EAAO9P,GAAG,CACf,GAAa,IAATgS,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEAtQ,EAAM8B,QAAUuO,GAAS,GAAKrQ,EAAMmP,OAAQ,EAE5CkB,KAAUrQ,EAAMmP,MAChBmB,GAAQtQ,EAAMmP,MAEdnP,EAAM8S,MAAQ9S,EAAMmP,KACtB,CAEAnP,EAAM+S,IAAM/S,EAAM8B,OAClB9B,EAAMuF,KAxmCU,GA0mClB,KA1mCkB,GA2mChB,KAGEsO,GAFAlD,EAAO3Q,EAAMiR,SAASZ,GAAS,GAAKrQ,EAAMmR,UAAW,MAEjC,GAAM,IAC1B2C,EAAkB,MAAPnD,KAFXiD,EAAYjD,IAAS,KAIFL,IANZ,CAQP,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CAEV,CACA,GAAyB,KAAV,IAAVuD,GAAuB,CAI1B,IAHAE,EAAYH,EACZI,EAAUH,EACVI,EAAWH,EAKTD,GAHAlD,EAAO3Q,EAAMiR,SAASgD,IACZ5D,GAAS,GAAM0D,EAAYC,GAAW,IAAoCD,OAEhE,GAAM,IAC1BD,EAAkB,MAAPnD,IAENoD,GAJLH,EAAYjD,IAAS,KAIUL,IAPxB,CASP,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CAEV,CAEAD,KAAU0D,EACVzD,GAAQyD,EAER/T,EAAM8S,MAAQiB,CAChB,CAMA,GAJA1D,KAAUuD,EACVtD,GAAQsD,EAER5T,EAAM8S,MAAQc,EACA,GAAVC,EAAc,CAChBjP,EAAK7E,IAAM,wBACXC,EAAMuF,KAAOoM,EACb,KACF,CACA3R,EAAMoS,OAAS0B,EACf9T,EAAMmP,MAAoB,GAAX0E,EACf7T,EAAMuF,KA7pCa,GA+pCrB,KA/pCqB,GAgqCnB,GAAIvF,EAAMmP,MAAO,CAGf,IADA3O,EAAIR,EAAMmP,MACHmB,EAAO9P,GAAG,CACf,GAAa,IAATgS,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEAtQ,EAAMoS,QAAU/B,GAAS,GAAKrQ,EAAMmP,OAAQ,EAE5CkB,KAAUrQ,EAAMmP,MAChBmB,GAAQtQ,EAAMmP,MAEdnP,EAAM8S,MAAQ9S,EAAMmP,KACtB,CAEA,GAAInP,EAAMoS,OAASpS,EAAMiQ,KAAM,CAC7BrL,EAAK7E,IAAM,gCACXC,EAAMuF,KAAOoM,EACb,KACF,CAGA3R,EAAMuF,KAxrCW,GA0rCnB,KA1rCmB,GA2rCjB,GAAa,IAATmO,EAAc,MAAMW,EAExB,GADAV,EAAO3D,EAAO0D,EACV1T,EAAMoS,OAASuB,EAAM,CAEvB,IADAA,EAAO3T,EAAMoS,OAASuB,GACX3T,EAAMmQ,OACXnQ,EAAMuR,KAAM,CACd3M,EAAK7E,IAAM,gCACXC,EAAMuF,KAAOoM,EACb,KACF,CAiBEgC,EAAO3T,EAAMoQ,OACfuD,GAAQ3T,EAAMoQ,MACdU,EAAO9Q,EAAMkQ,MAAQyD,GAGrB7C,EAAO9Q,EAAMoQ,MAAQuD,EAEnBA,EAAO3T,EAAM8B,SAAU6R,EAAO3T,EAAM8B,QACxCiP,EAAc/Q,EAAMiK,MACtB,MAEE8G,EAAcpR,EACdmR,EAAO2C,EAAMzT,EAAMoS,OACnBuB,EAAO3T,EAAM8B,OAEX6R,EAAOD,IAAQC,EAAOD,GAC1BA,GAAQC,EACR3T,EAAM8B,QAAU6R,EAChB,GACEhU,EAAO8T,KAAS1C,EAAYD,aACnB6C,GACU,IAAjB3T,EAAM8B,SAAgB9B,EAAMuF,KA9uCjB,IA+uCf,MACF,KA3uCiB,GA4uCf,GAAa,IAATmO,EAAc,MAAMW,EACxB1U,EAAO8T,KAASzT,EAAM8B,OACtB4R,IACA1T,EAAMuF,KApvCS,GAqvCf,MACF,KAhvCW,GAivCT,GAAIvF,EAAMoJ,KAAM,CAEd,KAAOkH,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IAEAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAaA,GAXAN,GAAQ0D,EACR9O,EAAK9E,WAAakQ,EAClBhQ,EAAMkS,OAASlC,EACXA,IACFpL,EAAK1E,MAAQF,EAAMiS,MAEdjS,EAAMgS,MAAQtK,EAAM1H,EAAMiS,MAAOtS,EAAQqQ,EAAMyD,EAAMzD,GAAQvI,EAAQzH,EAAMiS,MAAOtS,EAAQqQ,EAAMyD,EAAMzD,IAG7GA,EAAO0D,GAEF1T,EAAMgS,MAAQ3B,EAAOuB,EAAQvB,MAAWrQ,EAAMiS,MAAO,CACxDrN,EAAK7E,IAAM,uBACXC,EAAMuF,KAAOoM,EACb,KACF,CAEAtB,EAAO,EACPC,EAAO,CAGT,CACAtQ,EAAMuF,KAhxCI,GAkxCZ,KAlxCY,GAmxCV,GAAIvF,EAAMoJ,MAAQpJ,EAAMgS,MAAO,CAE7B,KAAO1B,EAAO,IAAI,CAChB,GAAa,IAATkC,EAAc,MAAM6B,EACxB7B,IACAnC,GAAQ9Q,EAAMkT,MAAWnC,EACzBA,GAAQ,CACV,CAEA,GAAID,KAAwB,WAAdrQ,EAAMkS,OAAqB,CACvCtN,EAAK7E,IAAM,yBACXC,EAAMuF,KAAOoM,EACb,KACF,CAEAtB,EAAO,EACPC,EAAO,CAGT,CACAtQ,EAAMuF,KAtyCE,GAwyCV,KAxyCU,GAyyCRmJ,EAt1CgB,EAu1ChB,MAAM2F,EACR,KAAK1C,EACHjD,GAr1CgB,EAs1ChB,MAAM2F,EACR,KA5yCS,GA6yCP,OAv1CgB,EA01ClB,QACE,OAAO1M,EAsBX,OARA/C,EAAKhF,SAAW6T,EAChB7O,EAAK/E,UAAY6T,EACjB9O,EAAKpF,QAAUiT,EACf7N,EAAKnF,SAAW+S,EAChBxS,EAAMqQ,KAAOA,EACbrQ,EAAMsQ,KAAOA,GAGTtQ,EAAMkQ,OAAUF,IAASpL,EAAK/E,WAAaG,EAAMuF,KAAOoM,IACvC3R,EAAMuF,KA50Cd,IApDO,IAg4CuBiG,KAtlC7C,SAAsB5G,EAAMlC,EAAK1B,EAAK2S,GACpC,IAAI9C,EACA7Q,EAAQ4E,EAAK5E,MAqCjB,OAlCqB,OAAjBA,EAAMiK,SACRjK,EAAMkQ,MAAQ,GAAKlQ,EAAMmS,MACzBnS,EAAMoQ,MAAQ,EACdpQ,EAAMmQ,MAAQ,EAEdnQ,EAAMiK,OAAS,IAAIpG,EAAML,KAAKxD,EAAMkQ,QAIlCyD,GAAQ3T,EAAMkQ,OAChBrM,EAAMrB,SAASxC,EAAMiK,OAAOvH,EAAK1B,EAAMhB,EAAMkQ,MAAOlQ,EAAMkQ,MAAO,GACjElQ,EAAMoQ,MAAQ,EACdpQ,EAAMmQ,MAAQnQ,EAAMkQ,SAGpBW,EAAO7Q,EAAMkQ,MAAQlQ,EAAMoQ,OAChBuD,IACT9C,EAAO8C,GAGT9P,EAAMrB,SAASxC,EAAMiK,OAAOvH,EAAK1B,EAAM2S,EAAM9C,EAAM7Q,EAAMoQ,QACzDuD,GAAQ9C,IAGNhN,EAAMrB,SAASxC,EAAMiK,OAAOvH,EAAK1B,EAAM2S,EAAMA,EAAM,GACnD3T,EAAMoQ,MAAQuD,EACd3T,EAAMmQ,MAAQnQ,EAAMkQ,QAGpBlQ,EAAMoQ,OAASS,EACX7Q,EAAMoQ,QAAUpQ,EAAMkQ,QAASlQ,EAAMoQ,MAAQ,GAC7CpQ,EAAMmQ,MAAQnQ,EAAMkQ,QAASlQ,EAAMmQ,OAASU,KAG7C,CACT,CA+iCQ8D,CAAa/P,EAAMA,EAAKjF,OAAQiF,EAAKhF,SAAUoQ,EAAOpL,EAAK/E,YAC7DG,EAAMuF,KA10CC,IA1CS,IAw3CpBwK,GAAOnL,EAAKnF,SACZuQ,GAAQpL,EAAK/E,UACb+E,EAAKlF,UAAYqQ,EACjBnL,EAAK9E,WAAakQ,EAClBhQ,EAAMkS,OAASlC,EACXhQ,EAAMoJ,MAAQ4G,IAChBpL,EAAK1E,MAAQF,EAAMiS,MAChBjS,EAAMgS,MAAQtK,EAAM1H,EAAMiS,MAAOtS,EAAQqQ,EAAMpL,EAAKhF,SAAWoQ,GAAQvI,EAAQzH,EAAMiS,MAAOtS,EAAQqQ,EAAMpL,EAAKhF,SAAWoQ,IAE/HpL,EAAK3E,UAAYD,EAAMsQ,MAAQtQ,EAAM2I,KAAO,GAAK,IAC9B3I,EAAMuF,OAASmM,EAAO,IAAM,IAn2C3B,KAo2CD1R,EAAMuF,MAz2CR,KAy2CyBvF,EAAMuF,KAAiB,IAAM,IACzD,IAARwK,GAAsB,IAATC,GAl5CC,IAk5CcxE,IA14Cd,IA04CqCkD,IACvDA,GAp4CkB,GAs4CbA,EACT,EAqCArP,EAAQgH,WAnCR,SAAoBzB,GAElB,IAAKA,IAASA,EAAK5E,MACjB,OAAO2H,EAGT,IAAI3H,EAAQ4E,EAAK5E,MAKjB,OAJIA,EAAMiK,SACRjK,EAAMiK,OAAS,MAEjBrF,EAAK5E,MAAQ,KA15CO,CA45CtB,EAwBAX,EAAQ6F,iBAtBR,SAA0BN,EAAMsG,GAC9B,IAAIlL,EAGJ,OAAK4E,GAASA,EAAK5E,MAEM,KAAP,GADlBA,EAAQ4E,EAAK5E,OACFoJ,MAA0BzB,GAGrC3H,EAAMkL,KAAOA,EACbA,EAAKoJ,MAAO,EAx6CQ,GAk6Ce3M,CAQrC,EAWAtI,EAAQuV,YAAc,oC,iCC56CtBxV,EAAOC,QApCP,WAEEC,KAAK2P,KAAa,EAElB3P,KAAKgQ,KAAa,EAElBhQ,KAAKiV,OAAa,EAElBjV,KAAKiQ,GAAa,EAElBjQ,KAAK6P,MAAa,KAElB7P,KAAKkV,UAAa,EAWlBlV,KAAK8P,KAAa,GAIlB9P,KAAK+P,QAAa,GAIlB/P,KAAK4P,KAAa,EAElB5P,KAAKgV,MAAa,CACpB,C,qCClCA,IAMIO,EAAO,CAAC,GAEZvT,EARgBsC,EAAAA,OAAAA,QAQTiR,EANSjR,EAAQ,OACRA,EAAQ,OACRA,EAAQ,QAMxBxE,EAAOC,QAAUwV,C,oBCbjBzV,EAAOC,QAAU,CAGfwG,WAAoB,EACpBiP,gBAAoB,EACpB7O,aAAoB,EACpB8O,aAAoB,EACpBnP,SAAoB,EACpBoP,QAAoB,EACpBC,QAAoB,EAKpBlQ,KAAoB,EACpBgB,aAAoB,EACpBmP,YAAoB,EACpBC,SAAoB,EACpBxN,gBAAoB,EACpByN,cAAoB,EAEpBC,aAAoB,EAIpBC,iBAA0B,EAC1BC,aAA0B,EAC1BC,mBAA0B,EAC1BC,uBAA0B,EAG1BC,WAA0B,EAC1BC,eAA0B,EAC1BC,MAA0B,EAC1BC,QAA0B,EAC1BC,mBAA0B,EAG1BC,SAA0B,EAC1BC,OAA0B,EAE1BC,UAA0B,EAG1BC,WAA0B,E,qCCxC5B,IAAIrS,EAAQD,EAAQ,OAQhBuS,GAAe,EACfC,GAAmB,EAEvB,IAAM3B,OAAOC,aAAarR,MAAM,KAAM,CAAC,GAAK,CAAE,MAAMgT,GAAMF,GAAe,CAAO,CAChF,IAAM1B,OAAOC,aAAarR,MAAM,KAAM,IAAIlC,WAAW,GAAK,CAAE,MAAMkV,GAAMD,GAAmB,CAAO,CAOlG,IADA,IAAIE,EAAW,IAAIzS,EAAML,KAAK,KACrBqO,EAAE,EAAGA,EAAE,IAAKA,IACnByE,EAASzE,GAAMA,GAAK,IAAM,EAAIA,GAAK,IAAM,EAAIA,GAAK,IAAM,EAAIA,GAAK,IAAM,EAAIA,GAAK,IAAM,EAAI,EA4D5F,SAASxK,EAAclH,EAAKC,GAE1B,GAAIA,EAAM,QACHD,EAAImC,UAAY8T,IAAuBjW,EAAImC,UAAY6T,GAC1D,OAAO1B,OAAOC,aAAarR,MAAM,KAAMQ,EAAMzB,UAAUjC,EAAKC,IAKhE,IADA,IAAI8C,EAAS,GACJjC,EAAE,EAAGA,EAAIb,EAAKa,IACrBiC,GAAUuR,OAAOC,aAAavU,EAAIc,IAEpC,OAAOiC,CACT,CAvEAoT,EAAS,KAAKA,EAAS,KAAK,EAI5BjX,EAAQ+H,WAAa,SAAU0D,GAC7B,IAAI3K,EAAKO,EAAG6V,EAAIC,EAAOvV,EAAGwV,EAAU3L,EAAIhJ,OAAQ4U,EAAU,EAG1D,IAAKF,EAAQ,EAAGA,EAAQC,EAASD,IAEV,SAAZ,OADT9V,EAAIoK,EAAI0E,WAAWgH,MACaA,EAAM,EAAIC,GAElB,SAAZ,OADVF,EAAKzL,EAAI0E,WAAWgH,EAAM,OAExB9V,EAAI,OAAYA,EAAI,OAAW,KAAO6V,EAAK,OAC3CC,KAGJE,GAAWhW,EAAI,IAAO,EAAIA,EAAI,KAAQ,EAAIA,EAAI,MAAU,EAAI,EAO9D,IAHAP,EAAM,IAAI0D,EAAML,KAAKkT,GAGhBzV,EAAE,EAAGuV,EAAQ,EAAGvV,EAAIyV,EAASF,IAEX,SAAZ,OADT9V,EAAIoK,EAAI0E,WAAWgH,MACaA,EAAM,EAAIC,GAElB,SAAZ,OADVF,EAAKzL,EAAI0E,WAAWgH,EAAM,OAExB9V,EAAI,OAAYA,EAAI,OAAW,KAAO6V,EAAK,OAC3CC,KAGA9V,EAAI,IAENP,EAAIc,KAAOP,EACFA,EAAI,MAEbP,EAAIc,KAAO,IAAQP,IAAM,EACzBP,EAAIc,KAAO,IAAY,GAAJP,GACVA,EAAI,OAEbP,EAAIc,KAAO,IAAQP,IAAM,GACzBP,EAAIc,KAAO,IAAQP,IAAM,EAAI,GAC7BP,EAAIc,KAAO,IAAY,GAAJP,IAGnBP,EAAIc,KAAO,IAAQP,IAAM,GACzBP,EAAIc,KAAO,IAAQP,IAAM,GAAK,GAC9BP,EAAIc,KAAO,IAAQP,IAAM,EAAI,GAC7BP,EAAIc,KAAO,IAAY,GAAJP,GAIvB,OAAOP,CACT,EAoBAd,EAAQgI,cAAgB,SAASlH,GAC/B,OAAOkH,EAAclH,EAAKA,EAAI2B,OAChC,EAIAzC,EAAQyG,cAAgB,SAASgF,GAE/B,IADA,IAAI3K,EAAM,IAAI0D,EAAML,KAAKsH,EAAIhJ,QACpBb,EAAE,EAAGb,EAAID,EAAI2B,OAAQb,EAAIb,EAAKa,IACrCd,EAAIc,GAAK6J,EAAI0E,WAAWvO,GAE1B,OAAOd,CACT,EAIAd,EAAQ8G,WAAa,SAAUhG,EAAKwW,GAClC,IAAI1V,EAAG2V,EAAKlW,EAAGmW,EACXzW,EAAMuW,GAAOxW,EAAI2B,OAKjBgV,EAAW,IAAIrV,MAAU,EAAJrB,GAEzB,IAAKwW,EAAI,EAAG3V,EAAE,EAAGA,EAAEb,GAGjB,IAFAM,EAAIP,EAAIc,MAEA,IAAQ6V,EAASF,KAASlW,OAIlC,IAFAmW,EAAQP,EAAS5V,IAEL,EAAKoW,EAASF,KAAS,MAAQ3V,GAAK4V,EAAM,MAAtD,CAKA,IAFAnW,GAAe,IAAVmW,EAAc,GAAiB,IAAVA,EAAc,GAAO,EAExCA,EAAQ,GAAK5V,EAAIb,GACtBM,EAAKA,GAAK,EAAiB,GAAXP,EAAIc,KACpB4V,IAIEA,EAAQ,EAAKC,EAASF,KAAS,MAE/BlW,EAAI,MACNoW,EAASF,KAASlW,GAElBA,GAAK,MACLoW,EAASF,KAAS,MAAWlW,GAAK,GAAM,KACxCoW,EAASF,KAAS,MAAc,KAAJlW,EAlBqC,CAsBrE,OAAO2G,EAAcyP,EAAUF,EACjC,EASAvX,EAAQ6G,WAAa,SAAS/F,EAAKwW,GACjC,IAAItW,EAOJ,KALAsW,EAAMA,GAAOxW,EAAI2B,QACP3B,EAAI2B,SAAU6U,EAAMxW,EAAI2B,QAGlCzB,EAAMsW,EAAI,EACHtW,GAAO,GAA2B,OAAV,IAAXF,EAAIE,KAAyBA,IAIjD,OAAIA,EAAM,GAIE,IAARA,EAJkBsW,EAMdtW,EAAMiW,EAASnW,EAAIE,IAAQsW,EAAOtW,EAAMsW,CAClD,C,qCCrLA,IAAI9S,EAAQD,EAAQ,OAEhBmT,EAAU,GASVC,EAAQ,CACV,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GACrD,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,EAAG,GAG3DC,EAAO,CACT,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAGtDC,EAAQ,CACV,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IACtD,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAClD,KAAM,MAAO,MAAO,MAAO,EAAG,GAG5BC,EAAO,CACT,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GACpC,GAAI,GAAI,GAAI,GAAI,GAAI,IAGtB/X,EAAOC,QAAU,SAAuB+X,EAAM1E,EAAM2E,EAAYC,EAAO3W,EAAO4W,EAAa5E,EAAMuB,GAE/F,IAYIsD,EACAC,EACAC,EACAC,EACAlF,EAIAzR,EAMA4S,EAAWC,EAASC,EA1BpBxD,EAAO4D,EAAK5D,KAGZlQ,EAAM,EACNmT,EAAM,EACNqE,EAAM,EAAGjB,EAAM,EACfkB,EAAO,EACPC,EAAO,EACPC,EAAO,EACPrE,EAAO,EACPsE,EAAO,EACPC,EAAO,EAMPC,EAAO,KACPC,EAAa,EAGbC,EAAQ,IAAIvU,EAAMJ,MAAMsT,IACxBsB,EAAO,IAAIxU,EAAMJ,MAAMsT,IACvB5H,EAAQ,KACRmJ,EAAc,EAoClB,IAAKlY,EAAM,EAAGA,GAAO2W,EAAS3W,IAC5BgY,EAAMhY,GAAO,EAEf,IAAKmT,EAAM,EAAGA,EAAM+D,EAAO/D,IACzB6E,EAAM1F,EAAK2E,EAAa9D,MAK1B,IADAsE,EAAOvH,EACFqG,EAAMI,EAASJ,GAAO,GACN,IAAfyB,EAAMzB,GADkBA,KAM9B,GAHIkB,EAAOlB,IACTkB,EAAOlB,GAEG,IAARA,EAaF,OATAhW,EAAM4W,KAAkB,SAMxB5W,EAAM4W,KAAkB,SAExBrD,EAAK5D,KAAO,EACL,EAET,IAAKsH,EAAM,EAAGA,EAAMjB,GACC,IAAfyB,EAAMR,GADaA,KASzB,IANIC,EAAOD,IACTC,EAAOD,GAITlE,EAAO,EACFtT,EAAM,EAAGA,GAAO2W,EAAS3W,IAG5B,GAFAsT,IAAS,GACTA,GAAQ0E,EAAMhY,IACH,EACT,OAAQ,EAGZ,GAAIsT,EAAO,IAtID,IAsIO0D,GAA0B,IAART,GACjC,OAAQ,EAKV,IADA0B,EAAK,GAAK,EACLjY,EAAM,EAAGA,EAAM2W,EAAS3W,IAC3BiY,EAAKjY,EAAM,GAAKiY,EAAKjY,GAAOgY,EAAMhY,GAIpC,IAAKmT,EAAM,EAAGA,EAAM+D,EAAO/D,IACM,IAA3Bb,EAAK2E,EAAa9D,KACpBZ,EAAK0F,EAAK3F,EAAK2E,EAAa9D,OAAWA,GAmE3C,GAtNU,IAyLN6D,GACFc,EAAO/I,EAAQwD,EACf3R,EAAM,IA1LC,IA4LEoW,GACTc,EAAOlB,EACPmB,GAAc,IACdhJ,EAAQ8H,EACRqB,GAAe,IACftX,EAAM,MAGNkX,EAAOhB,EACP/H,EAAQgI,EACRnW,GAAO,GAITiX,EAAO,EACP1E,EAAM,EACNnT,EAAMwX,EACNnF,EAAO8E,EACPO,EAAOD,EACPE,EAAO,EACPL,GAAO,EAEPC,GADAK,EAAO,GAAKH,GACE,EAlNL,IAqNJT,GAAiBY,EA1NN,KAMN,IAqNPZ,GAAkBY,EA1NJ,IA2Nf,OAAO,EAKT,OAAS,CAGPpE,EAAYxT,EAAM2X,EACdpF,EAAKY,GAAOvS,GACd6S,EAAU,EACVC,EAAWnB,EAAKY,IAETZ,EAAKY,GAAOvS,GACnB6S,EAAU1E,EAAMmJ,EAAc3F,EAAKY,IACnCO,EAAWoE,EAAKC,EAAaxF,EAAKY,MAGlCM,EAAU,GACVC,EAAW,GAIb0D,EAAO,GAAMpX,EAAM2X,EAEnBH,EADAH,EAAO,GAAKK,EAEZ,GAEEnX,EAAM8R,GAAQwF,GAAQF,IADtBN,GAAQD,IAC+B5D,GAAa,GAAOC,GAAW,GAAMC,QAC5D,IAAT2D,GAIT,IADAD,EAAO,GAAMpX,EAAM,EACZ6X,EAAOT,GACZA,IAAS,EAWX,GATa,IAATA,GACFS,GAAQT,EAAO,EACfS,GAAQT,GAERS,EAAO,EAIT1E,IACqB,MAAf6E,EAAMhY,GAAY,CACtB,GAAIA,IAAQuW,EAAO,MACnBvW,EAAMsS,EAAK2E,EAAa1E,EAAKY,GAC/B,CAGA,GAAInT,EAAMyX,IAASI,EAAON,KAAUD,EAAK,CAYvC,IAVa,IAATK,IACFA,EAAOF,GAITpF,GAAQmF,EAIRlE,EAAO,IADPoE,EAAO1X,EAAM2X,GAEND,EAAOC,EAAOpB,MACnBjD,GAAQ0E,EAAMN,EAAOC,KACT,IACZD,IACApE,IAAS,EAKX,GADAsE,GAAQ,GAAKF,EA9RR,IA+RAV,GAAiBY,EApSV,KAMN,IA+RHZ,GAAkBY,EApSR,IAqSX,OAAO,EAQTrX,EAJA+W,EAAMO,EAAON,GAICE,GAAQ,GAAOC,GAAQ,GAAOrF,EAAO8E,CACrD,CACF,CAeA,OAVa,IAATU,IAIFtX,EAAM8R,EAAOwF,GAAU7X,EAAM2X,GAAS,GAAO,IAAM,IAKrD7D,EAAK5D,KAAOuH,EACL,CACT,C,qCCnUA,IAAIhU,EAAQD,EAAQ,OAqBpB,SAASwE,EAAKjI,GAA6B,IAAtB,IAAIC,EAAMD,EAAI2B,SAAiB1B,GAAO,GAAKD,EAAIC,GAAO,CAAK,CAIhF,IAiBImY,EAAgB,IAGhBC,EAAgBD,IAGhBE,EAAgB,GAShB/K,EAAgB,GA0BhBgL,EACF,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAEvDC,EACF,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAEhEC,EACF,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAEnCC,EACF,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAc3CC,EAAgB,IAAIrX,MAAM,KAC9B2G,EAAK0Q,GAOL,IAAIC,EAAgB,IAAItX,MAAMgX,IAC9BrQ,EAAK2Q,GAKL,IAAIC,EAAgB,IAAIvX,MAjBJ,KAkBpB2G,EAAK4Q,GAML,IAAIC,EAAgB,IAAIxX,MAAMmG,KAC9BQ,EAAK6Q,GAGL,IAAIC,EAAgB,IAAIzX,MA7FJ,IA8FpB2G,EAAK8Q,GAGL,IAAIC,EAAgB,IAAI1X,MAAMgX,GAC9BrQ,EAAK+Q,GAIL,IAaIC,EACAC,EACAC,EAfAC,EAAiB,SAAUC,EAAaC,EAAYC,EAAYC,EAAOC,GAEzEta,KAAKka,YAAeA,EACpBla,KAAKma,WAAeA,EACpBna,KAAKoa,WAAeA,EACpBpa,KAAKqa,MAAeA,EACpBra,KAAKsa,WAAeA,EAGpBta,KAAKua,UAAeL,GAAeA,EAAY1X,MACjD,EAQIgY,EAAW,SAASC,EAAUC,GAChC1a,KAAKya,SAAWA,EAChBza,KAAK2a,SAAW,EAChB3a,KAAK0a,UAAYA,CACnB,EAIA,SAASE,EAAOrJ,GACd,OAAOA,EAAO,IAAMmI,EAAWnI,GAAQmI,EAAW,KAAOnI,IAAS,GACpE,CAOA,SAASsJ,EAAW7R,EAAG8R,GAGrB9R,EAAEE,YAAYF,EAAEC,WAAmB,IAAL6R,EAC9B9R,EAAEE,YAAYF,EAAEC,WAAc6R,IAAM,EAAK,GAC3C,CAOA,SAASC,EAAU/R,EAAGgS,EAAOxY,GACvBwG,EAAEgG,SAjIY,GAiIWxM,GAC3BwG,EAAE+F,QAAWiM,GAAShS,EAAEgG,SAAY,MACpC6L,EAAU7R,EAAGA,EAAE+F,QACf/F,EAAE+F,OAASiM,GApIK,GAoIgBhS,EAAEgG,SAClChG,EAAEgG,UAAYxM,EArIE,KAuIhBwG,EAAE+F,QAAWiM,GAAShS,EAAEgG,SAAY,MACpChG,EAAEgG,UAAYxM,EAElB,CAGA,SAASyY,EAAUjS,EAAG5H,EAAG8Z,GACvBH,EAAU/R,EAAGkS,EAAO,EAAF9Z,GAAe8Z,EAAO,EAAF9Z,EAAM,GAC9C,CAQA,SAAS+Z,EAAWC,EAAMta,GACxB,IAAIua,EAAM,EACV,GACEA,GAAc,EAAPD,EACPA,KAAU,EACVC,IAAQ,UACCva,EAAM,GACjB,OAAOua,IAAQ,CACjB,CA+HA,SAASC,EAAUJ,EAAMP,EAAUxM,GAKjC,IAEI6C,EACA9P,EAHAqa,EAAY,IAAIpZ,MAAMiM,IACtBgN,EAAO,EAOX,IAAKpK,EAAO,EAAGA,GAAQ5C,EAAU4C,IAC/BuK,EAAUvK,GAAQoK,EAAQA,EAAOjN,EAAS6C,EAAK,IAAO,EASxD,IAAK9P,EAAI,EAAIA,GAAKyZ,EAAUzZ,IAAK,CAC/B,IAAIJ,EAAMoa,EAAO,EAAFha,EAAM,GACT,IAARJ,IAEJoa,EAAO,EAAFha,GAAgBia,EAAWI,EAAUza,KAAQA,GAIpD,CACF,CA8GA,SAAS0a,EAAWxS,GAClB,IAAI9H,EAGJ,IAAKA,EAAI,EAAGA,EAAIgY,EAAUhY,IAAO8H,EAAE4E,UAAY,EAAF1M,GAAgB,EAC7D,IAAKA,EAAI,EAAGA,EAAIiY,EAAUjY,IAAO8H,EAAE8E,UAAY,EAAF5M,GAAgB,EAC7D,IAAKA,EAAI,EAAGA,EA3bM,GA2bQA,IAAO8H,EAAE+E,QAAU,EAAF7M,GAAgB,EAE3D8H,EAAE4E,UAAU6N,KAAwB,EACpCzS,EAAE4F,QAAU5F,EAAE6F,WAAa,EAC3B7F,EAAEyD,SAAWzD,EAAE8F,QAAU,CAC3B,CAMA,SAAS4M,EAAU1S,GAEbA,EAAEgG,SAAW,EACf6L,EAAU7R,EAAGA,EAAE+F,QACN/F,EAAEgG,SAAW,IAEtBhG,EAAEE,YAAYF,EAAEC,WAAaD,EAAE+F,QAEjC/F,EAAE+F,OAAS,EACX/F,EAAEgG,SAAW,CACf,CA6BA,SAAS2M,EAAQT,EAAMha,EAAGoK,EAAGkD,GAC3B,IAAIoN,EAAQ,EAAF1a,EACN2a,EAAQ,EAAFvQ,EACV,OAAQ4P,EAAKU,GAAgBV,EAAKW,IAC1BX,EAAKU,KAAkBV,EAAKW,IAAiBrN,EAAMtN,IAAMsN,EAAMlD,EACzE,CAQA,SAASwQ,EAAW9S,EAAGkS,EAAM5Z,GAO3B,IAFA,IAAIya,EAAI/S,EAAEqF,KAAK/M,GACX0a,EAAI1a,GAAK,EACN0a,GAAKhT,EAAEsF,WAER0N,EAAIhT,EAAEsF,UACRqN,EAAQT,EAAMlS,EAAEqF,KAAK2N,EAAE,GAAIhT,EAAEqF,KAAK2N,GAAIhT,EAAEwF,QACxCwN,KAGEL,EAAQT,EAAMa,EAAG/S,EAAEqF,KAAK2N,GAAIhT,EAAEwF,SAGlCxF,EAAEqF,KAAK/M,GAAK0H,EAAEqF,KAAK2N,GACnB1a,EAAI0a,EAGJA,IAAM,EAERhT,EAAEqF,KAAK/M,GAAKya,CACd,CASA,SAASE,EAAejT,EAAGkT,EAAOC,GAKhC,IAAI5K,EACA6K,EAEAhB,EACAvL,EAFAwM,EAAK,EAIT,GAAmB,IAAfrT,EAAEyD,SACJ,GACE8E,EAAQvI,EAAEE,YAAYF,EAAE2F,MAAW,EAAH0N,IAAS,EAAMrT,EAAEE,YAAYF,EAAE2F,MAAW,EAAH0N,EAAO,GAC9ED,EAAKpT,EAAEE,YAAYF,EAAEyF,MAAQ4N,GAC7BA,IAEa,IAAT9K,EACF0J,EAAUjS,EAAGoT,EAAIF,IAKjBjB,EAAUjS,GADVoS,EAAOzB,EAAayC,IACFnD,EAAS,EAAGiD,GAEhB,KADdrM,EAAQuJ,EAAYgC,KAGlBL,EAAU/R,EADVoT,GAAMxC,EAAYwB,GACDvL,GAMnBoL,EAAUjS,EAHVoS,EAAOR,IADPrJ,GAImB4K,GAEL,KADdtM,EAAQwJ,EAAY+B,KAGlBL,EAAU/R,EADVuI,GAAQsI,EAAUuB,GACCvL,UAQhBwM,EAAKrT,EAAEyD,UAGlBwO,EAAUjS,EAvjBM,IAujBQkT,EAC1B,CAWA,SAASI,EAAWtT,EAAGuT,GAIrB,IAIIrb,EAAGoK,EAEHkR,EANAtB,EAAWqB,EAAK9B,SAChBgC,EAAWF,EAAK7B,UAAUR,YAC1BK,EAAYgC,EAAK7B,UAAUH,UAC3BF,EAAWkC,EAAK7B,UAAUL,MAE1BM,GAAY,EAUhB,IAHA3R,EAAEsF,SAAW,EACbtF,EAAEuF,SArmBgB,IAumBbrN,EAAI,EAAGA,EAAImZ,EAAOnZ,IACQ,IAAzBga,EAAS,EAAJha,IACP8H,EAAEqF,OAAOrF,EAAEsF,UAAYqM,EAAWzZ,EAClC8H,EAAEwF,MAAMtN,GAAK,GAGbga,EAAO,EAAFha,EAAM,GAAa,EAS5B,KAAO8H,EAAEsF,SAAW,GAElB4M,EAAY,GADZsB,EAAOxT,EAAEqF,OAAOrF,EAAEsF,UAAaqM,EAAW,IAAMA,EAAW,IACjC,EAC1B3R,EAAEwF,MAAMgO,GAAQ,EAChBxT,EAAE4F,UAEE2L,IACFvR,EAAE6F,YAAc4N,EAAW,EAALD,EAAS,IASnC,IALAD,EAAK5B,SAAWA,EAKXzZ,EAAK8H,EAAEsF,UAAY,EAAcpN,GAAK,EAAGA,IAAO4a,EAAW9S,EAAGkS,EAAMha,GAKzEsb,EAAOnC,EACP,GAGEnZ,EAAI8H,EAAEqF,KAAK,GACXrF,EAAEqF,KAAK,GAAiBrF,EAAEqF,KAAKrF,EAAEsF,YACjCwN,EAAW9S,EAAGkS,EAAM,GAGpB5P,EAAItC,EAAEqF,KAAK,GAEXrF,EAAEqF,OAAOrF,EAAEuF,UAAYrN,EACvB8H,EAAEqF,OAAOrF,EAAEuF,UAAYjD,EAGvB4P,EAAY,EAAPsB,GAAqBtB,EAAS,EAAJha,GAAkBga,EAAS,EAAJ5P,GACtDtC,EAAEwF,MAAMgO,IAASxT,EAAEwF,MAAMtN,IAAM8H,EAAEwF,MAAMlD,GAAKtC,EAAEwF,MAAMtN,GAAK8H,EAAEwF,MAAMlD,IAAM,EACvE4P,EAAO,EAAFha,EAAM,GAAaga,EAAO,EAAF5P,EAAM,GAAakR,EAGhDxT,EAAEqF,KAAK,GAAiBmO,IACxBV,EAAW9S,EAAGkS,EAAM,SAEblS,EAAEsF,UAAY,GAEvBtF,EAAEqF,OAAOrF,EAAEuF,UAAYvF,EAAEqF,KAAK,GAjehC,SAAoBrF,EAAGuT,GAIrB,IAOIG,EACAxb,EAAGoK,EACH0F,EACA2L,EACA9T,EAXAqS,EAAkBqB,EAAK9B,SACvBE,EAAkB4B,EAAK5B,SACvB8B,EAAkBF,EAAK7B,UAAUR,YACjCK,EAAkBgC,EAAK7B,UAAUH,UACjC1K,EAAkB0M,EAAK7B,UAAUP,WACjCvB,EAAkB2D,EAAK7B,UAAUN,WACjCE,EAAkBiC,EAAK7B,UAAUJ,WAMjCsC,EAAW,EAEf,IAAK5L,EAAO,EAAGA,GAAQ5C,EAAU4C,IAC/BhI,EAAEmF,SAAS6C,GAAQ,EAQrB,IAFAkK,EAAwB,EAAnBlS,EAAEqF,KAAKrF,EAAEuF,UAAc,GAAa,EAEpCmO,EAAI1T,EAAEuF,SAAS,EAAGmO,EA9NL,IA8NoBA,KAEpC1L,EAAOkK,EAA4B,EAAvBA,EAAO,GADnBha,EAAI8H,EAAEqF,KAAKqO,IACW,GAAiB,GAAa,GACzCpC,IACTtJ,EAAOsJ,EACPsC,KAEF1B,EAAO,EAAFha,EAAM,GAAa8P,EAGpB9P,EAAIyZ,IAER3R,EAAEmF,SAAS6C,KACX2L,EAAQ,EACJzb,GAAK0X,IACP+D,EAAQ9M,EAAM3O,EAAE0X,IAElB/P,EAAIqS,EAAS,EAAJha,GACT8H,EAAE4F,SAAW/F,GAAKmI,EAAO2L,GACrBpC,IACFvR,EAAE6F,YAAchG,GAAK4T,EAAQ,EAAFvb,EAAM,GAAayb,KAGlD,GAAiB,IAAbC,EAAJ,CAMA,EAAG,CAED,IADA5L,EAAOsJ,EAAW,EACU,IAArBtR,EAAEmF,SAAS6C,IAAeA,IACjChI,EAAEmF,SAAS6C,KACXhI,EAAEmF,SAAS6C,EAAK,IAAM,EACtBhI,EAAEmF,SAASmM,KAIXsC,GAAY,CACd,OAASA,EAAW,GAOpB,IAAK5L,EAAOsJ,EAAqB,IAATtJ,EAAYA,IAElC,IADA9P,EAAI8H,EAAEmF,SAAS6C,GACF,IAAN9P,IACLoK,EAAItC,EAAEqF,OAAOqO,IACL/B,IACJO,EAAO,EAAF5P,EAAM,KAAe0F,IAE5BhI,EAAE4F,UAAYoC,EAAOkK,EAAO,EAAF5P,EAAM,IAAY4P,EAAO,EAAF5P,GACjD4P,EAAO,EAAF5P,EAAM,GAAa0F,GAE1B9P,IAjC0B,CAoChC,CAgZE2b,CAAW7T,EAAGuT,GAGdjB,EAAUJ,EAAMP,EAAU3R,EAAEmF,SAC9B,CAOA,SAAS2O,EAAU9T,EAAGkS,EAAMP,GAK1B,IAAIzZ,EAEA6b,EADAC,GAAW,EAGXC,EAAU/B,EAAK,GAEfpC,EAAQ,EACRoE,EAAY,EACZC,EAAY,EAQhB,IANgB,IAAZF,IACFC,EAAY,IACZC,EAAY,GAEdjC,EAAkB,GAAZP,EAAS,GAAO,GAAa,MAE9BzZ,EAAI,EAAGA,GAAKyZ,EAAUzZ,IACzB6b,EAASE,EACTA,EAAU/B,EAAW,GAALha,EAAE,GAAO,KAEnB4X,EAAQoE,GAAaH,IAAWE,IAG3BnE,EAAQqE,EACjBnU,EAAE+E,QAAiB,EAATgP,IAAwBjE,EAEd,IAAXiE,GAELA,IAAWC,GAAWhU,EAAE+E,QAAiB,EAATgP,KACpC/T,EAAE+E,QAAQqP,OAEDtE,GAAS,GAClB9P,EAAE+E,QAAQsP,MAGVrU,EAAE+E,QAAQuP,MAGZxE,EAAQ,EACRkE,EAAUD,EAEM,IAAZE,GACFC,EAAY,IACZC,EAAY,GAEHJ,IAAWE,GACpBC,EAAY,EACZC,EAAY,IAGZD,EAAY,EACZC,EAAY,GAGlB,CAOA,SAASI,EAAUvU,EAAGkS,EAAMP,GAK1B,IAAIzZ,EAEA6b,EADAC,GAAW,EAGXC,EAAU/B,EAAK,GAEfpC,EAAQ,EACRoE,EAAY,EACZC,EAAY,EAQhB,IALgB,IAAZF,IACFC,EAAY,IACZC,EAAY,GAGTjc,EAAI,EAAGA,GAAKyZ,EAAUzZ,IAIzB,GAHA6b,EAASE,EACTA,EAAU/B,EAAW,GAALha,EAAE,GAAO,OAEnB4X,EAAQoE,GAAaH,IAAWE,GAAtC,CAGO,GAAInE,EAAQqE,EACjB,GAAKlC,EAAUjS,EAAG+T,EAAQ/T,EAAE+E,eAA+B,MAAV+K,QAE7B,IAAXiE,GACLA,IAAWC,IACb/B,EAAUjS,EAAG+T,EAAQ/T,EAAE+E,SACvB+K,KAGFmC,EAAUjS,EAvwBE,GAuwBUA,EAAE+E,SACxBgN,EAAU/R,EAAG8P,EAAM,EAAG,IAEbA,GAAS,IAClBmC,EAAUjS,EAxwBE,GAwwBYA,EAAE+E,SAC1BgN,EAAU/R,EAAG8P,EAAM,EAAG,KAGtBmC,EAAUjS,EAzwBE,GAywBcA,EAAE+E,SAC5BgN,EAAU/R,EAAG8P,EAAM,GAAI,IAGzBA,EAAQ,EACRkE,EAAUD,EACM,IAAZE,GACFC,EAAY,IACZC,EAAY,GAEHJ,IAAWE,GACpBC,EAAY,EACZC,EAAY,IAGZD,EAAY,EACZC,EAAY,EAdd,CAiBJ,CAoHA,IAAIK,GAAmB,EA4BvB,SAASjN,EAAiBvH,EAAGnI,EAAK4c,EAAYpU,GAM5C0R,EAAU/R,EAAG,GAAmBK,EAAO,EAAI,GAAI,GAzfjD,SAAoBL,EAAGnI,EAAKC,EAAK6E,GAM/B+V,EAAU1S,GAENrD,IACFkV,EAAU7R,EAAGlI,GACb+Z,EAAU7R,GAAIlI,IAKhByD,EAAMrB,SAAS8F,EAAEE,YAAaF,EAAE2B,OAAQ9J,EAAKC,EAAKkI,EAAEC,SACpDD,EAAEC,SAAWnI,CACf,CAyeE4c,CAAW1U,EAAGnI,EAAK4c,GAAY,EACjC,CAmKA1d,EAAQmP,SAlMR,SAAkBlG,GAGXwU,KAxmBP,WACE,IAAItc,EACA8P,EACAxO,EACA4Y,EACA7J,EACApD,EAAW,IAAIhM,MAAMiM,IAiBzB,IADA5L,EAAS,EACJ4Y,EAAO,EAAGA,EAAOuC,GAAgBvC,IAEpC,IADAxB,EAAYwB,GAAQ5Y,EACftB,EAAI,EAAGA,EAAK,GAAGkY,EAAYgC,GAAQla,IACtCyY,EAAanX,KAAY4Y,EAY7B,IAJAzB,EAAanX,EAAO,GAAK4Y,EAGzB7J,EAAO,EACF6J,EAAO,EAAIA,EAAO,GAAIA,IAEzB,IADAvB,EAAUuB,GAAQ7J,EACbrQ,EAAI,EAAGA,EAAK,GAAGmY,EAAY+B,GAAQla,IACtCwY,EAAWnI,KAAU6J,EAKzB,IADA7J,IAAS,EACF6J,EAAOjC,EAASiC,IAErB,IADAvB,EAAUuB,GAAQ7J,GAAQ,EACrBrQ,EAAI,EAAGA,EAAK,GAAImY,EAAY+B,GAAM,EAAKla,IAC1CwY,EAAW,IAAMnI,KAAU6J,EAM/B,IAAKpK,EAAO,EAAGA,GAAQ5C,EAAU4C,IAC/B7C,EAAS6C,GAAQ,EAInB,IADA9P,EAAI,EACGA,GAAK,KACVsY,EAAe,EAAFtY,EAAM,GAAa,EAChCA,IACAiN,EAAS,KAEX,KAAOjN,GAAK,KACVsY,EAAe,EAAFtY,EAAM,GAAa,EAChCA,IACAiN,EAAS,KAEX,KAAOjN,GAAK,KACVsY,EAAe,EAAFtY,EAAM,GAAa,EAChCA,IACAiN,EAAS,KAEX,KAAOjN,GAAK,KACVsY,EAAe,EAAFtY,EAAM,GAAa,EAChCA,IACAiN,EAAS,KASX,IAHAmN,EAAU9B,EAAcN,IAAW/K,GAG9BjN,EAAI,EAAGA,EAAIiY,EAASjY,IACvBuY,EAAe,EAAFvY,EAAM,GAAa,EAChCuY,EAAe,EAAFvY,GAAgBia,EAAWja,EAAG,GAI7C4Y,EAAgB,IAAIG,EAAeT,EAAcJ,EAAaH,IAAYC,EAAS9K,GACnF2L,EAAgB,IAAIE,EAAeR,EAAcJ,EAAa,EAAYF,EAAS/K,GACnF4L,EAAgB,IAAIC,EAAe,IAAI9X,MAAM,GAAImX,EAAc,EA5a7C,GAiBF,EA8ZlB,CAugBIsE,GACAJ,GAAmB,GAGrBxU,EAAEgF,OAAU,IAAIwM,EAASxR,EAAE4E,UAAWkM,GACtC9Q,EAAEiF,OAAU,IAAIuM,EAASxR,EAAE8E,UAAWiM,GACtC/Q,EAAEkF,QAAU,IAAIsM,EAASxR,EAAE+E,QAASiM,GAEpChR,EAAE+F,OAAS,EACX/F,EAAEgG,SAAW,EAGbwM,EAAWxS,EACb,EAkLAjJ,EAAQwQ,iBAAmBA,EAC3BxQ,EAAQuJ,gBAnJR,SAAyBN,EAAGnI,EAAK4c,EAAYpU,GAM3C,IAAIwU,EAAUC,EACVC,EAAc,EAGd/U,EAAE3B,MAAQ,GAhhCY,IAmhCpB2B,EAAE1D,KAAK3E,YACTqI,EAAE1D,KAAK3E,UArGb,SAA0BqI,GAKxB,IACI9H,EADA8c,EAAa,WAIjB,IAAK9c,EAAI,EAAGA,GAAK,GAAIA,IAAK8c,KAAgB,EACxC,GAAkB,EAAbA,GAAkD,IAA9BhV,EAAE4E,UAAY,EAAF1M,GACnC,OA77BsB,EAk8B1B,GAAoC,IAAhC8H,EAAE4E,UAAU,KAA0D,IAAjC5E,EAAE4E,UAAU,KAChB,IAAjC5E,EAAE4E,UAAU,IACd,OAn8BwB,EAq8B1B,IAAK1M,EAAI,GAAIA,EAAI+X,EAAU/X,IACzB,GAAoC,IAAhC8H,EAAE4E,UAAc,EAAJ1M,GACd,OAv8BsB,EA88B1B,OA/8B0B,CAg9B5B,CAuEyB+c,CAAiBjV,IAItCsT,EAAWtT,EAAGA,EAAEgF,QAIhBsO,EAAWtT,EAAGA,EAAEiF,QAUhB8P,EAnMJ,SAAuB/U,GACrB,IAAI+U,EAgBJ,IAbAjB,EAAU9T,EAAGA,EAAE4E,UAAW5E,EAAEgF,OAAO2M,UACnCmC,EAAU9T,EAAGA,EAAE8E,UAAW9E,EAAEiF,OAAO0M,UAGnC2B,EAAWtT,EAAGA,EAAEkF,SASX6P,EAAcG,GAAYH,GAAe,GACW,IAAnD/U,EAAE+E,QAA8B,EAAtBwL,EAASwE,GAAiB,GADOA,KAUjD,OAJA/U,EAAE4F,SAAW,GAAGmP,EAAY,GAAK,EAAE,EAAE,EAI9BA,CACT,CAuKkBI,CAAcnV,GAG5B6U,EAAY7U,EAAE4F,QAAQ,EAAE,IAAO,GAC/BkP,EAAe9U,EAAE6F,WAAW,EAAE,IAAO,IAMlBgP,IAAYA,EAAWC,IAI1CD,EAAWC,EAAcL,EAAa,EAGnCA,EAAW,GAAKI,IAAuB,IAAThd,EASjC0P,EAAiBvH,EAAGnI,EAAK4c,EAAYpU,GAvkCb,IAykCfL,EAAExB,UAAwBsW,IAAgBD,GAEnD9C,EAAU/R,EAAG,GAAqBK,EAAO,EAAI,GAAI,GACjD4S,EAAejT,EAAGwQ,EAAcC,KAGhCsB,EAAU/R,EAAG,GAAkBK,EAAO,EAAI,GAAI,GAjMlD,SAAwBL,EAAGoV,EAAQC,EAAQC,GAIzC,IAAI1V,EASJ,IAHAmS,EAAU/R,EAAGoV,EAAO,IAAK,GACzBrD,EAAU/R,EAAGqV,EAAO,EAAK,GACzBtD,EAAU/R,EAAGsV,EAAQ,EAAI,GACpB1V,EAAO,EAAGA,EAAO0V,EAAS1V,IAE7BmS,EAAU/R,EAAGA,EAAE+E,QAAuB,EAAfwL,EAAS3Q,GAAU,GAAY,GAIxD2U,EAAUvU,EAAGA,EAAE4E,UAAWwQ,EAAO,GAGjCb,EAAUvU,EAAGA,EAAE8E,UAAWuQ,EAAO,EAEnC,CA0KIE,CAAevV,EAAGA,EAAEgF,OAAO2M,SAAS,EAAG3R,EAAEiF,OAAO0M,SAAS,EAAGoD,EAAY,GACxE9B,EAAejT,EAAGA,EAAE4E,UAAW5E,EAAE8E,YAMnC0N,EAAWxS,GAEPK,GACFqS,EAAU1S,EAId,EAmEAjJ,EAAQuM,UA7DR,SAAmBtD,EAAGuI,EAAM6K,GAmD1B,OA5CApT,EAAEE,YAAYF,EAAE2F,MAAqB,EAAb3F,EAAEyD,UAAqB8E,IAAS,EAAK,IAC7DvI,EAAEE,YAAYF,EAAE2F,MAAqB,EAAb3F,EAAEyD,SAAe,GAAY,IAAP8E,EAE9CvI,EAAEE,YAAYF,EAAEyF,MAAQzF,EAAEyD,UAAiB,IAAL2P,EACtCpT,EAAEyD,WAEW,IAAT8E,EAEFvI,EAAE4E,UAAa,EAAHwO,MAEZpT,EAAE8F,UAEFyC,IAKAvI,EAAE4E,UAA0C,GAA/B+L,EAAayC,GAAInD,EAAS,MACvCjQ,EAAE8E,UAAyB,EAAf8M,EAAOrJ,OA0BbvI,EAAEyD,WAAazD,EAAE0F,YAAY,CAKvC,EAMA3O,EAAQuQ,UAhKR,SAAmBtH,GACjB+R,EAAU/R,EAAGwV,EAAiB,GAC9BvD,EAAUjS,EAt8BM,IAs8BQwQ,GA5yB1B,SAAkBxQ,GACG,KAAfA,EAAEgG,UACJ6L,EAAU7R,EAAGA,EAAE+F,QACf/F,EAAE+F,OAAS,EACX/F,EAAEgG,SAAW,GAEJhG,EAAEgG,UAAY,IACvBhG,EAAEE,YAAYF,EAAEC,WAAwB,IAAXD,EAAE+F,OAC/B/F,EAAE+F,SAAW,EACb/F,EAAEgG,UAAY,EAElB,CAkyBEyP,CAASzV,EACX,C", "sources": ["../node_modules/pako/lib/zlib/messages.js", "../node_modules/pako/lib/zlib/zstream.js", "../node_modules/pako/lib/zlib/adler32.js", "../node_modules/pako/lib/zlib/crc32.js", "../node_modules/pako/lib/utils/common.js", "../node_modules/pako/lib/inflate.js", "../node_modules/pako/lib/deflate.js", "../node_modules/pako/lib/zlib/deflate.js", "../node_modules/pako/lib/zlib/inffast.js", "../node_modules/pako/lib/zlib/inflate.js", "../node_modules/pako/lib/zlib/gzheader.js", "../node_modules/pako/index.js", "../node_modules/pako/lib/zlib/constants.js", "../node_modules/pako/lib/utils/strings.js", "../node_modules/pako/lib/zlib/inftrees.js", "../node_modules/pako/lib/zlib/trees.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  '2':    'need dictionary',     /* Z_NEED_DICT       2  */\n  '1':    'stream end',          /* Z_STREAM_END      1  */\n  '0':    '',                    /* Z_OK              0  */\n  '-1':   'file error',          /* Z_ERRNO         (-1) */\n  '-2':   'stream error',        /* Z_STREAM_ERROR  (-2) */\n  '-3':   'data error',          /* Z_DATA_ERROR    (-3) */\n  '-4':   'insufficient memory', /* Z_MEM_ERROR     (-4) */\n  '-5':   'buffer error',        /* Z_BUF_ERROR     (-5) */\n  '-6':   'incompatible version' /* Z_VERSION_ERROR (-6) */\n};\n", "'use strict';\n\n\nfunction ZStream() {\n  /* next input byte */\n  this.input = null; // JS specific, because we have no pointers\n  this.next_in = 0;\n  /* number of bytes available at input */\n  this.avail_in = 0;\n  /* total number of input bytes read so far */\n  this.total_in = 0;\n  /* next output byte should be put there */\n  this.output = null; // JS specific, because we have no pointers\n  this.next_out = 0;\n  /* remaining free space at output */\n  this.avail_out = 0;\n  /* total number of bytes output so far */\n  this.total_out = 0;\n  /* last error message, NULL if no error */\n  this.msg = ''/*Z_NULL*/;\n  /* not visible by applications */\n  this.state = null;\n  /* best guess about the data type: binary or text */\n  this.data_type = 2/*Z_UNKNOWN*/;\n  /* adler32 value of the uncompressed data */\n  this.adler = 0;\n}\n\nmodule.exports = ZStream;\n", "'use strict';\n\n// Note: adler32 takes 12% for level 0 and 2% for level 6.\n// It doesn't worth to make additional optimizationa as in original.\n// Small size is preferable.\n\nfunction adler32(adler, buf, len, pos) {\n  var s1 = (adler & 0xffff) |0,\n      s2 = ((adler >>> 16) & 0xffff) |0,\n      n = 0;\n\n  while (len !== 0) {\n    // Set limit ~ twice less than 5552, to keep\n    // s2 in 31-bits, because we force signed ints.\n    // in other case %= will fail.\n    n = len > 2000 ? 2000 : len;\n    len -= n;\n\n    do {\n      s1 = (s1 + buf[pos++]) |0;\n      s2 = (s2 + s1) |0;\n    } while (--n);\n\n    s1 %= 65521;\n    s2 %= 65521;\n  }\n\n  return (s1 | (s2 << 16)) |0;\n}\n\n\nmodule.exports = adler32;\n", "'use strict';\n\n// Note: we can't get significant speed boost here.\n// So write code to minimize size - no pregenerated tables\n// and array tools dependencies.\n\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n  var c, table = [];\n\n  for (var n =0; n < 256; n++) {\n    c = n;\n    for (var k =0; k < 8; k++) {\n      c = ((c&1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n    }\n    table[n] = c;\n  }\n\n  return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n  var t = crcTable,\n      end = pos + len;\n\n  crc = crc ^ (-1);\n\n  for (var i = pos; i < end; i++) {\n    crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n  }\n\n  return (crc ^ (-1)); // >>> 0;\n}\n\n\nmodule.exports = crc32;\n", "'use strict';\n\n\nvar TYPED_OK =  (typeof Uint8Array !== 'undefined') &&\n                (typeof Uint16Array !== 'undefined') &&\n                (typeof Int32Array !== 'undefined');\n\n\nexports.assign = function (obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n  while (sources.length) {\n    var source = sources.shift();\n    if (!source) { continue; }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be non-object');\n    }\n\n    for (var p in source) {\n      if (source.hasOwnProperty(p)) {\n        obj[p] = source[p];\n      }\n    }\n  }\n\n  return obj;\n};\n\n\n// reduce buffer size, avoiding mem copy\nexports.shrinkBuf = function (buf, size) {\n  if (buf.length === size) { return buf; }\n  if (buf.subarray) { return buf.subarray(0, size); }\n  buf.length = size;\n  return buf;\n};\n\n\nvar fnTyped = {\n  arraySet: function (dest, src, src_offs, len, dest_offs) {\n    if (src.subarray && dest.subarray) {\n      dest.set(src.subarray(src_offs, src_offs+len), dest_offs);\n      return;\n    }\n    // Fallback to ordinary array\n    for (var i=0; i<len; i++) {\n      dest[dest_offs + i] = src[src_offs + i];\n    }\n  },\n  // Join array of chunks to single array.\n  flattenChunks: function(chunks) {\n    var i, l, len, pos, chunk, result;\n\n    // calculate data length\n    len = 0;\n    for (i=0, l=chunks.length; i<l; i++) {\n      len += chunks[i].length;\n    }\n\n    // join chunks\n    result = new Uint8Array(len);\n    pos = 0;\n    for (i=0, l=chunks.length; i<l; i++) {\n      chunk = chunks[i];\n      result.set(chunk, pos);\n      pos += chunk.length;\n    }\n\n    return result;\n  }\n};\n\nvar fnUntyped = {\n  arraySet: function (dest, src, src_offs, len, dest_offs) {\n    for (var i=0; i<len; i++) {\n      dest[dest_offs + i] = src[src_offs + i];\n    }\n  },\n  // Join array of chunks to single array.\n  flattenChunks: function(chunks) {\n    return [].concat.apply([], chunks);\n  }\n};\n\n\n// Enable/Disable typed arrays use, for testing\n//\nexports.setTyped = function (on) {\n  if (on) {\n    exports.Buf8  = Uint8Array;\n    exports.Buf16 = Uint16Array;\n    exports.Buf32 = Int32Array;\n    exports.assign(exports, fnTyped);\n  } else {\n    exports.Buf8  = Array;\n    exports.Buf16 = Array;\n    exports.Buf32 = Array;\n    exports.assign(exports, fnUntyped);\n  }\n};\n\nexports.setTyped(TYPED_OK);\n", "'use strict';\n\n\nvar zlib_inflate = require('./zlib/inflate.js');\nvar utils = require('./utils/common');\nvar strings = require('./utils/strings');\nvar c = require('./zlib/constants');\nvar msg = require('./zlib/messages');\nvar zstream = require('./zlib/zstream');\nvar gzheader = require('./zlib/gzheader');\n\nvar toString = Object.prototype.toString;\n\n/**\n * class Inflate\n *\n * Generic JS-style wrapper for zlib calls. If you don't need\n * streaming behaviour - use more simple functions: [[inflate]]\n * and [[inflateRaw]].\n **/\n\n/* internal\n * inflate.chunks -> Array\n *\n * Chunks of output data, if [[Inflate#onData]] not overriden.\n **/\n\n/**\n * Inflate.result -> Uint8Array|Array|String\n *\n * Uncompressed result, generated by default [[Inflate#onData]]\n * and [[Inflate#onEnd]] handlers. Filled after you push last chunk\n * (call [[Inflate#push]] with `Z_FINISH` / `true` param) or if you\n * push a chunk with explicit flush (call [[Inflate#push]] with\n * `Z_SYNC_FLUSH` param).\n **/\n\n/**\n * Inflate.err -> Number\n *\n * Error code after inflate finished. 0 (Z_OK) on success.\n * Should be checked if broken data possible.\n **/\n\n/**\n * Inflate.msg -> String\n *\n * Error message, if [[Inflate.err]] != 0\n **/\n\n\n/**\n * new Inflate(options)\n * - options (Object): zlib inflate options.\n *\n * Creates new inflator instance with specified params. Throws exception\n * on bad params. Supported options:\n *\n * - `windowBits`\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information on these.\n *\n * Additional options, for internal needs:\n *\n * - `chunkSize` - size of generated data chunks (16K by default)\n * - `raw` (Boolean) - do raw inflate\n * - `to` (String) - if equal to 'string', then result will be converted\n *   from utf8 to utf16 (javascript) string. When string output requested,\n *   chunk length can differ from `chunkSize`, depending on content.\n *\n * By default, when no options set, autodetect deflate/gzip data format via\n * wrapper header.\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , chunk1 = Uint8Array([1,2,3,4,5,6,7,8,9])\n *   , chunk2 = Uint8Array([10,11,12,13,14,15,16,17,18,19]);\n *\n * var inflate = new pako.Inflate({ level: 3});\n *\n * inflate.push(chunk1, false);\n * inflate.push(chunk2, true);  // true -> last chunk\n *\n * if (inflate.err) { throw new Error(inflate.err); }\n *\n * console.log(inflate.result);\n * ```\n **/\nvar Inflate = function(options) {\n\n  this.options = utils.assign({\n    chunkSize: 16384,\n    windowBits: 0,\n    to: ''\n  }, options || {});\n\n  var opt = this.options;\n\n  // Force window size for `raw` data, if not set directly,\n  // because we have no header for autodetect.\n  if (opt.raw && (opt.windowBits >= 0) && (opt.windowBits < 16)) {\n    opt.windowBits = -opt.windowBits;\n    if (opt.windowBits === 0) { opt.windowBits = -15; }\n  }\n\n  // If `windowBits` not defined (and mode not raw) - set autodetect flag for gzip/deflate\n  if ((opt.windowBits >= 0) && (opt.windowBits < 16) &&\n      !(options && options.windowBits)) {\n    opt.windowBits += 32;\n  }\n\n  // Gzip header has no info about windows size, we can do autodetect only\n  // for deflate. So, if window size not set, force it to max when gzip possible\n  if ((opt.windowBits > 15) && (opt.windowBits < 48)) {\n    // bit 3 (16) -> gzipped data\n    // bit 4 (32) -> autodetect gzip/deflate\n    if ((opt.windowBits & 15) === 0) {\n      opt.windowBits |= 15;\n    }\n  }\n\n  this.err    = 0;      // error code, if happens (0 = Z_OK)\n  this.msg    = '';     // error message\n  this.ended  = false;  // used to avoid multiple onEnd() calls\n  this.chunks = [];     // chunks of compressed data\n\n  this.strm   = new zstream();\n  this.strm.avail_out = 0;\n\n  var status  = zlib_inflate.inflateInit2(\n    this.strm,\n    opt.windowBits\n  );\n\n  if (status !== c.Z_OK) {\n    throw new Error(msg[status]);\n  }\n\n  this.header = new gzheader();\n\n  zlib_inflate.inflateGetHeader(this.strm, this.header);\n};\n\n/**\n * Inflate#push(data[, mode]) -> Boolean\n * - data (Uint8Array|Array|ArrayBuffer|String): input data\n * - mode (Number|Boolean): 0..6 for corresponding Z_NO_FLUSH..Z_TREE modes.\n *   See constants. Skipped or `false` means Z_NO_FLUSH, `true` meansh Z_FINISH.\n *\n * Sends input data to inflate pipe, generating [[Inflate#onData]] calls with\n * new output chunks. Returns `true` on success. The last data block must have\n * mode Z_FINISH (or `true`). That will flush internal pending buffers and call\n * [[Inflate#onEnd]]. For interim explicit flushes (without ending the stream) you\n * can use mode Z_SYNC_FLUSH, keeping the decompression context.\n *\n * On fail call [[Inflate#onEnd]] with error code and return false.\n *\n * We strongly recommend to use `Uint8Array` on input for best speed (output\n * format is detected automatically). Also, don't skip last param and always\n * use the same type in your code (boolean or number). That will improve JS speed.\n *\n * For regular `Array`-s make sure all elements are [0..255].\n *\n * ##### Example\n *\n * ```javascript\n * push(chunk, false); // push one of data chunks\n * ...\n * push(chunk, true);  // push last chunk\n * ```\n **/\nInflate.prototype.push = function(data, mode) {\n  var strm = this.strm;\n  var chunkSize = this.options.chunkSize;\n  var status, _mode;\n  var next_out_utf8, tail, utf8str;\n\n  if (this.ended) { return false; }\n  _mode = (mode === ~~mode) ? mode : ((mode === true) ? c.Z_FINISH : c.Z_NO_FLUSH);\n\n  // Convert data if needed\n  if (typeof data === 'string') {\n    // Only binary strings can be decompressed on practice\n    strm.input = strings.binstring2buf(data);\n  } else if (toString.call(data) === '[object ArrayBuffer]') {\n    strm.input = new Uint8Array(data);\n  } else {\n    strm.input = data;\n  }\n\n  strm.next_in = 0;\n  strm.avail_in = strm.input.length;\n\n  do {\n    if (strm.avail_out === 0) {\n      strm.output = new utils.Buf8(chunkSize);\n      strm.next_out = 0;\n      strm.avail_out = chunkSize;\n    }\n\n    status = zlib_inflate.inflate(strm, c.Z_NO_FLUSH);    /* no bad return value */\n\n    if (status !== c.Z_STREAM_END && status !== c.Z_OK) {\n      this.onEnd(status);\n      this.ended = true;\n      return false;\n    }\n\n    if (strm.next_out) {\n      if (strm.avail_out === 0 || status === c.Z_STREAM_END || (strm.avail_in === 0 && (_mode === c.Z_FINISH || _mode === c.Z_SYNC_FLUSH))) {\n\n        if (this.options.to === 'string') {\n\n          next_out_utf8 = strings.utf8border(strm.output, strm.next_out);\n\n          tail = strm.next_out - next_out_utf8;\n          utf8str = strings.buf2string(strm.output, next_out_utf8);\n\n          // move tail\n          strm.next_out = tail;\n          strm.avail_out = chunkSize - tail;\n          if (tail) { utils.arraySet(strm.output, strm.output, next_out_utf8, tail, 0); }\n\n          this.onData(utf8str);\n\n        } else {\n          this.onData(utils.shrinkBuf(strm.output, strm.next_out));\n        }\n      }\n    }\n  } while ((strm.avail_in > 0) && status !== c.Z_STREAM_END);\n\n  if (status === c.Z_STREAM_END) {\n    _mode = c.Z_FINISH;\n  }\n\n  // Finalize on the last chunk.\n  if (_mode === c.Z_FINISH) {\n    status = zlib_inflate.inflateEnd(this.strm);\n    this.onEnd(status);\n    this.ended = true;\n    return status === c.Z_OK;\n  }\n\n  // callback interim results if Z_SYNC_FLUSH.\n  if (_mode === c.Z_SYNC_FLUSH) {\n    this.onEnd(c.Z_OK);\n    strm.avail_out = 0;\n    return true;\n  }\n\n  return true;\n};\n\n\n/**\n * Inflate#onData(chunk) -> Void\n * - chunk (Uint8Array|Array|String): ouput data. Type of array depends\n *   on js engine support. When string output requested, each chunk\n *   will be string.\n *\n * By default, stores data blocks in `chunks[]` property and glue\n * those in `onEnd`. Override this handler, if you need another behaviour.\n **/\nInflate.prototype.onData = function(chunk) {\n  this.chunks.push(chunk);\n};\n\n\n/**\n * Inflate#onEnd(status) -> Void\n * - status (Number): inflate status. 0 (Z_OK) on success,\n *   other if not.\n *\n * Called either after you tell inflate that the input stream is\n * complete (Z_FINISH) or should be flushed (Z_SYNC_FLUSH)\n * or if an error happened. By default - join collected chunks,\n * free memory and fill `results` / `err` properties.\n **/\nInflate.prototype.onEnd = function(status) {\n  // On success - join\n  if (status === c.Z_OK) {\n    if (this.options.to === 'string') {\n      // Glue & convert here, until we teach pako to send\n      // utf8 alligned strings to onData\n      this.result = this.chunks.join('');\n    } else {\n      this.result = utils.flattenChunks(this.chunks);\n    }\n  }\n  this.chunks = [];\n  this.err = status;\n  this.msg = this.strm.msg;\n};\n\n\n/**\n * inflate(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to decompress.\n * - options (Object): zlib inflate options.\n *\n * Decompress `data` with inflate/ungzip and `options`. Autodetect\n * format via wrapper header by default. That's why we don't provide\n * separate `ungzip` method.\n *\n * Supported options are:\n *\n * - windowBits\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information.\n *\n * Sugar (options):\n *\n * - `raw` (Boolean) - say that we work with raw stream, if you don't wish to specify\n *   negative windowBits implicitly.\n * - `to` (String) - if equal to 'string', then result will be converted\n *   from utf8 to utf16 (javascript) string. When string output requested,\n *   chunk length can differ from `chunkSize`, depending on content.\n *\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , input = pako.deflate([1,2,3,4,5,6,7,8,9])\n *   , output;\n *\n * try {\n *   output = pako.inflate(input);\n * } catch (err)\n *   console.log(err);\n * }\n * ```\n **/\nfunction inflate(input, options) {\n  var inflator = new Inflate(options);\n\n  inflator.push(input, true);\n\n  // That will never happens, if you don't cheat with options :)\n  if (inflator.err) { throw inflator.msg; }\n\n  return inflator.result;\n}\n\n\n/**\n * inflateRaw(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to decompress.\n * - options (Object): zlib inflate options.\n *\n * The same as [[inflate]], but creates raw data, without wrapper\n * (header and adler32 crc).\n **/\nfunction inflateRaw(input, options) {\n  options = options || {};\n  options.raw = true;\n  return inflate(input, options);\n}\n\n\n/**\n * ungzip(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to decompress.\n * - options (Object): zlib inflate options.\n *\n * Just shortcut to [[inflate]], because it autodetects format\n * by header.content. Done for convenience.\n **/\n\n\nexports.Inflate = Inflate;\nexports.inflate = inflate;\nexports.inflateRaw = inflateRaw;\nexports.ungzip  = inflate;\n", "'use strict';\n\n\nvar zlib_deflate = require('./zlib/deflate.js');\nvar utils = require('./utils/common');\nvar strings = require('./utils/strings');\nvar msg = require('./zlib/messages');\nvar zstream = require('./zlib/zstream');\n\nvar toString = Object.prototype.toString;\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\nvar Z_NO_FLUSH      = 0;\nvar Z_FINISH        = 4;\n\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\nvar Z_SYNC_FLUSH    = 2;\n\nvar Z_DEFAULT_COMPRESSION = -1;\n\nvar Z_DEFAULT_STRATEGY    = 0;\n\nvar Z_DEFLATED  = 8;\n\n/* ===========================================================================*/\n\n\n/**\n * class Deflate\n *\n * Generic JS-style wrapper for zlib calls. If you don't need\n * streaming behaviour - use more simple functions: [[deflate]],\n * [[deflateRaw]] and [[gzip]].\n **/\n\n/* internal\n * Deflate.chunks -> Array\n *\n * Chunks of output data, if [[Deflate#onData]] not overriden.\n **/\n\n/**\n * Deflate.result -> Uint8Array|Array\n *\n * Compressed result, generated by default [[Deflate#onData]]\n * and [[Deflate#onEnd]] handlers. Filled after you push last chunk\n * (call [[Deflate#push]] with `Z_FINISH` / `true` param)  or if you\n * push a chunk with explicit flush (call [[Deflate#push]] with\n * `Z_SYNC_FLUSH` param).\n **/\n\n/**\n * Deflate.err -> Number\n *\n * Error code after deflate finished. 0 (Z_OK) on success.\n * You will not need it in real life, because deflate errors\n * are possible only on wrong options or bad `onData` / `onEnd`\n * custom handlers.\n **/\n\n/**\n * Deflate.msg -> String\n *\n * Error message, if [[Deflate.err]] != 0\n **/\n\n\n/**\n * new Deflate(options)\n * - options (Object): zlib deflate options.\n *\n * Creates new deflator instance with specified params. Throws exception\n * on bad params. Supported options:\n *\n * - `level`\n * - `windowBits`\n * - `memLevel`\n * - `strategy`\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information on these.\n *\n * Additional options, for internal needs:\n *\n * - `chunkSize` - size of generated data chunks (16K by default)\n * - `raw` (Boolean) - do raw deflate\n * - `gzip` (Boolean) - create gzip wrapper\n * - `to` (String) - if equal to 'string', then result will be \"binary string\"\n *    (each char code [0..255])\n * - `header` (Object) - custom header for gzip\n *   - `text` (Boolean) - true if compressed data believed to be text\n *   - `time` (Number) - modification time, unix timestamp\n *   - `os` (Number) - operation system code\n *   - `extra` (Array) - array of bytes with extra data (max 65536)\n *   - `name` (String) - file name (binary string)\n *   - `comment` (String) - comment (binary string)\n *   - `hcrc` (Boolean) - true if header crc should be added\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , chunk1 = Uint8Array([1,2,3,4,5,6,7,8,9])\n *   , chunk2 = Uint8Array([10,11,12,13,14,15,16,17,18,19]);\n *\n * var deflate = new pako.Deflate({ level: 3});\n *\n * deflate.push(chunk1, false);\n * deflate.push(chunk2, true);  // true -> last chunk\n *\n * if (deflate.err) { throw new Error(deflate.err); }\n *\n * console.log(deflate.result);\n * ```\n **/\nvar Deflate = function(options) {\n\n  this.options = utils.assign({\n    level: Z_DEFAULT_COMPRESSION,\n    method: Z_DEFLATED,\n    chunkSize: 16384,\n    windowBits: 15,\n    memLevel: 8,\n    strategy: Z_DEFAULT_STRATEGY,\n    to: ''\n  }, options || {});\n\n  var opt = this.options;\n\n  if (opt.raw && (opt.windowBits > 0)) {\n    opt.windowBits = -opt.windowBits;\n  }\n\n  else if (opt.gzip && (opt.windowBits > 0) && (opt.windowBits < 16)) {\n    opt.windowBits += 16;\n  }\n\n  this.err    = 0;      // error code, if happens (0 = Z_OK)\n  this.msg    = '';     // error message\n  this.ended  = false;  // used to avoid multiple onEnd() calls\n  this.chunks = [];     // chunks of compressed data\n\n  this.strm = new zstream();\n  this.strm.avail_out = 0;\n\n  var status = zlib_deflate.deflateInit2(\n    this.strm,\n    opt.level,\n    opt.method,\n    opt.windowBits,\n    opt.memLevel,\n    opt.strategy\n  );\n\n  if (status !== Z_OK) {\n    throw new Error(msg[status]);\n  }\n\n  if (opt.header) {\n    zlib_deflate.deflateSetHeader(this.strm, opt.header);\n  }\n};\n\n/**\n * Deflate#push(data[, mode]) -> Boolean\n * - data (Uint8Array|Array|ArrayBuffer|String): input data. Strings will be\n *   converted to utf8 byte sequence.\n * - mode (Number|Boolean): 0..6 for corresponding Z_NO_FLUSH..Z_TREE modes.\n *   See constants. Skipped or `false` means Z_NO_FLUSH, `true` meansh Z_FINISH.\n *\n * Sends input data to deflate pipe, generating [[Deflate#onData]] calls with\n * new compressed chunks. Returns `true` on success. The last data block must have\n * mode Z_FINISH (or `true`). That will flush internal pending buffers and call\n * [[Deflate#onEnd]]. For interim explicit flushes (without ending the stream) you\n * can use mode Z_SYNC_FLUSH, keeping the compression context.\n *\n * On fail call [[Deflate#onEnd]] with error code and return false.\n *\n * We strongly recommend to use `Uint8Array` on input for best speed (output\n * array format is detected automatically). Also, don't skip last param and always\n * use the same type in your code (boolean or number). That will improve JS speed.\n *\n * For regular `Array`-s make sure all elements are [0..255].\n *\n * ##### Example\n *\n * ```javascript\n * push(chunk, false); // push one of data chunks\n * ...\n * push(chunk, true);  // push last chunk\n * ```\n **/\nDeflate.prototype.push = function(data, mode) {\n  var strm = this.strm;\n  var chunkSize = this.options.chunkSize;\n  var status, _mode;\n\n  if (this.ended) { return false; }\n\n  _mode = (mode === ~~mode) ? mode : ((mode === true) ? Z_FINISH : Z_NO_FLUSH);\n\n  // Convert data if needed\n  if (typeof data === 'string') {\n    // If we need to compress text, change encoding to utf8.\n    strm.input = strings.string2buf(data);\n  } else if (toString.call(data) === '[object ArrayBuffer]') {\n    strm.input = new Uint8Array(data);\n  } else {\n    strm.input = data;\n  }\n\n  strm.next_in = 0;\n  strm.avail_in = strm.input.length;\n\n  do {\n    if (strm.avail_out === 0) {\n      strm.output = new utils.Buf8(chunkSize);\n      strm.next_out = 0;\n      strm.avail_out = chunkSize;\n    }\n    status = zlib_deflate.deflate(strm, _mode);    /* no bad return value */\n\n    if (status !== Z_STREAM_END && status !== Z_OK) {\n      this.onEnd(status);\n      this.ended = true;\n      return false;\n    }\n    if (strm.avail_out === 0 || (strm.avail_in === 0 && (_mode === Z_FINISH || _mode === Z_SYNC_FLUSH))) {\n      if (this.options.to === 'string') {\n        this.onData(strings.buf2binstring(utils.shrinkBuf(strm.output, strm.next_out)));\n      } else {\n        this.onData(utils.shrinkBuf(strm.output, strm.next_out));\n      }\n    }\n  } while ((strm.avail_in > 0 || strm.avail_out === 0) && status !== Z_STREAM_END);\n\n  // Finalize on the last chunk.\n  if (_mode === Z_FINISH) {\n    status = zlib_deflate.deflateEnd(this.strm);\n    this.onEnd(status);\n    this.ended = true;\n    return status === Z_OK;\n  }\n\n  // callback interim results if Z_SYNC_FLUSH.\n  if (_mode === Z_SYNC_FLUSH) {\n    this.onEnd(Z_OK);\n    strm.avail_out = 0;\n    return true;\n  }\n\n  return true;\n};\n\n\n/**\n * Deflate#onData(chunk) -> Void\n * - chunk (Uint8Array|Array|String): ouput data. Type of array depends\n *   on js engine support. When string output requested, each chunk\n *   will be string.\n *\n * By default, stores data blocks in `chunks[]` property and glue\n * those in `onEnd`. Override this handler, if you need another behaviour.\n **/\nDeflate.prototype.onData = function(chunk) {\n  this.chunks.push(chunk);\n};\n\n\n/**\n * Deflate#onEnd(status) -> Void\n * - status (Number): deflate status. 0 (Z_OK) on success,\n *   other if not.\n *\n * Called once after you tell deflate that the input stream is\n * complete (Z_FINISH) or should be flushed (Z_SYNC_FLUSH)\n * or if an error happened. By default - join collected chunks,\n * free memory and fill `results` / `err` properties.\n **/\nDeflate.prototype.onEnd = function(status) {\n  // On success - join\n  if (status === Z_OK) {\n    if (this.options.to === 'string') {\n      this.result = this.chunks.join('');\n    } else {\n      this.result = utils.flattenChunks(this.chunks);\n    }\n  }\n  this.chunks = [];\n  this.err = status;\n  this.msg = this.strm.msg;\n};\n\n\n/**\n * deflate(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to compress.\n * - options (Object): zlib deflate options.\n *\n * Compress `data` with deflate alrorythm and `options`.\n *\n * Supported options are:\n *\n * - level\n * - windowBits\n * - memLevel\n * - strategy\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information on these.\n *\n * Sugar (options):\n *\n * - `raw` (Boolean) - say that we work with raw stream, if you don't wish to specify\n *   negative windowBits implicitly.\n * - `to` (String) - if equal to 'string', then result will be \"binary string\"\n *    (each char code [0..255])\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , data = Uint8Array([1,2,3,4,5,6,7,8,9]);\n *\n * console.log(pako.deflate(data));\n * ```\n **/\nfunction deflate(input, options) {\n  var deflator = new Deflate(options);\n\n  deflator.push(input, true);\n\n  // That will never happens, if you don't cheat with options :)\n  if (deflator.err) { throw deflator.msg; }\n\n  return deflator.result;\n}\n\n\n/**\n * deflateRaw(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to compress.\n * - options (Object): zlib deflate options.\n *\n * The same as [[deflate]], but creates raw data, without wrapper\n * (header and adler32 crc).\n **/\nfunction deflateRaw(input, options) {\n  options = options || {};\n  options.raw = true;\n  return deflate(input, options);\n}\n\n\n/**\n * gzip(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to compress.\n * - options (Object): zlib deflate options.\n *\n * The same as [[deflate]], but create gzip wrapper instead of\n * deflate one.\n **/\nfunction gzip(input, options) {\n  options = options || {};\n  options.gzip = true;\n  return deflate(input, options);\n}\n\n\nexports.Deflate = Deflate;\nexports.deflate = deflate;\nexports.deflateRaw = deflateRaw;\nexports.gzip = gzip;\n", "'use strict';\n\nvar utils   = require('../utils/common');\nvar trees   = require('./trees');\nvar adler32 = require('./adler32');\nvar crc32   = require('./crc32');\nvar msg   = require('./messages');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n/* Allowed flush values; see deflate() and inflate() below for details */\nvar Z_NO_FLUSH      = 0;\nvar Z_PARTIAL_FLUSH = 1;\n//var Z_SYNC_FLUSH    = 2;\nvar Z_FULL_FLUSH    = 3;\nvar Z_FINISH        = 4;\nvar Z_BLOCK         = 5;\n//var Z_TREES         = 6;\n\n\n/* Return codes for the compression/decompression functions. Negative values\n * are errors, positive values are used for special but normal events.\n */\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\n//var Z_NEED_DICT     = 2;\n//var Z_ERRNO         = -1;\nvar Z_STREAM_ERROR  = -2;\nvar Z_DATA_ERROR    = -3;\n//var Z_MEM_ERROR     = -4;\nvar Z_BUF_ERROR     = -5;\n//var Z_VERSION_ERROR = -6;\n\n\n/* compression levels */\n//var Z_NO_COMPRESSION      = 0;\n//var Z_BEST_SPEED          = 1;\n//var Z_BEST_COMPRESSION    = 9;\nvar Z_DEFAULT_COMPRESSION = -1;\n\n\nvar Z_FILTERED            = 1;\nvar Z_HUFFMAN_ONLY        = 2;\nvar Z_RLE                 = 3;\nvar Z_FIXED               = 4;\nvar Z_DEFAULT_STRATEGY    = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\n//var Z_BINARY              = 0;\n//var Z_TEXT                = 1;\n//var Z_ASCII               = 1; // = Z_TEXT\nvar Z_UNKNOWN             = 2;\n\n\n/* The deflate compression method */\nvar Z_DEFLATED  = 8;\n\n/*============================================================================*/\n\n\nvar MAX_MEM_LEVEL = 9;\n/* Maximum value for memLevel in deflateInit2 */\nvar MAX_WBITS = 15;\n/* 32K LZ77 window */\nvar DEF_MEM_LEVEL = 8;\n\n\nvar LENGTH_CODES  = 29;\n/* number of length codes, not counting the special END_BLOCK code */\nvar LITERALS      = 256;\n/* number of literal bytes 0..255 */\nvar L_CODES       = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\nvar D_CODES       = 30;\n/* number of distance codes */\nvar BL_CODES      = 19;\n/* number of codes used to transfer the bit lengths */\nvar HEAP_SIZE     = 2*L_CODES + 1;\n/* maximum heap size */\nvar MAX_BITS  = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar MIN_MATCH = 3;\nvar MAX_MATCH = 258;\nvar MIN_LOOKAHEAD = (MAX_MATCH + MIN_MATCH + 1);\n\nvar PRESET_DICT = 0x20;\n\nvar INIT_STATE = 42;\nvar EXTRA_STATE = 69;\nvar NAME_STATE = 73;\nvar COMMENT_STATE = 91;\nvar HCRC_STATE = 103;\nvar BUSY_STATE = 113;\nvar FINISH_STATE = 666;\n\nvar BS_NEED_MORE      = 1; /* block not completed, need more input or more output */\nvar BS_BLOCK_DONE     = 2; /* block flush performed */\nvar BS_FINISH_STARTED = 3; /* finish started, need only more output at next deflate */\nvar BS_FINISH_DONE    = 4; /* finish done, accept no more input or output */\n\nvar OS_CODE = 0x03; // Unix :) . Don't detect, use this default.\n\nfunction err(strm, errorCode) {\n  strm.msg = msg[errorCode];\n  return errorCode;\n}\n\nfunction rank(f) {\n  return ((f) << 1) - ((f) > 4 ? 9 : 0);\n}\n\nfunction zero(buf) { var len = buf.length; while (--len >= 0) { buf[len] = 0; } }\n\n\n/* =========================================================================\n * Flush as much pending output as possible. All deflate() output goes\n * through this function so some applications may wish to modify it\n * to avoid allocating a large strm->output buffer and copying into it.\n * (See also read_buf()).\n */\nfunction flush_pending(strm) {\n  var s = strm.state;\n\n  //_tr_flush_bits(s);\n  var len = s.pending;\n  if (len > strm.avail_out) {\n    len = strm.avail_out;\n  }\n  if (len === 0) { return; }\n\n  utils.arraySet(strm.output, s.pending_buf, s.pending_out, len, strm.next_out);\n  strm.next_out += len;\n  s.pending_out += len;\n  strm.total_out += len;\n  strm.avail_out -= len;\n  s.pending -= len;\n  if (s.pending === 0) {\n    s.pending_out = 0;\n  }\n}\n\n\nfunction flush_block_only (s, last) {\n  trees._tr_flush_block(s, (s.block_start >= 0 ? s.block_start : -1), s.strstart - s.block_start, last);\n  s.block_start = s.strstart;\n  flush_pending(s.strm);\n}\n\n\nfunction put_byte(s, b) {\n  s.pending_buf[s.pending++] = b;\n}\n\n\n/* =========================================================================\n * Put a short in the pending buffer. The 16-bit value is put in MSB order.\n * IN assertion: the stream state is correct and there is enough room in\n * pending_buf.\n */\nfunction putShortMSB(s, b) {\n//  put_byte(s, (Byte)(b >> 8));\n//  put_byte(s, (Byte)(b & 0xff));\n  s.pending_buf[s.pending++] = (b >>> 8) & 0xff;\n  s.pending_buf[s.pending++] = b & 0xff;\n}\n\n\n/* ===========================================================================\n * Read a new buffer from the current input stream, update the adler32\n * and total number of bytes read.  All deflate() input goes through\n * this function so some applications may wish to modify it to avoid\n * allocating a large strm->input buffer and copying from it.\n * (See also flush_pending()).\n */\nfunction read_buf(strm, buf, start, size) {\n  var len = strm.avail_in;\n\n  if (len > size) { len = size; }\n  if (len === 0) { return 0; }\n\n  strm.avail_in -= len;\n\n  utils.arraySet(buf, strm.input, strm.next_in, len, start);\n  if (strm.state.wrap === 1) {\n    strm.adler = adler32(strm.adler, buf, len, start);\n  }\n\n  else if (strm.state.wrap === 2) {\n    strm.adler = crc32(strm.adler, buf, len, start);\n  }\n\n  strm.next_in += len;\n  strm.total_in += len;\n\n  return len;\n}\n\n\n/* ===========================================================================\n * Set match_start to the longest match starting at the given string and\n * return its length. Matches shorter or equal to prev_length are discarded,\n * in which case the result is equal to prev_length and match_start is\n * garbage.\n * IN assertions: cur_match is the head of the hash chain for the current\n *   string (strstart) and its distance is <= MAX_DIST, and prev_length >= 1\n * OUT assertion: the match length is not greater than s->lookahead.\n */\nfunction longest_match(s, cur_match) {\n  var chain_length = s.max_chain_length;      /* max hash chain length */\n  var scan = s.strstart; /* current string */\n  var match;                       /* matched string */\n  var len;                           /* length of current match */\n  var best_len = s.prev_length;              /* best match length so far */\n  var nice_match = s.nice_match;             /* stop if match long enough */\n  var limit = (s.strstart > (s.w_size - MIN_LOOKAHEAD)) ?\n      s.strstart - (s.w_size - MIN_LOOKAHEAD) : 0/*NIL*/;\n\n  var _win = s.window; // shortcut\n\n  var wmask = s.w_mask;\n  var prev  = s.prev;\n\n  /* Stop when cur_match becomes <= limit. To simplify the code,\n   * we prevent matches with the string of window index 0.\n   */\n\n  var strend = s.strstart + MAX_MATCH;\n  var scan_end1  = _win[scan + best_len - 1];\n  var scan_end   = _win[scan + best_len];\n\n  /* The code is optimized for HASH_BITS >= 8 and MAX_MATCH-2 multiple of 16.\n   * It is easy to get rid of this optimization if necessary.\n   */\n  // Assert(s->hash_bits >= 8 && MAX_MATCH == 258, \"Code too clever\");\n\n  /* Do not waste too much time if we already have a good match: */\n  if (s.prev_length >= s.good_match) {\n    chain_length >>= 2;\n  }\n  /* Do not look for matches beyond the end of the input. This is necessary\n   * to make deflate deterministic.\n   */\n  if (nice_match > s.lookahead) { nice_match = s.lookahead; }\n\n  // Assert((ulg)s->strstart <= s->window_size-MIN_LOOKAHEAD, \"need lookahead\");\n\n  do {\n    // Assert(cur_match < s->strstart, \"no future\");\n    match = cur_match;\n\n    /* Skip to next match if the match length cannot increase\n     * or if the match length is less than 2.  Note that the checks below\n     * for insufficient lookahead only occur occasionally for performance\n     * reasons.  Therefore uninitialized memory will be accessed, and\n     * conditional jumps will be made that depend on those values.\n     * However the length of the match is limited to the lookahead, so\n     * the output of deflate is not affected by the uninitialized values.\n     */\n\n    if (_win[match + best_len]     !== scan_end  ||\n        _win[match + best_len - 1] !== scan_end1 ||\n        _win[match]                !== _win[scan] ||\n        _win[++match]              !== _win[scan + 1]) {\n      continue;\n    }\n\n    /* The check at best_len-1 can be removed because it will be made\n     * again later. (This heuristic is not always a win.)\n     * It is not necessary to compare scan[2] and match[2] since they\n     * are always equal when the other bytes match, given that\n     * the hash keys are equal and that HASH_BITS >= 8.\n     */\n    scan += 2;\n    match++;\n    // Assert(*scan == *match, \"match[2]?\");\n\n    /* We check for insufficient lookahead only every 8th comparison;\n     * the 256th check will be made at strstart+258.\n     */\n    do {\n      /*jshint noempty:false*/\n    } while (_win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             scan < strend);\n\n    // Assert(scan <= s->window+(unsigned)(s->window_size-1), \"wild scan\");\n\n    len = MAX_MATCH - (strend - scan);\n    scan = strend - MAX_MATCH;\n\n    if (len > best_len) {\n      s.match_start = cur_match;\n      best_len = len;\n      if (len >= nice_match) {\n        break;\n      }\n      scan_end1  = _win[scan + best_len - 1];\n      scan_end   = _win[scan + best_len];\n    }\n  } while ((cur_match = prev[cur_match & wmask]) > limit && --chain_length !== 0);\n\n  if (best_len <= s.lookahead) {\n    return best_len;\n  }\n  return s.lookahead;\n}\n\n\n/* ===========================================================================\n * Fill the window when the lookahead becomes insufficient.\n * Updates strstart and lookahead.\n *\n * IN assertion: lookahead < MIN_LOOKAHEAD\n * OUT assertions: strstart <= window_size-MIN_LOOKAHEAD\n *    At least one byte has been read, or avail_in == 0; reads are\n *    performed for at least two bytes (required for the zip translate_eol\n *    option -- not supported here).\n */\nfunction fill_window(s) {\n  var _w_size = s.w_size;\n  var p, n, m, more, str;\n\n  //Assert(s->lookahead < MIN_LOOKAHEAD, \"already enough lookahead\");\n\n  do {\n    more = s.window_size - s.lookahead - s.strstart;\n\n    // JS ints have 32 bit, block below not needed\n    /* Deal with !@#$% 64K limit: */\n    //if (sizeof(int) <= 2) {\n    //    if (more == 0 && s->strstart == 0 && s->lookahead == 0) {\n    //        more = wsize;\n    //\n    //  } else if (more == (unsigned)(-1)) {\n    //        /* Very unlikely, but possible on 16 bit machine if\n    //         * strstart == 0 && lookahead == 1 (input done a byte at time)\n    //         */\n    //        more--;\n    //    }\n    //}\n\n\n    /* If the window is almost full and there is insufficient lookahead,\n     * move the upper half to the lower one to make room in the upper half.\n     */\n    if (s.strstart >= _w_size + (_w_size - MIN_LOOKAHEAD)) {\n\n      utils.arraySet(s.window, s.window, _w_size, _w_size, 0);\n      s.match_start -= _w_size;\n      s.strstart -= _w_size;\n      /* we now have strstart >= MAX_DIST */\n      s.block_start -= _w_size;\n\n      /* Slide the hash table (could be avoided with 32 bit values\n       at the expense of memory usage). We slide even when level == 0\n       to keep the hash table consistent if we switch back to level > 0\n       later. (Using level 0 permanently is not an optimal usage of\n       zlib, so we don't care about this pathological case.)\n       */\n\n      n = s.hash_size;\n      p = n;\n      do {\n        m = s.head[--p];\n        s.head[p] = (m >= _w_size ? m - _w_size : 0);\n      } while (--n);\n\n      n = _w_size;\n      p = n;\n      do {\n        m = s.prev[--p];\n        s.prev[p] = (m >= _w_size ? m - _w_size : 0);\n        /* If n is not on any hash chain, prev[n] is garbage but\n         * its value will never be used.\n         */\n      } while (--n);\n\n      more += _w_size;\n    }\n    if (s.strm.avail_in === 0) {\n      break;\n    }\n\n    /* If there was no sliding:\n     *    strstart <= WSIZE+MAX_DIST-1 && lookahead <= MIN_LOOKAHEAD - 1 &&\n     *    more == window_size - lookahead - strstart\n     * => more >= window_size - (MIN_LOOKAHEAD-1 + WSIZE + MAX_DIST-1)\n     * => more >= window_size - 2*WSIZE + 2\n     * In the BIG_MEM or MMAP case (not yet supported),\n     *   window_size == input_size + MIN_LOOKAHEAD  &&\n     *   strstart + s->lookahead <= input_size => more >= MIN_LOOKAHEAD.\n     * Otherwise, window_size == 2*WSIZE so more >= 2.\n     * If there was sliding, more >= WSIZE. So in all cases, more >= 2.\n     */\n    //Assert(more >= 2, \"more < 2\");\n    n = read_buf(s.strm, s.window, s.strstart + s.lookahead, more);\n    s.lookahead += n;\n\n    /* Initialize the hash value now that we have some input: */\n    if (s.lookahead + s.insert >= MIN_MATCH) {\n      str = s.strstart - s.insert;\n      s.ins_h = s.window[str];\n\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + 1]); */\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + 1]) & s.hash_mask;\n//#if MIN_MATCH != 3\n//        Call update_hash() MIN_MATCH-3 more times\n//#endif\n      while (s.insert) {\n        /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n        s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + MIN_MATCH-1]) & s.hash_mask;\n\n        s.prev[str & s.w_mask] = s.head[s.ins_h];\n        s.head[s.ins_h] = str;\n        str++;\n        s.insert--;\n        if (s.lookahead + s.insert < MIN_MATCH) {\n          break;\n        }\n      }\n    }\n    /* If the whole input has less than MIN_MATCH bytes, ins_h is garbage,\n     * but this is not important since only literal bytes will be emitted.\n     */\n\n  } while (s.lookahead < MIN_LOOKAHEAD && s.strm.avail_in !== 0);\n\n  /* If the WIN_INIT bytes after the end of the current data have never been\n   * written, then zero those bytes in order to avoid memory check reports of\n   * the use of uninitialized (or uninitialised as Julian writes) bytes by\n   * the longest match routines.  Update the high water mark for the next\n   * time through here.  WIN_INIT is set to MAX_MATCH since the longest match\n   * routines allow scanning to strstart + MAX_MATCH, ignoring lookahead.\n   */\n//  if (s.high_water < s.window_size) {\n//    var curr = s.strstart + s.lookahead;\n//    var init = 0;\n//\n//    if (s.high_water < curr) {\n//      /* Previous high water mark below current data -- zero WIN_INIT\n//       * bytes or up to end of window, whichever is less.\n//       */\n//      init = s.window_size - curr;\n//      if (init > WIN_INIT)\n//        init = WIN_INIT;\n//      zmemzero(s->window + curr, (unsigned)init);\n//      s->high_water = curr + init;\n//    }\n//    else if (s->high_water < (ulg)curr + WIN_INIT) {\n//      /* High water mark at or above current data, but below current data\n//       * plus WIN_INIT -- zero out to current data plus WIN_INIT, or up\n//       * to end of window, whichever is less.\n//       */\n//      init = (ulg)curr + WIN_INIT - s->high_water;\n//      if (init > s->window_size - s->high_water)\n//        init = s->window_size - s->high_water;\n//      zmemzero(s->window + s->high_water, (unsigned)init);\n//      s->high_water += init;\n//    }\n//  }\n//\n//  Assert((ulg)s->strstart <= s->window_size - MIN_LOOKAHEAD,\n//    \"not enough room for search\");\n}\n\n/* ===========================================================================\n * Copy without compression as much as possible from the input stream, return\n * the current block state.\n * This function does not insert new strings in the dictionary since\n * uncompressible data is probably not useful. This function is used\n * only for the level=0 compression option.\n * NOTE: this function should be optimized to avoid extra copying from\n * window to pending_buf.\n */\nfunction deflate_stored(s, flush) {\n  /* Stored blocks are limited to 0xffff bytes, pending_buf is limited\n   * to pending_buf_size, and each stored block has a 5 byte header:\n   */\n  var max_block_size = 0xffff;\n\n  if (max_block_size > s.pending_buf_size - 5) {\n    max_block_size = s.pending_buf_size - 5;\n  }\n\n  /* Copy as much as possible from input to output: */\n  for (;;) {\n    /* Fill the window as much as possible: */\n    if (s.lookahead <= 1) {\n\n      //Assert(s->strstart < s->w_size+MAX_DIST(s) ||\n      //  s->block_start >= (long)s->w_size, \"slide too late\");\n//      if (!(s.strstart < s.w_size + (s.w_size - MIN_LOOKAHEAD) ||\n//        s.block_start >= s.w_size)) {\n//        throw  new Error(\"slide too late\");\n//      }\n\n      fill_window(s);\n      if (s.lookahead === 0 && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n\n      if (s.lookahead === 0) {\n        break;\n      }\n      /* flush the current block */\n    }\n    //Assert(s->block_start >= 0L, \"block gone\");\n//    if (s.block_start < 0) throw new Error(\"block gone\");\n\n    s.strstart += s.lookahead;\n    s.lookahead = 0;\n\n    /* Emit a stored block if pending_buf will be full: */\n    var max_start = s.block_start + max_block_size;\n\n    if (s.strstart === 0 || s.strstart >= max_start) {\n      /* strstart == 0 is possible when wraparound on 16-bit machine */\n      s.lookahead = s.strstart - max_start;\n      s.strstart = max_start;\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n\n\n    }\n    /* Flush if we may have to slide, otherwise block_start may become\n     * negative and the data will be gone:\n     */\n    if (s.strstart - s.block_start >= (s.w_size - MIN_LOOKAHEAD)) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n\n  s.insert = 0;\n\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n\n  if (s.strstart > s.block_start) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n\n  return BS_NEED_MORE;\n}\n\n/* ===========================================================================\n * Compress as much as possible from the input stream, return the current\n * block state.\n * This function does not perform lazy evaluation of matches and inserts\n * new strings in the dictionary only for unmatched strings or for short\n * matches. It is used only for the fast compression options.\n */\nfunction deflate_fast(s, flush) {\n  var hash_head;        /* head of the hash chain */\n  var bflush;           /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break; /* flush the current block */\n      }\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0/*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     * At this point we have always match_length < MIN_MATCH\n     */\n    if (hash_head !== 0/*NIL*/ && ((s.strstart - hash_head) <= (s.w_size - MIN_LOOKAHEAD))) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n    }\n    if (s.match_length >= MIN_MATCH) {\n      // check_match(s, s.strstart, s.match_start, s.match_length); // for debug only\n\n      /*** _tr_tally_dist(s, s.strstart - s.match_start,\n                     s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, s.strstart - s.match_start, s.match_length - MIN_MATCH);\n\n      s.lookahead -= s.match_length;\n\n      /* Insert new strings in the hash table only if the match length\n       * is not too large. This saves time but degrades compression.\n       */\n      if (s.match_length <= s.max_lazy_match/*max_insert_length*/ && s.lookahead >= MIN_MATCH) {\n        s.match_length--; /* string at strstart already in table */\n        do {\n          s.strstart++;\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n          /* strstart never exceeds WSIZE-MAX_MATCH, so there are\n           * always MIN_MATCH bytes ahead.\n           */\n        } while (--s.match_length !== 0);\n        s.strstart++;\n      } else\n      {\n        s.strstart += s.match_length;\n        s.match_length = 0;\n        s.ins_h = s.window[s.strstart];\n        /* UPDATE_HASH(s, s.ins_h, s.window[s.strstart+1]); */\n        s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + 1]) & s.hash_mask;\n\n//#if MIN_MATCH != 3\n//                Call UPDATE_HASH() MIN_MATCH-3 more times\n//#endif\n        /* If lookahead < MIN_MATCH, ins_h is garbage, but it does not\n         * matter since it will be recomputed at next deflate call.\n         */\n      }\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s.window[s.strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = ((s.strstart < (MIN_MATCH-1)) ? s.strstart : MIN_MATCH-1);\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * Same as above, but achieves better compression. We use a lazy\n * evaluation for matches: a match is finally adopted only if there is\n * no better match at the next window position.\n */\nfunction deflate_slow(s, flush) {\n  var hash_head;          /* head of hash chain */\n  var bflush;              /* set if current block must be flushed */\n\n  var max_insert;\n\n  /* Process the input block. */\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) { break; } /* flush the current block */\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0/*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     */\n    s.prev_length = s.match_length;\n    s.prev_match = s.match_start;\n    s.match_length = MIN_MATCH-1;\n\n    if (hash_head !== 0/*NIL*/ && s.prev_length < s.max_lazy_match &&\n        s.strstart - hash_head <= (s.w_size-MIN_LOOKAHEAD)/*MAX_DIST(s)*/) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n\n      if (s.match_length <= 5 &&\n         (s.strategy === Z_FILTERED || (s.match_length === MIN_MATCH && s.strstart - s.match_start > 4096/*TOO_FAR*/))) {\n\n        /* If prev_match is also MIN_MATCH, match_start is garbage\n         * but we will ignore the current match anyway.\n         */\n        s.match_length = MIN_MATCH-1;\n      }\n    }\n    /* If there was a match at the previous step and the current\n     * match is not better, output the previous match:\n     */\n    if (s.prev_length >= MIN_MATCH && s.match_length <= s.prev_length) {\n      max_insert = s.strstart + s.lookahead - MIN_MATCH;\n      /* Do not insert strings in hash table beyond this. */\n\n      //check_match(s, s.strstart-1, s.prev_match, s.prev_length);\n\n      /***_tr_tally_dist(s, s.strstart - 1 - s.prev_match,\n                     s.prev_length - MIN_MATCH, bflush);***/\n      bflush = trees._tr_tally(s, s.strstart - 1- s.prev_match, s.prev_length - MIN_MATCH);\n      /* Insert in hash table all strings up to the end of the match.\n       * strstart-1 and strstart are already inserted. If there is not\n       * enough lookahead, the last two strings are not inserted in\n       * the hash table.\n       */\n      s.lookahead -= s.prev_length-1;\n      s.prev_length -= 2;\n      do {\n        if (++s.strstart <= max_insert) {\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n        }\n      } while (--s.prev_length !== 0);\n      s.match_available = 0;\n      s.match_length = MIN_MATCH-1;\n      s.strstart++;\n\n      if (bflush) {\n        /*** FLUSH_BLOCK(s, 0); ***/\n        flush_block_only(s, false);\n        if (s.strm.avail_out === 0) {\n          return BS_NEED_MORE;\n        }\n        /***/\n      }\n\n    } else if (s.match_available) {\n      /* If there was no match at the previous position, output a\n       * single literal. If there was a match but the current match\n       * is longer, truncate the previous match to a single literal.\n       */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n      /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart-1]);\n\n      if (bflush) {\n        /*** FLUSH_BLOCK_ONLY(s, 0) ***/\n        flush_block_only(s, false);\n        /***/\n      }\n      s.strstart++;\n      s.lookahead--;\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n    } else {\n      /* There is no previous match to compare with, wait for\n       * the next step to decide.\n       */\n      s.match_available = 1;\n      s.strstart++;\n      s.lookahead--;\n    }\n  }\n  //Assert (flush != Z_NO_FLUSH, \"no flush?\");\n  if (s.match_available) {\n    //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n    /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart-1]);\n\n    s.match_available = 0;\n  }\n  s.insert = s.strstart < MIN_MATCH-1 ? s.strstart : MIN_MATCH-1;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n\n  return BS_BLOCK_DONE;\n}\n\n\n/* ===========================================================================\n * For Z_RLE, simply look for runs of bytes, generate matches only of distance\n * one.  Do not maintain a hash table.  (It will be regenerated if this run of\n * deflate switches away from Z_RLE.)\n */\nfunction deflate_rle(s, flush) {\n  var bflush;            /* set if current block must be flushed */\n  var prev;              /* byte at distance one to match */\n  var scan, strend;      /* scan goes up to strend for length of run */\n\n  var _win = s.window;\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the longest run, plus one for the unrolled loop.\n     */\n    if (s.lookahead <= MAX_MATCH) {\n      fill_window(s);\n      if (s.lookahead <= MAX_MATCH && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) { break; } /* flush the current block */\n    }\n\n    /* See how many times the previous byte repeats */\n    s.match_length = 0;\n    if (s.lookahead >= MIN_MATCH && s.strstart > 0) {\n      scan = s.strstart - 1;\n      prev = _win[scan];\n      if (prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan]) {\n        strend = s.strstart + MAX_MATCH;\n        do {\n          /*jshint noempty:false*/\n        } while (prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 scan < strend);\n        s.match_length = MAX_MATCH - (strend - scan);\n        if (s.match_length > s.lookahead) {\n          s.match_length = s.lookahead;\n        }\n      }\n      //Assert(scan <= s->window+(uInt)(s->window_size-1), \"wild scan\");\n    }\n\n    /* Emit match if have run of MIN_MATCH or longer, else emit literal */\n    if (s.match_length >= MIN_MATCH) {\n      //check_match(s, s.strstart, s.strstart - 1, s.match_length);\n\n      /*** _tr_tally_dist(s, 1, s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, 1, s.match_length - MIN_MATCH);\n\n      s.lookahead -= s.match_length;\n      s.strstart += s.match_length;\n      s.match_length = 0;\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * For Z_HUFFMAN_ONLY, do not look for matches.  Do not maintain a hash table.\n * (It will be regenerated if this run of deflate switches away from Huffman.)\n */\nfunction deflate_huff(s, flush) {\n  var bflush;             /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we have a literal to write. */\n    if (s.lookahead === 0) {\n      fill_window(s);\n      if (s.lookahead === 0) {\n        if (flush === Z_NO_FLUSH) {\n          return BS_NEED_MORE;\n        }\n        break;      /* flush the current block */\n      }\n    }\n\n    /* Output a literal byte */\n    s.match_length = 0;\n    //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n    /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n    s.lookahead--;\n    s.strstart++;\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* Values for max_lazy_match, good_match and max_chain_length, depending on\n * the desired pack level (0..9). The values given below have been tuned to\n * exclude worst case performance for pathological files. Better values may be\n * found for specific files.\n */\nvar Config = function (good_length, max_lazy, nice_length, max_chain, func) {\n  this.good_length = good_length;\n  this.max_lazy = max_lazy;\n  this.nice_length = nice_length;\n  this.max_chain = max_chain;\n  this.func = func;\n};\n\nvar configuration_table;\n\nconfiguration_table = [\n  /*      good lazy nice chain */\n  new Config(0, 0, 0, 0, deflate_stored),          /* 0 store only */\n  new Config(4, 4, 8, 4, deflate_fast),            /* 1 max speed, no lazy matches */\n  new Config(4, 5, 16, 8, deflate_fast),           /* 2 */\n  new Config(4, 6, 32, 32, deflate_fast),          /* 3 */\n\n  new Config(4, 4, 16, 16, deflate_slow),          /* 4 lazy matches */\n  new Config(8, 16, 32, 32, deflate_slow),         /* 5 */\n  new Config(8, 16, 128, 128, deflate_slow),       /* 6 */\n  new Config(8, 32, 128, 256, deflate_slow),       /* 7 */\n  new Config(32, 128, 258, 1024, deflate_slow),    /* 8 */\n  new Config(32, 258, 258, 4096, deflate_slow)     /* 9 max compression */\n];\n\n\n/* ===========================================================================\n * Initialize the \"longest match\" routines for a new zlib stream\n */\nfunction lm_init(s) {\n  s.window_size = 2 * s.w_size;\n\n  /*** CLEAR_HASH(s); ***/\n  zero(s.head); // Fill with NIL (= 0);\n\n  /* Set the default configuration parameters:\n   */\n  s.max_lazy_match = configuration_table[s.level].max_lazy;\n  s.good_match = configuration_table[s.level].good_length;\n  s.nice_match = configuration_table[s.level].nice_length;\n  s.max_chain_length = configuration_table[s.level].max_chain;\n\n  s.strstart = 0;\n  s.block_start = 0;\n  s.lookahead = 0;\n  s.insert = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  s.ins_h = 0;\n}\n\n\nfunction DeflateState() {\n  this.strm = null;            /* pointer back to this zlib stream */\n  this.status = 0;            /* as the name implies */\n  this.pending_buf = null;      /* output still pending */\n  this.pending_buf_size = 0;  /* size of pending_buf */\n  this.pending_out = 0;       /* next pending byte to output to the stream */\n  this.pending = 0;           /* nb of bytes in the pending buffer */\n  this.wrap = 0;              /* bit 0 true for zlib, bit 1 true for gzip */\n  this.gzhead = null;         /* gzip header information to write */\n  this.gzindex = 0;           /* where in extra, name, or comment */\n  this.method = Z_DEFLATED; /* can only be DEFLATED */\n  this.last_flush = -1;   /* value of flush param for previous deflate call */\n\n  this.w_size = 0;  /* LZ77 window size (32K by default) */\n  this.w_bits = 0;  /* log2(w_size)  (8..16) */\n  this.w_mask = 0;  /* w_size - 1 */\n\n  this.window = null;\n  /* Sliding window. Input bytes are read into the second half of the window,\n   * and move to the first half later to keep a dictionary of at least wSize\n   * bytes. With this organization, matches are limited to a distance of\n   * wSize-MAX_MATCH bytes, but this ensures that IO is always\n   * performed with a length multiple of the block size.\n   */\n\n  this.window_size = 0;\n  /* Actual size of window: 2*wSize, except when the user input buffer\n   * is directly used as sliding window.\n   */\n\n  this.prev = null;\n  /* Link to older string with same hash index. To limit the size of this\n   * array to 64K, this link is maintained only for the last 32K strings.\n   * An index in this array is thus a window index modulo 32K.\n   */\n\n  this.head = null;   /* Heads of the hash chains or NIL. */\n\n  this.ins_h = 0;       /* hash index of string to be inserted */\n  this.hash_size = 0;   /* number of elements in hash table */\n  this.hash_bits = 0;   /* log2(hash_size) */\n  this.hash_mask = 0;   /* hash_size-1 */\n\n  this.hash_shift = 0;\n  /* Number of bits by which ins_h must be shifted at each input\n   * step. It must be such that after MIN_MATCH steps, the oldest\n   * byte no longer takes part in the hash key, that is:\n   *   hash_shift * MIN_MATCH >= hash_bits\n   */\n\n  this.block_start = 0;\n  /* Window position at the beginning of the current output block. Gets\n   * negative when the window is moved backwards.\n   */\n\n  this.match_length = 0;      /* length of best match */\n  this.prev_match = 0;        /* previous match */\n  this.match_available = 0;   /* set if previous match exists */\n  this.strstart = 0;          /* start of string to insert */\n  this.match_start = 0;       /* start of matching string */\n  this.lookahead = 0;         /* number of valid bytes ahead in window */\n\n  this.prev_length = 0;\n  /* Length of the best match at previous step. Matches not greater than this\n   * are discarded. This is used in the lazy match evaluation.\n   */\n\n  this.max_chain_length = 0;\n  /* To speed up deflation, hash chains are never searched beyond this\n   * length.  A higher limit improves compression ratio but degrades the\n   * speed.\n   */\n\n  this.max_lazy_match = 0;\n  /* Attempt to find a better match only when the current match is strictly\n   * smaller than this value. This mechanism is used only for compression\n   * levels >= 4.\n   */\n  // That's alias to max_lazy_match, don't use directly\n  //this.max_insert_length = 0;\n  /* Insert new strings in the hash table only if the match length is not\n   * greater than this length. This saves time but degrades compression.\n   * max_insert_length is used only for compression levels <= 3.\n   */\n\n  this.level = 0;     /* compression level (1..9) */\n  this.strategy = 0;  /* favor or force Huffman coding*/\n\n  this.good_match = 0;\n  /* Use a faster search when the previous match is longer than this */\n\n  this.nice_match = 0; /* Stop searching when current match exceeds this */\n\n              /* used by trees.c: */\n\n  /* Didn't use ct_data typedef below to suppress compiler warning */\n\n  // struct ct_data_s dyn_ltree[HEAP_SIZE];   /* literal and length tree */\n  // struct ct_data_s dyn_dtree[2*D_CODES+1]; /* distance tree */\n  // struct ct_data_s bl_tree[2*BL_CODES+1];  /* Huffman tree for bit lengths */\n\n  // Use flat array of DOUBLE size, with interleaved fata,\n  // because JS does not support effective\n  this.dyn_ltree  = new utils.Buf16(HEAP_SIZE * 2);\n  this.dyn_dtree  = new utils.Buf16((2*D_CODES+1) * 2);\n  this.bl_tree    = new utils.Buf16((2*BL_CODES+1) * 2);\n  zero(this.dyn_ltree);\n  zero(this.dyn_dtree);\n  zero(this.bl_tree);\n\n  this.l_desc   = null;         /* desc. for literal tree */\n  this.d_desc   = null;         /* desc. for distance tree */\n  this.bl_desc  = null;         /* desc. for bit length tree */\n\n  //ush bl_count[MAX_BITS+1];\n  this.bl_count = new utils.Buf16(MAX_BITS+1);\n  /* number of codes at each bit length for an optimal tree */\n\n  //int heap[2*L_CODES+1];      /* heap used to build the Huffman trees */\n  this.heap = new utils.Buf16(2*L_CODES+1);  /* heap used to build the Huffman trees */\n  zero(this.heap);\n\n  this.heap_len = 0;               /* number of elements in the heap */\n  this.heap_max = 0;               /* element of largest frequency */\n  /* The sons of heap[n] are heap[2*n] and heap[2*n+1]. heap[0] is not used.\n   * The same heap array is used to build all trees.\n   */\n\n  this.depth = new utils.Buf16(2*L_CODES+1); //uch depth[2*L_CODES+1];\n  zero(this.depth);\n  /* Depth of each subtree used as tie breaker for trees of equal frequency\n   */\n\n  this.l_buf = 0;          /* buffer index for literals or lengths */\n\n  this.lit_bufsize = 0;\n  /* Size of match buffer for literals/lengths.  There are 4 reasons for\n   * limiting lit_bufsize to 64K:\n   *   - frequencies can be kept in 16 bit counters\n   *   - if compression is not successful for the first block, all input\n   *     data is still in the window so we can still emit a stored block even\n   *     when input comes from standard input.  (This can also be done for\n   *     all blocks if lit_bufsize is not greater than 32K.)\n   *   - if compression is not successful for a file smaller than 64K, we can\n   *     even emit a stored file instead of a stored block (saving 5 bytes).\n   *     This is applicable only for zip (not gzip or zlib).\n   *   - creating new Huffman trees less frequently may not provide fast\n   *     adaptation to changes in the input data statistics. (Take for\n   *     example a binary file with poorly compressible code followed by\n   *     a highly compressible string table.) Smaller buffer sizes give\n   *     fast adaptation but have of course the overhead of transmitting\n   *     trees more frequently.\n   *   - I can't count above 4\n   */\n\n  this.last_lit = 0;      /* running index in l_buf */\n\n  this.d_buf = 0;\n  /* Buffer index for distances. To simplify the code, d_buf and l_buf have\n   * the same number of elements. To use different lengths, an extra flag\n   * array would be necessary.\n   */\n\n  this.opt_len = 0;       /* bit length of current block with optimal trees */\n  this.static_len = 0;    /* bit length of current block with static trees */\n  this.matches = 0;       /* number of string matches in current block */\n  this.insert = 0;        /* bytes at end of window left to insert */\n\n\n  this.bi_buf = 0;\n  /* Output buffer. bits are inserted starting at the bottom (least\n   * significant bits).\n   */\n  this.bi_valid = 0;\n  /* Number of valid bits in bi_buf.  All bits above the last valid bit\n   * are always zero.\n   */\n\n  // Used for window memory init. We safely ignore it for JS. That makes\n  // sense only for pointers and memory check tools.\n  //this.high_water = 0;\n  /* High water mark offset in window for initialized bytes -- bytes above\n   * this are set to zero in order to avoid memory check warnings when\n   * longest match routines access bytes past the input.  This is then\n   * updated to the new high water mark.\n   */\n}\n\n\nfunction deflateResetKeep(strm) {\n  var s;\n\n  if (!strm || !strm.state) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n  strm.total_in = strm.total_out = 0;\n  strm.data_type = Z_UNKNOWN;\n\n  s = strm.state;\n  s.pending = 0;\n  s.pending_out = 0;\n\n  if (s.wrap < 0) {\n    s.wrap = -s.wrap;\n    /* was made negative by deflate(..., Z_FINISH); */\n  }\n  s.status = (s.wrap ? INIT_STATE : BUSY_STATE);\n  strm.adler = (s.wrap === 2) ?\n    0  // crc32(0, Z_NULL, 0)\n  :\n    1; // adler32(0, Z_NULL, 0)\n  s.last_flush = Z_NO_FLUSH;\n  trees._tr_init(s);\n  return Z_OK;\n}\n\n\nfunction deflateReset(strm) {\n  var ret = deflateResetKeep(strm);\n  if (ret === Z_OK) {\n    lm_init(strm.state);\n  }\n  return ret;\n}\n\n\nfunction deflateSetHeader(strm, head) {\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  if (strm.state.wrap !== 2) { return Z_STREAM_ERROR; }\n  strm.state.gzhead = head;\n  return Z_OK;\n}\n\n\nfunction deflateInit2(strm, level, method, windowBits, memLevel, strategy) {\n  if (!strm) { // === Z_NULL\n    return Z_STREAM_ERROR;\n  }\n  var wrap = 1;\n\n  if (level === Z_DEFAULT_COMPRESSION) {\n    level = 6;\n  }\n\n  if (windowBits < 0) { /* suppress zlib wrapper */\n    wrap = 0;\n    windowBits = -windowBits;\n  }\n\n  else if (windowBits > 15) {\n    wrap = 2;           /* write gzip wrapper instead */\n    windowBits -= 16;\n  }\n\n\n  if (memLevel < 1 || memLevel > MAX_MEM_LEVEL || method !== Z_DEFLATED ||\n    windowBits < 8 || windowBits > 15 || level < 0 || level > 9 ||\n    strategy < 0 || strategy > Z_FIXED) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n\n  if (windowBits === 8) {\n    windowBits = 9;\n  }\n  /* until 256-byte window bug fixed */\n\n  var s = new DeflateState();\n\n  strm.state = s;\n  s.strm = strm;\n\n  s.wrap = wrap;\n  s.gzhead = null;\n  s.w_bits = windowBits;\n  s.w_size = 1 << s.w_bits;\n  s.w_mask = s.w_size - 1;\n\n  s.hash_bits = memLevel + 7;\n  s.hash_size = 1 << s.hash_bits;\n  s.hash_mask = s.hash_size - 1;\n  s.hash_shift = ~~((s.hash_bits + MIN_MATCH - 1) / MIN_MATCH);\n\n  s.window = new utils.Buf8(s.w_size * 2);\n  s.head = new utils.Buf16(s.hash_size);\n  s.prev = new utils.Buf16(s.w_size);\n\n  // Don't need mem init magic for JS.\n  //s.high_water = 0;  /* nothing written to s->window yet */\n\n  s.lit_bufsize = 1 << (memLevel + 6); /* 16K elements by default */\n\n  s.pending_buf_size = s.lit_bufsize * 4;\n  s.pending_buf = new utils.Buf8(s.pending_buf_size);\n\n  s.d_buf = s.lit_bufsize >> 1;\n  s.l_buf = (1 + 2) * s.lit_bufsize;\n\n  s.level = level;\n  s.strategy = strategy;\n  s.method = method;\n\n  return deflateReset(strm);\n}\n\nfunction deflateInit(strm, level) {\n  return deflateInit2(strm, level, Z_DEFLATED, MAX_WBITS, DEF_MEM_LEVEL, Z_DEFAULT_STRATEGY);\n}\n\n\nfunction deflate(strm, flush) {\n  var old_flush, s;\n  var beg, val; // for gzip header write only\n\n  if (!strm || !strm.state ||\n    flush > Z_BLOCK || flush < 0) {\n    return strm ? err(strm, Z_STREAM_ERROR) : Z_STREAM_ERROR;\n  }\n\n  s = strm.state;\n\n  if (!strm.output ||\n      (!strm.input && strm.avail_in !== 0) ||\n      (s.status === FINISH_STATE && flush !== Z_FINISH)) {\n    return err(strm, (strm.avail_out === 0) ? Z_BUF_ERROR : Z_STREAM_ERROR);\n  }\n\n  s.strm = strm; /* just in case */\n  old_flush = s.last_flush;\n  s.last_flush = flush;\n\n  /* Write the header */\n  if (s.status === INIT_STATE) {\n\n    if (s.wrap === 2) { // GZIP header\n      strm.adler = 0;  //crc32(0L, Z_NULL, 0);\n      put_byte(s, 31);\n      put_byte(s, 139);\n      put_byte(s, 8);\n      if (!s.gzhead) { // s->gzhead == Z_NULL\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, s.level === 9 ? 2 :\n                    (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ?\n                     4 : 0));\n        put_byte(s, OS_CODE);\n        s.status = BUSY_STATE;\n      }\n      else {\n        put_byte(s, (s.gzhead.text ? 1 : 0) +\n                    (s.gzhead.hcrc ? 2 : 0) +\n                    (!s.gzhead.extra ? 0 : 4) +\n                    (!s.gzhead.name ? 0 : 8) +\n                    (!s.gzhead.comment ? 0 : 16)\n                );\n        put_byte(s, s.gzhead.time & 0xff);\n        put_byte(s, (s.gzhead.time >> 8) & 0xff);\n        put_byte(s, (s.gzhead.time >> 16) & 0xff);\n        put_byte(s, (s.gzhead.time >> 24) & 0xff);\n        put_byte(s, s.level === 9 ? 2 :\n                    (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ?\n                     4 : 0));\n        put_byte(s, s.gzhead.os & 0xff);\n        if (s.gzhead.extra && s.gzhead.extra.length) {\n          put_byte(s, s.gzhead.extra.length & 0xff);\n          put_byte(s, (s.gzhead.extra.length >> 8) & 0xff);\n        }\n        if (s.gzhead.hcrc) {\n          strm.adler = crc32(strm.adler, s.pending_buf, s.pending, 0);\n        }\n        s.gzindex = 0;\n        s.status = EXTRA_STATE;\n      }\n    }\n    else // DEFLATE header\n    {\n      var header = (Z_DEFLATED + ((s.w_bits - 8) << 4)) << 8;\n      var level_flags = -1;\n\n      if (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2) {\n        level_flags = 0;\n      } else if (s.level < 6) {\n        level_flags = 1;\n      } else if (s.level === 6) {\n        level_flags = 2;\n      } else {\n        level_flags = 3;\n      }\n      header |= (level_flags << 6);\n      if (s.strstart !== 0) { header |= PRESET_DICT; }\n      header += 31 - (header % 31);\n\n      s.status = BUSY_STATE;\n      putShortMSB(s, header);\n\n      /* Save the adler32 of the preset dictionary: */\n      if (s.strstart !== 0) {\n        putShortMSB(s, strm.adler >>> 16);\n        putShortMSB(s, strm.adler & 0xffff);\n      }\n      strm.adler = 1; // adler32(0L, Z_NULL, 0);\n    }\n  }\n\n//#ifdef GZIP\n  if (s.status === EXTRA_STATE) {\n    if (s.gzhead.extra/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n\n      while (s.gzindex < (s.gzhead.extra.length & 0xffff)) {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            break;\n          }\n        }\n        put_byte(s, s.gzhead.extra[s.gzindex] & 0xff);\n        s.gzindex++;\n      }\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (s.gzindex === s.gzhead.extra.length) {\n        s.gzindex = 0;\n        s.status = NAME_STATE;\n      }\n    }\n    else {\n      s.status = NAME_STATE;\n    }\n  }\n  if (s.status === NAME_STATE) {\n    if (s.gzhead.name/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.name.length) {\n          val = s.gzhead.name.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.gzindex = 0;\n        s.status = COMMENT_STATE;\n      }\n    }\n    else {\n      s.status = COMMENT_STATE;\n    }\n  }\n  if (s.status === COMMENT_STATE) {\n    if (s.gzhead.comment/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.comment.length) {\n          val = s.gzhead.comment.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.status = HCRC_STATE;\n      }\n    }\n    else {\n      s.status = HCRC_STATE;\n    }\n  }\n  if (s.status === HCRC_STATE) {\n    if (s.gzhead.hcrc) {\n      if (s.pending + 2 > s.pending_buf_size) {\n        flush_pending(strm);\n      }\n      if (s.pending + 2 <= s.pending_buf_size) {\n        put_byte(s, strm.adler & 0xff);\n        put_byte(s, (strm.adler >> 8) & 0xff);\n        strm.adler = 0; //crc32(0L, Z_NULL, 0);\n        s.status = BUSY_STATE;\n      }\n    }\n    else {\n      s.status = BUSY_STATE;\n    }\n  }\n//#endif\n\n  /* Flush as much pending output as possible */\n  if (s.pending !== 0) {\n    flush_pending(strm);\n    if (strm.avail_out === 0) {\n      /* Since avail_out is 0, deflate will be called again with\n       * more output space, but possibly with both pending and\n       * avail_in equal to zero. There won't be anything to do,\n       * but this is not an error situation so make sure we\n       * return OK instead of BUF_ERROR at next call of deflate:\n       */\n      s.last_flush = -1;\n      return Z_OK;\n    }\n\n    /* Make sure there is something to do and avoid duplicate consecutive\n     * flushes. For repeated and useless calls with Z_FINISH, we keep\n     * returning Z_STREAM_END instead of Z_BUF_ERROR.\n     */\n  } else if (strm.avail_in === 0 && rank(flush) <= rank(old_flush) &&\n    flush !== Z_FINISH) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* User must not provide more input after the first FINISH: */\n  if (s.status === FINISH_STATE && strm.avail_in !== 0) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* Start a new block or continue the current one.\n   */\n  if (strm.avail_in !== 0 || s.lookahead !== 0 ||\n    (flush !== Z_NO_FLUSH && s.status !== FINISH_STATE)) {\n    var bstate = (s.strategy === Z_HUFFMAN_ONLY) ? deflate_huff(s, flush) :\n      (s.strategy === Z_RLE ? deflate_rle(s, flush) :\n        configuration_table[s.level].func(s, flush));\n\n    if (bstate === BS_FINISH_STARTED || bstate === BS_FINISH_DONE) {\n      s.status = FINISH_STATE;\n    }\n    if (bstate === BS_NEED_MORE || bstate === BS_FINISH_STARTED) {\n      if (strm.avail_out === 0) {\n        s.last_flush = -1;\n        /* avoid BUF_ERROR next call, see above */\n      }\n      return Z_OK;\n      /* If flush != Z_NO_FLUSH && avail_out == 0, the next call\n       * of deflate should use the same flush parameter to make sure\n       * that the flush is complete. So we don't have to output an\n       * empty block here, this will be done at next call. This also\n       * ensures that for a very small output buffer, we emit at most\n       * one empty block.\n       */\n    }\n    if (bstate === BS_BLOCK_DONE) {\n      if (flush === Z_PARTIAL_FLUSH) {\n        trees._tr_align(s);\n      }\n      else if (flush !== Z_BLOCK) { /* FULL_FLUSH or SYNC_FLUSH */\n\n        trees._tr_stored_block(s, 0, 0, false);\n        /* For a full flush, this empty block will be recognized\n         * as a special marker by inflate_sync().\n         */\n        if (flush === Z_FULL_FLUSH) {\n          /*** CLEAR_HASH(s); ***/             /* forget history */\n          zero(s.head); // Fill with NIL (= 0);\n\n          if (s.lookahead === 0) {\n            s.strstart = 0;\n            s.block_start = 0;\n            s.insert = 0;\n          }\n        }\n      }\n      flush_pending(strm);\n      if (strm.avail_out === 0) {\n        s.last_flush = -1; /* avoid BUF_ERROR at next call, see above */\n        return Z_OK;\n      }\n    }\n  }\n  //Assert(strm->avail_out > 0, \"bug2\");\n  //if (strm.avail_out <= 0) { throw new Error(\"bug2\");}\n\n  if (flush !== Z_FINISH) { return Z_OK; }\n  if (s.wrap <= 0) { return Z_STREAM_END; }\n\n  /* Write the trailer */\n  if (s.wrap === 2) {\n    put_byte(s, strm.adler & 0xff);\n    put_byte(s, (strm.adler >> 8) & 0xff);\n    put_byte(s, (strm.adler >> 16) & 0xff);\n    put_byte(s, (strm.adler >> 24) & 0xff);\n    put_byte(s, strm.total_in & 0xff);\n    put_byte(s, (strm.total_in >> 8) & 0xff);\n    put_byte(s, (strm.total_in >> 16) & 0xff);\n    put_byte(s, (strm.total_in >> 24) & 0xff);\n  }\n  else\n  {\n    putShortMSB(s, strm.adler >>> 16);\n    putShortMSB(s, strm.adler & 0xffff);\n  }\n\n  flush_pending(strm);\n  /* If avail_out is zero, the application will call deflate again\n   * to flush the rest.\n   */\n  if (s.wrap > 0) { s.wrap = -s.wrap; }\n  /* write the trailer only once! */\n  return s.pending !== 0 ? Z_OK : Z_STREAM_END;\n}\n\nfunction deflateEnd(strm) {\n  var status;\n\n  if (!strm/*== Z_NULL*/ || !strm.state/*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  status = strm.state.status;\n  if (status !== INIT_STATE &&\n    status !== EXTRA_STATE &&\n    status !== NAME_STATE &&\n    status !== COMMENT_STATE &&\n    status !== HCRC_STATE &&\n    status !== BUSY_STATE &&\n    status !== FINISH_STATE\n  ) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n  strm.state = null;\n\n  return status === BUSY_STATE ? err(strm, Z_DATA_ERROR) : Z_OK;\n}\n\n/* =========================================================================\n * Copy the source state to the destination state\n */\n//function deflateCopy(dest, source) {\n//\n//}\n\nexports.deflateInit = deflateInit;\nexports.deflateInit2 = deflateInit2;\nexports.deflateReset = deflateReset;\nexports.deflateResetKeep = deflateResetKeep;\nexports.deflateSetHeader = deflateSetHeader;\nexports.deflate = deflate;\nexports.deflateEnd = deflateEnd;\nexports.deflateInfo = 'pako deflate (from Nodeca project)';\n\n/* Not implemented\nexports.deflateBound = deflateBound;\nexports.deflateCopy = deflateCopy;\nexports.deflateSetDictionary = deflateSetDictionary;\nexports.deflateParams = deflateParams;\nexports.deflatePending = deflatePending;\nexports.deflatePrime = deflatePrime;\nexports.deflateTune = deflateTune;\n*/\n", "'use strict';\n\n// See state defs from inflate.js\nvar BAD = 30;       /* got a data error -- remain here until reset */\nvar TYPE = 12;      /* i: waiting for type bits, including last-flag bit */\n\n/*\n   Decode literal, length, and distance codes and write out the resulting\n   literal and match bytes until either not enough input or output is\n   available, an end-of-block is encountered, or a data error is encountered.\n   When large enough input and output buffers are supplied to inflate(), for\n   example, a 16K input buffer and a 64K output buffer, more than 95% of the\n   inflate execution time is spent in this routine.\n\n   Entry assumptions:\n\n        state.mode === LEN\n        strm.avail_in >= 6\n        strm.avail_out >= 258\n        start >= strm.avail_out\n        state.bits < 8\n\n   On return, state.mode is one of:\n\n        LEN -- ran out of enough output space or enough available input\n        TYPE -- reached end of block code, inflate() to interpret next block\n        BAD -- error in block data\n\n   Notes:\n\n    - The maximum input bits used by a length/distance pair is 15 bits for the\n      length code, 5 bits for the length extra, 15 bits for the distance code,\n      and 13 bits for the distance extra.  This totals 48 bits, or six bytes.\n      Therefore if strm.avail_in >= 6, then there is enough input to avoid\n      checking for available input while decoding.\n\n    - The maximum bytes that a single length/distance pair can output is 258\n      bytes, which is the maximum length that can be coded.  inflate_fast()\n      requires strm.avail_out >= 258 for each loop to avoid checking for\n      output space.\n */\nmodule.exports = function inflate_fast(strm, start) {\n  var state;\n  var _in;                    /* local strm.input */\n  var last;                   /* have enough input while in < last */\n  var _out;                   /* local strm.output */\n  var beg;                    /* inflate()'s initial strm.output */\n  var end;                    /* while out < end, enough space available */\n//#ifdef INFLATE_STRICT\n  var dmax;                   /* maximum distance from zlib header */\n//#endif\n  var wsize;                  /* window size or zero if not using window */\n  var whave;                  /* valid bytes in the window */\n  var wnext;                  /* window write index */\n  var window;                 /* allocated sliding window, if wsize != 0 */\n  var hold;                   /* local strm.hold */\n  var bits;                   /* local strm.bits */\n  var lcode;                  /* local strm.lencode */\n  var dcode;                  /* local strm.distcode */\n  var lmask;                  /* mask for first level of length codes */\n  var dmask;                  /* mask for first level of distance codes */\n  var here;                   /* retrieved table entry */\n  var op;                     /* code bits, operation, extra bits, or */\n                              /*  window position, window bytes to copy */\n  var len;                    /* match length, unused bytes */\n  var dist;                   /* match distance */\n  var from;                   /* where to copy match from */\n  var from_source;\n\n\n  var input, output; // JS specific, because we have no pointers\n\n  /* copy state to local variables */\n  state = strm.state;\n  //here = state.here;\n  _in = strm.next_in;\n  input = strm.input;\n  last = _in + (strm.avail_in - 5);\n  _out = strm.next_out;\n  output = strm.output;\n  beg = _out - (start - strm.avail_out);\n  end = _out + (strm.avail_out - 257);\n//#ifdef INFLATE_STRICT\n  dmax = state.dmax;\n//#endif\n  wsize = state.wsize;\n  whave = state.whave;\n  wnext = state.wnext;\n  window = state.window;\n  hold = state.hold;\n  bits = state.bits;\n  lcode = state.lencode;\n  dcode = state.distcode;\n  lmask = (1 << state.lenbits) - 1;\n  dmask = (1 << state.distbits) - 1;\n\n\n  /* decode literals and length/distances until end-of-block or not enough\n     input data or output space */\n\n  top:\n  do {\n    if (bits < 15) {\n      hold += input[_in++] << bits;\n      bits += 8;\n      hold += input[_in++] << bits;\n      bits += 8;\n    }\n\n    here = lcode[hold & lmask];\n\n    dolen:\n    for (;;) { // Goto emulation\n      op = here >>> 24/*here.bits*/;\n      hold >>>= op;\n      bits -= op;\n      op = (here >>> 16) & 0xff/*here.op*/;\n      if (op === 0) {                          /* literal */\n        //Tracevv((stderr, here.val >= 0x20 && here.val < 0x7f ?\n        //        \"inflate:         literal '%c'\\n\" :\n        //        \"inflate:         literal 0x%02x\\n\", here.val));\n        output[_out++] = here & 0xffff/*here.val*/;\n      }\n      else if (op & 16) {                     /* length base */\n        len = here & 0xffff/*here.val*/;\n        op &= 15;                           /* number of extra bits */\n        if (op) {\n          if (bits < op) {\n            hold += input[_in++] << bits;\n            bits += 8;\n          }\n          len += hold & ((1 << op) - 1);\n          hold >>>= op;\n          bits -= op;\n        }\n        //Tracevv((stderr, \"inflate:         length %u\\n\", len));\n        if (bits < 15) {\n          hold += input[_in++] << bits;\n          bits += 8;\n          hold += input[_in++] << bits;\n          bits += 8;\n        }\n        here = dcode[hold & dmask];\n\n        dodist:\n        for (;;) { // goto emulation\n          op = here >>> 24/*here.bits*/;\n          hold >>>= op;\n          bits -= op;\n          op = (here >>> 16) & 0xff/*here.op*/;\n\n          if (op & 16) {                      /* distance base */\n            dist = here & 0xffff/*here.val*/;\n            op &= 15;                       /* number of extra bits */\n            if (bits < op) {\n              hold += input[_in++] << bits;\n              bits += 8;\n              if (bits < op) {\n                hold += input[_in++] << bits;\n                bits += 8;\n              }\n            }\n            dist += hold & ((1 << op) - 1);\n//#ifdef INFLATE_STRICT\n            if (dist > dmax) {\n              strm.msg = 'invalid distance too far back';\n              state.mode = BAD;\n              break top;\n            }\n//#endif\n            hold >>>= op;\n            bits -= op;\n            //Tracevv((stderr, \"inflate:         distance %u\\n\", dist));\n            op = _out - beg;                /* max distance in output */\n            if (dist > op) {                /* see if copy from window */\n              op = dist - op;               /* distance back in window */\n              if (op > whave) {\n                if (state.sane) {\n                  strm.msg = 'invalid distance too far back';\n                  state.mode = BAD;\n                  break top;\n                }\n\n// (!) This block is disabled in zlib defailts,\n// don't enable it for binary compatibility\n//#ifdef INFLATE_ALLOW_INVALID_DISTANCE_TOOFAR_ARRR\n//                if (len <= op - whave) {\n//                  do {\n//                    output[_out++] = 0;\n//                  } while (--len);\n//                  continue top;\n//                }\n//                len -= op - whave;\n//                do {\n//                  output[_out++] = 0;\n//                } while (--op > whave);\n//                if (op === 0) {\n//                  from = _out - dist;\n//                  do {\n//                    output[_out++] = output[from++];\n//                  } while (--len);\n//                  continue top;\n//                }\n//#endif\n              }\n              from = 0; // window index\n              from_source = window;\n              if (wnext === 0) {           /* very common case */\n                from += wsize - op;\n                if (op < len) {         /* some from window */\n                  len -= op;\n                  do {\n                    output[_out++] = window[from++];\n                  } while (--op);\n                  from = _out - dist;  /* rest from output */\n                  from_source = output;\n                }\n              }\n              else if (wnext < op) {      /* wrap around window */\n                from += wsize + wnext - op;\n                op -= wnext;\n                if (op < len) {         /* some from end of window */\n                  len -= op;\n                  do {\n                    output[_out++] = window[from++];\n                  } while (--op);\n                  from = 0;\n                  if (wnext < len) {  /* some from start of window */\n                    op = wnext;\n                    len -= op;\n                    do {\n                      output[_out++] = window[from++];\n                    } while (--op);\n                    from = _out - dist;      /* rest from output */\n                    from_source = output;\n                  }\n                }\n              }\n              else {                      /* contiguous in window */\n                from += wnext - op;\n                if (op < len) {         /* some from window */\n                  len -= op;\n                  do {\n                    output[_out++] = window[from++];\n                  } while (--op);\n                  from = _out - dist;  /* rest from output */\n                  from_source = output;\n                }\n              }\n              while (len > 2) {\n                output[_out++] = from_source[from++];\n                output[_out++] = from_source[from++];\n                output[_out++] = from_source[from++];\n                len -= 3;\n              }\n              if (len) {\n                output[_out++] = from_source[from++];\n                if (len > 1) {\n                  output[_out++] = from_source[from++];\n                }\n              }\n            }\n            else {\n              from = _out - dist;          /* copy direct from output */\n              do {                        /* minimum length is three */\n                output[_out++] = output[from++];\n                output[_out++] = output[from++];\n                output[_out++] = output[from++];\n                len -= 3;\n              } while (len > 2);\n              if (len) {\n                output[_out++] = output[from++];\n                if (len > 1) {\n                  output[_out++] = output[from++];\n                }\n              }\n            }\n          }\n          else if ((op & 64) === 0) {          /* 2nd level distance code */\n            here = dcode[(here & 0xffff)/*here.val*/ + (hold & ((1 << op) - 1))];\n            continue dodist;\n          }\n          else {\n            strm.msg = 'invalid distance code';\n            state.mode = BAD;\n            break top;\n          }\n\n          break; // need to emulate goto via \"continue\"\n        }\n      }\n      else if ((op & 64) === 0) {              /* 2nd level length code */\n        here = lcode[(here & 0xffff)/*here.val*/ + (hold & ((1 << op) - 1))];\n        continue dolen;\n      }\n      else if (op & 32) {                     /* end-of-block */\n        //Tracevv((stderr, \"inflate:         end of block\\n\"));\n        state.mode = TYPE;\n        break top;\n      }\n      else {\n        strm.msg = 'invalid literal/length code';\n        state.mode = BAD;\n        break top;\n      }\n\n      break; // need to emulate goto via \"continue\"\n    }\n  } while (_in < last && _out < end);\n\n  /* return unused bytes (on entry, bits < 8, so in won't go too far back) */\n  len = bits >> 3;\n  _in -= len;\n  bits -= len << 3;\n  hold &= (1 << bits) - 1;\n\n  /* update state and return */\n  strm.next_in = _in;\n  strm.next_out = _out;\n  strm.avail_in = (_in < last ? 5 + (last - _in) : 5 - (_in - last));\n  strm.avail_out = (_out < end ? 257 + (end - _out) : 257 - (_out - end));\n  state.hold = hold;\n  state.bits = bits;\n  return;\n};\n", "'use strict';\n\n\nvar utils = require('../utils/common');\nvar adler32 = require('./adler32');\nvar crc32   = require('./crc32');\nvar inflate_fast = require('./inffast');\nvar inflate_table = require('./inftrees');\n\nvar CODES = 0;\nvar LENS = 1;\nvar DISTS = 2;\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n/* Allowed flush values; see deflate() and inflate() below for details */\n//var Z_NO_FLUSH      = 0;\n//var Z_PARTIAL_FLUSH = 1;\n//var Z_SYNC_FLUSH    = 2;\n//var Z_FULL_FLUSH    = 3;\nvar Z_FINISH        = 4;\nvar Z_BLOCK         = 5;\nvar Z_TREES         = 6;\n\n\n/* Return codes for the compression/decompression functions. Negative values\n * are errors, positive values are used for special but normal events.\n */\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\nvar Z_NEED_DICT     = 2;\n//var Z_ERRNO         = -1;\nvar Z_STREAM_ERROR  = -2;\nvar Z_DATA_ERROR    = -3;\nvar Z_MEM_ERROR     = -4;\nvar Z_BUF_ERROR     = -5;\n//var Z_VERSION_ERROR = -6;\n\n/* The deflate compression method */\nvar Z_DEFLATED  = 8;\n\n\n/* STATES ====================================================================*/\n/* ===========================================================================*/\n\n\nvar    HEAD = 1;       /* i: waiting for magic header */\nvar    FLAGS = 2;      /* i: waiting for method and flags (gzip) */\nvar    TIME = 3;       /* i: waiting for modification time (gzip) */\nvar    OS = 4;         /* i: waiting for extra flags and operating system (gzip) */\nvar    EXLEN = 5;      /* i: waiting for extra length (gzip) */\nvar    EXTRA = 6;      /* i: waiting for extra bytes (gzip) */\nvar    NAME = 7;       /* i: waiting for end of file name (gzip) */\nvar    COMMENT = 8;    /* i: waiting for end of comment (gzip) */\nvar    HCRC = 9;       /* i: waiting for header crc (gzip) */\nvar    DICTID = 10;    /* i: waiting for dictionary check value */\nvar    DICT = 11;      /* waiting for inflateSetDictionary() call */\nvar        TYPE = 12;      /* i: waiting for type bits, including last-flag bit */\nvar        TYPEDO = 13;    /* i: same, but skip check to exit inflate on new block */\nvar        STORED = 14;    /* i: waiting for stored size (length and complement) */\nvar        COPY_ = 15;     /* i/o: same as COPY below, but only first time in */\nvar        COPY = 16;      /* i/o: waiting for input or output to copy stored block */\nvar        TABLE = 17;     /* i: waiting for dynamic block table lengths */\nvar        LENLENS = 18;   /* i: waiting for code length code lengths */\nvar        CODELENS = 19;  /* i: waiting for length/lit and distance code lengths */\nvar            LEN_ = 20;      /* i: same as LEN below, but only first time in */\nvar            LEN = 21;       /* i: waiting for length/lit/eob code */\nvar            LENEXT = 22;    /* i: waiting for length extra bits */\nvar            DIST = 23;      /* i: waiting for distance code */\nvar            DISTEXT = 24;   /* i: waiting for distance extra bits */\nvar            MATCH = 25;     /* o: waiting for output space to copy string */\nvar            LIT = 26;       /* o: waiting for output space to write literal */\nvar    CHECK = 27;     /* i: waiting for 32-bit check value */\nvar    LENGTH = 28;    /* i: waiting for 32-bit length (gzip) */\nvar    DONE = 29;      /* finished check, done -- remain here until reset */\nvar    BAD = 30;       /* got a data error -- remain here until reset */\nvar    MEM = 31;       /* got an inflate() memory error -- remain here until reset */\nvar    SYNC = 32;      /* looking for synchronization bytes to restart inflate() */\n\n/* ===========================================================================*/\n\n\n\nvar ENOUGH_LENS = 852;\nvar ENOUGH_DISTS = 592;\n//var ENOUGH =  (ENOUGH_LENS+ENOUGH_DISTS);\n\nvar MAX_WBITS = 15;\n/* 32K LZ77 window */\nvar DEF_WBITS = MAX_WBITS;\n\n\nfunction ZSWAP32(q) {\n  return  (((q >>> 24) & 0xff) +\n          ((q >>> 8) & 0xff00) +\n          ((q & 0xff00) << 8) +\n          ((q & 0xff) << 24));\n}\n\n\nfunction InflateState() {\n  this.mode = 0;             /* current inflate mode */\n  this.last = false;          /* true if processing last block */\n  this.wrap = 0;              /* bit 0 true for zlib, bit 1 true for gzip */\n  this.havedict = false;      /* true if dictionary provided */\n  this.flags = 0;             /* gzip header method and flags (0 if zlib) */\n  this.dmax = 0;              /* zlib header max distance (INFLATE_STRICT) */\n  this.check = 0;             /* protected copy of check value */\n  this.total = 0;             /* protected copy of output count */\n  // TODO: may be {}\n  this.head = null;           /* where to save gzip header information */\n\n  /* sliding window */\n  this.wbits = 0;             /* log base 2 of requested window size */\n  this.wsize = 0;             /* window size or zero if not using window */\n  this.whave = 0;             /* valid bytes in the window */\n  this.wnext = 0;             /* window write index */\n  this.window = null;         /* allocated sliding window, if needed */\n\n  /* bit accumulator */\n  this.hold = 0;              /* input bit accumulator */\n  this.bits = 0;              /* number of bits in \"in\" */\n\n  /* for string and stored block copying */\n  this.length = 0;            /* literal or length of data to copy */\n  this.offset = 0;            /* distance back to copy string from */\n\n  /* for table and code decoding */\n  this.extra = 0;             /* extra bits needed */\n\n  /* fixed and dynamic code tables */\n  this.lencode = null;          /* starting table for length/literal codes */\n  this.distcode = null;         /* starting table for distance codes */\n  this.lenbits = 0;           /* index bits for lencode */\n  this.distbits = 0;          /* index bits for distcode */\n\n  /* dynamic table building */\n  this.ncode = 0;             /* number of code length code lengths */\n  this.nlen = 0;              /* number of length code lengths */\n  this.ndist = 0;             /* number of distance code lengths */\n  this.have = 0;              /* number of code lengths in lens[] */\n  this.next = null;              /* next available space in codes[] */\n\n  this.lens = new utils.Buf16(320); /* temporary storage for code lengths */\n  this.work = new utils.Buf16(288); /* work area for code table building */\n\n  /*\n   because we don't have pointers in js, we use lencode and distcode directly\n   as buffers so we don't need codes\n  */\n  //this.codes = new utils.Buf32(ENOUGH);       /* space for code tables */\n  this.lendyn = null;              /* dynamic table for length/literal codes (JS specific) */\n  this.distdyn = null;             /* dynamic table for distance codes (JS specific) */\n  this.sane = 0;                   /* if false, allow invalid distance too far */\n  this.back = 0;                   /* bits back of last unprocessed length/lit */\n  this.was = 0;                    /* initial length of match */\n}\n\nfunction inflateResetKeep(strm) {\n  var state;\n\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n  strm.total_in = strm.total_out = state.total = 0;\n  strm.msg = ''; /*Z_NULL*/\n  if (state.wrap) {       /* to support ill-conceived Java test suite */\n    strm.adler = state.wrap & 1;\n  }\n  state.mode = HEAD;\n  state.last = 0;\n  state.havedict = 0;\n  state.dmax = 32768;\n  state.head = null/*Z_NULL*/;\n  state.hold = 0;\n  state.bits = 0;\n  //state.lencode = state.distcode = state.next = state.codes;\n  state.lencode = state.lendyn = new utils.Buf32(ENOUGH_LENS);\n  state.distcode = state.distdyn = new utils.Buf32(ENOUGH_DISTS);\n\n  state.sane = 1;\n  state.back = -1;\n  //Tracev((stderr, \"inflate: reset\\n\"));\n  return Z_OK;\n}\n\nfunction inflateReset(strm) {\n  var state;\n\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n  state.wsize = 0;\n  state.whave = 0;\n  state.wnext = 0;\n  return inflateResetKeep(strm);\n\n}\n\nfunction inflateReset2(strm, windowBits) {\n  var wrap;\n  var state;\n\n  /* get the state */\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n\n  /* extract wrap request from windowBits parameter */\n  if (windowBits < 0) {\n    wrap = 0;\n    windowBits = -windowBits;\n  }\n  else {\n    wrap = (windowBits >> 4) + 1;\n    if (windowBits < 48) {\n      windowBits &= 15;\n    }\n  }\n\n  /* set number of window bits, free window if different */\n  if (windowBits && (windowBits < 8 || windowBits > 15)) {\n    return Z_STREAM_ERROR;\n  }\n  if (state.window !== null && state.wbits !== windowBits) {\n    state.window = null;\n  }\n\n  /* update state and reset the rest of it */\n  state.wrap = wrap;\n  state.wbits = windowBits;\n  return inflateReset(strm);\n}\n\nfunction inflateInit2(strm, windowBits) {\n  var ret;\n  var state;\n\n  if (!strm) { return Z_STREAM_ERROR; }\n  //strm.msg = Z_NULL;                 /* in case we return an error */\n\n  state = new InflateState();\n\n  //if (state === Z_NULL) return Z_MEM_ERROR;\n  //Tracev((stderr, \"inflate: allocated\\n\"));\n  strm.state = state;\n  state.window = null/*Z_NULL*/;\n  ret = inflateReset2(strm, windowBits);\n  if (ret !== Z_OK) {\n    strm.state = null/*Z_NULL*/;\n  }\n  return ret;\n}\n\nfunction inflateInit(strm) {\n  return inflateInit2(strm, DEF_WBITS);\n}\n\n\n/*\n Return state with length and distance decoding tables and index sizes set to\n fixed code decoding.  Normally this returns fixed tables from inffixed.h.\n If BUILDFIXED is defined, then instead this routine builds the tables the\n first time it's called, and returns those tables the first time and\n thereafter.  This reduces the size of the code by about 2K bytes, in\n exchange for a little execution time.  However, BUILDFIXED should not be\n used for threaded applications, since the rewriting of the tables and virgin\n may not be thread-safe.\n */\nvar virgin = true;\n\nvar lenfix, distfix; // We have no pointers in JS, so keep tables separate\n\nfunction fixedtables(state) {\n  /* build fixed huffman tables if first call (may not be thread safe) */\n  if (virgin) {\n    var sym;\n\n    lenfix = new utils.Buf32(512);\n    distfix = new utils.Buf32(32);\n\n    /* literal/length table */\n    sym = 0;\n    while (sym < 144) { state.lens[sym++] = 8; }\n    while (sym < 256) { state.lens[sym++] = 9; }\n    while (sym < 280) { state.lens[sym++] = 7; }\n    while (sym < 288) { state.lens[sym++] = 8; }\n\n    inflate_table(LENS,  state.lens, 0, 288, lenfix,   0, state.work, {bits: 9});\n\n    /* distance table */\n    sym = 0;\n    while (sym < 32) { state.lens[sym++] = 5; }\n\n    inflate_table(DISTS, state.lens, 0, 32,   distfix, 0, state.work, {bits: 5});\n\n    /* do this just once */\n    virgin = false;\n  }\n\n  state.lencode = lenfix;\n  state.lenbits = 9;\n  state.distcode = distfix;\n  state.distbits = 5;\n}\n\n\n/*\n Update the window with the last wsize (normally 32K) bytes written before\n returning.  If window does not exist yet, create it.  This is only called\n when a window is already in use, or when output has been written during this\n inflate call, but the end of the deflate stream has not been reached yet.\n It is also called to create a window for dictionary data when a dictionary\n is loaded.\n\n Providing output buffers larger than 32K to inflate() should provide a speed\n advantage, since only the last 32K of output is copied to the sliding window\n upon return from inflate(), and since all distances after the first 32K of\n output will fall in the output data, making match copies simpler and faster.\n The advantage may be dependent on the size of the processor's data caches.\n */\nfunction updatewindow(strm, src, end, copy) {\n  var dist;\n  var state = strm.state;\n\n  /* if it hasn't been done already, allocate space for the window */\n  if (state.window === null) {\n    state.wsize = 1 << state.wbits;\n    state.wnext = 0;\n    state.whave = 0;\n\n    state.window = new utils.Buf8(state.wsize);\n  }\n\n  /* copy state->wsize or less output bytes into the circular window */\n  if (copy >= state.wsize) {\n    utils.arraySet(state.window,src, end - state.wsize, state.wsize, 0);\n    state.wnext = 0;\n    state.whave = state.wsize;\n  }\n  else {\n    dist = state.wsize - state.wnext;\n    if (dist > copy) {\n      dist = copy;\n    }\n    //zmemcpy(state->window + state->wnext, end - copy, dist);\n    utils.arraySet(state.window,src, end - copy, dist, state.wnext);\n    copy -= dist;\n    if (copy) {\n      //zmemcpy(state->window, end - copy, copy);\n      utils.arraySet(state.window,src, end - copy, copy, 0);\n      state.wnext = copy;\n      state.whave = state.wsize;\n    }\n    else {\n      state.wnext += dist;\n      if (state.wnext === state.wsize) { state.wnext = 0; }\n      if (state.whave < state.wsize) { state.whave += dist; }\n    }\n  }\n  return 0;\n}\n\nfunction inflate(strm, flush) {\n  var state;\n  var input, output;          // input/output buffers\n  var next;                   /* next input INDEX */\n  var put;                    /* next output INDEX */\n  var have, left;             /* available input and output */\n  var hold;                   /* bit buffer */\n  var bits;                   /* bits in bit buffer */\n  var _in, _out;              /* save starting available input and output */\n  var copy;                   /* number of stored or match bytes to copy */\n  var from;                   /* where to copy match bytes from */\n  var from_source;\n  var here = 0;               /* current decoding table entry */\n  var here_bits, here_op, here_val; // paked \"here\" denormalized (JS specific)\n  //var last;                   /* parent table entry */\n  var last_bits, last_op, last_val; // paked \"last\" denormalized (JS specific)\n  var len;                    /* length to copy for repeats, bits to drop */\n  var ret;                    /* return code */\n  var hbuf = new utils.Buf8(4);    /* buffer for gzip header crc calculation */\n  var opts;\n\n  var n; // temporary var for NEED_BITS\n\n  var order = /* permutation of code lengths */\n    [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15];\n\n\n  if (!strm || !strm.state || !strm.output ||\n      (!strm.input && strm.avail_in !== 0)) {\n    return Z_STREAM_ERROR;\n  }\n\n  state = strm.state;\n  if (state.mode === TYPE) { state.mode = TYPEDO; }    /* skip check */\n\n\n  //--- LOAD() ---\n  put = strm.next_out;\n  output = strm.output;\n  left = strm.avail_out;\n  next = strm.next_in;\n  input = strm.input;\n  have = strm.avail_in;\n  hold = state.hold;\n  bits = state.bits;\n  //---\n\n  _in = have;\n  _out = left;\n  ret = Z_OK;\n\n  inf_leave: // goto emulation\n  for (;;) {\n    switch (state.mode) {\n    case HEAD:\n      if (state.wrap === 0) {\n        state.mode = TYPEDO;\n        break;\n      }\n      //=== NEEDBITS(16);\n      while (bits < 16) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      if ((state.wrap & 2) && hold === 0x8b1f) {  /* gzip header */\n        state.check = 0/*crc32(0L, Z_NULL, 0)*/;\n        //=== CRC2(state.check, hold);\n        hbuf[0] = hold & 0xff;\n        hbuf[1] = (hold >>> 8) & 0xff;\n        state.check = crc32(state.check, hbuf, 2, 0);\n        //===//\n\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        state.mode = FLAGS;\n        break;\n      }\n      state.flags = 0;           /* expect zlib header */\n      if (state.head) {\n        state.head.done = false;\n      }\n      if (!(state.wrap & 1) ||   /* check if zlib header allowed */\n        (((hold & 0xff)/*BITS(8)*/ << 8) + (hold >> 8)) % 31) {\n        strm.msg = 'incorrect header check';\n        state.mode = BAD;\n        break;\n      }\n      if ((hold & 0x0f)/*BITS(4)*/ !== Z_DEFLATED) {\n        strm.msg = 'unknown compression method';\n        state.mode = BAD;\n        break;\n      }\n      //--- DROPBITS(4) ---//\n      hold >>>= 4;\n      bits -= 4;\n      //---//\n      len = (hold & 0x0f)/*BITS(4)*/ + 8;\n      if (state.wbits === 0) {\n        state.wbits = len;\n      }\n      else if (len > state.wbits) {\n        strm.msg = 'invalid window size';\n        state.mode = BAD;\n        break;\n      }\n      state.dmax = 1 << len;\n      //Tracev((stderr, \"inflate:   zlib header ok\\n\"));\n      strm.adler = state.check = 1/*adler32(0L, Z_NULL, 0)*/;\n      state.mode = hold & 0x200 ? DICTID : TYPE;\n      //=== INITBITS();\n      hold = 0;\n      bits = 0;\n      //===//\n      break;\n    case FLAGS:\n      //=== NEEDBITS(16); */\n      while (bits < 16) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      state.flags = hold;\n      if ((state.flags & 0xff) !== Z_DEFLATED) {\n        strm.msg = 'unknown compression method';\n        state.mode = BAD;\n        break;\n      }\n      if (state.flags & 0xe000) {\n        strm.msg = 'unknown header flags set';\n        state.mode = BAD;\n        break;\n      }\n      if (state.head) {\n        state.head.text = ((hold >> 8) & 1);\n      }\n      if (state.flags & 0x0200) {\n        //=== CRC2(state.check, hold);\n        hbuf[0] = hold & 0xff;\n        hbuf[1] = (hold >>> 8) & 0xff;\n        state.check = crc32(state.check, hbuf, 2, 0);\n        //===//\n      }\n      //=== INITBITS();\n      hold = 0;\n      bits = 0;\n      //===//\n      state.mode = TIME;\n      /* falls through */\n    case TIME:\n      //=== NEEDBITS(32); */\n      while (bits < 32) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      if (state.head) {\n        state.head.time = hold;\n      }\n      if (state.flags & 0x0200) {\n        //=== CRC4(state.check, hold)\n        hbuf[0] = hold & 0xff;\n        hbuf[1] = (hold >>> 8) & 0xff;\n        hbuf[2] = (hold >>> 16) & 0xff;\n        hbuf[3] = (hold >>> 24) & 0xff;\n        state.check = crc32(state.check, hbuf, 4, 0);\n        //===\n      }\n      //=== INITBITS();\n      hold = 0;\n      bits = 0;\n      //===//\n      state.mode = OS;\n      /* falls through */\n    case OS:\n      //=== NEEDBITS(16); */\n      while (bits < 16) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      if (state.head) {\n        state.head.xflags = (hold & 0xff);\n        state.head.os = (hold >> 8);\n      }\n      if (state.flags & 0x0200) {\n        //=== CRC2(state.check, hold);\n        hbuf[0] = hold & 0xff;\n        hbuf[1] = (hold >>> 8) & 0xff;\n        state.check = crc32(state.check, hbuf, 2, 0);\n        //===//\n      }\n      //=== INITBITS();\n      hold = 0;\n      bits = 0;\n      //===//\n      state.mode = EXLEN;\n      /* falls through */\n    case EXLEN:\n      if (state.flags & 0x0400) {\n        //=== NEEDBITS(16); */\n        while (bits < 16) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.length = hold;\n        if (state.head) {\n          state.head.extra_len = hold;\n        }\n        if (state.flags & 0x0200) {\n          //=== CRC2(state.check, hold);\n          hbuf[0] = hold & 0xff;\n          hbuf[1] = (hold >>> 8) & 0xff;\n          state.check = crc32(state.check, hbuf, 2, 0);\n          //===//\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n      }\n      else if (state.head) {\n        state.head.extra = null/*Z_NULL*/;\n      }\n      state.mode = EXTRA;\n      /* falls through */\n    case EXTRA:\n      if (state.flags & 0x0400) {\n        copy = state.length;\n        if (copy > have) { copy = have; }\n        if (copy) {\n          if (state.head) {\n            len = state.head.extra_len - state.length;\n            if (!state.head.extra) {\n              // Use untyped array for more conveniend processing later\n              state.head.extra = new Array(state.head.extra_len);\n            }\n            utils.arraySet(\n              state.head.extra,\n              input,\n              next,\n              // extra field is limited to 65536 bytes\n              // - no need for additional size check\n              copy,\n              /*len + copy > state.head.extra_max - len ? state.head.extra_max : copy,*/\n              len\n            );\n            //zmemcpy(state.head.extra + len, next,\n            //        len + copy > state.head.extra_max ?\n            //        state.head.extra_max - len : copy);\n          }\n          if (state.flags & 0x0200) {\n            state.check = crc32(state.check, input, copy, next);\n          }\n          have -= copy;\n          next += copy;\n          state.length -= copy;\n        }\n        if (state.length) { break inf_leave; }\n      }\n      state.length = 0;\n      state.mode = NAME;\n      /* falls through */\n    case NAME:\n      if (state.flags & 0x0800) {\n        if (have === 0) { break inf_leave; }\n        copy = 0;\n        do {\n          // TODO: 2 or 1 bytes?\n          len = input[next + copy++];\n          /* use constant limit because in js we should not preallocate memory */\n          if (state.head && len &&\n              (state.length < 65536 /*state.head.name_max*/)) {\n            state.head.name += String.fromCharCode(len);\n          }\n        } while (len && copy < have);\n\n        if (state.flags & 0x0200) {\n          state.check = crc32(state.check, input, copy, next);\n        }\n        have -= copy;\n        next += copy;\n        if (len) { break inf_leave; }\n      }\n      else if (state.head) {\n        state.head.name = null;\n      }\n      state.length = 0;\n      state.mode = COMMENT;\n      /* falls through */\n    case COMMENT:\n      if (state.flags & 0x1000) {\n        if (have === 0) { break inf_leave; }\n        copy = 0;\n        do {\n          len = input[next + copy++];\n          /* use constant limit because in js we should not preallocate memory */\n          if (state.head && len &&\n              (state.length < 65536 /*state.head.comm_max*/)) {\n            state.head.comment += String.fromCharCode(len);\n          }\n        } while (len && copy < have);\n        if (state.flags & 0x0200) {\n          state.check = crc32(state.check, input, copy, next);\n        }\n        have -= copy;\n        next += copy;\n        if (len) { break inf_leave; }\n      }\n      else if (state.head) {\n        state.head.comment = null;\n      }\n      state.mode = HCRC;\n      /* falls through */\n    case HCRC:\n      if (state.flags & 0x0200) {\n        //=== NEEDBITS(16); */\n        while (bits < 16) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        if (hold !== (state.check & 0xffff)) {\n          strm.msg = 'header crc mismatch';\n          state.mode = BAD;\n          break;\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n      }\n      if (state.head) {\n        state.head.hcrc = ((state.flags >> 9) & 1);\n        state.head.done = true;\n      }\n      strm.adler = state.check = 0 /*crc32(0L, Z_NULL, 0)*/;\n      state.mode = TYPE;\n      break;\n    case DICTID:\n      //=== NEEDBITS(32); */\n      while (bits < 32) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      strm.adler = state.check = ZSWAP32(hold);\n      //=== INITBITS();\n      hold = 0;\n      bits = 0;\n      //===//\n      state.mode = DICT;\n      /* falls through */\n    case DICT:\n      if (state.havedict === 0) {\n        //--- RESTORE() ---\n        strm.next_out = put;\n        strm.avail_out = left;\n        strm.next_in = next;\n        strm.avail_in = have;\n        state.hold = hold;\n        state.bits = bits;\n        //---\n        return Z_NEED_DICT;\n      }\n      strm.adler = state.check = 1/*adler32(0L, Z_NULL, 0)*/;\n      state.mode = TYPE;\n      /* falls through */\n    case TYPE:\n      if (flush === Z_BLOCK || flush === Z_TREES) { break inf_leave; }\n      /* falls through */\n    case TYPEDO:\n      if (state.last) {\n        //--- BYTEBITS() ---//\n        hold >>>= bits & 7;\n        bits -= bits & 7;\n        //---//\n        state.mode = CHECK;\n        break;\n      }\n      //=== NEEDBITS(3); */\n      while (bits < 3) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      state.last = (hold & 0x01)/*BITS(1)*/;\n      //--- DROPBITS(1) ---//\n      hold >>>= 1;\n      bits -= 1;\n      //---//\n\n      switch ((hold & 0x03)/*BITS(2)*/) {\n      case 0:                             /* stored block */\n        //Tracev((stderr, \"inflate:     stored block%s\\n\",\n        //        state.last ? \" (last)\" : \"\"));\n        state.mode = STORED;\n        break;\n      case 1:                             /* fixed block */\n        fixedtables(state);\n        //Tracev((stderr, \"inflate:     fixed codes block%s\\n\",\n        //        state.last ? \" (last)\" : \"\"));\n        state.mode = LEN_;             /* decode codes */\n        if (flush === Z_TREES) {\n          //--- DROPBITS(2) ---//\n          hold >>>= 2;\n          bits -= 2;\n          //---//\n          break inf_leave;\n        }\n        break;\n      case 2:                             /* dynamic block */\n        //Tracev((stderr, \"inflate:     dynamic codes block%s\\n\",\n        //        state.last ? \" (last)\" : \"\"));\n        state.mode = TABLE;\n        break;\n      case 3:\n        strm.msg = 'invalid block type';\n        state.mode = BAD;\n      }\n      //--- DROPBITS(2) ---//\n      hold >>>= 2;\n      bits -= 2;\n      //---//\n      break;\n    case STORED:\n      //--- BYTEBITS() ---// /* go to byte boundary */\n      hold >>>= bits & 7;\n      bits -= bits & 7;\n      //---//\n      //=== NEEDBITS(32); */\n      while (bits < 32) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      if ((hold & 0xffff) !== ((hold >>> 16) ^ 0xffff)) {\n        strm.msg = 'invalid stored block lengths';\n        state.mode = BAD;\n        break;\n      }\n      state.length = hold & 0xffff;\n      //Tracev((stderr, \"inflate:       stored length %u\\n\",\n      //        state.length));\n      //=== INITBITS();\n      hold = 0;\n      bits = 0;\n      //===//\n      state.mode = COPY_;\n      if (flush === Z_TREES) { break inf_leave; }\n      /* falls through */\n    case COPY_:\n      state.mode = COPY;\n      /* falls through */\n    case COPY:\n      copy = state.length;\n      if (copy) {\n        if (copy > have) { copy = have; }\n        if (copy > left) { copy = left; }\n        if (copy === 0) { break inf_leave; }\n        //--- zmemcpy(put, next, copy); ---\n        utils.arraySet(output, input, next, copy, put);\n        //---//\n        have -= copy;\n        next += copy;\n        left -= copy;\n        put += copy;\n        state.length -= copy;\n        break;\n      }\n      //Tracev((stderr, \"inflate:       stored end\\n\"));\n      state.mode = TYPE;\n      break;\n    case TABLE:\n      //=== NEEDBITS(14); */\n      while (bits < 14) {\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n      }\n      //===//\n      state.nlen = (hold & 0x1f)/*BITS(5)*/ + 257;\n      //--- DROPBITS(5) ---//\n      hold >>>= 5;\n      bits -= 5;\n      //---//\n      state.ndist = (hold & 0x1f)/*BITS(5)*/ + 1;\n      //--- DROPBITS(5) ---//\n      hold >>>= 5;\n      bits -= 5;\n      //---//\n      state.ncode = (hold & 0x0f)/*BITS(4)*/ + 4;\n      //--- DROPBITS(4) ---//\n      hold >>>= 4;\n      bits -= 4;\n      //---//\n//#ifndef PKZIP_BUG_WORKAROUND\n      if (state.nlen > 286 || state.ndist > 30) {\n        strm.msg = 'too many length or distance symbols';\n        state.mode = BAD;\n        break;\n      }\n//#endif\n      //Tracev((stderr, \"inflate:       table sizes ok\\n\"));\n      state.have = 0;\n      state.mode = LENLENS;\n      /* falls through */\n    case LENLENS:\n      while (state.have < state.ncode) {\n        //=== NEEDBITS(3);\n        while (bits < 3) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.lens[order[state.have++]] = (hold & 0x07);//BITS(3);\n        //--- DROPBITS(3) ---//\n        hold >>>= 3;\n        bits -= 3;\n        //---//\n      }\n      while (state.have < 19) {\n        state.lens[order[state.have++]] = 0;\n      }\n      // We have separate tables & no pointers. 2 commented lines below not needed.\n      //state.next = state.codes;\n      //state.lencode = state.next;\n      // Switch to use dynamic table\n      state.lencode = state.lendyn;\n      state.lenbits = 7;\n\n      opts = {bits: state.lenbits};\n      ret = inflate_table(CODES, state.lens, 0, 19, state.lencode, 0, state.work, opts);\n      state.lenbits = opts.bits;\n\n      if (ret) {\n        strm.msg = 'invalid code lengths set';\n        state.mode = BAD;\n        break;\n      }\n      //Tracev((stderr, \"inflate:       code lengths ok\\n\"));\n      state.have = 0;\n      state.mode = CODELENS;\n      /* falls through */\n    case CODELENS:\n      while (state.have < state.nlen + state.ndist) {\n        for (;;) {\n          here = state.lencode[hold & ((1 << state.lenbits) - 1)];/*BITS(state.lenbits)*/\n          here_bits = here >>> 24;\n          here_op = (here >>> 16) & 0xff;\n          here_val = here & 0xffff;\n\n          if ((here_bits) <= bits) { break; }\n          //--- PULLBYTE() ---//\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n          //---//\n        }\n        if (here_val < 16) {\n          //--- DROPBITS(here.bits) ---//\n          hold >>>= here_bits;\n          bits -= here_bits;\n          //---//\n          state.lens[state.have++] = here_val;\n        }\n        else {\n          if (here_val === 16) {\n            //=== NEEDBITS(here.bits + 2);\n            n = here_bits + 2;\n            while (bits < n) {\n              if (have === 0) { break inf_leave; }\n              have--;\n              hold += input[next++] << bits;\n              bits += 8;\n            }\n            //===//\n            //--- DROPBITS(here.bits) ---//\n            hold >>>= here_bits;\n            bits -= here_bits;\n            //---//\n            if (state.have === 0) {\n              strm.msg = 'invalid bit length repeat';\n              state.mode = BAD;\n              break;\n            }\n            len = state.lens[state.have - 1];\n            copy = 3 + (hold & 0x03);//BITS(2);\n            //--- DROPBITS(2) ---//\n            hold >>>= 2;\n            bits -= 2;\n            //---//\n          }\n          else if (here_val === 17) {\n            //=== NEEDBITS(here.bits + 3);\n            n = here_bits + 3;\n            while (bits < n) {\n              if (have === 0) { break inf_leave; }\n              have--;\n              hold += input[next++] << bits;\n              bits += 8;\n            }\n            //===//\n            //--- DROPBITS(here.bits) ---//\n            hold >>>= here_bits;\n            bits -= here_bits;\n            //---//\n            len = 0;\n            copy = 3 + (hold & 0x07);//BITS(3);\n            //--- DROPBITS(3) ---//\n            hold >>>= 3;\n            bits -= 3;\n            //---//\n          }\n          else {\n            //=== NEEDBITS(here.bits + 7);\n            n = here_bits + 7;\n            while (bits < n) {\n              if (have === 0) { break inf_leave; }\n              have--;\n              hold += input[next++] << bits;\n              bits += 8;\n            }\n            //===//\n            //--- DROPBITS(here.bits) ---//\n            hold >>>= here_bits;\n            bits -= here_bits;\n            //---//\n            len = 0;\n            copy = 11 + (hold & 0x7f);//BITS(7);\n            //--- DROPBITS(7) ---//\n            hold >>>= 7;\n            bits -= 7;\n            //---//\n          }\n          if (state.have + copy > state.nlen + state.ndist) {\n            strm.msg = 'invalid bit length repeat';\n            state.mode = BAD;\n            break;\n          }\n          while (copy--) {\n            state.lens[state.have++] = len;\n          }\n        }\n      }\n\n      /* handle error breaks in while */\n      if (state.mode === BAD) { break; }\n\n      /* check for end-of-block code (better have one) */\n      if (state.lens[256] === 0) {\n        strm.msg = 'invalid code -- missing end-of-block';\n        state.mode = BAD;\n        break;\n      }\n\n      /* build code tables -- note: do not change the lenbits or distbits\n         values here (9 and 6) without reading the comments in inftrees.h\n         concerning the ENOUGH constants, which depend on those values */\n      state.lenbits = 9;\n\n      opts = {bits: state.lenbits};\n      ret = inflate_table(LENS, state.lens, 0, state.nlen, state.lencode, 0, state.work, opts);\n      // We have separate tables & no pointers. 2 commented lines below not needed.\n      // state.next_index = opts.table_index;\n      state.lenbits = opts.bits;\n      // state.lencode = state.next;\n\n      if (ret) {\n        strm.msg = 'invalid literal/lengths set';\n        state.mode = BAD;\n        break;\n      }\n\n      state.distbits = 6;\n      //state.distcode.copy(state.codes);\n      // Switch to use dynamic table\n      state.distcode = state.distdyn;\n      opts = {bits: state.distbits};\n      ret = inflate_table(DISTS, state.lens, state.nlen, state.ndist, state.distcode, 0, state.work, opts);\n      // We have separate tables & no pointers. 2 commented lines below not needed.\n      // state.next_index = opts.table_index;\n      state.distbits = opts.bits;\n      // state.distcode = state.next;\n\n      if (ret) {\n        strm.msg = 'invalid distances set';\n        state.mode = BAD;\n        break;\n      }\n      //Tracev((stderr, 'inflate:       codes ok\\n'));\n      state.mode = LEN_;\n      if (flush === Z_TREES) { break inf_leave; }\n      /* falls through */\n    case LEN_:\n      state.mode = LEN;\n      /* falls through */\n    case LEN:\n      if (have >= 6 && left >= 258) {\n        //--- RESTORE() ---\n        strm.next_out = put;\n        strm.avail_out = left;\n        strm.next_in = next;\n        strm.avail_in = have;\n        state.hold = hold;\n        state.bits = bits;\n        //---\n        inflate_fast(strm, _out);\n        //--- LOAD() ---\n        put = strm.next_out;\n        output = strm.output;\n        left = strm.avail_out;\n        next = strm.next_in;\n        input = strm.input;\n        have = strm.avail_in;\n        hold = state.hold;\n        bits = state.bits;\n        //---\n\n        if (state.mode === TYPE) {\n          state.back = -1;\n        }\n        break;\n      }\n      state.back = 0;\n      for (;;) {\n        here = state.lencode[hold & ((1 << state.lenbits) -1)];  /*BITS(state.lenbits)*/\n        here_bits = here >>> 24;\n        here_op = (here >>> 16) & 0xff;\n        here_val = here & 0xffff;\n\n        if (here_bits <= bits) { break; }\n        //--- PULLBYTE() ---//\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n        //---//\n      }\n      if (here_op && (here_op & 0xf0) === 0) {\n        last_bits = here_bits;\n        last_op = here_op;\n        last_val = here_val;\n        for (;;) {\n          here = state.lencode[last_val +\n                  ((hold & ((1 << (last_bits + last_op)) -1))/*BITS(last.bits + last.op)*/ >> last_bits)];\n          here_bits = here >>> 24;\n          here_op = (here >>> 16) & 0xff;\n          here_val = here & 0xffff;\n\n          if ((last_bits + here_bits) <= bits) { break; }\n          //--- PULLBYTE() ---//\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n          //---//\n        }\n        //--- DROPBITS(last.bits) ---//\n        hold >>>= last_bits;\n        bits -= last_bits;\n        //---//\n        state.back += last_bits;\n      }\n      //--- DROPBITS(here.bits) ---//\n      hold >>>= here_bits;\n      bits -= here_bits;\n      //---//\n      state.back += here_bits;\n      state.length = here_val;\n      if (here_op === 0) {\n        //Tracevv((stderr, here.val >= 0x20 && here.val < 0x7f ?\n        //        \"inflate:         literal '%c'\\n\" :\n        //        \"inflate:         literal 0x%02x\\n\", here.val));\n        state.mode = LIT;\n        break;\n      }\n      if (here_op & 32) {\n        //Tracevv((stderr, \"inflate:         end of block\\n\"));\n        state.back = -1;\n        state.mode = TYPE;\n        break;\n      }\n      if (here_op & 64) {\n        strm.msg = 'invalid literal/length code';\n        state.mode = BAD;\n        break;\n      }\n      state.extra = here_op & 15;\n      state.mode = LENEXT;\n      /* falls through */\n    case LENEXT:\n      if (state.extra) {\n        //=== NEEDBITS(state.extra);\n        n = state.extra;\n        while (bits < n) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.length += hold & ((1 << state.extra) -1)/*BITS(state.extra)*/;\n        //--- DROPBITS(state.extra) ---//\n        hold >>>= state.extra;\n        bits -= state.extra;\n        //---//\n        state.back += state.extra;\n      }\n      //Tracevv((stderr, \"inflate:         length %u\\n\", state.length));\n      state.was = state.length;\n      state.mode = DIST;\n      /* falls through */\n    case DIST:\n      for (;;) {\n        here = state.distcode[hold & ((1 << state.distbits) -1)];/*BITS(state.distbits)*/\n        here_bits = here >>> 24;\n        here_op = (here >>> 16) & 0xff;\n        here_val = here & 0xffff;\n\n        if ((here_bits) <= bits) { break; }\n        //--- PULLBYTE() ---//\n        if (have === 0) { break inf_leave; }\n        have--;\n        hold += input[next++] << bits;\n        bits += 8;\n        //---//\n      }\n      if ((here_op & 0xf0) === 0) {\n        last_bits = here_bits;\n        last_op = here_op;\n        last_val = here_val;\n        for (;;) {\n          here = state.distcode[last_val +\n                  ((hold & ((1 << (last_bits + last_op)) -1))/*BITS(last.bits + last.op)*/ >> last_bits)];\n          here_bits = here >>> 24;\n          here_op = (here >>> 16) & 0xff;\n          here_val = here & 0xffff;\n\n          if ((last_bits + here_bits) <= bits) { break; }\n          //--- PULLBYTE() ---//\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n          //---//\n        }\n        //--- DROPBITS(last.bits) ---//\n        hold >>>= last_bits;\n        bits -= last_bits;\n        //---//\n        state.back += last_bits;\n      }\n      //--- DROPBITS(here.bits) ---//\n      hold >>>= here_bits;\n      bits -= here_bits;\n      //---//\n      state.back += here_bits;\n      if (here_op & 64) {\n        strm.msg = 'invalid distance code';\n        state.mode = BAD;\n        break;\n      }\n      state.offset = here_val;\n      state.extra = (here_op) & 15;\n      state.mode = DISTEXT;\n      /* falls through */\n    case DISTEXT:\n      if (state.extra) {\n        //=== NEEDBITS(state.extra);\n        n = state.extra;\n        while (bits < n) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.offset += hold & ((1 << state.extra) -1)/*BITS(state.extra)*/;\n        //--- DROPBITS(state.extra) ---//\n        hold >>>= state.extra;\n        bits -= state.extra;\n        //---//\n        state.back += state.extra;\n      }\n//#ifdef INFLATE_STRICT\n      if (state.offset > state.dmax) {\n        strm.msg = 'invalid distance too far back';\n        state.mode = BAD;\n        break;\n      }\n//#endif\n      //Tracevv((stderr, \"inflate:         distance %u\\n\", state.offset));\n      state.mode = MATCH;\n      /* falls through */\n    case MATCH:\n      if (left === 0) { break inf_leave; }\n      copy = _out - left;\n      if (state.offset > copy) {         /* copy from window */\n        copy = state.offset - copy;\n        if (copy > state.whave) {\n          if (state.sane) {\n            strm.msg = 'invalid distance too far back';\n            state.mode = BAD;\n            break;\n          }\n// (!) This block is disabled in zlib defailts,\n// don't enable it for binary compatibility\n//#ifdef INFLATE_ALLOW_INVALID_DISTANCE_TOOFAR_ARRR\n//          Trace((stderr, \"inflate.c too far\\n\"));\n//          copy -= state.whave;\n//          if (copy > state.length) { copy = state.length; }\n//          if (copy > left) { copy = left; }\n//          left -= copy;\n//          state.length -= copy;\n//          do {\n//            output[put++] = 0;\n//          } while (--copy);\n//          if (state.length === 0) { state.mode = LEN; }\n//          break;\n//#endif\n        }\n        if (copy > state.wnext) {\n          copy -= state.wnext;\n          from = state.wsize - copy;\n        }\n        else {\n          from = state.wnext - copy;\n        }\n        if (copy > state.length) { copy = state.length; }\n        from_source = state.window;\n      }\n      else {                              /* copy from output */\n        from_source = output;\n        from = put - state.offset;\n        copy = state.length;\n      }\n      if (copy > left) { copy = left; }\n      left -= copy;\n      state.length -= copy;\n      do {\n        output[put++] = from_source[from++];\n      } while (--copy);\n      if (state.length === 0) { state.mode = LEN; }\n      break;\n    case LIT:\n      if (left === 0) { break inf_leave; }\n      output[put++] = state.length;\n      left--;\n      state.mode = LEN;\n      break;\n    case CHECK:\n      if (state.wrap) {\n        //=== NEEDBITS(32);\n        while (bits < 32) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          // Use '|' insdead of '+' to make sure that result is signed\n          hold |= input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        _out -= left;\n        strm.total_out += _out;\n        state.total += _out;\n        if (_out) {\n          strm.adler = state.check =\n              /*UPDATE(state.check, put - _out, _out);*/\n              (state.flags ? crc32(state.check, output, _out, put - _out) : adler32(state.check, output, _out, put - _out));\n\n        }\n        _out = left;\n        // NB: crc32 stored as signed 32-bit int, ZSWAP32 returns signed too\n        if ((state.flags ? hold : ZSWAP32(hold)) !== state.check) {\n          strm.msg = 'incorrect data check';\n          state.mode = BAD;\n          break;\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        //Tracev((stderr, \"inflate:   check matches trailer\\n\"));\n      }\n      state.mode = LENGTH;\n      /* falls through */\n    case LENGTH:\n      if (state.wrap && state.flags) {\n        //=== NEEDBITS(32);\n        while (bits < 32) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        if (hold !== (state.total & 0xffffffff)) {\n          strm.msg = 'incorrect length check';\n          state.mode = BAD;\n          break;\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        //Tracev((stderr, \"inflate:   length matches trailer\\n\"));\n      }\n      state.mode = DONE;\n      /* falls through */\n    case DONE:\n      ret = Z_STREAM_END;\n      break inf_leave;\n    case BAD:\n      ret = Z_DATA_ERROR;\n      break inf_leave;\n    case MEM:\n      return Z_MEM_ERROR;\n    case SYNC:\n      /* falls through */\n    default:\n      return Z_STREAM_ERROR;\n    }\n  }\n\n  // inf_leave <- here is real place for \"goto inf_leave\", emulated via \"break inf_leave\"\n\n  /*\n     Return from inflate(), updating the total counts and the check value.\n     If there was no progress during the inflate() call, return a buffer\n     error.  Call updatewindow() to create and/or update the window state.\n     Note: a memory error from inflate() is non-recoverable.\n   */\n\n  //--- RESTORE() ---\n  strm.next_out = put;\n  strm.avail_out = left;\n  strm.next_in = next;\n  strm.avail_in = have;\n  state.hold = hold;\n  state.bits = bits;\n  //---\n\n  if (state.wsize || (_out !== strm.avail_out && state.mode < BAD &&\n                      (state.mode < CHECK || flush !== Z_FINISH))) {\n    if (updatewindow(strm, strm.output, strm.next_out, _out - strm.avail_out)) {\n      state.mode = MEM;\n      return Z_MEM_ERROR;\n    }\n  }\n  _in -= strm.avail_in;\n  _out -= strm.avail_out;\n  strm.total_in += _in;\n  strm.total_out += _out;\n  state.total += _out;\n  if (state.wrap && _out) {\n    strm.adler = state.check = /*UPDATE(state.check, strm.next_out - _out, _out);*/\n      (state.flags ? crc32(state.check, output, _out, strm.next_out - _out) : adler32(state.check, output, _out, strm.next_out - _out));\n  }\n  strm.data_type = state.bits + (state.last ? 64 : 0) +\n                    (state.mode === TYPE ? 128 : 0) +\n                    (state.mode === LEN_ || state.mode === COPY_ ? 256 : 0);\n  if (((_in === 0 && _out === 0) || flush === Z_FINISH) && ret === Z_OK) {\n    ret = Z_BUF_ERROR;\n  }\n  return ret;\n}\n\nfunction inflateEnd(strm) {\n\n  if (!strm || !strm.state /*|| strm->zfree == (free_func)0*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  var state = strm.state;\n  if (state.window) {\n    state.window = null;\n  }\n  strm.state = null;\n  return Z_OK;\n}\n\nfunction inflateGetHeader(strm, head) {\n  var state;\n\n  /* check state */\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n  if ((state.wrap & 2) === 0) { return Z_STREAM_ERROR; }\n\n  /* save header structure */\n  state.head = head;\n  head.done = false;\n  return Z_OK;\n}\n\n\nexports.inflateReset = inflateReset;\nexports.inflateReset2 = inflateReset2;\nexports.inflateResetKeep = inflateResetKeep;\nexports.inflateInit = inflateInit;\nexports.inflateInit2 = inflateInit2;\nexports.inflate = inflate;\nexports.inflateEnd = inflateEnd;\nexports.inflateGetHeader = inflateGetHeader;\nexports.inflateInfo = 'pako inflate (from Nodeca project)';\n\n/* Not implemented\nexports.inflateCopy = inflateCopy;\nexports.inflateGetDictionary = inflateGetDictionary;\nexports.inflateMark = inflateMark;\nexports.inflatePrime = inflatePrime;\nexports.inflateSetDictionary = inflateSetDictionary;\nexports.inflateSync = inflateSync;\nexports.inflateSyncPoint = inflateSyncPoint;\nexports.inflateUndermine = inflateUndermine;\n*/\n", "'use strict';\n\n\nfunction GZheader() {\n  /* true if compressed data believed to be text */\n  this.text       = 0;\n  /* modification time */\n  this.time       = 0;\n  /* extra flags (not used when writing a gzip file) */\n  this.xflags     = 0;\n  /* operating system */\n  this.os         = 0;\n  /* pointer to extra field or Z_NULL if none */\n  this.extra      = null;\n  /* extra field length (valid if extra != Z_NULL) */\n  this.extra_len  = 0; // Actually, we don't need it in JS,\n                       // but leave for few code modifications\n\n  //\n  // Setup limits is not necessary because in js we should not preallocate memory\n  // for inflate use constant limit in 65536 bytes\n  //\n\n  /* space at extra (only when reading header) */\n  // this.extra_max  = 0;\n  /* pointer to zero-terminated file name or Z_NULL */\n  this.name       = '';\n  /* space at name (only when reading header) */\n  // this.name_max   = 0;\n  /* pointer to zero-terminated comment or Z_NULL */\n  this.comment    = '';\n  /* space at comment (only when reading header) */\n  // this.comm_max   = 0;\n  /* true if there was or will be a header crc */\n  this.hcrc       = 0;\n  /* true when done reading gzip header (not used when writing a gzip file) */\n  this.done       = false;\n}\n\nmodule.exports = GZheader;\n", "// Top level file is just a mixin of submodules & constants\n'use strict';\n\nvar assign    = require('./lib/utils/common').assign;\n\nvar deflate   = require('./lib/deflate');\nvar inflate   = require('./lib/inflate');\nvar constants = require('./lib/zlib/constants');\n\nvar pako = {};\n\nassign(pako, deflate, inflate, constants);\n\nmodule.exports = pako;\n", "module.exports = {\n\n  /* Allowed flush values; see deflate() and inflate() below for details */\n  Z_NO_FLUSH:         0,\n  Z_PARTIAL_FLUSH:    1,\n  Z_SYNC_FLUSH:       2,\n  Z_FULL_FLUSH:       3,\n  Z_FINISH:           4,\n  Z_BLOCK:            5,\n  Z_TREES:            6,\n\n  /* Return codes for the compression/decompression functions. Negative values\n  * are errors, positive values are used for special but normal events.\n  */\n  Z_OK:               0,\n  Z_STREAM_END:       1,\n  Z_NEED_DICT:        2,\n  Z_ERRNO:           -1,\n  Z_STREAM_ERROR:    -2,\n  Z_DATA_ERROR:      -3,\n  //Z_MEM_ERROR:     -4,\n  Z_BUF_ERROR:       -5,\n  //Z_VERSION_ERROR: -6,\n\n  /* compression levels */\n  Z_NO_COMPRESSION:         0,\n  Z_BEST_SPEED:             1,\n  Z_BEST_COMPRESSION:       9,\n  Z_DEFAULT_COMPRESSION:   -1,\n\n\n  Z_FILTERED:               1,\n  Z_HUFFMAN_ONLY:           2,\n  <PERSON>_<PERSON><PERSON>:                    3,\n  Z_FIXED:                  4,\n  Z_DEFAULT_STRATEGY:       0,\n\n  /* Possible values of the data_type field (though see inflate()) */\n  Z_BINARY:                 0,\n  Z_TEXT:                   1,\n  //Z_ASCII:                1, // = Z_TEXT (deprecated)\n  Z_UNKNOWN:                2,\n\n  /* The deflate compression method */\n  Z_DEFLATED:               8\n  //Z_NULL:                 null // Use -1 or null inline, depending on var type\n};\n", "// String encode/decode helpers\n'use strict';\n\n\nvar utils = require('./common');\n\n\n// Quick check if we can use fast array to bin string conversion\n//\n// - apply(Array) can fail on Android 2.2\n// - apply(Uint8Array) can fail on iOS 5.1 Safary\n//\nvar STR_APPLY_OK = true;\nvar STR_APPLY_UIA_OK = true;\n\ntry { String.fromCharCode.apply(null, [0]); } catch(__) { STR_APPLY_OK = false; }\ntry { String.fromCharCode.apply(null, new Uint8Array(1)); } catch(__) { STR_APPLY_UIA_OK = false; }\n\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new utils.Buf8(256);\nfor (var q=0; q<256; q++) {\n  _utf8len[q] = (q >= 252 ? 6 : q >= 248 ? 5 : q >= 240 ? 4 : q >= 224 ? 3 : q >= 192 ? 2 : 1);\n}\n_utf8len[254]=_utf8len[254]=1; // Invalid sequence start\n\n\n// convert string to array (typed, when possible)\nexports.string2buf = function (str) {\n  var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n  // count binary size\n  for (m_pos = 0; m_pos < str_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n      c2 = str.charCodeAt(m_pos+1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  buf = new utils.Buf8(buf_len);\n\n  // convert\n  for (i=0, m_pos = 0; i < buf_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n      c2 = str.charCodeAt(m_pos+1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xC0 | (c >>> 6);\n      buf[i++] = 0x80 | (c & 0x3f);\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xE0 | (c >>> 12);\n      buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n      buf[i++] = 0x80 | (c & 0x3f);\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | (c >>> 18);\n      buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n      buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n      buf[i++] = 0x80 | (c & 0x3f);\n    }\n  }\n\n  return buf;\n};\n\n// Helper (used in 2 places)\nfunction buf2binstring(buf, len) {\n  // use fallback for big arrays to avoid stack overflow\n  if (len < 65537) {\n    if ((buf.subarray && STR_APPLY_UIA_OK) || (!buf.subarray && STR_APPLY_OK)) {\n      return String.fromCharCode.apply(null, utils.shrinkBuf(buf, len));\n    }\n  }\n\n  var result = '';\n  for (var i=0; i < len; i++) {\n    result += String.fromCharCode(buf[i]);\n  }\n  return result;\n}\n\n\n// Convert byte array to binary string\nexports.buf2binstring = function(buf) {\n  return buf2binstring(buf, buf.length);\n};\n\n\n// Convert binary string (typed, when possible)\nexports.binstring2buf = function(str) {\n  var buf = new utils.Buf8(str.length);\n  for (var i=0, len=buf.length; i < len; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n};\n\n\n// convert array to string\nexports.buf2string = function (buf, max) {\n  var i, out, c, c_len;\n  var len = max || buf.length;\n\n  // Reserve max possible length (2 words per char)\n  // NB: by unknown reasons, Array is significantly faster for\n  //     String.fromCharCode.apply than Uint16Array.\n  var utf16buf = new Array(len*2);\n\n  for (out=0, i=0; i<len;) {\n    c = buf[i++];\n    // quick process ascii\n    if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n    c_len = _utf8len[c];\n    // skip 5 & 6 byte codes\n    if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len-1; continue; }\n\n    // apply mask on first byte\n    c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n    // join the rest\n    while (c_len > 1 && i < len) {\n      c = (c << 6) | (buf[i++] & 0x3f);\n      c_len--;\n    }\n\n    // terminated by end of string?\n    if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n    if (c < 0x10000) {\n      utf16buf[out++] = c;\n    } else {\n      c -= 0x10000;\n      utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n      utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n    }\n  }\n\n  return buf2binstring(utf16buf, out);\n};\n\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nexports.utf8border = function(buf, max) {\n  var pos;\n\n  max = max || buf.length;\n  if (max > buf.length) { max = buf.length; }\n\n  // go back from last position, until start of sequence found\n  pos = max-1;\n  while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n  // Fuckup - very small and broken sequence,\n  // return max, because we should return something anyway.\n  if (pos < 0) { return max; }\n\n  // If we came to start of buffer - that means vuffer is too small,\n  // return max too.\n  if (pos === 0) { return max; }\n\n  return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n", "'use strict';\n\n\nvar utils = require('../utils/common');\n\nvar MAXBITS = 15;\nvar ENOUGH_LENS = 852;\nvar ENOUGH_DISTS = 592;\n//var ENOUGH = (ENOUGH_LENS+ENOUGH_DISTS);\n\nvar CODES = 0;\nvar LENS = 1;\nvar DISTS = 2;\n\nvar lbase = [ /* Length codes 257..285 base */\n  3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31,\n  35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0\n];\n\nvar lext = [ /* Length codes 257..285 extra */\n  16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18,\n  19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78\n];\n\nvar dbase = [ /* Distance codes 0..29 base */\n  1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193,\n  257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145,\n  8193, 12289, 16385, 24577, 0, 0\n];\n\nvar dext = [ /* Distance codes 0..29 extra */\n  16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22,\n  23, 23, 24, 24, 25, 25, 26, 26, 27, 27,\n  28, 28, 29, 29, 64, 64\n];\n\nmodule.exports = function inflate_table(type, lens, lens_index, codes, table, table_index, work, opts)\n{\n  var bits = opts.bits;\n      //here = opts.here; /* table entry for duplication */\n\n  var len = 0;               /* a code's length in bits */\n  var sym = 0;               /* index of code symbols */\n  var min = 0, max = 0;          /* minimum and maximum code lengths */\n  var root = 0;              /* number of index bits for root table */\n  var curr = 0;              /* number of index bits for current table */\n  var drop = 0;              /* code bits to drop for sub-table */\n  var left = 0;                   /* number of prefix codes available */\n  var used = 0;              /* code entries in table used */\n  var huff = 0;              /* Huffman code */\n  var incr;              /* for incrementing code, index */\n  var fill;              /* index for replicating entries */\n  var low;               /* low bits for current root entry */\n  var mask;              /* mask for low root bits */\n  var next;             /* next available space in table */\n  var base = null;     /* base value table to use */\n  var base_index = 0;\n//  var shoextra;    /* extra bits table to use */\n  var end;                    /* use base and extra for symbol > end */\n  var count = new utils.Buf16(MAXBITS+1); //[MAXBITS+1];    /* number of codes of each length */\n  var offs = new utils.Buf16(MAXBITS+1); //[MAXBITS+1];     /* offsets in table for each length */\n  var extra = null;\n  var extra_index = 0;\n\n  var here_bits, here_op, here_val;\n\n  /*\n   Process a set of code lengths to create a canonical Huffman code.  The\n   code lengths are lens[0..codes-1].  Each length corresponds to the\n   symbols 0..codes-1.  The Huffman code is generated by first sorting the\n   symbols by length from short to long, and retaining the symbol order\n   for codes with equal lengths.  Then the code starts with all zero bits\n   for the first code of the shortest length, and the codes are integer\n   increments for the same length, and zeros are appended as the length\n   increases.  For the deflate format, these bits are stored backwards\n   from their more natural integer increment ordering, and so when the\n   decoding tables are built in the large loop below, the integer codes\n   are incremented backwards.\n\n   This routine assumes, but does not check, that all of the entries in\n   lens[] are in the range 0..MAXBITS.  The caller must assure this.\n   1..MAXBITS is interpreted as that code length.  zero means that that\n   symbol does not occur in this code.\n\n   The codes are sorted by computing a count of codes for each length,\n   creating from that a table of starting indices for each length in the\n   sorted table, and then entering the symbols in order in the sorted\n   table.  The sorted table is work[], with that space being provided by\n   the caller.\n\n   The length counts are used for other purposes as well, i.e. finding\n   the minimum and maximum length codes, determining if there are any\n   codes at all, checking for a valid set of lengths, and looking ahead\n   at length counts to determine sub-table sizes when building the\n   decoding tables.\n   */\n\n  /* accumulate lengths for codes (assumes lens[] all in 0..MAXBITS) */\n  for (len = 0; len <= MAXBITS; len++) {\n    count[len] = 0;\n  }\n  for (sym = 0; sym < codes; sym++) {\n    count[lens[lens_index + sym]]++;\n  }\n\n  /* bound code lengths, force root to be within code lengths */\n  root = bits;\n  for (max = MAXBITS; max >= 1; max--) {\n    if (count[max] !== 0) { break; }\n  }\n  if (root > max) {\n    root = max;\n  }\n  if (max === 0) {                     /* no symbols to code at all */\n    //table.op[opts.table_index] = 64;  //here.op = (var char)64;    /* invalid code marker */\n    //table.bits[opts.table_index] = 1;   //here.bits = (var char)1;\n    //table.val[opts.table_index++] = 0;   //here.val = (var short)0;\n    table[table_index++] = (1 << 24) | (64 << 16) | 0;\n\n\n    //table.op[opts.table_index] = 64;\n    //table.bits[opts.table_index] = 1;\n    //table.val[opts.table_index++] = 0;\n    table[table_index++] = (1 << 24) | (64 << 16) | 0;\n\n    opts.bits = 1;\n    return 0;     /* no symbols, but wait for decoding to report error */\n  }\n  for (min = 1; min < max; min++) {\n    if (count[min] !== 0) { break; }\n  }\n  if (root < min) {\n    root = min;\n  }\n\n  /* check for an over-subscribed or incomplete set of lengths */\n  left = 1;\n  for (len = 1; len <= MAXBITS; len++) {\n    left <<= 1;\n    left -= count[len];\n    if (left < 0) {\n      return -1;\n    }        /* over-subscribed */\n  }\n  if (left > 0 && (type === CODES || max !== 1)) {\n    return -1;                      /* incomplete set */\n  }\n\n  /* generate offsets into symbol table for each length for sorting */\n  offs[1] = 0;\n  for (len = 1; len < MAXBITS; len++) {\n    offs[len + 1] = offs[len] + count[len];\n  }\n\n  /* sort symbols by length, by symbol order within each length */\n  for (sym = 0; sym < codes; sym++) {\n    if (lens[lens_index + sym] !== 0) {\n      work[offs[lens[lens_index + sym]]++] = sym;\n    }\n  }\n\n  /*\n   Create and fill in decoding tables.  In this loop, the table being\n   filled is at next and has curr index bits.  The code being used is huff\n   with length len.  That code is converted to an index by dropping drop\n   bits off of the bottom.  For codes where len is less than drop + curr,\n   those top drop + curr - len bits are incremented through all values to\n   fill the table with replicated entries.\n\n   root is the number of index bits for the root table.  When len exceeds\n   root, sub-tables are created pointed to by the root entry with an index\n   of the low root bits of huff.  This is saved in low to check for when a\n   new sub-table should be started.  drop is zero when the root table is\n   being filled, and drop is root when sub-tables are being filled.\n\n   When a new sub-table is needed, it is necessary to look ahead in the\n   code lengths to determine what size sub-table is needed.  The length\n   counts are used for this, and so count[] is decremented as codes are\n   entered in the tables.\n\n   used keeps track of how many table entries have been allocated from the\n   provided *table space.  It is checked for LENS and DIST tables against\n   the constants ENOUGH_LENS and ENOUGH_DISTS to guard against changes in\n   the initial root table size constants.  See the comments in inftrees.h\n   for more information.\n\n   sym increments through all symbols, and the loop terminates when\n   all codes of length max, i.e. all codes, have been processed.  This\n   routine permits incomplete codes, so another loop after this one fills\n   in the rest of the decoding tables with invalid code markers.\n   */\n\n  /* set up for code type */\n  // poor man optimization - use if-else instead of switch,\n  // to avoid deopts in old v8\n  if (type === CODES) {\n    base = extra = work;    /* dummy value--not used */\n    end = 19;\n\n  } else if (type === LENS) {\n    base = lbase;\n    base_index -= 257;\n    extra = lext;\n    extra_index -= 257;\n    end = 256;\n\n  } else {                    /* DISTS */\n    base = dbase;\n    extra = dext;\n    end = -1;\n  }\n\n  /* initialize opts for loop */\n  huff = 0;                   /* starting code */\n  sym = 0;                    /* starting code symbol */\n  len = min;                  /* starting code length */\n  next = table_index;              /* current table to fill in */\n  curr = root;                /* current table index bits */\n  drop = 0;                   /* current bits to drop from code for index */\n  low = -1;                   /* trigger new sub-table when len > root */\n  used = 1 << root;          /* use root table entries */\n  mask = used - 1;            /* mask for comparing low */\n\n  /* check available table space */\n  if ((type === LENS && used > ENOUGH_LENS) ||\n    (type === DISTS && used > ENOUGH_DISTS)) {\n    return 1;\n  }\n\n  var i=0;\n  /* process all codes and make table entries */\n  for (;;) {\n    i++;\n    /* create table entry */\n    here_bits = len - drop;\n    if (work[sym] < end) {\n      here_op = 0;\n      here_val = work[sym];\n    }\n    else if (work[sym] > end) {\n      here_op = extra[extra_index + work[sym]];\n      here_val = base[base_index + work[sym]];\n    }\n    else {\n      here_op = 32 + 64;         /* end of block */\n      here_val = 0;\n    }\n\n    /* replicate for those indices with low len bits equal to huff */\n    incr = 1 << (len - drop);\n    fill = 1 << curr;\n    min = fill;                 /* save offset to next table */\n    do {\n      fill -= incr;\n      table[next + (huff >> drop) + fill] = (here_bits << 24) | (here_op << 16) | here_val |0;\n    } while (fill !== 0);\n\n    /* backwards increment the len-bit code huff */\n    incr = 1 << (len - 1);\n    while (huff & incr) {\n      incr >>= 1;\n    }\n    if (incr !== 0) {\n      huff &= incr - 1;\n      huff += incr;\n    } else {\n      huff = 0;\n    }\n\n    /* go to next symbol, update count, len */\n    sym++;\n    if (--count[len] === 0) {\n      if (len === max) { break; }\n      len = lens[lens_index + work[sym]];\n    }\n\n    /* create new sub-table if needed */\n    if (len > root && (huff & mask) !== low) {\n      /* if first time, transition to sub-tables */\n      if (drop === 0) {\n        drop = root;\n      }\n\n      /* increment past last table */\n      next += min;            /* here min is 1 << curr */\n\n      /* determine length of next table */\n      curr = len - drop;\n      left = 1 << curr;\n      while (curr + drop < max) {\n        left -= count[curr + drop];\n        if (left <= 0) { break; }\n        curr++;\n        left <<= 1;\n      }\n\n      /* check for enough space */\n      used += 1 << curr;\n      if ((type === LENS && used > ENOUGH_LENS) ||\n        (type === DISTS && used > ENOUGH_DISTS)) {\n        return 1;\n      }\n\n      /* point entry in root table to sub-table */\n      low = huff & mask;\n      /*table.op[low] = curr;\n      table.bits[low] = root;\n      table.val[low] = next - opts.table_index;*/\n      table[low] = (root << 24) | (curr << 16) | (next - table_index) |0;\n    }\n  }\n\n  /* fill in remaining table entry if code is incomplete (guaranteed to have\n   at most one remaining entry, since if the code is incomplete, the\n   maximum code length that was allowed to get this far is one bit) */\n  if (huff !== 0) {\n    //table.op[next + huff] = 64;            /* invalid code marker */\n    //table.bits[next + huff] = len - drop;\n    //table.val[next + huff] = 0;\n    table[next + huff] = ((len - drop) << 24) | (64 << 16) |0;\n  }\n\n  /* set return parameters */\n  //opts.table_index += used;\n  opts.bits = root;\n  return 0;\n};\n", "'use strict';\n\n\nvar utils = require('../utils/common');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n//var Z_FILTERED          = 1;\n//var Z_HUFFMAN_ONLY      = 2;\n//var Z_RLE               = 3;\nvar Z_FIXED               = 4;\n//var Z_DEFAULT_STRATEGY  = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\nvar Z_BINARY              = 0;\nvar Z_TEXT                = 1;\n//var Z_ASCII             = 1; // = Z_TEXT\nvar Z_UNKNOWN             = 2;\n\n/*============================================================================*/\n\n\nfunction zero(buf) { var len = buf.length; while (--len >= 0) { buf[len] = 0; } }\n\n// From zutil.h\n\nvar STORED_BLOCK = 0;\nvar STATIC_TREES = 1;\nvar DYN_TREES    = 2;\n/* The three kinds of block type */\n\nvar MIN_MATCH    = 3;\nvar MAX_MATCH    = 258;\n/* The minimum and maximum match lengths */\n\n// From deflate.h\n/* ===========================================================================\n * Internal compression state.\n */\n\nvar LENGTH_CODES  = 29;\n/* number of length codes, not counting the special END_BLOCK code */\n\nvar LITERALS      = 256;\n/* number of literal bytes 0..255 */\n\nvar L_CODES       = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\n\nvar D_CODES       = 30;\n/* number of distance codes */\n\nvar BL_CODES      = 19;\n/* number of codes used to transfer the bit lengths */\n\nvar HEAP_SIZE     = 2*L_CODES + 1;\n/* maximum heap size */\n\nvar MAX_BITS      = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar Buf_size      = 16;\n/* size of bit buffer in bi_buf */\n\n\n/* ===========================================================================\n * Constants\n */\n\nvar MAX_BL_BITS = 7;\n/* Bit length codes must not exceed MAX_BL_BITS bits */\n\nvar END_BLOCK   = 256;\n/* end of block literal code */\n\nvar REP_3_6     = 16;\n/* repeat previous bit length 3-6 times (2 bits of repeat count) */\n\nvar REPZ_3_10   = 17;\n/* repeat a zero length 3-10 times  (3 bits of repeat count) */\n\nvar REPZ_11_138 = 18;\n/* repeat a zero length 11-138 times  (7 bits of repeat count) */\n\nvar extra_lbits =   /* extra bits for each length code */\n  [0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0];\n\nvar extra_dbits =   /* extra bits for each distance code */\n  [0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];\n\nvar extra_blbits =  /* extra bits for each bit length code */\n  [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7];\n\nvar bl_order =\n  [16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];\n/* The lengths of the bit length codes are sent in order of decreasing\n * probability, to avoid transmitting the lengths for unused bit length codes.\n */\n\n/* ===========================================================================\n * Local data. These are initialized only once.\n */\n\n// We pre-fill arrays with 0 to avoid uninitialized gaps\n\nvar DIST_CODE_LEN = 512; /* see definition of array dist_code below */\n\n// !!!! Use flat array insdead of structure, Freq = i*2, Len = i*2+1\nvar static_ltree  = new Array((L_CODES+2) * 2);\nzero(static_ltree);\n/* The static literal tree. Since the bit lengths are imposed, there is no\n * need for the L_CODES extra codes used during heap construction. However\n * The codes 286 and 287 are needed to build a canonical tree (see _tr_init\n * below).\n */\n\nvar static_dtree  = new Array(D_CODES * 2);\nzero(static_dtree);\n/* The static distance tree. (Actually a trivial tree since all codes use\n * 5 bits.)\n */\n\nvar _dist_code    = new Array(DIST_CODE_LEN);\nzero(_dist_code);\n/* Distance codes. The first 256 values correspond to the distances\n * 3 .. 258, the last 256 values correspond to the top 8 bits of\n * the 15 bit distances.\n */\n\nvar _length_code  = new Array(MAX_MATCH-MIN_MATCH+1);\nzero(_length_code);\n/* length code for each normalized match length (0 == MIN_MATCH) */\n\nvar base_length   = new Array(LENGTH_CODES);\nzero(base_length);\n/* First normalized length for each code (0 = MIN_MATCH) */\n\nvar base_dist     = new Array(D_CODES);\nzero(base_dist);\n/* First normalized distance for each code (0 = distance of 1) */\n\n\nvar StaticTreeDesc = function (static_tree, extra_bits, extra_base, elems, max_length) {\n\n  this.static_tree  = static_tree;  /* static tree or NULL */\n  this.extra_bits   = extra_bits;   /* extra bits for each code or NULL */\n  this.extra_base   = extra_base;   /* base index for extra_bits */\n  this.elems        = elems;        /* max number of elements in the tree */\n  this.max_length   = max_length;   /* max bit length for the codes */\n\n  // show if `static_tree` has data or dummy - needed for monomorphic objects\n  this.has_stree    = static_tree && static_tree.length;\n};\n\n\nvar static_l_desc;\nvar static_d_desc;\nvar static_bl_desc;\n\n\nvar TreeDesc = function(dyn_tree, stat_desc) {\n  this.dyn_tree = dyn_tree;     /* the dynamic tree */\n  this.max_code = 0;            /* largest code with non zero frequency */\n  this.stat_desc = stat_desc;   /* the corresponding static tree */\n};\n\n\n\nfunction d_code(dist) {\n  return dist < 256 ? _dist_code[dist] : _dist_code[256 + (dist >>> 7)];\n}\n\n\n/* ===========================================================================\n * Output a short LSB first on the stream.\n * IN assertion: there is enough room in pendingBuf.\n */\nfunction put_short (s, w) {\n//    put_byte(s, (uch)((w) & 0xff));\n//    put_byte(s, (uch)((ush)(w) >> 8));\n  s.pending_buf[s.pending++] = (w) & 0xff;\n  s.pending_buf[s.pending++] = (w >>> 8) & 0xff;\n}\n\n\n/* ===========================================================================\n * Send a value on a given number of bits.\n * IN assertion: length <= 16 and value fits in length bits.\n */\nfunction send_bits(s, value, length) {\n  if (s.bi_valid > (Buf_size - length)) {\n    s.bi_buf |= (value << s.bi_valid) & 0xffff;\n    put_short(s, s.bi_buf);\n    s.bi_buf = value >> (Buf_size - s.bi_valid);\n    s.bi_valid += length - Buf_size;\n  } else {\n    s.bi_buf |= (value << s.bi_valid) & 0xffff;\n    s.bi_valid += length;\n  }\n}\n\n\nfunction send_code(s, c, tree) {\n  send_bits(s, tree[c*2]/*.Code*/, tree[c*2 + 1]/*.Len*/);\n}\n\n\n/* ===========================================================================\n * Reverse the first len bits of a code, using straightforward code (a faster\n * method would use a table)\n * IN assertion: 1 <= len <= 15\n */\nfunction bi_reverse(code, len) {\n  var res = 0;\n  do {\n    res |= code & 1;\n    code >>>= 1;\n    res <<= 1;\n  } while (--len > 0);\n  return res >>> 1;\n}\n\n\n/* ===========================================================================\n * Flush the bit buffer, keeping at most 7 bits in it.\n */\nfunction bi_flush(s) {\n  if (s.bi_valid === 16) {\n    put_short(s, s.bi_buf);\n    s.bi_buf = 0;\n    s.bi_valid = 0;\n\n  } else if (s.bi_valid >= 8) {\n    s.pending_buf[s.pending++] = s.bi_buf & 0xff;\n    s.bi_buf >>= 8;\n    s.bi_valid -= 8;\n  }\n}\n\n\n/* ===========================================================================\n * Compute the optimal bit lengths for a tree and update the total bit length\n * for the current block.\n * IN assertion: the fields freq and dad are set, heap[heap_max] and\n *    above are the tree nodes sorted by increasing frequency.\n * OUT assertions: the field len is set to the optimal bit length, the\n *     array bl_count contains the frequencies for each bit length.\n *     The length opt_len is updated; static_len is also updated if stree is\n *     not null.\n */\nfunction gen_bitlen(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc;    /* the tree descriptor */\n{\n  var tree            = desc.dyn_tree;\n  var max_code        = desc.max_code;\n  var stree           = desc.stat_desc.static_tree;\n  var has_stree       = desc.stat_desc.has_stree;\n  var extra           = desc.stat_desc.extra_bits;\n  var base            = desc.stat_desc.extra_base;\n  var max_length      = desc.stat_desc.max_length;\n  var h;              /* heap index */\n  var n, m;           /* iterate over the tree elements */\n  var bits;           /* bit length */\n  var xbits;          /* extra bits */\n  var f;              /* frequency */\n  var overflow = 0;   /* number of elements with bit length too large */\n\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    s.bl_count[bits] = 0;\n  }\n\n  /* In a first pass, compute the optimal bit lengths (which may\n   * overflow in the case of the bit length tree).\n   */\n  tree[s.heap[s.heap_max]*2 + 1]/*.Len*/ = 0; /* root of the heap */\n\n  for (h = s.heap_max+1; h < HEAP_SIZE; h++) {\n    n = s.heap[h];\n    bits = tree[tree[n*2 +1]/*.Dad*/ * 2 + 1]/*.Len*/ + 1;\n    if (bits > max_length) {\n      bits = max_length;\n      overflow++;\n    }\n    tree[n*2 + 1]/*.Len*/ = bits;\n    /* We overwrite tree[n].Dad which is no longer needed */\n\n    if (n > max_code) { continue; } /* not a leaf node */\n\n    s.bl_count[bits]++;\n    xbits = 0;\n    if (n >= base) {\n      xbits = extra[n-base];\n    }\n    f = tree[n * 2]/*.Freq*/;\n    s.opt_len += f * (bits + xbits);\n    if (has_stree) {\n      s.static_len += f * (stree[n*2 + 1]/*.Len*/ + xbits);\n    }\n  }\n  if (overflow === 0) { return; }\n\n  // Trace((stderr,\"\\nbit length overflow\\n\"));\n  /* This happens for example on obj2 and pic of the Calgary corpus */\n\n  /* Find the first bit length which could increase: */\n  do {\n    bits = max_length-1;\n    while (s.bl_count[bits] === 0) { bits--; }\n    s.bl_count[bits]--;      /* move one leaf down the tree */\n    s.bl_count[bits+1] += 2; /* move one overflow item as its brother */\n    s.bl_count[max_length]--;\n    /* The brother of the overflow item also moves one step up,\n     * but this does not affect bl_count[max_length]\n     */\n    overflow -= 2;\n  } while (overflow > 0);\n\n  /* Now recompute all bit lengths, scanning in increasing frequency.\n   * h is still equal to HEAP_SIZE. (It is simpler to reconstruct all\n   * lengths instead of fixing only the wrong ones. This idea is taken\n   * from 'ar' written by Haruhiko Okumura.)\n   */\n  for (bits = max_length; bits !== 0; bits--) {\n    n = s.bl_count[bits];\n    while (n !== 0) {\n      m = s.heap[--h];\n      if (m > max_code) { continue; }\n      if (tree[m*2 + 1]/*.Len*/ !== bits) {\n        // Trace((stderr,\"code %d bits %d->%d\\n\", m, tree[m].Len, bits));\n        s.opt_len += (bits - tree[m*2 + 1]/*.Len*/)*tree[m*2]/*.Freq*/;\n        tree[m*2 + 1]/*.Len*/ = bits;\n      }\n      n--;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Generate the codes for a given tree and bit counts (which need not be\n * optimal).\n * IN assertion: the array bl_count contains the bit length statistics for\n * the given tree and the field len is set for all tree elements.\n * OUT assertion: the field code is set for all tree elements of non\n *     zero code length.\n */\nfunction gen_codes(tree, max_code, bl_count)\n//    ct_data *tree;             /* the tree to decorate */\n//    int max_code;              /* largest code with non zero frequency */\n//    ushf *bl_count;            /* number of codes at each bit length */\n{\n  var next_code = new Array(MAX_BITS+1); /* next code value for each bit length */\n  var code = 0;              /* running code value */\n  var bits;                  /* bit index */\n  var n;                     /* code index */\n\n  /* The distribution counts are first used to generate the code values\n   * without bit reversal.\n   */\n  for (bits = 1; bits <= MAX_BITS; bits++) {\n    next_code[bits] = code = (code + bl_count[bits-1]) << 1;\n  }\n  /* Check that the bit counts in bl_count are consistent. The last code\n   * must be all ones.\n   */\n  //Assert (code + bl_count[MAX_BITS]-1 == (1<<MAX_BITS)-1,\n  //        \"inconsistent bit counts\");\n  //Tracev((stderr,\"\\ngen_codes: max_code %d \", max_code));\n\n  for (n = 0;  n <= max_code; n++) {\n    var len = tree[n*2 + 1]/*.Len*/;\n    if (len === 0) { continue; }\n    /* Now reverse the bits */\n    tree[n*2]/*.Code*/ = bi_reverse(next_code[len]++, len);\n\n    //Tracecv(tree != static_ltree, (stderr,\"\\nn %3d %c l %2d c %4x (%x) \",\n    //     n, (isgraph(n) ? n : ' '), len, tree[n].Code, next_code[len]-1));\n  }\n}\n\n\n/* ===========================================================================\n * Initialize the various 'constant' tables.\n */\nfunction tr_static_init() {\n  var n;        /* iterates over tree elements */\n  var bits;     /* bit counter */\n  var length;   /* length value */\n  var code;     /* code value */\n  var dist;     /* distance index */\n  var bl_count = new Array(MAX_BITS+1);\n  /* number of codes at each bit length for an optimal tree */\n\n  // do check in _tr_init()\n  //if (static_init_done) return;\n\n  /* For some embedded targets, global variables are not initialized: */\n/*#ifdef NO_INIT_GLOBAL_POINTERS\n  static_l_desc.static_tree = static_ltree;\n  static_l_desc.extra_bits = extra_lbits;\n  static_d_desc.static_tree = static_dtree;\n  static_d_desc.extra_bits = extra_dbits;\n  static_bl_desc.extra_bits = extra_blbits;\n#endif*/\n\n  /* Initialize the mapping length (0..255) -> length code (0..28) */\n  length = 0;\n  for (code = 0; code < LENGTH_CODES-1; code++) {\n    base_length[code] = length;\n    for (n = 0; n < (1<<extra_lbits[code]); n++) {\n      _length_code[length++] = code;\n    }\n  }\n  //Assert (length == 256, \"tr_static_init: length != 256\");\n  /* Note that the length 255 (match length 258) can be represented\n   * in two different ways: code 284 + 5 bits or code 285, so we\n   * overwrite length_code[255] to use the best encoding:\n   */\n  _length_code[length-1] = code;\n\n  /* Initialize the mapping dist (0..32K) -> dist code (0..29) */\n  dist = 0;\n  for (code = 0 ; code < 16; code++) {\n    base_dist[code] = dist;\n    for (n = 0; n < (1<<extra_dbits[code]); n++) {\n      _dist_code[dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: dist != 256\");\n  dist >>= 7; /* from now on, all distances are divided by 128 */\n  for (; code < D_CODES; code++) {\n    base_dist[code] = dist << 7;\n    for (n = 0; n < (1<<(extra_dbits[code]-7)); n++) {\n      _dist_code[256 + dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: 256+dist != 512\");\n\n  /* Construct the codes of the static literal tree */\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    bl_count[bits] = 0;\n  }\n\n  n = 0;\n  while (n <= 143) {\n    static_ltree[n*2 + 1]/*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  while (n <= 255) {\n    static_ltree[n*2 + 1]/*.Len*/ = 9;\n    n++;\n    bl_count[9]++;\n  }\n  while (n <= 279) {\n    static_ltree[n*2 + 1]/*.Len*/ = 7;\n    n++;\n    bl_count[7]++;\n  }\n  while (n <= 287) {\n    static_ltree[n*2 + 1]/*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  /* Codes 286 and 287 do not exist, but we must include them in the\n   * tree construction to get a canonical Huffman tree (longest code\n   * all ones)\n   */\n  gen_codes(static_ltree, L_CODES+1, bl_count);\n\n  /* The static distance tree is trivial: */\n  for (n = 0; n < D_CODES; n++) {\n    static_dtree[n*2 + 1]/*.Len*/ = 5;\n    static_dtree[n*2]/*.Code*/ = bi_reverse(n, 5);\n  }\n\n  // Now data ready and we can init static trees\n  static_l_desc = new StaticTreeDesc(static_ltree, extra_lbits, LITERALS+1, L_CODES, MAX_BITS);\n  static_d_desc = new StaticTreeDesc(static_dtree, extra_dbits, 0,          D_CODES, MAX_BITS);\n  static_bl_desc =new StaticTreeDesc(new Array(0), extra_blbits, 0,         BL_CODES, MAX_BL_BITS);\n\n  //static_init_done = true;\n}\n\n\n/* ===========================================================================\n * Initialize a new block.\n */\nfunction init_block(s) {\n  var n; /* iterates over tree elements */\n\n  /* Initialize the trees. */\n  for (n = 0; n < L_CODES;  n++) { s.dyn_ltree[n*2]/*.Freq*/ = 0; }\n  for (n = 0; n < D_CODES;  n++) { s.dyn_dtree[n*2]/*.Freq*/ = 0; }\n  for (n = 0; n < BL_CODES; n++) { s.bl_tree[n*2]/*.Freq*/ = 0; }\n\n  s.dyn_ltree[END_BLOCK*2]/*.Freq*/ = 1;\n  s.opt_len = s.static_len = 0;\n  s.last_lit = s.matches = 0;\n}\n\n\n/* ===========================================================================\n * Flush the bit buffer and align the output on a byte boundary\n */\nfunction bi_windup(s)\n{\n  if (s.bi_valid > 8) {\n    put_short(s, s.bi_buf);\n  } else if (s.bi_valid > 0) {\n    //put_byte(s, (Byte)s->bi_buf);\n    s.pending_buf[s.pending++] = s.bi_buf;\n  }\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n}\n\n/* ===========================================================================\n * Copy a stored block, storing first the length and its\n * one's complement if requested.\n */\nfunction copy_block(s, buf, len, header)\n//DeflateState *s;\n//charf    *buf;    /* the input data */\n//unsigned len;     /* its length */\n//int      header;  /* true if block header must be written */\n{\n  bi_windup(s);        /* align on byte boundary */\n\n  if (header) {\n    put_short(s, len);\n    put_short(s, ~len);\n  }\n//  while (len--) {\n//    put_byte(s, *buf++);\n//  }\n  utils.arraySet(s.pending_buf, s.window, buf, len, s.pending);\n  s.pending += len;\n}\n\n/* ===========================================================================\n * Compares to subtrees, using the tree depth as tie breaker when\n * the subtrees have equal frequency. This minimizes the worst case length.\n */\nfunction smaller(tree, n, m, depth) {\n  var _n2 = n*2;\n  var _m2 = m*2;\n  return (tree[_n2]/*.Freq*/ < tree[_m2]/*.Freq*/ ||\n         (tree[_n2]/*.Freq*/ === tree[_m2]/*.Freq*/ && depth[n] <= depth[m]));\n}\n\n/* ===========================================================================\n * Restore the heap property by moving down the tree starting at node k,\n * exchanging a node with the smallest of its two sons if necessary, stopping\n * when the heap property is re-established (each father smaller than its\n * two sons).\n */\nfunction pqdownheap(s, tree, k)\n//    deflate_state *s;\n//    ct_data *tree;  /* the tree to restore */\n//    int k;               /* node to move down */\n{\n  var v = s.heap[k];\n  var j = k << 1;  /* left son of k */\n  while (j <= s.heap_len) {\n    /* Set j to the smallest of the two sons: */\n    if (j < s.heap_len &&\n      smaller(tree, s.heap[j+1], s.heap[j], s.depth)) {\n      j++;\n    }\n    /* Exit if v is smaller than both sons */\n    if (smaller(tree, v, s.heap[j], s.depth)) { break; }\n\n    /* Exchange v with the smallest son */\n    s.heap[k] = s.heap[j];\n    k = j;\n\n    /* And continue down the tree, setting j to the left son of k */\n    j <<= 1;\n  }\n  s.heap[k] = v;\n}\n\n\n// inlined manually\n// var SMALLEST = 1;\n\n/* ===========================================================================\n * Send the block data compressed using the given Huffman trees\n */\nfunction compress_block(s, ltree, dtree)\n//    deflate_state *s;\n//    const ct_data *ltree; /* literal tree */\n//    const ct_data *dtree; /* distance tree */\n{\n  var dist;           /* distance of matched string */\n  var lc;             /* match length or unmatched char (if dist == 0) */\n  var lx = 0;         /* running index in l_buf */\n  var code;           /* the code to send */\n  var extra;          /* number of extra bits to send */\n\n  if (s.last_lit !== 0) {\n    do {\n      dist = (s.pending_buf[s.d_buf + lx*2] << 8) | (s.pending_buf[s.d_buf + lx*2 + 1]);\n      lc = s.pending_buf[s.l_buf + lx];\n      lx++;\n\n      if (dist === 0) {\n        send_code(s, lc, ltree); /* send a literal byte */\n        //Tracecv(isgraph(lc), (stderr,\" '%c' \", lc));\n      } else {\n        /* Here, lc is the match length - MIN_MATCH */\n        code = _length_code[lc];\n        send_code(s, code+LITERALS+1, ltree); /* send the length code */\n        extra = extra_lbits[code];\n        if (extra !== 0) {\n          lc -= base_length[code];\n          send_bits(s, lc, extra);       /* send the extra length bits */\n        }\n        dist--; /* dist is now the match distance - 1 */\n        code = d_code(dist);\n        //Assert (code < D_CODES, \"bad d_code\");\n\n        send_code(s, code, dtree);       /* send the distance code */\n        extra = extra_dbits[code];\n        if (extra !== 0) {\n          dist -= base_dist[code];\n          send_bits(s, dist, extra);   /* send the extra distance bits */\n        }\n      } /* literal or match pair ? */\n\n      /* Check that the overlay between pending_buf and d_buf+l_buf is ok: */\n      //Assert((uInt)(s->pending) < s->lit_bufsize + 2*lx,\n      //       \"pendingBuf overflow\");\n\n    } while (lx < s.last_lit);\n  }\n\n  send_code(s, END_BLOCK, ltree);\n}\n\n\n/* ===========================================================================\n * Construct one Huffman tree and assigns the code bit strings and lengths.\n * Update the total bit length for the current block.\n * IN assertion: the field freq is set for all tree elements.\n * OUT assertions: the fields len and code are set to the optimal bit length\n *     and corresponding code. The length opt_len is updated; static_len is\n *     also updated if stree is not null. The field max_code is set.\n */\nfunction build_tree(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc; /* the tree descriptor */\n{\n  var tree     = desc.dyn_tree;\n  var stree    = desc.stat_desc.static_tree;\n  var has_stree = desc.stat_desc.has_stree;\n  var elems    = desc.stat_desc.elems;\n  var n, m;          /* iterate over heap elements */\n  var max_code = -1; /* largest code with non zero frequency */\n  var node;          /* new node being created */\n\n  /* Construct the initial heap, with least frequent element in\n   * heap[SMALLEST]. The sons of heap[n] are heap[2*n] and heap[2*n+1].\n   * heap[0] is not used.\n   */\n  s.heap_len = 0;\n  s.heap_max = HEAP_SIZE;\n\n  for (n = 0; n < elems; n++) {\n    if (tree[n * 2]/*.Freq*/ !== 0) {\n      s.heap[++s.heap_len] = max_code = n;\n      s.depth[n] = 0;\n\n    } else {\n      tree[n*2 + 1]/*.Len*/ = 0;\n    }\n  }\n\n  /* The pkzip format requires that at least one distance code exists,\n   * and that at least one bit should be sent even if there is only one\n   * possible code. So to avoid special checks later on we force at least\n   * two codes of non zero frequency.\n   */\n  while (s.heap_len < 2) {\n    node = s.heap[++s.heap_len] = (max_code < 2 ? ++max_code : 0);\n    tree[node * 2]/*.Freq*/ = 1;\n    s.depth[node] = 0;\n    s.opt_len--;\n\n    if (has_stree) {\n      s.static_len -= stree[node*2 + 1]/*.Len*/;\n    }\n    /* node is 0 or 1 so it does not have extra bits */\n  }\n  desc.max_code = max_code;\n\n  /* The elements heap[heap_len/2+1 .. heap_len] are leaves of the tree,\n   * establish sub-heaps of increasing lengths:\n   */\n  for (n = (s.heap_len >> 1/*int /2*/); n >= 1; n--) { pqdownheap(s, tree, n); }\n\n  /* Construct the Huffman tree by repeatedly combining the least two\n   * frequent nodes.\n   */\n  node = elems;              /* next internal node of the tree */\n  do {\n    //pqremove(s, tree, n);  /* n = node of least frequency */\n    /*** pqremove ***/\n    n = s.heap[1/*SMALLEST*/];\n    s.heap[1/*SMALLEST*/] = s.heap[s.heap_len--];\n    pqdownheap(s, tree, 1/*SMALLEST*/);\n    /***/\n\n    m = s.heap[1/*SMALLEST*/]; /* m = node of next least frequency */\n\n    s.heap[--s.heap_max] = n; /* keep the nodes sorted by frequency */\n    s.heap[--s.heap_max] = m;\n\n    /* Create a new node father of n and m */\n    tree[node * 2]/*.Freq*/ = tree[n * 2]/*.Freq*/ + tree[m * 2]/*.Freq*/;\n    s.depth[node] = (s.depth[n] >= s.depth[m] ? s.depth[n] : s.depth[m]) + 1;\n    tree[n*2 + 1]/*.Dad*/ = tree[m*2 + 1]/*.Dad*/ = node;\n\n    /* and insert the new node in the heap */\n    s.heap[1/*SMALLEST*/] = node++;\n    pqdownheap(s, tree, 1/*SMALLEST*/);\n\n  } while (s.heap_len >= 2);\n\n  s.heap[--s.heap_max] = s.heap[1/*SMALLEST*/];\n\n  /* At this point, the fields freq and dad are set. We can now\n   * generate the bit lengths.\n   */\n  gen_bitlen(s, desc);\n\n  /* The field len is now set, we can generate the bit codes */\n  gen_codes(tree, max_code, s.bl_count);\n}\n\n\n/* ===========================================================================\n * Scan a literal or distance tree to determine the frequencies of the codes\n * in the bit length tree.\n */\nfunction scan_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree;   /* the tree to be scanned */\n//    int max_code;    /* and its largest code of non zero frequency */\n{\n  var n;                     /* iterates over all tree elements */\n  var prevlen = -1;          /* last emitted length */\n  var curlen;                /* length of current code */\n\n  var nextlen = tree[0*2 + 1]/*.Len*/; /* length of next code */\n\n  var count = 0;             /* repeat count of the current code */\n  var max_count = 7;         /* max repeat count */\n  var min_count = 4;         /* min repeat count */\n\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n  tree[(max_code+1)*2 + 1]/*.Len*/ = 0xffff; /* guard */\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n+1)*2 + 1]/*.Len*/;\n\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n\n    } else if (count < min_count) {\n      s.bl_tree[curlen * 2]/*.Freq*/ += count;\n\n    } else if (curlen !== 0) {\n\n      if (curlen !== prevlen) { s.bl_tree[curlen * 2]/*.Freq*/++; }\n      s.bl_tree[REP_3_6*2]/*.Freq*/++;\n\n    } else if (count <= 10) {\n      s.bl_tree[REPZ_3_10*2]/*.Freq*/++;\n\n    } else {\n      s.bl_tree[REPZ_11_138*2]/*.Freq*/++;\n    }\n\n    count = 0;\n    prevlen = curlen;\n\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Send a literal or distance tree in compressed form, using the codes in\n * bl_tree.\n */\nfunction send_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree; /* the tree to be scanned */\n//    int max_code;       /* and its largest code of non zero frequency */\n{\n  var n;                     /* iterates over all tree elements */\n  var prevlen = -1;          /* last emitted length */\n  var curlen;                /* length of current code */\n\n  var nextlen = tree[0*2 + 1]/*.Len*/; /* length of next code */\n\n  var count = 0;             /* repeat count of the current code */\n  var max_count = 7;         /* max repeat count */\n  var min_count = 4;         /* min repeat count */\n\n  /* tree[max_code+1].Len = -1; */  /* guard already set */\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n+1)*2 + 1]/*.Len*/;\n\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n\n    } else if (count < min_count) {\n      do { send_code(s, curlen, s.bl_tree); } while (--count !== 0);\n\n    } else if (curlen !== 0) {\n      if (curlen !== prevlen) {\n        send_code(s, curlen, s.bl_tree);\n        count--;\n      }\n      //Assert(count >= 3 && count <= 6, \" 3_6?\");\n      send_code(s, REP_3_6, s.bl_tree);\n      send_bits(s, count-3, 2);\n\n    } else if (count <= 10) {\n      send_code(s, REPZ_3_10, s.bl_tree);\n      send_bits(s, count-3, 3);\n\n    } else {\n      send_code(s, REPZ_11_138, s.bl_tree);\n      send_bits(s, count-11, 7);\n    }\n\n    count = 0;\n    prevlen = curlen;\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Construct the Huffman tree for the bit lengths and return the index in\n * bl_order of the last bit length code to send.\n */\nfunction build_bl_tree(s) {\n  var max_blindex;  /* index of last bit length code of non zero freq */\n\n  /* Determine the bit length frequencies for literal and distance trees */\n  scan_tree(s, s.dyn_ltree, s.l_desc.max_code);\n  scan_tree(s, s.dyn_dtree, s.d_desc.max_code);\n\n  /* Build the bit length tree: */\n  build_tree(s, s.bl_desc);\n  /* opt_len now includes the length of the tree representations, except\n   * the lengths of the bit lengths codes and the 5+5+4 bits for the counts.\n   */\n\n  /* Determine the number of bit length codes to send. The pkzip format\n   * requires that at least 4 bit length codes be sent. (appnote.txt says\n   * 3 but the actual value used is 4.)\n   */\n  for (max_blindex = BL_CODES-1; max_blindex >= 3; max_blindex--) {\n    if (s.bl_tree[bl_order[max_blindex]*2 + 1]/*.Len*/ !== 0) {\n      break;\n    }\n  }\n  /* Update opt_len to include the bit length tree and counts */\n  s.opt_len += 3*(max_blindex+1) + 5+5+4;\n  //Tracev((stderr, \"\\ndyn trees: dyn %ld, stat %ld\",\n  //        s->opt_len, s->static_len));\n\n  return max_blindex;\n}\n\n\n/* ===========================================================================\n * Send the header for a block using dynamic Huffman trees: the counts, the\n * lengths of the bit length codes, the literal tree and the distance tree.\n * IN assertion: lcodes >= 257, dcodes >= 1, blcodes >= 4.\n */\nfunction send_all_trees(s, lcodes, dcodes, blcodes)\n//    deflate_state *s;\n//    int lcodes, dcodes, blcodes; /* number of codes for each tree */\n{\n  var rank;                    /* index in bl_order */\n\n  //Assert (lcodes >= 257 && dcodes >= 1 && blcodes >= 4, \"not enough codes\");\n  //Assert (lcodes <= L_CODES && dcodes <= D_CODES && blcodes <= BL_CODES,\n  //        \"too many codes\");\n  //Tracev((stderr, \"\\nbl counts: \"));\n  send_bits(s, lcodes-257, 5); /* not +255 as stated in appnote.txt */\n  send_bits(s, dcodes-1,   5);\n  send_bits(s, blcodes-4,  4); /* not -3 as stated in appnote.txt */\n  for (rank = 0; rank < blcodes; rank++) {\n    //Tracev((stderr, \"\\nbl code %2d \", bl_order[rank]));\n    send_bits(s, s.bl_tree[bl_order[rank]*2 + 1]/*.Len*/, 3);\n  }\n  //Tracev((stderr, \"\\nbl tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_ltree, lcodes-1); /* literal tree */\n  //Tracev((stderr, \"\\nlit tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_dtree, dcodes-1); /* distance tree */\n  //Tracev((stderr, \"\\ndist tree: sent %ld\", s->bits_sent));\n}\n\n\n/* ===========================================================================\n * Check if the data type is TEXT or BINARY, using the following algorithm:\n * - TEXT if the two conditions below are satisfied:\n *    a) There are no non-portable control characters belonging to the\n *       \"black list\" (0..6, 14..25, 28..31).\n *    b) There is at least one printable character belonging to the\n *       \"white list\" (9 {TAB}, 10 {LF}, 13 {CR}, 32..255).\n * - BINARY otherwise.\n * - The following partially-portable control characters form a\n *   \"gray list\" that is ignored in this detection algorithm:\n *   (7 {BEL}, 8 {BS}, 11 {VT}, 12 {FF}, 26 {SUB}, 27 {ESC}).\n * IN assertion: the fields Freq of dyn_ltree are set.\n */\nfunction detect_data_type(s) {\n  /* black_mask is the bit mask of black-listed bytes\n   * set bits 0..6, 14..25, and 28..31\n   * 0xf3ffc07f = binary 11110011111111111100000001111111\n   */\n  var black_mask = 0xf3ffc07f;\n  var n;\n\n  /* Check for non-textual (\"black-listed\") bytes. */\n  for (n = 0; n <= 31; n++, black_mask >>>= 1) {\n    if ((black_mask & 1) && (s.dyn_ltree[n*2]/*.Freq*/ !== 0)) {\n      return Z_BINARY;\n    }\n  }\n\n  /* Check for textual (\"white-listed\") bytes. */\n  if (s.dyn_ltree[9 * 2]/*.Freq*/ !== 0 || s.dyn_ltree[10 * 2]/*.Freq*/ !== 0 ||\n      s.dyn_ltree[13 * 2]/*.Freq*/ !== 0) {\n    return Z_TEXT;\n  }\n  for (n = 32; n < LITERALS; n++) {\n    if (s.dyn_ltree[n * 2]/*.Freq*/ !== 0) {\n      return Z_TEXT;\n    }\n  }\n\n  /* There are no \"black-listed\" or \"white-listed\" bytes:\n   * this stream either is empty or has tolerated (\"gray-listed\") bytes only.\n   */\n  return Z_BINARY;\n}\n\n\nvar static_init_done = false;\n\n/* ===========================================================================\n * Initialize the tree data structures for a new zlib stream.\n */\nfunction _tr_init(s)\n{\n\n  if (!static_init_done) {\n    tr_static_init();\n    static_init_done = true;\n  }\n\n  s.l_desc  = new TreeDesc(s.dyn_ltree, static_l_desc);\n  s.d_desc  = new TreeDesc(s.dyn_dtree, static_d_desc);\n  s.bl_desc = new TreeDesc(s.bl_tree, static_bl_desc);\n\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n\n  /* Initialize the first block of the first file: */\n  init_block(s);\n}\n\n\n/* ===========================================================================\n * Send a stored block\n */\nfunction _tr_stored_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  send_bits(s, (STORED_BLOCK<<1)+(last ? 1 : 0), 3);    /* send block type */\n  copy_block(s, buf, stored_len, true); /* with header */\n}\n\n\n/* ===========================================================================\n * Send one empty static block to give enough lookahead for inflate.\n * This takes 10 bits, of which 7 may remain in the bit buffer.\n */\nfunction _tr_align(s) {\n  send_bits(s, STATIC_TREES<<1, 3);\n  send_code(s, END_BLOCK, static_ltree);\n  bi_flush(s);\n}\n\n\n/* ===========================================================================\n * Determine the best encoding for the current block: dynamic trees, static\n * trees or store, and output the encoded block to the zip file.\n */\nfunction _tr_flush_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block, or NULL if too old */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  var opt_lenb, static_lenb;  /* opt_len and static_len in bytes */\n  var max_blindex = 0;        /* index of last bit length code of non zero freq */\n\n  /* Build the Huffman trees unless a stored block is forced */\n  if (s.level > 0) {\n\n    /* Check if the file is binary or text */\n    if (s.strm.data_type === Z_UNKNOWN) {\n      s.strm.data_type = detect_data_type(s);\n    }\n\n    /* Construct the literal and distance trees */\n    build_tree(s, s.l_desc);\n    // Tracev((stderr, \"\\nlit data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n\n    build_tree(s, s.d_desc);\n    // Tracev((stderr, \"\\ndist data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n    /* At this point, opt_len and static_len are the total bit lengths of\n     * the compressed block data, excluding the tree representations.\n     */\n\n    /* Build the bit length tree for the above two trees, and get the index\n     * in bl_order of the last bit length code to send.\n     */\n    max_blindex = build_bl_tree(s);\n\n    /* Determine the best encoding. Compute the block lengths in bytes. */\n    opt_lenb = (s.opt_len+3+7) >>> 3;\n    static_lenb = (s.static_len+3+7) >>> 3;\n\n    // Tracev((stderr, \"\\nopt %lu(%lu) stat %lu(%lu) stored %lu lit %u \",\n    //        opt_lenb, s->opt_len, static_lenb, s->static_len, stored_len,\n    //        s->last_lit));\n\n    if (static_lenb <= opt_lenb) { opt_lenb = static_lenb; }\n\n  } else {\n    // Assert(buf != (char*)0, \"lost buf\");\n    opt_lenb = static_lenb = stored_len + 5; /* force a stored block */\n  }\n\n  if ((stored_len+4 <= opt_lenb) && (buf !== -1)) {\n    /* 4: two words for the lengths */\n\n    /* The test buf != NULL is only necessary if LIT_BUFSIZE > WSIZE.\n     * Otherwise we can't have processed more than WSIZE input bytes since\n     * the last block flush, because compression would have been\n     * successful. If LIT_BUFSIZE <= WSIZE, it is never too late to\n     * transform a block into a stored block.\n     */\n    _tr_stored_block(s, buf, stored_len, last);\n\n  } else if (s.strategy === Z_FIXED || static_lenb === opt_lenb) {\n\n    send_bits(s, (STATIC_TREES<<1) + (last ? 1 : 0), 3);\n    compress_block(s, static_ltree, static_dtree);\n\n  } else {\n    send_bits(s, (DYN_TREES<<1) + (last ? 1 : 0), 3);\n    send_all_trees(s, s.l_desc.max_code+1, s.d_desc.max_code+1, max_blindex+1);\n    compress_block(s, s.dyn_ltree, s.dyn_dtree);\n  }\n  // Assert (s->compressed_len == s->bits_sent, \"bad compressed size\");\n  /* The above check is made mod 2^32, for files larger than 512 MB\n   * and uLong implemented on 32 bits.\n   */\n  init_block(s);\n\n  if (last) {\n    bi_windup(s);\n  }\n  // Tracev((stderr,\"\\ncomprlen %lu(%lu) \", s->compressed_len>>3,\n  //       s->compressed_len-7*last));\n}\n\n/* ===========================================================================\n * Save the match info and tally the frequency counts. Return true if\n * the current block must be flushed.\n */\nfunction _tr_tally(s, dist, lc)\n//    deflate_state *s;\n//    unsigned dist;  /* distance of matched string */\n//    unsigned lc;    /* match length-MIN_MATCH or unmatched char (if dist==0) */\n{\n  //var out_length, in_length, dcode;\n\n  s.pending_buf[s.d_buf + s.last_lit * 2]     = (dist >>> 8) & 0xff;\n  s.pending_buf[s.d_buf + s.last_lit * 2 + 1] = dist & 0xff;\n\n  s.pending_buf[s.l_buf + s.last_lit] = lc & 0xff;\n  s.last_lit++;\n\n  if (dist === 0) {\n    /* lc is the unmatched char */\n    s.dyn_ltree[lc*2]/*.Freq*/++;\n  } else {\n    s.matches++;\n    /* Here, lc is the match length - MIN_MATCH */\n    dist--;             /* dist = match distance - 1 */\n    //Assert((ush)dist < (ush)MAX_DIST(s) &&\n    //       (ush)lc <= (ush)(MAX_MATCH-MIN_MATCH) &&\n    //       (ush)d_code(dist) < (ush)D_CODES,  \"_tr_tally: bad match\");\n\n    s.dyn_ltree[(_length_code[lc]+LITERALS+1) * 2]/*.Freq*/++;\n    s.dyn_dtree[d_code(dist) * 2]/*.Freq*/++;\n  }\n\n// (!) This block is disabled in zlib defailts,\n// don't enable it for binary compatibility\n\n//#ifdef TRUNCATE_BLOCK\n//  /* Try to guess if it is profitable to stop the current block here */\n//  if ((s.last_lit & 0x1fff) === 0 && s.level > 2) {\n//    /* Compute an upper bound for the compressed length */\n//    out_length = s.last_lit*8;\n//    in_length = s.strstart - s.block_start;\n//\n//    for (dcode = 0; dcode < D_CODES; dcode++) {\n//      out_length += s.dyn_dtree[dcode*2]/*.Freq*/ * (5 + extra_dbits[dcode]);\n//    }\n//    out_length >>>= 3;\n//    //Tracev((stderr,\"\\nlast_lit %u, in %ld, out ~%ld(%ld%%) \",\n//    //       s->last_lit, in_length, out_length,\n//    //       100L - out_length*100L/in_length));\n//    if (s.matches < (s.last_lit>>1)/*int /2*/ && out_length < (in_length>>1)/*int /2*/) {\n//      return true;\n//    }\n//  }\n//#endif\n\n  return (s.last_lit === s.lit_bufsize-1);\n  /* We avoid equality with lit_bufsize because of wraparound at 64K\n   * on 16 bit machines and because stored blocks are restricted to\n   * 64K-1 bytes.\n   */\n}\n\nexports._tr_init  = _tr_init;\nexports._tr_stored_block = _tr_stored_block;\nexports._tr_flush_block  = _tr_flush_block;\nexports._tr_tally = _tr_tally;\nexports._tr_align = _tr_align;\n"], "names": ["module", "exports", "this", "input", "next_in", "avail_in", "total_in", "output", "next_out", "avail_out", "total_out", "msg", "state", "data_type", "<PERSON><PERSON>", "buf", "len", "pos", "s1", "s2", "n", "crcTable", "c", "table", "k", "makeTable", "crc", "t", "end", "i", "TYPED_OK", "Uint8Array", "Uint16Array", "Int32Array", "assign", "obj", "sources", "Array", "prototype", "slice", "call", "arguments", "length", "source", "shift", "TypeError", "p", "hasOwnProperty", "shrinkBuf", "size", "subarray", "fnTyped", "arraySet", "dest", "src", "src_offs", "dest_offs", "set", "flattenChunks", "chunks", "l", "chunk", "result", "fnUntyped", "concat", "apply", "setTyped", "on", "Buf8", "Buf16", "Buf32", "zlib_inflate", "require", "utils", "strings", "zstream", "gzheader", "toString", "Object", "Inflate", "options", "chunkSize", "windowBits", "to", "opt", "raw", "err", "ended", "strm", "status", "inflateInit2", "Z_OK", "Error", "header", "inflateGetHeader", "inflate", "inflator", "push", "data", "mode", "_mode", "next_out_utf8", "tail", "utf8str", "Z_FINISH", "Z_NO_FLUSH", "binstring2buf", "Z_STREAM_END", "onEnd", "Z_SYNC_FLUSH", "utf8border", "buf2string", "onData", "inflateEnd", "join", "inflateRaw", "ungzip", "zlib_deflate", "Deflate", "level", "method", "memLevel", "strategy", "gzip", "deflateInit2", "deflateSetHeader", "deflate", "deflator", "string2buf", "buf2binstring", "deflateEnd", "deflateRaw", "trees", "adler32", "crc32", "Z_STREAM_ERROR", "MAX_MATCH", "MIN_LOOKAHEAD", "HCRC_STATE", "BUSY_STATE", "FINISH_STATE", "errorCode", "rank", "f", "zero", "flush_pending", "s", "pending", "pending_buf", "pending_out", "flush_block_only", "last", "_tr_flush_block", "block_start", "strstart", "put_byte", "b", "putShortMSB", "read_buf", "start", "wrap", "longest_match", "cur_match", "match", "chain_length", "max_chain_length", "scan", "best_len", "prev_length", "nice_match", "limit", "w_size", "_win", "window", "wmask", "w_mask", "prev", "strend", "scan_end1", "scan_end", "good_match", "<PERSON><PERSON><PERSON>", "match_start", "fill_window", "m", "more", "str", "_w_size", "window_size", "hash_size", "head", "insert", "ins_h", "hash_shift", "hash_mask", "deflate_fast", "flush", "hash_head", "bflush", "match_length", "_tr_tally", "max_lazy_match", "MIN_MATCH", "last_lit", "deflate_slow", "max_insert", "prev_match", "match_available", "configuration_table", "Config", "good_length", "max_lazy", "nice_length", "max_chain", "func", "DeflateState", "pending_buf_size", "gzhead", "gzindex", "last_flush", "w_bits", "hash_bits", "dyn_ltree", "HEAP_SIZE", "dyn_dtree", "bl_tree", "l_desc", "d_desc", "bl_desc", "bl_count", "MAX_BITS", "heap", "heap_len", "heap_max", "depth", "l_buf", "lit_bufsize", "d_buf", "opt_len", "static_len", "matches", "bi_buf", "bi_valid", "deflateResetKeep", "_tr_init", "deflateReset", "ret", "max_block_size", "max_start", "deflateInit", "old_flush", "beg", "val", "text", "hcrc", "extra", "name", "comment", "time", "os", "charCodeAt", "bstate", "deflate_huff", "deflate_rle", "_tr_align", "_tr_stored_block", "deflateInfo", "_in", "_out", "dmax", "wsize", "whave", "wnext", "hold", "bits", "lcode", "dcode", "lmask", "dmask", "here", "op", "dist", "from", "from_source", "lencode", "distcode", "lenbits", "distbits", "top", "dolen", "dodist", "sane", "inflate_fast", "inflate_table", "TYPE", "BAD", "ZSWAP32", "q", "InflateState", "havedict", "flags", "check", "total", "wbits", "offset", "ncode", "nlen", "ndist", "have", "next", "lens", "work", "<PERSON><PERSON>", "distdyn", "back", "was", "inflateResetKeep", "inflateReset", "inflateReset2", "lenfix", "distfix", "virgin", "fixedtables", "sym", "inflateInit", "put", "left", "copy", "here_bits", "here_op", "here_val", "last_bits", "last_op", "last_val", "opts", "hbuf", "order", "inf_leave", "done", "xflags", "extra_len", "String", "fromCharCode", "updatewindow", "inflateInfo", "pako", "Z_PARTIAL_FLUSH", "Z_FULL_FLUSH", "Z_BLOCK", "Z_TREES", "Z_NEED_DICT", "Z_ERRNO", "Z_DATA_ERROR", "Z_BUF_ERROR", "Z_NO_COMPRESSION", "Z_BEST_SPEED", "Z_BEST_COMPRESSION", "Z_DEFAULT_COMPRESSION", "Z_FILTERED", "Z_HUFFMAN_ONLY", "Z_RLE", "Z_FIXED", "Z_DEFAULT_STRATEGY", "Z_BINARY", "Z_TEXT", "Z_UNKNOWN", "Z_DEFLATED", "STR_APPLY_OK", "STR_APPLY_UIA_OK", "__", "_utf8len", "c2", "m_pos", "str_len", "buf_len", "max", "out", "c_len", "utf16buf", "MAXBITS", "lbase", "lext", "dbase", "dext", "type", "lens_index", "codes", "table_index", "incr", "fill", "low", "mask", "min", "root", "curr", "drop", "used", "huff", "base", "base_index", "count", "offs", "extra_index", "LITERALS", "L_CODES", "D_CODES", "extra_lbits", "extra_dbits", "extra_blbits", "bl_order", "static_ltree", "static_dtree", "_dist_code", "_length_code", "base_length", "base_dist", "static_l_desc", "static_d_desc", "static_bl_desc", "StaticTreeDesc", "static_tree", "extra_bits", "extra_base", "elems", "max_length", "has_stree", "TreeDesc", "dyn_tree", "stat_desc", "max_code", "d_code", "put_short", "w", "send_bits", "value", "send_code", "tree", "bi_reverse", "code", "res", "gen_codes", "next_code", "init_block", "END_BLOCK", "bi_windup", "smaller", "_n2", "_m2", "pqdownheap", "v", "j", "compress_block", "ltree", "dtree", "lc", "lx", "build_tree", "desc", "node", "stree", "h", "xbits", "overflow", "gen_bitlen", "scan_tree", "curlen", "prevlen", "nextlen", "max_count", "min_count", "REP_3_6", "REPZ_3_10", "REPZ_11_138", "send_tree", "static_init_done", "stored_len", "copy_block", "LENGTH_CODES", "tr_static_init", "opt_lenb", "static_lenb", "max_blindex", "black_mask", "detect_data_type", "BL_CODES", "build_bl_tree", "lcodes", "dcodes", "blcodes", "send_all_trees", "STATIC_TREES", "bi_flush"], "sourceRoot": ""}