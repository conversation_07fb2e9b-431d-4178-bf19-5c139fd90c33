{"version": 3, "file": "static/css/21.0332ebe4.chunk.css", "mappings": "AAAA,2BAGE,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CADhB,UAIF,CCLA,eAGE,MAAO,CAFP,eAAgB,CAChB,uBAEF,CAEA,mBACE,aAAc,CACd,aAAc,CACd,UACF,CAEA,8BAEE,sBAAuB,CADvB,gBAEF,CAEA,2BACE,eAAgB,CAChB,wBACF,CAEA,0DAGE,eAAgB,CADhB,eAAgB,CAEhB,sBACF,CAEA,4BAEE,eAAgB,CADhB,UAEF,CAEA,qCACE,oCAA2C,CAC3C,UACF,CAOA,oEAJE,wBAA2C,CAC3C,UAMF,CAGA,eAGE,kBAAmB,CADnB,cAAe,CADf,UAGF,CACA,oCAGE,+BAAgC,CADhC,gBAEF,CACA,kBACE,qBAAsB,CACtB,eAAgB,CAEhB,eACF,CACA,uFAHE,wBAKF", "sources": ["common/components/RequestStateWrapper.css", "experiment-tracking/components/CompareRunView.css"], "sourcesContent": [".RequestStateWrapper-error {\n  width: auto;\n  margin-top: 50px;\n  margin-left: auto;\n  margin-right: auto;\n}\n", ".sticky-header {\n  position: sticky;\n  position: -webkit-sticky;\n  left: 0;\n}\n\n.compare-run-table {\n  display: block;\n  overflow: auto;\n  width: 100%;\n}\n\n.compare-table th.inter-title {\n  padding: 20px 0 0;\n  background: transparent;\n}\n\n.compare-table .head-value {\n  overflow: hidden;\n  overflow-wrap: break-word;\n}\n\n.compare-table td.data-value,\n.compare-table th.data-value {\n  overflow: hidden;\n  max-width: 120px;\n  text-overflow: ellipsis;\n}\n\n.responsive-table-container {\n  width: 100%;\n  overflow-x: auto;\n}\n\n.compare-table .diff-row .data-value {\n  background-color: rgba(249, 237, 190, 0.5) ;\n  color: #555;\n}\n\n.compare-table .diff-row .head-value {\n  background-color: rgba(249, 237, 190, 1.0) ;\n  color: #555;\n}\n\n.compare-table .diff-row:hover {\n  background-color: rgba(249, 237, 190, 1.0) ;\n  color: #555;\n}\n\n/* Overrides to make it look more like antd */\n.compare-table {\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 20px;\n}\n.compare-table th,\n.compare-table td {\n  padding: 12px 8px;\n  border-bottom: 1px solid #e8e8e8;\n}\n.compare-table th {\n  color: rgba(0,0,0,.85);\n  font-weight: 500;\n  background-color:rgb(250, 250, 250);\n  text-align: left;\n}\n.compare-table > tbody > tr:hover:not(.diff-row) > td:not(.highlight-data) {\n  background-color: rgb(250, 250, 250);\n}\n"], "names": [], "sourceRoot": ""}