(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[8245],{23237:function(){},31815:function(){},82787:function(){},97492:function(){},97540:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return g}});var r=a(31014),s=a(83314),n=a(48012),o=a(32599),i=a(77484),d=a(76010),c=a(52350),l=a(8220),u=a(57596),h=a(69650),p=a(50111);s.Uy.GlobalWorkerOptions.workerSrc="./static-files/pdf.worker.js";class f extends r.Component{constructor(){super(...arguments),this.state={loading:!0,error:void 0,pdfData:void 0,currentPage:1,numPages:1},this.onDocumentLoadSuccess=e=>{let{numPages:t}=e;this.setState({numPages:t})},this.onDocumentLoadError=e=>{d.A.logErrorAndNotifyUser(new c.s(e))},this.onPageChange=(e,t)=>{this.setState({currentPage:e})},this.renderPdf=()=>(0,p.Y)(r.Fragment,{children:(0,p.FD)("div",{className:"pdf-viewer",children:[(0,p.Y)("div",{className:"paginator",children:(0,p.Y)(n.dKS,{simple:!0,currentPageIndex:this.state.currentPage,numTotal:this.state.numPages,pageSize:1,onChange:this.onPageChange,dangerouslySetAntdProps:{simple:!0}})}),(0,p.Y)("div",{className:"document",children:(0,p.Y)(s.yo,{file:this.state.pdfData,onLoadSuccess:this.onDocumentLoadSuccess,onLoadError:this.onDocumentLoadError,loading:(0,p.Y)(o.S,{}),children:(0,p.Y)(s.YW,{pageNumber:this.state.currentPage,loading:(0,p.Y)(o.S,{})})})})]})})}fetchPdf(){var e,t;const{path:a,runUuid:r,isLoggedModelsMode:s,loggedModelId:n,experimentId:o}=this.props;null===(e=(t=this.props).getArtifact)||void 0===e||e.call(t,{path:a,runUuid:r,isLoggedModelsMode:s,loggedModelId:n,experimentId:o},i.xC).then((e=>{this.setState({pdfData:{data:e},loading:!1})})).catch((e=>{this.setState({error:e,loading:!1})}))}componentDidMount(){this.fetchPdf()}componentDidUpdate(e){this.props.path===e.path&&this.props.runUuid===e.runUuid||this.fetchPdf()}render(){return this.state.loading?(0,p.Y)(l.$,{className:"artifact-pdf-view-loading"}):this.state.error?(0,p.Y)(u.F,{className:"artifact-pdf-view-error"}):(0,p.Y)("div",{className:"pdf-outer-container",children:this.renderPdf()})}}f.defaultProps={getArtifact:h.F};var g=f}}]);
//# sourceMappingURL=8245.96b6bfe5.chunk.js.map