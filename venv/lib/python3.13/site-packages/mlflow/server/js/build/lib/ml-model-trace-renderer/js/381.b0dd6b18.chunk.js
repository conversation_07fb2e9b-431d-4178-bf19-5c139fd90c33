"use strict";(self.webpackChunkdatabricks_mlModelTraceRenderer=self.webpackChunkdatabricks_mlModelTraceRenderer||[]).push([[381],{8161:(e,t,r)=>{r.d(t,{Ie:()=>u,NI:()=>s,PS:()=>function e(t,r){if(n().isValidElement(t))return n().cloneElement(t,r);if(Array.isArray(t))return t.map(t=>e(t,r));return t},WF:()=>y,ei:()=>m,s7:()=>c,v_:()=>l,z8:()=>h,zI:()=>v});var o=r(65848),n=r.n(o),a=r(47088),i=r(98358);let l=function(e){return e.Accordion="accordion",e.<PERSON>ert="alert",e.<PERSON>="banner",e.But<PERSON>="button",e.Card="card",e.Checkbox="checkbox",e.ContextMenuCheckboxItem="context_menu_checkbox_item",e.ContextMenuItem="context_menu_item",e.ContextMenuRadioGroup="context_menu_radio_group",e.DialogCombobox="dialog_combobox",e.Drawer="drawer_content",e.DropdownMenuCheckboxItem="dropdown_menu_checkbox_item",e.DropdownMenuItem="dropdown_menu_item",e.DropdownMenuRadioGroup="dropdown_menu_radio_group",e.Form="form",e.Input="input",e.LegacySelect="legacy_select",e.Modal="modal",e.Notification="notification",e.Pagination="pagination",e.PillControl="pill_control",e.Popover="popover",e.PreviewCard="preview_card",e.Radio="radio",e.RadioGroup="radio_group",e.SegmentedControlGroup="segmented_control_group",e.SimpleSelect="simple_select",e.Switch="switch",e.TableHeader="table_header",e.Tabs="tabs",e.Tag="tag",e.TextArea="text_area",e.ToggleButton="toggle_button",e.Tooltip="tooltip",e.TypeaheadCombobox="typeahead_combobox",e.TypographyLink="typography_link",e}({}),c=function(e){return e.OnClick="onClick",e.OnSubmit="onSubmit",e.OnValueChange="onValueChange",e.OnView="onView",e}({}),s=function(e){return e.Success="success",e.Error="error",e.Warning="warning",e.Info="info",e.InfoLightPurple="info_light_purple",e.InfoDarkPurple="info_dark_purple",e}({}),u={success:s.Success,error:s.Error,warning:s.Warning,info:s.Info,info_light_purple:s.InfoLightPurple,info_dark_purple:s.InfoDarkPurple},d=(e,t)=>t.includes(e),g={callback:()=>{}},p=n().createContext(g),f=()=>(0,o.useContext)(p),h=(0,o.createContext)({dataComponentProps:{}}),y=e=>{let t=(0,o.useContext)(h);return e===t.dataComponentProps["data-component-type"]?t.dataComponentProps:{}},m=e=>{let{componentType:t,componentId:r,componentSubType:n,analyticsEvents:i,valueHasNoPii:l,shouldStartInteraction:s,isInteractionSubject:u}=e,p=f(),h=(0,a.dU)(),y=(0,o.useMemo)(()=>s||void 0===s&&!h.suppressAnalyticsStartInteraction,[h,s]);return(0,o.useMemo)(()=>{let e=r?{"data-component-id":r,"data-component-type":t}:{};if(p===g||void 0===r)return{onClick:()=>{},onSubmit:()=>{},onValueChange:()=>{},onView:()=>{},dataComponentProps:e};return{onClick:e=>{d(c.OnClick,i)&&p.callback({eventType:c.OnClick,componentType:t,componentId:r,componentSubType:n,value:void 0,shouldStartInteraction:y,event:e,isInteractionSubject:u})},onSubmit:e=>{let o=d(c.OnSubmit,i)?"default":"associate_event_only";p.callback({eventType:c.OnSubmit,componentType:t,componentId:r,componentSubType:n,value:void 0,shouldStartInteraction:y,event:e.event,mode:o,referrerComponent:e.referrerComponent,formPropertyValues:{initial:e.initialState,final:e.finalState}})},onValueChange:e=>{d(c.OnValueChange,i)&&p.callback({eventType:c.OnValueChange,componentType:t,componentId:r,componentSubType:n,value:l?e:void 0,shouldStartInteraction:!1})},onView:e=>{d(c.OnView,i)&&p.callback({eventType:c.OnView,componentType:t,componentId:r,componentSubType:n,value:l?e:void 0,shouldStartInteraction:!1})},dataComponentProps:e}},[p,r,i,t,n,y,u,l])};function v(e){let{children:t,callback:r}=e,n=(0,o.useMemo)(()=>({callback:r}),[r]);return(0,i.Y)(p.Provider,{value:n,children:t})}},47088:(e,t,r)=>{r.d(t,{dU:()=>l,pz:()=>a,vR:()=>i});var o=r(65848),n=r.n(o);let a={suppressAnalyticsStartInteraction:!0},i=n().createContext({suppressAnalyticsStartInteraction:!1}),l=()=>(0,o.useContext)(i)},83221:(e,t,r)=>{r.d(t,{Js:()=>d,_G:()=>n,aV:()=>u,ed:()=>c,kQ:()=>s,oS:()=>i,pY:()=>p,v8:()=>a,vu:()=>g,x9:()=>o});let o=(e,t)=>{let r=e.spacing.xs+e.spacing.sm,o=e.spacing.md,n=e.spacing.sm;if("string"==typeof t)return`calc(${t} - ${r+o+n}px)`;return t-r+o+n};function n(e){return e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"start",r=arguments.length>2?arguments[2]:void 0;if(n(e)){let o=i(e,"end"===t?"previous":"next");o&&c(o,r)}else c(e,r)}function i(e,t){let r="previous"===t?e.previousElementSibling:e.nextElementSibling;if((null==r?void 0:r.getAttribute("role"))==="option"){if(n(r))return i(r,t);return r}if(r){let e=r;for(;e&&("option"!==e.getAttribute("role")||n(e));)e="previous"===t?e.previousElementSibling:e.nextElementSibling;return e}return null}let l=e=>{let t=u(e);null==t||t.forEach(e=>{e.setAttribute("tabIndex","-1"),e.setAttribute("data-highlighted","false")})},c=function(e,t){var r;let o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];t&&(t.setAttribute("tabIndex","-1"),t.setAttribute("data-highlighted","false")),o&&e.focus(),e.setAttribute("tabIndex","0"),e.setAttribute("data-highlighted","true"),null===(r=e.scrollIntoView)||void 0===r||r.call(e,{block:"center"})},s=e=>{var t;return null!==(t=e.find(e=>"true"===e.getAttribute("data-highlighted")))&&void 0!==t?t:void 0},u=e=>{var t;let r=null===(t=e.closest('[data-combobox-option-list="true"]'))||void 0===t?void 0:t.querySelectorAll('[role="option"]');return r?Array.from(r):void 0},d=(e,t)=>{let{onKeyDown:r,onMouseEnter:o,onDefaultKeyDown:n,disableMouseOver:l,setDisableMouseOver:s}=t;return{onKeyDown:t=>{var o,l;switch(null==r||r(t),t.key){case"ArrowDown":t.preventDefault(),s(!0);let d=i(t.currentTarget,"next");if(d)c(d,t.currentTarget);else{let e=null===(o=u(t.currentTarget))||void 0===o?void 0:o[0];e&&a(e,"start",t.currentTarget)}break;case"ArrowUp":t.preventDefault(),s(!0);let g=i(t.currentTarget,"previous");if(g)c(g,t.currentTarget);else{let e=null===(l=u(t.currentTarget))||void 0===l?void 0:l.slice(-1)[0];e&&a(e,"end",t.currentTarget)}break;case"Enter":t.preventDefault(),e(t);break;default:null==n||n(t)}},onMouseMove:e=>{l&&s(!1)},onMouseEnter:e=>{l||(null==o||o(e),g(e.currentTarget))}}},g=e=>{l(e),e.setAttribute("tabIndex","0"),e.focus()},p=(e,t,r)=>{var o,n;if("Escape"===e.key||"Tab"===e.key||"Enter"===e.key)return;e.preventDefault();let a=Array.from(null!==(o=null===(n=e.currentTarget.parentElement)||void 0===n?void 0:n.children)&&void 0!==o?o:[]),i=a.findIndex(t=>{var o,n;return(null!==(o=null===(n=t.textContent)||void 0===n?void 0:n.toLowerCase())&&void 0!==o?o:"").startsWith(r+e.key)});if(-1!==i){let o=a[i];o.focus(),t&&t(r+e.key),g(o)}}},70821:(e,t,r)=>{r.d(t,{S:()=>g});var o=r(71218);r(65848);var n=r(80838),a=r(86648),i=r(3078),l=r(94167),c=r(98358);let{Title:s,Paragraph:u}=i.o;var d={name:"zl1inp",styles:"display:flex;justify-content:center"};let g=e=>{let{theme:t,classNamePrefix:r}=(0,n.wn)(),{title:i,description:g,image:p=(0,c.Y)(a.A,{}),button:f,dangerouslyAppendEmotionCSS:h,...y}=e;return(0,c.Y)("div",{...y,...(0,l.VG)(),css:d,children:(0,c.FD)("div",{css:[function(e){let t={display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",maxWidth:600,wordBreak:"break-word",'> [role="img"]':{fontSize:64,color:e.colors.actionDisabledText,marginBottom:e.spacing.md}};return(0,o.AH)(t,"","")}(t),h,"",""],children:[p,i&&(0,c.Y)(s,{level:3,css:function(e,t){let r={[`&.${t}-typography`]:{color:e.colors.textSecondary,marginTop:0,marginBottom:0}};return(0,o.AH)(r,"","")}(t,r),children:i}),(0,c.Y)(u,{css:function(e,t){let r={[`&.${t}-typography`]:{color:e.colors.textSecondary,marginBottom:e.spacing.md}};return(0,o.AH)(r,"","")}(t,r),children:g}),f]})})}},96277:(e,t,r)=>{r.d(t,{I:()=>p});var o=r(71218),n=r(15232),a=r(65848),i=r(39659),l=r(80838),c=r(63434),s=r(94167),u=r(60499),d=r(98358);let g=(e,t)=>{switch(t){case"success":return{color:e.colors.textValidationSuccess};case"warning":return{color:e.colors.textValidationWarning};case"danger":return{color:e.colors.textValidationDanger};case"ai":return{"svg *":{fill:"var(--ai-icon-gradient)"}};default:return{color:t}}},p=(0,a.forwardRef)((e,t)=>{let{component:r,dangerouslySetAntdProps:p,color:f,style:h,...y}=e,{theme:m}=(0,l.wn)(),v=(0,u.Y)("ai-linear-gradient"),b=(0,a.useMemo)(()=>r?e=>{let{fill:t,...o}=e;return(0,d.FD)(d.FK,{children:[(0,d.Y)(r,{fill:"none",...o,style:"ai"===f?{"--ai-icon-gradient":`url(#${v})`,...o.style}:o.style}),"ai"===f&&(0,d.Y)("svg",{width:"0",height:"0",viewBox:"0 0 0 0",css:c.Q,children:(0,d.Y)("defs",{children:(0,d.FD)("linearGradient",{id:v,x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,d.Y)("stop",{offset:"20.5%",stopColor:m.colors.branded.ai.gradientStart}),(0,d.Y)("stop",{offset:"46.91%",stopColor:m.colors.branded.ai.gradientMid}),(0,d.Y)("stop",{offset:"79.5%",stopColor:m.colors.branded.ai.gradientEnd})]})})})]})}:void 0,[r,f,v,m]);return(0,d.Y)(i.wC,{children:(0,d.Y)(n.A,{...(0,s.VG)(),ref:t,"aria-hidden":"true",css:(0,o.AH)({fontSize:m.general.iconFontSize,...g(m,f)},"",""),component:b,style:{...h},...y,...p})})})},89481:(e,t,r)=>{r.d(t,{A:()=>c});var o=r(65848),n=r(96277),a=r(98358);function i(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M7.248 10.748a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0M8.748 4.998v4h-1.5v-4z"}),(0,a.Y)("path",{fill:"currentColor",fillRule:"evenodd",d:"m11.533 15.776 4.243-4.243a.75.75 0 0 0 .22-.53v-6.01a.75.75 0 0 0-.22-.53L11.533.22a.75.75 0 0 0-.53-.22h-6.01a.75.75 0 0 0-.53.22L.22 4.462a.75.75 0 0 0-.22.53v6.011c0 .*************.53l4.242 4.243c.141.14.332.22.53.22h6.011a.75.75 0 0 0 .53-.22m2.963-10.473v5.39l-3.804 3.803H5.303L1.5 10.692V5.303L5.303 1.5h5.39z",clipRule:"evenodd"})]})}let l=(0,o.forwardRef)((e,t)=>(0,a.Y)(n.I,{ref:t,...e,component:i}));l.displayName="DangerIcon";let c=l},86648:(e,t,r)=>{r.d(t,{A:()=>c});var o=r(65848),n=r(96277),a=r(98358);function i(e){return(0,a.Y)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:(0,a.Y)("path",{fill:"currentColor",d:"M1.5 2.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0M3 2h13v1.5H3zM3 5.5h13V7H3zM3 9h13v1.5H3zM3 12.5h13V14H3zM.75 7a.75.75 0 1 0 0-********* 0 0 0 0 1.5M1.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0M.75 10.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5"})})}let l=(0,o.forwardRef)((e,t)=>(0,a.Y)(n.I,{ref:t,...e,component:i}));l.displayName="ListIcon";let c=l},10954:(e,t,r)=>{r.d(t,{A:()=>c});var o=r(65848),n=r(96277),a=r(98358);function i(e){return(0,a.FD)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 16 16",...e,children:[(0,a.Y)("path",{fill:"currentColor",d:"M10 1h5v5h-1.5V3.56L8.53 8.53 7.47 7.47l4.97-4.97H10z"}),(0,a.Y)("path",{fill:"currentColor",d:"M1 2.75A.75.75 0 0 1 1.75 2H8v1.5H2.5v10h10V8H14v6.25a.75.75 0 0 1-.75.75H1.75a.75.75 0 0 1-.75-.75z"})]})}let l=(0,o.forwardRef)((e,t)=>(0,a.Y)(n.I,{ref:t,...e,component:i}));l.displayName="NewWindowIcon";let c=l},99342:(e,t,r)=>{r.d(t,{h:()=>d});var o=r(71218),n=r(80646),a=r(39659),i=r(80838),l=r(69391),c=r(94167),s=r(98358);let{Title:u}=n.A;function d(e){let{dangerouslySetAntdProps:t,withoutMargins:r,color:n,elementLevel:d,...g}=e,{theme:p}=(0,i.wn)();return(0,s.Y)(a.wC,{children:(0,s.Y)(u,{...(0,c.VG)(),...g,level:null!=d?d:g.level,className:g.className,css:(0,o.AH)(function(e,t){switch(t.level){case 1:return(0,o.AH)({"&&":{fontSize:e.typography.fontSizeXxl,lineHeight:e.typography.lineHeightXxl,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightXxl}},"","");case 2:return(0,o.AH)({"&&":{fontSize:e.typography.fontSizeXl,lineHeight:e.typography.lineHeightXl,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightXl}},"","");case 3:return(0,o.AH)({"&&":{fontSize:e.typography.fontSizeLg,lineHeight:e.typography.lineHeightLg,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightLg}},"","");default:return(0,o.AH)({"&&":{fontSize:e.typography.fontSizeMd,lineHeight:e.typography.lineHeightMd,fontWeight:e.typography.typographyBoldFontWeight},"& > .anticon":{lineHeight:e.typography.lineHeightMd}},"","")}}(p,e),{"&&":{color:(0,l.X1)(p,e.color,p.colors.textPrimary)},"& > .anticon":{verticalAlign:"middle"}},e.withoutMargins&&{"&&":{marginTop:"0 !important",marginBottom:"0 !important"}},"",""),...t})})}},3078:(e,t,r)=>{r.d(t,{o:()=>z});var o=r(80646),n=r(71218),a=r(39659),i=r(80838),l=r(94167),c=r(98358);let{Text:s}=o.A;function u(e){let{dangerouslySetAntdProps:t,bold:r,withoutMargins:o,color:u,...d}=e,{theme:g}=(0,i.wn)();return(0,c.Y)(a.wC,{children:(0,c.Y)(s,{...(0,l.VG)(),...d,css:(0,n.AH)({"&&":{display:"block",fontSize:g.typography.fontSizeSm,lineHeight:g.typography.lineHeightSm,color:g.colors.textSecondary,...e.withoutMargins&&{"&&":{marginTop:0,marginBottom:0}}}},"",""),...t})})}var d=r(81434),g=r(65848),p=r(8161),f=r(10954),h=r(80842),y=r(58829);let m=(e,t)=>{let r=`.${t}-typography`,o={[`&${r}, &${r}:focus`]:{color:e.colors.actionTertiaryTextDefault},[`&${r}:hover, &${r}:hover .anticon`]:{color:e.colors.actionTertiaryTextHover,textDecoration:"underline"},[`&${r}:active, &${r}:active .anticon`]:{color:e.colors.actionTertiaryTextPress,textDecoration:"underline"},[`&${r}:focus-visible`]:{textDecoration:"underline"},".anticon":{fontSize:12,verticalAlign:"baseline"},[`.${t}-tooltip-inner a&${r}`]:{"&, :focus":{color:e.colors.blue500,".anticon":{color:e.colors.blue500}},":active":{color:e.colors.blue500,".anticon":{color:e.colors.blue500}},":hover":{color:e.colors.blue400,".anticon":{color:e.colors.blue400}}}};return(0,n.AH)(o,"","")},v=()=>(0,n.AH)({paddingRight:"calc(2px + 1em)",position:"relative"},"",""),b=e=>{let t={marginLeft:4,color:e.colors.actionTertiaryTextDefault,position:"relative",top:"1px"};return(0,n.AH)(t,"","")},x=e=>{let t={position:"absolute",right:0,bottom:0,top:0,display:"flex",alignItems:"center",...e&&{fontSize:12}};return(0,n.AH)(t,"","")},k=(0,g.forwardRef)(function(e,t){let{dangerouslySetAntdProps:r,componentId:n,analyticsEvents:s,onClick:u,...k}=e,D=(0,y.W)("databricks.fe.observability.defaultComponentView.typographyLink",!1),{children:w,openInNewTab:A,...S}=k,{theme:T,classNamePrefix:H}=(0,i.wn)(),B=(0,g.useMemo)(()=>null!=s?s:D?[p.s7.OnClick,p.s7.OnView]:[p.s7.OnClick],[s,D]),P=(0,p.ei)({componentType:p.v_.TypographyLink,componentId:n,analyticsEvents:B,shouldStartInteraction:!1}),{elementRef:I}=(0,h.z)({onView:P.onView}),$=(0,d.SV)([t,I]),z=(0,g.useCallback)(e=>{P.onClick(e),null==u||u(e)},[P,u]),C={rel:"noopener noreferrer",target:"_blank"},V=A?{...S,...C}:{...S},_=k.ellipsis&&A?[m(T,H),v()]:m(T,H),M=k.ellipsis?[b(T),x()]:b(T);return(0,c.Y)(a.wC,{children:(0,c.FD)(o.A.Link,{...(0,l.VG)(),"aria-disabled":V.disabled,css:_,ref:$,onClick:z,...V,...r,...P.dataComponentProps,children:[w,A?(0,c.Y)(f.A,{css:M,...C}):null]})})});var D=r(69391);let{Paragraph:w}=o.A;function A(e){let{dangerouslySetAntdProps:t,withoutMargins:r,color:o,...s}=e,{theme:u,classNamePrefix:d}=(0,i.wn)();return(0,c.Y)(a.wC,{children:(0,c.Y)(w,{...(0,l.VG)(),...s,className:s.className,css:(0,n.AH)({"&&":{fontSize:u.typography.fontSizeBase,fontWeight:u.typography.typographyRegularFontWeight,lineHeight:u.typography.lineHeightBase,color:(0,D.X1)(u,e.color,u.colors.textPrimary)},"& .anticon":{verticalAlign:"text-bottom"},[`& .${d}-btn-link`]:{verticalAlign:"baseline !important"}},e.disabled&&{"&&":{color:u.colors.actionDisabledText}},e.withoutMargins&&{"&&":{marginTop:0,marginBottom:0}},"",""),...t})})}let{Text:S}=o.A;function T(e){let{dangerouslySetAntdProps:t,bold:r,hint:o,withoutMargins:s,color:u,...d}=e,{theme:g}=(0,i.wn)();return(0,c.Y)(a.wC,{children:(0,c.Y)(S,{...(0,l.VG)(),...d,className:d.className,css:(0,n.AH)({"&&":{fontSize:g.typography.fontSizeBase,fontWeight:g.typography.typographyRegularFontWeight,lineHeight:g.typography.lineHeightBase,color:(0,D.X1)(g,e.color,g.colors.textPrimary)}},e.disabled&&{"&&":{color:g.colors.actionDisabledText}},e.hint&&{"&&":{fontSize:g.typography.fontSizeSm,lineHeight:g.typography.lineHeightSm}},e.bold&&{"&&":{fontSize:g.typography.fontSizeBase,fontWeight:g.typography.typographyBoldFontWeight,lineHeight:g.typography.lineHeightBase}},e.code&&{"&& > code":{color:g.colors.textPrimary,fontSize:g.typography.fontSizeBase,lineHeight:g.typography.lineHeightBase,background:g.colors.typographyCodeBg,fontFamily:"monospace",borderRadius:g.borders.borderRadiusSm,padding:"2px 4px",border:"unset",margin:0}},e.size&&{"&&":(()=>{switch(e.size){case"xxl":return{fontSize:g.typography.fontSizeXxl,lineHeight:g.typography.lineHeightXxl,"& .anticon":{lineHeight:g.typography.lineHeightXxl,verticalAlign:"middle"}};case"xl":return{fontSize:g.typography.fontSizeXl,lineHeight:g.typography.lineHeightXl,"& .anticon":{lineHeight:g.typography.lineHeightXl,verticalAlign:"middle"}};case"lg":return{fontSize:g.typography.fontSizeLg,lineHeight:g.typography.lineHeightLg,"& .anticon":{lineHeight:g.typography.lineHeightLg,verticalAlign:"middle"}};case"sm":return{fontSize:g.typography.fontSizeSm,lineHeight:g.typography.lineHeightSm,"& .anticon":{verticalAlign:"-0.219em"}};default:return{}}})()},e.withoutMargins&&{"&&":{marginTop:0,marginBottom:0}},"",""),...t})})}var H=r(25656),B=r(64229);function P(e){var t,r;let{text:o,suffixLength:n=6,...a}=e,i=(0,g.useMemo)(()=>(0,H.uniqueId)("text-middle-elided-"),[]),{start:l,suffix:s}=function(e,t){if(e.length<=t)return{start:e,suffix:void 0};return{start:e.slice(0,e.length-t).trim(),suffix:e.slice(-t).trim()}}(o,n),u=(null===(t=B)||void 0===B||null===(t=t.env)||void 0===t?void 0:"production")==="test";return(0,g.useEffect)(()=>{var e;let t=e=>{var t;null==e||e.preventDefault(),null==e||null===(t=e.clipboardData)||void 0===t||t.setData("text/plain",o)},r=`.${i}`;return null===(e=document.querySelector(r))||void 0===e||e.addEventListener("copy",t),()=>{var e;null===(e=document.querySelector(r))||void 0===e||e.removeEventListener("copy",t)}},[i,o]),(0,c.Y)(T,{ellipsis:u?void 0:{suffix:s},...a,"aria-label":o,title:null!==(r=a.title)&&void 0!==r?r:o,className:i,children:u?o:l})}var I=r(99342);let $=e=>{let{children:t,lines:r=1,...o}=e,[a,i]=(0,g.useState)(""),l=(0,g.useRef)(null);return(0,g.useEffect)(()=>{l.current&&i(l.current.textContent||"")},[l,t]),(0,c.Y)("span",{ref:l,title:a,css:(0,n.AH)({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"normal",wordBreak:"break-word",display:"-webkit-box",webkitLineClamp:r,WebkitBoxOrient:"vertical",WebkitLineClamp:r},"",""),...o,children:t})},z=(()=>{function e(e){let{dangerouslySetAntdProps:t,...r}=e;return(0,c.Y)(a.wC,{children:(0,c.Y)(o.A,{...(0,l.VG)(),...r,...t})})}return e.Text=T,e.Title=I.h,e.Paragraph=A,e.Link=k,e.Hint=u,e.Truncate=$,e.TextMiddleElide=P,e})()},37590:(e,t,r)=>{r.d(t,{DQ:()=>s,GE:()=>l,Kb:()=>i,RB:()=>c,UA:()=>g,WS:()=>d,eO:()=>u,rj:()=>p,y3:()=>f});var o=r(71218),n=r(83221),a=r(69391);let i=(e,t)=>{let{maxHeight:r="100vh",maxWidth:n="100vw",minHeight:i=0,minWidth:l=0,width:c,useNewShadows:s}=t;return(0,o.AH)({maxHeight:r,maxWidth:n,minHeight:i,minWidth:l,...c?{width:c}:{},background:e.colors.backgroundPrimary,color:e.colors.textPrimary,overflow:"auto",zIndex:e.options.zIndexBase+10,boxSizing:"border-box",border:`1px solid ${e.colors.border}`,boxShadow:s?e.shadows.lg:e.general.shadowLow,borderRadius:e.borders.borderRadiusSm,colorScheme:e.isDarkMode?"dark":"light",...(0,a.WO)(e,s)},"","")},l=[6,32,6,12],c=e=>(0,o.AH)((0,a.dg)({display:"flex",flexDirection:"row",alignItems:"flex-start",justifyContent:"flex-start",alignSelf:"stretch",padding:`${l.map(e=>`${e}px`).join(" ")}`,lineHeight:e.typography.lineHeightBase,boxSizing:"content-box",cursor:"pointer",userSelect:"none",'&[data-highlighted="true"]':{background:e.colors.actionTertiaryBackgroundHover},"&:focus":{background:e.colors.actionTertiaryBackgroundHover,outline:"none"},"&[disabled]":{pointerEvents:"none",color:e.colors.actionDisabledText,background:e.colors.backgroundPrimary}}),"",""),s=e=>{let{theme:t,dangerouslyHideCheck:r,textOverflowMode:a,contentWidth:i,hasHintColumn:l}=e;return(0,o.AH)({marginLeft:r?0:t.spacing.sm,fontSize:t.typography.fontSizeBase,fontStyle:"normal",fontWeight:400,cursor:"pointer",overflow:"hidden",wordBreak:"break-word",..."ellipsis"===a&&{textOverflow:"ellipsis",whiteSpace:"nowrap"},...i?{width:(0,n.x9)(t,i)}:{},...l&&{display:"flex"}},"","")},u=e=>(0,o.AH)({paddingLeft:e.spacing.xs,color:e.colors.textSecondary,pointerEvents:"all",cursor:"pointer",verticalAlign:"middle"},"",""),d=(e,t)=>(0,o.AH)({pointerEvents:"none",height:"unset",width:"100%","& > label":{display:"flex",width:"100%",fontSize:e.typography.fontSizeBase,fontStyle:"normal",fontWeight:400,cursor:"pointer","& > span:first-of-type":{alignSelf:"flex-start",display:"inline-flex",alignItems:"center",paddingTop:e.spacing.xs/2},"& > span:last-of-type, & > span:last-of-type > label":{paddingRight:0,width:"100%",overflow:"hidden",wordBreak:"break-word",..."ellipsis"===t?{textOverflow:"ellipsis",whiteSpace:"nowrap"}:{}}}},"",""),g=e=>(0,o.AH)({width:"100%",background:e.colors.backgroundPrimary,padding:`${e.spacing.sm}px ${e.spacing.lg/2}px ${e.spacing.sm}px ${e.spacing.lg/2}px`,position:"sticky",bottom:0,boxSizing:"border-box","&:has(> .combobox-footer-add-button)":{padding:`${e.spacing.sm}px 0 ${e.spacing.sm}px 0`,"& > :not(.combobox-footer-add-button)":{marginLeft:`${e.spacing.lg/2}px`,marginRight:`${e.spacing.lg/2}px`},"& > .combobox-footer-add-button":{justifyContent:"flex-start !important"}}},"",""),p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;return(0,o.AH)({flexGrow:1,display:"inline-grid",gridTemplateColumns:`${100-e}% ${e}%`},"","")},f=(e,t,r)=>(0,o.AH)({color:e.colors.textSecondary,fontSize:e.typography.fontSizeSm,textAlign:"right",...t&&{color:e.colors.actionDisabledText},..."ellipsis"===r&&{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}},"","")},37402:(e,t,r)=>{r.d(t,{I:()=>n});let o=function(e){return e.ActionDangerDefaultBackgroundDefault="actionDangerDefaultBackgroundDefault",e.ActionDangerDefaultBackgroundHover="actionDangerDefaultBackgroundHover",e.ActionDangerDefaultBackgroundPress="actionDangerDefaultBackgroundPress",e.ActionDangerDefaultBorderDefault="actionDangerDefaultBorderDefault",e.ActionDangerDefaultBorderHover="actionDangerDefaultBorderHover",e.ActionDangerDefaultBorderPress="actionDangerDefaultBorderPress",e.ActionDangerDefaultTextDefault="actionDangerDefaultTextDefault",e.ActionDangerDefaultTextHover="actionDangerDefaultTextHover",e.ActionDangerDefaultTextPress="actionDangerDefaultTextPress",e.ActionDangerPrimaryBackgroundDefault="actionDangerPrimaryBackgroundDefault",e.ActionDangerPrimaryBackgroundHover="actionDangerPrimaryBackgroundHover",e.ActionDangerPrimaryBackgroundPress="actionDangerPrimaryBackgroundPress",e.ActionDangerPrimaryText="actionDangerPrimaryText",e.ActionDefaultBackgroundDefault="actionDefaultBackgroundDefault",e.ActionDefaultBackgroundHover="actionDefaultBackgroundHover",e.ActionDefaultBackgroundPress="actionDefaultBackgroundPress",e.ActionDefaultBorderDefault="actionDefaultBorderDefault",e.ActionDefaultBorderFocus="actionDefaultBorderFocus",e.ActionDefaultBorderHover="actionDefaultBorderHover",e.ActionDefaultBorderPress="actionDefaultBorderPress",e.ActionDefaultIconDefault="actionDefaultIconDefault",e.ActionDefaultIconHover="actionDefaultIconHover",e.ActionDefaultIconPress="actionDefaultIconPress",e.ActionDefaultTextDefault="actionDefaultTextDefault",e.ActionDefaultTextHover="actionDefaultTextHover",e.ActionDefaultTextPress="actionDefaultTextPress",e.ActionDisabledBackground="actionDisabledBackground",e.ActionDisabledBorder="actionDisabledBorder",e.ActionDisabledText="actionDisabledText",e.ActionIconBackgroundDefault="actionIconBackgroundDefault",e.ActionIconBackgroundHover="actionIconBackgroundHover",e.ActionIconBackgroundPress="actionIconBackgroundPress",e.ActionIconIconDefault="actionIconIconDefault",e.ActionIconIconHover="actionIconIconHover",e.ActionIconIconPress="actionIconIconPress",e.ActionLinkDefault="actionLinkDefault",e.ActionLinkHover="actionLinkHover",e.ActionLinkPress="actionLinkPress",e.ActionPrimaryBackgroundDefault="actionPrimaryBackgroundDefault",e.ActionPrimaryBackgroundHover="actionPrimaryBackgroundHover",e.ActionPrimaryBackgroundPress="actionPrimaryBackgroundPress",e.ActionPrimaryIcon="actionPrimaryIcon",e.ActionPrimaryTextDefault="actionPrimaryTextDefault",e.ActionPrimaryTextHover="actionPrimaryTextHover",e.ActionPrimaryTextPress="actionPrimaryTextPress",e.ActionTertiaryBackgroundDefault="actionTertiaryBackgroundDefault",e.ActionTertiaryBackgroundHover="actionTertiaryBackgroundHover",e.ActionTertiaryBackgroundPress="actionTertiaryBackgroundPress",e.ActionTertiaryIconDefault="actionTertiaryIconDefault",e.ActionTertiaryIconHover="actionTertiaryIconHover",e.ActionTertiaryIconPress="actionTertiaryIconPress",e.ActionTertiaryTextDefault="actionTertiaryTextDefault",e.ActionTertiaryTextHover="actionTertiaryTextHover",e.ActionTertiaryTextPress="actionTertiaryTextPress",e.BackgroundDanger="backgroundDanger",e.BackgroundPrimary="backgroundPrimary",e.BackgroundSecondary="backgroundSecondary",e.BackgroundSuccess="backgroundSuccess",e.BackgroundWarning="backgroundWarning",e.Border="border",e.BorderAccessible="borderAccessible",e.BorderDanger="borderDanger",e.BorderWarning="borderWarning",e.CodeBackground="codeBackground",e.OverlayOverlay="overlayOverlay",e.ProgressFill="progressFill",e.ProgressTrack="progressTrack",e.Skeleton="skeleton",e.TableBackgroundSelectedDefault="tableBackgroundSelectedDefault",e.TableBackgroundSelectedHover="tableBackgroundSelectedHover",e.TableBackgroundUnselectedDefault="tableBackgroundUnselectedDefault",e.TableBackgroundUnselectedHover="tableBackgroundUnselectedHover",e.TextPlaceholder="textPlaceholder",e.TextPrimary="textPrimary",e.TextSecondary="textSecondary",e.TextValidationDanger="textValidationDanger",e.TextValidationSuccess="textValidationSuccess",e.TextValidationWarning="textValidationWarning",e.TooltipBackgroundTooltip="tooltipBackgroundTooltip",e}({}),n={primary:o.TextPrimary,secondary:o.TextSecondary,info:o.TextSecondary,error:o.TextValidationDanger,success:o.TextValidationSuccess,warning:o.TextValidationWarning}},63434:(e,t,r)=>{r.d(t,{Q:()=>o});let o={"&:not(:focus):not(:active)":{clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",overflow:"hidden",position:"absolute",whiteSpace:"nowrap",width:"1px"}}},69391:(e,t,r)=>{r.d(t,{D7:()=>B,Ud:()=>S,WO:()=>d,X1:()=>s,dg:()=>function e(t){return a().mapValues(t,(t,r)=>{if(a().isString(t)||a().isNumber(t)||a().isBoolean(t)){if(a().isString(t)&&a().endsWith(t,"!important"))return t;if(a().isNumber(t)){if(o.A[r])return`${t}!important`;return`${t}px!important`}return`${t}!important`}if(a().isNil(t))return t;return e(t)})},hG:()=>H,yu:()=>u});var o=r(32646),n=r(25656),a=r.n(n),i=r(72646),l=r(37590),c=r(37402);function s(e,t,r){if(e&&t&&Object(e.colors).hasOwnProperty(c.I[t]))return e.colors[c.I[t]];return null!=r?r:e.colors.textPrimary}function u(e,t){let{errorColor:r,warningColor:o,successColor:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};switch(t){case"error":return r||e.colors.actionDangerPrimaryBackgroundDefault;case"warning":return o||e.colors.textValidationWarning;case"success":return n||e.colors.textValidationSuccess;default:return}}function d(e,t){if(!e||!e.isDarkMode)return{};return{border:`1px solid ${e.colors.borderDecorative}`,...t?{}:{boxShadow:"none"}}}let g=e=>`linear-gradient(${e} 30%, rgba(0, 0, 0, 0)) center top`,p=e=>`linear-gradient(rgba(0, 0, 0, 0), ${e} 70%) center bottom`,f=e=>`linear-gradient(to left, rgba(0, 0, 0, 0), ${e} 30%) left center`,h=e=>`linear-gradient(to left, ${e} 70%, rgba(0, 0, 0, 0)) right center`,y=e=>`radial-gradient(
  farthest-side at 50% 0,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) center top`,m=e=>`radial-gradient(
  farthest-side at 50% 100%,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) center bottom`,v=e=>`radial-gradient(
  farthest-side at 0 50%,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) left center`,b=e=>`radial-gradient(
  farthest-side at 100% 50%,
  rgba(${e}, 0.2),
  rgba(${e}, 0)
) right center`,x="100% 40px",k="40px 100%",D="100% 14px",w="14px 100%",A=(0,n.memoize)(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"vertical",o=null!=t?t:e.colors.backgroundPrimary,n=e.isDarkMode?i.Az:i.aH;if("horizontal"===r)return{background:`
            ${f(o)},
            ${h(o)},
            ${v(n)},
            ${b(n)}`,backgroundRepeat:"no-repeat",backgroundSize:`
            ${k},
            ${k},
            ${w},
            ${w}`,backgroundAttachment:"local, local, scroll, scroll"};return{background:`
          ${g(o)},
          ${p(o)},
          ${y(n)},
          ${m(n)}`,backgroundRepeat:"no-repeat",backgroundSize:`
          ${x},
          ${x},
          ${D},
          ${D}`,backgroundAttachment:"local, local, scroll, scroll"}},(e,t,r)=>`${e.isDarkMode}-${t}-${r}`),S=function(e){let{backgroundColor:t,orientation:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return A(e,t,r)},T=(0,n.memoize)(function(e,t){let r=null!=t?t:e.colors.backgroundPrimary;return{background:`
          ${p(r)},
          ${m(e.isDarkMode?i.Az:i.aH)}`,backgroundRepeat:"no-repeat",backgroundSize:`
          ${x},
          ${D}`,backgroundAttachment:"local, scroll"}}),H=function(e){let{backgroundColor:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return T(e,t)};function B(e){let t=l.GE[0]+l.GE[2],r=l.GE[1]+l.GE[3];return{position:"absolute",width:`calc(100% - ${r}px)`,height:`${e.size-t}px`,transform:`translateY(${e.start}px)`}}},94167:(e,t,r)=>{r.d(t,{De:()=>l,VG:()=>i});var o=r(71218),n=r(58829);let a=e=>(0,o.AH)({outline:`1px dashed ${(e.isDarkMode,e.colors.lime)}`,outlineOffset:"2px"},"","");function i(){return(0,n.W)("databricks.fe.designsystem.showDebugOutline",!1)?{"data-dubois-show-outline":!0}:{}}function l(e){return(0,n.W)("databricks.fe.designsystem.showDebugOutline",!1)?a(e):{}}},58829:(e,t,r)=>{r.d(t,{W:()=>o});let o=(e,t)=>{try{let r=window.__debug__safex;return r?r(e,t):t}catch(e){return t}}},80842:(e,t,r)=>{r.d(t,{z:()=>l});var o=r(65848);let n=null,a=new WeakMap,i=()=>{n||(n=new IntersectionObserver(e=>{e.forEach(e=>{if(e.isIntersecting){let t=a.get(e.target);t&&(t(),a.delete(e.target))}})}))},l=e=>{let{onView:t,resetKey:r,value:l}=e,c=(0,o.useRef)(!1),s=(0,o.useRef)(null),u=(0,o.useRef)(r);return(0,o.useEffect)(()=>{u.current!==r&&(c.current=!1,u.current=r)},[r]),(0,o.useEffect)(()=>{let e=s.current;if(!e||c.current)return;let r=()=>{c.current=!0,t(l)};if(!window.IntersectionObserver){r();return}return function(e,t){var r;return i(),a.set(e,t),null===(r=n)||void 0===r||r.observe(e),()=>{if(e){var t;null===(t=n)||void 0===t||t.unobserve(e),a.delete(e)}}}(e,r)},[t,l]),{elementRef:s}}},60499:(e,t,r)=>{r.d(t,{Y:()=>a});var o=r(65848);let n=()=>new Date().getTime()+Array(16).fill("").map(()=>parseInt((10*Math.random()).toString())).join("");function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return(0,o.useState)(()=>`${e}-${n()}`)[0]}}}]);
//# sourceMappingURL=https://sourcemaps.dev.databricks.com/ml-model-trace-renderer/js/381.b0dd6b18.chunk.js.map