"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[532],{20109:function(e,o,s){s.d(o,{X:function(){return c}});s(31014);var t=s(28486),r=s(48012),n=s(32599),i=s(88443),a=s(50111);function l(){return(0,a.Y)(r.SvL,{"data-testid":"fallback",title:(0,a.Y)(i.A,{id:"qAdWdK",defaultMessage:"Error"}),description:(0,a.Y)(i.A,{id:"RzZVxC",defaultMessage:"An error occurred while rendering this component."}),image:(0,a.Y)(n.j,{})})}function d(e){let{children:o,customFallbackComponent:s}=e;function r(e,o){console.error("Caught Unexpected Error: ",e,o.componentStack)}return s?(0,a.Y)(t.tH,{onError:r,FallbackComponent:s,children:o}):(0,a.Y)(t.tH,{onError:r,fallback:(0,a.Y)(l,{}),children:o})}function c(e,o,s,t){return function(e){return(0,a.Y)(d,{customFallbackComponent:t,children:(0,a.Y)(o,{...e})})}}},30311:function(e,o,s){s.d(o,{o:function(){return w}});var t=s(89555),r=s(9133),n=s(31014),i=s(48012),a=s(32599),l=s(15579),d=s(64756),c=s(88464),m=s(88443),p=s(50111);var u={name:"1d3w5wq",styles:"width:100%"},g={name:"s079uh",styles:"margin-top:2px"},v={name:"82a6rk",styles:"flex:1"},f={name:"82a6rk",styles:"flex:1"};const h=e=>{let{renderKey:o,setDraftAliases:s,existingAliases:r,draftAliases:l,version:h,aliasToVersionMap:Y,disabled:M}=e;const A=(0,c.A)(),[w,y]=(0,n.useState)(!1),{theme:E}=(0,a.u)(),S=(0,n.useCallback)((e=>{s((o=>o.filter((o=>o!==e))))}),[s]),x=(0,n.useCallback)((e=>{const o=e.map((e=>e.replace(/[^\w-]/g,"").toLowerCase().substring(0,255))).filter((e=>e.length>0)),t=Array.from(new Set(o));s(t),y(!1)}),[s]);return(0,p.FD)(i._vn,{disabled:M,filterOption:(e,o)=>null===o||void 0===o?void 0:o.value.toLowerCase().startsWith(e.toLowerCase()),placeholder:A.formatMessage({id:"bQZDSv",defaultMessage:"Enter aliases (champion, challenger, etc)"}),allowClear:!0,css:u,mode:"tags",onChange:x,dangerouslySetAntdProps:{dropdownMatchSelectWidth:!0,tagRender:e=>{let{value:o}=e;return(0,p.Y)(d.m,{compact:!0,css:g,closable:!0,onClose:()=>S(o.toString()),value:o.toString()})}},onDropdownVisibleChange:y,open:w,value:l||[],children:[r.map((e=>(0,p.Y)(i._vn.Option,{value:e,"data-testid":"model-alias-option",children:(0,p.FD)("div",{css:(0,t.AH)({display:"flex",marginRight:E.spacing.xs},""),children:[(0,p.Y)("div",{css:v,children:e}),(0,p.Y)("div",{children:(0,p.Y)(m.A,{id:"4OcdLd",defaultMessage:"This version"})})]},e)},e))),Object.entries(Y).filter((e=>{let[,o]=e;return o!==h})).map((e=>{let[o,s]=e;return(0,p.Y)(i._vn.Option,{value:o,"data-testid":"model-alias-option",children:(0,p.FD)("div",{css:(0,t.AH)({display:"flex",marginRight:E.spacing.xs},""),children:[(0,p.Y)("div",{css:f,children:o}),(0,p.Y)("div",{children:(0,p.Y)(m.A,{id:"Boj6Fm",defaultMessage:"Version {version}",values:{version:s}})})]},o)},o)}))]},JSON.stringify(o))};var Y=s(10811),M=s(69708),A=s(81866);const w=e=>{let{model:o,onSuccess:s,modalTitle:d,modalDescription:c}=e;const[u,g]=(0,n.useState)(!1),[v]=i.SQ4.useForm(),[f,w]=(0,n.useState)(""),{theme:y}=(0,a.u)(),[E,S]=(0,n.useState)([]),[x,D]=(0,n.useState)([]),[b,_]=(0,n.useState)("0"),V=(0,Y.wA)(),C=(0,n.useCallback)((e=>{var s;if(!o)return;const t=(null===(s=o.aliases)||void 0===s?void 0:s.filter((o=>{let{version:s}=o;return s===e})).map((e=>{let{alias:o}=e;return o})))||[];e&&(S(t),D(t),_(e),g(!0))}),[o]),T=(0,n.useMemo)((()=>{if(null===o||void 0===o||!o.aliases)return[];const e=o.aliases.reduce(((e,o)=>{var s;return e.some((e=>{let{version:s}=e;return s===o.version}))?(null===(s=e.find((e=>{let{version:s}=e;return s===o.version})))||void 0===s||s.aliases.push(o.alias),e):[...e,{version:o.version,aliases:[o.alias]}]}),[]),s=e.filter((e=>{let{version:o}=e;return o!==b}));return x.map((e=>({alias:e,otherVersion:s.find((o=>{var s;return null===(s=o.aliases)||void 0===s?void 0:s.find((o=>o===e))}))}))).filter((e=>{let{otherVersion:o}=e;return o}))}),[null===o||void 0===o?void 0:o.aliases,x,b]),P=(0,n.useMemo)((()=>{var e;return(null===o||void 0===o||null===(e=o.aliases)||void 0===e?void 0:e.reduce(((e,o)=>{let{alias:s,version:t}=o;return{...e,[s]:t}}),{}))||{}}),[o]),R=(0,r.isEqual)(E.slice().sort(),x.slice().sort()),k=x.length>10,I=R||k;return{EditAliasesModal:(0,p.FD)(l.d,{componentId:"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_127",visible:u,footer:(0,p.FD)("div",{children:[(0,p.Y)(a.B,{componentId:"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_131",onClick:()=>g(!1),children:(0,p.Y)(m.A,{id:"97hGxL",defaultMessage:"Cancel"})}),(0,p.Y)(a.B,{componentId:"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_137",loading:!1,type:"primary",disabled:I,onClick:()=>{o&&(w(""),V((0,M.mq)(o.name,b,E,x)).then((()=>{g(!1),null===s||void 0===s||s()})).catch((e=>{const o=e.getMessageField()||e.getUserVisibleError().toString()||e.text;w(o)})))},children:(0,p.Y)(m.A,{id:"cruwL4",defaultMessage:"Save aliases"})})]}),destroyOnClose:!0,title:d?d(b):(0,p.Y)(m.A,{id:"4Jyn2b",defaultMessage:"Add/Edit alias for model version {version}",values:{version:b}}),onCancel:()=>g(!1),confirmLoading:!1,children:[(0,p.Y)(a.T.Paragraph,{children:null!==c&&void 0!==c?c:(0,p.Y)(m.A,{id:"2DNVN4",defaultMessage:"Aliases allow you to assign a mutable, named reference to a particular model version. <link>Learn more</link>",values:{link:e=>(0,p.Y)("a",{href:A.Tm,rel:"noreferrer",target:"_blank",children:e})}})}),(0,p.FD)(i.SQ4,{form:v,layout:"vertical",children:[(0,p.Y)(i.SQ4.Item,{children:(0,p.Y)(h,{disabled:!1,renderKey:T,aliasToVersionMap:P,version:b,draftAliases:x,existingAliases:E,setDraftAliases:D})}),(0,p.FD)("div",{css:(0,t.AH)({display:"flex",flexDirection:"column",gap:y.spacing.xs},""),children:[k&&(0,p.Y)(i.FcD,{componentId:"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_192",role:"alert",message:(0,p.Y)(m.A,{id:"q/Z61H",defaultMessage:"You are exceeding a limit of {limit} aliases assigned to the single model version",values:{limit:10}}),type:"error",closable:!1}),T.map((e=>{let{alias:o,otherVersion:s}=e;return(0,p.Y)(i.FcD,{componentId:"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_206",role:"alert",message:(0,p.Y)(m.A,{id:"EBQwqu",defaultMessage:'The "{alias}" alias is also being used on version {otherVersion}. Adding it to this version will remove it from version {otherVersion}.',values:{otherVersion:null===s||void 0===s?void 0:s.version,alias:o}}),type:"info",closable:!1},o)})),f&&(0,p.Y)(i.FcD,{componentId:"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_220",role:"alert",message:f,type:"error",closable:!1})]})]})]}),showEditAliasesModal:C}}},30922:function(e,o,s){s.d(o,{q:function(){return i}});var t=s(32599),r=s(88443),n=s(50111);const i=e=>{let{row:{original:o},getValue:s}=e;const i=s();return i?(0,n.Y)(t.T.Text,{children:(0,n.Y)(r.A,{id:"sM0MBJ",defaultMessage:"Version {version}",values:{version:i}})}):null}},38235:function(e,o,s){s.d(o,{i:function(){return c}});var t=s(89555),r=(s(31014),s(32599)),n=s(48012),i=s(37616),a=s(56412),l=s(50111);var d={name:"bjn8wh",styles:"position:relative"};const c=e=>{let{code:o}=e;const{theme:s}=(0,r.u)();return(0,l.FD)("div",{css:d,children:[(0,l.Y)(a.i,{css:(0,t.AH)({zIndex:1,position:"absolute",top:s.spacing.xs,right:s.spacing.xs},""),showLabel:!1,copyText:o,icon:(0,l.Y)(n.TdU,{})}),(0,l.Y)(i.z7,{language:"python",showLineNumbers:!1,style:{padding:s.spacing.sm,color:s.colors.textPrimary,backgroundColor:s.colors.backgroundSecondary,whiteSpace:"pre-wrap"},wrapLongLines:!0,children:o})]})}},62448:function(e,o,s){s.d(o,{h:function(){return a}});var t=s(39416),r=s(52350),n=s(47664);class i{}i.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"};const a=(e,o)=>{if(!(e instanceof r.s))return;const{status:s}=e;let i;const a={status:s};e.getErrorCode()===n.tG.RESOURCE_DOES_NOT_EXIST&&(i=new t.m_(a)),e.getErrorCode()===n.tG.PERMISSION_DENIED&&(i=new t.i_(a)),e.getErrorCode()===n.tG.INTERNAL_ERROR&&(i=new t.PO(a)),e.getErrorCode()===n.tG.INVALID_PARAMETER_VALUE&&(i=new t.v7(a));const l=e.getMessageField();return i&&l&&(i.message=l),i};o.A=i},64756:function(e,o,s){s.d(o,{m:function(){return a}});var t=s(89555),r=s(32599),n=s(48012),i=s(50111);const a=e=>{let{value:o,closable:s,onClose:a,className:l,compact:d=!1}=e;const{theme:c}=(0,r.u)();return(0,i.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversionaliastag.tsx_23",css:(0,t.AH)({fontWeight:c.typography.typographyBoldFontWeight,marginRight:c.spacing.xs},""),className:l,closable:s,onClose:a,title:o,children:(0,i.FD)("span",{css:(0,t.AH)({display:"block",whiteSpace:"nowrap",maxWidth:d?160:300,textOverflow:"ellipsis",overflow:"hidden"},""),children:["@","\xa0",o]})})}},70724:function(e,o,s){s.d(o,{v:function(){return u},z:function(){return g}});var t=s(15579),r=s(48012),n=s(31014),i=s(13369),a=s(88464),l=s(88443),d=s(77020),c=s(82832),m=s(84565);var p=s(50111);let u=function(e){return e.CreatePrompt="CreatePrompt",e.CreatePromptVersion="CreatePromptVersion",e}({});const g=e=>{let{mode:o=u.CreatePromptVersion,registeredPrompt:s,latestVersion:g,onSuccess:v}=e;const[f,h]=(0,n.useState)(!1),Y=(0,a.A)(),M=(0,i.mN)({defaultValues:{draftName:"",draftValue:"",commitMessage:"",tags:[]}}),A=o===u.CreatePrompt,w=o===u.CreatePromptVersion,{mutate:y,error:E,reset:S,isLoading:x}=(0,d.n)({mutationFn:async e=>{var o;let{promptName:s,createPromptEntity:t,content:r,commitMessage:n,tags:i}=e;t&&await c.M.createRegisteredPrompt(s);const a=await c.M.createRegisteredPromptVersion(s,[{key:m.Dh,value:r},...i],n),l=null===a||void 0===a||null===(o=a.model_version)||void 0===o?void 0:o.version;if(!l)throw new Error("Failed to create a new prompt version");return{version:l}}});return{CreatePromptModal:(0,p.FD)(t.d,{componentId:"mlflow.prompts.create.modal",visible:f,onCancel:()=>h(!1),title:w?(0,p.Y)(l.A,{id:"XLSmZx",defaultMessage:"Create prompt version"}):(0,p.Y)(l.A,{id:"wh+Eos",defaultMessage:"Create prompt"}),okText:(0,p.Y)(l.A,{id:"TSXmaB",defaultMessage:"Create"}),okButtonProps:{loading:x},onOk:M.handleSubmit((async e=>{const o=w&&null!==s&&void 0!==s&&s.name?null===s||void 0===s?void 0:s.name:e.draftName;y({createPromptEntity:A,content:e.draftValue,commitMessage:e.commitMessage,promptName:o,tags:e.tags},{onSuccess:e=>{const s=null===e||void 0===e?void 0:e.version;null===v||void 0===v||v({promptName:o,promptVersion:s}),h(!1)}})})),cancelText:(0,p.Y)(l.A,{id:"WP50re",defaultMessage:"Cancel"}),size:"wide",children:[(null===E||void 0===E?void 0:E.message)&&(0,p.FD)(p.FK,{children:[(0,p.Y)(r.FcD,{componentId:"mlflow.prompts.create.error",closable:!1,message:E.message,type:"error"}),(0,p.Y)(t.S,{})]}),A&&(0,p.FD)(p.FK,{children:[(0,p.Y)(r.D$Q.Label,{htmlFor:"mlflow.prompts.create.name",children:"Name:"}),(0,p.Y)(r.tc_.Input,{control:M.control,id:"mlflow.prompts.create.name",componentId:"mlflow.prompts.create.name",name:"draftName",rules:{required:{value:!0,message:Y.formatMessage({id:"Rlwm5V",defaultMessage:"Name is required"})},pattern:{value:/^[a-zA-Z0-9_\-.]+$/,message:Y.formatMessage({id:"HZdpLU",defaultMessage:"Only alphanumeric characters, underscores, hyphens, and dots are allowed"})}},placeholder:Y.formatMessage({id:"zAuZ2j",defaultMessage:"Provide an unique prompt name"}),validationState:M.formState.errors.draftName?"error":void 0}),M.formState.errors.draftName&&(0,p.Y)(r.D$Q.Message,{type:"error",message:M.formState.errors.draftName.message}),(0,p.Y)(t.S,{})]}),(0,p.Y)(r.D$Q.Label,{htmlFor:"mlflow.prompts.create.content",children:"Prompt:"}),(0,p.Y)(r.tc_.TextArea,{control:M.control,id:"mlflow.prompts.create.content",componentId:"mlflow.prompts.create.content",name:"draftValue",autoSize:{minRows:3,maxRows:10},rules:{required:{value:!0,message:Y.formatMessage({id:"wq+WyH",defaultMessage:"Prompt content is required"})}},placeholder:Y.formatMessage({id:"2JfWh3",defaultMessage:"Type prompt content here. Wrap variables with double curly brace e.g. '{{' name '}}'."}),validationState:M.formState.errors.draftValue?"error":void 0}),M.formState.errors.draftValue&&(0,p.Y)(r.D$Q.Message,{type:"error",message:M.formState.errors.draftValue.message}),(0,p.Y)(t.S,{}),(0,p.Y)(r.D$Q.Label,{htmlFor:"mlflow.prompts.create.commit_message",children:"Commit message (optional):"}),(0,p.Y)(r.tc_.Input,{control:M.control,id:"mlflow.prompts.create.commit_message",componentId:"mlflow.prompts.create.commit_message",name:"commitMessage"})]}),openModal:()=>{var e;(S(),o===u.CreatePromptVersion&&g)&&M.reset({commitMessage:"",draftName:"",draftValue:null!==(e=(0,m.dv)(g))&&void 0!==e?e:"",tags:[]});h(!0)}}}},72282:function(e,o,s){s.d(o,{u:function(){return d}});var t=s(48012),r=s(32599),n=s(88443),i=s(84069),a=s(50111);var l={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const d=e=>{var o;let{error:s}=e;return(0,a.Y)(i.m,{css:l,children:(0,a.Y)(t.SvL,{"data-testid":"fallback",title:(0,a.Y)(n.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(o=null===s||void 0===s?void 0:s.message)&&void 0!==o?o:(0,a.Y)(n.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,a.Y)(r.j,{})})})}},80532:function(e,o,s){s.r(o),s.d(o,{default:function(){return Ee}});var t=s(89555),r=s(4877),n=s.n(r),i=s(41261),a=s(82832);const l=async e=>{var o;let{queryKey:s}=e;const[,{promptName:t}]=s,[r,n]=await Promise.all([a.M.getPromptDetails(t),a.M.getPromptVersions(t)]);return{prompt:r.registered_model,versions:null!==(o=n.model_versions)&&void 0!==o?o:[]}};var d=s(93215),c=s(84069),m=s(32599),p=s(48012),u=s(15579),g=s(88443),v=s(84565),f=s(31014),h=s(58481),Y=s(70724),M=s(77020),A=s(50111);var w=s(70618),y=s(9856),E=s(88464),S=s(76010),x=s(30922),D=s(91164);const b=e=>{let{getValue:o,row:{original:s},table:{options:{meta:t}}}=e;const{showEditAliasesModal:r,aliasesByVersion:n,registeredPrompt:i}=t,a=n[s.version]||[];return i?(0,A.Y)(D.Y,{modelName:null===i||void 0===i?void 0:i.name,version:s.version,aliases:a,onAddEdit:()=>{null===r||void 0===r||r(s.version)}}):null},_=e=>{let{isSelectedFirstToCompare:o,isSelectedSecondToCompare:s,onSelectFirst:r,onSelectSecond:n}=e;const{theme:i}=(0,m.u)(),a=(0,E.A)();return(0,A.Y)("div",{css:(0,t.AH)({width:i.general.buttonHeight,display:"flex",alignItems:"center",paddingRight:i.spacing.sm},""),children:(0,A.FD)("div",{css:(0,t.AH)({display:"flex",height:i.general.buttonInnerHeight+i.spacing.xs,gap:0,flex:1},""),children:[(0,A.Y)(u.T,{componentId:"mlflow.prompts.details.select_baseline.tooltip",content:(0,A.Y)(g.A,{id:"KV3BXl",defaultMessage:"Select as baseline version"}),delayDuration:0,side:"left",children:(0,A.Y)("button",{onClick:r,role:"radio","aria-checked":o,"aria-label":a.formatMessage({id:"KV3BXl",defaultMessage:"Select as baseline version"}),css:(0,t.AH)({flex:1,border:`1px solid ${o?i.colors.actionDefaultBorderFocus:i.colors.actionDefaultBorderDefault}`,borderRight:0,marginLeft:1,borderTopLeftRadius:i.borders.borderRadiusMd,borderBottomLeftRadius:i.borders.borderRadiusMd,backgroundColor:o?i.colors.actionDefaultBackgroundPress:i.colors.actionDefaultBackgroundDefault,cursor:"pointer","&:hover":{backgroundColor:i.colors.actionDefaultBackgroundHover}},"")})}),(0,A.Y)(u.T,{componentId:"mlflow.prompts.details.select_compared.tooltip",content:(0,A.Y)(g.A,{id:"9IN1I8",defaultMessage:"Select as compared version"}),delayDuration:0,side:"right",children:(0,A.Y)("button",{onClick:n,role:"radio","aria-checked":s,"aria-label":a.formatMessage({id:"9IN1I8",defaultMessage:"Select as compared version"}),css:(0,t.AH)({flex:1,border:`1px solid ${s?i.colors.actionDefaultBorderFocus:i.colors.actionDefaultBorderDefault}`,borderLeft:`1px solid ${o||s?i.colors.actionDefaultBorderFocus:i.colors.actionDefaultBorderDefault}`,borderTopRightRadius:i.borders.borderRadiusMd,borderBottomRightRadius:i.borders.borderRadiusMd,backgroundColor:s?i.colors.actionDefaultBackgroundPress:i.colors.actionDefaultBackgroundDefault,cursor:"pointer","&:hover":{backgroundColor:i.colors.actionDefaultBackgroundHover}},"")})})]})})};var V={name:"inxa61",styles:"flex:1;overflow:hidden"},C={name:"1h3rtzg",styles:"align-items:center"};const T=e=>{let{promptVersions:o,onUpdateComparedVersion:s,isLoading:r,onUpdateSelectedVersion:n,comparedVersion:i,selectedVersion:a,mode:l,registeredPrompt:d,showEditAliasesModal:c,aliasesByVersion:u}=e;const h=(0,E.A)(),{theme:Y}=(0,m.u)(),M=(0,f.useMemo)((()=>{const e=[{id:"version",header:h.formatMessage({id:"Vn+uJi",defaultMessage:"Version"}),accessorKey:"version",cell:x.q}];return l===v.Dp.TABLE&&(e.push({id:"creation_timestamp",header:h.formatMessage({id:"1tRtls",defaultMessage:"Registered at"}),accessorFn:e=>{let{creation_timestamp:o}=e;return S.A.formatTimestamp(o,h)}}),e.push({id:"commit_message",header:h.formatMessage({id:"ZaQ42C",defaultMessage:"Commit message"}),accessorKey:"description"}),e.push({id:"aliases",header:h.formatMessage({id:"M0zIfe",defaultMessage:"Aliases"}),accessorKey:"aliases",cell:b})),e}),[l,h]),D=(0,w.N4)({data:null!==o&&void 0!==o?o:[],getRowId:e=>e.version,columns:M,getCoreRowModel:(0,y.HT)(),meta:{showEditAliasesModal:c,aliasesByVersion:u,registeredPrompt:d}});return(0,A.Y)("div",{css:V,children:(0,A.FD)(p.XIK,{scrollable:!0,empty:r||0!==(null===o||void 0===o?void 0:o.length)?null:(0,A.Y)(p.SvL,{title:(0,A.Y)(g.A,{id:"9pJlQd",defaultMessage:"No prompt versions created"}),description:(0,A.Y)(g.A,{id:"7zgX/l",defaultMessage:'Use "Create prompt version" button in order to create a new prompt version'})}),"aria-label":"Prompt versions table",children:[(0,A.Y)(p.Hjg,{isHeader:!0,children:D.getLeafHeaders().map((e=>(0,A.Y)(p.A0N,{componentId:"mlflow.prompts.versions.table.header",children:(0,w.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),r?(0,A.Y)(p.BAM,{table:D}):D.getRowModel().rows.map((e=>{const o=[v.Dp.PREVIEW].includes(l)&&a===e.original.version,r=[v.Dp.COMPARE].includes(l)&&a===e.original.version,d=[v.Dp.COMPARE].includes(l)&&i===e.original.version,c=l===v.Dp.PREVIEW;return(0,A.FD)(p.Hjg,{css:(0,t.AH)({height:Y.general.heightBase,backgroundColor:o?Y.colors.actionDefaultBackgroundPress:r||d?Y.colors.actionDefaultBackgroundHover:"transparent",cursor:c?"pointer":"default"},""),onClick:()=>{l===v.Dp.PREVIEW&&n(e.original.version)},children:[e.getAllCells().map((e=>(0,A.Y)(p.nA6,{css:C,children:(0,w.Kv)(e.column.columnDef.cell,e.getContext())},e.id))),o&&(0,A.Y)("div",{css:(0,t.AH)({width:2*Y.spacing.md,display:"flex",alignItems:"center",paddingRight:Y.spacing.sm},""),children:(0,A.Y)(m.q,{})}),l===v.Dp.COMPARE&&(0,A.Y)(_,{onSelectFirst:()=>n(e.original.version),onSelectSecond:()=>s(e.original.version),isSelectedFirstToCompare:r,isSelectedSecondToCompare:d})]},e.id)}))]})})};var P=s(30311),R=s(9133);const k=(e,o)=>"setTableMode"===o.type?{...e,mode:v.Dp.TABLE}:"switchSides"===o.type?{...e,selectedVersion:e.comparedVersion,comparedVersion:e.selectedVersion}:"setPreviewMode"===o.type?{...e,mode:v.Dp.PREVIEW,selectedVersion:o.selectedVersion}:"setCompareMode"===o.type?{...e,mode:v.Dp.COMPARE,selectedVersion:o.selectedVersion,comparedVersion:o.comparedVersion}:"setSelectedVersion"===o.type?{...e,selectedVersion:o.selectedVersion}:"setComparedVersion"===o.type?{...e,comparedVersion:o.comparedVersion}:e;var I=s(77735),N=s(63515),F=s(63528);var O={name:"1pzk433",styles:"width:100px"};const B=e=>{let{isLoadingRuns:o,runIds:s,runInfoMap:t}=e;const[r,n]=(0,f.useState)(!1),{theme:i}=(0,m.u)(),a=r?s.length:Math.min(3,s.length||0),l=s.length>3;return(0,A.FD)(A.FK,{children:[(0,A.Y)(m.T.Text,{bold:!0,children:(0,A.Y)(g.A,{id:"9ZHB3D",defaultMessage:"MLflow runs:"})}),(0,A.Y)("div",{children:o?(0,A.Y)(p.I_K,{css:O}):(0,A.Y)(A.FK,{children:(0,A.FD)("div",{style:{display:"flex",flexWrap:"wrap",gap:i.spacing.sm},children:[s.slice(0,a).map(((e,o)=>{const s=t[e];if(!(0,R.isNil)(null===s||void 0===s?void 0:s.experimentId)&&null!==s&&void 0!==s&&s.runUuid&&null!==s&&void 0!==s&&s.runName){const{experimentId:e,runUuid:t,runName:r}=s;return(0,A.FD)(m.T.Text,{children:[(0,A.Y)(d.N_,{to:h.h.getRunPageRoute(e,t),children:r}),o<a-1&&","]})}return(0,A.Y)("span",{children:(null===s||void 0===s?void 0:s.runName)||(null===s||void 0===s?void 0:s.runUuid)})})),l&&(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.runs.show_more",size:"small",type:"link",onClick:()=>n(!r),children:r?(0,A.Y)(g.A,{id:"pVF+2s",defaultMessage:"Show less"}):(0,A.Y)(g.A,{id:"fh6RWb",defaultMessage:"{count} more...",values:{count:s.length-a}})})]})})})]})};var L=s(98590),H=s(98597);var U={name:"ti75j2",styles:"margin:0"};const j=e=>{let{tags:o,onEditVersionMetadata:s}=e;const[t,r]=(0,f.useState)(!1),{theme:n}=(0,m.u)(),i=t?o.length:Math.min(3,o.length||0),a=o.length>3,l=!(0,R.isNil)(s),d=o.length>0?(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.version.edit_tags",size:"small",icon:(0,A.Y)(p.R2l,{}),onClick:s}):(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.version.add_tags",size:"small",type:"link",onClick:s,children:(0,A.Y)(g.A,{id:"JGQ5B2",defaultMessage:"Add"})});return(0,A.FD)(A.FK,{children:[(0,A.Y)(m.T.Text,{bold:!0,children:(0,A.Y)(g.A,{id:"GgPNAZ",defaultMessage:"Metadata:"})}),(0,A.Y)("div",{children:(0,A.Y)(A.FK,{children:(0,A.FD)("div",{style:{display:"flex",flexWrap:"wrap",gap:n.spacing.xs},children:[o.slice(0,i).map((e=>(0,A.Y)(H.t,{css:U,tag:e},e.key))),l&&d,!l&&0===o.length&&(0,A.Y)(m.T.Hint,{children:"\u2014"}),a&&(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.version.tags.show_more",size:"small",type:"link",onClick:()=>r(!t),children:t?(0,A.Y)(g.A,{id:"pVF+2s",defaultMessage:"Show less"}):(0,A.Y)(g.A,{id:"fh6RWb",defaultMessage:"{count} more...",values:{count:o.length-i}})})]})})})]})};var K={name:"wjlmq0",styles:"max-width:none"};const W=e=>{var o;let{registeredPromptVersion:s,registeredPrompt:r,showEditAliasesModal:n,onEditVersion:i,showEditPromptVersionMetadataModal:a,aliasesByVersion:l,isBaseline:d}=e;const{theme:c}=(0,m.u)(),p=(0,f.useMemo)((()=>{var e,o;const t=null===s||void 0===s||null===(e=s.tags)||void 0===e||null===(o=e.find((e=>e.key===v.xd)))||void 0===o?void 0:o.value;return t?t.split(",").map((e=>e.trim())):[]}),[s]),{isLoading:u,runInfoMap:h}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const o=(0,I.E)({queries:e.map((e=>({queryKey:["prompt_associated_runs",e],queryFn:async e=>{let{queryKey:[,o]}=e;try{const e=await F.x.getRun({run_id:o});return(0,N.LJ)(e)}catch(s){return null}}})))}),s={};return o.forEach(((o,t)=>{var r,n;const i=e[t];s[i]=null===(r=o.data)||void 0===r||null===(n=r.run)||void 0===n?void 0:n.info})),{isLoading:e.length>0&&o.some((e=>e.isLoading)),runInfoMap:s}}(p||[]);if(!r||!s)return null;const Y=(null===s||void 0===s||null===(o=s.tags)||void 0===o?void 0:o.filter((e=>(0,L.oD)(e.key))))||[],M=(0,A.Y)(g.A,{id:"loe2Ov",defaultMessage:"Version {version}",values:{version:s.version}}),w=a?()=>{a(s)}:void 0;return(0,A.FD)("div",{css:(0,t.AH)({display:"grid",gridTemplateColumns:"120px 1fr",gridAutoRows:`minmax(${c.typography.lineHeightLg}, auto)`,alignItems:"flex-start",rowGap:c.spacing.xs,columnGap:c.spacing.sm},""),children:[i&&(0,A.FD)(A.FK,{children:[(0,A.Y)(m.T.Text,{bold:!0,children:"Version:"}),(0,A.FD)(m.T.Text,{children:[(0,A.Y)(m.T.Link,{componentId:"mlflow.prompts.details.version.goto",onClick:()=>i(s),children:M})," ",d&&(0,A.Y)(g.A,{id:"FTx+Hi",defaultMessage:"(baseline)"})]})]}),(0,A.Y)(m.T.Text,{bold:!0,children:(0,A.Y)(g.A,{id:"wFvyI1",defaultMessage:"Registered at:"})}),(0,A.Y)(m.T.Text,{children:S.A.formatTimestamp(s.creation_timestamp)}),(0,A.Y)(m.T.Text,{bold:!0,children:(0,A.Y)(g.A,{id:"h3vlVA",defaultMessage:"Aliases:"})}),(0,A.Y)("div",{children:(0,A.Y)(D.Y,{css:K,modelName:r.name,version:s.version,aliases:l[s.version]||[],onAddEdit:()=>{null===n||void 0===n||n(s.version)}})}),s.description&&(0,A.FD)(A.FK,{children:[(0,A.Y)(m.T.Text,{bold:!0,children:(0,A.Y)(g.A,{id:"KGMbzq",defaultMessage:"Commit message:"})}),(0,A.Y)(m.T.Text,{children:s.description})]}),(0,A.Y)(j,{onEditVersionMetadata:w,tags:Y}),(u||p.length>0)&&(0,A.Y)(B,{isLoadingRuns:u,runIds:p,runInfoMap:h})]})};var z=s(38235);const G=/\{\{\s*(.*?)\s*\}\}/g;var Q={name:"1eoy87d",styles:"display:flex;justify-content:space-between"},$={name:"1089mxj",styles:"white-space:pre-wrap"};const q=e=>{let{promptVersion:o,onUpdatedContent:s,onDeletedVersion:r,aliasesByVersion:n,registeredPrompt:i,showEditAliasesModal:l,showEditPromptVersionMetadataModal:d}=e;const c=(0,f.useMemo)((()=>o?(0,v.dv)(o):""),[o]),{DeletePromptModal:h,openModal:Y}=(e=>{let{promptVersion:o,onSuccess:s}=e;const[t,r]=(0,f.useState)(!1),{mutate:n}=(0,M.n)({mutationFn:async e=>{let{promptName:o,version:s}=e;await a.M.deleteRegisteredPromptVersion(o,s)}});return{DeletePromptModal:(0,A.Y)(u.d,{componentId:"mlflow.prompts.delete_version_modal",visible:t,onCancel:()=>r(!1),title:(0,A.Y)(g.A,{id:"WfY+Gj",defaultMessage:"Delete prompt version"}),okText:(0,A.Y)(g.A,{id:"orKoPo",defaultMessage:"Delete"}),okButtonProps:{danger:!0},onOk:async()=>{null!==o&&void 0!==o&&o.name?(n({promptName:o.name,version:o.version},{onSuccess:()=>{null===s||void 0===s||s(),r(!1)}}),r(!1)):r(!1)},cancelText:(0,A.Y)(g.A,{id:"VpFZR/",defaultMessage:"Cancel"}),children:(0,A.Y)(g.A,{id:"x2+7hZ",defaultMessage:"Are you sure you want to delete the prompt version?"})}),openModal:()=>r(!0)}})({promptVersion:o,onSuccess:()=>null===r||void 0===r?void 0:r()}),[w,y]=(0,f.useState)(!1),E=(0,f.useMemo)((()=>{if(!c)return[];const e=[];let o;for(;null!==(o=G.exec(c));)e.push(o[1]);return e.some((e=>e.includes("{")||e.includes("}")))?null:(0,R.uniq)(e)}),[c]),{theme:S}=(J(o,E),(0,m.u)());return(0,A.FD)("div",{css:(0,t.AH)({flex:1,padding:S.spacing.md,paddingTop:0,borderRadius:S.borders.borderRadiusSm,overflow:"auto",display:"flex",flexDirection:"column"},""),children:[(0,A.FD)("div",{css:Q,children:[(0,A.FD)(m.T.Title,{level:3,children:["Viewing version ",null===o||void 0===o?void 0:o.version]}),(0,A.FD)("div",{css:(0,t.AH)({display:"flex",gap:S.spacing.sm},""),children:[(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.delete_version",icon:(0,A.Y)(p.ucK,{}),type:"primary",danger:!0,onClick:Y,children:(0,A.Y)(g.A,{id:"H7JwOl",defaultMessage:"Delete version"})}),(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.preview.use",icon:(0,A.Y)(p.udU,{}),onClick:()=>y(!0),children:(0,A.Y)(g.A,{id:"8EK+SZ",defaultMessage:"Use"})})]})]}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.Y)(W,{aliasesByVersion:n,registeredPrompt:i,registeredPromptVersion:o,showEditAliasesModal:l,showEditPromptVersionMetadataModal:d}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.Y)("div",{css:(0,t.AH)({backgroundColor:S.colors.backgroundSecondary,padding:S.spacing.md,overflow:"auto"},""),children:(0,A.Y)(m.T.Text,{css:$,children:c||"Empty"})}),(0,A.Y)(u.d,{componentId:"mlflow.prompts.details.preview.usage_example_modal",title:(0,A.Y)(g.A,{id:"VrxfuC",defaultMessage:"Usage example"}),visible:w,onCancel:()=>y(!1),cancelText:(0,A.Y)(g.A,{id:"cmYzGP",defaultMessage:"Dismiss"}),children:(0,A.Y)(z.i,{code:J(o,E)})}),h]})},J=(e,o)=>{let s=`from openai import OpenAI\nimport mlflow\nclient = OpenAI(api_key="<YOUR_API_KEY>")\n\n# Set MLflow tracking URI\nmlflow.set_tracking_uri("<YOUR_TRACKING_URI>")\n\n# Example of loading and using the prompt\nprompt = mlflow.genai.load_prompt("prompts:/${null===e||void 0===e?void 0:e.name}/${null===e||void 0===e?void 0:e.version}")`;return s+=null===o?'\n\n# Replace the variables with the actual values\nvariables = {\n   "key": "value",\n   ...\n}\n\nresponse = client.chat.completions.create(\n    messages=[{\n        "role": "user",\n        "content": prompt.format(**variables),\n    }],\n    model="gpt-4o-mini",\n)':`\nresponse = client.chat.completions.create(\n    messages=[{\n        "role": "user",\n        "content": prompt.format(${o.map((e=>`${e}="<${e}>"`)).join(", ")}),\n    }],\n    model="gpt-4o-mini",\n)`,s+="\n\nprint(response.choices[0].message.content)",s};var Z=s(21678);var X={name:"1eoy87d",styles:"display:flex;justify-content:space-between"},ee={name:"zjik7",styles:"display:flex"},oe={name:"82a6rk",styles:"flex:1"},se={name:"82a6rk",styles:"flex:1"},te={name:"ppz665",styles:"display:flex;flex:1;overflow:auto;align-items:flex-start"},re={name:"1089mxj",styles:"white-space:pre-wrap"},ne={name:"h55e3f",styles:"svg{rotate:90deg;}"},ie={name:"1089mxj",styles:"white-space:pre-wrap"};const ae=e=>{let{baselineVersion:o,comparedVersion:s,onSwitchSides:r,onEditVersion:n,registeredPrompt:i,aliasesByVersion:a,showEditAliasesModal:l}=e;const{theme:d}=(0,m.u)(),c=(0,E.A)(),h=(0,f.useMemo)((()=>o?(0,v.dv)(o):""),[o]),Y=(0,f.useMemo)((()=>s?(0,v.dv)(s):""),[s]),M=(0,f.useMemo)((()=>{var e;return null!==(e=(0,Z.b2)(null!==h&&void 0!==h?h:"",null!==Y&&void 0!==Y?Y:""))&&void 0!==e?e:[]}),[h,Y]),w=(0,f.useMemo)((()=>({addedBackground:d.isDarkMode?d.colors.green700:d.colors.green300,removedBackground:d.isDarkMode?d.colors.red700:d.colors.red300})),[d]);return(0,A.FD)("div",{css:(0,t.AH)({flex:1,padding:d.spacing.md,paddingTop:0,borderRadius:d.borders.borderRadiusSm,overflow:"hidden",display:"flex",flexDirection:"column"},""),children:[(0,A.Y)("div",{css:X,children:(0,A.Y)(m.T.Title,{level:3,children:(0,A.Y)(g.A,{id:"B8RvlZ",defaultMessage:"Comparing version {baseline} with version {compared}",values:{baseline:null===o||void 0===o?void 0:o.version,compared:null===s||void 0===s?void 0:s.version}})})}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.FD)("div",{css:ee,children:[(0,A.Y)("div",{css:oe,children:(0,A.Y)(W,{aliasesByVersion:a,onEditVersion:n,registeredPrompt:i,registeredPromptVersion:o,showEditAliasesModal:l,isBaseline:!0})}),(0,A.Y)("div",{css:(0,t.AH)({paddingLeft:d.spacing.sm,paddingRight:d.spacing.sm},""),children:(0,A.Y)("div",{css:(0,t.AH)({width:d.general.heightSm},"")})}),(0,A.Y)("div",{css:se,children:(0,A.Y)(W,{aliasesByVersion:a,onEditVersion:n,registeredPrompt:i,registeredPromptVersion:s,showEditAliasesModal:l})})]}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.FD)("div",{css:te,children:[(0,A.Y)("div",{css:(0,t.AH)({backgroundColor:d.colors.backgroundSecondary,padding:d.spacing.md,flex:1},""),children:(0,A.Y)(m.T.Text,{css:re,children:h||"Empty"})}),(0,A.Y)("div",{css:(0,t.AH)({paddingLeft:d.spacing.sm,paddingRight:d.spacing.sm},""),children:(0,A.Y)(u.T,{componentId:"mlflow.prompts.details.switch_sides.tooltip",content:(0,A.Y)(g.A,{id:"Cbgg9B",defaultMessage:"Switch sides"}),side:"top",children:(0,A.Y)(m.B,{"aria-label":c.formatMessage({id:"Cbgg9B",defaultMessage:"Switch sides"}),componentId:"mlflow.prompts.details.switch_sides",icon:(0,A.Y)(p.rf3,{css:ne}),onClick:r})})}),(0,A.Y)("div",{css:(0,t.AH)({backgroundColor:d.colors.backgroundSecondary,padding:d.spacing.md,flex:1},""),children:(0,A.Y)(m.T.Text,{css:ie,children:M.map(((e,o)=>(0,A.Y)("span",{css:(0,t.AH)({backgroundColor:e.added?w.addedBackground:e.removed?w.removedBackground:void 0,textDecoration:e.removed?"line-through":"none"},""),children:e.value},o)))})})]})]})};var le=s(20109),de=s(62448),ce=s(72282),me=s(83090);const pe=e=>{let{promptEntity:o,onTagsUpdated:s}=e;const r=(0,E.A)(),{theme:n}=(0,m.u)(),{EditTagsModal:i,showEditPromptTagsModal:a}=(0,me.i)({onSuccess:s}),l=(null===o||void 0===o?void 0:o.tags.filter((e=>(0,L.oD)(e.key))))||[],d=l.length>0;return(0,A.FD)("div",{css:(0,t.AH)({paddingTop:n.spacing.xs,paddingBottom:n.spacing.xs,display:"flex",flexWrap:"wrap",alignItems:"center","> *":{marginRight:"0 !important"},gap:n.spacing.xs},""),children:[null===l||void 0===l?void 0:l.map((e=>(0,A.Y)(H.t,{tag:e},e.key))),(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.tags.edit",size:"small",icon:d?(0,A.Y)(p.R2l,{}):void 0,onClick:()=>o&&a(o),"aria-label":r.formatMessage({id:"DHO5TT",defaultMessage:"Edit tags"}),children:d?void 0:(0,A.Y)(g.A,{id:"Nlm9bK",defaultMessage:"Add tags"}),type:"tertiary"}),i]})};var ue=s(82214);function ge(e){let{promptName:o}=e;return(0,A.Y)(ue.E,{statusCode:404,subMessage:`Prompt name '${o}' does not exist`,fallbackHomePageReactRoute:h.h.promptsPageRoute})}var ve=s(43683);const fe=e=>(0,A.Y)(g.A,{id:"b0KTgh",defaultMessage:"Add/edit alias for prompt version {version}",values:{version:e}});var he={name:"1m5f2z8",styles:"overflow:hidden;display:flex;flex-direction:column"},Ye={name:"1rtaly3",styles:"flex:1;display:flex;overflow:hidden"},Me={name:"1t91kdm",styles:"flex:1;display:flex;flex-direction:column"};const Ae=()=>{var e;const{promptName:o}=(0,d.g)(),{theme:s}=(0,m.u)(),r=(0,d.Zp)();n()(o,"Prompt name should be defined");const{data:w,refetch:y,isLoading:E,error:S}=function(e){var o;let{promptName:s}=e,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=(0,i.I)(["prompt_details",{promptName:s}],{queryFn:l,retry:!1,...t});return{data:r.data,error:null!==(o=r.error)&&void 0!==o?o:void 0,isLoading:r.isLoading,refetch:r.refetch}}({promptName:o}),{CreatePromptModal:x,openModal:D}=(0,Y.z)({mode:Y.v.CreatePromptVersion,registeredPrompt:null===w||void 0===w?void 0:w.prompt,latestVersion:(0,R.first)(null===w||void 0===w?void 0:w.versions),onSuccess:async e=>{let{promptVersion:o}=e;await y(),o&&N({version:o})}}),{DeletePromptModal:b,openModal:_}=(e=>{let{registeredPrompt:o,onSuccess:s}=e;const[t,r]=(0,f.useState)(!1),{mutate:n}=(0,M.n)({mutationFn:async e=>{let{promptName:o}=e;await a.M.deleteRegisteredPrompt(o)}});return{DeletePromptModal:(0,A.Y)(u.d,{componentId:"mlflow.prompts.delete_modal",visible:t,onCancel:()=>r(!1),title:(0,A.Y)(g.A,{id:"p2po2x",defaultMessage:"Delete prompt"}),okText:(0,A.Y)(g.A,{id:"kgJSBI",defaultMessage:"Delete"}),okButtonProps:{danger:!0},onOk:async()=>{null!==o&&void 0!==o&&o.name?(n({promptName:o.name},{onSuccess:()=>{null===s||void 0===s||s(),r(!1)}}),r(!1)):r(!1)},cancelText:(0,A.Y)(g.A,{id:"ZlokCq",defaultMessage:"Cancel"}),children:(0,A.Y)(g.A,{id:"WVUF2N",defaultMessage:"Are you sure you want to delete the prompt?"})}),openModal:()=>r(!0)}})({registeredPrompt:null===w||void 0===w?void 0:w.prompt,onSuccess:()=>r(h.h.promptsPageRoute)}),{EditPromptVersionMetadataModal:V,showEditPromptVersionMetadataModal:C}=(e=>{let{onSuccess:o}=e;const s=(0,M.n)({mutationFn:async e=>{let{toAdd:o,toDelete:s,promptName:t,promptVersion:r}=e;return Promise.all([...o.map((e=>{let{key:o,value:s}=e;return a.M.setRegisteredPromptVersionTag(t,r,o,s)})),...s.map((e=>{let{key:o}=e;return a.M.deleteRegisteredPromptVersionTag(t,r,o)}))])}}),{EditTagsModal:t,showEditTagsModal:r,isLoading:n}=(0,ve.Q)({title:(0,A.Y)(g.A,{id:"zxfWCx",defaultMessage:"Add/Edit Prompt Version Metadata"}),valueRequired:!0,saveTagsHandler:(e,t,r)=>{const{addedOrModifiedTags:n,deletedTags:i}=(0,L.Rf)(t,r);return new Promise(((t,r)=>{if(!e.name)return r();s.mutate({promptName:e.name,promptVersion:e.version,toAdd:n,toDelete:i},{onSuccess:()=>{t(),null===o||void 0===o||o()},onError:r})}))}});return{EditPromptVersionMetadataModal:t,showEditPromptVersionMetadataModal:(0,f.useCallback)((e=>{var o;return r({name:e.name,version:e.version,tags:null===(o=e.tags)||void 0===o?void 0:o.filter((e=>(0,L.oD)(e.key)))})}),[r]),isLoading:n}})({onSuccess:y}),{setCompareMode:I,setPreviewMode:N,setTableMode:F,switchSides:O,viewState:B,setSelectedVersion:H,setComparedVersion:U}=(e=>{const[o,s]=(0,f.useReducer)(k,{mode:v.Dp.PREVIEW}),t=(0,f.useCallback)((()=>{s({type:"setTableMode"})}),[]),r=(0,f.useCallback)((o=>{var t;const r=null===(t=null!==o&&void 0!==o?o:(0,R.first)(null===e||void 0===e?void 0:e.versions))||void 0===t?void 0:t.version;s({type:"setPreviewMode",selectedVersion:r})}),[e]),n=(0,f.useCallback)((e=>{s({type:"setSelectedVersion",selectedVersion:e})}),[]),i=(0,f.useCallback)((e=>{s({type:"setComparedVersion",comparedVersion:e})}),[]),a=(0,f.useCallback)((()=>{var o,t;const r=null===(o=(0,R.first)(null===e||void 0===e?void 0:e.versions))||void 0===o?void 0:o.version,n=null===e||void 0===e||null===(t=e.versions[1])||void 0===t?void 0:t.version;s({type:"setCompareMode",selectedVersion:n,comparedVersion:r})}),[e]),l=(0,f.useCallback)((()=>s({type:"switchSides"})),[]);return(0,R.first)(null===e||void 0===e?void 0:e.versions)&&o.mode===v.Dp.PREVIEW&&!o.selectedVersion&&r((0,R.first)(null===e||void 0===e?void 0:e.versions)),{viewState:o,setTableMode:t,setPreviewMode:r,setCompareMode:a,switchSides:l,setSelectedVersion:n,setComparedVersion:i}})(w),{mode:j}=B,K=!E&&!(null!==w&&void 0!==w&&w.versions.length),W=!E&&!K&&[v.Dp.PREVIEW,v.Dp.COMPARE].includes(j),z=null===w||void 0===w?void 0:w.versions.find((e=>{let{version:o}=e;return o===B.selectedVersion})),G=null===w||void 0===w?void 0:w.versions.find((e=>{let{version:o}=e;return o===B.comparedVersion})),Q=(0,f.useMemo)((()=>{var e,o;const s={};return null===w||void 0===w||null===(e=w.prompt)||void 0===e||null===(o=e.aliases)||void 0===o||o.forEach((e=>{let{alias:o,version:t}=e;s[t]||(s[t]=[]),s[t].push(o)})),s}),[w]),{EditAliasesModal:$,showEditAliasesModal:J}=(0,P.o)({model:(null===w||void 0===w?void 0:w.prompt)||null,onSuccess:y,modalTitle:fe,modalDescription:(0,A.Y)(g.A,{id:"4GPLHq",defaultMessage:"Aliases allow you to assign a mutable, named reference to a particular prompt version."})});if(S)return(0,A.Y)(ge,{promptName:o});const Z=(0,A.Y)(p.QpV,{children:(0,A.Y)(p.QpV.Item,{children:(0,A.Y)(d.N_,{to:h.h.promptsPageRoute,children:"Prompts"})})});return E?(0,A.Y)(c.m,{children:(0,A.Y)(Ae.Skeleton,{breadcrumbs:Z})}):(0,A.FD)(c.m,{css:he,children:[(0,A.Y)(u.S,{shrinks:!1}),(0,A.Y)(p.Y9Y,{breadcrumbs:Z,title:null===w||void 0===w||null===(e=w.prompt)||void 0===e?void 0:e.name,buttons:(0,A.FD)(A.FK,{children:[(0,A.FD)(p.rId.Root,{children:[(0,A.Y)(p.rId.Trigger,{asChild:!0,children:(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.actions",icon:(0,A.Y)(p.ssM,{}),"aria-label":"More actions"})}),(0,A.Y)(p.rId.Content,{children:(0,A.Y)(p.rId.Item,{componentId:"mlflow.prompts.details.actions.delete",onClick:_,children:(0,A.Y)(g.A,{id:"BwgPX0",defaultMessage:"Delete"})})})]}),(0,A.Y)(m.B,{componentId:"mlflow.prompts.details.create",type:"primary",onClick:D,children:(0,A.Y)(g.A,{id:"RJenO+",defaultMessage:"Create prompt version"})})]})}),(0,A.Y)(pe,{onTagsUpdated:y,promptEntity:null===w||void 0===w?void 0:w.prompt}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.FD)("div",{css:Ye,children:[(0,A.FD)("div",{css:(0,t.AH)({flex:W?"0 0 320px":1,display:"flex",flexDirection:"column"},""),children:[(0,A.Y)("div",{css:(0,t.AH)({display:"flex",gap:s.spacing.sm},""),children:(0,A.FD)(p.d98,{name:"mlflow.prompts.details.mode",componentId:"mlflow.prompts.details.mode",value:j,disabled:E,children:[(0,A.Y)(p.EPn,{value:v.Dp.PREVIEW,onClick:()=>N(),children:(0,A.FD)("div",{css:(0,t.AH)({display:"flex",alignItems:"center",gap:s.spacing.xs},""),children:[(0,A.Y)(p.ioF,{}),(0,A.Y)(g.A,{id:"wrNl6F",defaultMessage:"Preview"})]})}),(0,A.Y)(p.EPn,{value:v.Dp.TABLE,onClick:F,children:(0,A.FD)("div",{css:(0,t.AH)({display:"flex",alignItems:"center",gap:s.spacing.xs},""),children:[(0,A.Y)(p.KbA,{})," ",(0,A.Y)(g.A,{id:"guBsqD",defaultMessage:"List"})]})}),(0,A.Y)(p.EPn,{disabled:Boolean(!(null!==w&&void 0!==w&&w.versions.length)||(null===w||void 0===w?void 0:w.versions.length)<2),value:v.Dp.COMPARE,onClick:I,children:(0,A.FD)("div",{css:(0,t.AH)({display:"flex",alignItems:"center",gap:s.spacing.xs},""),children:[(0,A.Y)(p.jng,{})," ",(0,A.Y)(g.A,{id:"SI6n4L",defaultMessage:"Compare"})]})})]})}),(0,A.Y)(u.S,{shrinks:!1,size:"sm"}),(0,A.Y)(T,{isLoading:E,registeredPrompt:null===w||void 0===w?void 0:w.prompt,promptVersions:null===w||void 0===w?void 0:w.versions,selectedVersion:B.selectedVersion,comparedVersion:B.comparedVersion,showEditAliasesModal:J,aliasesByVersion:Q,onUpdateSelectedVersion:H,onUpdateComparedVersion:U,mode:j})]}),W&&(0,A.Y)("div",{css:Me,children:(0,A.FD)("div",{css:(0,t.AH)({borderLeft:`1px solid ${s.colors.border}`,flex:1,overflow:"hidden",display:"flex"},""),children:[j===v.Dp.PREVIEW&&(0,A.Y)(q,{promptVersion:z,onUpdatedContent:y,onDeletedVersion:async()=>{await y().then((e=>{let{data:o}=e;!(0,R.isEmpty)(null===o||void 0===o?void 0:o.versions)&&null!==o&&void 0!==o&&o.versions[0].version?H(null===o||void 0===o?void 0:o.versions[0].version):F()}))},aliasesByVersion:Q,showEditAliasesModal:J,registeredPrompt:null===w||void 0===w?void 0:w.prompt,showEditPromptVersionMetadataModal:C}),j===v.Dp.COMPARE&&(0,A.Y)(ae,{baselineVersion:z,comparedVersion:G,onSwitchSides:O,onEditVersion:N,showEditAliasesModal:J,registeredPrompt:null===w||void 0===w?void 0:w.prompt,aliasesByVersion:Q})]})})]}),(0,A.Y)(u.S,{shrinks:!1}),$,x,b,V]})};var we={name:"1gt3dvv",styles:"flex:0 0 320px"},ye={name:"82a6rk",styles:"flex:1"};Ae.Skeleton=function(e){let{breadcrumbs:o}=e;const{theme:s}=(0,m.u)();return(0,A.FD)(A.FK,{children:[(0,A.Y)(u.S,{shrinks:!1}),(0,A.Y)(p.Y9Y,{breadcrumbs:o,title:(0,A.Y)(p.xUE,{css:(0,t.AH)({height:s.general.heightBase,width:200},"")}),buttons:(0,A.Y)(p.xUE,{css:(0,t.AH)({height:s.general.heightBase,width:120},"")})}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.Y)(p.QvX,{lines:4}),(0,A.Y)(u.S,{shrinks:!1}),(0,A.FD)("div",{css:(0,t.AH)({display:"flex",gap:s.spacing.lg},""),children:[(0,A.Y)("div",{css:we,children:(0,A.Y)(p.QvX,{lines:6})}),(0,A.Y)("div",{css:ye,children:(0,A.Y)(p.QvX,{lines:4})})]})]})};var Ee=(0,le.X)(de.A.mlflowServices.EXPERIMENTS,Ae,void 0,ce.u)},81866:function(e,o,s){s.d(o,{$0:function(){return c},$p:function(){return m},BE:function(){return A},CG:function(){return h},Gs:function(){return f},IP:function(){return p},QQ:function(){return d},Qs:function(){return M},SF:function(){return Y},Tm:function(){return E},UA:function(){return v},e3:function(){return a},gL:function(){return y},jI:function(){return l},uB:function(){return w},zA:function(){return u},zr:function(){return g}});var t=s(48012),r=s(88443),n=s(24478),i=s(50111);const a={NONE:"None",STAGING:"Staging",PRODUCTION:"Production",ARCHIVED:"Archived"},l=[a.STAGING,a.PRODUCTION],d={[a.NONE]:"None",[a.STAGING]:"Staging",[a.PRODUCTION]:"Production",[a.ARCHIVED]:"Archived"},c={[a.NONE]:(0,i.Y)(t.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_37",children:d[a.NONE]}),[a.STAGING]:(0,i.Y)(t.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_38",color:"lemon",children:d[a.STAGING]}),[a.PRODUCTION]:(0,i.Y)(t.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_39",color:"lime",children:d[a.PRODUCTION]}),[a.ARCHIVED]:(0,i.Y)(t.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_40",color:"charcoal",children:d[a.ARCHIVED]})};let m=function(e){return e.APPLIED_TRANSITION="APPLIED_TRANSITION",e.REQUESTED_TRANSITION="REQUESTED_TRANSITION",e.SYSTEM_TRANSITION="SYSTEM_TRANSITION",e.CANCELLED_REQUEST="CANCELLED_REQUEST",e.APPROVED_REQUEST="APPROVED_REQUEST",e.REJECTED_REQUEST="REJECTED_REQUEST",e.NEW_COMMENT="NEW_COMMENT",e}({});(0,i.Y)("div",{style:{marginTop:-12},children:"_"});const p={READY:"READY"},u={[p.READY]:(0,i.Y)(r.A,{id:"f/An1W",defaultMessage:"Ready."})},g={[p.READY]:(0,i.Y)(r.A,{id:"zAvilr",defaultMessage:"Ready"})},v={[p.READY]:(0,i.Y)(n.vV,{})},f=1e4,h=25,Y=75,M="name",A="timestamp",w={ASC:"ascend",DESC:"descend"},y=e=>(0,i.Y)(r.A,{id:"Ll6vbT",defaultMessage:"Model versions in the `{currentStage}` stage will be moved to the `Archived` stage.",values:{currentStage:e}}),E="https://mlflow.org/docs/latest/model-registry.html#using-registered-model-aliases"},82832:function(e,o,s){s.d(o,{M:function(){return a}});var t=s(39416),r=s(53962),n=s(84565);const i=async e=>{let{reject:o,response:s,err:r}=e;const n=(0,t.a$)(s),i=n instanceof t.Bk?r:n;if(s)try{var a;const e=null===(a=await s.json())||void 0===a?void 0:a.message;e&&(i.message=e)}catch{}o(i)},a={listRegisteredPrompts:(e,o)=>{const s=new URLSearchParams;let t=`tags.\`${n.PS}\` = '${n.pY}'`;e&&(t=`${t} AND name ILIKE '%${e}%'`),o&&s.append("page_token",o),s.append("filter",t);const a=["ajax-api/2.0/mlflow/registered-models/search",s.toString()].join("?");return(0,r.AC)({relativeUrl:a,error:i})},setRegisteredPromptTag:(e,o,s)=>(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/set-tag",method:"POST",body:JSON.stringify({key:o,value:s,name:e}),error:i}),deleteRegisteredPromptTag:(e,o)=>(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete-tag",method:"DELETE",body:JSON.stringify({key:o,name:e}),error:i}),createRegisteredPrompt:e=>(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/create",method:"POST",body:JSON.stringify({name:e,tags:[{key:n.PS,value:n.pY}]}),error:i}),createRegisteredPromptVersion:function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=arguments.length>2?arguments[2]:void 0;return(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/create",method:"POST",body:JSON.stringify({name:e,description:s,source:"dummy-source",tags:[{key:n.PS,value:n.pY},...o]}),error:i})},setRegisteredPromptVersionTag:(e,o,s,t)=>(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/set-tag",method:"POST",body:JSON.stringify({key:s,value:t,name:e,version:o}),error:i}),deleteRegisteredPromptVersionTag:(e,o,s)=>{(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete-tag",method:"DELETE",body:JSON.stringify({key:s,name:e,version:o}),error:i})},getPromptDetails:e=>{const o=new URLSearchParams;o.append("name",e);const s=["ajax-api/2.0/mlflow/registered-models/get",o.toString()].join("?");return(0,r.AC)({relativeUrl:s,error:i})},getPromptVersions:e=>{const o=new URLSearchParams;o.append("filter",`name='${e}' AND tags.\`${n.PS}\` = '${n.pY}'`);const s=["ajax-api/2.0/mlflow/model-versions/search",o.toString()].join("?");return(0,r.AC)({relativeUrl:s,error:i})},getPromptVersionsForRun:e=>{const o=new URLSearchParams;o.append("filter",`tags.\`${n.PS}\` = '${n.pY}' AND tags.\`${n.xd}\` ILIKE "%${e}%"`);const s=["ajax-api/2.0/mlflow/model-versions/search",o.toString()].join("?");return(0,r.AC)({relativeUrl:s,error:i})},deleteRegisteredPrompt:e=>(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete",method:"DELETE",body:JSON.stringify({name:e}),error:i}),deleteRegisteredPromptVersion:(e,o)=>(0,r.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete",method:"DELETE",body:JSON.stringify({name:e,version:o}),error:i})}},83090:function(e,o,s){s.d(o,{i:function(){return l}});var t=s(77020),r=s(43683),n=s(82832),i=s(31014),a=s(98590);const l=e=>{let{onSuccess:o}=e;const s=(0,t.n)({mutationFn:async e=>{let{toAdd:o,toDelete:s,promptId:t}=e;return Promise.all([...o.map((e=>{let{key:o,value:s}=e;return n.M.setRegisteredPromptTag(t,o,s)})),...s.map((e=>{let{key:o}=e;return n.M.deleteRegisteredPromptTag(t,o)}))])}}),{EditTagsModal:l,showEditTagsModal:d,isLoading:c}=(0,r.Q)({valueRequired:!0,saveTagsHandler:(e,t,r)=>{const{addedOrModifiedTags:n,deletedTags:i}=(0,a.Rf)(t,r);return new Promise(((t,r)=>{if(!e.name)return r();s.mutate({promptId:e.name,toAdd:n,toDelete:i},{onSuccess:()=>{t(),null===o||void 0===o||o()},onError:r})}))}});return{EditTagsModal:l,showEditPromptTagsModal:(0,i.useCallback)((e=>d({name:e.name,tags:e.tags.filter((e=>(0,a.oD)(e.key)))})),[d]),isLoading:c}}},84069:function(e,o,s){s.d(o,{m:function(){return i}});var t=s(48012),r=s(50111);var n={name:"gmowil",styles:"height:calc(100% - 60px)"};const i=e=>{let{children:o,className:s}=e;return(0,r.Y)(t.ffj,{css:n,className:s,children:o})}},84565:function(e,o,s){s.d(o,{Dh:function(){return t},Dp:function(){return a},PS:function(){return n},dv:function(){return l},pY:function(){return i},xd:function(){return r}});const t="mlflow.prompt.text",r="mlflow.prompt.run_ids",n="mlflow.prompt.is_prompt",i="true";let a=function(e){return e.TABLE="table",e.PREVIEW="preview",e.COMPARE="compare",e}({});const l=e=>{var o,s;return null===e||void 0===e||null===(o=e.tags)||void 0===o||null===(s=o.find((e=>e.key===t)))||void 0===s?void 0:s.value}},91164:function(e,o,s){s.d(o,{Y:function(){return d}});var t=s(89555),r=s(32599),n=s(48012),i=s(64756),a=s(88443),l=s(50111);const d=e=>{let{aliases:o=[],onAddEdit:s,className:d}=e;const{theme:c}=(0,r.u)();return(0,l.Y)("div",{css:(0,t.AH)({maxWidth:300,display:"flex",flexWrap:"wrap",alignItems:"flex-start","> *":{marginRight:"0 !important"},rowGap:c.spacing.xs/2,columnGap:c.spacing.xs},""),className:d,children:o.length<1?(0,l.Y)(r.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversiontablealiasescell.tsx_30",size:"small",type:"link",onClick:s,children:(0,l.Y)(a.A,{id:"pU6cxH",defaultMessage:"Add"})}):(0,l.FD)(l.FK,{children:[o.map((e=>(0,l.Y)(i.m,{value:e,css:(0,t.AH)({marginTop:c.spacing.xs/2},"")},e))),(0,l.Y)(r.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversiontablealiasescell.tsx_41",size:"small",icon:(0,l.Y)(n.R2l,{}),onClick:s})]})})}}}]);
//# sourceMappingURL=532.b5248bdf.chunk.js.map