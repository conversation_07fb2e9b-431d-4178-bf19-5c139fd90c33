{"version": 3, "file": "static/css/252.df0eba4c.chunk.css", "mappings": "AAAA,kEACE,UAAW,CACX,eACF,CAEA,gDACE,yBACF,CAEA,6CACE,qBAA0B,CAC1B,cACF,CCZA,uBACE,eACF,CAEA,qCACE,gBACF,CAEA,YACE,eACF,CCVA,2BAGE,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CADhB,UAIF", "sources": ["model-registry/components/RegisterModelForm.css", "common/components/EditableNote.css", "common/components/RequestStateWrapper.css"], "sourcesContent": [".model-select-dropdown .ant-select-dropdown-menu-item-group-title {\n  color: #666;\n  font-weight: bold;\n}\n\n.model-select-dropdown .create-new-model-option {\n  border-top: 1px solid #ccc;\n}\n\n.register-model-form .modal-explanatory-text {\n  color: rgba(0, 0, 0, 0.52);\n  font-size: 13px;\n}\n", ".editable-note-actions {\n  margin-top: 16px;\n}\n\n.editable-note-actions button + button {\n  margin-left: 16px;\n}\n\n.mde-header {\n  background: none;\n}\n", ".RequestStateWrapper-error {\n  width: auto;\n  margin-top: 50px;\n  margin-left: auto;\n  margin-right: auto;\n}\n"], "names": [], "sourceRoot": ""}