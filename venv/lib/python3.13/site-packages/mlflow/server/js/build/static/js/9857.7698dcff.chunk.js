(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[9857],{688:function(e,t,n){"use strict";n.d(t,{f:function(){return f}});var r=n(89555),i=n(32599),o=n(48012),s=n(15579),a=n(36118),l=n(31014),c=n(20193),d=n(88464),u=n(88443),p=n(50111);var h={name:"1v0pok0",styles:"min-width:300px"},m={name:"11jf4ye",styles:"display:flex;gap:8px"},g={name:"bsla3r",styles:"max-height:300px;overflow:auto"};const f=e=>{let{globalLineChartConfig:t,metricKeyList:n,updateUIState:f}=e;const{theme:v}=(0,i.u)(),y=(0,d.A)(),{lineSmoothness:x,selectedXAxisMetricKey:C,xAxisKey:b}=t||{},_=(0,l.useCallback)((e=>f((t=>({...t,globalLineChartConfig:{...t.globalLineChartConfig,...e}})))),[f]),w=b===a.fj.METRIC,M=y.formatMessage({id:"Kkr/RI",defaultMessage:"Configure charts"});return(0,p.FD)(o.rId.Root,{modal:!1,children:[(0,p.Y)(s.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsglobalchartsettingsdropdown.tsx_44",content:M,children:(0,p.Y)(o.rId.Trigger,{asChild:!0,children:(0,p.Y)(i.B,{componentId:"mlflow.charts.controls.global_chart_setup_dropdown",icon:(0,p.Y)(o.L64,{}),"aria-label":M})})}),(0,p.FD)(o.rId.Content,{align:"end",css:h,children:[(0,p.FD)(o.rId.Group,{role:"region","aria-label":y.formatMessage({id:"Jp79WH",defaultMessage:"X-axis"}),children:[(0,p.Y)(o.rId.Label,{css:m,children:(0,p.Y)(u.A,{id:"Jp79WH",defaultMessage:"X-axis"})}),(0,p.FD)(o.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsglobalchartsettingsdropdown.tsx_68",checked:b===a.fj.STEP,onClick:()=>_({xAxisKey:a.fj.STEP}),children:[(0,p.Y)(o.rId.ItemIndicator,{}),(0,p.Y)(u.A,{id:"53b+wP",defaultMessage:"Step"})]}),(0,p.FD)(o.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsglobalchartsettingsdropdown.tsx_78",checked:b===a.fj.TIME,onClick:()=>_({xAxisKey:a.fj.TIME}),children:[(0,p.Y)(o.rId.ItemIndicator,{}),(0,p.Y)(u.A,{id:"/QYbzP",defaultMessage:"Time (wall)"})]}),(0,p.FD)(o.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsglobalchartsettingsdropdown.tsx_88",checked:b===a.fj.TIME_RELATIVE,onClick:()=>_({xAxisKey:a.fj.TIME_RELATIVE}),children:[(0,p.Y)(o.rId.ItemIndicator,{}),(0,p.Y)(u.A,{id:"sIC5K3",defaultMessage:"Time (relative)"})]}),(0,p.FD)(o.rId.Sub,{children:[(0,p.FD)(o.rId.SubTrigger,{css:(0,r.AH)({paddingLeft:v.spacing.xs+v.spacing.sm},""),children:[(0,p.Y)(o.rId.IconWrapper,{children:(0,p.Y)(s.C,{css:(0,r.AH)({visibility:w?"visible":"hidden"},"")})}),(0,p.Y)(u.A,{id:"yCncIr",defaultMessage:"Metric"})]}),(0,p.Y)(o.rId.SubContent,{css:g,children:n.map((e=>(0,p.FD)(o.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsglobalchartsettingsdropdown.tsx_118",checked:C===e&&w,onClick:()=>_({xAxisKey:a.fj.METRIC,selectedXAxisMetricKey:e}),children:[(0,p.Y)(o.rId.ItemIndicator,{}),e]},e)))})]})]}),(0,p.FD)(o.rId.Group,{role:"region","aria-label":y.formatMessage({id:"qkRBUr",defaultMessage:"Line smoothing"}),children:[(0,p.Y)(o.rId.Label,{children:(0,p.Y)(u.A,{id:"qkRBUr",defaultMessage:"Line smoothing"})}),(0,p.Y)("div",{css:(0,r.AH)({padding:v.spacing.sm},""),children:(0,p.Y)(c.o,{min:0,max:100,onChange:e=>_({lineSmoothness:e}),value:x||0})})]})]})]})}},1670:function(e,t,n){"use strict";n.d(t,{h:function(){return l}});var r,i,o=n(31014);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(null,arguments)}function a(e,t){let{title:n,titleId:a,...l}=e;return o.createElement("svg",s({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":a},l),n?o.createElement("title",{id:a},n):null,r||(r=o.createElement("path",{d:"M8 3C3.45 3 1.4375 6.33333 1 8C1.4375 9.66667 3.45 13 8 13C12.55 13 14.5625 9.66667 15 8C14.5625 6.33333 12.55 3 8 3Z",fill:"currentColor",stroke:"currentColor"})),i||(i=o.createElement("circle",{cx:8,cy:8,r:2.25,fill:"currentColor",stroke:"white",strokeWidth:1.5})))}const l=o.forwardRef(a);n.p},3546:function(e,t,n){"use strict";n.d(t,{ts:function(){return u},sO:function(){return d},sR:function(){return p},Px:function(){return h}});var r=n(9133),i=n(31014),o=n(93215),s=n(83028),a=n(45653);const l=e=>(0,r.isArray)(e)?e.join():e,c={searchFilter:{deserializeLocalStorage:l,deserializeQueryString:l},orderByAsc:{serializeQueryString(e){return e.toString()},deserializeQueryString(e){return"true"===e}},datasetsFilter:{serializeQueryString(e){const t=e.map((e=>{let{name:t,digest:n,context:r}=e;return{name:t,digest:n,context:r}}));return(0,a.Qr)(JSON.stringify(t))},deserializeQueryString(e){try{const t=JSON.parse((0,a.yv)(e));return Array.isArray(t)?t:[]}catch{return[]}}},compareRunCharts:{serializeQueryString(e){return(0,a.Qr)(JSON.stringify(e))},deserializeQueryString(e){try{const t=JSON.parse((0,a.yv)(e));if(!Array.isArray(t))return;return t}catch{return}}},compareRunsMode:{serializeLocalStorage(){}}},d=["searchFilter","orderByKey","orderByAsc","startTime","lifecycleFilter","modelVersionFilter","datasetsFilter"],u="isPreview",p=()=>{const[e]=(0,o.ok)(),t=(0,i.useMemo)((()=>(0,r.pick)(Object.fromEntries(e.entries()),d)),[e]),n="true"===e.get(u),{searchFilter:a,orderByKey:l,orderByAsc:p,startTime:h,lifecycleFilter:m,modelVersionFilter:g,datasetsFilter:f}=t,v=(0,r.keys)(t).length<1,{experimentId:y}=(0,o.g)(),x=e.get("experiments"),C=(0,i.useMemo)((()=>y?[y]:x?(e=>{try{return e?JSON.parse(e):[]}catch{return[]}})(x):[]),[y,x]);return[(0,i.useMemo)((()=>{if(v)return null;const e=(e=>{const t={...e};for(const r of Object.keys(t)){var n;const e=null===(n=c[r])||void 0===n?void 0:n.deserializeQueryString;e&&(t[r]=e(t[r]))}return t})((0,r.omitBy)({searchFilter:a,orderByKey:l,orderByAsc:p,startTime:h,lifecycleFilter:m,modelVersionFilter:g,datasetsFilter:f},r.isNil));return(0,r.assign)((0,s.G)(),e)}),[a,l,p,h,m,g,f,v]),C,n]},h=()=>{const[,e]=(0,o.ok)();return(t,n)=>{const i=(e=>{const t={...e};for(const r of Object.keys(t)){var n;const e=null===(n=c[r])||void 0===n?void 0:n.serializeQueryString;e&&(t[r]=e(t[r]))}return t})(t);e((e=>((0,r.entries)(i).forEach((t=>{let[n,r]=t;e.set(n,r)})),e.delete(u),e)),n)}}},4972:function(e,t,n){"use strict";n.d(t,{$:function(){return i}});var r=n(31014);const i=()=>{const[e,t]=(0,r.useState)("visible"===document.visibilityState);return(0,r.useEffect)((()=>{document.addEventListener("visibilitychange",(e=>{t("visible"===document.visibilityState)}))}),[]),e}},5900:function(e,t,n){"use strict";n.d(t,{i:function(){return xe}});var r=n(31014),i=n(30152),o=n(91144),s=n(59264),a=n(36118),l=n(73724),c=n(75627),d=n(6922),u=n(24789),p=n(9133);const h=(e,t,n,r)=>{const i=(0,p.compact)(t.map((t=>{var i;const o=null===(i=e[t])||void 0===i?void 0:i[n];return o?function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const r=(0,p.orderBy)((0,p.compact)((0,p.values)(e).map((e=>{let{metricsHistory:t}=e;return t})).flat()),"timestamp");if(r.length<2)return;const i=new Date(t[0]).valueOf(),o=new Date(t[1]).valueOf();let s=n?(0,p.first)(r):void 0;for(let l=0;l<r.length;l++){const e=r[l];if(e.timestamp>i){s=r[l-1]||e;break}}let a=n?(0,p.last)(r):void 0;for(let l=r.length-1;l>=0;l--){const e=r[l];if(e.timestamp<o){a=r[l+1]||e;break}}return(0,p.isUndefined)(s)||(0,p.isUndefined)(a)?void 0:[s.step,a.step]}(o,r,!1):void 0}))),o=(0,p.min)(i.map((e=>{let[t]=e;return t}))),s=(0,p.max)(i.map((e=>{let[,t]=e;return t})));if(!(0,p.isUndefined)(o)&&!(0,p.isUndefined)(s))return[o,s]},m=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e3;const i=(0,p.compact)(t.map((t=>{var i;const o=e[t];if(!o)return null;const{runUuid:s,...a}=o,l=(0,p.values)(a).flatMap((e=>{var t;return null!==(t=e.metricsHistory)&&void 0!==t?t:[]})),c=(null===(i=(0,p.minBy)(l,"timestamp"))||void 0===i?void 0:i.timestamp)||0;return[n[0]*r+c,n[1]*r+c]}))),o=(0,p.min)(i.map((e=>{let[t]=e;return t}))),s=(0,p.max)(i.map((e=>{let[,t]=e;return t})));if(!(0,p.isUndefined)(o)&&!(0,p.isUndefined)(s))return[o,s]};var g=n(10811);var f=n(81850),v=n(50684),y=n(21616),x=n(75145),C=n(6531),b=n(50111);const _=e=>{if((0,o.Iq)()&&e.yAxisKey===i.Kb.EXPRESSION){var t;const n=(null===(t=e.yAxisExpressions)||void 0===t?void 0:t.map((e=>e.expression)))||[];return(null===n||void 0===n?void 0:n.join(" vs "))||""}return e.selectedMetricKeys&&0!==e.selectedMetricKeys.length?e.selectedMetricKeys.join(" vs "):e.metricKey},w=["png","svg","csv","csv-full"],M=e=>{var t;let{config:n,chartRunData:s,onDelete:M,onEdit:I,onDownloadFullMetricHistoryCsv:E,groupBy:R,fullScreen:A,setFullScreenChart:k,autoRefreshEnabled:Y,hideEmptyCharts:T,globalLineChartConfig:D,isInViewport:L,isInViewportDeferred:F,positionInSection:K,...N}=e;const{xAxisKey:H,selectedXAxisMetricKey:O,lineSmoothness:B}=(0,C.x)(n,D),P=(0,r.useCallback)((()=>{null===k||void 0===k||k({config:n,title:_(n),subtitle:null})}),[n,k]),U=(0,r.useMemo)((()=>s.filter((e=>{let{hidden:t}=e;return!t})).reverse()),[s]),z=(0,r.useMemo)((()=>U.some((e=>e.groupParentInfo))),[U]),j=(0,r.useMemo)((()=>{var e;const t=null!==(e=n.selectedMetricKeys)&&void 0!==e?e:[n.metricKey],r=U.flatMap((e=>{let{metrics:t}=e;return Object.keys(t)}));return 0===(0,p.intersection)(t,(0,p.uniq)(r)).length}),[n,U]),G=(0,r.useMemo)((()=>{if(z){return[...(0,p.compact)(U.map((e=>e.groupParentInfo))).flatMap((e=>e.runUuids)),...(0,p.compact)(U.filter((e=>!e.groupParentInfo&&!e.belongsToGroup)).map((e=>{var t;return null===(t=e.runInfo)||void 0===t?void 0:t.runUuid})))]}return(0,p.compact)(U.map((e=>{var t;return null===(t=e.runInfo)||void 0===t?void 0:t.runUuid})))}),[U,z]),V=(0,r.useMemo)((()=>{const e=(e=>{var t;const n=[e.metricKey];var r;if(!(0,o.Iq)()||e.yAxisKey!==i.Kb.EXPRESSION)return null!==(r=e.selectedMetricKeys)&&void 0!==r?r:n;const s=null===(t=e.yAxisExpressions)||void 0===t?void 0:t.reduce(((e,t)=>(t.variables.forEach((t=>e.add(t))),e)),new Set);return void 0===s?n:Array.from(s)})(n),t=O?[O]:[];return e.concat(t)}),[n,O]),{setTooltip:W,resetTooltip:$,destroyTooltip:q,selectedRunUuid:X}=(0,c.uN)(n,c.QS.MultipleTracesWithScanline),Q=A||L,J=A||F,{aggregateFunction:Z}=R||{},ee=(0,g.d4)((e=>(0,p.pick)(e.entities.sampledMetricsByRunUuid,G)),g.bN),[te,ne]=(0,r.useState)((()=>{if(n.range&&!(0,p.isUndefined)(n.range.yMin)&&!(0,p.isUndefined)(n.range.yMax))return[n.range.yMin,n.range.yMax]})),{setOffsetTimestamp:re,stepRange:ie,xRangeLocal:oe,setXRangeLocal:se}=function(e,t,n,i,o){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"linear";const[l,c]=(0,r.useState)((()=>{if(e.range&&!(0,p.isUndefined)(e.range.xMin)&&!(0,p.isUndefined)(e.range.xMax))return[e.range.xMin,e.range.xMax]})),[d,u]=(0,r.useState)(void 0);return{setOffsetTimestamp:u,stepRange:(0,r.useMemo)((()=>{if(l){if(t===a.fj.TIME&&(0,p.isString)(l[0])&&(0,p.isString)(l[1]))return h(i,o,n,l);if(t===a.fj.TIME_RELATIVE&&d&&(0,p.isNumber)(l[0])&&(0,p.isNumber)(l[1]))return h(i,o,n,d);if(t===a.fj.STEP&&(0,p.isNumber)(l[0])&&(0,p.isNumber)(l[1])){const e=Math.floor("log"===s?10**l[0]:l[0]),t=Math.ceil("log"===s?10**l[1]:l[1]);return e&&t?[e-1,t+1]:void 0}}}),[t,n,l,i,o,d,s]),xRangeLocal:l,setXRangeLocal:c}}(n,H,n.metricKey,ee,G,H===a.fj.STEP?n.xAxisScaleType:"linear"),{resultsByRunUuid:ae,isLoading:le,isRefreshing:ce}=(0,u.S)({runUuids:G,metricKeys:V,enabled:J,maxResults:320,range:ie,autoRefreshEnabled:Y});(0,r.useEffect)((()=>{q()}),[q,le]);const de=(0,r.useMemo)((()=>U.map((e=>{const t=V.reduce(((t,r)=>{var i,o;const s=null===(i=ae[e.uuid])||void 0===i||null===(o=i[r])||void 0===o?void 0:o.metricsHistory;return s&&(t[r]=n.ignoreOutliers?(0,a.c1)(s):s),t}),{});return{...e,metricsHistory:t}}))),[V,ae,U,n.ignoreOutliers]),ue=(0,f.B)({enabled:z,ungroupedRunsData:de,metricKeys:V,sampledDataResultsByRunUuid:ae,aggregateFunction:Z,selectedXAxisMetricKey:H===a.fj.METRIC?O:void 0,ignoreOutliers:null!==(t=n.ignoreOutliers)&&void 0!==t&&t}),pe=z?ue:de,[he,me]=(0,v.H)(),ge=Q,fe=le||!J,ve=(0,b.Y)("div",{css:[S.lineChartCardWrapper,{height:A?"100%":void 0},""],children:ge?fe?(0,b.Y)(d.Us,{}):(0,b.Y)(l.h,{runsData:pe,metricKey:n.metricKey,selectedMetricKeys:n.selectedMetricKeys,scaleType:n.scaleType,xAxisKey:H,xAxisScaleType:n.xAxisScaleType,yAxisKey:n.yAxisKey,yAxisExpressions:n.yAxisExpressions,selectedXAxisMetricKey:O,lineSmoothness:B,useDefaultHoverBox:!1,onHover:W,onUnhover:$,selectedRunUuid:X,onUpdate:e=>{let{layout:t}=e;if(!A){let e=null===te||void 0===te?void 0:te[0],n=null===te||void 0===te?void 0:te[1],r=null===oe||void 0===oe?void 0:oe[0],i=null===oe||void 0===oe?void 0:oe[1];const{autorange:s,range:l}=t.yaxis||{};!(0,p.isEqual)(s?[void 0,void 0]:l,[e,n])&&q(),s?(e=void 0,n=void 0):l&&(e=l[0],n=l[1]);const{autorange:c,range:d}=t.xaxis||{};if(c)r=void 0,i=void 0;else if(d){const e=(0,p.compact)(U.map((e=>{let{runInfo:t}=e;return null===t||void 0===t?void 0:t.runUuid}))),t=U.flatMap((e=>{var t;let{groupParentInfo:n}=e;return null!==(t=null===n||void 0===n?void 0:n.runUuids)&&void 0!==t?t:[]}));if((0,o.v$)()||H!==a.fj.TIME_RELATIVE)if(H===a.fj.TIME_RELATIVE_HOURS){const n=m(ae,[...e,...t],d,36e5);re([...n])}else re(void 0);else{const n=m(ae,[...e,...t],d);re([...n])}r=d[0],i=d[1]}(0,p.isEqual)({xMin:null===oe||void 0===oe?void 0:oe[0],xMax:null===oe||void 0===oe?void 0:oe[1],yMin:null===te||void 0===te?void 0:te[0],yMax:null===te||void 0===te?void 0:te[1]},{xMin:r,xMax:i,yMin:e,yMax:n})||(se((0,p.isUndefined)(r)||(0,p.isUndefined)(i)?void 0:[r,i]),ne((0,p.isUndefined)(e)||(0,p.isUndefined)(n)?void 0:[e,n]))}},xRange:oe,yRange:te,fullScreen:A,displayPoints:n.displayPoints,onSetDownloadHandler:me,positionInSection:null!==K&&void 0!==K?K:0}):null}),ye=(0,r.useCallback)((e=>{var t,r;const i=null!==(t=null===(r=n.selectedMetricKeys)||void 0===r?void 0:r.join("-"))&&void 0!==t?t:n.metricKey;if("csv-full"!==e)"csv"!==e?null===he||void 0===he||he(e,i):(0,y.Fz)(pe,n.selectedMetricKeys||[n.metricKey],i);else{const e=[...(0,p.compact)(pe.map((e=>{var t;return null===(t=e.runInfo)||void 0===t?void 0:t.runUuid}))),...(0,p.compact)(pe.filter((e=>{let{groupParentInfo:t}=e;return t})).flatMap((e=>{var t;return null===(t=e.groupParentInfo)||void 0===t?void 0:t.runUuids})))];null===E||void 0===E||E(e,n.selectedMetricKeys||[n.metricKey])}}),[pe,n,he,E]);return T&&j?null:A?ve:(0,b.Y)(d.Ox,{onEdit:I,onDelete:M,title:_(n),uuid:n.uuid,dragGroupKey:d.cN.GENERAL_AREA,supportedDownloadFormats:w,onClickDownload:ye,toggleFullScreenChart:j?void 0:P,isRefreshing:ce,isHidden:!Q,...N,children:j?(0,b.Y)(x.u,{}):ve})},S={lineChartCardWrapper:{overflow:"hidden"}};var I=n(89555),E=n(10661),R=n(32599),A=n(48012);var k={name:"1wcfv52",styles:"margin-right:0"},Y={name:"1wcfv52",styles:"margin-right:0"};const T=e=>{let{config:t,chartRunData:n,onDelete:i,onEdit:o,fullScreen:s,setFullScreenChart:a,hideEmptyCharts:l,isInViewport:u,...h}=e;const{theme:m}=(0,R.u)(),g=t.xaxis.datasetName||t.yaxis.datasetName?(0,b.FD)("div",{css:(0,I.AH)({flex:1,display:"flex",alignItems:"center",overflow:"hidden",gap:m.spacing.xs},""),children:[(0,b.FD)(R.T.Text,{title:t.xaxis.key,ellipsis:!0,bold:!0,children:[t.xaxis.datasetName&&(0,b.FD)(b.FK,{children:[(0,b.Y)(A.vwO,{componentId:"mlflow.charts.scatter_card_title.dataset_tag",css:k,children:t.xaxis.datasetName})," "]}),t.xaxis.key]}),(0,b.Y)(R.T.Text,{children:"vs"}),(0,b.FD)(R.T.Text,{title:t.xaxis.key,ellipsis:!0,bold:!0,children:[t.yaxis.datasetName&&(0,b.FD)(b.FK,{children:[(0,b.Y)(A.vwO,{componentId:"mlflow.charts.scatter_card_title.dataset_tag",css:Y,children:t.yaxis.datasetName})," "]}),t.yaxis.key]})]}):`${t.xaxis.key} vs. ${t.yaxis.key}`,f=(0,r.useMemo)((()=>n.filter((e=>{let{hidden:t}=e;return!t}))),[n]),C=(0,r.useMemo)((()=>{var e,n;const r=[null!==(e=t.xaxis.dataAccessKey)&&void 0!==e?e:t.xaxis.key,null!==(n=t.yaxis.dataAccessKey)&&void 0!==n?n:t.yaxis.key],i=f.flatMap((e=>{let{metrics:t}=e;return Object.keys(t)}));return 0===(0,p.intersection)(r,(0,p.uniq)(i)).length}),[t,f]),{setTooltip:_,resetTooltip:w,selectedRunUuid:M}=(0,c.uN)(t),S=s||u,[T,L]=(0,v.H)(),F=(0,b.Y)("div",{css:[D.scatterChartCardWrapper,{height:s?"100%":void 0},""],children:S?(0,b.Y)(E.i,{runsData:f,xAxis:t.xaxis,yAxis:t.yaxis,onHover:_,onUnhover:w,useDefaultHoverBox:!1,selectedRunUuid:M,onSetDownloadHandler:L}):null});return l&&C?null:s?F:(0,b.Y)(d.Ox,{onEdit:o,onDelete:i,title:g,uuid:t.uuid,dragGroupKey:d.cN.GENERAL_AREA,toggleFullScreenChart:C?void 0:()=>{null===a||void 0===a||a({config:t,title:g,subtitle:null})},supportedDownloadFormats:["png","svg","csv"],onClickDownload:e=>{const n=[t.xaxis.key,t.yaxis.key].join("-");if("csv"!==e&&"csv-full"!==e)null===T||void 0===T||T(e,n);else{const e=[],r=[];for(const n of["xaxis","yaxis"])"PARAM"===t[n].type?e.push(t[n].key):r.push(t[n].key);(0,y.LL)(f,r,e,n)}},...h,children:C?(0,b.Y)(x.u,{}):F})},D={scatterChartCardWrapper:{overflow:"hidden"}};var L=n(66117);const F=e=>{let{config:t,chartRunData:n,onDelete:i,onEdit:o,fullScreen:s,setFullScreenChart:a,hideEmptyCharts:l,...u}=e;const h=`${t.xaxis.key} vs. ${t.yaxis.key} vs. ${t.zaxis.key}`,m=(0,r.useMemo)((()=>n.filter((e=>{let{hidden:t}=e;return!t}))),[n]),g=(0,r.useMemo)((()=>{const e=[t.xaxis.key,t.yaxis.key,t.zaxis.key],n=m.flatMap((e=>{let{metrics:t}=e;return Object.keys(t)}));return 0===(0,p.intersection)(e,(0,p.uniq)(n)).length}),[t,m]),{setTooltip:f,resetTooltip:C,selectedRunUuid:_}=(0,c.uN)(t),[w,M]=(0,v.H)(),S=(0,b.Y)("div",{css:[K.contourChartCardWrapper,{height:s?"100%":void 0},""],children:(0,b.Y)(L.a,{runsData:m,xAxis:t.xaxis,yAxis:t.yaxis,zAxis:t.zaxis,useDefaultHoverBox:!1,onHover:f,onUnhover:C,selectedRunUuid:_,onSetDownloadHandler:M})});return l&&g?null:s?S:(0,b.Y)(d.Ox,{onEdit:o,onDelete:i,title:h,uuid:t.uuid,dragGroupKey:d.cN.GENERAL_AREA,toggleFullScreenChart:g?void 0:()=>{null===a||void 0===a||a({config:t,title:h,subtitle:null})},supportedDownloadFormats:["png","svg","csv"],onClickDownload:e=>{const n=[t.xaxis.key,t.yaxis.key,t.zaxis.key].join("-");if("csv"!==e&&"csv-full"!==e)null===w||void 0===w||w(e,n);else{const e=[],r=[];for(const n of["xaxis","yaxis","zaxis"])"PARAM"===t[n].type?e.push(t[n].key):r.push(t[n].key);(0,y.LL)(m,r,e,n)}},...u,children:g?(0,b.Y)(x.u,{}):S})},K={contourChartCardWrapper:{overflow:"hidden"}};var N,H,O,B;function P(){return P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},P.apply(null,arguments)}function U(e,t){let{title:n,titleId:i,...o}=e;return r.createElement("svg",P({width:80,height:42,viewBox:"0 0 80 42",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":i},o),n?r.createElement("title",{id:i},n):null,N||(N=r.createElement("path",{d:"M1.28418 2.34094L39.4998 17.0908L76.3746 7.87216",stroke:"#CF797B",strokeLinecap:"round"})),H||(H=r.createElement("path",{d:"M1.95459 20.443L40.1702 25.1361L77.045 21.1134M1.95459 36.5338L40.1702 35.1929L77.045 25.1361",stroke:"#A88E6F",strokeLinecap:"round"})),O||(O=r.createElement("g",{opacity:.8},r.createElement("path",{d:"M1.28418 1V40.3341",stroke:"#338ECC",strokeLinecap:"round"}),r.createElement("path",{d:"M40.6189 2.07251V39.9763",stroke:"#338ECC",strokeLinecap:"round"}),r.createElement("path",{d:"M77.7156 2.07251V39.9763",stroke:"url(#paint0_linear_2781_406689)",strokeWidth:4,strokeLinecap:"square"}))),B||(B=r.createElement("defs",null,r.createElement("linearGradient",{id:"paint0_linear_2781_406689",x1:78.2156,y1:2.07251,x2:78.2156,y2:39.9763,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{stopColor:"#D04F5E"}),r.createElement("stop",{offset:1,stopColor:"#4D9734"})))))}const z=r.forwardRef(U);n.p;var j=n(21809),G=n(70391),V=n(88443),W=n(21879);var $={name:"1neqb0a",styles:"display:flex;flex:1;justify-content:center;align-items:center"},q={name:"10zh1lj",styles:"display:flex;flex-direction:column;align-items:center;max-width:360px"};const X=e=>{let{onEdit:t}=e;const{theme:n}=(0,R.u)();return(0,b.Y)("div",{css:$,children:(0,b.FD)("div",{css:q,children:[(0,b.Y)(z,{}),(0,b.Y)(R.T.Title,{css:(0,I.AH)({marginTop:n.spacing.md},""),color:"secondary",level:3,children:(0,b.Y)(V.A,{id:"VPU43C",defaultMessage:"Compare parameter importance"})}),(0,b.Y)(R.T.Text,{css:(0,I.AH)({marginBottom:n.spacing.md},""),color:"secondary",children:(0,b.Y)(V.A,{id:"V9FtFz",defaultMessage:"Use the parallel coordinates chart to compare how various parameters in model affect your model metrics."})}),(0,b.Y)(R.B,{componentId:"mlflow.charts.parallel_coords_chart_configure_button",type:"primary",onClick:t,children:(0,b.Y)(V.A,{id:"ga32oj",defaultMessage:"Configure chart"})})]})})};var Q={name:"1neqb0a",styles:"display:flex;flex:1;justify-content:center;align-items:center"},J={name:"10zh1lj",styles:"display:flex;flex-direction:column;align-items:center;max-width:360px"};const Z=()=>{const{theme:e}=(0,R.u)();return(0,b.Y)("div",{css:Q,children:(0,b.FD)("div",{css:J,children:[(0,b.Y)(z,{}),(0,b.Y)(R.T.Title,{css:(0,I.AH)({marginTop:e.spacing.md,textAlign:"center"},""),color:"secondary",level:3,children:(0,b.Y)(V.A,{id:"glxGz2",defaultMessage:"Parallel coordinates chart does not support aggregated string values."})}),(0,b.Y)(R.T.Text,{css:(0,I.AH)({marginBottom:e.spacing.md},""),color:"secondary",children:(0,b.Y)(V.A,{id:"QPC4hV",defaultMessage:"Use other parameters or disable run grouping to continue."})})]})})};var ee={name:"82a6rk",styles:"flex:1"};const te=e=>{let{config:t,chartRunData:n,onDelete:i,onEdit:o,groupBy:s,fullScreen:a,setFullScreenChart:l,hideEmptyCharts:u,isInViewport:p,...h}=e;const m=(0,W.e)(),g=(0,r.useMemo)((()=>null!==t&&void 0!==t&&t.showAllRuns?n:null===n||void 0===n?void 0:n.filter((e=>{let{hidden:t}=e;return!t}))),[n,null===t||void 0===t?void 0:t.showAllRuns]),f=(0,r.useMemo)((()=>{var e;return null===(e=t.selectedParams)||void 0===e?void 0:e.some((e=>null===g||void 0===g?void 0:g.some((t=>{var n;return isNaN(Number(null===(n=t.params[e])||void 0===n?void 0:n.value))}))),[t.selectedParams,g])}),[t.selectedParams,g]),v=(0,r.useCallback)((e=>{m((n=>{var r;const i=null===(r=n.compareRunCharts)||void 0===r?void 0:r.map((n=>{if(n.uuid===t.uuid){return{...n,showAllRuns:e}}return n}));return{...n,compareRunCharts:i}}))}),[t.uuid,m]),[C,_]=(0,r.useMemo)((()=>{const e=(0,G.ad)(t);return[e,e?(0,G.fM)(g,t.selectedParams,t.selectedMetrics):[]]}),[t,g]),w=(0,r.useMemo)((()=>0===_.length),[_]),M=a||p,{setTooltip:S,resetTooltip:I,selectedRunUuid:E,closeContextMenu:R}=(0,c.uN)(t),k=f&&s,Y=C&&!k,T=(0,b.Y)(b.FK,{children:t.showAllRuns?(0,b.Y)(V.A,{id:"oY+vPi",defaultMessage:"Showing all runs"}):(0,b.Y)(V.A,{id:"1hI96w",defaultMessage:"Showing only visible runs"})}),D=(0,b.Y)(b.FK,{children:C?k?(0,b.Y)(Z,{}):0===_.length?(0,b.Y)(x.u,{}):(0,b.Y)("div",{css:[ne.parallelChartCardWrapper,{height:a?"100%":void 0},""],children:M?(0,b.Y)(j.A,{data:_,selectedParams:t.selectedParams,selectedMetrics:t.selectedMetrics,onHover:S,onUnhover:I,axesRotateThreshold:8,selectedRunUuid:E,closeContextMenu:R,fallback:(0,b.Y)(d.Us,{css:ee})}):null}):(0,b.Y)(X,{onEdit:o})});if(a)return D;if(u&&w)return null;const L=C&&!k&&!w;return(0,b.Y)(d.Ox,{onEdit:o,onDelete:i,title:"Parallel Coordinates",subtitle:Y?T:null,uuid:t.uuid,tooltip:(0,b.Y)(V.A,{id:"XvzXs3",defaultMessage:"The parallel coordinates chart shows runs with columns that are either numbers or strings. If a column has string entries, the runs corresponding to the 30 most recent unique values will be shown. Only runs with all relevant metrics and/or parameters will be displayed."}),dragGroupKey:d.cN.PARALLEL_CHARTS_AREA,toggleFullScreenChart:L?()=>{null===l||void 0===l||l({config:t,title:"Parallel Coordinates",subtitle:Y?T:null})}:void 0,additionalMenuContent:(0,b.FD)(b.FK,{children:[(0,b.Y)(A.rId.Separator,{}),(0,b.FD)(A.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_runschartsparallelchartcard.tsx_293",checked:!t.showAllRuns,onClick:()=>v(!1),children:[(0,b.Y)(A.rId.ItemIndicator,{}),(0,b.Y)(V.A,{id:"d8I3cy",defaultMessage:"Show only visible"})]}),(0,b.FD)(A.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_runschartsparallelchartcard.tsx_300",checked:t.showAllRuns,onClick:()=>v(!0),children:[(0,b.Y)(A.rId.ItemIndicator,{}),(0,b.Y)(V.A,{id:"e7K2T3",defaultMessage:"Show all runs"})]})]}),supportedDownloadFormats:["csv"],onClickDownload:e=>{const n=[...t.selectedMetrics,...t.selectedParams].join("-");"csv"===e&&(0,y.LL)(g,t.selectedMetrics,t.selectedParams,n)},...h,children:D})},ne={parallelChartCardWrapper:{display:"flex",overflow:"hidden",cursor:"pointer"}};var re=n(62758),ie=n(88464),oe=n(31276);var se={name:"1neqb0a",styles:"display:flex;flex:1;justify-content:center;align-items:center"},ae={name:"10zh1lj",styles:"display:flex;flex-direction:column;align-items:center;max-width:360px"};const le=e=>{let{onEdit:t}=e;const{theme:n}=(0,R.u)();return(0,b.Y)("div",{css:se,children:(0,b.FD)("div",{css:ae,children:[(0,b.Y)(R.T.Title,{css:(0,I.AH)({marginTop:n.spacing.md},""),color:"secondary",level:3,children:(0,b.Y)(V.A,{id:"RCjxf0",defaultMessage:"Compare runs"})}),(0,b.Y)(R.T.Text,{css:(0,I.AH)({marginBottom:n.spacing.md},""),color:"secondary",children:(0,b.Y)(V.A,{id:"sblqXA",defaultMessage:"Use the runs difference view to compare model and system metrics, parameters, attributes, and tags across runs."})}),(0,b.Y)(R.B,{componentId:"mlflow.charts.difference_chart_configure_button",type:"primary",onClick:t,children:(0,b.Y)(V.A,{id:"NDEhn/",defaultMessage:"Configure chart"})})]})})},ce=e=>{let{config:t,chartRunData:n,onDelete:o,onEdit:s,groupBy:a,fullScreen:l,setFullScreenChart:c,hideEmptyCharts:u,...p}=e;const[h,m]=(0,r.useMemo)((()=>{var e;return[Boolean(null===(e=t.compareGroups)||void 0===e?void 0:e.length),n.filter((e=>{let{hidden:t}=e;return!t})).reverse()]}),[n,t]),g=(0,r.useMemo)((()=>!h),[h]),f=(0,re.Ez)(),v=(0,r.useCallback)((()=>{f({...t,showChangeFromBaseline:!t.showChangeFromBaseline})}),[t,f]),y=(0,r.useCallback)((()=>{f({...t,showDifferencesOnly:!t.showDifferencesOnly})}),[t,f]),{formatMessage:x}=(0,ie.A)(),C=(0,b.Y)(b.FK,{children:h?(0,b.Y)(oe.I,{previewData:m,groupBy:a,cardConfig:t,setCardConfig:e=>{f(e(t))}}):(0,b.Y)(le,{onEdit:s})});let _;return a&&i.Nf.some((e=>t.compareGroups.includes(e)))&&(_=x({id:"csNr/8",defaultMessage:"Disable grouped runs to compare parameters, tag, or attributes"})),l?C:u&&g?null:(0,b.Y)(d.Ox,{onEdit:s,onDelete:o,title:t.chartName,uuid:t.uuid,dragGroupKey:d.cN.GENERAL_AREA,toggleFullScreenChart:()=>{null===c||void 0===c||c({config:t,title:t.chartName,subtitle:null})},toggles:[{toggleLabel:x({id:"4tpkvx",defaultMessage:"Show change from baseline"}),currentToggle:t.showChangeFromBaseline,setToggle:v},{toggleLabel:x({id:"e7GTKi",defaultMessage:"Show differences only"}),currentToggle:t.showDifferencesOnly,setToggle:y}],tooltip:_,...p,children:C})};var de=n(34389),ue=n(99061),pe=n(25866),he=n(20193);var me={name:"1tc6xju",styles:"flex:1;overflow:auto"},ge={name:"82a6rk",styles:"flex:1"},fe={name:"1hfe3dm",styles:'&[data-orientation="horizontal"]{width:auto;}'};const ve=e=>{let{config:t,chartRunData:n,onDelete:i,onEdit:o,groupBy:s,fullScreen:a,setFullScreenChart:l,...c}=e;const{theme:u}=(0,R.u)(),p=(0,r.useRef)(null),[h,m]=(0,r.useState)(0),[g,f]=(0,r.useState)(t),v=(0,re.Ez)(),y=(0,r.useCallback)((e=>{v({...t,step:e})}),[t,v]),x=1===t.imageKeys.length?t.imageKeys[0]:pe._g,C=(0,r.useMemo)((()=>n.filter((e=>{let{hidden:t}=e;return!t})).reverse()),[n]),{stepMarks:_,maxMark:w,minMark:M}=(0,ue._)({data:C,selectedImageKeys:t.imageKeys||[]}),S=Object.keys(_).length;(0,r.useEffect)((()=>{1===S&&g.step!==M&&y(M)}),[M,S,g.step,y]);const E=C.filter((e=>e.tags[pe.Cr])).length>pe.Lf,A=(0,b.FD)("div",{css:(0,I.AH)({display:"flex",flexDirection:"column",height:a?"100%":void 0,width:"100%",overflow:"hidden",marginTop:u.spacing.sm,gap:u.spacing.md},""),children:[(0,b.Y)("div",{ref:p,css:me,children:(0,b.Y)(de.f,{previewData:C,groupBy:s,cardConfig:g,setCardConfig:e=>{v(e(t))}})}),(0,b.Y)("div",{css:(0,I.AH)({justifyContent:"center",alignItems:"center",display:"inline-flex",gap:u.spacing.md},""),children:(0,b.Y)("div",{css:ge,children:(0,b.Y)(he.o,{value:g.step,onChange:e=>{f((t=>({...t,step:e})))},max:w,min:M,marks:_,disabled:Object.keys(_).length<=1,onAfterChange:y,css:fe})})})]});if(a)return A;const k=A;return(0,b.Y)(d.Ox,{onEdit:o,onDelete:i,title:x,subtitle:E&&`Displaying images from first ${pe.Lf} runs`,uuid:t.uuid,dragGroupKey:d.cN.GENERAL_AREA,toggleFullScreenChart:()=>{null===l||void 0===l||l({config:t,title:x,subtitle:null})},...c,children:k})},ye=e=>{let{cardConfig:t,chartRunData:n,index:a,sectionIndex:l,onStartEditChart:c,onRemoveChart:d,setFullScreenChart:u,groupBy:p,fullScreen:h,canMoveDown:m,canMoveUp:g,previousChartUuid:f,nextChartUuid:v,onReorderWith:y,autoRefreshEnabled:x,onDownloadFullMetricHistoryCsv:C,hideEmptyCharts:_,globalLineChartConfig:w,height:S,isInViewport:I,isInViewportDeferred:E}=e;const R=(0,r.useMemo)((()=>({onReorderWith:y,canMoveDown:m,canMoveUp:g,previousChartUuid:f,nextChartUuid:v})),[y,m,g,f,v]),A=(0,r.useMemo)((()=>({onEdit:()=>c(t),onDelete:()=>d(t),setFullScreenChart:u})),[c,d,u,t]),k=(0,r.useMemo)((()=>({fullScreen:h,autoRefreshEnabled:x,groupBy:p,hideEmptyCharts:_,height:S,isInViewport:I,isInViewportDeferred:E,...A,...R})),[h,x,p,A,R,_,S,I,E]),Y=(0,r.useMemo)((()=>n.filter((e=>{let{hidden:t}=e;return!t})).reverse()),[n]);return t.type===i.zL.PARALLEL?(0,b.Y)(te,{config:t,chartRunData:n,...k}):(0,o.uP)()&&t.type===i.zL.DIFFERENCE?(0,b.Y)(ce,{config:t,chartRunData:n,...k}):t.type===i.zL.IMAGE?(0,b.Y)(ve,{config:t,chartRunData:n,...k}):t.type===i.zL.BAR?(0,b.Y)(s.X,{config:t,chartRunData:Y,...k}):t.type===i.zL.LINE?(0,b.Y)(M,{config:t,chartRunData:Y,onDownloadFullMetricHistoryCsv:C,globalLineChartConfig:w,positionInSection:a,...k}):t.type===i.zL.SCATTER?(0,b.Y)(T,{config:t,chartRunData:Y,...k}):t.type===i.zL.CONTOUR?(0,b.Y)(F,{config:t,chartRunData:Y,...k}):null},xe=(0,r.memo)(ye)},6211:function(e,t,n){"use strict";n.d(t,{h:function(){return c}});var r,i,o,s=n(31014);function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a.apply(null,arguments)}function l(e,t){let{title:n,titleId:l,...c}=e;return s.createElement("svg",a({width:18,height:18,viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":l},c),n?s.createElement("title",{id:l},n):null,r||(r=s.createElement("path",{d:"M1 1V17H17",stroke:"#A3AEB8",strokeLinecap:"round"})),i||(i=s.createElement("path",{d:"M12.9749 4.94148C12.9188 4.71702 12.221 4.73284 12.0618 4.71515C11.1779 4.61693 10.2817 4.66052 9.39271 4.66052C8.11919 4.66052 7.15648 4.97961 6.24753 5.90922C5.86197 6.30355 5.5235 6.77684 5.23295 7.24378C4.99434 7.62726 4.65544 7.93479 4.40569 8.31299C3.93777 9.02154 3.56281 9.80723 3.56281 10.6699C3.56281 11.1806 3.37479 11.598 3.76962 12.0669C4.19449 12.5714 4.6863 12.9507 5.27978 13.2649C5.88594 13.5858 6.42202 13.8619 7.13723 13.8619C7.74338 13.8619 8.34952 13.8619 8.95567 13.8619C9.45735 13.8619 9.96042 13.8731 10.4619 13.8619C11.1412 13.8468 11.9484 13.1895 12.413 12.7381C13.581 11.6035 14.2393 9.88016 14.2393 8.24665C14.2393 7.69923 14.3797 7.17063 14.3797 6.62723C14.3797 6.40084 14.4779 5.87696 14.2744 5.71411C13.9268 5.43606 13.5785 5.13789 13.1857 4.94148",stroke:"#338ECC",strokeLinecap:"round"})),o||(o=s.createElement("path",{d:"M11.9213 6.83789C11.2761 6.83789 10.6309 6.83789 9.98577 6.83789C9.19776 6.83789 8.80182 7.44144 8.2844 7.97734C7.58163 8.70521 7.28544 9.67306 7.28544 10.666C7.28544 10.9641 7.22004 11.177 7.46104 11.3645C7.6831 11.5372 8.10719 11.6142 8.37806 11.6142C9.07316 11.6142 9.91042 11.5583 10.4462 11.0523C10.9277 10.5975 11.473 10.3701 11.8159 9.75284C12.045 9.34042 12.2022 8.86324 12.2022 8.38317C12.2022 8.14903 12.2022 7.9149 12.2022 7.68077C12.2022 7.4241 12.0208 7.38817 11.9213 7.18909",stroke:"#338ECC",strokeLinecap:"round"})))}const c=s.forwardRef(l);n.p},6531:function(e,t,n){"use strict";n.d(t,{x:function(){return s}});var r=n(9133),i=n(31014),o=n(36118);const s=(e,t)=>(0,i.useMemo)((()=>{const n=(0,r.pick)(e,["xAxisKey","selectedXAxisMetricKey","lineSmoothness"]);if(!t)return n;const i=t.xAxisKey;if(e.useGlobalLineSmoothing&&!(0,r.isUndefined)(t.lineSmoothness)&&(n.lineSmoothness=t.lineSmoothness),!(0,r.isUndefined)(i)&&e.useGlobalXaxisKey){n.xAxisKey=i;const e=null===t||void 0===t?void 0:t.selectedXAxisMetricKey;i===o.fj.METRIC&&e&&(n.selectedXAxisMetricKey=e)}return n}),[e,t])},6604:function(e,t,n){"use strict";n.d(t,{TB:function(){return v},cK:function(){return g},e9:function(){return x},iH:function(){return y},jA:function(){return f},t4:function(){return m}});var r=n(9133),i=n(81866),o=n(25866),s=n(50361),a=n(32378),l=n(21616),c=n(26809),d=n(7204),u=n(91144);const p={ALL:null,LAST_HOUR:36e5,LAST_24_HOURS:864e5,LAST_7_DAYS:6048e5,LAST_30_DAYS:2592e6,LAST_YEAR:31104e6},h=new RegExp(`(${["attribute","attributes","attr","run","metric","metrics","param","params","parameter","tag","tags","dataset","datasets","model","models"].join("|")})\\.\\S+\\s*(>|<|>=|<=|=|!=| like| ilike| rlike| in)`,"i"),m=3e4,g=e=>h.test(e),f=e=>`attributes.run_name RLIKE '${e.replace(/'/g,"\\'")}'`,v=(e,t,n,r,i)=>{const l=t.lifecycleFilter===a.gy.ACTIVE?s.qi.ACTIVE_ONLY:s.qi.DELETED_ONLY,{runsPinned:d}=t,h=(e=>{let{orderByKey:t,orderByAsc:n}=e;return t?n?[t+" ASC"]:[t+" DESC"]:[]})(t),m=((e,t)=>{let{startTime:n}=e;const r=p[n];return n&&r&&"ALL"!==n?"attributes.start_time >= "+(t-r):null})(t,n),v=((e,t,n)=>{let{searchFilter:r}=e;if((0,u.ey)()&&r.length>0&&!g(r))return f(r);const i=[];return r&&i.push(r),t&&i.push(t),n&&i.push(n),0!==i.length?i.join(" and "):void 0})(t,m,(e=>{let{datasetsFilter:t}=e;return 0===t.length?null:`dataset.name IN (${t.map((e=>`'${e.name}'`)).join(",")}) AND dataset.digest IN (${t.map((e=>`'${e.digest}'`)).join(",")})`})(t));return{experimentIds:e,filter:v,runViewType:l,orderBy:h,shouldFetchParents:(e=>{let{orderByKey:t,searchFilter:n}=e;return!t&&!n||t===o.T8.DATE})(t),pageToken:r,runsPinned:d,maxResults:i||c.Aj}},y=(e,t,n)=>{const o=e.filter((e=>e.data.tags.some((e=>e.key===l.tS)))),s=(0,r.chunk)(o,i.SF).map((e=>{let r;const i=t({run_id:e.map((e=>e.info.run_id))},(0,d.yk)(),r);return n(i)}));return Promise.all(s)},x=e=>{const{lifecycleFilter:t,modelVersionFilter:n,datasetsFilter:r,searchFilter:i,startTime:s}=e;return Boolean(t!==o.ae||n!==o.eR||0!==r.length||i||s!==o.xx)}},6922:function(e,t,n){"use strict";n.d(t,{H8:function(){return u},Ox:function(){return C},Us:function(){return _},b7:function(){return p},cN:function(){return h},rZ:function(){return d}});var r=n(89555),i=n(32599),o=n(48012),s=n(31014),a=n(88443),l=n(9133),c=n(50111);const d="drag-handle",u="--drag-transform",p=`var(${u})`;let h=function(e){return e.PARALLEL_CHARTS_AREA="PARALLEL_CHARTS_AREA",e.GENERAL_AREA="GENERAL_AREA",e}({});var m={name:"andpxo",styles:"display:flex;overflow:hidden"},g={name:"19mhjsb",styles:"overflow:hidden;flex:1;flex-shrink:1"},f={name:"72wqca",styles:"margin-bottom:0;overflow:hidden;white-space:nowrap;text-overflow:ellipsis"},v={name:"1989ovb",styles:"vertical-align:middle"};const y=e=>{let{title:t,subtitle:n,onDelete:u,onEdit:p,children:h,uuid:y,dragGroupKey:C,tooltip:b="",onReorderWith:_=l.noop,canMoveDown:w,canMoveUp:M,previousChartUuid:S,nextChartUuid:I,additionalMenuContent:E,toggleFullScreenChart:R,toggles:A,supportedDownloadFormats:k=[],onClickDownload:Y,isHidden:T,height:D,isRefreshing:L=!1}=e;const{theme:F}=(0,i.u)(),K=(0,s.useCallback)((()=>_(y||"",S||"")),[_,y,S]),N=(0,s.useCallback)((()=>_(y||"",I||"")),[_,y,I]),H=s.isValidElement(t);return(0,c.FD)("div",{css:(0,r.AH)({height:null!==D&&void 0!==D?D:360,overflow:"hidden",display:"grid",gridTemplateRows:"auto 1fr",backgroundColor:F.colors.backgroundPrimary,padding:12,paddingBottom:F.spacing.sm,border:`1px solid ${F.colors.border}`,borderRadius:F.general.borderRadiusBase,transition:"opacity 0.12s",position:"relative"},""),"data-testid":"experiment-view-compare-runs-card",children:[(0,c.FD)("div",{css:m,children:[(0,c.Y)("div",{"data-testid":"experiment-view-compare-runs-card-drag-handle",css:(0,r.AH)({marginTop:H?F.spacing.sm:F.spacing.xs,marginRight:F.spacing.sm,cursor:"grab"},""),className:d,children:(0,c.Y)(o.WP0,{})}),H?t:(0,c.FD)("div",{css:g,children:[(0,c.Y)(i.T.Title,{title:String(t),level:4,css:f,children:t}),n&&(0,c.Y)("span",{css:x.subtitle(F),children:n}),b&&(0,c.Y)(o.Jsb,{css:v,title:b})]}),L&&(0,c.Y)("div",{css:(0,r.AH)({width:F.general.heightSm,height:F.general.heightSm,display:"flex",alignItems:"center",justifyContent:"center"},""),children:(0,c.Y)(i.S,{})}),A&&(0,c.Y)("div",{css:(0,r.AH)({display:"flex",padding:`0px ${F.spacing.lg}px`,gap:F.spacing.md,alignItems:"flex-start"},""),children:A.map((e=>(0,c.Y)(o.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_262",checked:e.currentToggle,onChange:e.setToggle,label:e.toggleLabel},e.toggleLabel)))}),(0,c.Y)(i.B,{componentId:"fullscreen_button_chartcard",icon:(0,c.Y)(o.P7m,{}),onClick:R,disabled:!R}),(0,c.FD)(o.rId.Root,{modal:!1,children:[(0,c.Y)(o.rId.Trigger,{asChild:!0,children:(0,c.Y)(i.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_cards_chartcard.common.tsx_158",type:"tertiary",icon:(0,c.Y)(o.ssM,{}),"data-testid":"experiment-view-compare-runs-card-menu"})}),(0,c.FD)(o.rId.Content,{align:"end",minWidth:100,children:[(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_288",onClick:p,"data-testid":"experiment-view-compare-runs-card-edit",children:"Configure"}),(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_291",onClick:u,"data-testid":"experiment-view-compare-runs-card-delete",children:"Delete"}),k.length>0&&Y&&(0,c.FD)(c.FK,{children:[(0,c.Y)(o.rId.Separator,{}),k.includes("csv")&&(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_298",onClick:()=>Y("csv"),children:(0,c.Y)(a.A,{id:"5YOBk/",defaultMessage:"Export as CSV"})}),k.includes("svg")&&(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_316",onClick:()=>Y("svg"),children:(0,c.Y)(a.A,{id:"LiW/yM",defaultMessage:"Download as SVG"})}),k.includes("png")&&(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_324",onClick:()=>Y("png"),children:(0,c.Y)(a.A,{id:"s5ygFF",defaultMessage:"Download as PNG"})})]}),(0,c.Y)(o.rId.Separator,{}),(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_334",disabled:!M,onClick:K,"data-testid":"experiment-view-compare-runs-move-up",children:(0,c.Y)(a.A,{id:"IAyAgH",defaultMessage:"Move up"})}),(0,c.Y)(o.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_cards_chartcard.common.tsx_344",disabled:!w,onClick:N,"data-testid":"experiment-view-compare-runs-move-down",children:(0,c.Y)(a.A,{id:"Z6kodo",defaultMessage:"Move down"})}),E]})]})]}),h]})},x={chartEntry:e=>({height:360,overflow:"hidden",display:"grid",gridTemplateRows:"auto 1fr",backgroundColor:e.colors.backgroundPrimary,padding:e.spacing.md,border:`1px solid ${e.colors.border}`,borderRadius:e.general.borderRadiusBase}),chartComponentWrapper:()=>({overflow:"hidden"}),subtitle:e=>({color:e.colors.textSecondary,fontSize:11,marginRight:4,verticalAlign:"middle"})},C=(0,s.memo)(y);var b={name:"cgzj3x",styles:"display:flex;height:100%;justify-content:center;align-items:center"};const _=(0,s.forwardRef)(((e,t)=>{let{className:n,style:r}=e;return(0,c.Y)("div",{css:b,className:n,style:r,ref:t,children:(0,c.Y)(i.S,{})})}))},10661:function(e,t,n){"use strict";n.d(t,{i:function(){return g}});var r=n(32599),i=n(9133),o=n(31014),s=n(79445),a=n(97604),l=n(25790),c=n(36118),d=n(23541),u=n(50684),p=n(6922),h=n(50111);const m={displaylogo:!1,scrollZoom:!1,modeBarButtonsToRemove:["toImage"]},g=o.memo((e=>{let{runsData:t,xAxis:n,yAxis:g,markerSize:f=10,className:v,margin:y=c.am,onUpdate:x,onHover:C,onUnhover:b,width:_,height:w,useDefaultHoverBox:M=!0,selectedRunUuid:S,onSetDownloadHandler:I}=e;const{theme:E}=(0,r.u)(),{layoutHeight:R,layoutWidth:A,setContainerDiv:k,containerDiv:Y,isDynamicSizeSupported:T}=(0,c.xN)(),D=(0,o.useMemo)((()=>{const e=[],r=[],o=[],s=[];for(const c of t){var a,l,d,u;const{runInfo:t,metrics:p,params:h,color:m,uuid:f,displayName:v}=c,{runUuid:y,runName:x}=t||{},C="METRIC"===n.type?p:h,b="METRIC"===g.type?p:h,_=null===C||void 0===C||null===(a=C[null!==(l=n.dataAccessKey)&&void 0!==l?l:n.key])||void 0===a?void 0:a.value,w=null===b||void 0===b||null===(d=b[null!==(u=g.dataAccessKey)&&void 0!==u?u:g.key])||void 0===d?void 0:d.value;(0,i.isNil)(_)||(0,i.isNil)(w)||(e.push(_),r.push(w),o.push(m||E.colors.primary),y?s.push([y,x||y]):s.push([f,v]))}return[{x:e,y:r,customdata:s,text:t.map((e=>{let{displayName:t}=e;return t})),hovertemplate:M?"<b>%{customdata[1]}:</b><br><b>%{xaxis.title.text}:</b> %{x:.2f}<br><b>%{yaxis.title.text}:</b> %{y:.2f}<br><extra></extra>":void 0,hoverinfo:M?void 0:"none",hoverlabel:M?c.rv:void 0,type:"scatter",mode:"markers",textposition:"bottom center",marker:{size:f,color:o}}]}),[t,n,g,E,f,M]),L=(0,o.useMemo)((()=>(0,c.y)(E)),[E]),[F,K]=(0,o.useState)({width:_||A,height:w||R,margin:y,xaxis:{title:n.key,tickfont:{size:11,color:E.colors.textSecondary}},yaxis:{title:g.key,tickfont:{size:11,color:E.colors.textSecondary}},template:{layout:L}});(0,o.useEffect)((()=>{K((e=>{const t={...e,width:_||A,height:w||R,margin:y};return t.xaxis&&(t.xaxis.title=n.key),t.yaxis&&(t.yaxis.title=g.key),t}))}),[A,R,y,n.key,g.key,_,w]);const{setHoveredPointIndex:N}=(0,l.pO)(Y,S,t,l.nD),H=(0,o.useCallback)((e=>{var t,n,r,i;let{points:o}=e;const s=null===(t=o[0])||void 0===t||null===(n=t.customdata)||void 0===n?void 0:n[0];N(null!==(r=null===(i=o[0])||void 0===i?void 0:i.pointIndex)&&void 0!==r?r:-1),s&&(null===C||void 0===C||C(s,void 0,{}))}),[C,N]),O=(0,o.useCallback)((()=>{null===b||void 0===b||b(),N(-1)}),[b,N]),B=(0,a.l)(H),P=(0,o.useMemo)((()=>(0,c.eY)(t)),[t]);(0,o.useEffect)((()=>{const e=D.map((e=>({...e,mode:"text+markers"})));null===I||void 0===I||I((0,u.W)(e,F))}),[F,I,D]);const U=(0,h.Y)("div",{css:[c.gz.chartWrapper(E),c.gz.scatterChartHighlightStyles,""],className:v,ref:k,children:(0,h.Y)(s.W,{data:D,useResizeHandler:!T,css:c.gz.chart(E),layout:F,config:m,onUpdate:x,onHover:B,onUnhover:O,fallback:(0,h.Y)(p.Us,{})})});return(0,h.Y)(d.A,{labelData:P,children:U})}))},16026:function(e,t,n){"use strict";n.d(t,{Y:function(){return l}});var r=n(89555),i=n(32599),o=(n(31014),n(50111));var s={name:"aj4kza",styles:"white-space:nowrap;text-overflow:ellipsis;overflow:hidden"};const a=e=>{let{label:t,color:n,dashStyle:a}=e;const{theme:c}=(0,i.u)();return(0,o.FD)("div",{css:(0,r.AH)({display:"flex",alignItems:"center",textOverflow:"ellipsis",flexShrink:0,marginRight:c.spacing.md,maxWidth:"100%"},""),children:[(0,o.Y)(l,{color:n,dashStyle:a}),(0,o.Y)(i.T.Text,{color:"secondary",size:"sm",css:s,children:t})]})},l=e=>{let{color:t,dashStyle:n}=e;const{theme:s}=(0,i.u)(),a=n?(e=>{switch(e){case"dot":return"3";case"dash":return"6, 3";case"longdash":return"9, 3";case"dashdot":return"6, 3, 3, 3";case"longdashdot":return"9, 3, 3, 3";default:return""}})(n):void 0,l=s.typography.fontSizeSm/2;return(0,o.Y)("svg",{css:(0,r.AH)({height:s.typography.fontSizeSm,width:24,marginRight:s.spacing.xs,flexShrink:0},""),children:(0,o.Y)("path",{d:`M0,${l}h24`,style:{strokeWidth:3,stroke:t,strokeDasharray:a}})})};t.A=e=>{let{labelData:t,height:n,fullScreen:s}=e;const{theme:l}=(0,i.u)();return(0,o.Y)("div",{css:(0,r.AH)({display:"flex",flexWrap:"wrap",height:n,alignContent:s?"flex-start":"normal",gap:s?l.spacing.sm:0,overflowY:"auto",overflowX:"hidden",marginTop:s?l.spacing.lg:l.spacing.sm},""),children:t.map((e=>{var t;return(0,o.Y)(a,{...e},null!==(t=e.uuid)&&void 0!==t?t:e.label)}))})}},19415:function(e,t,n){"use strict";n.d(t,{z:function(){return Ie}});var r=n(89555),i=n(32599),o=n(15579),s=n(48012),a=n(31014),l=n(88464),c=n(88443),d=n(30152),u=n(77198),p=n(6211),h=n(73549),m=n(43270),g=n(74035),f=n(32582),v=n(99790),y=n(21616),x=n(50111);const C=e=>{let{title:t,compact:n=!1,children:o}=e;const{theme:s}=(0,i.u)();return(0,x.FD)("div",{css:(0,r.AH)({marginBottom:n?s.spacing.sm:2*s.spacing.md},""),"data-testid":"experiment-view-compare-runs-config-field",children:[(0,x.Y)(i.T.Title,{level:4,children:t}),o]})},b=e=>{let{value:t,onChange:n,metricKeyList:r,paramKeyList:i}=e;const{formatMessage:o}=(0,l.A)(),a=!(null!==i&&void 0!==i&&i.length)&&!(null!==r&&void 0!==r&&r.length);return(0,x.FD)(s._vn,{css:M.selectFull,value:a?o({id:"3VURvw",defaultMessage:"No metrics or parameters available"}):t,disabled:a,onChange:n,dangerouslySetAntdProps:{showSearch:!0},children:[null!==r&&void 0!==r&&r.length?(0,x.Y)(s._vn.OptGroup,{label:o({id:"jH0+gA",defaultMessage:"Metrics"}),children:r.map((e=>(0,x.Y)(s._vn.Option,{value:(0,y.GF)("METRIC",e),children:e},(0,y.GF)("METRIC",e))))}):null,null!==i&&void 0!==i&&i.length?(0,x.Y)(s._vn.OptGroup,{label:o({id:"kWUhea",defaultMessage:"Params"}),children:i.map((e=>(0,x.Y)(s._vn.Option,{value:(0,y.GF)("PARAM",e),children:e},(0,y.GF)("PARAM",e))))}):null]})},_=e=>{let{value:t,id:n,onChange:r,metricOptions:i=[],paramOptions:o=[]}=e;const{formatMessage:a}=(0,l.A)(),c=!o.length&&!i.length;return(0,x.FD)(s.XAe,{componentId:"mlflow.charts.chart_configure.metric_with_dataset_select",id:n,css:M.selectFull,value:c?a({id:"3VURvw",defaultMessage:"No metrics or parameters available"}):t,disabled:c,onChange:e=>{let{target:t}=e;r(t.value)},contentProps:{matchTriggerWidth:!0,maxHeight:500},children:[null!==i&&void 0!==i&&i.length?(0,x.Y)(s.lQN,{label:a({id:"jH0+gA",defaultMessage:"Metrics"}),children:i.map((e=>{let{datasetName:t,key:n,metricKey:r}=e;return(0,x.FD)(s.wS0,{value:n,children:[t&&(0,x.Y)(s.vwO,{componentId:"mlflow.charts.chart_configure.metric_with_dataset_select.tag",children:t})," ",r]},n)}))}):null,null!==o&&void 0!==o&&o.length?(0,x.Y)(s.lQN,{label:a({id:"kWUhea",defaultMessage:"Params"}),children:o.map((e=>{let{key:t,paramKey:n}=e;return(0,x.Y)(s.wS0,{value:t,children:n},t)}))}):null]})},w=[{value:5,label:(0,x.Y)(c.A,{id:"Mc6Dhy",defaultMessage:"5"})},{value:10,label:(0,x.Y)(c.A,{id:"2K8tsp",defaultMessage:"10"})},{value:20,label:(0,x.Y)(c.A,{id:"zwO8+l",defaultMessage:"20"})}],M={selectFull:{width:"100%"}};var S=n(9133);var I={name:"1d3w5wq",styles:"width:100%"};const E=e=>{let{metricKeysByDataset:t,value:n,onChange:r}=e;return(0,x.Y)(s.XAe,{css:I,componentId:"mlflow.charts.chart_configure.metric_with_dataset_select",id:"mlflow.charts.chart_configure.metric_with_dataset_select",value:n,onChange:e=>{let{target:n}=e;const i=null===t||void 0===t?void 0:t.find((e=>{let{dataAccessKey:t}=e;return t===n.value}));i&&r(i)},contentProps:{matchTriggerWidth:!0,maxHeight:400},children:null===t||void 0===t?void 0:t.map((e=>{let{datasetName:t,metricKey:n,dataAccessKey:r}=e;return(0,x.FD)(s.wS0,{value:r,children:[t&&(0,x.Y)(s.vwO,{componentId:"mlflow.charts.chart_configure.metric_with_dataset_select.tag",children:t})," ",n]},r)}))})},R=e=>{var t;let{state:n,onStateChange:r,metricKeyList:i,metricKeysByDataset:o}=e;const l=(0,a.useCallback)(((e,t,n)=>{r((r=>({...r,metricKey:e,datasetName:t,dataAccessKey:n})))}),[r]);(0,a.useCallback)((e=>{r((t=>({...t,runsCountToCompare:e})))}),[r]);(0,a.useEffect)((()=>{n.metricKey||null===o||void 0===o||!o[0]?!n.metricKey&&null!==i&&void 0!==i&&i[0]&&l(i[0]):l(o[0].metricKey,o[0].datasetName,o[0].dataAccessKey)}),[n.metricKey,l,i,o]);const c=0===i.length;return(0,x.Y)(x.FK,{children:(0,x.Y)(C,{title:"Metric",children:(0,S.isEmpty)(o)?(0,x.Y)(s._vn,{css:A.selectFull,value:c?"No metrics available":n.metricKey,onChange:e=>l(e),disabled:c,dangerouslySetAntdProps:{showSearch:!0},children:i.map((e=>(0,x.Y)(s._vn.Option,{value:e,"data-testid":`metric-${e}`,children:e},e)))}):(0,x.Y)(E,{metricKeysByDataset:o,onChange:e=>{let{metricKey:t,datasetName:n,dataAccessKey:r}=e;return l(t,n,r)},value:null!==(t=n.dataAccessKey)&&void 0!==t?t:n.metricKey})})})},A={selectFull:{width:"100%"}},k=e=>{let{state:t,onStateChange:n,metricKeyList:r,paramKeyList:i}=e;const o=(0,a.useCallback)((e=>{n((t=>({...t,selectedParams:e})))}),[n]),l=(0,a.useCallback)((e=>{n((t=>({...t,selectedMetrics:e})))}),[n]),c=0===r.length,d=0===i.length;return(0,x.FD)(x.FK,{children:[(0,x.Y)(C,{title:"Params",children:(0,x.Y)(s._vn,{mode:d?void 0:"multiple",onChange:o,style:{width:275},value:d?"No parameters available":t.selectedParams,disabled:d,children:i.map((e=>(0,x.Y)(s._vn.Option,{value:e,children:e},e)))})}),(0,x.Y)(C,{title:"Metrics",children:(0,x.Y)(s._vn,{mode:c?void 0:"multiple",onChange:l,style:{width:275},value:c?"No metrics available":t.selectedMetrics,disabled:c,children:r.map((e=>(0,x.Y)(s._vn.Option,{value:e,children:e},e)))})})]})};var Y=n(41028),T=n(91144),D=n(36118),L=n(20193),F=n(49698);var K={name:"1d3w5wq",styles:"width:100%"};const N=e=>{let{metricKeyList:t,selectedMetricKeys:n,updateSelectedMetrics:r}=e;const i=0===t.length;return(0,x.Y)(s._vn,{mode:"multiple",placeholder:i?(0,x.Y)(c.A,{id:"M5bYHa",defaultMessage:"No metrics available"}):(0,x.Y)(c.A,{id:"eQqRYQ",defaultMessage:"Select metrics"}),css:K,value:i?[]:n,onChange:r,disabled:i,dangerouslySetAntdProps:{showSearch:!0},children:t.map((e=>(0,x.Y)(s._vn.Option,{value:e,"data-testid":`metric-${e}`,children:e},e)))})},H=e=>{let{chartExpression:t,index:n,updateYAxisExpression:o,removeYAxisExpression:s,metricKeyList:l}=e;const{theme:c}=(0,i.u)(),{compileExpression:d}=(0,F.l)(),[u,p]=(0,a.useState)(!0);return(0,x.FD)("span",{css:(0,r.AH)({display:"flex",width:"100%",gap:c.spacing.sm},""),children:[(0,x.Y)(Y.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsyaxismetricandexpressionselector.tsx_122",value:t.expression,onChange:e=>(e=>{const t=d(e,l);void 0===t?(p(!1),o({rpn:[],variables:[],expression:e},n)):(p(!0),o(t,n))})(e.target.value),validationState:u?void 0:"error"}),(0,x.Y)(i.B,{componentId:"mlflow.charts.line-chart-expressions-remove",icon:(0,x.Y)(i.C,{}),onClick:()=>s(n)})]})},O=e=>{let{state:t,onStateChange:n,metricKeyList:o,updateSelectedMetrics:l}=e;const{theme:u}=(0,i.u)(),p=(0,T.Iq)()&&t.xAxisKey!==D.fj.METRIC,[h,m]=(0,a.useState)(t.yAxisExpressions||[]),g=(e,t)=>{m((n=>{const r=[...n];return r[t]=e,r}))},f=e=>{m((t=>{const n=[...t];return n.splice(e,1),n}))};(0,a.useEffect)((()=>{const e=setTimeout((()=>{var e;e=h,n((t=>({...t,yAxisExpressions:e})))}),300);return()=>{clearTimeout(e)}}),[h,n]);const v=(0,a.useCallback)((e=>{n((t=>{const n=t;return{...n,yAxisKey:e,range:{...n.range,yMin:void 0,yMax:void 0}}}))}),[n]);return(0,x.FD)(x.FK,{children:[p&&(0,x.Y)(C,{title:"Metric type",compact:!0,children:(0,x.FD)(s.sxL.Group,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsyaxismetricandexpressionselector.tsx_221",name:"runs-charts-field-group-metric-type-y-axis",value:t.yAxisKey||d.Kb.METRIC,onChange:e=>{let{target:{value:t}}=e;return v(t)},children:[(0,x.Y)(s.sxL,{value:d.Kb.METRIC,children:(0,x.Y)(c.A,{id:"hW7XxH",defaultMessage:"Logged metrics"})},d.Kb.METRIC),(0,x.Y)(s.sxL,{value:d.Kb.EXPRESSION,children:(0,x.Y)(c.A,{id:"tkAOSa",defaultMessage:"Custom expression"})},d.Kb.EXPRESSION)]})}),p&&t.yAxisKey===d.Kb.EXPRESSION?(0,x.Y)(C,{title:"Expression",compact:!0,children:(0,x.FD)("div",{css:(0,r.AH)({display:"flex",flexDirection:"column",alignItems:"flex-start",gap:u.spacing.sm},""),children:[h.map(((e,t)=>(0,x.Y)(H,{chartExpression:e,index:t,updateYAxisExpression:g,removeYAxisExpression:f,metricKeyList:o},t))),(0,x.Y)(i.B,{componentId:"mlflow.charts.line-chart-expressions-add-new",icon:(0,x.Y)(s.c11,{}),onClick:()=>{m((e=>[...e,{rpn:[],variables:[],expression:""}]))},children:"Add new"})]})}):(0,x.Y)(C,{title:"Metric",compact:!0,children:N({metricKeyList:o,selectedMetricKeys:t.selectedMetricKeys,updateSelectedMetrics:l})})]})};const B="_GLOBAL",P=e=>{let{theme:t,metricKeyList:n,selectedXAxisMetricKey:i,updateSelectedXAxisMetricKey:o,disabled:a=!1}=e;const l=0===n.length;return(0,x.FD)(s.sxL,{value:D.fj.METRIC,disabled:a,children:[(0,x.Y)(c.A,{id:"M/c4l0",defaultMessage:"Metric"}),(0,x.Y)(s._vn,{css:(0,r.AH)({marginTop:t.spacing.xs,width:"100%"},""),value:i||void 0,placeholder:l?(0,x.Y)(c.A,{id:"ER3oVW",defaultMessage:"No metrics available"}):(0,x.Y)(c.A,{id:"BOz0T2",defaultMessage:"Select metric"}),onClick:e=>{e.preventDefault(),e.stopPropagation()},onChange:o,disabled:l||a,dangerouslySetAntdProps:{showSearch:!0},children:n.map((e=>(0,x.Y)(s._vn.Option,{value:e,"data-testid":`metric-${e}`,children:e},e)))})]})},U=e=>(0,S.isUndefined)(e)?e:e<=0?void 0:Math.log10(e),z=e=>(0,S.isUndefined)(e)?e:Math.pow(10,e);var j={name:"4c6dm7",styles:"display:flex;flex-direction:row;justify-content:space-between"};const G=e=>{var t,n,u,p,h,m,g,f;let{state:v,onStateChange:y,metricKeyList:b}=e;const _=(0,T.Iq)(),{theme:w}=(0,i.u)(),M=(0,l.A)(),[I,E]=(0,a.useState)({xMin:"log"===v.xAxisScaleType?z(null===(t=v.range)||void 0===t?void 0:t.xMin):null===(n=v.range)||void 0===n?void 0:n.xMin,xMax:"log"===v.xAxisScaleType?z(null===(u=v.range)||void 0===u?void 0:u.xMax):null===(p=v.range)||void 0===p?void 0:p.xMax,yMin:"log"===v.scaleType?z(null===(h=v.range)||void 0===h?void 0:h.yMin):null===(m=v.range)||void 0===m?void 0:m.yMin,yMax:"log"===v.scaleType?z(null===(g=v.range)||void 0===g?void 0:g.yMax):null===(f=v.range)||void 0===f?void 0:f.yMax}),R=(0,a.useCallback)((e=>{y((t=>({...t,metricKey:e})))}),[y]),A=(0,a.useCallback)((e=>{y((t=>({...t,metricKey:e[0],selectedMetricKeys:e})))}),[y]),k=(0,a.useCallback)((function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];y((n=>{const r=n;return{...r,xAxisKey:e,selectedXAxisMetricKey:"",range:{...r.range,xMin:void 0,xMax:void 0},useGlobalXaxisKey:null!==t&&void 0!==t?t:r.useGlobalXaxisKey,selectedYAxisMetricKey:d.Kb.METRIC}}))}),[y]),F=e=>!(0,S.isUndefined)(e)&&e<=0,K=(0,a.useCallback)((e=>{y((t=>{const n=t;let r=e?U(I.xMin):I.xMin,i=e?U(I.xMax):I.xMax;return e&&F(I.xMin)&&I.xMax&&I.xMax>1?(E((e=>({...e,xMin:1}))),r=0):e&&(F(I.xMin)||F(I.xMax))&&(E((e=>({...e,xMin:void 0,xMax:void 0}))),r=void 0,i=void 0),{...n,xAxisScaleType:e?"log":"linear",range:{...n.range,xMin:r,xMax:i}}}))}),[y,I.xMin,I.xMax]),N=(0,a.useCallback)((e=>{y((t=>({...t,selectedXAxisMetricKey:e,xAxisKey:D.fj.METRIC})))}),[y]),H=(0,a.useCallback)((e=>{y((t=>{const n=t;let r=e?U(I.yMin):I.yMin,i=e?U(I.yMax):I.yMax;return e&&F(I.yMin)&&I.yMax&&I.yMax>1?(E((e=>({...e,yMin:1}))),r=0):e&&(F(I.yMin)||F(I.yMax))&&(E((e=>({...e,yMin:void 0,yMax:void 0}))),r=void 0,i=void 0),{...n,scaleType:e?"log":"linear",range:{...n.range,yMin:r,yMax:i}}}))}),[y,I.yMin,I.yMax]),G=(0,a.useCallback)((e=>{y((t=>({...t,ignoreOutliers:e})))}),[y]),W=(0,a.useCallback)((e=>{y((t=>({...t,lineSmoothness:e})))}),[y]);(0,a.useCallback)((e=>{y((t=>({...t,runsCountToCompare:e})))}),[y]);(0,a.useEffect)((()=>{!v.metricKey&&null!==b&&void 0!==b&&b[0]&&R(b[0])}),[v.metricKey,R,b]),(0,a.useEffect)((()=>{(0,S.isUndefined)(v.selectedMetricKeys)&&!(0,S.isUndefined)(v.metricKey)&&""!==v.metricKey&&A([v.metricKey])}),[v.selectedMetricKeys,v.metricKey,A]);const $=(e,t)=>{X(e,t)||y((n=>{const r=n;return{...r,range:{...r.range,xMin:"log"===r.xAxisScaleType?U(e):e,xMax:"log"===r.xAxisScaleType?U(t):t}}}))},q=(e,t)=>{X(e,t)||y((n=>{const r=n;return{...r,range:{...r.range,yMin:"log"===r.scaleType?U(e):e,yMax:"log"===r.scaleType?U(t):t}}}))},X=(e,t)=>(!(0,S.isUndefined)(e)||!(0,S.isUndefined)(t))&&!(!(0,S.isUndefined)(e)&&!(0,S.isUndefined)(t)),Q=(e,t)=>"log"===e&&F(t)?(0,x.Y)(s.D$Q.Message,{message:(0,x.Y)(c.A,{id:"o21MFS",defaultMessage:"Invalid log value"}),type:"warning"}):null,J="metric-";return(0,x.FD)(x.FK,{children:[(0,x.Y)(i.T.Title,{level:4,color:"secondary",children:"X-axis"}),(0,x.Y)(C,{title:"Type",compact:!0,children:_?(0,x.FD)(s.XAe,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_436",id:"x-axis-type",width:"100%",value:v.useGlobalXaxisKey?B:v.xAxisKey,contentProps:{matchTriggerWidth:!0,textOverflowMode:"ellipsis"},onChange:e=>{let{target:{value:t}}=e;t.startsWith(J)?N(t.slice(7)):t===D.fj.STEP?k(D.fj.STEP):t===D.fj.TIME?k(D.fj.TIME):t===D.fj.TIME_RELATIVE?k(D.fj.TIME_RELATIVE):t===B&&k(D.fj.STEP,!0)},children:[(0,x.Y)(s.wS0,{value:B,children:(0,x.Y)(c.A,{id:"srbhok",defaultMessage:"Use workspace settings"})}),(0,x.Y)(s.wS0,{value:D.fj.STEP,children:(0,x.Y)(c.A,{id:"IfcpiM",defaultMessage:"Step"})}),(0,x.FD)(s.wS0,{value:D.fj.TIME,children:[(0,x.Y)(c.A,{id:"2Hf+yU",defaultMessage:"Time (wall)"}),(0,x.Y)(o.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_474",content:(0,x.Y)(c.A,{id:"nlXn2G",defaultMessage:"Absolute date and time"}),side:"right",children:(0,x.FD)("span",{children:[" ",(0,x.Y)(s.JGH,{css:V.timeStepQuestionMarkIcon})]})})]}),(0,x.FD)(s.wS0,{value:D.fj.TIME_RELATIVE,children:[(0,x.Y)(c.A,{id:"Y47tVZ",defaultMessage:"Time (relative)"}),(0,x.Y)(o.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_494",content:(0,x.Y)(c.A,{id:"NwNrl4",defaultMessage:"Amount of time that has passed since the first metric value was logged"}),side:"right",children:(0,x.FD)("span",{children:[" ",(0,x.Y)(s.JGH,{css:V.timeStepQuestionMarkIcon})]})})]}),b.length>0&&(0,x.Y)(s.lQN,{label:"Metrics",children:b.map((e=>(0,x.Y)(s.wS0,{value:`${J}${e}`,"data-testid":`${J}${e}`,children:e},e)))})]}):(0,x.FD)(s.sxL.Group,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_524",name:"runs-charts-field-group-x-axis",value:v.useGlobalXaxisKey?B:v.xAxisKey,onChange:e=>{let{target:{value:t}}=e;t===B?k(D.fj.STEP,!0):k(t)},children:[(0,x.Y)(s.sxL,{value:B,children:(0,x.Y)(c.A,{id:"srbhok",defaultMessage:"Use workspace settings"})}),(0,x.Y)(s.sxL,{value:D.fj.STEP,children:(0,x.Y)(c.A,{id:"IfcpiM",defaultMessage:"Step"})}),(0,x.FD)(s.sxL,{value:D.fj.TIME,children:[(0,x.Y)(c.A,{id:"2Hf+yU",defaultMessage:"Time (wall)"}),(0,x.FD)(s.paO,{title:(0,x.Y)(c.A,{id:"nlXn2G",defaultMessage:"Absolute date and time"}),placement:"right",children:[" ",(0,x.Y)(s.JGH,{css:V.timeStepQuestionMarkIcon})]})]}),(0,x.FD)(s.sxL,{value:D.fj.TIME_RELATIVE,children:[(0,x.Y)(c.A,{id:"Y47tVZ",defaultMessage:"Time (relative)"}),(0,x.FD)(s.paO,{title:(0,x.Y)(c.A,{id:"NwNrl4",defaultMessage:"Amount of time that has passed since the first metric value was logged"}),placement:"right",children:[" ",(0,x.Y)(s.JGH,{css:V.timeStepQuestionMarkIcon})]})]}),P({theme:w,metricKeyList:b,selectedXAxisMetricKey:v.selectedXAxisMetricKey,updateSelectedXAxisMetricKey:N})]})}),v.xAxisKey===D.fj.STEP&&(0,x.Y)(x.FK,{children:(0,x.FD)(C,{title:"X-axis scale",compact:!0,children:[(0,x.FD)("div",{css:(0,r.AH)({display:"flex",gap:w.spacing.sm},""),children:[(0,x.FD)("div",{children:[(0,x.Y)(Y.I,{componentId:"mlflow.charts.line_chart_configure.x_axis_min","aria-label":"x-axis-min",name:"min",type:"number",value:I.xMin,onChange:e=>(e=>{const t=e?Number(e):void 0;E((e=>({...e,xMin:t}))),$(t,I.xMax)})(e.target.value),max:I.xMax,placeholder:"Min"}),Q(v.xAxisScaleType,I.xMin)]}),(0,x.FD)("div",{children:[(0,x.Y)(Y.I,{componentId:"mlflow.charts.line_chart_configure.x_axis_max","aria-label":"x-axis-max",name:"max",type:"number",value:I.xMax,onChange:e=>(e=>{const t=e?Number(e):void 0;E((e=>({...e,xMax:t}))),$(I.xMin,t)})(e.target.value),min:I.xMin,placeholder:"Max"}),Q(v.xAxisScaleType,I.xMax)]})]}),(0,x.Y)("div",{style:{padding:w.spacing.xs}}),(0,x.Y)(s.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_628","aria-label":"x-axis-log",checked:"log"===v.xAxisScaleType,onChange:K,label:"Log scale",activeLabel:"On",inactiveLabel:"Off",disabledLabel:"Disabled"})]})}),(0,x.Y)(i.T.Title,{level:4,color:"secondary",css:(0,r.AH)({paddingTop:w.spacing.lg},""),children:"Y-axis"}),(0,x.Y)(O,{state:v,onStateChange:y,metricKeyList:b,updateSelectedMetrics:A}),(0,x.FD)(C,{title:"Y-axis scale",compact:!0,children:[(0,x.FD)("div",{css:(0,r.AH)({display:"flex",gap:w.spacing.sm},""),children:[(0,x.FD)("div",{children:[(0,x.Y)(Y.I,{componentId:"mlflow.charts.line_chart_configure.y_axis_min","aria-label":"y-axis-min",name:"min",type:"number",value:I.yMin,onChange:e=>(e=>{const t=e?Number(e):void 0;E((e=>({...e,yMin:t}))),q(t,I.yMax)})(e.target.value),max:I.yMax,placeholder:"Min"}),Q(v.scaleType,I.yMin)]}),(0,x.FD)("div",{children:[(0,x.Y)(Y.I,{componentId:"mlflow.charts.line_chart_configure.y_axis_max","aria-label":"y-axis-max",name:"max",type:"number",value:I.yMax,onChange:e=>(e=>{const t=e?Number(e):void 0;E((e=>({...e,yMax:t}))),q(I.yMin,t)})(e.target.value),min:I.yMin,placeholder:"Max"}),Q(v.scaleType,I.yMax)]})]}),(0,x.Y)(o.S,{size:"xs"}),(0,x.Y)(s.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_682","aria-label":"y-axis-log",checked:"log"===v.scaleType,onChange:H,label:"Log scale",activeLabel:"On",inactiveLabel:"Off",disabledLabel:"Disabled"}),(0,x.Y)(o.S,{size:"xs"}),(0,x.FD)("div",{css:j,children:[(0,x.FD)("div",{children:[(0,x.Y)(i.T.Text,{bold:!0,children:(0,x.Y)(c.A,{id:"7FvLl2",defaultMessage:"Ignore outliers"})}),(0,x.Y)(o.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_703",delayDuration:0,content:(0,x.Y)(c.A,{id:"mN6m2e",defaultMessage:"Only display data points between the p5 and p95 of the data. This can help with chart readability in cases where outliers significantly affect the Y-axis range"}),side:"right",children:(0,x.FD)("span",{children:[" ",(0,x.Y)(s.JGH,{css:V.timeStepQuestionMarkIcon})]})})]}),(0,x.Y)(s.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_716","aria-label":"y-axis-ignore-outliers",checked:v.ignoreOutliers,onChange:G,label:" ",activeLabel:M.formatMessage({id:"bXA79t",defaultMessage:"On"}),inactiveLabel:M.formatMessage({id:"73ydH0",defaultMessage:"Off"}),disabledLabel:M.formatMessage({id:"Ka1jM2",defaultMessage:"Disabled"})})]})]}),(0,x.Y)(i.T.Title,{level:4,color:"secondary",css:(0,r.AH)({paddingTop:w.spacing.lg},""),children:"Advanced"}),(0,x.Y)(C,{title:M.formatMessage({id:"g7xNvF",defaultMessage:"Display points"}),compact:!0,children:(0,x.FD)(s.d98,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_747",name:M.formatMessage({id:"g7xNvF",defaultMessage:"Display points"}),value:v.displayPoints,onChange:e=>{let{target:t}=e;y((e=>({...e,displayPoints:t.value})))},children:[(0,x.FD)(s.EPn,{value:void 0,"aria-label":[M.formatMessage({id:"g7xNvF",defaultMessage:"Display points"}),M.formatMessage({id:"7nPjTL",defaultMessage:"Auto"})].join(": "),children:[(0,x.Y)(c.A,{id:"7nPjTL",defaultMessage:"Auto"})," ",(0,x.Y)(s.paO,{title:(0,x.Y)(c.A,{id:"ax3YJx",defaultMessage:"Show points on line charts if there are fewer than 60 data points per trace"}),children:(0,x.Y)(o.I,{})})]}),(0,x.Y)(s.EPn,{value:!0,"aria-label":[M.formatMessage({id:"g7xNvF",defaultMessage:"Display points"}),M.formatMessage({id:"QUMV9L",defaultMessage:"On"})].join(": "),children:(0,x.Y)(c.A,{id:"QUMV9L",defaultMessage:"On"})}),(0,x.Y)(s.EPn,{value:!1,"aria-label":[M.formatMessage({id:"g7xNvF",defaultMessage:"Display points"}),M.formatMessage({id:"NtaCJN",defaultMessage:"Off"})].join(": "),children:(0,x.Y)(c.A,{id:"NtaCJN",defaultMessage:"Off"})})]})}),(0,x.FD)(C,{title:(0,x.Y)(x.FK,{children:(0,x.Y)(c.A,{id:"qkRBUr",defaultMessage:"Line smoothing"})}),compact:!0,children:[(0,x.FD)(s.sxL.Group,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigurelinechart.tsx_838",name:"use-global-line-smoothness",value:Boolean(v.useGlobalLineSmoothing),onChange:e=>{let{target:t}=e;y((e=>({...e,useGlobalLineSmoothing:!0===t.value})))},children:[(0,x.Y)(s.sxL,{value:!0,children:"Use workspace settings"}),(0,x.Y)(s.sxL,{value:!1,children:"Custom"})]}),(0,x.Y)(L.o,{"data-testid":"smoothness-toggle",min:0,max:100,onChange:W,value:v.lineSmoothness?v.lineSmoothness:0,disabled:v.useGlobalLineSmoothing})]})]})},V={selectFull:{width:"100%"},timeStepQuestionMarkIcon:()=>({svg:{width:12,height:12}})};var W=n(10811),$=n(73724),q=n(75627),X=n(24789),Q=n(81850),J=n(6531),Z=n(6922);const ee=(0,W.Ng)((e=>{let{entities:{metricsByRunUuid:t}}=e;return{metricsByRunUuid:t}}),void 0,void 0,{areStatesEqual:(e,t)=>e.entities.metricsByRunUuid===t.entities.metricsByRunUuid})((e=>{var t,n,r,i,o;let{previewData:s,cardConfig:l,metricsByRunUuid:c,groupBy:u,globalLineChartConfig:p}=e;const{lineSmoothness:h,selectedXAxisMetricKey:m,xAxisKey:g}=(0,J.x)(l,p),f=(0,a.useMemo)((()=>s.some((e=>e.groupParentInfo))),[s]),{aggregateFunction:v}=u||{},y=(0,a.useMemo)((()=>{if(f){return[...(0,S.compact)(s.map((e=>e.groupParentInfo))).flatMap((e=>e.runUuids)),...(0,S.compact)(s.filter((e=>!e.groupParentInfo&&!e.belongsToGroup)).map((e=>{var t,n;return null!==(t=null===(n=e.runInfo)||void 0===n?void 0:n.runUuid)&&void 0!==t?t:void 0})))]}return(0,S.compact)(s.map((e=>e.runInfo))).map((e=>{var t;return null!==(t=e.runUuid)&&void 0!==t?t:""}))}),[s,f]),C=(0,a.useMemo)((()=>{const e=(e=>{var t;const n=[e.metricKey];var r;if(!(0,T.Iq)()||e.yAxisKey!==d.Kb.EXPRESSION)return null!==(r=e.selectedMetricKeys)&&void 0!==r?r:n;const i=null===(t=e.yAxisExpressions)||void 0===t?void 0:t.reduce(((e,t)=>(t.variables.forEach((t=>e.add(t))),e)),new Set);return void 0===i?n:Array.from(i)})(l),t=m?[m]:[];return e.concat(t)}),[l,m]),{resultsByRunUuid:b,isLoading:_}=(0,X.S)({runUuids:y,metricKeys:C,enabled:!0,maxResults:320,autoRefreshEnabled:!1}),w=(0,a.useMemo)((()=>s.map((e=>{const t=C.reduce(((t,n)=>{var r,i;const o=null===(r=b[e.uuid])||void 0===r||null===(i=r[n])||void 0===i?void 0:i.metricsHistory;return o&&(t[n]=l.ignoreOutliers?(0,D.c1)(o):o),t}),{});return{...e,metricsHistory:t}}))),[C,b,s,l.ignoreOutliers]),M=(0,Q.B)({enabled:f,ungroupedRunsData:w,metricKeys:C,sampledDataResultsByRunUuid:b,aggregateFunction:v,selectedXAxisMetricKey:g===D.fj.METRIC?m:void 0,ignoreOutliers:null!==(t=l.ignoreOutliers)&&void 0!==t&&t}),I=f?M:w,{setTooltip:E,resetTooltip:R}=(0,q.uN)(l,q.QS.MultipleTracesWithScanline);if(_)return(0,x.Y)(Z.Us,{});const A=(e,t)=>{if(!(0,S.isUndefined)(e)&&!(0,S.isUndefined)(t))return[e,t]},k=A(null===(n=l.range)||void 0===n?void 0:n.xMin,null===(r=l.range)||void 0===r?void 0:r.xMax),Y=A(null===(i=l.range)||void 0===i?void 0:i.yMin,null===(o=l.range)||void 0===o?void 0:o.yMax);return(0,x.Y)($.h,{runsData:I,metricKey:l.metricKey,selectedMetricKeys:l.selectedMetricKeys,scaleType:l.scaleType,xAxisScaleType:l.xAxisScaleType,lineSmoothness:h,xAxisKey:g,selectedXAxisMetricKey:m,displayPoints:l.displayPoints,yAxisExpressions:l.yAxisExpressions,yAxisKey:l.yAxisKey,useDefaultHoverBox:!1,onHover:E,onUnhover:R,xRange:k,yRange:Y})}));var te=n(59264),ne=n(65243);var re=n(66117);var ie=n(10661);var oe=n(21809),se=n(70391);var ae={name:"i1civq",styles:"display:flex;justify-content:center;align-items:center;text-align:center"},le={name:"1vcob1d",styles:"display:flex;justify-content:center;align-items:center"};const ce=e=>{let{state:t,onStateChange:n,metricKeyList:r,paramKeyList:i}=e;const{formatMessage:o}=(0,l.A)(),s=(0,a.useCallback)(((e,t)=>{const r=(0,y.tG)(e,"METRIC")?"METRIC":"PARAM",i=(0,y.dz)(e,r);n((e=>({...e,[t]:{key:i,type:r}})))}),[n]);(0,a.useCallback)((e=>{n((t=>({...t,runsCountToCompare:e})))}),[n]);return(0,a.useEffect)((()=>{var e,n,o;const a=null===r||void 0===r?void 0:r[0],l=null===i||void 0===i?void 0:i[0];null!==(e=t.xaxis)&&void 0!==e&&e.key||(a?s((0,y.GF)("METRIC",a),"xaxis"):l&&s((0,y.GF)("PARAM",l),"xaxis")),null!==(n=t.yaxis)&&void 0!==n&&n.key||(a?s((0,y.GF)("METRIC",a),"yaxis"):l&&s((0,y.GF)("PARAM",l),"yaxis")),null!==(o=t.zaxis)&&void 0!==o&&o.key||(a?s((0,y.GF)("METRIC",a),"zaxis"):l&&s((0,y.GF)("PARAM",l),"zaxis"))}),[t.xaxis,t.yaxis,t.zaxis,s,r,i]),(0,x.FD)(x.FK,{children:[(0,x.Y)(C,{title:o({id:"2Us2jl",defaultMessage:"X axis"}),children:(0,x.Y)(b,{value:t.xaxis.key?(0,y.GF)(t.xaxis.type,t.xaxis.key):"",onChange:e=>{s(e,"xaxis")},paramKeyList:i,metricKeyList:r})}),(0,x.Y)(C,{title:o({id:"8YmId5",defaultMessage:"Y axis"}),children:(0,x.Y)(b,{value:t.yaxis.key?(0,y.GF)(t.yaxis.type,t.yaxis.key):"",onChange:e=>{s(e,"yaxis")},paramKeyList:i,metricKeyList:r})}),(0,x.Y)(C,{title:o({id:"cB0/61",defaultMessage:"Z axis"}),children:(0,x.Y)(b,{value:t.zaxis.key?(0,y.GF)(t.zaxis.type,t.zaxis.key):"",onChange:e=>{s(e,"zaxis")},paramKeyList:i,metricKeyList:r})})]})};w.push({value:100,label:(0,x.Y)(c.A,{id:"Bbm59f",defaultMessage:"100"})},{value:250,label:(0,x.Y)(c.A,{id:"W8bOkn",defaultMessage:"250"})},{value:500,label:(0,x.Y)(c.A,{id:"Bnruyp",defaultMessage:"500"})});const de=e=>{let{state:t,onStateChange:n,metricKeyList:r,paramKeyList:i}=e;const{formatMessage:o}=(0,l.A)(),s=(0,a.useCallback)(((e,t)=>{const r=(0,y.tG)(e,"METRIC")?"METRIC":"PARAM",i=(0,y.dz)(e,r);n((e=>({...e,[t]:{key:i,type:r}})))}),[n]);(0,a.useCallback)((e=>{n((t=>({...t,runsCountToCompare:e})))}),[n]);return(0,a.useEffect)((()=>{var e,n;const o=null===r||void 0===r?void 0:r[0],a=null===i||void 0===i?void 0:i[0];null!==(e=t.xaxis)&&void 0!==e&&e.key||(o?s((0,y.GF)("METRIC",o),"xaxis"):a&&s((0,y.GF)("PARAM",a),"xaxis")),null!==(n=t.yaxis)&&void 0!==n&&n.key||(o?s((0,y.GF)("METRIC",o),"yaxis"):a&&s((0,y.GF)("PARAM",a),"yaxis"))}),[t.xaxis,t.yaxis,s,r,i]),(0,x.FD)(x.FK,{children:[(0,x.Y)(C,{title:o({id:"vBKFgp",defaultMessage:"X axis"}),children:(0,x.Y)(b,{value:t.xaxis.key?(0,y.GF)(t.xaxis.type,t.xaxis.key):"",onChange:e=>{s(e,"xaxis")},paramKeyList:i,metricKeyList:r})}),(0,x.Y)(C,{title:o({id:"DaF+KK",defaultMessage:"Y axis"}),children:(0,x.Y)(b,{value:t.yaxis.key?(0,y.GF)(t.yaxis.type,t.yaxis.key):"",onChange:e=>{s(e,"yaxis")},paramKeyList:i,metricKeyList:r})})]})};var ue=n(73150);var pe={name:"8irbms",styles:"display:inline-flex;align-items:center"};const he=e=>{let{state:t,onStateChange:n,metricKeyList:o,paramKeyList:u,groupBy:p}=e;const h=(0,a.useCallback)((e=>{n((t=>{const n=t.compareGroups;return n.includes(e)?{...t,compareGroups:n.filter((t=>t!==e))}:{...t,compareGroups:[...n,e]}}))}),[n]),m=(0,a.useCallback)((e=>n((t=>({...t,chartName:e.target.value})))),[n]),g=(0,a.useCallback)((e=>n((t=>({...t,showChangeFromBaseline:e})))),[n]),f=(0,a.useCallback)((e=>n((t=>({...t,showDifferencesOnly:e})))),[n]),{theme:v}=(0,i.u)(),{formatMessage:y}=(0,l.A)();return(0,x.FD)(x.FK,{children:[(0,x.FD)(C,{title:y({id:"46xd2Z",defaultMessage:"Compare"}),children:[(0,x.Y)(s.Sc0.Group,{id:"checkbox-group",defaultValue:t.compareGroups,children:Object.values(d.K8).map((e=>{var n;const r=!!p&&d.Nf.includes(e);return(0,x.FD)("div",{css:pe,children:[(0,x.Y)(s.Sc0,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfiguredifferencechart.tsx_98",value:e,isChecked:null===(n=t.compareGroups)||void 0===n?void 0:n.includes(e),onChange:()=>h(e),disabled:r,children:e},e),r&&(0,x.Y)(s.Jsb,{title:(0,x.Y)(c.A,{id:"LKAZ2n",defaultMessage:"Disable grouped runs to compare"})})]},e)}))}),(0,x.FD)("div",{css:(0,r.AH)({display:"flex",flexDirection:"column",padding:`${v.spacing.md}px 0px`,gap:v.spacing.sm},""),children:[(0,x.Y)(s.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfiguredifferencechart.tsx_129",checked:t.showChangeFromBaseline,onChange:g,label:y({id:"9g0sMx",defaultMessage:"Show change from baseline"})}),(0,x.Y)(s.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfiguredifferencechart.tsx_138",checked:t.showDifferencesOnly,onChange:f,label:y({id:"nWeQCU",defaultMessage:"Show differences only"})})]})]}),(0,x.Y)(C,{title:y({id:"ZgAOhX",defaultMessage:"Chart name"}),children:(0,x.Y)(Y.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfiguredifferencechart.tsx_157",value:t.chartName,onChange:m})})]})};var me=n(99061);const ge=e=>{let{previewData:t,state:n,onStateChange:r,imageKeyList:i}=e;const{stepMarks:o,maxMark:c,minMark:d}=(0,me._)({data:t,selectedImageKeys:n.imageKeys||[]}),u=((0,a.useCallback)((e=>{r((t=>({...t,imageKeys:e})))}),[r]),(0,a.useCallback)((e=>{r((t=>({...t,step:e})))}),[r])),{formatMessage:p}=(0,l.A)(),h=e=>{r((t=>{var n;const r=t;var i;return null!==(n=r.imageKeys)&&void 0!==n&&n.includes(e)?{...r,imageKeys:null===(i=r.imageKeys)||void 0===i?void 0:i.filter((t=>t!==e))}:{...r,imageKeys:[...r.imageKeys||[],e]}}))};return(0,x.FD)(x.FK,{children:[(0,x.Y)(C,{title:p({id:"6Jan3t",defaultMessage:"Images"}),children:(0,x.FD)(s.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_config_runschartsconfigureimagechart.tsx_84",value:n.imageKeys,label:"Images",multiSelect:!0,children:[(0,x.Y)(s.gGe,{onClear:()=>{r((e=>({...e,imageKeys:[]})))},minWidth:275}),(0,x.Y)(s.dn6,{matchTriggerWidth:!0,children:(0,x.Y)(s.HI_,{children:i.map((e=>{var t;return(0,x.Y)(s.jTC,{value:e,onChange:h,checked:null===(t=n.imageKeys)||void 0===t?void 0:t.includes(e)},e)}))})})]})}),(0,x.Y)(C,{title:"Step",children:(0,x.Y)(L.o,{max:c,min:d,marks:o,value:n.step,disabled:Object.keys(o).length<=1,onChange:u})})]})};var fe=n(34389),ve=n(25866);var ye={name:"1bivvgq",styles:"width:100%;overflow:auto hidden"};const xe=e=>{let{state:t,onStateChange:n,paramKeyList:r,metricKeysByDataset:i}=e;const{formatMessage:o}=(0,l.A)(),s=(0,a.useMemo)((()=>{var e;return null!==(e=null===i||void 0===i?void 0:i.map((e=>{let{dataAccessKey:t,metricKey:n,datasetName:r}=e;return{key:JSON.stringify(["METRIC",t]),dataAccessKey:t,datasetName:r,metricKey:n}})))&&void 0!==e?e:[]}),[i]),c=(0,a.useMemo)((()=>{var e;return null!==(e=null===r||void 0===r?void 0:r.map((e=>({key:JSON.stringify(["PARAM",e]),paramKey:e}))))&&void 0!==e?e:[]}),[r]),d=(0,a.useCallback)((e=>t=>{const r=s.find((e=>{let{key:n}=e;return n===t}));if(r){const{dataAccessKey:t,datasetName:i,metricKey:o}=r;n((n=>({...n,[e]:{key:o,type:"METRIC",datasetName:i,dataAccessKey:t}})))}const i=c.find((e=>{let{key:n}=e;return n===t}));i&&n((t=>({...t,[e]:{key:i.paramKey,type:"PARAM"}})))}),[n,s,c]);(0,a.useEffect)((()=>{for(const n of["xaxis","yaxis"]){var e;null!==(e=t[n])&&void 0!==e&&e.key||(null!==s&&void 0!==s&&s[0]?d(n)(s[0].key):null!==c&&void 0!==c&&c[0]&&d(n)(c[0].key))}}),[t,s,c,d]);const u=(0,a.useCallback)((e=>{if("METRIC"===t[e].type){const n=s.find((n=>{let{dataAccessKey:r}=n;return r===t[e].dataAccessKey}));if(n)return n.key}if("PARAM"===t[e].type){const n=c.find((n=>{let{paramKey:r}=n;return r===t[e].key}));if(n)return n.key}return""}),[t,s,c]),p=(0,a.useMemo)((()=>u("xaxis")),[u]),h=(0,a.useMemo)((()=>u("yaxis")),[u]);return(0,x.FD)(x.FK,{children:[(0,x.Y)(C,{title:o({id:"vBKFgp",defaultMessage:"X axis"}),children:(0,x.Y)(_,{value:p,onChange:d("xaxis"),metricOptions:s,paramOptions:c,id:"mlflow.charts.chart_configure.scatter.x_axis"})}),(0,x.Y)(C,{title:o({id:"DaF+KK",defaultMessage:"Y axis"}),children:(0,x.Y)(_,{value:h,onChange:d("yaxis"),metricOptions:s,paramOptions:c,id:"mlflow.charts.chart_configure.scatter.y_axis"})})]})};var Ce=n(31276);const be={[d.zL.BAR]:e=>{var t;let{previewData:n,cardConfig:r}=e;const{resetTooltip:i,setTooltip:o}=(0,q.uN)(r),s=null!==(t=r.dataAccessKey)&&void 0!==t?t:r.metricKey;return(0,x.Y)(ne.u,{useDefaultHoverBox:!1,displayRunNames:!1,displayMetricKey:!1,metricKey:s,runsData:n,margin:te.J,onHover:o,onUnhover:i})},[d.zL.CONTOUR]:e=>{let{previewData:t,cardConfig:n}=e;const{resetTooltip:r,setTooltip:i}=(0,q.uN)(n),o=(0,a.useMemo)((()=>{const{xaxis:e,yaxis:t,zaxis:r}=n;return JSON.stringify({xaxis:e,yaxis:t,zaxis:r})}),[n]);return(0,x.Y)(re.a,{xAxis:n.xaxis,yAxis:n.yaxis,zAxis:n.zaxis,runsData:t,onHover:i,onUnhover:r,useDefaultHoverBox:!1},o)},[d.zL.LINE]:ee,[d.zL.PARALLEL]:e=>{let{previewData:t,cardConfig:n,groupBy:r}=e;const i=(0,se.ad)(n),{setTooltip:o,resetTooltip:l}=(0,q.uN)(n);if((0,a.useMemo)((()=>{var e;return null===(e=n.selectedParams)||void 0===e?void 0:e.some((e=>null===t||void 0===t?void 0:t.some((t=>{var n;return isNaN(Number(null===(n=t.params[e])||void 0===n?void 0:n.value))}))),[n.selectedParams,t])}),[n.selectedParams,t])&&r)return(0,x.Y)("div",{css:ae,children:(0,x.Y)(c.A,{id:"kTkJkb",defaultMessage:"Parallel coordinates chart does not support aggregated string values. Use other parameters or disable run grouping to continue."})});const d=i?(0,se.fM)(t,n.selectedParams,n.selectedMetrics):[];return i?d.length?(0,x.Y)(oe.A,{selectedMetrics:n.selectedMetrics,selectedParams:n.selectedParams,data:d,axesRotateThreshold:6,onHover:o,onUnhover:l,fallback:(0,x.Y)(Z.Us,{})}):(0,x.Y)(s.SvL,{description:(0,x.Y)(c.A,{id:"lTrmPI",defaultMessage:"No matching data found for the available runs."}),image:(0,x.Y)(s.xfv,{})}):(0,x.Y)("div",{css:le,children:"Select at least two metrics and params first"})},[d.zL.SCATTER]:e=>{let{previewData:t,cardConfig:n}=e;const{resetTooltip:r,setTooltip:i}=(0,q.uN)(n),o=(0,a.useMemo)((()=>{const{xaxis:e,yaxis:t}=n;return JSON.stringify({xaxis:e,yaxis:t})}),[n]);return(0,x.Y)(ie.i,{xAxis:n.xaxis,yAxis:n.yaxis,runsData:t,onHover:i,onUnhover:r,useDefaultHoverBox:!1},o)},[d.zL.DIFFERENCE]:Ce.I,[d.zL.IMAGE]:e=>{var t;let{previewData:n,cardConfig:r,setCardConfig:i,groupBy:o}=e;if(n.some((e=>Boolean(e.tags[ve.Cr])))&&0===(null===r||void 0===r||null===(t=r.imageKeys)||void 0===t?void 0:t.length))return(0,x.Y)(s.SvL,{title:(0,x.Y)(c.A,{id:"qN1Uqj",defaultMessage:"No images configured for preview"}),description:(0,x.Y)(c.A,{id:"Zpi7wv",defaultMessage:"Please use controls on the left to select images to be compared"})});const a=(0,x.Y)(fe.f,{previewData:n,cardConfig:r,setCardConfig:i,groupBy:o});return(0,x.Y)("div",{css:ye,children:a})}};var _e={name:"1uahl7",styles:"width:1280px"},we={name:"1d3w5wq",styles:"width:100%"},Me={name:"1nbuksq",styles:"overflow:auto;flex-grow:1"},Se={name:"yawz8a",styles:"min-height:500px;height:100%;width:500px;padding:32px 0px"};const Ie=e=>{let{onCancel:t,onSubmit:n,config:y,chartRunData:b,metricKeyList:_,metricKeysByDataset:w,paramKeyList:M,groupBy:I,supportedChartTypes:E,globalLineChartConfig:A}=e;const Y=e=>!E||E.includes(e),{theme:D}=(0,i.u)(),L=`1px solid ${D.colors.actionDefaultBorderDefault}`,[F,K]=(0,a.useState)(y),N=Boolean(F.uuid),H=(0,a.useCallback)((e=>{if(!e)return;const t=d.i$.getEmptyChartCardByType(e,!0);t&&K(t)}),[]),O=(0,a.useMemo)((()=>b.filter((e=>{let{hidden:t}=e;return!t})).reverse()),[b]),B=(0,a.useMemo)((()=>{const e=new Set;return O.forEach((t=>{Object.keys(t.images).forEach((t=>{e.add(t)}))})),Array.from(e).sort()}),[O]),{formatMessage:P}=(0,l.A)();let U=!1;if(F.type===d.zL.LINE){var z;U=0===(null!==(z=F.selectedMetricKeys)&&void 0!==z?z:[]).length}return(0,x.Y)(o.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsconfiguremodal.tsx_232",visible:!0,onCancel:t,onOk:()=>n(F),title:P(N?{id:"kBXHTA",defaultMessage:"Edit chart"}:{id:"6y36Fr",defaultMessage:"Add new chart"}),okButtonProps:{"data-testid":"experiment-view-compare-runs-chart-modal-confirm",disabled:U},cancelText:P({id:"TlVXcS",defaultMessage:"Cancel"}),okText:P(N?{id:"vPnoNk",defaultMessage:"Save changes"}:{id:"UzZf+2",defaultMessage:"Add chart"}),size:"wide",css:_e,dangerouslySetAntdProps:{bodyStyle:{overflowY:"hidden",display:"flex"}},children:(0,x.FD)("div",{css:(0,r.AH)({display:"flex",width:"100%",gridTemplateColumns:"300px 1fr",gap:D.spacing.md,borderTop:L,borderBottom:L},""),children:[(0,x.FD)("div",{css:(0,r.AH)({overflowY:"auto",borderRight:L,padding:`${D.spacing.md}px ${D.spacing.md}px ${D.spacing.md}px 0px`,width:"300px"},""),children:[!N&&(0,x.Y)(C,{title:"Chart type",children:(0,x.FD)(s.XAe,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsconfiguremodal.tsx_296",id:"chart-type-select",css:we,value:F.type,onChange:e=>{let{target:t}=e;const n=t.value;Object.values(d.zL).includes(n)&&H(n)},children:[Y(d.zL.BAR)&&(0,x.Y)(s.wS0,{value:d.zL.BAR,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(u.h,{}),(0,x.Y)(c.A,{id:"gdK07H",defaultMessage:"Bar chart"})]})}),Y(d.zL.SCATTER)&&(0,x.Y)(s.wS0,{value:d.zL.SCATTER,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(g.h,{}),(0,x.Y)(c.A,{id:"Q0i5xG",defaultMessage:"Scatter chart"})]})}),Y(d.zL.LINE)&&(0,x.Y)(s.wS0,{value:d.zL.LINE,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(h.h,{}),(0,x.Y)(c.A,{id:"CyTYL6",defaultMessage:"Line chart"})]})}),Y(d.zL.PARALLEL)&&(0,x.Y)(s.wS0,{value:d.zL.PARALLEL,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(m.h,{}),(0,x.Y)(c.A,{id:"FC9mcS",defaultMessage:"Parallel coordinates"})]})}),Y(d.zL.CONTOUR)&&(0,x.Y)(s.wS0,{value:d.zL.CONTOUR,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(p.h,{}),(0,x.Y)(c.A,{id:"6XfILf",defaultMessage:"Contour chart"})]})}),(0,T.uP)()&&Y(d.zL.DIFFERENCE)&&(0,x.Y)(s.wS0,{value:d.zL.DIFFERENCE,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(f.h,{}),(0,x.Y)(c.A,{id:"G5mMJI",defaultMessage:"Difference view"})]})}),Y(d.zL.IMAGE)&&(0,x.Y)(s.wS0,{value:d.zL.IMAGE,children:(0,x.FD)("div",{css:Ee.chartTypeOption(D),children:[(0,x.Y)(v.h,{}),(0,x.Y)(c.A,{id:"5m2VPy",defaultMessage:"Image grid"})]})})]})}),(j=F.type,j===d.zL.BAR?(0,x.Y)(R,{metricKeyList:_,metricKeysByDataset:w,state:F,onStateChange:K}):j===d.zL.CONTOUR?(0,x.Y)(ce,{metricKeyList:_,paramKeyList:M,state:F,onStateChange:K}):j===d.zL.LINE?(0,x.Y)(G,{metricKeyList:_,state:F,onStateChange:K}):j===d.zL.PARALLEL?(0,x.Y)(k,{metricKeyList:_,paramKeyList:M,state:F,onStateChange:K}):j===d.zL.SCATTER?(0,S.isEmpty)(w)?(0,x.Y)(de,{metricKeyList:_,paramKeyList:M,state:F,onStateChange:K}):(0,x.Y)(xe,{paramKeyList:M,metricKeysByDataset:w,state:F,onStateChange:K}):(0,T.uP)()&&j===d.zL.DIFFERENCE?(0,x.Y)(he,{metricKeyList:_,paramKeyList:M,state:F,onStateChange:K,groupBy:I}):j===d.zL.IMAGE?(0,x.Y)(ge,{previewData:O,imageKeyList:B,state:F,onStateChange:K}):null)]}),(0,x.Y)("div",{css:Me,children:(0,x.Y)(q.W,{contextData:{runs:b},component:ue.X,hoverOnly:!0,children:(0,x.Y)("div",{css:Se,children:(e=>{if(!e)return null;const t=be[e];return t?(0,x.Y)(t,{previewData:O,cardConfig:F,groupBy:I,setCardConfig:K,globalLineChartConfig:A}):null})(F.type)})})})]})});var j},Ee={chartTypeOption:e=>({display:"grid",gridTemplateColumns:`${e.general.iconSize+e.spacing.xs}px 1fr`,gap:e.spacing.xs,alignItems:"center"}),field:{display:"grid",gridTemplateColumns:"80px 1fr",marginBottom:16}}},21039:function(e,t,n){"use strict";n.d(t,{K:function(){return r}});const r={CARD_PREVIEW:1,CARD_DRAGGED:2,TOOLTIP_CONTAINER:3,SEARCH_BAR:3,TOOLTIP:4}},21809:function(e,t,n){"use strict";var r=n(48012),i=n(31014),o=n(50111);const s=i.lazy((()=>Promise.all([n.e(9800),n.e(5275)]).then(n.bind(n,47300))));t.A=e=>{let{fallback:t,...n}=e;return(0,o.Y)(i.Suspense,{fallback:null!==t&&void 0!==t?t:(0,o.Y)(r.PLz,{}),children:(0,o.Y)(s,{...n})})}},21879:function(e,t,n){"use strict";n.d(t,{e:function(){return a},i:function(){return s}});var r=n(31014),i=n(50111);const o=r.createContext((e=>e)),s=e=>{let{children:t,setUIState:n}=e;return(0,i.Y)(o.Provider,{value:n,children:t})},a=()=>r.useContext(o)},22853:function(e,t,n){"use strict";n.d(t,{v:function(){return a}});var r=n(31014),i=n(21879),o=n(58898),s=n(91144);const a=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=(0,i.e)(),a=(0,r.useRef)(e);a.current=e;const l=(0,r.useCallback)(((e,r,i)=>{n((n=>{if(e===o.oy.CUSTOM&&r){var l;const e={...n.runsVisibilityMap},o=null===(l=a.current.find((e=>{let{rowUuid:t,groupParentInfo:n}=e;return t===r&&n})))||void 0===l?void 0:l.groupParentInfo;if(o&&(0,s.Bh)()&&!1===t)for(const t of o.runUuids)e[t]=!i;else e[r]=!i;return{...n,runsVisibilityMap:e}}return[o.oy.SHOWALL,o.oy.HIDEALL,o.oy.FIRST_10_RUNS,o.oy.FIRST_20_RUNS].includes(e)?{...n,runsHiddenMode:e,runsHidden:[],runsVisibilityMap:{}}:n}))}),[n,t]),c=(0,r.useCallback)(((e,r)=>{n((n=>{if(e===o.oy.SHOWALL)return{...n,runsHiddenMode:o.oy.SHOWALL,runsHidden:[]};if(e===o.oy.HIDEALL)return{...n,runsHiddenMode:o.oy.HIDEALL,runsHidden:[]};if(e===o.oy.FIRST_10_RUNS)return{...n,runsHiddenMode:o.oy.FIRST_10_RUNS,runsHidden:[]};if(e===o.oy.FIRST_20_RUNS)return{...n,runsHiddenMode:o.oy.FIRST_20_RUNS,runsHidden:[]};if(r){var i;const e=a.current.filter((e=>{let{hidden:t}=e;return t})).map((e=>{let{groupParentInfo:t,rowUuid:n,runUuid:r}=e;return t?n:r})),l=null===(i=a.current.find((e=>{let{rowUuid:t,groupParentInfo:n}=e;return t===r&&n})))||void 0===i?void 0:i.groupParentInfo;if(l&&(0,s.Bh)()&&!1===t){let t=[];return t=l.allRunsHidden?e.filter((e=>!l.runUuids.includes(e))):e.concat(l.runUuids.filter((t=>!e.includes(t)))),{...n,runsHiddenMode:o.oy.CUSTOM,runsHidden:t}}const c=e.includes(r)?e.filter((e=>e!==r)):[...e,r];return{...n,runsHiddenMode:o.oy.CUSTOM,runsHidden:c}}return n}))}),[n,t]);return(0,s.Rn)()?l:c}},23541:function(e,t,n){"use strict";var r=n(89555),i=(n(31014),n(16026)),o=n(32599),s=n(50111);t.A=e=>{let{labelData:t,fullScreen:n,children:a}=e;const{theme:l}=(0,o.u)(),c=n?100:32,d=n?l.spacing.lg:l.spacing.md;return(0,s.FD)(s.FK,{children:[(0,s.Y)("div",{css:(0,r.AH)({height:`calc(100% - ${c+d}px)`},""),children:a}),(0,s.Y)(i.A,{labelData:t,height:c,fullScreen:n})]})}},24789:function(e,t,n){"use strict";n.d(t,{S:function(){return f}});var r=n(9133),i=n(31014),o=n(10811),s=n(36118),a=n(16297),l=n(32039),c=n(76010),d=n(91144),u=n(56675),p=n(67671),h=n(95947),m=n(88464);const g=u.J1`
  query GetMetricHistoryBulkInterval($data: MlflowGetMetricHistoryBulkIntervalInput!)
  @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetMetricHistoryBulkInterval(input: $data) {
      __typename
      metrics {
        timestamp
        step
        runId
        key
        value
      }
      apiError {
        code
        message
      }
    }
  }
`,f=e=>{const{metricKeys:t,enabled:n,autoRefreshEnabled:u,runUuids:f}=e,v=(0,d.X1)()&&1===t.length&&f.length<=100,y=(e=>{const{metricKeys:t,runUuids:n,enabled:d,maxResults:u,range:p,autoRefreshEnabled:h}=e,m=(0,o.wA)(),{resultsByRunUuid:g,isLoading:f,isRefreshing:v}=(0,o.d4)((e=>{const i=(0,s.Cx)(p);let o=!1,a=!1;const l=n.map((n=>{const r=t.reduce(((t,r)=>{var s,l;const c=null===(s=e.entities.sampledMetricsByRunUuid[n])||void 0===s||null===(l=s[r])||void 0===l?void 0:l[i];return c?(a=a||Boolean(c.loading),o=o||Boolean(c.refreshing),t[r]=c,t):t}),{});return{runUuid:n,...r}}));return{isLoading:a,isRefreshing:o,resultsByRunUuid:(0,r.keyBy)(l,"runUuid")}}),((e,t)=>(0,r.isEqual)(e.resultsByRunUuid,t.resultsByRunUuid)&&e.isLoading===t.isLoading&&e.isRefreshing===t.isRefreshing)),y=(0,i.useCallback)((()=>{t.forEach((e=>{(0,r.chunk)(n,100).forEach((t=>{const n=(0,a.H)(t,e,u,p,"all");m(n)}))}))}),[m,u,n,t,p]),x=(0,i.useRef)(void 0),C=(0,i.useRef)(h&&e.enabled);C.current=h&&e.enabled;const b=(0,i.useMemo)((()=>n.join(",")),[n]);return(0,i.useEffect)((()=>{d&&!h&&t.forEach((e=>{(0,r.chunk)(n,100).forEach((t=>{const n=(0,a.H)(t,e,u,p);m(n)}))}))}),[m,u,n,t,p,d,h]),(0,i.useEffect)((()=>{let e=!1;if(!d||!h)return;const n=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const n=b.split(",").filter((e=>""!==e));await Promise.all(t.map((async t=>Promise.all((0,r.chunk)(n,100).map((async n=>m((0,a.H)(n,t,u,p,e?"auto":void 0))))))))},i=async()=>{if(C.current&&!e){try{await n(!0)}catch(t){c.A.logErrorAndNotifyUser(t)}clearTimeout(x.current),C.current&&!e&&(x.current=window.setTimeout(i,l.QB))}};return n().then(i),()=>{e=!0,clearTimeout(x.current)}}),[m,u,b,t,p,d,h]),{isLoading:f,isRefreshing:v,resultsByRunUuid:g,refresh:y}})({...e,enabled:n&&!v,autoRefreshEnabled:u&&!v}),x=(e=>{var t,n,o;let{metricKey:s,runUuids:a,autoRefreshEnabled:d,enabled:u,maxResults:f=320,range:v}=e;const y=(0,m.A)(),{data:x,refetch:C,startPolling:b,stopPolling:_,networkStatus:w,error:M}=(0,h.I)(g,{skip:!u,notifyOnNetworkStatusChange:!0,pollInterval:d?l.QB:void 0,onCompleted(e){var t,n,r,i;"RESOURCE_DOES_NOT_EXIST"===(null===(t=e.mlflowGetMetricHistoryBulkInterval)||void 0===t||null===(n=t.apiError)||void 0===n?void 0:n.code)?c.A.displayGlobalErrorNotification(y.formatMessage({id:"hm9IM7",defaultMessage:"Requested resource does not exist"})):null!==(r=e.mlflowGetMetricHistoryBulkInterval)&&void 0!==r&&null!==(i=r.apiError)&&void 0!==i&&i.message&&c.A.logErrorAndNotifyUser(new Error(e.mlflowGetMetricHistoryBulkInterval.apiError.message))},variables:{data:{runIds:a,metricKey:s,startStep:null!==(t=null===v||void 0===v?void 0:v[0])&&void 0!==t?t:null,endStep:null!==(n=null===v||void 0===v?void 0:v[1])&&void 0!==n?n:null,maxResults:f}}});return(0,i.useEffect)((()=>{d?b(l.QB):_()}),[d,b,_]),{resultsByRunUuid:(0,i.useMemo)((()=>{if(x){var e;const t=null===x||void 0===x||null===(e=x.mlflowGetMetricHistoryBulkInterval)||void 0===e?void 0:e.metrics,n=(0,r.groupBy)(t,"runId");return(0,r.keyBy)(a.map((e=>{var t;return{runUuid:e,[s]:{metricsHistory:null===(t=n[e])||void 0===t?void 0:t.map((e=>{let{key:t,step:n,timestamp:r,value:i}=e;return{key:null!==t&&void 0!==t?t:void 0,step:Number(n),timestamp:Number(r),value:null!==i&&void 0!==i?i:void 0}}))}}})),"runUuid")}return{}}),[x,s,a]),isLoading:w===p.p.loading||w===p.p.setVariables,isRefreshing:w===p.p.poll,refresh:C,error:M,apiError:null===x||void 0===x||null===(o=x.mlflowGetMetricHistoryBulkInterval)||void 0===o?void 0:o.apiError}})({...e,metricKey:t[0],enabled:n&&v,autoRefreshEnabled:u&&v});return v?x:y}},24947:function(e,t,n){"use strict";n.d(t,{z:function(){return l}});var r=n(51079),i=n.n(r),o=n(31014),s=n(93215),a=n(76010);const l=()=>{const e=(0,s.g)(),t=(0,s.zy)(),n=(0,o.useMemo)((()=>decodeURIComponent(t.search)),[t.search]),r=(0,o.useMemo)((()=>{const e=i().parse(n.substring(1));if(e.experiments){const t=e.experiments;return(null===t||void 0===t?void 0:t.toString())||""}return""}),[n]);return(0,o.useMemo)((()=>{if(null!==e&&void 0!==e&&e.experimentId)return[null===e||void 0===e?void 0:e.experimentId];if(r)try{return JSON.parse(r)}catch{return a.A.logErrorAndNotifyUser(`Could not parse experiment query parameter ${r}`),""}return[]}),[r,null===e||void 0===e?void 0:e.experimentId])}},25790:function(e,t,n){"use strict";n.d(t,{Co:function(){return h},Iq:function(){return a},PQ:function(){return l},WF:function(){return u},ZY:function(){return m},nD:function(){return c},pO:function(){return d}});var r=n(9133),i=n(31014),o=n(91144);const s=(e,t)=>function(n,r,i){var o,s;let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;const l=-1===r&&-1===i;var c,d,u,p;(null===(o=n.querySelector(".is-hover-highlight"))||void 0===o||o.classList.remove("is-hover-highlight"),r>-1)&&(null===(c=n.querySelectorAll(e)[r])||void 0===c||c.classList.add("is-hover-highlight"));(null===(s=n.querySelector(".is-selection-highlight"))||void 0===s||s.classList.remove("is-selection-highlight"),i>-1)&&(null===(d=n.querySelectorAll(e)[i])||void 0===d||d.classList.add("is-selection-highlight"));if(a>0){const t=i>-1?i-a:r>-1?r-a:-1;n.querySelectorAll(e).forEach(((e,n)=>{e.classList.toggle("is-band",n>=0&&n<a),e.classList.toggle("is-band-highlighted",n===t)}))}else n.querySelectorAll(e).forEach((e=>e.classList.remove("is-band")));l?null===(u=n.querySelector(t))||void 0===u||u.classList.remove("is-highlight"):null===(p=n.querySelector(t))||void 0===p||p.classList.add("is-highlight")},a=s("svg .trace.bars g.point",".trace.bars"),l=s("svg .scatterlayer g.trace",".scatterlayer"),c=s("svg .scatterlayer path.point",".trace.scatter"),d=function(e,t,n,s){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;const l=(0,i.useRef)(n);l.current=n;const c=(0,i.useMemo)((()=>e&&t?n.findIndex((e=>{let{uuid:n}=e;return n===t})):-1),[n,e,t]),[d,u]=(0,i.useState)(-1),{onHighlightChange:p}=m();(0,i.useEffect)((()=>{(0,o.GA)()||e&&s(e,d,c,a)}),[s,e,c,d,a]),(0,i.useEffect)((()=>{(0,o.GA)()&&e&&s(e,-1,c,a)}),[s,e,c,a]);const h=(0,i.useRef)(c);h.current=c;const g=(0,i.useCallback)((t=>{if(!e)return;const n=l.current.findIndex((e=>{let{uuid:n}=e;return n===t}));s(e,n,h.current,a)}),[s,e,a]);return(0,i.useEffect)((()=>p(g)),[p,g]),{selectedTraceIndex:c,hoveredPointIndex:d,setHoveredPointIndex:(0,o.GA)()?r.noop:u}};let u=function(e){return e[e.NONE=0]="NONE",e[e.CHART=1]="CHART",e[e.TABLE=2]="TABLE",e}({});const p=(0,i.createContext)({highlightDataTrace:()=>{},onHighlightChange:()=>()=>{}}),h=e=>{let{children:t}=e;const n=(0,i.useRef)([]),s=(0,i.useRef)(!1),a=(0,i.useMemo)((()=>{if(!(0,o.GA)())return{highlightDataTrace:()=>{},onHighlightChange:()=>()=>{}};return{highlightDataTrace:function(e){let{shouldBlock:t,source:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((0,r.isUndefined)(t)){if(s.current)return}else s.current=t;((e,t)=>{for(const r of n.current)r(e,t)})(e,i)},onHighlightChange:e=>(n.current.push(e),()=>{n.current=n.current.filter((t=>t!==e))})}}),[]);return(0,i.createElement)(p.Provider,{value:a},t)},m=()=>(0,i.useContext)(p)},26626:function(e,t,n){"use strict";n.d(t,{b:function(){return i}});var r=n(55003);const i=e=>{if(e)return e instanceof r.K&&e.graphQLErrors.length>0?e.graphQLErrors.map((e=>e.toString())).join(", "):"message"in e?e.message:e.toString()}},28239:function(e,t,n){"use strict";n.d(t,{_:function(){return o}});var r=n(31014),i=n(25790);const o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"is-highlighted",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3?arguments[3]:void 0;const{onHighlightChange:s,highlightDataTrace:a}=(0,i.ZY)(),l=(0,r.useCallback)(((r,o)=>{var s,a;const l=null===(s=e.current)||void 0===s?void 0:s.querySelector(`.${t}`),c=n?".ag-center-cols-viewport":"",d=null===(a=e.current)||void 0===a?void 0:a.querySelector(`${c} .ag-row[row-id="${r}"]`);l&&l!==d&&l.classList.remove(t),o!==i.WF.TABLE&&d&&d.classList.add(t)}),[e,t,n]);(0,r.useEffect)((()=>s(l)),[l,s]);return{cellMouseOverHandler:(0,r.useCallback)((e=>{let{data:t}=e;const n="groupParentInfo"in t,r=o?o({data:t}):n?t.rowUuid:t.runUuid;a(r,{source:i.WF.TABLE})}),[a,o]),cellMouseOutHandler:(0,r.useCallback)((()=>a(null)),[a])}}},28940:function(e,t,n){"use strict";n.d(t,{P:function(){return xt},T:function(){return bt}});var r=n(45959),i=n.n(r),o=n(31014),s=n(51293),a=n(76010),l=n(6604),c=n(32599),d=n(9133),u=n(25866),p=n(89555),h=n(48012),m=n(3546),g=n(50111);var f={name:"12si88q",styles:"height:100%;width:100%;display:flex;align-items:center;justify-content:space-between"};const v=e=>{let{enableSorting:t,canonicalSortKey:n,displayName:r,context:i}=e;const{orderByKey:o,orderByAsc:s}=i||{},a=(0,m.Px)(),l=n,{theme:d}=(0,c.u)();return(0,g.Y)("div",{role:"columnheader",css:f,children:(0,g.FD)("div",{css:(0,p.AH)({height:"100%",width:"100%",display:"flex",alignItems:"center",overflow:"hidden",paddingLeft:d.spacing.xs+d.spacing.sm,paddingRight:d.spacing.xs+d.spacing.sm,gap:d.spacing.sm,svg:{color:d.colors.textSecondary},"&:hover":{color:t?d.colors.actionTertiaryTextHover:"unset",svg:{color:d.colors.actionTertiaryTextHover}}},""),className:l===o?"is-ordered-by":"",onClick:t?()=>{let e=!s;l!==o&&(e=!1),a({orderByKey:l,orderByAsc:e})}:void 0,children:[(0,g.Y)("span",{"data-test-id":`sort-header-${r}`,children:r}),t&&l===o?s?(0,g.Y)(h.GCP,{}):(0,g.Y)(h.MMv,{}):null]})})};var y=n(76758),x=n(88464);const C=o.memo((e=>{let{value:t}=e;const{startTime:n,referenceTime:r,runStatus:i}=t||{},o=(0,x.A)();return n?(0,g.FD)("span",{css:b.cellWrapper,title:a.A.formatTimestamp(n,o),children:[(0,g.Y)(y.F,{status:i}),a.A.timeSinceStr(n,r)]}):(0,g.Y)(g.FK,{children:"-"})})),b={cellWrapper:e=>({display:"flex",alignItems:"center",gap:e.spacing.sm})},_=e=>{let{text:t,maxSize:n,className:r,allowShowMore:i=!1,dataTestId:s}=e;if(t.length<=n)return(0,g.Y)("span",{className:r,"data-testid":s,children:t});const a=`${t.substr(0,n)}...`,[l,d]=(0,o.useState)(!1);return(0,g.FD)("span",{className:r,"data-testid":s,children:[l?t:a,i&&(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_common_components_trimmedtext.tsx_30",type:"link",onClick:()=>d(!l),size:"small",css:w.expandButton,"data-testid":"trimmed-text-button",children:l?"collapse":"expand"})]})},w={expandButton:{display:"inline-block"}},M=o.memo((e=>{let{value:t}=e;const n=a.A.getRunDescriptionFromTags(t)||"-";return(0,g.Y)(g.FK,{children:(0,g.Y)(h.paO,{title:n,children:(0,g.Y)("span",{children:(0,g.Y)(_,{text:n,maxSize:50})})})})}));var S=n(93215),I=n(58481);const E=o.memo((e=>{let{data:t,value:n}=e;return t.experimentId?(0,g.Y)(S.N_,{to:I.h.getExperimentPageRoute(t.experimentId),title:n.name,children:n.basename}):null}));var R=n(69869),A=n(21317),k=n(88443);var Y={name:"1989ovb",styles:"vertical-align:middle"},T={name:"1p2zdak",styles:"margin-right:0;vertical-align:middle"},D={name:"osm46o",styles:"width:20px;display:flex;align-items:center;justify-content:flex-start;flex-shrink:0"},L={name:"167129t",styles:"text-overflow:ellipsis;overflow:hidden;cursor:pointer"};const F=e=>{let{model:{isUc:t,registeredModelName:n,registeredModelVersion:r,flavors:i,artifactPath:o}={},experimentId:s,runUuid:a}=e;const{theme:l}=(0,c.u)();return(0,g.FD)("div",{css:(0,p.AH)({display:"flex",alignItems:"center",gap:l.spacing.xs,overflow:"hidden"},""),children:[(0,g.Y)("div",{css:D,children:n?(0,g.Y)(A.h,{css:(0,p.AH)({color:l.colors.actionPrimaryBackgroundDefault},"")}):(0,g.Y)(h.oiI,{css:(0,p.AH)({color:l.colors.actionPrimaryBackgroundDefault},"")})}),(0,g.Y)(S.N_,{to:n&&r?R.fM.getModelVersionPageRoute(n,r):I.h.getRunPageRoute(s,a,o),target:"_blank",css:L,children:(()=>{const e=`${n} v${r}`;if(n)return(0,g.FD)(h.paO,{title:e,placement:"topLeft",children:[(0,g.Y)("span",{css:Y,children:n})," ",(0,g.FD)(h.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_modelscellrenderer.tsx_49",css:T,children:["v",r]})]});return(null===i||void 0===i?void 0:i[0])||(0,g.Y)(k.A,{id:"giZdtW",defaultMessage:"Model"})})()})]})};var K={name:"osm46o",styles:"width:20px;display:flex;align-items:center;justify-content:flex-start;flex-shrink:0"},N={name:"167129t",styles:"text-overflow:ellipsis;overflow:hidden;cursor:pointer"};const H=e=>{var t,n;let{model:r}=e;const{theme:i}=(0,c.u)();return null!==(t=r.info)&&void 0!==t&&t.model_id&&null!==(n=r.info)&&void 0!==n&&n.experiment_id?(0,g.FD)("div",{css:(0,p.AH)({display:"flex",alignItems:"center",gap:i.spacing.xs,overflow:"hidden"},""),children:[(0,g.Y)("div",{css:K,children:(0,g.Y)(h.oiI,{css:(0,p.AH)({color:i.colors.actionPrimaryBackgroundDefault},"")})}),(0,g.Y)(S.N_,{to:I.h.getExperimentLoggedModelDetailsPage(r.info.experiment_id,r.info.model_id),target:"_blank",css:N,children:r.info.name})]}):null};var O={name:"1xsgnar",styles:"width:100%;&>div{max-width:100%;display:flex;}"};const B=o.memo((e=>{var t;if(!e.value)return(0,g.Y)(g.FK,{children:"-"});const{registeredModels:n,loggedModels:r,experimentId:i,runUuid:o}=e.value,s=a.A.mergeLoggedAndRegisteredModels(r,n);var l;return Boolean((null===s||void 0===s?void 0:s.length)||(null===(t=e.value.loggedModelsV3)||void 0===t?void 0:t.length))?(0,g.Y)("div",{css:O,children:(0,g.FD)(h.nEg,{children:[s.map(((e,t)=>(0,g.Y)(F,{model:e,experimentId:i,runUuid:o},e.artifactPath||t))),null===(l=e.value.loggedModelsV3)||void 0===l?void 0:l.map(((e,t)=>{var n,r;return(0,g.Y)(H,{model:e},null!==(n=null===(r=e.info)||void 0===r?void 0:r.model_id)&&void 0!==n?n:t)}))]})}):(0,g.Y)(g.FK,{children:"-"})})),P=o.memo((()=>{const{theme:e}=(0,c.u)();return(0,g.Y)("div",{role:"columnheader",css:(0,p.AH)({height:"100%",width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0 12px",gap:e.spacing.sm},""),children:(0,g.Y)(h.paO,{title:(0,g.Y)(k.A,{id:"c0slEY",defaultMessage:"Click into an individual run to see all models associated with it"}),children:u.qo.MODELS})})}));var U=n(15164);const z=o.memo((e=>{let{value:t}=e;if(!t)return(0,g.Y)(g.FK,{children:"-"});const{version:n,name:r,type:i}=t;return a.A.renderSourceVersion(n,r,i)||(0,g.Y)(g.FK,{children:"-"})}));var j=n(21616),G=n(69526),V=n(12772),W=n(91144),$=n(21879),q=n(62862),X=n(58898),Q=n(1670);const J=()=>(0,g.Y)(c.I,{component:Q.h}),Z={mouseEnterDelay:0,mouseLeaveDelay:0},ee=e=>{let{runUuid:t,className:n,rowHidden:r,buttonHidden:i,disabled:o,onClick:s,label:a}=e;const{theme:l}=(0,c.u)();return i?(0,g.Y)("div",{className:n,css:[te.button(l),""]}):o?(0,g.Y)(h.DAb,{className:n,css:[te.button(l),{opacity:.25,color:l.colors.grey400},""]}):(0,g.Y)(h.paO,{dangerouslySetAntdProps:Z,placement:"right",title:a,children:(0,g.FD)("label",{className:n,css:te.button(l),children:[(0,g.Y)("span",{css:c.O,children:a}),(0,g.Y)("input",{type:"checkbox",className:"is-visibility-toggle-checkbox",checked:!r,onChange:()=>{if(t){const e=!r;s(X.oy.CUSTOM,t,e)}}}),r?(0,g.Y)(h.DAb,{}):(0,g.Y)(J,{})]})})},te={button:e=>({width:e.general.iconFontSize,color:e.colors.grey400,".ag-row:hover &":{color:e.colors.grey500}})},ne=o.createContext({runsHiddenMode:X.oy.FIRST_10_RUNS,useGroupedValuesInCharts:!0,usingCustomVisibility:!1,allRunsHidden:!1}),re=e=>{let{children:t,runsHiddenMode:n,useGroupedValuesInCharts:r,usingCustomVisibility:i,allRunsHidden:s}=e;const a=(0,o.useMemo)((()=>({runsHiddenMode:n,useGroupedValuesInCharts:null===r||void 0===r||r,usingCustomVisibility:null!==i&&void 0!==i&&i,allRunsHidden:null!==s&&void 0!==s&&s})),[n,r,i,s]);return(0,g.Y)(ne.Provider,{value:a,children:t})},ie=()=>o.useContext(ne),oe={visibility:{groups:(0,G.YK)({unhide:{id:"8Gz0OE",defaultMessage:"Unhide group"},hide:{id:"TfuAgs",defaultMessage:"Hide group"}}),runs:(0,G.YK)({unhide:{id:"xmpvlI",defaultMessage:"Unhide run"},hide:{id:"qTjYVU",defaultMessage:"Hide run"}})},pinning:{groups:(0,G.YK)({unpin:{id:"jkeYf8",defaultMessage:"Unpin group"},pin:{id:"8hpS0b",defaultMessage:"Pin group"}}),runs:(0,G.YK)({unpin:{id:"ugm2f6",defaultMessage:"Unpin run"},pin:{id:"fjiojF",defaultMessage:"Pin run"}})}},se={mouseEnterDelay:0,mouseLeaveDelay:0},ae=o.memo((e=>{const t=(0,$.e)(),{theme:n}=(0,c.u)(),{useGroupedValuesInCharts:r}=ie(),{groupParentInfo:i,runDateAndNestInfo:s,visibilityControl:a}=e.data,{belongsToGroup:l}=s||{},d=Boolean(i),u=(0,W.Bh)()&&a===V.VB.Disabled,{pinned:p,hidden:m}=e.value,{runUuid:f,rowUuid:v}=e.data,y=i?v:f,x=(0,W.Bh)()&&!1===r&&i?Boolean(i.allRunsHidden):m,C=d?x?oe.visibility.groups.unhide:oe.visibility.groups.hide:x?oe.visibility.runs.unhide:oe.visibility.runs.hide,b=d?p?oe.pinning.groups.unpin:oe.pinning.groups.pin:p?oe.pinning.runs.unpin:oe.pinning.runs.pin,_=(0,o.useMemo)((()=>(0,W.Bh)()?a===V.VB.Hidden:!(i&&!(0,q.mC)(i)||Boolean(f)&&!l)),[i,l,f,a]);return(0,g.FD)("div",{css:ce.actionsContainer,children:[(0,g.Y)(ee,{rowHidden:x,buttonHidden:_,disabled:u,label:(0,g.Y)(k.A,{...C}),onClick:e.onToggleVisibility,runUuid:y,css:[ce.actionCheckbox(n),ce.showOnlyInCompareMode,""]}),(e.data.pinnable&&f||i)&&(0,g.Y)(h.paO,{dangerouslySetAntdProps:se,placement:"right",title:(0,g.Y)(k.A,{...b}),children:(0,g.FD)("label",{css:ce.actionCheckbox(n),className:"is-pin-toggle","data-testid":"column-pin-toggle",children:[(0,g.Y)("span",{css:c.O,children:(0,g.Y)(k.A,{...b})}),(0,g.Y)("input",{type:"checkbox",checked:p,onChange:()=>{const n=i?e.data.rowUuid:f;t((e=>n?{...e,runsPinned:e.runsPinned.includes(n)?e.runsPinned.filter((e=>e!==n)):[...e.runsPinned,n]}:e))}}),p?(0,g.Y)(h.aS3,{}):(0,g.Y)(h.tsw,{})]})},Math.random())]})}),((e,t)=>{var n,r;return e.value.hidden===t.value.hidden&&e.value.pinned===t.value.pinned&&e.data.visibilityControl===t.data.visibilityControl&&(null===(n=e.data.groupParentInfo)||void 0===n?void 0:n.allRunsHidden)===(null===(r=t.data.groupParentInfo)||void 0===r?void 0:r.allRunsHidden)})),le=e=>{let{event:t}=e;return!!("Tab"===t.key&&t.target instanceof HTMLElement&&(t.target.classList.contains("ag-cell")||t.target.classList.contains("is-visibility-toggle-checkbox")))},ce={actionsContainer:{display:"flex",gap:18},showOnlyInCompareMode:{display:"none",".is-table-comparing-runs-mode &":{display:"flex"}},actionCheckbox:e=>({input:{width:0,appearance:"none"},cursor:"pointer",display:"flex",svg:{width:e.general.iconFontSize,height:e.general.iconFontSize,cursor:"pointer"},"&.is-pin-toggle svg":{color:"transparent",".ag-row:hover &":{color:e.colors.grey500}},"& input:checked + span svg":{color:e.colors.grey500},"& input:focus-visible + span svg":{color:e.colors.grey500}})},de=()=>(0,g.Y)(c.I,{component:Q.h}),ue=o.memo((e=>{let{onToggleVisibility:t}=e;const{theme:n}=(0,c.u)(),r=(0,x.A)(),{runsHiddenMode:i,usingCustomVisibility:o,allRunsHidden:s}=ie();return(0,g.FD)(h.rId.Root,{modal:!1,children:[(0,g.Y)(h.rId.Trigger,{asChild:!0,children:(0,g.Y)("button",{css:[he.actionButton(n),he.showOnlyInCompareMode,""],"data-testid":"experiment-view-runs-visibility-column-header","aria-label":r.formatMessage({id:"9HXup+",defaultMessage:"Toggle visibility of runs"}),children:i===X.oy.HIDEALL||s?(0,g.Y)(h.DAb,{}):(0,g.Y)(de,{})})}),(0,g.Y)(h.rId.Content,{children:(0,g.FD)(h.rId.RadioGroup,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_rowactionsheadercellrenderer.tsx_52",value:i,onValueChange:e=>t(e),children:[(0,g.FD)(h.rId.RadioItem,{value:X.oy.FIRST_10_RUNS,children:[(0,g.Y)(h.rId.ItemIndicator,{children:o?(0,g.Y)(h.YTx,{}):null}),(0,g.Y)(k.A,{id:"w39cZ4",defaultMessage:"Show first 10"})]}),(0,g.FD)(h.rId.RadioItem,{value:X.oy.FIRST_20_RUNS,children:[(0,g.Y)(h.rId.ItemIndicator,{children:o?(0,g.Y)(h.YTx,{}):null}),(0,g.Y)(k.A,{id:"ONIiUE",defaultMessage:"Show first 20"})]}),(0,g.FD)(h.rId.RadioItem,{value:X.oy.SHOWALL,children:[(0,g.Y)(h.rId.ItemIndicator,{children:o?(0,g.Y)(h.YTx,{}):null}),(0,g.Y)(k.A,{id:"4yjF8O",defaultMessage:"Show all runs"})]}),(0,g.FD)(h.rId.RadioItem,{value:X.oy.HIDEALL,children:[(0,g.Y)(h.rId.ItemIndicator,{children:o?(0,g.Y)(h.YTx,{}):null}),(0,g.Y)(k.A,{id:"Qi6Etm",defaultMessage:"Hide all runs"})]})]})})]})})),pe=o.memo((e=>{const t=(0,x.A)();return(0,o.useEffect)((()=>{var n;const r=null===(n=e.eGridHeader)||void 0===n?void 0:n.querySelector("input");r&&(r.ariaLabel=t.formatMessage({id:"Csmj/2",defaultMessage:"Select all runs"}))}),[e.eGridHeader,t]),(0,g.Y)(ue,{...e})})),he={actionButton:e=>({background:"transparent",border:"none",cursor:"pointer",padding:"8px",".ag-checkbox:not(.ag-hidden) + &":{padding:"0 1px"},svg:{width:e.general.iconFontSize,height:e.general.iconFontSize,cursor:"pointer",color:e.colors.grey500}}),showOnlyInCompareMode:{display:"none",".is-table-comparing-runs-mode &":{display:"flex"}}};var me=n(15579),ge=n(79432),fe=n(4877),ve=n.n(fe),ye=n(70403);var xe={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"},Ce={name:"mlsake",styles:"margin-left:0;margin-right:0"},be={name:"oq2rn6",styles:"svg{width:12px;height:12px;}"};const _e=e=>{let{data:t,isComparingRuns:n}=e;const r=t.groupParentInfo,i=t.hidden;ve()(r,"groupParentInfo should be defined");const{theme:s}=(0,c.u)(),a=(0,S.zy)(),{useGroupedValuesInCharts:l}=ie(),d=(0,ye.LE)(),u=(0,ye.MQ)(),f=(0,$.e)(),v=(0,o.useCallback)(((e,t)=>{f((n=>{const{groupsExpanded:r}=n;return{...n,groupsExpanded:{...r,[e]:t}}}))}),[f]),y=(0,q.QD)(r),x=(0,o.useMemo)((()=>(0,W.Bh)()?l&&!(0,q.mC)(r):!(0,q.mC)(r)),[r,l]),C=(0,o.useMemo)((()=>{const e=(0,q.dx)(r),t=new URLSearchParams(a.search);t.set("searchFilter",e),t.set(m.ts,"true");return{...a,search:t.toString()}}),[r,a]);return(0,g.FD)("div",{css:(0,p.AH)({display:"flex",gap:s.spacing.sm,alignItems:"center"},""),children:[r.expanderOpen?(0,g.Y)(h.D3D,{role:"button",onClick:()=>{v(r.groupId,!1)}}):(0,g.Y)(c.q,{role:"button",onClick:()=>{v(r.groupId,!0)}}),x&&(0,g.Y)(ge.E,{color:d(r.groupId),hidden:n&&i,onChangeColor:e=>{u({groupUuid:r.groupId,colorValue:e})}}),(0,g.FD)("div",{css:(0,p.AH)({display:"inline-flex",gap:s.spacing.sm,alignItems:"center",overflow:"hidden",textOverflow:"ellipsis"},""),children:[(0,q.mC)(r)?(0,g.Y)(k.A,{id:"PKYh23",defaultMessage:"Additional runs"}):(0,g.Y)("span",{title:y,css:xe,children:(0,g.Y)(k.A,{id:"jq95vC",defaultMessage:"Group: {groupName}",values:{groupName:y}})}),(0,g.Y)(h.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_groupparentcellrenderer.tsx_109",css:Ce,children:r.runUuids.length}),(0,g.Y)(me.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_groupparentcellrenderer.tsx_136",content:(0,g.Y)(k.A,{id:"+w9a+1",defaultMessage:"Open runs in this group in the new tab"}),children:(0,g.Y)(S.N_,{to:C,target:"_blank",css:(0,p.AH)({marginLeft:-s.spacing.xs,display:"none",".ag-row-hover &":{display:"inline-flex"}},""),children:(0,g.Y)(c.B,{type:"link",componentId:"mlflow.experiment_page.grouped_runs.open_runs_in_new_tab",size:"small",icon:(0,g.Y)(c.at,{css:be})})})})]})]})};var we={name:"xahbfd",styles:"width:12px;height:12px;flex-shrink:0"};const Me=o.memo((e=>{const{theme:t}=(0,c.u)(),n=(0,ye.MQ)(),r=(0,ye.LE)(),{useGroupedValuesInCharts:i}=ie();if(e.data.groupParentInfo)return(0,g.Y)(_e,{...e});const{onExpand:o,data:s}=e,{runName:a,experimentId:l,runUuid:d,runDateAndNestInfo:u,hidden:p}=s;ve()(l,"experimentId should be set for run rows"),ve()(d,"runUuid should be set for run rows"),ve()(u,"runDateAndNestInfo should be set for run rows");const{hasExpander:m,expanderOpen:f,childrenIds:v,level:y,belongsToGroup:x}=u,C=!isNaN(y)&&m,b=(0,W.Bh)()?x&&i:x;return(0,g.FD)("div",{css:Se.cellWrapper,children:[(0,g.Y)("div",{css:Se.expanderWrapper,children:(0,g.Y)("div",{css:Se.nestLevel(t),style:{width:(y+1)*t.spacing.lg},children:C&&(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_runnamecellrenderer.tsx_46",css:Se.expanderButton,size:"small",onClick:()=>{o(d,v)},type:"link",icon:f?(0,g.Y)(h.NEo,{}):(0,g.Y)(h.Xeq,{})},"Expander-"+d)})}),(0,g.FD)("div",{css:Se.runLink,children:[b?(0,g.Y)("div",{css:we}):(0,g.Y)(ge.E,{color:r(d),hidden:e.isComparingRuns&&p,"data-testid":"experiment-view-table-run-color",onChangeColor:e=>n({runUuid:d,colorValue:e})}),(0,g.Y)(S.N_,{to:I.h.getRunPageRoute(l,d),css:Se.runLink,tabIndex:0,children:(0,g.Y)("span",{css:Se.runName,children:a})})]})]})})),Se={link:e=>({display:"inline-block",minWidth:e.typography.fontSizeBase,minHeight:e.typography.fontSizeBase}),cellWrapper:{display:"flex"},expanderButton:{svg:{width:12,height:12}},runLink:{overflow:"hidden",display:"flex",gap:8,alignItems:"center","&:focus-visible":{textDecoration:"underline"}},runName:{overflow:"hidden",textOverflow:"ellipsis"},expanderWrapper:{display:"none",".ag-grid-expanders-visible &":{display:"block"}},nestLevel:e=>({display:"flex",justifyContent:"flex-end",height:e.spacing.lg})};var Ie={name:"gikiyg",styles:"display:flex;justify-content:center;align-items:center;height:32px"};const Ee=o.memo((e=>{let{loadMoreRunsFunc:t}=e;return(0,g.Y)("div",{css:Ie,children:(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_loadmorerowrenderer.tsx_20",type:"primary",onClick:t,size:"small",children:(0,g.Y)(k.A,{id:"9CqEWy",defaultMessage:"Load more"})})})}));var Re={name:"rnnx2x",styles:"font-size:12px"},Ae={name:"rnnx2x",styles:"font-size:12px"};const ke=e=>{let{datasetWithTags:t,onDatasetSelected:n,appendComma:r=!1,inPopover:i=!1}=e;const{theme:o}=(0,c.u)(),{dataset:s,tags:a}=t;if(!s)return null;const l=null===a||void 0===a?void 0:a.find((e=>{let{key:t}=e;return t===u.AS}));return(0,g.FD)("div",{css:(0,p.AH)({display:"flex",flexShrink:0,alignItems:"center",overflow:"hidden",marginRight:o.spacing.xs},""),children:[(0,g.Y)(h.KbA,{css:(0,p.AH)({color:o.colors.textSecondary,marginRight:o.spacing.xs},"")})," ",(0,g.Y)("span",{css:(0,p.AH)({minWidth:32,marginRight:o.spacing.xs,flexShrink:0},""),title:`${s.name} (${s.digest})`,children:i?(0,g.Y)(c.av.Close,{asChild:!0,children:(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_datasetscellrenderer.tsx_49",type:"link",onClick:n,tabIndex:0,children:(0,g.FD)("span",{css:Re,children:[s.name," (",s.digest,")"]})})}):(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_datasetscellrenderer.tsx_56",type:"link",onClick:n,"data-testid":"open-dataset-drawer",tabIndex:0,children:(0,g.FD)("span",{children:[s.name," (",s.digest,")"]})})}),l&&(0,g.Y)(h.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_datasetscellrenderer.tsx_75",css:(0,p.AH)({textTransform:"capitalize",marginRight:o.spacing.xs},""),children:(0,g.Y)("span",{css:Ae,children:l.value})}),r&&(0,g.Y)(g.FK,{children:","})]})};var Ye={name:"zjik7",styles:"display:flex"},Te={name:"eq7f8j",styles:"display:flex;align-items:flex-end"},De={name:"h1n7cn",styles:"max-height:400px;overflow:auto"};const Le=o.memo((e=>{let{value:t,data:n,onDatasetSelected:r,expandRows:i}=e;const s=(0,o.useRef)(null),[a,l]=(0,o.useState)(0),[u,h]=(0,o.useState)(!1),m=(0,o.useMemo)((()=>(t||[]).slice(0,3)),[t]),{theme:f}=(0,c.u)(),v=(t||[]).length;(0,o.useEffect)((()=>{if(!s.current)return()=>{};const e=(0,d.throttle)((e=>{let[t]=e;if(i){const e=t.contentRect.height;let n=0,r=0;for(let i=0;i<t.target.children.length;i++){const o=t.target.children.item(i);if(r+o.clientHeight>e)break;r+=o.clientHeight,n++}l(n),h(n<v)}else{const e=t.contentRect.width;if(0===e&&v)return l(0),void h(!0);let n=0,r=0;for(let o=0;o<t.target.children.length;o++){const i=t.target.children.item(o);if(r+i.clientWidth>=e)break;r+=i.clientWidth,n++}const i=Math.min(v,n+1);l(i),h(n<v)}}),100),t=new ResizeObserver(e);return t.observe(s.current),()=>t.disconnect()}),[i,v]);const y=v-a;if(!t||v<1)return(0,g.Y)(g.FK,{children:"-"});const x=i?m:t;return(0,g.FD)("div",{css:Ye,children:[(0,g.Y)("div",{css:(0,p.AH)({overflow:"hidden",display:"flex",flexDirection:i?"column":"row"},""),ref:s,children:x.map(((e,t)=>(0,g.Y)(ke,{appendComma:!i&&t<x.length-1,datasetWithTags:e,onDatasetSelected:()=>null===r||void 0===r?void 0:r(e,n)},`${e.dataset.name}-${e.dataset.digest}`)))}),(y>0||u)&&(0,g.FD)("div",{css:Te,children:[!i&&u&&(0,g.Y)("span",{css:(0,p.AH)({paddingLeft:0,paddingRight:f.spacing.xs},""),children:"\u2026"}),y>0&&(0,g.FD)(c.av.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_datasetscellrenderer.tsx_184",modal:!1,children:[(0,g.Y)(c.av.Trigger,{asChild:!0,children:(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_cells_datasetscellrenderer.tsx_172",size:"small",style:{borderRadius:"8px",width:"40px"},tabIndex:0,children:(0,g.FD)(c.T.Text,{color:"secondary",children:["+",y]})})}),(0,g.Y)(c.av.Content,{align:"start",css:De,children:t.slice(v-y).map((e=>(0,g.Y)("div",{css:(0,p.AH)({height:f.general.heightSm,display:"flex",alignItems:"center"},""),children:(0,g.Y)(ke,{datasetWithTags:e,onDatasetSelected:()=>null===r||void 0===r?void 0:r(e,n),inPopover:!0})},`${e.dataset.name}-${e.dataset.digest}`)))})]})]})]})})),Fe=e=>{let{event:t}=e;return"Tab"===t.key&&t.target instanceof HTMLElement&&(t.target.classList.contains("ag-cell")||t.target instanceof HTMLButtonElement)},Ke=e=>{var t;let{value:n,data:r,valueFormatted:i}=e;const{theme:o}=(0,c.u)();return null!==(t=r.groupParentInfo)&&void 0!==t&&t.aggregateFunction?(0,g.FD)(c.T.Text,{children:[null!==i&&void 0!==i?i:n," ",(0,g.FD)("span",{css:(0,p.AH)({color:o.colors.textSecondary},""),children:["(",r.groupParentInfo.aggregateFunction,")"]})]}):n};var Ne=n(53606),He=n(35286);const Oe=e=>{var t;let{colDef:n,context:r}=e;return r.orderByKey===(null===(t=n.headerComponentParams)||void 0===t?void 0:t.canonicalSortKey)},Be=e=>e?105:73,Pe=e=>`${j.iy}-${e}`,Ue=e=>`${j.Pl}-${e}`,ze=e=>{let{loading:t,...n}=e;return(0,c.S)({loading:!1,...n})},je=e=>{let{event:t}=e;return"Tab"===t.key&&t.target instanceof HTMLElement&&t.target.classList.contains("ag-cell")},Ge={[u.T8.USER]:(0,j.GF)(u.RO.ATTRIBUTES,"User"),[u.T8.RUN_NAME]:(0,j.GF)(u.RO.ATTRIBUTES,"Run Name"),[u.T8.SOURCE]:(0,j.GF)(u.RO.ATTRIBUTES,"Source"),[u.T8.VERSION]:(0,j.GF)(u.RO.ATTRIBUTES,"Version"),[u.T8.DESCRIPTION]:(0,j.GF)(u.RO.ATTRIBUTES,"Description")},Ve=e=>{let{data:t}=e;return t.rowUuid},We=e=>{let{rowNode:t}=e;return t.data.isLoadMoreRow},$e=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=[u.qo.USER,u.qo.SOURCE,u.qo.VERSION,u.qo.MODELS,u.qo.DATASET,u.qo.DESCRIPTION];return e&&t.push(u.qo.EXPERIMENT_NAME),t},qe=e=>{let{selectedColumns:t,compareExperiments:n,onTogglePin:r,onToggleVisibility:i,onExpand:s,paramKeyList:l,metricKeyList:p,tagKeyList:h,columnApi:m,onDatasetSelected:g,isComparingRuns:f,expandRows:v,runsHiddenMode:y}=e;const{theme:x}=(0,c.u)(),C=(e=>{let{paramKeyList:t,metricKeyList:n,tagKeyList:r}=e;const i=(0,o.useRef)(new Set),s=(0,o.useRef)(new Set),a=(0,o.useRef)(new Set),l=(0,o.useMemo)((()=>(t.forEach((e=>s.current.add(e))),Array.from(s.current))),[t]),c=(0,o.useMemo)((()=>(n.forEach((e=>i.current.add(e))),Array.from(i.current))),[n]),d=(0,o.useMemo)((()=>(r.forEach((e=>a.current.add(e))),Array.from(a.current))),[r]);return(0,o.useMemo)((()=>({paramKeys:l,metricKeys:c,tagKeys:d})),[c,l,d])})({metricKeyList:p,tagKeyList:h,paramKeyList:l}),b=(0,Ne.U)(`(max-width: ${x.responsive.breakpoints.sm}px)`),_=(0,o.useMemo)((()=>{const e=[];e.push({valueGetter:e=>{let{data:{pinned:t,hidden:n}}=e;return{pinned:t,hidden:n}},checkboxSelection:!0,headerComponent:"RowActionsHeaderCellRenderer",headerComponentParams:{onToggleVisibility:i},headerCheckboxSelection:!0,headerName:"",cellClass:"is-checkbox-cell",cellRenderer:"RowActionsCellRenderer",cellRendererParams:{onTogglePin:r,onToggleVisibility:i},pinned:b?void 0:"left",minWidth:Be(f),width:Be(f),maxWidth:Be(f),resizable:!1,suppressKeyboardEvent:le});const t=f;if(e.push({headerName:u.qo.RUN_NAME,colId:t?void 0:Ge[u.T8.RUN_NAME],headerTooltip:u.T8.RUN_NAME,pinned:b?void 0:"left",sortable:!0,cellRenderer:"RunNameCellRenderer",cellRendererParams:{onExpand:s,isComparingRuns:f},equals:(e,t)=>{var n,r;return(null===e||void 0===e?void 0:e.rowUuid)===(null===t||void 0===t?void 0:t.rowUuid)&&(null===e||void 0===e||null===(n=e.groupParentInfo)||void 0===n?void 0:n.expanderOpen)===(null===t||void 0===t||null===(r=t.groupParentInfo)||void 0===r?void 0:r.expanderOpen)},headerComponentParams:{canonicalSortKey:u.T8.RUN_NAME},cellClassRules:{"is-ordered-by":Oe},initialWidth:t?void 0:190,flex:t?1:void 0,resizable:!f,suppressKeyboardEvent:je}),f)return e;e.push({headerName:u.qo.DATE,headerTooltip:u.T8.DATE,pinned:b?void 0:"left",sortable:!0,field:"runDateAndNestInfo",cellRenderer:"DateCellRenderer",cellRendererParams:{onExpand:s},equals:(e,t)=>(0,d.isEqual)(e,t),headerComponentParams:{canonicalSortKey:u.T8.DATE},cellClassRules:{"is-ordered-by":Oe},initialWidth:150}),e.push({headerName:u.qo.DATASET,colId:(0,j.GF)(u.RO.ATTRIBUTES,u.qo.DATASET),headerTooltip:u.qo.DATASET,sortable:!1,field:"datasets",cellRenderer:"DatasetsCellRenderer",cellRendererParams:{onDatasetSelected:g,expandRows:v},cellClass:"is-multiline-cell",initialWidth:300,suppressKeyboardEvent:Fe}),e.push({headerName:u.qo.DURATION,field:"duration",initialWidth:80}),n&&e.push({headerName:u.qo.EXPERIMENT_NAME,colId:(0,j.GF)(u.RO.ATTRIBUTES,u.qo.EXPERIMENT_NAME),field:"experimentName",cellRenderer:"ExperimentNameCellRenderer",equals:(e,t)=>(0,d.isEqual)(e,t),initialWidth:140,initialHide:!0,suppressKeyboardEvent:je}),e.push({headerName:u.qo.USER,colId:Ge[u.T8.USER],headerTooltip:u.T8.USER,field:"user",sortable:!0,headerComponentParams:{canonicalSortKey:u.T8.USER},cellClassRules:{"is-ordered-by":Oe},initialHide:!0}),e.push({headerName:u.qo.SOURCE,colId:Ge[u.T8.SOURCE],field:"tags",cellRenderer:"SourceCellRenderer",equals:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return a.A.getSourceName(e)===a.A.getSourceName(t)},sortable:!0,headerComponentParams:{canonicalSortKey:u.T8.SOURCE},cellClassRules:{"is-ordered-by":Oe},initialHide:!0,suppressKeyboardEvent:je}),e.push({headerName:u.qo.VERSION,colId:Ge[u.T8.VERSION],field:"version",cellRenderer:"VersionCellRenderer",equals:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,d.isEqual)(e,t)},sortable:!0,headerComponentParams:{canonicalSortKey:u.T8.VERSION},cellClassRules:{"is-ordered-by":Oe},initialHide:!0}),e.push({headerComponent:"ModelsHeaderCellRenderer",colId:(0,j.GF)(u.RO.ATTRIBUTES,u.qo.MODELS),field:"models",cellRenderer:"ModelsCellRenderer",initialWidth:200,equals:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,d.isEqual)(e,t)},initialHide:!0,suppressKeyboardEvent:je}),e.push({headerName:u.qo.DESCRIPTION,colId:Ge[u.T8.DESCRIPTION],field:"tags",cellRenderer:"RunDescriptionCellRenderer",initialWidth:300,initialHide:!0,sortable:!0,headerComponentParams:{canonicalSortKey:u.T8.DESCRIPTION},cellClassRules:{"is-ordered-by":Oe}});const{metricKeys:o,paramKeys:l,tagKeys:c}=C;return o.length&&e.push({headerName:"Metrics",groupId:u.RO.METRICS,children:o.map((e=>{var t,n;const r=(0,j.GF)(u.RO.METRICS,e),i=He.g[e],o=null!==(t=null===i||void 0===i?void 0:i.displayName)&&void 0!==t?t:e,s=(a=e,`${j.tZ}-${a}`);var a;return{headerName:o,colId:r,headerTooltip:(0,j.$e)(u.RO.METRICS,e),field:s,tooltipValueGetter:e=>{var t;return null===(t=e.data)||void 0===t?void 0:t[s]},initialWidth:null!==(n=null===i||void 0===i?void 0:i.initialColumnWidth)&&void 0!==n?n:100,initialHide:!0,sortable:!0,headerComponentParams:{canonicalSortKey:r},valueFormatter:null===i||void 0===i?void 0:i.valueFormatter,cellRendererSelector:e=>{let{data:{groupParentInfo:t}}=e;return t?{component:"AggregateMetricValueCell"}:void 0},cellClassRules:{"is-previewable-cell":()=>!0,"is-ordered-by":Oe}}}))}),l.length&&e.push({headerName:"Parameters",groupId:u.RO.PARAMS,children:l.map((e=>{const t=(0,j.GF)(u.RO.PARAMS,e);return{colId:t,headerName:e,headerTooltip:(0,j.$e)(u.RO.PARAMS,e),field:Pe(e),tooltipField:Pe(e),initialHide:!0,initialWidth:100,sortable:!0,headerComponentParams:{canonicalSortKey:t},cellClassRules:{"is-previewable-cell":()=>!0,"is-ordered-by":Oe}}}))}),c.length&&e.push({headerName:"Tags",colId:u.RO.TAGS,children:c.map((e=>({colId:(0,j.GF)(u.RO.TAGS,e),headerName:e,initialHide:!0,initialWidth:100,headerTooltip:(0,j.$e)(u.RO.TAGS,e),field:Ue(e),tooltipField:Ue(e)})))}),e}),[r,i,s,n,C,f,g,v,b]),w=(0,o.useMemo)((()=>[...$e(!0).map((e=>(0,j.GF)(u.RO.ATTRIBUTES,e))),...C.paramKeys.map((e=>(0,j.GF)(u.RO.PARAMS,e))),...C.metricKeys.map((e=>(0,j.GF)(u.RO.METRICS,e))),...C.tagKeys.map((e=>(0,j.GF)(u.RO.TAGS,e)))]),[C]);return(0,o.useEffect)((()=>{if(m&&!f)for(const e of w){const n=t.includes(e);m.setColumnVisible(e,n)}}),[t,m,w,f]),_},Xe={initialWidth:100,autoSizePadding:0,headerComponentParams:{menuIcon:"fa-bars"},resizable:!0,filter:!0,suppressMenu:!0,suppressMovable:!0};var Qe=n(47664);const Je=e=>{let{isFiltered:t,onClearFilters:n}=e;return(0,g.Y)("div",{css:Ze.noResultsWrapper,children:(0,g.Y)("div",{css:Ze.noResults,children:t?(0,g.Y)(h.SvL,{button:(0,g.Y)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsemptytable.tsx_35",type:"primary",onClick:n,children:(0,g.Y)(k.A,{id:"pEpexK",defaultMessage:"Clear filters"})}),description:(0,g.Y)(k.A,{id:"Nnsm0p",defaultMessage:"All runs in this experiment have been filtered. Change or clear filters to view runs."}),title:(0,g.Y)(k.A,{id:"Tl63jz",defaultMessage:"All runs are filtered"}),image:(0,g.Y)(h.YGH,{})}):(0,g.Y)(h.SvL,{description:(0,g.Y)(k.A,{id:"coo35p",defaultMessage:"No runs have been logged yet. <link>Learn more</link> about how to create ML model training runs in this experiment.",values:{link:e=>(0,g.Y)("a",{target:"_blank",href:Qe.XO,rel:"noreferrer",children:e})}}),title:(0,g.Y)(k.A,{id:"sByLVU",defaultMessage:"No runs logged"}),image:(0,g.Y)(h.cuA,{})})})})},Ze={noResults:{maxWidth:360},noResultsWrapper:e=>({marginTop:e.spacing.lg,inset:0,backgroundColor:e.colors.backgroundPrimary,position:"absolute",display:"flex",alignItems:"center",justifyContent:"center"})},et="is-out-of-viewport",tt="is-hidden",nt="is-minimized",rt=".ag-root",it=".ag-pinned-left-cols-container",ot=".ag-center-cols-container",st=".ag-header",at=".ag-body-viewport",lt=e=>{let{onClick:t,gridContainerElement:n,isInitialized:r,visible:i,moreRunsAvailable:s,moreAvailableRunsTableColumnCount:a=0}=e;const l=(0,o.useRef)(null),d=(0,o.useRef)(),u=(0,o.useRef)(Boolean(s));(0,o.useEffect)((()=>{u.current=Boolean(s)}),[s]);const p=(0,o.useCallback)((e=>{if(!l.current||!window.ResizeObserver||!e)return;const t=l.current,n=e.querySelector(rt),r=e.querySelector(it),i=e.querySelector(ot),o=e.querySelector(st),s=e.querySelector(at);let a=0,c=0,p=0,h=0,m=0,g=0;if(r&&i&&o&&n&&s){const e=new ResizeObserver((e=>{var l,f,v,y;for(const t of e)t.target===n&&(a=t.contentRect.width),t.target===r&&(c=t.contentRect.width,p=t.contentRect.height),t.target===s&&(m=t.contentRect.height),t.target===o&&(g=t.contentRect.height),t.target===i&&(h=t.contentRect.width);const x=c+h,C=g;x+180>=a?null===(l=d.current)||void 0===l||l.classList.add(et):null===(f=d.current)||void 0===f||f.classList.remove(et);const b=u.current?32:0,_=p<m?p-b:m;_<100?null===(v=d.current)||void 0===v||v.classList.add(nt):null===(y=d.current)||void 0===y||y.classList.remove(nt),t.style.transform=`translate3d(${x}px, ${C}px, 0)`,t.style.height=`${_+1}px`}));return e.observe(r),e.observe(i),e.observe(o),e.observe(n),e.observe(s),()=>e.disconnect()}}),[]);return(0,o.useEffect)((()=>{r&&n&&(d.current=n,p(n))}),[p,r,n]),window.ResizeObserver?(0,g.Y)("div",{ref:l,css:ct.columnContainer,className:i?"":tt,children:i&&(0,g.Y)("div",{css:ct.buttonContainer,children:(0,g.FD)(c.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunstableaddcolumncta.tsx_218",css:ct.button,type:"link",onClick:t,children:[(0,g.Y)(h.GYj,{css:ct.buttonIcon}),(0,g.Y)("div",{css:ct.caption,children:(0,g.Y)(k.A,{id:"npHb2a",defaultMessage:"Show more columns {count, select, 0 {} other {({count} total)}}",values:{count:a}})})]})})}):null},ct={columnContainer:e=>({width:180,height:0,position:"absolute",border:`1px solid ${e.colors.borderDecorative}`,borderTop:0,top:0,left:0,willChange:"transform",transform:"translate3d(0, 0, 0)",[`.${nt} &`]:{display:"flex",alignItems:"center"},[`&.${tt}, .${et} &`]:{display:"none"},pointerEvents:"none",display:"flex",alignItems:"center"}),buttonContainer:e=>({top:0,paddingLeft:e.spacing.lg,paddingRight:e.spacing.lg,width:"100%",[`.${nt} &`]:{paddingTop:e.spacing.xs},pointerEvents:"all"}),button:{whiteSpace:"pre-wrap",width:"100%"},buttonIcon:e=>({color:e.colors.textSecondary}),caption:e=>({color:e.colors.textSecondary,[`.${nt} &`]:{display:"none"}})},dt=e=>(0,g.Y)("strong",{children:e}),ut=e=>{let{isLoading:t,allRunsCount:n}=e;return(0,g.FD)("div",{css:pt.statusBar,children:[(0,g.Y)(c.T.Text,{size:"sm",color:t?"secondary":void 0,children:(0,g.Y)(k.A,{id:"gLj8lU",defaultMessage:"<strong>{length}</strong> matching {length, plural, =0 {runs} =1 {run} other {runs}}",values:{strong:dt,length:n}})}),t&&(0,g.Y)(c.S,{size:"small"})]})},pt={statusBar:e=>({height:28,display:"flex",gap:8,marginTop:-1,position:"relative",alignItems:"center",borderTop:`1px solid ${e.colors.border}`})};var ht=n(37752),mt=n(83028);const gt=(e,t,n)=>{n.forEachNode((n=>{var r;if(null!==(r=n.data)&&void 0!==r&&r.isLoadMoreRow)return;const{runInfo:i,runDateAndNestInfo:o}=n.data;if(!i)return;const s=i.runUuid;e.includes(s)&&(null!==o&&void 0!==o&&o.childrenIds&&e.push(...o.childrenIds),n.setSelected(t,!1,!0))}))};var ft=n(22853),vt=n(28239);var yt={name:"yqjhl6",styles:"display:flex;flex-direction:column;position:relative"};const xt=o.memo((e=>{let{experiments:t,searchFacetsState:n,runsData:r,isLoading:p,moreRunsAvailable:f,updateViewState:y,onAddColumnClicked:x,rowsData:b,loadMoreRunsFunc:_,onDatasetSelected:w,expandRows:S,viewState:I,uiState:R,compareRunsMode:A}=e;const{theme:Y}=(0,c.u)(),T=(0,$.e)(),D=(0,m.Px)(),{orderByKey:L,orderByAsc:F}=n,{selectedColumns:K,runsPinned:N,runsHidden:H,runListHidden:O}=R,G="TABLE"!==A,{paramKeyList:V,metricKeyList:q,tagsList:X}=r,[Q,J]=(0,o.useState)(),[Z,ee]=(0,o.useState)(),te=((0,o.useRef)([]),(0,o.useMemo)((()=>a.A.getVisibleTagKeyList(X)),[X])),ne=(0,o.useRef)(null),ie=(0,o.useMemo)((()=>b.some((e=>{var t;return null===(t=e.runDateAndNestInfo)||void 0===t?void 0:t.hasExpander}))),[b]),oe=(0,o.useCallback)((e=>T((t=>{let{runsExpanded:n,...r}=t;return{...r,runsExpanded:{...n,[e]:!n[e]}}}))),[T]),se=(0,o.useCallback)((e=>{T((t=>({...t,runsPinned:t.runsPinned.includes(e)?t.runsPinned.filter((t=>t!==e)):[...t.runsPinned,e]})))}),[T]),le=(0,ft.v)(b,R.useGroupedValuesInCharts),ce=(0,o.useCallback)((e=>{J(e.api),ee(e.columnApi)}),[]),{handleRowSelected:de,onSelectionChange:ue}=(e=>{const t=(0,o.useCallback)((t=>{let{api:n}=t;const r=n.getSelectedRows().filter((e=>e.runInfo)).map((e=>{let{runInfo:t}=e;return t.runUuid}));e({runsSelected:r.reduce(((e,t)=>({...e,[t]:!0})),{})})}),[e]);return{handleRowSelected:(0,o.useCallback)((e=>{const t=Boolean(e.node.isSelected()),{runDateAndNestInfo:n,runInfo:r,groupParentInfo:i}=e.data;if(i&&gt(i.runUuids,t,e.api),!n)return;const{isParent:o,expanderOpen:s,childrenIds:a}=n;if(o&&s&&a)gt(a,t,e.api);else if(r){gt([r.runUuid],t,e.api);const n=(0,d.uniqBy)(e.api.getSelectedRows().filter((e=>Boolean(e.runUuid))),"runUuid");l=n,e.api.forEachNode((e=>{const t=e.data;t.groupParentInfo&&(t.groupParentInfo.runUuids.every((e=>l.some((t=>t.runUuid===e))))&&e.setSelected(!0,!1,!0),t.groupParentInfo.runUuids.some((e=>l.some((t=>t.runUuid===e))))||e.setSelected(!1,!1,!0))}))}var l}),[]),onSelectionChange:t}})(y),he=(0,o.useMemo)((()=>(0,W.Rn)()?b.every((e=>e.hidden)):r.runInfos.every((e=>{let{runUuid:t}=e;return H.includes(t)}))),[r,b,H]),me=(0,W.Rn)()&&!(0,d.isEmpty)(R.runsVisibilityMap),ge=qe({selectedColumns:K,onExpand:oe,compareExperiments:t.length>1,onTogglePin:se,onToggleVisibility:le,metricKeyList:q,paramKeyList:V,tagKeyList:te,columnApi:Z,isComparingRuns:G,onDatasetSelected:w,expandRows:S,runsHiddenMode:R.runsHiddenMode}),fe=(0,o.useCallback)((e=>{if(e&&G)try{e.sizeColumnsToFit()}catch{}}),[G]);(0,o.useEffect)((()=>{if(Q)if(p)Q.showLoadingOverlay();else{if(Q.hideOverlay(),b.length&&f)return Q.setRowData([...b,{runUuid:"",rowUuid:(0,d.uniqueId)("load_more"),isLoadMoreRow:!0}]),void fe(Q);Q.setRowData(b),fe(Q)}}),[Q,b,p,f,_,fe]);const ve=(0,o.useMemo)((()=>$e(t.length>1).length+(q.length+V.length+te.length)),[t.length,te.length,q.length,V.length]),ye=K.length>=ve,xe=Math.max(0,ve-K.length),Ce=(0,o.useMemo)((()=>r.runInfos.filter((e=>N.includes(e.runUuid)||r.runUuidsMatchingFilter.includes(e.runUuid))).length),[r,N]);(0,o.useLayoutEffect)((()=>{Q&&(G&&(Q.deselectAll(),Q.sizeColumnsToFit()),Q.resetRowHeights())}),[Q,G]);const be=(0,o.useCallback)((e=>{if(G||!S)return j.Fc;const t=(0,j.GF)(u.RO.ATTRIBUTES,u.qo.DATASET);return((e,t)=>{if(e){var n;const{data:e}=t,r=Math.min((null===(n=e.datasets)||void 0===n?void 0:n.length)||1,3);return j.Fc*r}return j.Fc})(K.includes(t),e)}),[K,G,S]);(0,o.useEffect)((()=>{null===Q||void 0===Q||Q.resetRowHeights()}),[Q,K,S]);const[_e,we]=(0,o.useState)(null),Se=(0,o.useCallback)((e=>{var t;let{column:n,data:r,value:i}=e;const o=null===(t=n.getParent())||void 0===t?void 0:t.getGroupId();(o===u.RO.METRICS||o===u.RO.PARAMS)&&(we({value:i,header:`Run name: ${r.runName}, Column name: ${n.getColDef().headerTooltip}`}),y({previewPaneVisible:!0}))}),[y]),Ie=!ye&&!G&&b.length>0,Re=!G&&I.previewPaneVisible,Ae=!O||!G,ke=!O,Ye=b.length<1&&!p&&Ae,Te=(0,o.useMemo)((()=>({orderByAsc:F,orderByKey:L})),[F,L]),{cellMouseOverHandler:De,cellMouseOutHandler:Fe}=(0,vt._)(ne),Ne=bt();return(0,g.FD)("div",{css:e=>({display:"grid",flex:1,gridTemplateColumns:Re?"1fr auto":"1fr",borderTop:`1px solid ${e.colors.border}`}),className:G?"is-table-comparing-runs-mode":void 0,children:[(0,g.FD)("div",{css:yt,children:[(0,g.FD)("div",{ref:ne,className:i()("ag-theme-balham ag-grid-sticky",{"ag-grid-expanders-visible":ie,"is-table-comparing-runs-mode":G}),css:[Ne,{display:Ae?"block":"hidden",height:"100%"},""],"aria-hidden":!Ae,children:[(0,g.Y)(re,{runsHiddenMode:R.runsHiddenMode,usingCustomVisibility:me,useGroupedValuesInCharts:Boolean(R.groupBy)&&R.useGroupedValuesInCharts,allRunsHidden:he,children:(0,g.Y)(s.p,{context:Te,defaultColDef:Xe,columnDefs:ge,rowSelection:"multiple",onGridReady:ce,onSelectionChanged:ue,getRowHeight:be,headerHeight:j.Fc,onRowSelected:de,suppressRowClickSelection:!0,suppressColumnMoveAnimation:!0,suppressScrollOnNewData:!0,isFullWidthRow:We,fullWidthCellRenderer:"LoadMoreRowRenderer",fullWidthCellRendererParams:{loadMoreRunsFunc:_},suppressFieldDotNotation:!0,enableCellTextSelection:!0,components:{agColumnHeader:v,loadingOverlayComponent:ze,LoadMoreRowRenderer:Ee,SourceCellRenderer:U.t,ModelsCellRenderer:B,ModelsHeaderCellRenderer:P,VersionCellRenderer:z,DateCellRenderer:C,ExperimentNameCellRenderer:E,RunDescriptionCellRenderer:M,RowActionsCellRenderer:ae,RowActionsHeaderCellRenderer:pe,RunNameCellRenderer:Me,DatasetsCellRenderer:Le,AggregateMetricValueCell:Ke},suppressNoRowsOverlay:!0,loadingOverlayComponent:"loadingOverlayComponent",loadingOverlayComponentParams:{showImmediately:!0},getRowId:Ve,rowBuffer:101,onCellClicked:Se,onGridSizeChanged:e=>{let{api:t}=e;return fe(t)},onCellMouseOver:De,onCellMouseOut:Fe})}),Ie&&(0,g.Y)(lt,{gridContainerElement:ne.current,isInitialized:Boolean(Q),onClick:x,visible:!p,moreRunsAvailable:f,moreAvailableRunsTableColumnCount:xe})]}),Ye&&(0,g.Y)(Je,{onClearFilters:()=>{D((0,mt.G)())},isFiltered:(0,l.e9)(n)}),ke&&(0,g.Y)(ut,{allRunsCount:Ce,isLoading:p})]}),Re&&(0,g.Y)(ht.V,{content:null===_e||void 0===_e?void 0:_e.value,copyText:null===_e||void 0===_e?void 0:_e.value,headerText:null===_e||void 0===_e?void 0:_e.header,onClose:()=>y({previewPaneVisible:!1}),empty:(0,g.Y)(h.SvL,{description:(0,g.Y)(k.A,{id:"cAKSkQ",defaultMessage:"Select a cell to display preview"})})})]})})),Ct=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=(e=>({rowForeground:e.colors.textPrimary,rowBackground:e.colors.backgroundPrimary,rowBackgroundHover:e.colors.tableBackgroundUnselectedHover,rowBackgroundSelected:e.colors.tableBackgroundSelectedDefault,rowBackgroundHoverSelected:e.colors.tableBackgroundSelectedHover,columnSortedBy:`${e.colors.blue400}1F`,headerBackground:e.colors.backgroundSecondary,headerTextColor:e.colors.textSecondary,headerGroupTextColor:e.colors.textSecondary,borderColor:e.colors.borderDecorative,headerBorderColor:"transparent",checkboxBorderColor:e.colors.actionDefaultBorderDefault,checkboxBorderColorChecked:e.colors.backgroundPrimary,checkboxBackgroundColorChecked:e.colors.actionPrimaryBackgroundDefault,overlayBackground:`${e.colors.backgroundSecondary}99`}))(e);return{height:"100%",position:"relative","&.ag-theme-balham":{"--ag-border-color":n.borderColor,"--ag-row-border-color":n.borderColor,"--ag-foreground-color":n.rowForeground,"--ag-background-color":n.rowBackground,"--ag-odd-row-background-color":n.rowBackground,"--ag-row-hover-color":n.rowBackgroundHover,"--ag-selected-row-background-color":n.rowBackgroundSelected,"--ag-header-foreground-color":n.headerTextColor,"--ag-header-background-color":n.headerBackground,"--ag-modal-overlay-background-color":n.overlayBackground,"&.ag-grid-sticky .ag-header":{position:"sticky",top:0,zIndex:1},"&.ag-grid-sticky .ag-root":{overflow:"visible"},"&.ag-grid-sticky .ag-root-wrapper":{border:"0",borderRadius:"4px",overflow:"visible"},".is-table-comparing-runs-mode & .ag-body-horizontal-scroll.ag-scrollbar-invisible":{display:"none"},".ag-header::after":{content:'""',position:"absolute",top:j.Fc,left:0,right:0,height:1,backgroundColor:n.borderColor},".ag-cell":{display:"flex",overflow:"hidden","& > .ag-cell-wrapper":{overflow:"hidden"}},".ag-header-cell":t?{padding:0}:void 0,".ag-header-cell .ag-checkbox":t?{padding:"0 7px",borderLeft:"1px solid transparent"}:void 0,".ag-cell.is-ordered-by, .ag-header-cell > .is-ordered-by":{backgroundColor:n.columnSortedBy},".ag-header-row":{"--ag-border-color":n.headerBorderColor},".ag-header-row.ag-header-row-column-group":{"--ag-header-foreground-color":n.headerGroupTextColor},".ag-row.ag-row-selected.ag-row-hover":{backgroundColor:n.rowBackgroundHoverSelected},".ag-row.is-highlighted":{backgroundColor:n.rowBackgroundHoverSelected},".ag-header:not(:hover) .ag-header-cell::after, .ag-header:not(:hover) .ag-header-group-cell::after":{opacity:0},".ag-pinned-left-header":{borderRight:"none"},".ag-overlay-loading-wrapper":{paddingTop:4*e.spacing.md,alignItems:"center",zIndex:2},".ag-overlay-loading-wrapper .ag-react-container":{flex:1},".ag-center-cols-container":{minHeight:0},".ag-full-width-row":{borderBottom:0,backgroundColor:"transparent",zIndex:1,"&.ag-row-hover":{backgroundColor:"transparent"}},".is-checkbox-cell":{display:"flex",alignItems:"center",paddingLeft:7,".is-multiline-cell .ag-cell-value":{height:"100%"}},".is-previewable-cell":{cursor:"pointer"},".ag-header-cell .ag-checkbox .ag-input-wrapper:not(.ag-indeterminate):not(.ag-checked)":{"--ag-checkbox-background-color":n.headerBackground},".ag-cell-wrapper .ag-selection-checkbox":{marginRight:20},".is-checkbox-cell, .ag-header-cell .ag-checkbox":{".ag-checkbox-input-wrapper::after":{color:n.checkboxBorderColor},".ag-checkbox-input-wrapper.ag-checked":{"--ag-checkbox-background-color":n.checkboxBackgroundColorChecked,"--ag-checkbox-checked-color":n.checkboxBorderColorChecked,"&::after":{color:n.checkboxBorderColorChecked}}}}}},bt=function(){let{usingCustomHeaderComponent:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{theme:t}=(0,c.u)();return(0,o.useMemo)((()=>Ct(t,e)),[t,e])}},31276:function(e,t,n){"use strict";n.d(t,{I:function(){return D}});var r=n(89555),i=n(32599),o=n(48012),s=n(70618),a=n(9856),l=n(58732),c=n(31014),d=n(88464),u=n(30152),p=n(25866),h=n(76010);const m="-",g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if("number"===typeof e)return e.toFixed(t);if("string"===typeof e)return e;try{return JSON.stringify(e)}catch{return m}};let f=function(e){return e[e.POSITIVE=0]="POSITIVE",e[e.NEGATIVE=1]="NEGATIVE",e[e.SAME=2]="SAME",e}({});const v=(e,t)=>e!==m&&t!==m&&(typeof e!==typeof t||("number"===typeof e&&"number"===typeof t?Math.abs(e-t)>1e-6:"string"===typeof e&&"string"===typeof t&&e!==t)),y="expand",x="headingColumn",C=e=>{const t=(e,n)=>Object.keys(n).map((r=>{if("boolean"===typeof n[r]){let t={key:r,[x]:r};return Object.keys(e).forEach((n=>{var i;const o=null===(i=e[n])||void 0===i?void 0:i[r];t={...t,[n]:void 0===o?m:o}})),t}const i={};return Object.keys(e).forEach((t=>{var n;i[t]=null===(n=e[t])||void 0===n?void 0:n[r]})),{key:r,[x]:r,children:t(i,n[r])}}));return e.map((e=>{let n,r=!0;const i={};if(Object.keys(e).forEach((t=>{if(t!==x&&e[t]!==m){const o=(e=>{try{const t=JSON.parse(e);return null===t||"object"!==typeof t||Array.isArray(t)||0===Object.keys(t).length?null:t}catch(t){return null}})(e[t]);if(i[t]=o,null===o)r=!1;else{const e=((e,t)=>{if(void 0!==e&&0===Object.keys(e).length)return null;if(void 0!==t&&0===Object.keys(t).length)return null;const n={},r=(e,t)=>{for(const n in t)e.hasOwnProperty(n)&&!e[n]||("object"!==typeof t[n]||null===t[n]||Array.isArray(t[n])?t[n]===m?e[n]=!0:e[n]=!1:(e[n]=e[n]||{},r(e[n],t[n])))};return void 0!==e&&r(n,e),void 0!==t&&r(n,t),n})(n,o);null===e?r=!1:n=e}}})),!r||void 0===n)return e;try{return{[x]:e[x],children:t(i,n),key:e[x]}}catch{return e}}))};var b=n(50111);var _={name:"ti75j2",styles:"margin:0"};const w=e=>{let{label:t,direction:n}=e;const{theme:s}=(0,i.u)();let a,l=null;switch(n){case f.NEGATIVE:a="error",l=(0,b.Y)(o.ZLN,{color:"danger","data-testid":"negative-cell-direction"});break;case f.POSITIVE:a="success",l=(0,b.Y)(o.Kpk,{color:"success","data-testid":"positive-cell-direction"});break;case f.SAME:a="info",l=(0,b.Y)(o.YTx,{css:(0,r.AH)({color:s.colors.textSecondary},""),"data-testid":"same-cell-direction"})}return(0,b.FD)("div",{css:(0,r.AH)({display:"inline-flex",backgroundColor:s.colors.actionDisabledBackground,padding:`${s.spacing.xs/2}px ${s.spacing.xs}px`,fontSize:s.typography.fontSizeSm,borderRadius:s.borders.borderRadiusSm,userSelect:"none",gap:s.spacing.xs,alignItems:"center",svg:{width:s.typography.fontSizeSm,height:s.typography.fontSizeSm},overflow:"hidden"},""),children:[(0,b.Y)(i.T.Text,{size:"sm",color:a,css:_,ellipsis:!0,children:t}),l]})},M=e=>{var t;let{getValue:n,row:{original:o},column:{columnDef:s}}=e;const{theme:a}=(0,i.u)(),{isBaseline:l,baselineColumnUuid:c,showChangeFromBaseline:d}=null!==(t=s.meta)&&void 0!==t?t:{},u=n();if(l)return g(n());if(void 0===u)return null;const p=c?((e,t)=>{if("number"===typeof e&&"number"===typeof t){const n=e-t;return 0===n?{label:g(n).toString(),direction:f.SAME}:n>0?{label:`+${g(n)}`,direction:f.POSITIVE}:{label:g(n).toString(),direction:f.NEGATIVE}}})(u,o[c]):null;return(0,b.FD)("span",{css:(0,r.AH)({display:"inline-flex",overflow:"hidden",gap:a.spacing.sm,alignItems:"center"},""),children:[(0,b.Y)(i.T.Text,{ellipsis:!0,children:g(u)}),p&&d&&(0,b.Y)(w,{label:p.label,direction:p.direction})]})};var S=n(79432),I=n(88443);var E={name:"82a6rk",styles:"flex:1"};const R=e=>{var t,n;let{column:{columnDef:s}}=e;const{theme:a}=(0,i.u)(),l=null===(t=s.meta)||void 0===t?void 0:t.traceData,c=null===(n=s.meta)||void 0===n?void 0:n.updateBaselineColumnUuid;return l?(0,b.FD)("div",{css:(0,r.AH)({flex:1,display:"inline-flex",overflow:"hidden",alignItems:"center",gap:a.spacing.xs,fontWeight:"normal"},""),children:[(0,b.Y)(S.E,{color:l.color})," ",(0,b.Y)(i.T.Text,{ellipsis:!0,children:null===l||void 0===l?void 0:l.displayName}),(0,b.Y)("div",{css:E}),(0,b.FD)(o.rId.Root,{children:[(0,b.Y)(o.rId.Trigger,{asChild:!0,children:(0,b.Y)(i.B,{type:"link",size:"small",componentId:"mlflow.charts.difference_plot.overflow_menu.trigger",icon:(0,b.Y)(o.ssM,{})})}),(0,b.Y)(o.rId.Content,{children:(0,b.Y)(o.rId.Item,{componentId:"mlflow.charts.difference_plot.overflow_menu.set_as_baseline",onClick:()=>null===c||void 0===c?void 0:c(l.uuid),children:(0,b.Y)(I.A,{id:"wxq/qM",defaultMessage:"Set as baseline"})})})]})]}):null};const A=e=>{let{row:t,toggleExpand:n}=e;return t.getCanExpand()&&n?(0,b.Y)(i.B,{componentId:"mlflow.charts.difference_plot.expand_button",size:"small",type:"link",onClick:()=>n(t),icon:t.getIsExpanded()?(0,b.Y)(o.D3D,{}):(0,b.Y)(i.q,{})}):null};var k={name:"1djur21",styles:"flex:1;overflow-x:scroll;height:100%"},Y={name:"77ql30",styles:"position:sticky;top:0;z-index:101"},T={name:"1bh2wf5",styles:"width:auto;position:absolute;top:0"};const D=e=>{let{previewData:t,cardConfig:n,groupBy:g,setCardConfig:f}=e;const{formatMessage:_}=(0,d.A)(),{theme:w}=(0,i.u)(),{modelMetrics:S,systemMetrics:I,parameters:E,tags:D,attributes:L}=(0,c.useMemo)((()=>((e,t,n,r)=>{const i=(r,i,o)=>Array.from(new Set(e.flatMap((e=>i(e))))).filter((e=>r(e))).sort().flatMap((r=>{const i={};let s=!1;return e.forEach(((t,n)=>{if(i[t.uuid]=o(t)[r]?o(t)[r].value:m,n>0){const r=e[n-1];v(i[r.uuid],i[t.uuid])&&(s=!0)}})),t.showDifferencesOnly&&!s?[]:[{[n]:r,...i}]})),o=i((e=>!e.startsWith(p.qt)),(e=>Object.keys(e.metrics)),(e=>e.metrics)),s=i((e=>e.startsWith(p.qt)),(e=>Object.keys(e.metrics)),(e=>e.metrics));return r?{modelMetrics:o,systemMetrics:s,parameters:[],tags:[],attributes:[]}:{modelMetrics:o,systemMetrics:s,parameters:i((()=>!0),(e=>Object.keys(e.params)),(e=>e.params)),tags:i((()=>!0),(e=>h.A.getVisibleTagValues(e.tags).map((e=>{let[t]=e;return t}))),(e=>e.tags)),attributes:[u.rJ.USER,u.rJ.SOURCE,u.rJ.VERSION,u.rJ.MODELS].flatMap((r=>{const i={};let o=!1;return e.forEach(((t,n)=>{if(r===u.rJ.USER){var s;const e=h.A.getUser(null!==(s=t.runInfo)&&void 0!==s?s:{},t.tags);i[t.uuid]=e}else if(r===u.rJ.SOURCE){const e=h.A.getSourceName(t.tags);i[t.uuid]=e}else if(r===u.rJ.VERSION){const e=h.A.getSourceVersion(t.tags);i[t.uuid]=e}else{const e=h.A.getLoggedModelsFromTags(t.tags);i[t.uuid]=e.join(",")}if(n>0){const r=e[n-1];v(i[r.uuid],i[t.uuid])&&(o=!0)}})),t.showDifferencesOnly&&!o?[]:[{[n]:r,...i}]}))}})(t,n,x,g)),[t,n,g]),{baselineColumn:F,nonBaselineColumns:K}=(0,c.useMemo)((()=>{const e=t.slice().reverse();let r=e.find((e=>e.uuid===n.baselineColumnUuid));void 0===r&&e.length>0&&(r=e[0]);const i=e.filter((e=>void 0===r||e.uuid!==r.uuid));return{baselineColumn:r,nonBaselineColumns:i}}),[t,n.baselineColumnUuid]),N=(0,c.useCallback)((e=>{null===f||void 0===f||f((t=>({...t,baselineColumnUuid:e})))}),[f]),H=(0,c.useMemo)((()=>n.compareGroups.reduce(((e,t)=>{switch(t){case u.K8.MODEL_METRICS:e.push({[x]:_({id:"d8OeBh",defaultMessage:"Model Metrics"}),children:[...S],key:"modelMetrics"});break;case u.K8.SYSTEM_METRICS:e.push({[x]:_({id:"CDOfWP",defaultMessage:"System Metrics"}),children:[...I],key:"systemMetrics"});break;case u.K8.PARAMETERS:e.push({[x]:_({id:"28Dnzz",defaultMessage:"Parameters"}),children:C(E),key:"parameters"});break;case u.K8.ATTRIBUTES:e.push({[x]:_({id:"MBNIRk",defaultMessage:"Attributes"}),children:[...L],key:"attributes"});break;case u.K8.TAGS:e.push({[x]:_({id:"7mgmG6",defaultMessage:"Tags"}),children:[...D],key:"tags"})}return e}),[])),[S,I,E,D,L,n.compareGroups,_]),O=(0,c.useMemo)((()=>[{id:y,cell:A,size:32,enableResizing:!1},{accessorKey:x,size:150,header:_({id:"c5AQYh",defaultMessage:"Compare by"}),id:x,enableResizing:!0},...[F,...K].map(((e,t)=>{var r;return{accessorKey:null===e||void 0===e?void 0:e.uuid,size:200,enableResizing:!0,meta:{traceData:e,updateBaselineColumnUuid:N,showChangeFromBaseline:n.showChangeFromBaseline,isBaseline:e===F,baselineColumnUuid:null===F||void 0===F?void 0:F.uuid},id:null!==(r=null===e||void 0===e?void 0:e.uuid)&&void 0!==r?r:t.toString(),header:R,cell:M}}))]),[_,F,K,N,n.showChangeFromBaseline]),[B,P]=(0,c.useState)({modelMetrics:!0,systemMetrics:!0,parameters:!0,attributes:!0,tags:!0}),U=(0,s.N4)({columns:O,data:H,getCoreRowModel:(0,a.HT)(),getExpandedRowModel:(0,a.D0)(),columnResizeMode:"onChange",enableExpanding:!0,getSubRows:e=>e.children,getRowId:e=>e.key,getRowCanExpand:e=>Boolean(e.subRows.length),state:{expanded:B,columnPinning:{left:[y,x]}}}),z=(0,c.useRef)(null),j=(0,c.useCallback)((e=>{const t=e.original.key;P((n=>({...n,[t]:!e.getIsExpanded()})))}),[]),{getVirtualItems:G,getTotalSize:V}=(0,l.Te)({count:U.getExpandedRowModel().rows.length,getScrollElement:()=>z.current,estimateSize:()=>33,paddingStart:37}),W=U.getExpandedRowModel().rows;return(0,b.Y)("div",{css:k,ref:z,children:(0,b.FD)(o.XIK,{css:(0,r.AH)({width:U.getTotalSize(),position:"relative"},""),children:[(0,b.Y)(o.Hjg,{isHeader:!0,css:Y,children:U.getLeafHeaders().map(((e,t)=>{const n=e.column.getIsPinned();return(0,b.Y)(o.A0N,{header:e,column:e.column,setColumnSizing:U.setColumnSizing,componentId:"mlflow.charts.difference_plot.header",multiline:!1,style:{left:"left"===n?`${e.column.getStart("left")}px`:void 0,position:n?"sticky":"relative",width:e.column.getSize(),flexBasis:e.column.getSize(),zIndex:n?100:0},wrapContent:!1,children:(0,s.Kv)(e.column.columnDef.header,e.getContext())},e.id)}))}),(0,b.Y)("div",{css:(0,r.AH)({height:V()},""),children:G().map((e=>{let{index:t,start:n,size:r}=e;const i=W[t];return(0,b.Y)(o.Hjg,{css:T,style:{transform:`translateY(${n}px)`,height:r},children:i.getVisibleCells().map(((e,t)=>{const n=e.column.getIsPinned(),r=e.column.columnDef.id===x;return(0,b.Y)(o.nA6,{style:{left:"left"===n?`${e.column.getStart("left")}px`:void 0,position:n?"sticky":"relative",width:e.column.getSize(),zIndex:n?100:0,flexBasis:e.column.getSize()},css:[{backgroundColor:n?w.colors.backgroundPrimary:void 0},r&&{borderRight:`1px solid ${w.colors.border}`},r&&{paddingLeft:i.depth*w.spacing.lg},""],wrapContent:!1,children:(0,s.Kv)(e.column.columnDef.cell,{...e.getContext(),toggleExpand:j})},e.id)}))},i.id+t)}))})]})})}},32582:function(e,t,n){"use strict";n.d(t,{h:function(){return M}});var r,i,o,s,a,l,c,d,u,p,h,m,g,f,v,y,x,C,b=n(31014);function _(){return _=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_.apply(null,arguments)}function w(e,t){let{title:n,titleId:w,...M}=e;return b.createElement("svg",_({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":w},M),n?b.createElement("title",{id:w},n):null,r||(r=b.createElement("rect",{y:2,width:4,height:1,fill:"#338ECC"})),i||(i=b.createElement("rect",{x:5,y:2,width:4,height:1,fill:"#338ECC"})),o||(o=b.createElement("rect",{x:10,y:2,width:4,height:1,fill:"#338ECC"})),s||(s=b.createElement("rect",{y:4,width:4,height:1,fill:"#338ECC"})),a||(a=b.createElement("rect",{x:5,y:4,width:4,height:1,fill:"#338ECC"})),l||(l=b.createElement("rect",{x:10,y:4,width:4,height:1,fill:"#338ECC"})),c||(c=b.createElement("rect",{y:6,width:4,height:1,fill:"#D9D9D9"})),d||(d=b.createElement("rect",{x:5,y:6,width:4,height:1,fill:"#D9D9D9"})),u||(u=b.createElement("rect",{x:10,y:6,width:4,height:1,fill:"#D9D9D9"})),p||(p=b.createElement("rect",{y:8,width:4,height:1,fill:"#D9D9D9"})),h||(h=b.createElement("rect",{x:5,y:8,width:4,height:1,fill:"#D9D9D9"})),m||(m=b.createElement("rect",{x:10,y:8,width:4,height:1,fill:"#D9D9D9"})),g||(g=b.createElement("rect",{y:10,width:4,height:1,fill:"#D9D9D9"})),f||(f=b.createElement("rect",{x:5,y:10,width:4,height:1,fill:"#D9D9D9"})),v||(v=b.createElement("rect",{x:10,y:10,width:4,height:1,fill:"#D9D9D9"})),y||(y=b.createElement("rect",{y:12,width:4,height:1,fill:"#D9D9D9"})),x||(x=b.createElement("rect",{x:5,y:12,width:4,height:1,fill:"#D9D9D9"})),C||(C=b.createElement("rect",{x:10,y:12,width:4,height:1,fill:"#D9D9D9"})))}const M=b.forwardRef(w);n.p},33946:function(e,t,n){"use strict";var r=n(31014),i=n(97026),o=n(26809),s=n(10811),a=n(76010),l=n(64912),c=n(50111);class d extends r.Component{constructor(e){super(e),this.handleSubmit=this.handleSubmit.bind(this)}handleSubmit(){const e=[];return this.props.selectedRunIds.forEach((t=>{e.push(this.props.deleteRunApi(t))})),Promise.all(e).catch((()=>{const e=`${this.props.intl.formatMessage({id:"tdmVWN",defaultMessage:"While deleting an experiment run, an error occurred."})}`;this.props.openErrorModal(e)})).then((()=>{var e,t;null===(e=(t=this.props).onSuccess)||void 0===e||e.call(t)}))}render(){const e=this.props.selectedRunIds.length;return(0,c.Y)(i.u,{isOpen:this.props.isOpen,onClose:this.props.onClose,handleSubmit:this.handleSubmit,title:`Delete Experiment ${a.A.pluralize("Run",e)}`,helpText:(0,c.FD)("div",{children:[(0,c.Y)("p",{children:(0,c.FD)("b",{children:[e," experiment ",a.A.pluralize("run",e)," will be deleted."]})}),""]}),confirmButtonText:"Delete"})}}const u={deleteRunApi:o.IX,openErrorModal:o.Yi};t.A=(0,s.Ng)(null,u)((0,l.Ay)(d))},34389:function(e,t,n){"use strict";n.d(t,{f:function(){return f}});var r=n(89555),i=n(32599),o=n(15579),s=n(79432),a=n(54421),l=n(50111);const c=e=>{let{previewData:t,cardConfig:n}=e;const{theme:c}=(0,i.u)(),d=t.filter((e=>{const t=e.images[n.imageKeys[0]];return t&&Object.keys(t).length>0}));return 0===d.length?(0,l.Y)(a.mQ,{}):(0,l.Y)("div",{css:(0,r.AH)({display:"flex",justifyContent:"flex-start",flexWrap:"wrap",gap:c.spacing.xs},""),children:d.map((e=>{const t=Object.values(e.images[n.imageKeys[0]]).reduce(((e,t)=>(void 0!==t.step&&(e[t.step]=t),e)),{});return(0,l.FD)("div",{css:(0,r.AH)({border:"1px solid transparent",borderRadius:c.borders.borderRadiusSm,padding:c.spacing.sm,"&:hover":{border:`1px solid ${c.colors.border}`,backgroundColor:c.colors.tableBackgroundUnselectedHover}},""),children:[(0,l.Y)(o.T,{content:e.displayName,componentId:"mlflow.charts.image-plot.run-name-tooltip",children:(0,l.FD)("div",{css:(0,r.AH)({height:c.typography.lineHeightMd,overflow:"hidden",whiteSpace:"nowrap",display:"inline-flex",alignItems:"center",gap:c.spacing.sm},""),children:[(0,l.Y)(s.E,{color:e.color}),e.displayName]})}),(0,l.Y)(a.Rn,{step:n.step,metadataByStep:t,runUuid:e.uuid},e.uuid)]},e.uuid)}))})};var d=n(48012),u=n(88443);var p={name:"1kzq5ms",styles:"height:100%;width:100%"};const h=e=>{let{previewData:t,cardConfig:n}=e;const{theme:c}=(0,i.u)(),h=t.filter((e=>0!==Object.keys(e.images).length));return 0===h.length?(0,l.Y)(a.mQ,{}):(0,l.Y)("div",{css:p,children:(0,l.FD)(d.XIK,{grid:!0,scrollable:!0,children:[(0,l.FD)(d.Hjg,{isHeader:!0,children:[(0,l.Y)(d.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_charts_imagegridmultiplekeyplot.tsx_44",css:(0,r.AH)({minWidth:a.Mc+c.spacing.md},""),children:(0,l.Y)(u.A,{id:"IdxSB8",defaultMessage:"images"})}),h.map((e=>(0,l.Y)(d.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_charts_imagegridmultiplekeyplot.tsx_52",css:(0,r.AH)({minWidth:a.Mc+c.spacing.md},""),children:(0,l.Y)(o.T,{content:e.displayName,componentId:"mlflow.charts.image-plot.run-name-tooltip",children:(0,l.FD)("div",{css:(0,r.AH)({height:c.typography.lineHeightMd,whiteSpace:"nowrap",display:"inline-flex",alignItems:"center",margin:"auto",gap:c.spacing.sm,fontWeight:"normal"},""),children:[(0,l.Y)(s.E,{color:e.color}),e.displayName]})})},e.uuid)))]}),n.imageKeys.map((e=>(0,l.FD)(d.Hjg,{children:[(0,l.Y)(d.nA6,{css:(0,r.AH)({minWidth:a.Mc+c.spacing.md},""),children:(0,l.Y)("div",{style:{whiteSpace:"normal"},children:e})}),h.map((t=>{if(t.images[e]&&Object.keys(t.images[e]).length>0){const i=Object.values(t.images[e]).reduce(((e,t)=>(void 0!==t.step&&(e[t.step]=t),e)),{});return(0,l.Y)(d.nA6,{css:(0,r.AH)({minWidth:a.Mc+c.spacing.md,"&:hover":{backgroundColor:c.colors.tableBackgroundUnselectedHover}},""),children:(0,l.Y)(a.Rn,{metadataByStep:i,step:n.step,runUuid:t.uuid})},t.uuid)}return(0,l.Y)(d.nA6,{css:(0,r.AH)({minWidth:a.Mc+c.spacing.md},"")},t.uuid)}))]},e)))]})})};var m=n(25866);var g={name:"r3950p",styles:"flex:1;display:flex;justify-content:center;align-items:center"};const f=e=>{let{previewData:t,cardConfig:n,groupBy:r,setCardConfig:i}=e;const o=t.some((e=>Boolean(e.tags[m.Cr]))),s=t.filter((e=>e.tags[m.Cr])).slice(-m.Lf);return o?1===n.imageKeys.length?(0,l.Y)(c,{previewData:s,cardConfig:n}):n.imageKeys.length>1?(0,l.Y)(h,{previewData:s,cardConfig:n}):null:(0,l.Y)("div",{css:g,children:(0,l.Y)(d.SvL,{title:(0,l.Y)(u.A,{id:"ez06Dt",defaultMessage:"No images found"}),description:(0,l.Y)(u.A,{id:"E5Ruk+",defaultMessage:"No logged images found in the currently visible runs"})})})}},37752:function(e,t,n){"use strict";n.d(t,{V:function(){return c}});var r=n(89555),i=(n(31014),n(32599)),o=n(48012),s=n(56412),a=n(50111);var l={name:"4fhn5u",styles:"white-space:pre-wrap;overflow-y:auto;flex:1"};const c=e=>{let{content:t,copyText:n,headerText:c,empty:d,onClose:u}=e;const{theme:p}=(0,i.u)();return(0,a.Y)("div",{css:(0,r.AH)({width:300,padding:p.spacing.sm,paddingRight:0,borderLeft:`1px solid ${p.colors.borderDecorative}`,overflow:"hidden",display:"flex",flexDirection:"column",height:"100%"},""),"data-testid":"preview-sidebar-content",children:t?(0,a.FD)(a.FK,{children:[(0,a.FD)("div",{css:(0,r.AH)({display:"grid",gridTemplateColumns:"1fr auto auto",rowGap:p.spacing.sm,alignItems:"flex-start",flex:"0 0 auto"},""),children:[c&&(0,a.Y)(i.T.Title,{level:4,css:(0,r.AH)({overflowX:"hidden",overflowY:"auto",marginTop:p.spacing.sm,marginRight:p.spacing.xs,maxHeight:200},""),children:c}),n&&(0,a.Y)(s.i,{copyText:n,showLabel:!1,icon:(0,a.Y)(o.TdU,{})}),u&&(0,a.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_components_previewsidebar.tsx_67",type:"primary",icon:(0,a.Y)(i.C,{}),onClick:u})]}),(0,a.Y)("div",{css:l,children:t})]}):(0,a.Y)("div",{css:(0,r.AH)({marginTop:p.spacing.md},""),children:d})})}},39045:function(e,t,n){"use strict";n.d(t,{J:function(){return ee}});var r=n(89555),i=n(48012),o=n(30152),s=n(32599),a=n(31014),l=n(50111);var c=e=>{let{activeKey:t,onActiveKeyChange:n,children:o,disableCollapse:c=!1}=e;const{theme:d,getPrefixedClassName:u}=(0,s.u)(),p=u("collapse"),h=(0,a.useMemo)((()=>{const e=`.${p}-item`,t=`${e}-active`,n=`.${p}-header`,r=`.${p}-content`,i=`.${p}-content-box`,o=`.${p}-arrow`;return{[r]:{padding:"0px !important",backgroundColor:"transparent !important"},[i]:{padding:"0 0 12px 0px !important",backgroundColor:"transparent !important"},[`& > ${e} > ${n} > ${o}`]:{fontSize:d.general.iconSize,left:12,verticalAlign:"-7px",transform:"rotate(-90deg)",display:c?"none":void 0},[`& > ${t} > ${n} > ${o}`]:{transform:"rotate(0deg)"},[n]:{display:"flex",color:d.colors.textPrimary,fontWeight:600,alignItems:"center","&:focus-visible":{outlineColor:`${d.colors.primary} !important`,outlineStyle:"auto !important"}},[`& > ${e}`]:{borderBottom:`1px solid ${d.colors.border}`,borderRadius:0},[`& > ${e} > ${n}`]:{padding:0,lineHeight:"20px",height:55}}}),[d,p,c]);return(0,l.Y)(i.nD3,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_metricchartsaccordion.tsx_82",...t?{activeKey:t}:{},...n?{onChange:n}:{},dangerouslyAppendEmotionCSS:(0,r.AH)(h,""),dangerouslySetAntdProps:{expandIconPosition:"left"},children:o})},d=n(41028),u=n(15579),p=n(77198),h=n(6211),m=n(73549),g=n(43270),f=n(74035),v=n(32582),y=n(99790),x=n(91144),C=n(88443);const b=e=>{let{onAddChart:t,supportedChartTypes:n}=e;const r=e=>!n||n.includes(e);return(0,l.FD)(i.rId.Root,{modal:!1,children:[(0,l.Y)(i.rId.Trigger,{asChild:!0,children:(0,l.Y)(s.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_runscompareaddchartmenu.tsx_19",css:_.addChartButton,icon:(0,l.Y)(i.c11,{}),"data-testid":"experiment-view-compare-runs-add-chart",children:(0,l.Y)(C.A,{id:"9vwPnB",defaultMessage:"Add chart"})})}),(0,l.FD)(i.rId.Content,{align:"end",children:[r(o.zL.BAR)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_42",onClick:()=>t(o.zL.BAR),"data-testid":"experiment-view-compare-runs-chart-type-bar",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(p.h,{})}),(0,l.Y)(C.A,{id:"gdK07H",defaultMessage:"Bar chart"})]}),r(o.zL.LINE)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_56",onClick:()=>t(o.zL.LINE),"data-testid":"experiment-view-compare-runs-chart-type-line",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(m.h,{})}),(0,l.Y)(C.A,{id:"CyTYL6",defaultMessage:"Line chart"})]}),r(o.zL.PARALLEL)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_70",onClick:()=>t(o.zL.PARALLEL),"data-testid":"experiment-view-compare-runs-chart-type-parallel",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(g.h,{})}),(0,l.Y)(C.A,{id:"FC9mcS",defaultMessage:"Parallel coordinates"})]}),r(o.zL.SCATTER)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_84",onClick:()=>t(o.zL.SCATTER),"data-testid":"experiment-view-compare-runs-chart-type-scatter",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(f.h,{})}),(0,l.Y)(C.A,{id:"Q0i5xG",defaultMessage:"Scatter chart"})]}),r(o.zL.CONTOUR)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_98",onClick:()=>t(o.zL.CONTOUR),"data-testid":"experiment-view-compare-runs-chart-type-contour",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(h.h,{})}),(0,l.Y)(C.A,{id:"6XfILf",defaultMessage:"Contour chart"})]}),(0,x.uP)()&&r(o.zL.DIFFERENCE)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_112",onClick:()=>t(o.zL.DIFFERENCE),"data-testid":"experiment-view-compare-runs-chart-type-difference",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(v.h,{})}),(0,l.Y)(C.A,{id:"G5mMJI",defaultMessage:"Difference view"})]}),r(o.zL.IMAGE)&&(0,l.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsaddchartmenu.tsx_126",onClick:()=>t(o.zL.IMAGE),"data-testid":"experiment-view-compare-runs-chart-type-image",children:[(0,l.Y)(i.rId.IconWrapper,{css:_.iconWrapper,children:(0,l.Y)(y.h,{})}),(0,l.Y)(C.A,{id:"5m2VPy",defaultMessage:"Image grid"})]})]})]})},_={addChartButton:e=>({backgroundColor:`${e.colors.backgroundPrimary} !important`}),iconWrapper:e=>({width:e.general.iconSize+e.spacing.xs})};var w=n(90765),M=n(45959),S=n.n(M);var I={name:"5s5off",styles:"position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);cursor:grab"};const E=e=>{let{index:t,section:n,sectionChartsLength:o,addNewChartCard:c,onDeleteSection:p,onAddSection:h,editSection:m,onSetEditSection:g,onSetSectionName:f,onSectionReorder:v,isExpanded:y,hideExtraControls:x,supportedChartTypes:_}=e;const{theme:M}=(0,s.u)(),[E,R]=(0,a.useState)(n.name),[A,k]=(0,a.useState)(!1),Y=(0,a.useRef)(null),T=(0,a.useRef)(null),[D,L]=(0,a.useState)(0),[F,K]=(0,a.useState)(!1),N=e=>{e.stopPropagation()},H=e=>{e.stopPropagation(),E.trim()?(g(-1),f(n.uuid,E)):e.preventDefault()};(0,a.useEffect)((()=>{if(!Y.current)return;const e=new ResizeObserver((e=>{let[t]=e;L(t.contentRect.width)}));return e.observe(Y.current),()=>e.disconnect()}),[]);const O=m===t,[B,P]=(0,a.useState)(!1),{dragHandleRef:U,dragPreviewRef:z,dropTargetRef:j,isOver:G,isDragging:V}=(0,w.S)({dragGroupKey:"sections",dragKey:n.uuid,onDrop:v});return(0,l.Y)(l.FK,{children:(0,l.FD)("div",{role:"figure",css:(0,r.AH)({display:"flex",alignItems:"center",width:"100%",padding:`${M.spacing.xs}px 0px`,height:"55px",".section-element-visibility-on-hover":{visibility:B?"visible":"hidden",opacity:B?1:0},".section-element-visibility-on-hover-and-not-drag":{visibility:B?"visible":"hidden",opacity:B?A?0:1:0},".section-element-hidden-on-edit":{display:O?"none":"inherit"}},""),onMouseMove:()=>P(!0),onMouseLeave:()=>P(!1),ref:e=>{null===j||void 0===j||j(e),null===z||void 0===z||z(e)},"data-testid":"experiment-view-compare-runs-section-header",children:[G&&(0,l.Y)("div",{css:(0,r.AH)({position:"absolute",inset:0,backgroundColor:M.isDarkMode?M.colors.blue800:M.colors.blue100,border:`2px dashed ${M.colors.blue400}`,opacity:.75},"")}),(0,l.FD)("div",{style:{maxWidth:"40%",display:"flex",alignItems:"center"},children:[(0,l.Y)("div",{ref:Y,style:{position:O?"absolute":"relative",visibility:O?"hidden":"visible",textOverflow:O?void 0:"ellipsis",maxWidth:"100%",overflow:"clip",paddingLeft:7,whiteSpace:"pre"},children:E}),m===t&&(0,l.Y)(d.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_sections_runschartssectionheader.tsx_220",autoFocus:!0,onClick:N,onMouseDown:N,onMouseUp:N,onDoubleClick:N,onChange:e=>{R(e.target.value)},value:E,css:(0,r.AH)({color:M.colors.textPrimary,fontWeight:600,padding:"1px 6px 1px 6px",background:M.colors.backgroundSecondary,minWidth:"50px",width:D+14,position:"relative",lineHeight:M.typography.lineHeightBase,maxWidth:"100%"},""),onKeyDown:e=>{"Escape"===e.key&&(e.stopPropagation(),g(-1),R(n.name))},onPressEnter:H,dangerouslyAppendEmotionCSS:{"&&":{minHeight:"20px !important"}},onBlur:e=>{e.relatedTarget!==T.current&&(g(-1),f(n.uuid,E))}}),(0,l.Y)("div",{css:(0,r.AH)({padding:M.spacing.xs,position:"relative"},""),style:{visibility:O?"hidden":"visible",display:O?"none":"inherit"},children:`(${o})`}),!x&&(0,l.Y)("div",{className:"section-element-visibility-on-hover-and-not-drag section-element-hidden-on-edit",children:(0,l.Y)(s.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_sections_runscomparesectionheader.tsx_246",onClick:e=>{e.stopPropagation(),g(t)},"aria-label":"Icon label",icon:(0,l.Y)(i.R2l,{})})})]}),m===t&&!x&&(0,l.Y)("div",{style:{padding:`0 ${M.spacing.xs}px`},children:(0,l.Y)(s.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_sections_runscomparesectionheader.tsx_251",onClick:H,icon:(0,l.Y)(u.C,{}),ref:T})}),!x&&(0,l.Y)("div",{className:"section-element-visibility-on-hover section-element-hidden-on-edit",css:I,children:(0,l.Y)(i.WP0,{rotate:90,style:{color:M.colors.textSecondary},ref:U,onMouseDown:()=>k(!0),onMouseLeave:()=>{k(!1)},"data-testid":"experiment-view-compare-runs-section-header-drag-handle"})}),!x&&(0,l.FD)("div",{style:{position:"absolute",top:"50%",right:"0",transform:"translate(0, -50%)",display:"flex",alignItems:"center"},children:[(0,l.FD)("div",{onClick:N,onMouseDown:N,onMouseUp:N,onDoubleClick:N,className:"section-element-visibility-on-hover-and-not-drag section-element-hidden-on-edit",children:[(0,l.FD)(i.rId.Root,{modal:!1,children:[(0,l.Y)(i.rId.Trigger,{asChild:!0,children:(0,l.Y)(s.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_sections_runscomparesectionheader.tsx_288",icon:(0,l.Y)(i.ssM,{})})}),(0,l.FD)(i.rId.Content,{children:[(0,l.Y)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_sections_runschartssectionheader.tsx_321",onClick:()=>{h(n.uuid,!0)},children:(0,l.Y)(C.A,{id:"CevPsF",defaultMessage:"Add section above"})}),(0,l.Y)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_sections_runschartssectionheader.tsx_327",onClick:()=>{h(n.uuid,!1)},children:(0,l.Y)(C.A,{id:"aaKoNq",defaultMessage:"Add section below"})}),(0,l.Y)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_sections_runschartssectionheader.tsx_333",onClick:()=>{K(!0)},children:(0,l.Y)(C.A,{id:"8XrJnT",defaultMessage:"Delete section"})})]})]}),(0,l.Y)(u.f,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_sections_runschartssectionheader.tsx_351",visible:F,onOk:()=>{p(n.uuid)},onCancel:()=>{K(!1)},title:"Delete section",children:(0,l.Y)(C.A,{id:"TPspzA",defaultMessage:"Deleting the section will permanently remove it and the charts it contains. This cannot be undone."})})]}),(0,l.Y)("div",{onClick:N,onMouseDown:N,onMouseUp:N,onDoubleClick:N,className:S()({"section-element-visibility-on-hover-and-not-drag":!y},"section-element-hidden-on-edit"),css:(0,r.AH)({alignSelf:"flex-end",marginLeft:M.spacing.xs},""),children:(0,l.Y)(b,{onAddChart:c(n.uuid),supportedChartTypes:_})})]})]})})};var R=n(62758),A=n(36118),k=n(53606),Y=n(5900),T=n(11428),D=n(15230);let L=null;const F=new WeakMap;function K(e,t){var n;return L||(L=new IntersectionObserver((e=>{for(const t of e){const e=F.get(t.target);null===e||void 0===e||e(t.isIntersecting)}}))),F.set(e,t),null===(n=L)||void 0===n||n.observe(e),()=>{var t;e&&(null===(t=L)||void 0===t||t.unobserve(e),F.delete(e))}}var N=n(76137),H=n(6922),O=n(71932),B=n(21039);const P=(0,a.memo)((e=>{var t,n;const{setElementRef:i,isInViewport:o}=function(){let{enabled:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const[t,n]=(0,a.useState)(null),[r,i]=(0,a.useState)(!e);return(0,a.useEffect)((()=>{if(t){if(window.IntersectionObserver&&e)return K(t,i);i(!0)}}),[e,t]),{isInViewport:r,setElementRef:n}}(),{uuid:c,translateBy:d,onResizeStart:u,onResize:p,onResizeStop:h,...m}=e,{theme:g}=(0,s.u)(),[f]=(0,N.d7)(o,150),v=f,[y,x]=(0,a.useState)(0),[C,b]=(0,a.useState)(0),[_,w]=(0,a.useState)(!1),[M,S]=(0,a.useState)({x:0,y:0}),{setDraggedCardUuid:I,onDropChartCard:E}=(0,O.Mz)(),R=(0,a.useRef)(null),A=(0,a.useCallback)(((e,t)=>{let{x:n,y:r}=t;w(!0),I(null!==c&&void 0!==c?c:null),S({x:n,y:r})}),[I,c]),k=(0,a.useCallback)(((e,t)=>{let{x:n,y:r}=t;R.current&&(R.current.style.transform=`translate3d(${n-M.x}px, ${r-M.y}px, 0)`)}),[M]),L=(0,a.useCallback)((()=>{E(),I(null),R.current&&(R.current.style.transform=""),w(!1)}),[E,I,R]),F=(0,a.useCallback)((()=>{var e;const t=null===(e=R.current)||void 0===e?void 0:e.getBoundingClientRect();var n,r;t&&(x(null!==(n=null===t||void 0===t?void 0:t.width)&&void 0!==n?n:0),b(null!==(r=null===t||void 0===t?void 0:t.height)&&void 0!==r?r:0),null===u||void 0===u||u(t))}),[u,R]),P=(0,a.useCallback)(((e,t)=>{let{size:n}=t;x(n.width),b(n.height),p(n.width,n.height)}),[p]);return o?(0,l.Y)(T.DraggableCore,{enableUserSelectHack:!1,onStart:A,onDrag:k,onStop:L,handle:`.${H.rZ}`,children:(0,l.Y)(D.Resizable,{width:y,height:C,axis:"both",onResizeStart:F,onResizeStop:h,onResize:P,handle:(0,l.Y)(U,{}),children:(0,l.Y)("div",{ref:e=>{R.current=e,i(e)},style:_?{zIndex:B.K.CARD_DRAGGED,pointerEvents:"none"}:{transition:H.b7,transform:`translate3d(${null!==(t=null===d||void 0===d?void 0:d.x)&&void 0!==t?t:0}px,${null!==(n=null===d||void 0===d?void 0:d.y)&&void 0!==n?n:0}px,0)`,opacity:null!==d&&void 0!==d&&d.overflowing?0:1},children:(0,l.Y)(Y.i,{...m,isInViewport:o,isInViewportDeferred:v})})})}):(0,l.Y)(H.Us,{style:{height:e.height},css:(0,r.AH)({backgroundColor:g.colors.backgroundPrimary,border:`1px solid ${g.colors.border}`,borderRadius:g.general.borderRadiusBase},""),ref:i})})),U=(0,a.forwardRef)(((e,t)=>{const{theme:n}=(0,s.u)();return(0,l.Y)("div",{ref:t,...e,"data-testid":"draggable-card-resize-handle",css:(0,r.AH)({position:"absolute",bottom:0,right:0,cursor:"se-resize",lineHeight:0,padding:n.spacing.xs,color:n.colors.actionDefaultIconDefault},""),children:(0,l.Y)("svg",{width:"8",height:"8",viewBox:"0 0 8 8",children:(0,l.Y)("path",{d:"M6 6V0H8V8H0V6H6Z",fill:"currentColor"})})})}));var z={name:"b98nv4",styles:"position:absolute;inset:0"};const j=e=>{let{x:t,y:n,width:i,height:o}=e;const{theme:a}=(0,s.u)();return(0,l.FD)(l.FK,{children:[(0,l.Y)("div",{css:z}),(0,l.Y)("div",{css:(0,r.AH)({position:"absolute",backgroundColor:a.colors.actionDefaultBackgroundHover,borderStyle:"dashed",borderColor:a.colors.actionDefaultBorderDefault,pointerEvents:"none",borderRadius:a.general.borderRadiusBase,borderWidth:2,inset:0,zIndex:B.K.CARD_PREVIEW},""),style:{transform:`translate3d(${t}px, ${n}px, 0)`,width:i,height:o}})]})};const G=[300,330,360,400,500],V=Symbol("placeholder");var W={name:"1h0bf8r",styles:"body, :host{user-select:none;}"},$={name:"1ezujoe",styles:"display:flex;justify-content:center;min-height:160px"};const q=(0,a.memo)((e=>{var t,n;let{cardsConfig:o,sectionConfig:c,chartRunData:d,sectionId:u,hideEmptyCharts:p,...h}=e;const{theme:m}=(0,s.u)(),g=(0,k.U)(`(max-width: ${m.responsive.breakpoints.md}px)`)?1:null!==(t=c.columns)&&void 0!==t?t:3,f=null!==(n=c.cardHeight)&&void 0!==n?n:360,v=(0,a.useRef)(null),{draggedCardUuid:y,isDragging:x}=(0,O.GP)(),{setTargetSection:b,setTargetPosition:_,onSwapCards:w}=(0,O.Mz)(),M=(0,R.g_)(),S=(0,a.useCallback)((e=>{M((t=>{var n,r;return(null===(n=t.compareRunSections)||void 0===n?void 0:n.find((e=>e.uuid===u)))?{...t,compareRunSections:null===(r=t.compareRunSections)||void 0===r?void 0:r.map((t=>t.uuid===u?{...t,columns:e}:t))}:t}))}),[u,M]),I=(0,a.useCallback)((e=>{M((t=>{var n,r;return(null===(n=t.compareRunSections)||void 0===n?void 0:n.find((e=>e.uuid===u)))?{...t,compareRunSections:null===(r=t.compareRunSections)||void 0===r?void 0:r.map((t=>t.uuid===u?{...t,cardHeight:e}:t))}:t}))}),[u,M]),E=(0,a.useRef)(0),[Y,T]=(0,a.useState)(null),[D,L]=(0,a.useState)(null);E.current=o.length;const F=y?Y:null,K=(0,a.useCallback)((e=>{var t,n;const r=m.spacing.sm,i=Math.ceil(o.length/g),s=Math.floor(e/g),a=e%g,l=g-1,c=a,d=s,u=null===(t=v.current)||void 0===t?void 0:t.getBoundingClientRect();return{overflowing:s>=i,row:s,col:a,x:a*(((null!==(n=null===u||void 0===u?void 0:u.width)&&void 0!==n?n:0)-l*r)/g)+c*r,y:s*f+d*r}}),[g,f,m,o.length]),N=(0,a.useMemo)((()=>o.filter((e=>!p||!(0,A.Cs)(d,e)))),[o,d,p]),B=(0,a.useMemo)((()=>{if(!y||null===F)return{};const e={},t=N.slice(),n=N.findIndex((e=>e.uuid===y)),r=F;-1!==n?(t.splice(n,1),t.splice(r,0,N[n])):t.splice(r,0,V);for(const i of N){const n=t.indexOf(i),r=N.indexOf(i),o=K(r),s=K(n);if(s.x===o.x&&s.y===o.y)continue;const a=s.x-o.x,l=s.y-o.y;i.uuid&&(e[i.uuid]={x:a,y:l,overflowing:s.overflowing})}return e}),[y,F,N,K]),U=(0,a.useMemo)((()=>{var e,t;if(null===F)return null;if(0===N.length)return{x:0,y:0,width:"100%",height:"100%"};const{x:n,y:r}=K(F),i=g-1,o=null===(e=v.current)||void 0===e?void 0:e.getBoundingClientRect();return{x:n,y:r,width:((null!==(t=null===o||void 0===o?void 0:o.width)&&void 0!==t?t:0)-i*m.spacing.sm)/g,height:f}}),[F,K,g,f,m,N.length]),z=(0,a.useCallback)((e=>{if(!x()||!v.current)return;const t=v.current.getBoundingClientRect(),n=Math.ceil(E.current/g);b(u);const r=Math.floor((e.clientY-t.top)/t.height*n)*g+Math.floor((e.clientX-t.left)/t.width*g);T(r),_(r)}),[g,x,u,b,_]),[q,X]=(0,a.useState)([]),Q=(0,a.useRef)(null),J=(0,a.useRef)(null),Z=(0,a.useCallback)((e=>{var t;const n=null===(t=v.current)||void 0===t?void 0:t.getBoundingClientRect();n&&(L({x:e.left-n.left,y:e.top-n.top,width:e.width,height:e.height}),X(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return[1,2,3,4,5].map((n=>({cols:n,width:(e-(n-1)*t)/n})))}(n.width)))}),[]),ee=(0,a.useCallback)((()=>{var e,t;S(null!==(e=Q.current)&&void 0!==e?e:g),I(null!==(t=J.current)&&void 0!==t?t:f),L(null)}),[f,g,I,S]),te=(0,a.useCallback)(((e,t)=>{const n=q.reduce(((t,n)=>Math.abs(n.width-e)<Math.abs(t.width-e)?n:t)),r=G.reduce(((e,n)=>Math.abs(n-t)<Math.abs(e-t)?n:e));Q.current=n.cols,J.current=r,L((e=>e?e.width!==n.width||e.height!==r?{...e,width:n.width,height:r}:e:null))}),[q]);return(0,l.FD)("div",{ref:v,css:["position:relative;",N.length>0&&{display:"grid",gap:m.spacing.sm},""],style:{gridTemplateColumns:"repeat("+g+", 1fr)",...y&&{[H.H8]:"transform 0.1s"}},"data-testid":"draggable-chart-cards-grid",onMouseMove:z,onMouseLeave:()=>{T(null)},children:[(y||D)&&(0,l.Y)(r.mL,{styles:W}),0===N.length&&(0,l.Y)("div",{css:$,children:(0,l.Y)(i.SvL,{title:(0,l.Y)(C.A,{id:"9TOU1G",defaultMessage:"No charts in this section"}),description:(0,l.Y)(C.A,{id:"9wWapn",defaultMessage:"Click 'Add chart' or drag and drop to add charts here."})})}),N.map(((e,t,n)=>{var r,i,o,s;return(0,l.Y)(P,{uuid:null!==(r=e.uuid)&&void 0!==r?r:"",translateBy:B[null!==(i=e.uuid)&&void 0!==i?i:""],onResizeStart:Z,onResizeStop:ee,onResize:te,cardConfig:e,chartRunData:d,onReorderWith:w,index:t,height:f,canMoveDown:Boolean(n[t+1]),canMoveUp:Boolean(n[t-1]),previousChartUuid:null===(o=n[t-1])||void 0===o?void 0:o.uuid,nextChartUuid:null===(s=n[t+1])||void 0===s?void 0:s.uuid,hideEmptyCharts:p,...h},e.uuid)})),U&&(0,l.Y)(j,{...U}),D&&(0,l.Y)(j,{...D})]})})),X=e=>{let{sectionId:t,sectionCharts:n,reorderCharts:r,insertCharts:i,isMetricHistoryLoading:o,chartData:s,startEditChart:a,removeChart:c,groupBy:d,sectionIndex:u,setFullScreenChart:p,autoRefreshEnabled:h,hideEmptyCharts:m,globalLineChartConfig:g,sectionConfig:f}=e;return(0,l.Y)(q,{sectionConfig:f,cardsConfig:n,chartRunData:s,onStartEditChart:a,onRemoveChart:c,setFullScreenChart:p,sectionId:t,groupBy:d,autoRefreshEnabled:h,hideEmptyCharts:m,globalLineChartConfig:g})};var Q=n(7204),J=n(9133);const Z=e=>{if(e.type===o.zL.BAR){const t=e;return t.dataAccessKey?[t.metricKey,t.dataAccessKey]:[t.metricKey]}if(e.type===o.zL.LINE){const t=e;return(0,J.isArray)(t.selectedMetricKeys)?t.selectedMetricKeys:[t.metricKey]}if(e.type===o.zL.SCATTER){const t=e;return[t.xaxis.key.toLowerCase(),t.yaxis.key.toLowerCase()]}if(e.type===o.zL.PARALLEL){const t=e;return[...t.selectedMetrics,...t.selectedParams]}{const t=e;return[t.xaxis.key,t.yaxis.key,t.zaxis.key]}},ee=e=>{let{compareRunSections:t,compareRunCharts:n,reorderCharts:d,insertCharts:p,chartData:h,isMetricHistoryLoading:m=!1,autoRefreshEnabled:g=!1,startEditChart:f,removeChart:v,addNewChartCard:y,search:x,groupBy:b,supportedChartTypes:_,hideEmptyCharts:w,setFullScreenChart:M=()=>{},globalLineChartConfig:S,noRunsSelectedEmptyState:I}=e;const A=(0,R.g_)(),[k,Y]=(0,a.useState)(-1),{theme:T}=(0,s.u)(),D=(0,a.useMemo)((()=>(t||[]).flatMap((e=>e.display?[e.uuid]:[]))),[t]),L=(0,a.useCallback)((e=>{A((t=>{const n=(t.compareRunSections||[]).map((t=>{const n=t.uuid,r="string"===typeof e&&n===e||Array.isArray(e)&&e.includes(n);return{...t,display:r}}));return{...t,compareRunSections:n}}))}),[A]),F=(0,a.useCallback)((e=>{A((t=>{const n=(t.compareRunCharts||[]).filter((t=>t.isGenerated||t.metricSectionId!==e)).map((t=>t.isGenerated&&t.metricSectionId===e?{...t,deleted:!0}:t)),r=(t.compareRunSections||[]).slice().filter((t=>t.uuid!==e));return{...t,compareRunCharts:n,compareRunSections:r,isAccordionReordered:!0}}))}),[A]),K=(0,a.useCallback)(((e,t)=>{let n=-1;return A((r=>{const i=[...r.compareRunSections||[]];n=i.findIndex((t=>t.uuid===e));const o={name:"",uuid:(0,Q.yk)(),display:!1,isReordered:!1};return n<0?i.push(o):(t||(n+=1),i.splice(n,0,o)),{...r,compareRunSections:i,isAccordionReordered:!0}})),n}),[A]),N=(0,a.useCallback)((()=>{A((e=>{const t=[...e.compareRunSections||[],{name:"",uuid:(0,Q.yk)(),display:!1,isReordered:!1}];return{...e,compareRunSections:t,isAccordionReordered:!0}})),Y((null===t||void 0===t?void 0:t.length)||-1)}),[A,null===t||void 0===t?void 0:t.length]),H=(0,a.useCallback)(((e,t)=>{A((n=>{const r=(n.compareRunSections||[]).map((n=>n.uuid===e?{...n,name:t}:n));return{...n,compareRunSections:r,isAccordionReordered:!0}}))}),[A]),O=(0,a.useCallback)(((e,t)=>{A((n=>{const r=(n.compareRunSections||[]).slice(),i=r.findIndex((t=>t.uuid===e)),o=r.findIndex((e=>e.uuid===t)),s=r.splice(i,1)[0];return r.splice(o,0,s),{...n,compareRunSections:r,isAccordionReordered:!0}}))}),[A]),B=(0,a.useMemo)((()=>0===h.filter((e=>{let{hidden:t}=e;return!t})).length),[h]),{sectionsToRender:P,chartsToRender:U}=(0,a.useMemo)((()=>{if(""===x)return{sectionsToRender:t,chartsToRender:n};const e=(n||[]).filter((e=>!e.deleted&&((e,t)=>{if(t.type===o.zL.IMAGE||t.type===o.zL.DIFFERENCE)return!0;try{const n=new RegExp(e,"i");return Z(t).some((e=>e.match(n)))}catch{return!0}})(x,e))),r=new Set;e.forEach((e=>{e.metricSectionId&&r.add(e.metricSectionId)}));return{sectionsToRender:(t||[]).filter((e=>r.has(e.uuid))),chartsToRender:e}}),[x,n,t]),z=""!==x;return t&&n?B?null!==I&&void 0!==I?I:(0,l.Y)("div",{css:(0,r.AH)({marginTop:T.spacing.lg},""),children:(0,l.Y)(i.SvL,{description:(0,l.Y)(C.A,{id:"jXfgov",defaultMessage:"All runs are hidden. Select at least one run to view charts."})})}):z&&0===(null===U||void 0===U?void 0:U.length)?(0,l.FD)(l.FK,{children:[(0,l.Y)(u.S,{size:"lg"}),(0,l.Y)(i.SvL,{title:(0,l.Y)(C.A,{id:"D+UN8o",defaultMessage:"No metric charts"}),description:(0,l.Y)(C.A,{id:"gZSJbg",defaultMessage:"All charts are filtered. Clear the search filter to see hidden metric charts."})})]}):(0,l.FD)("div",{children:[(0,l.Y)(c,{activeKey:D,onActiveKeyChange:L,children:(P||[]).map(((e,t)=>{const n=(U||[]).filter((t=>{const n=t.metricSectionId;return!t.deleted&&n===e.uuid}));return(0,l.Y)(i.nD3.Panel,{header:(0,l.Y)(E,{index:t,section:e,onDeleteSection:F,onAddSection:K,editSection:k,onSetEditSection:Y,onSetSectionName:H,sectionChartsLength:n.length,addNewChartCard:y,onSectionReorder:O,isExpanded:D.includes(e.uuid),supportedChartTypes:_,hideExtraControls:z}),"aria-hidden":!D.includes(e.uuid),children:(0,l.Y)(X,{sectionId:e.uuid,sectionConfig:e,sectionCharts:n,reorderCharts:d,insertCharts:p,isMetricHistoryLoading:m,chartData:h,startEditChart:f,removeChart:v,groupBy:b,sectionIndex:t,setFullScreenChart:M,autoRefreshEnabled:g,hideEmptyCharts:w,globalLineChartConfig:S})},e.uuid)}))}),!z&&(0,l.Y)("div",{children:(0,l.Y)(s.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_sections_runscomparesectionaccordion.tsx_405",block:!0,onClick:N,icon:(0,l.Y)(i.c11,{}),style:{border:"none",marginTop:"6px"},children:(0,l.Y)(C.A,{id:"YlavFP",defaultMessage:"Add section"})})})]}):null}},40029:function(e,t,n){"use strict";n.d(t,{I:function(){return u}});var r=n(89555),i=n(31014),o=n(62758),s=n(88464),a=n(32599),l=n(41028),c=n(76137),d=n(50111);const u=e=>{let{chartsSearchFilter:t}=e;const n=(0,o.g_)(),{theme:u}=(0,a.u)(),[p,h]=(0,i.useState)((()=>null!==t&&void 0!==t?t:"")),[m,g]=(0,i.useState)(!1),{formatMessage:f}=(0,s.A)(),v=(0,i.useCallback)((e=>{n((t=>({...t,chartsSearchFilter:e}))),g(!1)}),[n]),y=(0,c.YQ)(v,150);return(0,d.Y)(l.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsfilterinput.tsx_30",role:"searchbox",prefix:(0,d.Y)("div",{css:(0,r.AH)({width:u.general.iconFontSize,lineHeight:0},""),children:m?(0,d.Y)(a.S,{size:"small"}):(0,d.Y)(l.S,{})}),value:p,allowClear:!0,onChange:e=>{h(e.target.value),g(!0),y(e.target.value)},placeholder:f({id:"Xs1oJm",defaultMessage:"Search metric charts"})})}},43270:function(e,t,n){"use strict";n.d(t,{h:function(){return u}});var r,i,o,s,a,l=n(31014);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(null,arguments)}function d(e,t){let{title:n,titleId:d,...u}=e;return l.createElement("svg",c({width:18,height:18,viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":d},u),n?l.createElement("title",{id:d},n):null,r||(r=l.createElement("path",{d:"M1 1V17H17",stroke:"#A3AEB8",strokeLinecap:"round"})),i||(i=l.createElement("path",{d:"M4.21875 3.58289C4.55663 3.58289 4.88067 3.68935 5.21469 3.73008C5.6474 3.78285 6.02137 3.96513 6.37191 4.21552C6.76902 4.49917 7.0271 4.74589 7.24101 5.18954C7.44337 5.60925 7.59378 6.06989 7.72958 6.51432C7.86002 6.94121 8.09708 7.33726 8.19153 7.77803C8.25347 8.0671 8.34514 8.32427 8.44678 8.60015C8.58028 8.96252 8.80086 9.27915 8.96667 9.62583C9.15145 10.0122 9.216 10.4461 9.34876 10.8551C9.57831 11.5623 9.91132 12.4763 10.7205 12.6731C11.4284 12.8453 12.1459 12.8845 12.8705 12.8845C13.0981 12.8845 13.3979 12.898 13.5831 12.7499C13.6402 12.7042 13.7451 12.6907 13.8164 12.6591C13.8752 12.6329 14.0919 12.5871 14.1123 12.5463",stroke:"#CCE3F2",strokeLinecap:"round"})),o||(o=l.createElement("path",{d:"M4.16162 12.9805C4.32512 12.9805 4.4895 12.9859 4.65284 12.9805C4.84076 12.9742 5.0468 12.7952 5.2099 12.707C5.62236 12.4841 5.99426 12.0648 6.24299 11.6739C6.49491 11.2781 6.8776 10.9871 7.0887 10.5649C7.27767 10.187 7.50992 9.80749 7.73691 9.45078C7.93911 9.13304 8.10211 8.77722 8.28637 8.44554C8.43088 8.18544 8.45 7.88672 8.60542 7.62768C8.94898 7.05509 9.16215 6.418 9.45873 5.82484C9.72614 5.29001 9.96898 4.82426 10.4159 4.42207C10.5753 4.27853 10.8984 4.21824 11.0995 4.14861C11.3303 4.06873 11.579 4.02714 11.8136 3.93592C12.0394 3.84807 12.2521 3.84383 12.4795 3.77386C12.6993 3.70623 12.9319 3.63713 13.1632 3.63713C13.5192 3.63713 13.9089 3.59155 14.2798 3.59155",stroke:"#CCE3F2",strokeLinecap:"round"})),s||(s=l.createElement("path",{d:"M3.79248 2.74207V13.8225",stroke:"#338ECC",strokeLinecap:"round"})),a||(a=l.createElement("path",{d:"M14.873 3.04419V13.7217",stroke:"#338ECC",strokeLinecap:"round"})))}const u=l.forwardRef(d);n.p},45653:function(e,t,n){"use strict";n.d(t,{Qr:function(){return c},U1:function(){return f},Xb:function(){return h},Y7:function(){return s},fx:function(){return a},ib:function(){return v},yv:function(){return d},z7:function(){return g}});var r=n(9133),i=n.n(r);const o=()=>n.e(6386).then(n.t.bind(n,86386,23)),s=(e,t)=>{const n=i().truncate(e,{length:t});return i().takeWhile(n,(e=>"\n"!==e)).join("")},a=(e,t)=>{if(e.length>t){const n=Math.floor((t-3)/2),r=t-3-n;return e.substring(0,n)+"..."+e.substring(e.length-r,e.length)}return e},l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",c=e=>{let t="",n=0;const r=u(e);for(;n<r.length;){const e=r.charCodeAt(n++),i=r.charCodeAt(n++),o=r.charCodeAt(n++),s=e>>2,a=(3&e)<<4|i>>4;let c=(15&i)<<2|o>>6,d=63&o;isNaN(i)?(d=64,c=d):isNaN(o)&&(d=64),t=t+l.charAt(s)+l.charAt(a)+l.charAt(c)+l.charAt(d)}return t},d=e=>{let t="",n=0;const r=(null===e||void 0===e?void 0:e.replace(/[^A-Za-z0-9+/=]/g,""))||"";for(;n<r.length;){const e=l.indexOf(r.charAt(n++)),i=l.indexOf(r.charAt(n++)),o=l.indexOf(r.charAt(n++)),s=l.indexOf(r.charAt(n++)),a=e<<2|i>>4,c=(15&i)<<4|o>>2,d=(3&o)<<6|s;t+=String.fromCharCode(a),64!==o&&(t+=String.fromCharCode(c)),64!==s&&(t+=String.fromCharCode(d))}return p(t)},u=function(){const e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/\r\n/g,"\n");let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);t+=r<128?String.fromCharCode(r):r>127&&r<2048?String.fromCharCode(r>>6|192)+String.fromCharCode(63&r|128):String.fromCharCode(r>>12|224)+String.fromCharCode(r>>6&63|128)+String.fromCharCode(63&r|128)}return t},p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t="",n=0;for(;n<e.length;){const r=e.charCodeAt(n);if(r<128)t+=String.fromCharCode(r),n++;else if(r>191&&r<224){const i=e.charCodeAt(n+1);t+=String.fromCharCode((31&r)<<6|63&i),n+=2}else{const i=e.charCodeAt(n+1),o=e.charCodeAt(n+2);t+=String.fromCharCode((15&r)<<12|(63&i)<<6|63&o),n+=3}}return t},h=e=>crypto.subtle.digest("SHA-256",(new TextEncoder).encode(e)).then((e=>Array.prototype.map.call(new Uint8Array(e),(e=>("00"+e.toString(16)).slice(-2))).join(""))),m="deflate;",g=async e=>{const t=(await o()).deflate(e);if("undefined"!==typeof Buffer){const e=Buffer.from(t).toString("base64");return`${m}${e}`}const n=Array.from(t,(e=>String.fromCodePoint(e))).join("");return`${m}${btoa(n)}`},f=async e=>{const t=await o();if(!e.startsWith(m))throw new Error("Invalid compressed text, payload header invalid");const n=e.slice(8);if("undefined"!==typeof Buffer){const e=Buffer.from(n,"base64");return t.inflate(e,{to:"string"})}const r=atob(n);return t.inflate(Uint8Array.from(r,(e=>{var t;return null!==(t=e.codePointAt(0))&&void 0!==t?t:0})),{to:"string"})},v=e=>e.startsWith(m)},47790:function(){},49698:function(e,t,n){"use strict";n.d(t,{l:function(){return g}});var r=n(9133),i=n(31014);const o=/\$\{([^}]+)\}/g;var s=function(e){return e.ADD="+",e.SUBTRACT="-",e.MULTIPLY="*",e.DIVIDE="/",e.POWER="^",e.NEGATIVE_SIGN="_",e.POSITIVE_SIGN="|",e}(s||{}),a=function(e){return e.OPEN="(",e.CLOSE=")",e}(a||{}),l=function(e){return e.OPEN="{",e.CLOSE="}",e}(l||{});const c={[s.ADD]:1,[s.SUBTRACT]:1,[s.MULTIPLY]:2,[s.DIVIDE]:2,[s.POWER]:3,[s.NEGATIVE_SIGN]:4,[s.POSITIVE_SIGN]:4},d=e=>"string"===typeof e&&[s.ADD,s.SUBTRACT,s.MULTIPLY,s.DIVIDE,s.POWER].includes(e),u=e=>"string"===typeof e&&[s.NEGATIVE_SIGN,s.POSITIVE_SIGN].includes(e),p=e=>e.slice(2,-1),h=(e,t)=>{const n=e.pop();if(void 0===n)throw new Error("Invalid expression: stack is empty");if(d(n)){if(t[t.length-1]<2)throw new Error("Invalid expression: Stack has binary operator without enough operands");t[t.length-1]--}else{if(!u(n))throw new Error("Invalid expression: Stack has invalid elements");if(t[t.length-1]<1)throw new Error("Invalid expression: Stack has unary operator without enough operands")}return n},m=(e,t,n)=>{for(;e.length>0&&u((0,r.last)(e));)t.push(h(e,n))},g=()=>({compileExpression:(0,i.useCallback)(((e,t)=>{try{const n=e.replace(o,"");if(!/^[0-9+\-*/().\s^]*$/.test(n))return;const{expression:i,variables:g}=(e=>{const t=e.match(o);if(!t)return{expression:e,variables:[]};const n=(0,r.uniq)(t),i={};return n.forEach(((e,t)=>{i[e]=`\${${t}}`})),{expression:e.replace(o,(e=>e in i?i[e]:e)),variables:n.map(p)}})(e);for(const e of g)if(!t.includes(e))return;const f=i.replace(/\s/g,""),v=(e=>{const t=[],n=[],i=[0],o=()=>{i[i.length-1]++};for(let g=0;g<e.length;g++){let f=e[g];const v=0===g||d(e[g-1])||e[g-1]===a.OPEN;if(f===s.SUBTRACT&&v?f=s.NEGATIVE_SIGN:f===s.ADD&&v&&(f=s.POSITIVE_SIGN),"$"===f){let r="";if(g+1>=e.length||e[g+1]!==l.OPEN)throw new Error("Invalid expression: Variable must be followed by {");for(g++;g+1<e.length&&e[g+1]!==l.CLOSE;)r+=e[++g];g++,n.push(r),o(),m(t,n,i)}else if(/\d/.test(f)||"."===f){let r=f;for(;g+1<e.length&&(/\d/.test(e[g+1])||"."===e[g+1]);)r+=e[++g];const s=r.match(/\./g);if(s&&s.length>1)throw new Error("Invalid expression: Number has multiple decimal points");const a=parseFloat(r);o(),n.push(a),m(t,n,i)}else if(u(f))t.push(f);else if(d(f)){if(0===n.length)throw new Error("Invalid expression: Binary operator without operands");for(;t.length>0&&d((0,r.last)(t))&&(p=(0,r.last)(t),c[p]>c[f]||(0,r.last)(t)===f&&f!==s.POWER);)n.push(h(t,i));t.push(f)}else if(f===a.OPEN)t.push(f),i.push(0);else{if(f!==a.CLOSE)throw new Error("Invalid expression: Unknown character in expression");for(;t.length>0&&(0,r.last)(t)!==a.OPEN;)n.push(h(t,i));if(t.pop()!==a.OPEN)throw new Error("Invalid expression: Parenthesis mismatch");if(1!==i[i.length-1])throw new Error("Invalid expression: Parenthesis does not have exactly one operand");i.pop(),o(),m(t,n,i)}}for(var p;t.length>0;)n.push(h(t,i));if(1!==i.length||1!==i[0])throw new Error("Invalid expression: Invalid number of operands");return n})(f.replace(o,(e=>{const t=parseInt(e.slice(2,-1),10);return`\${${g[t]}}`})));return{rpn:v,variables:g,expression:e}}catch(n){return}}),[]),evaluateExpression:(e,t)=>{if(void 0!==e)try{return(e=>{const t=[];if(e.forEach((e=>{if("number"!==typeof e)if(u(e)){const n=t.pop();if("number"!==typeof n)throw new Error("Invalid expression: Unary operator without operand");switch(e){case s.NEGATIVE_SIGN:t.push(-n);break;case s.POSITIVE_SIGN:t.push(n)}}else{if(!d(e))throw new Error("Invalid expression: Unknown token in expression");{const n=t.pop(),r=t.pop();if("number"!==typeof r||"number"!==typeof n)throw new Error("Invalid expression: Binary operator without enough operands");switch(e){case s.ADD:t.push(r+n);break;case s.SUBTRACT:t.push(r-n);break;case s.MULTIPLY:t.push(r*n);break;case s.DIVIDE:t.push(r/n);break;case s.POWER:t.push(Math.pow(r,n))}}}else t.push(e)})),1!==t.length||"number"!==typeof t[0])throw new Error("Invalid expression: Invalid expression result");return t[0]})(e.rpn.map((n=>"string"===typeof n&&e.variables.includes(n)?t[n]:n)))}catch(n){return}}})},50684:function(e,t,n){"use strict";n.d(t,{H:function(){return c},W:function(){return l}});var r=n(31014),i=n(64596);const o={paper_bgcolor:"white",plot_bgcolor:"white"},s={width:1200,height:600},a={displaylogo:!1,modeBarButtonsToRemove:["toImage"]},l=(e,t)=>(n,r)=>(0,i.downloadImage)({data:e,layout:{...t,...o},config:a},{...s,format:n,filename:r}),c=()=>{const e=(0,r.useRef)(null),[t,n]=(0,r.useState)(null),i=(0,r.useCallback)((t=>{e.current=t,n((t=>t||((t,n)=>{var r;return null===(r=e.current)||void 0===r?void 0:r.call(e,t,n)})))}),[]);return[t,i]}},51293:function(e,t,n){"use strict";n.d(t,{p:function(){return a}});var r=n(32599),i=n(31014),o=n(50111);const s=i.lazy((()=>Promise.all([n.e(1570),n.e(2402)]).then(n.bind(n,22402)))),a=e=>(0,o.Y)(i.Suspense,{fallback:(0,o.Y)("div",{css:e=>({display:"flex",justifyContent:"center",margin:e.spacing.md}),children:(0,o.Y)(r.S,{})}),children:(0,o.Y)(s,{...e})})},53606:function(e,t,n){"use strict";n.d(t,{U:function(){return o}});var r=n(31014),i=n(18930);function o(e){return function(e){return(0,i.useSyncExternalStore)(r.useCallback((t=>(e.addEventListener("change",t),()=>{e.removeEventListener("change",t)})),[e]),(()=>e.matches))}(r.useMemo((()=>window.matchMedia(e)),[e]))}},53677:function(e,t,n){"use strict";n.d(t,{j:function(){return m}});var r=n(31014),i=n(10811),o=n(64912),s=n(67245),a=n(58645),l=n(26809),c=n(7204),d=n(50111);class u extends r.Component{constructor(){super(...arguments),this.formRef=r.createRef(),this.handleRenameRun=e=>{const t=e[a.m],n=(0,c.yk)();return this.props.updateRunApi(this.props.runUuid,t,n).then((()=>{var e,t;return null===(e=(t=this.props).onSuccess)||void 0===e?void 0:e.call(t)}))}}render(){const{isOpen:e=!1,runName:t}=this.props;return(0,d.Y)(s.B,{title:this.props.intl.formatMessage({id:"+Dnww0",defaultMessage:"Rename Run"}),okText:this.props.intl.formatMessage({id:"7NKkk1",defaultMessage:"Save"}),isOpen:e,handleSubmit:this.handleRenameRun,onClose:this.props.onClose,children:(0,d.Y)(a.P,{type:"run",name:t,innerRef:this.formRef,visible:e,validator:async(e,t)=>{if("string"===typeof t&&t.length&&!t.trim())throw new Error(this.props.intl.formatMessage({id:"ze1m7Z",defaultMessage:"Run name cannot consist only of whitespace!"}));return!0}})})}}const p={updateRunApi:l.FZ},h=(0,o.Ay)(u),m=(0,i.Ng)(void 0,p)(h)},55999:function(e,t,n){"use strict";n.d(t,{L:function(){return l}});var r=n(76010),i=n(26809),o=n(25866),s=n(31014),a=n(10811);const l=e=>{let{runUuids:t,runUuidsIsActive:n,autoRefreshEnabled:l,enabled:c}=e;const d=(0,a.wA)(),u=t.slice(0,o.Lf).join(",");(0,s.useEffect)((()=>{c&&!l&&u.split(",").forEach((e=>{e&&d((0,i.LN)(e))}))}),[u,d,l,c]);const p=(0,s.useRef)(void 0),h=(0,s.useRef)(l&&c);h.current=l;const m=n.slice(0,o.Lf).join(",");(0,s.useEffect)((()=>{let e=!1;if(!c||!l)return;const t=async e=>{const t=u.split(","),n=m.split(",");(e?t.filter(((e,t)=>"true"===n[t])):t).forEach((t=>{t&&d((0,i.LN)(t,e))}))},n=async()=>{if(h.current&&!e){try{await t(!0)}catch(i){r.A.logErrorAndNotifyUser(i)}clearTimeout(p.current),h.current&&!e&&(p.current=window.setTimeout(n,o.zD))}};return t(!1).then(n),()=>{t(!0),e=!0,clearTimeout(p.current)}}),[d,u,m,l,c])}},58645:function(e,t,n){"use strict";n.d(t,{P:function(){return c},m:function(){return a}});var r=n(31014),i=n(48012),o=n(41028),s=n(50111);const a="newName";class l extends r.Component{constructor(){super(...arguments),this.inputToAutoFocus=void 0,this.autoFocusInputRef=e=>{this.inputToAutoFocus=e,e&&e.focus(),e&&e.select()},this.autoFocus=e=>{!1===e.visible&&!0===this.props.visible&&(this.inputToAutoFocus&&this.inputToAutoFocus.focus(),this.inputToAutoFocus&&this.inputToAutoFocus.select())},this.resetFields=e=>{const t=this.props.innerRef;e.name!==this.props.name&&t.current.resetFields([a])}}componentDidUpdate(e){this.autoFocus(e),this.resetFields(e)}render(){return(0,s.Y)(i.SQ4,{ref:this.props.innerRef,layout:"vertical",children:(0,s.Y)(i.SQ4.Item,{name:a,initialValue:this.props.name,rules:[{required:!0,message:`Please input a new name for the ${this.props.type}.`},{validator:this.props.validator}],label:`New ${this.props.type} name`,children:(0,s.Y)(o.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_renameform.tsx_69",placeholder:`Input a ${this.props.type} name`,ref:this.autoFocusInputRef,"data-testid":"rename-modal-input"})})})}}const c=l},59264:function(e,t,n){"use strict";n.d(t,{J:function(){return m},X:function(){return v}});var r=n(31014),i=n(65243),o=n(75627),s=n(6922),a=n(50684),l=n(21616),c=n(35286),d=n(75145),u=n(32599),p=n(48012),h=n(50111);const m={t:24,b:48,r:0,l:4,pad:0};var g={name:"uxy5jl",styles:"flex:1;display:flex;align-items:center;overflow:hidden"},f={name:"1wcfv52",styles:"margin-right:0"};const v=e=>{var t;let{config:n,chartRunData:v,onDelete:x,onEdit:C,fullScreen:b,setFullScreenChart:_,hideEmptyCharts:w,isInViewport:M,...S}=e;const I=null!==(t=n.dataAccessKey)&&void 0!==t?t:n.metricKey,E=(0,r.useMemo)((()=>v.filter((e=>{let{hidden:t,metrics:n}=e;return!t&&n[I]}))),[v,I]),R=(0,r.useMemo)((()=>!E.flatMap((e=>{let{metrics:t}=e;return Object.keys(t)})).includes(I)),[I,E]),{setTooltip:A,resetTooltip:k,selectedRunUuid:Y}=(0,o.uN)(n),T=b||M,[D,L]=(0,a.H)(),F=(0,h.Y)("div",{css:[y.barChartCardWrapper,{height:b?"100%":void 0},""],children:T?(0,h.Y)(i.u,{runsData:E,metricKey:I,displayRunNames:!1,displayMetricKey:!1,useDefaultHoverBox:!1,margin:m,onHover:A,onUnhover:k,selectedRunUuid:Y,onSetDownloadHandler:L}):null});if(b)return F;if(w&&R)return null;const K=n.datasetName?(0,h.Y)("div",{css:g,children:(0,h.FD)(u.T.Text,{title:n.metricKey,ellipsis:!0,bold:!0,children:[(0,h.Y)(p.vwO,{componentId:"mlflow.charts.bar_card_title.dataset_tag",css:f,children:n.datasetName})," ",n.metricKey]})}):null!==(N=null!==(H=null===(O=c.g[n.metricKey])||void 0===O?void 0:O.displayName)&&void 0!==H?H:n.displayName)&&void 0!==N?N:n.metricKey;var N,H,O;return(0,h.Y)(s.Ox,{onEdit:C,onDelete:x,title:K,uuid:n.uuid,dragGroupKey:s.cN.GENERAL_AREA,toggleFullScreenChart:R?void 0:()=>{var e,t;null===_||void 0===_||_({config:n,title:null!==(e=null===(t=c.g[n.metricKey])||void 0===t?void 0:t.displayName)&&void 0!==e?e:n.metricKey,subtitle:null})},supportedDownloadFormats:["png","svg","csv"],onClickDownload:e=>{if("csv"!==e&&"csv-full"!==e)null===D||void 0===D||D(e,n.metricKey);else{const e=[...E].reverse();(0,l.LL)(e,[n.metricKey],[],n.metricKey)}},...S,children:R?(0,h.Y)(d.u,{}):F})},y={barChartCardWrapper:{overflow:"hidden"}}},59397:function(e,t,n){"use strict";n.d(t,{G:function(){return f}});var r=n(89555),i=n(9133),o=n(31014),s=n(16026),a=n(69526),l=n(88464),c=n(36118),d=n(32599),u=n(91144),p=n(50111);const h=e=>{let{value:t}=e;return(0,p.FD)(p.FK,{children:[(0,p.Y)(a.XU,{value:t,year:"numeric"}),"-",(0,p.Y)(a.XU,{value:t,month:"2-digit"}),"-",(0,p.Y)(a.XU,{value:t,day:"2-digit"})," ",(0,p.Y)(a.ib,{value:t,hour:"numeric",hourCycle:"h24"}),":",(0,p.Y)(a.ib,{value:t,minute:"2-digit"}),":",(0,p.Y)(a.ib,{value:t,second:"2-digit",fractionalSecondDigits:3})]})},m=e=>{let{value:t}=e;return(0,p.FD)(p.FK,{children:[(0,p.Y)(a.ib,{value:t,hour:"2-digit",hourCycle:"h23"}),":",(0,p.Y)(a.ib,{value:t,minute:"2-digit"}),":",(0,p.Y)(a.ib,{value:t,second:"2-digit",fractionalSecondDigits:3})]})};var g={name:"1efi8gv",styles:"font-weight:bold"};const f=e=>{let{hoverData:t}=e;const{tooltipLegendItems:n,hoveredDataPoint:a,xValue:f,xAxisKeyLabel:v}=t,{traceUuid:y,metricEntity:x}=a||{},C=(0,l.A)(),{theme:b}=(0,d.u)(),_=`${y}.${null===x||void 0===x?void 0:x.key}`,w=t.xAxisKey===c.fj.METRIC?v:C.formatMessage((0,c.fw)(t.xAxisKey));return n?(0,p.FD)("div",{children:[!(0,i.isUndefined)(f)&&(0,p.FD)("div",{css:(0,r.AH)({marginBottom:b.spacing.xs},""),children:[(0,p.Y)("span",{css:g,children:w})," ",t.xAxisKey===c.fj.TIME?(0,p.Y)(h,{value:f}):(0,u.v$)()&&t.xAxisKey===c.fj.TIME_RELATIVE?(0,p.Y)(m,{value:f}):f]}),(0,p.Y)("div",{css:(0,r.AH)({display:"grid",gridTemplateColumns:`${b.general.iconSize}px auto auto`,columnGap:b.spacing.sm,rowGap:b.spacing.sm/4,alignItems:"center"},""),children:n.map((e=>{let{displayName:t,color:n,uuid:a,value:l,dashStyle:c}=e;return(0,p.FD)(o.Fragment,{children:[(0,p.Y)(s.Y,{color:n||"transparent",dashStyle:c}),(0,p.Y)("div",{css:(0,r.AH)({marginRight:b.spacing.md,fontSize:b.typography.fontSizeSm,color:_===a?"unset":b.colors.textPlaceholder},""),children:t}),(0,p.Y)("div",{children:!(0,i.isUndefined)(l)&&(0,p.Y)("span",{css:(0,r.AH)({fontWeight:_===a?"bold":"normal",color:_===a?"unset":b.colors.textPlaceholder},""),children:l})})]},a)}))})]}):null}},62758:function(e,t,n){"use strict";n.d(t,{Ez:function(){return u},KP:function(){return h},cA:function(){return p},g_:function(){return c},iO:function(){return d},oB:function(){return l}});var r=n(31014),i=n(30152),o=n(7204),s=n(50111);const a=r.createContext((()=>{})),l=e=>{let{children:t,updateChartsUIState:n}=e;return(0,s.Y)(a.Provider,{value:n,children:t})},c=()=>r.useContext(a),d=()=>{const e=c();return(0,r.useCallback)(((t,n)=>{e((e=>{var r,i;const o=null===(r=e.compareRunCharts)||void 0===r?void 0:r.slice(),s=null===(i=e.compareRunSections)||void 0===i?void 0:i.slice();if(!o||!s)return e;const a=o.findIndex((e=>e.uuid===t)),l=o.findIndex((e=>e.uuid===n));if(a<0||l<0)return e;const c=o[a],d=o[l],u=d.metricSectionId===c.metricSectionId,p=s.findIndex((e=>e.uuid===c.metricSectionId)),h=s.findIndex((e=>e.uuid===d.metricSectionId));s.splice(p,1,{...s[p],isReordered:!0}),s.splice(h,1,{...s[h],isReordered:!0});const m={...c};return m.metricSectionId=d.metricSectionId,o.splice(a,1),u?o.splice(l,0,m):o.splice(o.findIndex((e=>e.uuid===n)),0,m),{...e,compareRunCharts:o,compareRunSections:s}}))}),[e])},u=()=>{const e=c();return t=>{const n=i.i$.serialize({...t,uuid:(0,o.yk)()});t.uuid?e((e=>{var r;return{...e,compareRunCharts:null===(r=e.compareRunCharts)||void 0===r?void 0:r.map((e=>e.uuid===t.uuid?{...n,uuid:e.uuid}:e))}})):e((e=>({...e,compareRunCharts:e.compareRunCharts&&[...e.compareRunCharts,n]})))}},p=()=>{const e=c();return(t,n)=>{e((e=>{var r,i;const o=null===(r=e.compareRunCharts)||void 0===r?void 0:r.slice(),s=null===(i=e.compareRunSections)||void 0===i?void 0:i.slice();if(!o||!s)return e;const a=o.findIndex((e=>e.uuid===t));if(a<0)return e;const l=o[a],c={...l};c.metricSectionId=n;const d=s.findIndex((e=>e.uuid===l.metricSectionId)),u=s.findIndex((e=>e.uuid===n));return s.splice(d,1,{...s[d],isReordered:!0}),s.splice(u,1,{...s[u],isReordered:!0}),o.splice(a,1),o.push(c),{...e,compareRunCharts:o,compareRunSections:s}}))}},h=()=>{const e=c();return(0,r.useCallback)((t=>{e((e=>{var n,r;return{...e,compareRunCharts:t.isGenerated?null===(n=e.compareRunCharts)||void 0===n?void 0:n.map((e=>e.uuid===t.uuid?{...e,deleted:!0}:e)):null===(r=e.compareRunCharts)||void 0===r?void 0:r.filter((e=>e.uuid!==t.uuid))}}))}),[e])}},63609:function(e,t,n){"use strict";n.d(t,{e:function(){return a}});var r=n(19404),i=n(9133),o=n(8986),s=n(31014);const a=function(e){var t;let{experimentIds:n,orderByAsc:a,orderByField:l,searchQuery:c,selectedFilterDatasets:d,orderByDatasetName:u,orderByDatasetDigest:p}=e,{enabled:h=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const m=["SEARCH_LOGGED_MODELS",JSON.stringify(n),l,a,c,JSON.stringify(d),u,p],{data:g,isLoading:f,isFetching:v,fetchNextPage:y,refetch:x,error:C}=(0,r.q)({queryKey:m,queryFn:async e=>{let{pageParam:t}=e;const r={experiment_ids:n,order_by:[{field_name:null!==l&&void 0!==l?l:"creation_time",ascending:null!==a&&void 0!==a&&a,dataset_name:u,dataset_digest:p}],page_token:t,filter:c,datasets:(0,i.isEmpty)(d)?void 0:d};return(0,o.G)("ajax-api/2.0/mlflow/logged-models/search","POST",r)},cacheTime:0,getNextPageParam:e=>e.next_page_token,refetchOnWindowFocus:!1,retry:!1,enabled:h});return{isLoading:f,isFetching:v,data:(0,s.useMemo)((()=>null===g||void 0===g?void 0:g.pages.flatMap((e=>null===e||void 0===e?void 0:e.models)).filter(Boolean)),[g]),nextPageToken:null===(t=(0,i.last)(null===g||void 0===g?void 0:g.pages))||void 0===t?void 0:t.next_page_token,refetch:x,error:C,loadMoreResults:y}}},63617:function(e,t,n){"use strict";n.d(t,{_:function(){return d}});var r=n(89555),i=n(32599),o=n(15579),s=n(75627),a=n(5900),l=n(50111);var c={name:"1fttcpj",styles:"display:flex;flex-direction:column"};const d=e=>{let{chartData:t,isMetricHistoryLoading:n=!1,groupBy:d,fullScreenChart:u,onCancel:p,tooltipContextValue:h,tooltipComponent:m,autoRefreshEnabled:g,globalLineChartConfig:f}=e;const{theme:v,getPrefixedClassName:y}=(0,i.u)();return u?(0,l.Y)(o.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsfullscreenmodal.tsx_53",visible:!0,onCancel:p,title:(0,l.FD)("div",{css:c,children:[u.title,(0,l.Y)("span",{css:(0,r.AH)({color:v.colors.textSecondary,fontSize:v.typography.fontSizeSm,marginRight:v.spacing.xs},""),children:u.subtitle})]}),footer:null,verticalSizing:"maxed_out",dangerouslySetAntdProps:{width:"95%"},css:(0,r.AH)({[`.${y("modal-body")}`]:{flex:1}},""),children:(0,l.Y)(s.W,{contextData:h,component:m,children:(0,l.Y)(a.i,{cardConfig:u.config,chartRunData:t,groupBy:d,index:0,sectionIndex:0,fullScreen:!0,autoRefreshEnabled:g,globalLineChartConfig:f,onRemoveChart:()=>{},onReorderCharts:()=>{},onStartEditChart:()=>{},setFullScreenChart:()=>{},canMoveDown:!1,canMoveUp:!1,onMoveDown:()=>{},onMoveUp:()=>{},onReorderWith:()=>{}})})}):null}},63779:function(){},65243:function(e,t,n){"use strict";n.d(t,{u:function(){return f}});var r=n(32599),i=n(31014),o=n(88464),s=n(79445),a=n(97604),l=n(25790),c=n(36118),d=n(23541),u=n(50684),p=n(35286),h=n(6922),m=n(50111);const g={displaylogo:!1,scrollZoom:!1,doubleClick:"autosize",showTips:!1,modeBarButtonsToRemove:["toImage"]},f=i.memo((e=>{var t,n;let{runsData:f,metricKey:y,className:x,margin:C=c.am,onUpdate:b,onHover:_,onUnhover:w,barWidth:M=3/4,width:S,height:I,displayRunNames:E=!0,useDefaultHoverBox:R=!0,displayMetricKey:A=!0,selectedRunUuid:k,onSetDownloadHandler:Y}=e;const T=(0,i.useMemo)((()=>{const e=f.map((e=>e.uuid)),t=f.map((e=>{let{displayName:t}=e;return t})),n=f.map((e=>{var t;return(0,c.jn)(null===(t=e.metrics[y])||void 0===t?void 0:t.value)})),r=f.map((e=>{var t;const n=p.g[y];var r;return n?n.valueFormatter({value:null===(r=e.metrics[y])||void 0===r?void 0:r.value}):function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return"number"===typeof e?e.toFixed(t):e}(null===(t=e.metrics[y])||void 0===t?void 0:t.value)})),i=f.map((e=>e.color));return[{y:e,x:n,names:t,text:r,textposition:n.map((e=>0===e?"outside":"auto")),textfont:{size:11},metrics:f.map((e=>e.metrics[y])),type:"bar",hovertemplate:R?"%{label}<extra></extra>":void 0,hoverinfo:R?"y":"none",hoverlabel:R?c.rv:void 0,width:M,orientation:"h",marker:{color:i}}]}),[f,y,M,R]),{layoutHeight:D,layoutWidth:L,setContainerDiv:F,containerDiv:K,isDynamicSizeSupported:N}=(0,c.xN)(),{formatMessage:H}=(0,o.A)(),{theme:O}=(0,r.u)(),B=(0,i.useMemo)((()=>(0,c.y)(O)),[O]),[P,U]=(0,i.useState)({width:S||L,height:I||D,hovermode:"y",margin:C,xaxis:{title:A?y:void 0,tickfont:{size:11,color:O.colors.textSecondary},tickformat:null!==(t=null===(n=p.g[y])||void 0===n?void 0:n.chartAxisTickFormat)&&void 0!==t?t:void 0},yaxis:{showticklabels:E,title:E?H({id:"+hQ+de",defaultMessage:"Run name"}):void 0,tickfont:{size:11,color:O.colors.textSecondary},fixedrange:!0},template:{layout:B}});(0,i.useEffect)((()=>{U((e=>({...e,width:S||L,height:I||D,margin:C,xaxis:{...e.xaxis,title:A?y:void 0}})))}),[L,D,C,y,S,I,A]);const{setHoveredPointIndex:z}=(0,l.pO)(K,k,f,l.Iq),j=(0,i.useCallback)((e=>{var t,n,r,i;let{points:o,event:s}=e;const a=null===(t=o[0].data)||void 0===t?void 0:t.metrics[o[0].pointIndex];z(null!==(n=null===(r=o[0])||void 0===r?void 0:r.pointIndex)&&void 0!==n?n:-1);const l={xValue:o[0].x,yValue:o[0].value,index:o[0].pointIndex,metricEntity:a},c=null===(i=o[0])||void 0===i?void 0:i.label;c&&(null===_||void 0===_||_(c,s,l))}),[_,z]),G=(0,i.useCallback)((()=>{null===w||void 0===w||w(),z(-1)}),[w,z]),V=(0,a.l)(j),W=(0,i.useMemo)((()=>(0,c.eY)(f)),[f]);(0,i.useEffect)((()=>{const e={...P,yaxis:{...P.yaxis,showticklabels:!0,automargin:!0}},t=T.map((e=>({...e,y:e.names})));null===Y||void 0===Y||Y((0,u.W)(t,e))}),[P,Y,T]);const $=(0,m.Y)("div",{css:[c.gz.chartWrapper(O),v.highlightStyles,""],className:x,ref:F,children:(0,m.Y)(s.W,{data:T,useResizeHandler:!N,css:c.gz.chart(O),onUpdate:b,layout:P,config:g,onHover:V,onUnhover:G,fallback:(0,m.Y)(h.Us,{})})});return(0,m.Y)(d.A,{labelData:W,children:$})})),v={highlightStyles:{".trace.bars g.point path":{transition:"var(--trace-transition)"},".trace.bars.is-highlight g.point path":{opacity:"var(--trace-opacity-dimmed-high) !important"},".trace.bars g.point.is-hover-highlight path":{opacity:"var(--trace-opacity-highlighted) !important"},".trace.bars g.point.is-selection-highlight path":{opacity:"var(--trace-opacity-highlighted) !important",stroke:"var(--trace-stroke-color)",strokeWidth:"var(--trace-stroke-width) !important"}}}},66117:function(e,t,n){"use strict";n.d(t,{a:function(){return g}});var r=n(32599),i=n(31014),o=n(79445),s=n(97604),a=n(25790),l=n(36118),c=n(23541),d=n(50684),u=n(6922),p=n(50111);const h={displaylogo:!1,scrollZoom:!1,modeBarButtonsToRemove:["toImage"]},m=[[0,"rgb(5,10,172)"],[.35,"rgb(40,60,190)"],[.5,"rgb(70,100,245)"],[.6,"rgb(90,120,245)"],[.7,"rgb(106,137,247)"],[1,"rgb(220,220,220)"]],g=i.memo((e=>{let{runsData:t,xAxis:n,yAxis:g,zAxis:f,markerSize:v=10,className:y,reverseScale:x,margin:C=l.AQ,colorScale:b=m,onUpdate:_,onHover:w,onUnhover:M,width:S,height:I,useDefaultHoverBox:E=!0,selectedRunUuid:R,onSetDownloadHandler:A}=e;const{theme:k}=(0,r.u)(),{layoutHeight:Y,layoutWidth:T,setContainerDiv:D,containerDiv:L,isDynamicSizeSupported:F}=(0,l.xN)(),K=(0,i.useMemo)((()=>{const e=[],r=[],i=[],o=[],s=[];for(const l of t){var a,c,d;const{metrics:t,params:u,color:p,uuid:h,displayName:m}=l,v="METRIC"===n.type?t:u,y="METRIC"===g.type?t:u,x="METRIC"===f.type?t:u,C=(null===v||void 0===v||null===(a=v[n.key])||void 0===a?void 0:a.value)||void 0,b=(null===y||void 0===y||null===(c=y[g.key])||void 0===c?void 0:c.value)||void 0,_=(null===x||void 0===x||null===(d=x[f.key])||void 0===d?void 0:d.value)||void 0;C&&b&&_&&(e.push(C),r.push(b),i.push(_),o.push(p||k.colors.primary),s.push([h,m||h,_]))}const u=[{x:e,y:r,customdata:s,text:t.map((e=>{let{displayName:t}=e;return t})),hovertemplate:E?(p=f.key,`<b>%{customdata[1]}:</b><br><b>%{xaxis.title.text}:</b> %{x:.2f}<br><b>%{yaxis.title.text}:</b> %{y:.2f}<br><b>${p}:</b> %{customdata[2]:.2f}<extra></extra>`):void 0,hoverinfo:E?void 0:"none",hoverlabel:E?l.rv:void 0,type:"scatter",mode:"markers",textposition:"bottom center",marker:{size:v,color:o,line:{color:"black",width:1}}}];var p;return t.length>1&&u.unshift({x:e,y:r,z:i,type:"contour",connectgaps:!0,hoverinfo:"none",contours:{coloring:"heatmap"},colorscale:b,reversescale:x,colorbar:{tickfont:{size:11,color:k.colors.textSecondary,family:""}}}),u}),[b,x,v,t,n.type,n.key,g.type,g.key,f.type,f.key,k.colors.primary,k.colors.textSecondary,E]),N=(0,i.useMemo)((()=>(0,l.y)(k)),[k]),[H,O]=(0,i.useState)({width:S||T,height:I||Y,margin:C,xaxis:{title:n.key,tickfont:{size:11,color:k.colors.textSecondary}},yaxis:{ticks:"inside",title:{standoff:32,text:g.key},tickfont:{size:11,color:k.colors.textSecondary}},template:{layout:N}});(0,i.useEffect)((()=>{O((e=>{const t={...e,width:S||T,height:I||Y,margin:C};return t.xaxis&&(t.xaxis.title=n.key),t.yaxis&&(t.yaxis.title={standoff:32,text:g.key}),t}))}),[T,Y,C,n.key,g.key,S,I]);const{setHoveredPointIndex:B}=(0,a.pO)(L,R,t,a.nD),P=(0,i.useCallback)((e=>{var t,n;let{points:r}=e;const i=r.find((e=>{let{curveNumber:t}=e;return 1===t}));if(B(null!==(t=null===i||void 0===i?void 0:i.pointIndex)&&void 0!==t?t:-1),!i)return;const o=null===i||void 0===i||null===(n=i.customdata)||void 0===n?void 0:n[0];o&&(null===w||void 0===w||w(o))}),[w,B]),U=(0,i.useCallback)((()=>{null===M||void 0===M||M(),B(-1)}),[M,B]),z=(0,s.l)(P),j=(0,i.useMemo)((()=>(0,l.eY)(t)),[t]);(0,i.useEffect)((()=>{const e=K.map((e=>({...e,mode:"text+markers"})));null===A||void 0===A||A((0,d.W)(e,H))}),[H,A,K]);const G=(0,p.Y)("div",{css:[l.gz.chartWrapper(k),l.gz.scatterChartHighlightStyles,""],className:y,ref:D,children:(0,p.Y)(o.W,{data:K,useResizeHandler:!F,css:l.gz.chart(k),onUpdate:_,layout:H,config:h,onHover:z,onUnhover:U,fallback:(0,p.Y)(u.Us,{})})});return(0,p.Y)(c.A,{labelData:j,children:G})}))},67245:function(e,t,n){"use strict";n.d(t,{B:function(){return a}});var r=n(31014),i=n(15579),o=n(76010),s=n(50111);class a extends r.Component{constructor(){super(...arguments),this.state={isSubmitting:!1},this.formRef=r.createRef(),this.onSubmit=async()=>{this.setState({isSubmitting:!0});try{const e=await this.formRef.current.validateFields();await this.props.handleSubmit(e),this.resetAndClearModalForm(),this.onRequestCloseHandler()}catch(e){this.handleSubmitFailure(e)}},this.resetAndClearModalForm=()=>{this.setState({isSubmitting:!1}),this.formRef.current.resetFields()},this.handleSubmitFailure=e=>{this.setState({isSubmitting:!1}),o.A.logErrorAndNotifyUser(e)},this.onRequestCloseHandler=()=>{this.resetAndClearModalForm(),this.props.onClose()},this.handleCancel=()=>{var e,t;this.onRequestCloseHandler(),null===(e=(t=this.props).onCancel)||void 0===e||e.call(t)}}render(){const{isSubmitting:e}=this.state,{okText:t,cancelText:n,isOpen:o,footer:a,children:l}=this.props,c=r.Children.map(l,(e=>r.isValidElement(e)?r.cloneElement(e,{innerRef:this.formRef}):e));return(0,s.Y)(i.d,{"data-testid":"mlflow-input-modal",className:this.props.className,title:this.props.title,width:540,visible:o,onOk:this.onSubmit,okText:t,cancelText:n,confirmLoading:e,onCancel:this.handleCancel,footer:a,centered:!0,children:c})}}},71932:function(e,t,n){"use strict";n.d(t,{GP:function(){return d},Mz:function(){return u},c_:function(){return p}});var r=n(31014),i=n(90765),o=n(62758),s=n(9133),a=n(50111);const l=(0,r.createContext)({draggedCardUuid:"",targetSection:"",isDragging:()=>!1}),c=(0,r.createContext)({setDraggedCardUuid:()=>{},setTargetSection:()=>{},setTargetPosition:()=>{},onDropChartCard:()=>{},onSwapCards:()=>{}}),d=()=>(0,r.useContext)(l),u=()=>(0,r.useContext)(c),p=e=>{let{children:t,visibleChartCards:n=[]}=e;const[d,u]=(0,r.useState)(null),[p,h]=(0,r.useState)(null),m=(0,r.useRef)(null),g=(0,r.useRef)(null);m.current=d,g.current=p;const f=(0,r.useRef)(null),v=(0,r.useCallback)((e=>{f.current=e}),[]),y=(0,r.useCallback)((()=>null!==m.current),[]),x=(0,o.g_)(),C=(0,r.useCallback)(((e,t)=>{x((n=>{var r;const i=null===(r=n.compareRunCharts)||void 0===r?void 0:r.slice();if(!i)return n;const o=i.findIndex((t=>t.uuid===e)),s=i.findIndex((e=>e.uuid===t));if(o<0||s<0)return n;const a=i[o];return i.splice(o,1),i.splice(s,0,a),{...n,compareRunCharts:i}}))}),[x]),b=(0,r.useCallback)((()=>{const e=m.current,t=g.current,r=f.current;null!==e&&null!==t&&null!==r&&(u(null),x((i=>{var o,a,l,c;const d=null===(o=i.compareRunCharts)||void 0===o?void 0:o.find((t=>t.uuid===e));if(!d)return i;const u=null===(a=i.compareRunSections)||void 0===a?void 0:a.find((e=>e.uuid===(null===d||void 0===d?void 0:d.metricSectionId))),p=null===(l=i.compareRunSections)||void 0===l?void 0:l.find((e=>e.uuid===t)),h=null===(c=i.compareRunCharts)||void 0===c?void 0:c.filter((e=>e.metricSectionId===(null===u||void 0===u?void 0:u.uuid)));if(u===p){var m;const t=null===(m=i.compareRunCharts)||void 0===m?void 0:m.slice(),o=null===h||void 0===h?void 0:h.filter((e=>n.includes(e)));if(!t||!o)return i;const a=t.findIndex((t=>t.uuid===e)),l=Math.min(r,o.length-1),c=(0,s.indexOf)(t,null===o||void 0===o?void 0:o[l]);return t&&-1!==a&&-1!==c&&(t.splice(a,1),t.splice(c,0,d)),{...i,compareRunCharts:t}}{var g,f;const e=null===(g=i.compareRunCharts)||void 0===g?void 0:g.filter((e=>e.metricSectionId===t&&!e.deleted));return e&&e.splice(r,0,d),{...i,compareRunCharts:(0,s.sortBy)(i.compareRunCharts,(t=>{var n;return null!==(n=null===e||void 0===e?void 0:e.indexOf(t))&&void 0!==n?n:-1})).map((e=>e.uuid===d.uuid?{...e,metricSectionId:t}:e)),compareRunSections:null===(f=i.compareRunSections)||void 0===f?void 0:f.map((e=>e.uuid===(null===u||void 0===u?void 0:u.uuid)||e.uuid===(null===p||void 0===p?void 0:p.uuid)?{...e,isReordered:!0}:e))}}})))}),[x,n]),_=(0,r.useMemo)((()=>({draggedCardUuid:d,targetSection:p,isDragging:y})),[d,p,y]),w=(0,r.useMemo)((()=>({setDraggedCardUuid:u,setTargetSection:h,setTargetPosition:v,onDropChartCard:b,onSwapCards:C})),[b,C,v]);return(0,a.Y)(i.G,{children:(0,a.Y)(l.Provider,{value:_,children:(0,a.Y)(c.Provider,{value:w,children:t})})})}},73150:function(e,t,n){"use strict";n.d(t,{X:function(){return x}});var r=n(9133),i=n(32599),o=n(48012),s=n(88443),a=n(93215),l=n(58481),c=n(24947),d=n(36118),u=n(75627),p=n(30152),h=n(59397),m=n(91144),g=n(35286),f=n(50111);const v=(e,t,n)=>{var i,o;const{metricKey:s,xAxisKey:a}=e,l=(null===n||void 0===n||null===(i=n.metricEntity)||void 0===i?void 0:i.key)||s,c=null!==(o=null===n||void 0===n?void 0:n.yValue)&&void 0!==o?o:null===t||void 0===t?void 0:t.metrics[l].value;if((0,r.isNil)(c))return null;const u=((e,t)=>{var n,r,i;return t===d.fj.METRIC?null!==(n=null===e||void 0===e?void 0:e.xValue)&&void 0!==n?n:"":(0,m.v$)()&&t===d.fj.TIME_RELATIVE?"number"===typeof(i=null!==(r=null===e||void 0===e?void 0:e.xValue)&&void 0!==r?r:"")?i:i.split(" ")[1]||"00:00:00":null===e||void 0===e?void 0:e.xValue})(n,a);return(0,f.FD)(f.FK,{children:[n&&(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:[n.label,":"]})," ",u]}),(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:[l,":"]})," ",c]})]})},y=e=>{let{activeRun:t,cardConfig:n,isHovering:r,hoverData:i}=e;return n.type===p.zL.BAR?((e,t)=>{var n,r;const{metricKey:i,dataAccessKey:o}=e,s=null!==o&&void 0!==o?o:i,a=null===t||void 0===t?void 0:t.metrics[s];if(!a)return null;const l=g.g[a.key],c=null!==(n=null===l||void 0===l?void 0:l.displayName)&&void 0!==n?n:a.key,d=null!==(r=null===l||void 0===l?void 0:l.valueFormatter({value:a.value}))&&void 0!==r?r:a.value;return(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:[c,":"]})," ",d]})})(n,t):n.type===p.zL.SCATTER?((e,t)=>{var n,r,i,o,s,a;const{xaxis:l,yaxis:c}=e,d=null!==(n=l.dataAccessKey)&&void 0!==n?n:l.key,u=null!==(r=l.dataAccessKey)&&void 0!==r?r:c.key,p=l.key,h=c.key,m="METRIC"===l.type?null===(i=t.metrics[d])||void 0===i?void 0:i.value:null===(o=t.params[d])||void 0===o?void 0:o.value,g="METRIC"===c.type?null===(s=t.metrics[u])||void 0===s?void 0:s.value:null===(a=t.params[u])||void 0===a?void 0:a.value;return(0,f.FD)(f.FK,{children:[m&&(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:["X (",p,"):"]})," ",m]}),g&&(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:["Y (",h,"):"]})," ",g]})]})})(n,t):n.type===p.zL.CONTOUR?((e,t)=>{var n,r,i,o,s,a;const{xaxis:l,yaxis:c,zaxis:d}=e,u=l.key,p=c.key,h=d.key,m="METRIC"===l.type?null===(n=t.metrics[u])||void 0===n?void 0:n.value:null===(r=t.params[u])||void 0===r?void 0:r.value,g="METRIC"===c.type?null===(i=t.metrics[p])||void 0===i?void 0:i.value:null===(o=t.params[p])||void 0===o?void 0:o.value,v="METRIC"===d.type?null===(s=t.metrics[h])||void 0===s?void 0:s.value:null===(a=t.params[h])||void 0===a?void 0:a.value;return(0,f.FD)(f.FK,{children:[m&&(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:["X (",u,"):"]})," ",m]}),g&&(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:["Y (",p,"):"]})," ",g]}),v&&(0,f.FD)("div",{css:C.value,children:[(0,f.FD)("strong",{children:["Z (",h,"):"]})," ",v]})]})})(n,t):n.type===p.zL.LINE?v(n,t,i):n.type===p.zL.PARALLEL?((e,t,n)=>{const{selectedParams:r,selectedMetrics:i}=e,o=r.map((e=>{const n=null===t||void 0===t?void 0:t.params[e];return!n||(0,f.FD)("div",{children:[(0,f.FD)("strong",{children:[n.key,":"]})," ",n.value]},e)})),s=i.map((e=>{const n=null===t||void 0===t?void 0:t.metrics[e];return!n||(0,f.FD)("div",{children:[(0,f.FD)("strong",{children:[n.key,":"]})," ",n.value]},e)}));return n?(0,f.FD)(f.FK,{children:[o.slice(0,3),(o.length>3||s.length>1)&&(0,f.Y)("div",{children:"..."}),s[s.length-1]]}):(0,f.FD)(f.FK,{children:[o,s]})})(n,t,r):null},x=e=>{var t;let{closeContextMenu:n,contextData:r,hoverData:d,chartData:p,runUuid:m,isHovering:g,mode:v}=e;const{runs:x,onTogglePin:b,onHideRun:_,getDataTraceLink:w}=r,[M]=(0,c.z)(),S=null===x||void 0===x?void 0:x.find((e=>e.uuid===m));if((0,u.bL)(d)&&v===u.QS.MultipleTracesWithScanline&&g)return(0,f.Y)(h.G,{hoverData:d});const I=(0,u.bL)(d)?d.hoveredDataPoint:d;if(!S)return null;const E=S.displayName||S.uuid,R=null!==I&&void 0!==I&&I.metricEntity?` (${I.metricEntity.key})`:"";return(0,f.FD)("div",{children:[(0,f.FD)("div",{css:C.contentWrapper,children:[(0,f.FD)("div",{css:C.header,children:[(0,f.Y)("div",{css:C.colorPill,style:{backgroundColor:S.color}}),S.groupParentInfo?(0,f.Y)(i.T.Text,{children:E+R}):(0,f.Y)(a.N_,{to:null!==(t=null===w||void 0===w?void 0:w(M,m))&&void 0!==t?t:l.h.getRunPageRoute(M,m),target:"_blank",css:C.runLink,onClick:n,children:E+R})]}),!g&&(0,f.Y)(i.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_runscomparetooltipbody.tsx_259",size:"small",onClick:n,icon:(0,f.Y)(i.C,{})})]}),(0,f.Y)(y,{isHovering:g,activeRun:S,cardConfig:p,hoverData:I}),(0,f.FD)("div",{css:C.actionsWrapper,children:[S.pinnable&&b&&(0,f.Y)(o.paO,{title:S.pinned?(0,f.Y)(s.A,{id:"ugm2f6",defaultMessage:"Unpin run"}):(0,f.Y)(s.A,{id:"58xhem",defaultMessage:"Click to pin the run"}),placement:"bottom",children:(0,f.Y)(i.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_runscomparetooltipbody.tsx_282",size:"small",onClick:()=>{b(m),n()},icon:S.pinned?(0,f.Y)(o.aS3,{}):(0,f.Y)(o.tsw,{})})}),_&&(0,f.Y)(o.paO,{title:(0,f.Y)(s.A,{id:"eE5VIF",defaultMessage:"Click to hide the run"}),placement:"bottom",children:(0,f.Y)(i.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-compare_runscomparetooltipbody.tsx_302","data-testid":"experiment-view-compare-runs-tooltip-visibility-button",size:"small",onClick:()=>{_(m),n()},icon:(0,f.Y)(o.kFX,{})})})]})]})},C={runLink:e=>({color:e.colors.primary,"&:hover":{}}),actionsWrapper:{marginTop:8,display:"flex",gap:8,alignItems:"center"},header:{display:"flex",gap:8,alignItems:"center"},value:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},contentWrapper:{display:"flex",gap:8,alignItems:"center",marginBottom:12,justifyContent:"space-between",height:24},colorPill:{width:12,height:12,borderRadius:"100%"}}},73549:function(e,t,n){"use strict";n.d(t,{h:function(){return l}});var r,i,o=n(31014);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(null,arguments)}function a(e,t){let{title:n,titleId:a,...l}=e;return o.createElement("svg",s({width:22,height:22,viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":a},l),n?o.createElement("title",{id:a},n):null,r||(r=o.createElement("path",{d:"M4.38977 15.717L8.79655 10.8113L12.5254 14.9622L19.644 7.03772",stroke:"#338ECC",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})),i||(i=o.createElement("path",{d:"M1 1V21H21",stroke:"#A3AEB8",strokeLinecap:"round"})))}const l=o.forwardRef(a);n.p},73724:function(e,t,n){"use strict";n.d(t,{h:function(){return k}});var r=n(32599),i=n(9133),o=n(31014),s=n(88464),a=n(79445),l=n(97604),c=n(25790),d=n(36118),u=n(82645),p=n(23541),h=n(91144),m=n(89555),g=n(50111);const f=".main-svg .draglayer .nsewdrag",v=e=>{var t;const n=(0,i.isString)(e)&&(null===(t=e.match(/[0-9]{2}:[0-9]{2}:[0-9]{2}\.([0-9]+)/))||void 0===t?void 0:t[1]);return n?1e3*Number(`0.${n}`)%1:0};var y=n(50684),x=n(25866),C=n(30152),b=n(49698);const _=(e,t)=>t===d.fj.STEP?e.step:t===d.fj.TIME||t===d.fj.TIME_RELATIVE?e.timestamp:null;var w=n(6922);const M=e=>{var t,n,r,o,s;let{runEntry:a,metricKey:l,xAxisKey:c,selectedXAxisMetricKey:p,useDefaultHoverBox:h,lineSmoothness:m,lineShape:g,lineDash:f,displayPoints:v,displayOriginalLine:y,xAxisScaleType:x,expression:C,evaluateExpression:b}=e;if(!a.metricsHistory)return{};let w,M,I=[];if(C&&b)I=(e=>{let{expression:t,runEntry:n,evaluateExpression:r,xAxisKey:o}=e;const s=t.variables.map((e=>{var t;const r=null===(t=n.metricsHistory)||void 0===t?void 0:t[e];return void 0!==r?r.flatMap((e=>{const t=_(e,o);return null!==t?[t]:[]})):[]})),a=(0,i.intersection)(...s);return a.sort(((e,t)=>e-t)),a.flatMap((e=>{const i=t.variables.reduce(((t,r)=>{var i,s;const a=null===(i=n.metricsHistory)||void 0===i?void 0:i[r],l=null===a||void 0===a||null===(s=a.find((t=>_(t,o)===e)))||void 0===s?void 0:s.value;return void 0!==l&&(t[r]=l),t}),{}),s=r(t,i);return void 0!==s?[{value:s,key:t.expression,...o===d.fj.STEP||o===d.fj.METRIC?{step:e,timestamp:0}:{timestamp:e,step:0}}]:[]}))})({expression:C,runEntry:a,evaluateExpression:b,xAxisKey:c});else{if(void 0===l)return{};var k;I=null===(k=a.metricsHistory[l])||void 0===k?void 0:k.sort(((e,t)=>c===d.fj.STEP?e.step-t.step:e.timestamp-t.timestamp))}if(c===d.fj.METRIC){var Y,T;const e=new Set((null!==(Y=I)&&void 0!==Y?Y:[]).map((e=>{let{step:t}=e;return t}))),t=A(e,a.metricsHistory[p]),n=t.map((e=>{let{step:t}=e;return t})),r=new Set(n),i=S(null!==(T=I)&&void 0!==T?T:[],n).filter((e=>{let{step:t}=e;return r.has(t)}));w=t.map((e=>{let{value:t}=e;return t})),M=i.map((e=>{let{value:t}=e;return(0,d.jn)(t)}))}else{var D;if(w=R(I,c),M=null===(D=I)||void 0===D?void 0:D.map((e=>{let{value:t}=e;return(0,d.jn)(t)})),"log"===x){const e=w.findIndex((e=>e>0));-1!==e&&(w=w.slice(e),M=M.slice(e))}}const L=(0,d.fH)(w)?"linear":g,F=!y&&(null!==v&&void 0!==v?v:w.length<60),K=1===(null===(t=M)||void 0===t?void 0:t.length),N=y?"skip":h?void 0:"none";return{uuid:a.uuid,name:(null===(n=a.runInfo)||void 0===n?void 0:n.runName)||"",x:w,y:(0,u.mV)(null!==(r=M)&&void 0!==r?r:[],y?0:m),metricHistory:I,metricKey:l||(null===C||void 0===C?void 0:C.expression),hovertext:(null===(o=a.runInfo)||void 0===o?void 0:o.runName)||"",text:"x",textposition:"outside",textfont:{size:11},mode:K||F?"lines+markers":"lines",hovertemplate:h?E((null===(s=a.runInfo)||void 0===s?void 0:s.runName)||""):void 0,hoverinfo:N,hoverlabel:h?d.rv:void 0,type:"scatter",line:{dash:f,shape:L},marker:{color:y?(0,d.FI)(a.color,.15):a.color}}},S=(e,t)=>{const n=t.reduce(((e,t,n)=>(e[t]=n,e)),{});return e.slice().sort(((e,t)=>{var r,i;return(null!==(r=n[e.step])&&void 0!==r?r:1/0)-(null!==(i=n[t.step])&&void 0!==i?i:1/0)}))},I={displaylogo:!1,doubleClick:"autosize",scrollZoom:!1,modeBarButtonsToRemove:["toImage"]},E=e=>`<b>${e}</b>:<br><b>%{xaxis.title.text}:</b> %{x}<br><b>%{yaxis.title.text}:</b> %{y:.2f}<br><extra></extra>`,R=(e,t)=>{if(!e)return[];if(t===d.fj.TIME_RELATIVE){const{timestamp:t}=(0,i.minBy)(e,"timestamp")||{};return t?(0,h.v$)()?e.map((e=>{let{timestamp:n}=e;return n-t+x.$V})):e.map((e=>{let{timestamp:n}=e;return(n-t)/1e3})):e.map((e=>{let{step:t}=e;return t}))}if((0,h.v$)()&&t===d.fj.TIME_RELATIVE_HOURS){const{timestamp:t}=(0,i.minBy)(e,"timestamp")||{};if(t)return e.map((e=>{let{timestamp:n}=e;return(n-t)/x.uv}))}else if(t===d.fj.TIME)return e.map((e=>{let{timestamp:t}=e;return t}));return e.map((e=>{let{step:t}=e;return t}))},A=(e,t)=>t?t.filter((t=>e.has(t.step))).map((e=>({value:(0,d.jn)(e.value),step:e.step}))).sort(((e,t)=>Number(e.value)-Number(t.value))):[],k=o.memo((e=>{let{runsData:t,metricKey:n,selectedMetricKeys:u,scaleType:_="linear",xAxisScaleType:E="linear",xAxisKey:k=d.fj.STEP,yAxisKey:T=C.Kb.METRIC,yAxisExpressions:D=[],selectedXAxisMetricKey:L="",lineSmoothness:F=70,className:K,margin:N=d.am,lineShape:H="linear",onUpdate:O,onHover:B,onUnhover:P,width:U,height:z,useDefaultHoverBox:j=!0,selectedRunUuid:G,xRange:V,yRange:W,lockXAxisZoom:$,fullScreen:q,displayPoints:X,onSetDownloadHandler:Q,positionInSection:J=0}=e;const{theme:Z}=(0,r.u)(),{evaluateExpression:ee}=(0,b.l)(),te=(0,o.useMemo)((()=>{let e=k;if((0,h.v$)()&&k===d.fj.TIME_RELATIVE){const r=u||[n];let o=0;t.forEach((e=>{const t=e.metricsHistory;t&&r.forEach((e=>{if(t[e]){const{timestamp:n}=(0,i.minBy)(t[e],"timestamp")||{},{timestamp:r}=(0,i.maxBy)(t[e],"timestamp")||{};if(r&&n){const e=r-n;o=Math.max(o,e)}}}))})),o>=x.B5&&(e=d.fj.TIME_RELATIVE_HOURS)}return e}),[t,u,n,k]),ne=e=>{const t=M(e);if((0,h.gF)()){const n={...e,lineSmoothness:0,useDefaultHoverBox:!1,displayPoints:!1,displayOriginalLine:!0};return[t,M(n)]}return[t]},re=(0,o.useMemo)((()=>{const e=null!==u&&void 0!==u?u:[n];return t.map((t=>(0,h.Iq)()&&k!==d.fj.METRIC&&T===C.Kb.EXPRESSION?D.flatMap(((e,n)=>ne({runEntry:t,expression:e,xAxisKey:te,selectedXAxisMetricKey:L,useDefaultHoverBox:j,lineSmoothness:F,lineShape:H,lineDash:d.J2[n%d.J2.length],displayPoints:X,xAxisScaleType:E,evaluateExpression:ee}))):e.filter((e=>{var n;return!(0,i.isEmpty)(null===(n=t.metricsHistory)||void 0===n?void 0:n[e])})).flatMap(((e,n)=>ne({runEntry:t,metricKey:e,xAxisKey:te,selectedXAxisMetricKey:L,useDefaultHoverBox:j,lineSmoothness:F,lineShape:H,lineDash:d.J2[n%d.J2.length],displayPoints:X,xAxisScaleType:E}))))).flat()}),[t,H,te,F,n,j,u,L,X,E,T,D,ee,k]),ie=(0,o.useMemo)((()=>{const e=null!==u&&void 0!==u?u:[n];return t.filter((e=>{let{groupParentInfo:t}=e;return t})).flatMap((t=>e.map((e=>(e=>{var t;let{runEntry:n,metricKey:r,lineShape:o,xAxisKey:s,selectedXAxisMetricKey:a,xAxisScaleType:l}=e;if(!n.aggregatedMetricsHistory)return{};const{max:c,min:u}=n.aggregatedMetricsHistory[r];let p,h,m,g;if(s===d.fj.METRIC){if(!n.metricsHistory)return{};const e=new Set(c.map((e=>{let{step:t}=e;return t}))),t=A(e,n.metricsHistory[a]),r=t.map((e=>e.step)),i=new Set(r),o=t.map((e=>e.value));m=S(u,r).filter((e=>{let{step:t}=e;return i.has(t)})).map((e=>{let{value:t}=e;return(0,d.jn)(t)})).reverse(),g=S(c,r).filter((e=>{let{step:t}=e;return i.has(t)})).map((e=>{let{value:t}=e;return(0,d.jn)(t)})),p=o.slice().reverse(),h=o}else{const e=u.slice().reverse();if(p=R(e,s),h=R(c,s),m=e.map((e=>{let{value:t}=e;return(0,d.jn)(t)})),g=c.map((e=>{let{value:t}=e;return(0,d.jn)(t)})),"log"===l){const e=h.findIndex((e=>(0,i.isNumber)(e)&&e>0)),t=p.length-p.findIndex((e=>(0,i.isNumber)(e)&&e>0));-1!==e&&-1!==t&&(h=h.slice(e),g=g.slice(e),p=p.slice(0,t-1),m=m.slice(0,t-1))}}const f=[...p,null,...h],v=[...m,null,...g];return{name:(null===(t=n.runInfo)||void 0===t?void 0:t.runName)||"",x:f,y:v,fillcolor:(0,d.FI)(n.color,.2),hovertemplate:void 0,hoverlabel:void 0,hoverinfo:"skip",line:{color:"transparent",shape:o},fill:"tozeroy",type:"scatter"}})({runEntry:t,metricKey:e,lineShape:H,xAxisKey:te,selectedXAxisMetricKey:L,xAxisScaleType:E})))))}),[H,n,t,u,te,L,E]),oe=(0,o.useMemo)((()=>[...ie,...re]),[re,ie]),{layoutHeight:se,layoutWidth:ae,setContainerDiv:le,containerDiv:ce,isDynamicSizeSupported:de}=(0,d.xN)(),{formatMessage:ue}=(0,s.A)(),{setHoveredPointIndex:pe}=(0,c.pO)(ce,G,oe,c.PQ,ie.length),he=((e,t,n)=>e===d.fj.TIME||(0,h.v$)()&&n===d.fj.TIME_RELATIVE?"date":e===d.fj.STEP&&"log"===t?"log":"linear")(k,E,te),me=(0,o.useMemo)((()=>te===d.fj.METRIC?L:ue((0,d.fw)(te))),[ue,te,L]),ge=(0,o.useMemo)((()=>({tickfont:{size:11,color:Z.colors.textSecondary},type:"log"===_?"log":"linear",fixedrange:$,range:W,autorange:void 0===W,tickformat:"f"})),[_,$,Z,W]),fe=(0,o.useMemo)((()=>({title:me,tickfont:{size:11,color:Z.colors.textSecondary},range:V,autorange:void 0===V,type:he})),[Z,me,V,he]),[ve,ye]=(0,o.useState)({width:U||ae,height:z||se,margin:N,xaxis:fe,yaxis:ge,showlegend:!1});(0,o.useEffect)((()=>{ye((e=>{const t={...e,width:U||ae,height:z||se,margin:N,yaxis:ge,xaxis:{...e.xaxis,...fe},showlegend:!1};return(0,i.isEqual)(t,e)?e:t}))}),[ae,se,N,fe,ge,U,z,me]);const xe=(0,o.useMemo)((()=>((null===u||void 0===u?void 0:u.length)||0)>1),[u]),Ce=(0,o.useCallback)((()=>{null===P||void 0===P||P(),pe(-1)}),[P,pe]),be=(0,o.useMemo)((()=>(0,d.y)(Z)),[Z]),_e=ve;_e.xaxis&&(_e.xaxis.title=me,_e.xaxis.type=he,V&&(_e.xaxis.range=V),_e.xaxis.automargin=!0,_e.xaxis.tickformat=(0,h.v$)()&&te===d.fj.TIME_RELATIVE?"%H:%M:%S":void 0),_e.template={layout:be},_e.yaxis&&W&&(_e.yaxis.range=W,_e.yaxis.automargin=!0,_e.yaxis.tickformat="f");const we=(0,o.useMemo)((()=>(0,d.Ct)(t,u,n,T,D)),[t,u,n,T,D]),{scanlineElement:Me,initHandler:Se,updateHandler:Ie,onPointHover:Ee,onPointUnhover:Re}=(e=>{let{onUnhover:t,onHover:n,runsData:r,plotData:s,legendLabelData:a,containsMultipleMetricKeys:l,xAxisKeyLabel:c,xAxisKey:d,disabled:u=!1,setHoveredPointIndex:p,xAxisScaleType:h="linear",positionInSection:y=0}=e;const x=(0,o.useRef)({containerLeftPixels:0,plotWidthPixels:0,plotOffsetPixels:0,lowerBoundValue:0,valueRange:0,mainContainer:null,dragLayer:null,initialized:!1}),C=(0,o.useRef)(null),b=(0,o.useRef)(void 0),_=(0,o.useMemo)((()=>(0,i.uniq)(s.map((e=>{let{x:t}=e;return t})).flat())),[s]),[w,M]=(0,o.useState)(null),S=(0,o.useRef)(void 0),I=(0,o.useRef)(a),E=(0,o.useRef)(r),R=(0,o.useRef)(s),A=(0,o.useRef)(_),k=(0,o.useRef)(w);I.current=a,E.current=r,R.current=s,A.current=_,k.current=w;const Y=(0,o.useCallback)((e=>{var t,n,r,o,s,a;const l=null===(t=e.layout.xaxis)||void 0===t||null===(n=t.range)||void 0===n?void 0:n[0],c=null===(r=e.layout.xaxis)||void 0===r||null===(o=r.range)||void 0===o?void 0:o[1];let d=(0,i.isNumber)(l)?l:new Date(null!==l&&void 0!==l?l:0).getTime(),u=(0,i.isNumber)(c)?c:new Date(null!==c&&void 0!==c?c:0).getTime();d+=v(l),u+=v(c),x.current.lowerBoundValue=null!==(s=d)&&void 0!==s?s:0,x.current.valueRange=(null!==(a=u)&&void 0!==a?a:0)-x.current.lowerBoundValue}),[]),T=(0,o.useCallback)((()=>{const{mainContainer:e,dragLayer:t}=x.current;if(e&&t){const n=e.getBoundingClientRect(),r=t.getBoundingClientRect();x.current.containerLeftPixels=n.x,x.current.plotWidthPixels=r.width,x.current.plotOffsetPixels=r.x-n.x,x.current.initialized=r.width>0}}),[]),D=(0,o.useCallback)(((e,t)=>{Y(e);const n=t.querySelector(".main-svg"),r=t.querySelector(f);x.current.mainContainer=n,x.current.dragLayer=r,T()}),[Y,T]),L=(0,o.useCallback)(((e,t)=>{M({figure:e,graphDiv:t}),D(e,t)}),[D]);(0,o.useEffect)((()=>{requestAnimationFrame((()=>{k.current&&D(k.current.figure,k.current.graphDiv)}))}),[y,D]);const F=(0,o.useCallback)((e=>{e.relatedTarget instanceof Element&&e.relatedTarget.classList.contains("dragcover")||(null===t||void 0===t||t(),C.current&&(C.current.style.display="none"))}),[t]),K=(0,o.useCallback)((e=>{var t,n;let{points:r}=e;const i=r[0],o=null===i||void 0===i?void 0:i.data;if(p(null!==(t=null===i||void 0===i?void 0:i.curveNumber)&&void 0!==t?t:-1),!o)return;const s=null===(n=o.metricHistory)||void 0===n?void 0:n[i.pointIndex],a={xValue:i.x,yValue:i.y,metricEntity:s,index:i.pointIndex,label:c,traceUuid:o.uuid};b.current=a}),[c,p]),N=(0,o.useCallback)((()=>{b.current=void 0,p(-1)}),[p]),H=(0,o.useCallback)((e=>{const t=x.current,n=(e-t.plotOffsetPixels-t.containerLeftPixels)/t.plotWidthPixels;let r=t.lowerBoundValue+t.valueRange*n;return"log"===h&&(r=10**r),A.current.reduce(((e,t)=>Math.abs(t-r)<Math.abs(e-r)?t:e),A.current[0])}),[h]);(0,o.useEffect)((()=>{if(u)return;if(!w)return;Y(w.figure);const e=w.graphDiv.querySelector(f),t=(0,i.throttle)((e=>{x.current.initialized||T();const t=H(e.clientX),n=I.current.map((e=>{var n,r;const o=E.current.find((t=>{let{uuid:n}=t;return n===e.uuid})),s=R.current.find((t=>{let{uuid:n,metricKey:r}=t;return n===e.uuid&&e.metricKey===r}));if(!s)return;const a=l?e.label:null===o||void 0===o?void 0:o.displayName,c=null===(n=s.x)||void 0===n?void 0:n.indexOf(t);if((0,i.isUndefined)(c)||-1===c)return;const d=null===(r=s.y)||void 0===r?void 0:r[c];return{displayName:a||"",value:(0,i.isNumber)(d)?d:void 0,color:null===e||void 0===e?void 0:e.color,dashStyle:null===e||void 0===e?void 0:e.dashStyle,uuid:`${e.uuid}.${e.metricKey}`}}));S.current={tooltipLegendItems:(0,i.orderBy)((0,i.compact)(n),"value","desc"),hoveredDataPoint:null===b||void 0===b?void 0:b.current,xValue:t,xAxisKey:d,xAxisKeyLabel:c}}),50,{leading:!0}),r=(0,i.throttle)(T,50),o=e=>{var t,r;if(!S.current)return;const i=x.current,o=H(e.clientX),s=(("log"===h?Math.log10(o):o)-i.lowerBoundValue)/i.valueRange*i.plotWidthPixels;C.current&&(C.current.style.display="block",C.current.style.left=`${i.plotOffsetPixels+s}px`),null===n||void 0===n||n((null===(t=S.current)||void 0===t||null===(r=t.hoveredDataPoint)||void 0===r?void 0:r.traceUuid)||"",{x:i.containerLeftPixels+i.plotOffsetPixels+s,y:e.clientY,originalEvent:e},S.current)};return e?(e.addEventListener("pointermove",t),e.addEventListener("pointermove",o),window.addEventListener("resize",r),e.addEventListener("pointerleave",F),()=>{e.removeEventListener("pointermove",t),e.removeEventListener("pointermove",o),e.removeEventListener("pointerleave",F),window.removeEventListener("resize",r)}):()=>{}}),[F,w,Y,n,l,b,u,d,c,h,H,T]);const O=u?null:(0,g.Y)("div",{css:(0,m.AH)({top:0,width:0,borderLeft:"1px dashed rgba(0,0,0,0.5)",height:"100%",position:"absolute",pointerEvents:"none"},""),ref:C,style:{display:"none"}});return{updateHandler:D,initHandler:L,scanlineElement:O,onPointHover:K,onPointUnhover:N}})({legendLabelData:we,plotData:re,runsData:t,containsMultipleMetricKeys:xe,onHover:B,onUnhover:Ce,xAxisKeyLabel:me,xAxisKey:te,xAxisScaleType:k===d.fj.STEP?E:"linear",setHoveredPointIndex:pe,positionInSection:J}),Ae=(0,l.l)(Ee);(0,o.useEffect)((()=>{const e=((null===u||void 0===u?void 0:u.length)||0)>1?oe.map((e=>e.metricKey?{...e,name:`${e.name} (${e.metricKey})`}:e)):oe,t={...ve,showlegend:!0,legend:{orientation:"h"}};null===Q||void 0===Q||Q((0,y.W)(e,t))}),[ve,Q,oe,null===u||void 0===u?void 0:u.length]);const ke=(0,g.FD)("div",{css:[d.gz.chartWrapper(Z),Y.highlightStyles,""],className:K,ref:le,children:[(0,g.Y)(a.W,{data:oe,useResizeHandler:!de,css:d.gz.chart(Z),onUpdate:(e,t)=>{Ie(e,t),null===O||void 0===O||O(e,t)},layout:_e,config:I,onHover:Ae,onUnhover:Re,onInitialized:Se,fallback:(0,g.Y)(w.Us,{})}),Me]});return(0,g.Y)(p.A,{labelData:we,fullScreen:q,children:ke})})),Y={highlightStyles:{".scatterlayer g.trace":{transition:"var(--trace-transition)"},".scatterlayer.is-highlight g.trace:not(.is-band)":{opacity:"var(--trace-opacity-dimmed-low) !important"},".scatterlayer g.trace.is-hover-highlight:not(.is-band)":{opacity:"var(--trace-opacity-highlighted) !important"},".scatterlayer g.trace.is-selection-highlight:not(.is-band)":{opacity:"var(--trace-opacity-highlighted) !important"},".scatterlayer g.trace.is-selection-highlight path.point":{stroke:"var(--trace-stroke-color)",strokeWidth:"var(--trace-stroke-width) !important"},".scatterlayer.is-highlight g.trace.is-band:not(.is-band-highlighted)":{opacity:"var(--trace-opacity-dimmed) !important"}}}},74035:function(e,t,n){"use strict";n.d(t,{h:function(){return h}});var r,i,o,s,a,l,c,d=n(31014);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(null,arguments)}function p(e,t){let{title:n,titleId:p,...h}=e;return d.createElement("svg",u({width:18,height:16,viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":p},h),n?d.createElement("title",{id:p},n):null,r||(r=d.createElement("path",{d:"M1 0.813599V15.1865H17",stroke:"#A3AEB8",strokeLinecap:"round"})),i||(i=d.createElement("circle",{cx:3.98301,cy:12.4746,r:1.35593,fill:"#338ECC"})),o||(o=d.createElement("ellipse",{cx:9.40684,cy:10.8475,rx:1.35593,ry:1.35593,fill:"#338ECC"})),s||(s=d.createElement("circle",{cx:5.6102,cy:8.1356,r:1.35593,fill:"#338ECC",fillOpacity:.25})),a||(a=d.createElement("circle",{cx:9.40684,cy:6.50852,r:1.35593,fill:"#338ECC"})),l||(l=d.createElement("ellipse",{cx:13.7457,cy:5.96616,rx:1.35593,ry:1.35593,fill:"#338ECC",fillOpacity:.25})),c||(c=d.createElement("ellipse",{cx:16.4576,cy:2.46128,rx:1.35593,ry:1.35593,fill:"#338ECC"})))}const h=d.forwardRef(p);n.p},75145:function(e,t,n){"use strict";n.d(t,{u:function(){return d}});var r=n(89555),i=n(32599),o=n(48012),s=n(21879),a=n(31014),l=n(88443),c=n(50111);const d=()=>{const e=(0,s.e)(),{theme:t}=(0,i.u)(),n=(0,a.useCallback)((()=>{e((e=>({...e,hideEmptyCharts:!0})))}),[e]);return(0,c.Y)("div",{css:(0,r.AH)({flex:1,display:"flex",alignItems:"center",justifyContent:"center",paddingLeft:t.spacing.lg,paddingRight:t.spacing.lg},""),children:(0,c.Y)(o.SvL,{description:(0,c.Y)(l.A,{id:"VAUHXW",defaultMessage:"No chart data available for the currently visible runs. Select other runs or <link>hide empty charts.</link>",values:{link:e=>(0,c.Y)(i.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_runs-charts_components_runschartsnodatafoundindicator.tsx_31",onClick:n,children:e})}}),image:(0,c.Y)(o.xfv,{})})})}},75627:function(e,t,n){"use strict";n.d(t,{QS:function(){return a},W:function(){return h},bL:function(){return d},uN:function(){return m}});var r=n(31014),i=n(25790),o=n(21039),s=n(50111);let a=function(e){return e[e.Simple=1]="Simple",e[e.MultipleTracesWithScanline=2]="MultipleTracesWithScanline",e}({});const l=r.createContext(null);let c=function(e){return e[e.HIDDEN=0]="HIDDEN",e[e.HOVER=1]="HOVER",e[e.VISIBLE=2]="VISIBLE",e}({});const d=e=>e&&"tooltipLegendItems"in e,u=e=>{if(null===e||!(e instanceof Element))return null;if(e instanceof HTMLElement)return e;let t=e;for(;t&&!(t instanceof HTMLElement);)t=t.parentElement;return t};var p={name:"13udsys",styles:"height:100%"};const h=e=>{let{className:t,children:n,contextData:o,component:d,hoverOnly:h=!1}=e;const m=(0,r.useRef)(null),f=(0,r.useRef)(null),v=(0,r.useRef)({x:0,y:0}),y=(0,r.useRef)({x:0,y:0}),[x,C]=(0,r.useState)(a.Simple),[b,_]=(0,r.useState)(c.HIDDEN),[w,M]=(0,r.useState)(null),[S,I]=(0,r.useState)(""),[E,R]=(0,r.useState)(null),A=(0,r.useRef)(null),k=(0,r.useRef)(b),Y=(0,r.useRef)(S),T=(0,r.useRef)(w),D=(0,r.useRef)(E),{highlightDataTrace:L}=(0,i.ZY)(),F=(0,r.useCallback)((function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!f.current||!m.current)return;let t=x===a.MultipleTracesWithScanline?y.current.x:v.current.x,n=v.current.y;const r=f.current,i=m.current.getBoundingClientRect();x===a.MultipleTracesWithScanline&&(t-=i.x,n-=i.y),f.current.style.left="0px",f.current.style.top="0px",f.current.style.transform=`translate3d(${t+1}px, ${n+1}px, 0)`;const o=()=>{const e=r.getBoundingClientRect();t+e.width>=i.width&&(t-=e.width),n+e.height>=i.height&&(n-=e.height),r.style.transform=`translate3d(${t+1}px, ${n+1}px, 0)`};e?requestAnimationFrame(o):o()}),[x]);(0,r.useEffect)((()=>{k.current=b}),[b]);const K=(0,r.useCallback)(((e,t)=>{var n;const r=e.getBoundingClientRect(),i=(null===(n=m.current)||void 0===n?void 0:n.getBoundingClientRect())||{left:0,top:0};return{x:t.offsetX+(r.left-i.left),y:t.offsetY+(r.top-i.top)}}),[]),N=(0,r.useCallback)((e=>{if(k.current===c.HOVER&&f.current&&m.current){A.current=null;const t=u(e.target);t&&(v.current=K(t,e.nativeEvent),F())}}),[F,K]),H=(0,r.useCallback)(((e,t,n,r,i)=>{var o;Y.current=e,T.current=n,D.current=i,k.current!==c.VISIBLE&&(M(n),!e&&null!==(o=A.current)&&void 0!==o&&o.runUuid||(t===a.MultipleTracesWithScanline&&(y.current.x=(null===r||void 0===r?void 0:r.x)||0),C(t),I((t=>(i&&R(i),k.current===c.HIDDEN||k.current===c.HOVER&&e!==t?(_(c.HOVER),e):t)))))}),[]),O=(0,r.useCallback)((e=>{h||0===e.button&&Y.current&&(A.current={x:e.pageX,y:e.pageY,runUuid:Y.current})}),[h]),B=(0,r.useCallback)((e=>{if(h)return;if(A.current&&(()=>{var t;return(null===(t=A.current)||void 0===t?void 0:t.runUuid)&&Math.abs(e.pageX-A.current.x)<5&&Math.abs(e.pageY-A.current.y)<5})()){if(k.current===c.VISIBLE){I(A.current.runUuid),R(D.current);const t=u(e.nativeEvent.target);t&&(v.current=K(t,e.nativeEvent),F(!0))}else _(c.VISIBLE),F(!0);e.stopPropagation()}A.current=null}),[F,h,K]),P=(0,r.useCallback)((()=>_(c.HIDDEN)),[]);(0,r.useLayoutEffect)((()=>{if(!m.current)return;const e=m.current.getRootNode(),t=e=>{var t;if(k.current!==c.VISIBLE)return;const n=u(e.target);if(!n)return;n instanceof HTMLElement&&(null===f||void 0===f?void 0:f.current)instanceof HTMLElement&&f.current.contains(n)||null!==(t=A.current)&&void 0!==t&&t.runUuid||_(c.HIDDEN)};return e.addEventListener("click",t,{capture:!0}),()=>{e.removeEventListener("click",t,{capture:!0})}}),[K,F]);const U=(0,r.useCallback)((()=>{var e;Y.current="",null!==(e=A.current)&&void 0!==e&&e.runUuid||k.current===c.VISIBLE||(I(""),_(c.HIDDEN))}),[]),z=(0,r.useCallback)((function(){Y.current="",I(""),_(c.HIDDEN)}),[]),j=(0,r.useMemo)((()=>b!==c.VISIBLE?null:S),[b,S]);(0,r.useEffect)((()=>L(j,{source:i.WF.CHART,shouldBlock:Boolean(j)})),[L,j]);const G=(0,r.useMemo)((()=>({updateTooltip:H,resetTooltip:U,destroyTooltip:z,selectedRunUuid:j,closeContextMenu:P})),[H,U,z,j,P]),V=b!==c.HIDDEN&&(x===a.MultipleTracesWithScanline||""!==S);return(0,s.FD)(l.Provider,{value:G,children:[(0,s.Y)("div",{onMouseMove:N,onMouseDownCapture:O,onClickCapture:B,css:p,children:n}),(0,s.Y)("div",{css:g.contextMenuContainer,className:t,ref:m,children:V&&(0,s.Y)("div",{ref:f,css:g.contextMenuWrapper,"data-testid":"tooltip-container",style:{userSelect:b===c.HOVER?"none":"unset",pointerEvents:b===c.HOVER?"none":"all"},children:(0,s.Y)(d,{runUuid:S,hoverData:E,chartData:w,contextData:o,isHovering:b===c.HOVER,closeContextMenu:P,mode:x})})})]})},m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.Simple;const n=(0,r.useContext)(l);if(!n)throw new Error("You must invoke useRunsChartsTooltip() in a component being ancestor of <RunsChartsTooltipWrapper />!");const{updateTooltip:o,resetTooltip:s,selectedRunUuid:c,closeContextMenu:d,destroyTooltip:u}=n,{highlightDataTrace:p}=(0,i.ZY)(),h=(0,r.useCallback)((function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";o(n,t,e,arguments.length>1?arguments[1]:void 0,arguments.length>2?arguments[2]:void 0),p(n,{source:i.WF.CHART})}),[o,e,t,p]);return{setTooltip:h,resetTooltip:(0,r.useCallback)((()=>{s(),p(null)}),[s,p]),selectedRunUuid:c,closeContextMenu:d,destroyTooltip:u}},g={contextMenuContainer:{overflow:"hidden",top:0,left:0,width:"100%",height:"100%",position:"fixed",pointerEvents:"none",zIndex:o.K.TOOLTIP_CONTAINER},contextMenuWrapper:e=>({zIndex:o.K.TOOLTIP,position:"absolute",padding:e.spacing.sm,backgroundColor:e.colors.backgroundPrimary,border:`1px solid ${e.colors.border}`,left:-999,top:-999,borderRadius:e.general.borderRadiusBase,boxShadow:e.general.shadowLow}),overlayElement:()=>({"&::after":{content:'""',position:"absolute",left:0,top:0,right:0,bottom:0,backgroundColor:"transparent"}})}},76758:function(e,t,n){"use strict";n.d(t,{F:function(){return d}});var r=n(89555),i=n(32599),o=n(48012),s=n(41028),a=n(50111);const l=()=>{const{theme:e}=(0,i.u)();return(0,a.Y)(o.qhh,{css:(0,r.AH)({color:e.colors.textValidationDanger},"")})},c=()=>{const{theme:e}=(0,i.u)();return(0,a.Y)(o.C1y,{css:(0,r.AH)({color:e.colors.textValidationSuccess},"")})},d=e=>{let{status:t}=e;switch(t){case"FAILED":case"KILLED":return(0,a.Y)(l,{});case"FINISHED":return(0,a.Y)(c,{});case"SCHEDULED":case"RUNNING":return(0,a.Y)(s.C,{});default:return null}}},77198:function(e,t,n){"use strict";n.d(t,{h:function(){return u}});var r,i,o,s,a,l=n(31014);function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(null,arguments)}function d(e,t){let{title:n,titleId:d,...u}=e;return l.createElement("svg",c({width:18,height:16,viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":d},u),n?l.createElement("title",{id:d},n):null,r||(r=l.createElement("path",{d:"M1 0.813599V15.1865H17",stroke:"#A3AEB8",strokeLinecap:"round"})),i||(i=l.createElement("rect",{x:3.16943,y:10.5763,width:2.16949,height:3.25424,fill:"#338ECC"})),o||(o=l.createElement("rect",{x:6.42371,y:5.15259,width:2.16949,height:8.67797,fill:"#338ECC"})),s||(s=l.createElement("rect",{x:9.67798,y:8.40686,width:2.16949,height:5.42373,fill:"#338ECC"})),a||(a=l.createElement("rect",{x:12.9323,y:1.89832,width:2.16949,height:11.9322,fill:"#338ECC"})))}const u=l.forwardRef(d);n.p},77199:function(){},81850:function(e,t,n){"use strict";n.d(t,{B:function(){return c}});var r=n(31014),i=n(4877),o=n.n(i),s=n(36118),a=n(62862),l=n(9133);const c=e=>{let{ungroupedRunsData:t,enabled:n,aggregateFunction:i,metricKeys:c,sampledDataResultsByRunUuid:d,selectedXAxisMetricKey:u,ignoreOutliers:p}=e;return(0,r.useMemo)((()=>{if(!n||!i)return t;return[...t.filter((e=>{let{groupParentInfo:t}=e;return t})).map((e=>{const t={};c.forEach((n=>{var r;o()(e.groupParentInfo,"groupParentInfo should be defined");const i=null!==(r=e.groupParentInfo.runUuidsForAggregation)&&void 0!==r?r:e.groupParentInfo.runUuids;let c;if((0,l.isNil)(u)){const e=(0,l.compact)(i.flatMap((e=>{var t,r;const i=null===(t=d[e])||void 0===t||null===(r=t[n])||void 0===r?void 0:r.metricsHistory;return p?i?(0,s.c1)(i):void 0:i}))),t=(0,l.uniq)(e.map((e=>e.step))).sort(((e,t)=>e-t));c=(0,a.yu)(t,n,e)}else c=(0,a.nN)((0,l.pick)(d,i),n,u,p);t[n]=c}));const n=c.reduce(((e,n)=>{const r=t[n][i];return r&&r.length>0&&(e[n]=r),e}),{});return{...e,metricsHistory:n,aggregatedMetricsHistory:t}})),...t.filter((e=>{let{belongsToGroup:t}=e;return!1===t}))]}),[c,d,t,n,i,u,p])}},83028:function(e,t,n){"use strict";n.d(t,{G:function(){return i}});var r=n(25866);const i=()=>({searchFilter:"",orderByKey:r.XM,orderByAsc:r.z$,startTime:r.xx,lifecycleFilter:r.ae,datasetsFilter:[],modelVersionFilter:r.eR})},90765:function(e,t,n){"use strict";n.d(t,{G:function(){return p},S:function(){return d}});var r=n(42380),i=n(31014),o=n(7781),s=n(95177),a=n(6993),l=n(4591),c=n(50111);const d=e=>{let{dragGroupKey:t,dragKey:n,onDrop:r,disabled:a=!1}=e;const l=(0,i.useRef)(r);l.current=r;const[{isOver:c,draggedItem:d},u]=(0,o.H)({canDrop:()=>!a,accept:`dnd-${t}`,drop:(e,t)=>{let{key:r}=e;r===n||t.didDrop()||l.current(r,n)},collect:e=>({isOver:e.isOver({shallow:!0}),draggedItem:e.getItem()})},[a,t,n]),[{isDragging:p},h,m]=(0,s.i)({canDrag:()=>!a,type:`dnd-${t}`,item:{key:n,groupKey:t},collect:e=>({isDragging:e.isDragging()})},[a,t,n]);return{dropTargetRef:u,dragHandleRef:h,dragPreviewRef:m,isDragging:p,isOver:c,isDraggingOtherGroup:Boolean(d&&d.groupKey!==t)}};var u={name:"49aokf",styles:"display:contents"};const p=e=>{let{children:t}=e;const n=(0,i.useRef)(null),[o,s]=(0,i.useState)(null);return(0,i.useLayoutEffect)((()=>{const e=n.current,t=(0,r.b)(l.t2,void 0,{rootElement:e});return s(t),()=>{t.getBackend().teardown()}}),[]),(0,c.Y)("div",{css:u,ref:n,children:o&&(0,c.Y)(a.Q,{manager:o,children:t})})}},97026:function(e,t,n){"use strict";n.d(t,{u:function(){return s}});var r=n(31014),i=n(15579),o=n(50111);class s extends r.Component{constructor(e){super(e),this.state={isSubmitting:!1},this.onRequestCloseHandler=this.onRequestCloseHandler.bind(this),this.handleSubmitWrapper=this.handleSubmitWrapper.bind(this)}onRequestCloseHandler(){this.state.isSubmitting||this.props.onClose()}handleSubmitWrapper(){return this.setState({isSubmitting:!0}),this.props.handleSubmit().finally((()=>{this.props.onClose(),this.setState({isSubmitting:!1})}))}render(){return(0,o.Y)(i.d,{"data-testid":"confirm-modal",title:this.props.title,visible:this.props.isOpen,onOk:this.handleSubmitWrapper,okText:this.props.confirmButtonText,confirmLoading:this.state.isSubmitting,onCancel:this.onRequestCloseHandler,centered:!0,children:(0,o.Y)("div",{className:"modal-explanatory-text",children:this.props.helpText})})}}},97604:function(e,t,n){"use strict";n.d(t,{l:function(){return i}});var r=n(31014);const i=e=>{const t=(0,r.useRef)(e);return(0,r.useEffect)((()=>{t.current=e}),[e]),e=>{t.current(e)}}},99061:function(e,t,n){"use strict";n.d(t,{_:function(){return r}});const r=e=>{let{data:t,selectedImageKeys:n}=e;const r=t.reduce(((e,t)=>{for(const r of Object.keys(t.images))if(null!==n&&void 0!==n&&n.includes(r)){const n=t.images[r];for(const t of Object.values(n))void 0!==t.step&&(e[t.step]={style:{display:"none"},label:""})}return e}),{});return{stepMarks:r,maxMark:Math.max(...Object.keys(r).map(Number)),minMark:Math.min(...Object.keys(r).map(Number))}}},99790:function(e,t,n){"use strict";n.d(t,{h:function(){return d}});var r,i,o,s,a=n(31014);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(null,arguments)}function c(e,t){let{title:n,titleId:c,...d}=e;return a.createElement("svg",l({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",ref:t,"aria-labelledby":c},d),n?a.createElement("title",{id:c},n):null,r||(r=a.createElement("rect",{x:.5,y:.5,width:15,height:15,stroke:"#C4C4C4"})),i||(i=a.createElement("line",{x1:8.5,y1:.800049,x2:8.5,y2:16,stroke:"#C4C4C4"})),o||(o=a.createElement("line",{x1:16,y1:5.69995,x2:-2.63656e-8,y2:5.69995,stroke:"#C4C4C4"})),s||(s=a.createElement("line",{x1:16,y1:10.9,x2:-2.63656e-8,y2:10.9,stroke:"#C4C4C4"})))}const d=a.forwardRef(c);n.p}}]);
//# sourceMappingURL=9857.7698dcff.chunk.js.map