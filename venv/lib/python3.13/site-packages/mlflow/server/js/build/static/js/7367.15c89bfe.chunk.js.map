{"version": 3, "file": "static/js/7367.15c89bfe.chunk.js", "mappings": "qUAQO,MAAMA,GACX,EAAAC,EAAAA,eAA+C,MCK3CC,EAAmC,CACvCC,UAAU,EACVC,MAAO,MAGF,MAAMC,UAAsBC,EAAAA,UAIjCC,MAAA,KAAQL,EAAR,GAEA,+BAAOM,CAAyBJ,GAC9B,MAAO,CAAED,UAAU,E,MAAMC,EAC3B,CAEAK,mBAAA,UAAAC,EAAA,YAAqB,WACnB,MAAMN,MAAEA,GAAUM,EAAKH,MAEvB,GAAc,OAAVH,EAAgB,SAAAO,EAAAC,UAAAC,OAHGC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAIrBN,EAAKO,MAAMC,UAAU,C,KACnBJ,EACAK,OAAQ,mBAGVT,EAAKU,SAASlB,EAChB,CACF,CAAC,EAXD,GAaAmB,iBAAAA,CAAkBjB,EAAckB,GAC9BC,KAAKN,MAAMO,UAAUpB,EAAOkB,EAC9B,CAEAG,kBAAAA,CACEC,EACAC,GAEA,MAAMxB,SAAEA,GAAaoB,KAAKhB,OACpBqB,UAAEA,GAAcL,KAAKN,MAQzBd,GACoB,OAApBwB,EAAUvB,OAqDhB,WAAuD,IAA9ByB,EAAAjB,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAW,GAAImB,EAAAnB,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAW,GACjD,OACEiB,EAAEhB,SAAWkB,EAAElB,QAAUgB,EAAEG,MAAK,CAACC,EAAMC,KAAWC,OAAOC,GAAGH,EAAMF,EAAEG,KAExE,CAxDMG,CAAgBX,EAAUE,UAAWA,KAErCL,KAAKN,MAAMC,UAAU,CACnBoB,KAAMV,EACNW,KAAMb,EAAUE,UAChBT,OAAQ,SAGVI,KAAKH,SAASlB,GAElB,CAEAsC,MAAAA,GACE,MAAMC,SAAEA,EAAQC,eAAEA,EAAcC,kBAAEA,EAAiBC,SAAEA,GACnDrB,KAAKN,OACDd,SAAEA,EAAQC,MAAEA,GAAUmB,KAAKhB,MAEjC,IAAIsC,EAAgBJ,EAEpB,GAAItC,EAAU,CACZ,MAAMc,EAAuB,C,MAC3Bb,EACAK,mBAAoBc,KAAKd,oBAG3B,IAAI,EAAAqC,EAAAA,gBAAeF,GACjBC,EAAgBD,OACX,GAA8B,oBAAnBF,EAChBG,EAAgBH,EAAezB,OAC1B,KAAI0B,EAGT,MAAM,IAAII,MACR,8FAHFF,GAAgB,EAAAG,EAAAA,eAAcL,EAAmB1B,EAG/C,CAGN,CAEA,OAAO,EAAA+B,EAAAA,eACLhD,EAAqBiD,SACrB,CACEC,MAAO,C,SACL/C,E,MACAC,EACAK,mBAAoBc,KAAKd,qBAG7BoC,EAEJ,EC5GK,SAASM,EACdD,GAEA,GACW,MAATA,GAC0B,mBAAnBA,EAAM/C,UACuB,oBAA7B+C,EAAMzC,mBAEb,MAAM,IAAIsC,MAAM,kCAGlB,OAAO,CACT,CCLO,SAASK,IACd,MAAMC,GAAU,EAAAC,EAAAA,YAAWtD,GAE3BmD,EAA2BE,GAE3B,MAAO9C,EAAOa,IAAY,EAAAmC,EAAAA,UAGvB,CACDnD,MAAO,KACPoD,UAAU,IAGNC,GAAW,EAAAC,EAAAA,UACf,KAAM,CACJC,cAAeA,KACbN,GAAS5C,qBACTW,EAAS,CAAEhB,MAAO,KAAMoD,UAAU,GAAQ,EAE5CI,aAAexD,GACbgB,EAAS,C,MACPhB,EACAoD,UAAU,OAGhB,CAACH,GAAS5C,qBAGZ,GAAIF,EAAMiD,SACR,MAAMjD,EAAMH,MAGd,OAAOqD,CACT,C,iCCtCO,SAASI,EACdC,EACAC,GAEA,MAAMC,EAAiC/C,IAC9B,EAAA+B,EAAAA,eACL3C,EACA0D,GACA,EAAAf,EAAAA,eAAcc,EAAW7C,IAKvBgD,EAAOH,EAAUI,aAAeJ,EAAUG,MAAQ,UAGxD,OAFAD,EAAQE,YAAc,qBAAqBD,KAEpCD,CACT,C,mSClBO,IAAKG,EAAiC,SAAjCA,GAAiC,OAAjCA,EAAiC,cAAjCA,EAAiC,cAAjCA,CAAiC,MAK7C,MAAMC,EAAwB,W,+CCE9B,MAoBMC,EAA0B,CAC9B,WACA,aACA,SACA,eACA,gBACA,qBAGWC,EAA4CC,IAQlD,IARmD,YACxDC,EAAW,oBACXC,EAAmB,iBACnBC,GAKDH,EACC,MAAMI,GAAUC,EAAAA,EAAAA,UAAQ,KACtB,MAAMC,EAtCRC,KAEA,MAAMC,EAAc,IAAIC,IAClBC,EAAa,IAAID,IACjBE,EAAW,IAAIF,IAErB,IAAK,MAAMG,KAAeL,EAAc,CAAC,IAADM,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACtB,QAAhBL,EAAAD,EAAYO,YAAI,IAAAN,GAAS,QAATC,EAAhBD,EAAkBO,eAAO,IAAAN,GAAzBA,EAA2BO,SAASC,GAAWA,EAAOC,KAAOf,EAAYgB,IAAIF,EAAOC,OACpE,QAAhBR,EAAAH,EAAYO,YAAI,IAAAJ,GAAQ,QAARC,EAAhBD,EAAkBU,cAAM,IAAAT,GAAxBA,EAA0BK,SAASK,GAAUA,EAAMH,KAAOb,EAAWc,IAAIE,EAAMH,OAC/D,QAAhBN,EAAAL,EAAY7D,YAAI,IAAAkE,GAAM,QAANC,EAAhBD,EAAkBU,YAAI,IAAAT,GAAtBA,EAAwBG,SAASO,GAAQA,EAAIL,KAAOZ,EAASa,IAAII,EAAIL,MACvE,CAEA,MAAO,CACLf,YAAahE,MAAMqF,KAAKrB,GACxBE,WAAYlE,MAAMqF,KAAKnB,GACvBC,SAAUnE,MAAMqF,KAAKlB,GAAUmB,OAAOC,EAAAA,IACvC,EAsBqBC,CAAmC7B,GACjD8B,EAAwBnC,EAAwBoC,KAAKC,IAAS,CAClExD,MAAO,cAAcwD,QAEvB,OAAOC,EAAAA,EAAAA,IAAsC9B,EAAa2B,EAAsB,GAC/E,CAAC9B,IAEJ,OACEkC,EAAAA,EAAAA,GAACC,EAAAA,EAAwB,CACvBC,aAAyB,OAAXtC,QAAW,IAAXA,EAAAA,EAAe,GAC7BuC,qBAAsBtC,EACtBuC,0BAA0B,EAC1BC,YAAatC,EACbuC,QAASA,IAAMzC,EAAoB,IACnC0C,YAAY,sBACZC,gBACEC,EAAAA,EAAAA,IAAA,OAAA5E,SAAA,EACEmE,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iFAEfC,OAAQ,CAAEC,WAAWd,EAAAA,EAAAA,GAAA,KAAAnE,SAAG,aACvB,KACHmE,EAAAA,EAAAA,GAAA,UACAA,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,eAGjBZ,EAAAA,EAAAA,GAAA,SACC,8BACDA,EAAAA,EAAAA,GAAA,SACC,iCACDA,EAAAA,EAAAA,GAAA,SAAM,+BAENA,EAAAA,EAAAA,GAAA,SAAM,iCAENA,EAAAA,EAAAA,GAAA,SAAM,yCAENA,EAAAA,EAAAA,GAAA,aAGJ,ECxEN,MAAMe,EAAkBC,GACtBC,KAAKC,UAAU,CAACF,EAAQG,aAAcH,EAAQI,iBAAiB,IAAAC,EAAA,CAAAhE,KAAA,UAAAiE,OAAA,mBAE1D,MAAMC,EAA+C5D,IAUrD,IAVsD,iBAC3DG,EAAgB,uBAChB0D,EAAsB,gBACtBC,EAAe,wBACfC,GAMD/D,EACC,MAAM,MAAEgE,IAAUC,EAAAA,EAAAA,KACZC,GAAOC,EAAAA,EAAAA,KAEPC,GAAiBC,EAAAA,EAAAA,QAAiE,IAAIC,KAKtFC,GAAclE,EAAAA,EAAAA,UAAQ,KAC1B,IAAK,MAAMmE,KAASrE,EAClB,IAAK,MAAMmB,KAAoB,QAAVmD,EAAAD,EAAMrD,YAAI,IAAAsD,OAAA,EAAVA,EAAYrD,UAAW,GAAI,CAAC,IAADqD,EAC9C,IAAKnD,EAAOkC,eAAiBlC,EAAOmC,eAClC,SAEF,MAAMiB,EAActB,EAAe9B,GAC9B8C,EAAeO,QAAQC,IAAIF,IAE9BN,EAAeO,QAAQE,IAAIH,EAAa,CACtCI,KAAMJ,EACNlB,aAAclC,EAAOkC,aACrBC,eAAgBnC,EAAOmC,gBAG7B,CAEF,OAAOjH,MAAMqF,KAAKuC,EAAeO,QAAQzB,SAAS,GACjD,CAAC/C,IAGE4E,GAA6B1E,EAAAA,EAAAA,UACjC,KAA4B,OAAtBwD,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwB3B,IAAIkB,KAAmB,IACrD,CAACS,IAGH,OACEf,EAAAA,EAAAA,IAACkC,EAAAA,IAAc,CACbC,YAAY,gDACZjC,GAAG,gDACHrE,MAAOoG,EACPG,MAAOhB,EAAKiB,cAAc,CAAAnC,GAAA,SACxBC,eAAe,aAGjBmC,qBAAmB,EAAAlH,SAAA,EAEnBmE,EAAAA,EAAAA,GAACgD,EAAAA,IAAwC,CAAAnH,UACvC4E,EAAAA,EAAAA,IAACwC,EAAAA,EAAM,CACLC,SAASlD,EAAAA,EAAAA,GAACmD,EAAAA,IAAe,IACzBP,YAAY,uDACZQ,MAAMpD,EAAAA,EAAAA,GAACqD,EAAAA,IAAS,IAAIxH,SAAA,CACrB,WAEE6G,EAA2BzI,OAAS,GACnCwG,EAAAA,EAAAA,IAAA6C,EAAAA,GAAA,CAAAzH,SAAA,EACEmE,EAAAA,EAAAA,GAACuD,EAAAA,IAAwB,CAACC,IAAGnC,EAAoBxF,SAC9C6G,EAA2BzI,UAE9B+F,EAAAA,EAAAA,GAACyD,EAAAA,IAAe,CACd,cAAY,QACZC,KAAK,SACLC,QAAUC,IACRA,EAAEC,kBACFD,EAAEE,iBACqB,OAAvBpC,QAAuB,IAAvBA,GAAAA,GAA2B,EAE7B8B,KAAGO,EAAAA,EAAAA,IAAE,CACHC,MAAOrC,EAAMsC,OAAOC,gBACpBC,SAAUxC,EAAMyC,WAAWC,WAC3BC,WAAY3C,EAAM4C,QAAQC,GAE1B,SAAU,CACRR,MAAOrC,EAAMsC,OAAOQ,0BAEvB,SAGH,WAGRzE,EAAAA,EAAAA,GAAC0E,EAAAA,IAAqB,CAAA7I,UACpBmE,EAAAA,EAAAA,GAAC2E,EAAAA,IAAwB,CAAA9I,SACtBqG,EAAYrC,KAAI+E,IAAA,IAAGnC,KAAMoC,EAAiB,eAAEzD,EAAc,aAAED,GAAcyD,EAAA,OACzEnE,EAAAA,EAAAA,IAACqE,EAAAA,IAAoC,CACnCxI,MAAOuI,EACPE,QAASrC,EAA2BsC,SAASH,GAE7CI,SAAUA,IAAqB,OAAfxD,QAAe,IAAfA,OAAe,EAAfA,EAAkB,CAAEL,iBAAgBD,iBAAgBtF,SAAA,CAEnEsF,EAAa,MAAIC,EAAe,MAH5ByD,EAIgC,UAI9B,E,gDChGrB,MAAMK,EAAyBA,CAACC,EAA2DtD,KACzF,MAOMuD,EAPiG,CACrG,CAACC,EAAAA,GAA0CC,eAAeC,EAAAA,EAAAA,IAAc,CAAA5E,GAAA,SACtEC,eAAe,mBAKOuE,GAE1B,GAAIC,EACF,OAAOvD,EAAKiB,cAAcsC,GAG5B,MAAMI,GAAeC,EAAAA,EAAAA,IAAsCN,GAE3D,OAAIK,EACKA,EAAaE,UAGfP,CAAK,EACZ,IAAAQ,EAAA,CAAAtI,KAAA,SAAAiE,OAAA,kCAEK,MAAMsE,EAA+CjI,IAUrD,IAVsD,cAC3DkI,EAAa,WACbC,EAAU,gBACVC,EAAe,WACfC,EAAa,IAMdrI,EACC,MAAMkE,GAAOC,EAAAA,EAAAA,MACNrC,EAAQwG,IAAaC,EAAAA,EAAAA,UAAS,KAC/B,MAAEvE,IAAUC,EAAAA,EAAAA,KAEZuE,GAAwBnI,EAAAA,EAAAA,UAA0B,KACtD,MAAMoI,EAAkB3G,EAAO4G,cACzBC,EAAuB,CAC3BC,QAAS,aACTC,WAAY3E,EAAKiB,cAAc,CAAAnC,GAAA,SAC7BC,eAAe,eAGjB/E,SAAU,CACR,CACEsJ,MAAOE,EAAAA,GAA0CC,aACjDkB,WAAYtB,EAAuBG,EAAAA,GAA0CC,aAAczD,KAE7FpC,QAAO4B,IAAA,IAAC,WAAEmF,GAAYnF,EAAA,OAAe,OAAVmF,QAAU,IAAVA,OAAU,EAAVA,EAAYH,cAAcrB,SAASoB,EAAgB,KAI5EK,EAAqBT,EACxBvG,QAAQiH,IAAG,IAAAC,EAAA,OAAgB,QAAhBA,EAAKD,EAAIH,eAAO,IAAAI,OAAA,EAAXA,EAAaC,WAAWC,EAAAA,GAAuC,IAC/EhH,KAAK6G,IAAG,IAAAI,EAAA,MAAM,IACVJ,EACH7K,SAAsB,QAAdiL,EAAEJ,EAAI7K,gBAAQ,IAAAiL,OAAA,EAAZA,EAAcrH,QAAOmF,IAAA,IAAC,MAAEO,GAAOP,EAAA,OAAU,OAALO,QAAK,IAALA,OAAK,EAALA,EAAOH,SAASoB,EAAgB,IAC9EI,WAAYE,EAAIF,WACZ,YAAYE,EAAIF,cAChB3E,EAAKiB,cAAc,CAAAnC,GAAA,SACjBC,eAAe,YAGtB,IAEGmG,EAAuB,CAACT,KAAyBG,GAAoBhH,QACxEiH,GAAQA,EAAI7K,UAAY6K,EAAI7K,SAAS5B,OAAS,IAMjD,IACG8M,EAAqB3L,MAAM4L,GAAUA,EAAMnL,UAAYmL,EAAMnL,SAAST,MAAMsL,GAAQA,EAAIvB,QAAUU,MACnG,CACA,MAAM,UAAEH,IAAcD,EAAAA,EAAAA,IAAsCI,GAExDH,GACFqB,EAAqBE,KAAK,CACxBV,QAAS,UACTC,WAAY3E,EAAKiB,cAAc,CAAAnC,GAAA,SAC7BC,eAAe,wBAGjB/E,SAAU,CAAC,CAAEsJ,MAAOU,EAAeW,WAAYd,KAGrD,CACA,OAAOqB,CAAoB,GAC1B,CAACf,EAAYnE,EAAMpC,EAAQoG,IAE9B,OACEpF,EAAAA,EAAAA,IAACyG,EAAAA,IAAaC,KAAI,CAACC,OAAO,EAAMvL,SAAA,EAC9BmE,EAAAA,EAAAA,GAACkH,EAAAA,IAAaG,QAAO,CAACC,SAAO,EAAAzL,UAC3BmE,EAAAA,EAAAA,GAACiD,EAAAA,EAAM,CACLL,YAAY,oCACZQ,KAAM0C,GAAa9F,EAAAA,EAAAA,GAACuH,EAAAA,IAAiB,KAAMvH,EAAAA,EAAAA,GAACwH,EAAAA,IAAkB,IAAI3L,UAElEmE,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAEfC,OAAQ,CAAE4G,OAAQvC,EAAuBW,EAAehE,WAI9DpB,EAAAA,EAAAA,IAACyG,EAAAA,IAAaQ,QAAO,CAAClE,IAAGmC,EAAuC9J,SAAA,EAC9D4E,EAAAA,EAAAA,IAAA,OACE+C,KAAGO,EAAAA,EAAAA,IAAE,CACH4D,QAAS,GAAGhG,EAAM4C,QAAQqD,QAAQjG,EAAM4C,QAAQsD,GAAK,OAAOlG,EAAM4C,QAAQqD,OAC1EE,MAAO,OACPC,QAAS,OACTC,IAAKrG,EAAM4C,QAAQC,IACpB,IAAC3I,SAAA,EAEFmE,EAAAA,EAAAA,GAACiI,EAAAA,EAAK,CACJrF,YAAY,2CACZsF,QAAQlI,EAAAA,EAAAA,GAACmI,EAAAA,EAAU,IACnB7L,MAAOmD,EACP2I,KAAK,SACLnD,SAAWrB,GAAMqC,EAAUrC,EAAEyE,OAAO/L,OACpCiE,YAAasB,EAAKiB,cAAc,CAAAnC,GAAA,SAC9BC,eAAe,WAGjB0H,WAAS,EACTC,YAAU,KAEZ9H,EAAAA,EAAAA,IAAA,OACE+C,KAAGO,EAAAA,EAAAA,IAAE,CACHgE,QAAS,OACTC,IAAKrG,EAAM4C,QAAQC,IACpB,IAAC3I,SAAA,EAEFmE,EAAAA,EAAAA,GAACwI,EAAAA,EAAgB,CACfC,SAAU3C,EACV1C,MAAMpD,EAAAA,EAAAA,GAAC0I,EAAAA,IAAa,IACpB9F,YAAY,gDACZe,QAASA,IAAMoC,EAAgBF,GAAe,GAC9C,aAAYhE,EAAKiB,cAAc,CAAAnC,GAAA,SAC7BC,eAAe,uBAInBZ,EAAAA,EAAAA,GAACwI,EAAAA,EAAgB,CACfC,QAAS3C,EACT1C,MAAMpD,EAAAA,EAAAA,GAAC2I,EAAAA,IAAW,IAClB/F,YAAY,+CACZe,QAASA,IAAMoC,EAAgBF,GAAe,GAC9C,aAAYhE,EAAKiB,cAAc,CAAAnC,GAAA,SAC7BC,eAAe,2BAOtBuF,EAAsBtG,KAAI+I,IAAA,IAAC,WAAEpC,EAAU,SAAE3K,EAAQ,QAAE0K,GAASqC,EAAA,OAC3DnI,EAAAA,EAAAA,IAACyG,EAAAA,IAAa2B,MAAK,CAAe,aAAYrC,EAAW3K,SAAA,EACvDmE,EAAAA,EAAAA,GAACkH,EAAAA,IAAa4B,MAAK,CAAAjN,SAAE2K,IACZ,OAAR3K,QAAQ,IAARA,OAAQ,EAARA,EAAUgE,KAAIkJ,IAAA,IAAGvC,WAAYwC,EAAgB,MAAE7D,GAAO4D,EAAA,OACrDtI,EAAAA,EAAAA,IAACyG,EAAAA,IAAa+B,aAAY,CAExBrG,YAAY,kDACZmC,QAASc,IAAkBV,EAC3BxB,QAASA,KACFwB,GAGLY,EAAgBZ,EAAO+D,QAAQpD,GAAY,EAC3CjK,SAAA,EAEFmE,EAAAA,EAAAA,GAACkH,EAAAA,IAAaiC,cAAa,IAC1BH,IAXI7D,EAYqB,MAhBPoB,EAkBJ,SAGP,ECzLX6C,EAAwCzL,IAgC9C,IAhC+C,cACpDkI,EAAa,WACbC,EAAU,2BACVuD,EAA0B,gBAC1BtD,EAAe,gBACfuD,EAAe,WACftD,EAAU,iBACVuD,EAAmB,CAAC,EAAC,SACrBC,EAAQ,YACRC,EAAW,YACX7L,EAAc,GAAE,oBAChBC,EAAmB,iBACnBC,EAAgB,uBAChB0D,EAAsB,gBACtBC,EAAe,wBACfC,GAiBD/D,EACC,MAAMkE,GAAOC,EAAAA,EAAAA,MACP,MAAEH,IAAUC,EAAAA,EAAAA,KAElB,OACEnB,EAAAA,EAAAA,IAAA,OAAK+C,KAAGO,EAAAA,EAAAA,IAAE,CAAEgE,QAAS,OAAQ2B,SAAU,OAAQ1B,IAAKrG,EAAM4C,QAAQqD,IAAI,IAAC/L,SAAA,EACrE4E,EAAAA,EAAAA,IAACkJ,EAAAA,IAAqB,CACpB/G,YAAY,qCACZvF,KAAK,YACLf,MAAOkN,EACPvE,SAAWrB,IACT6F,GACEG,EAAAA,EAAAA,IAAarM,EAAmCqG,EAAEyE,OAAO/L,MAAOiB,EAAkCsM,OACnG,EACDhO,SAAA,EAEF4E,EAAAA,EAAAA,IAACqJ,EAAAA,IAAsB,CAACxN,MAAM,QAAOT,SAAA,EACnCmE,EAAAA,EAAAA,GAAC+J,EAAAA,EAAO,CACNnH,YAAY,mDACZoH,QAASnI,EAAKiB,cAAc,CAAAnC,GAAA,SAC1BC,eAAe,eAEd/E,UAEHmE,EAAAA,EAAAA,GAACiK,EAAAA,EAAQ,OAEXjK,EAAAA,EAAAA,GAAA,QAAMwD,IAAK0G,EAAAA,EAAerO,SACvBgG,EAAKiB,cAAc,CAAAnC,GAAA,SAClBC,eAAe,qBAKrBH,EAAAA,EAAAA,IAACqJ,EAAAA,IAAsB,CAACxN,MAAM,QAAOT,SAAA,EACnCmE,EAAAA,EAAAA,GAAC+J,EAAAA,EAAO,CACNnH,YAAY,mDACZoH,QAASnI,EAAKiB,cAAc,CAAAnC,GAAA,SAC1BC,eAAe,eAEd/E,UAEHmE,EAAAA,EAAAA,GAACmK,EAAAA,IAAa,OAEhBnK,EAAAA,EAAAA,GAAA,QAAMwD,IAAK0G,EAAAA,EAAerO,SACvBgG,EAAKiB,cAAc,CAAAnC,GAAA,SAClBC,eAAe,uBAMtByI,GACC5I,EAAAA,EAAAA,IAAA6C,EAAAA,GAAA,CAAAzH,SAAA,EACEmE,EAAAA,EAAAA,GAACtC,EAAyC,CACxCE,YAAaA,EACbC,oBAAqBA,EACrBC,iBAAkBA,KAEpBkC,EAAAA,EAAAA,GAACuB,EAA4C,CAC3CzD,iBAAkBA,EAClB2D,gBAAiBA,EACjBC,wBAAyBA,EACzBF,uBAAwBA,KAE1BxB,EAAAA,EAAAA,GAAC4F,EAA4C,CAC3CC,cAA4B,OAAbA,QAAa,IAAbA,EAAAA,EAAiB,GAChCC,WAAYA,EACZC,gBAAiBA,EACjBC,WAAYA,QAIhBhG,EAAAA,EAAAA,GAACiD,EAAAA,EAAM,CACLL,YAAY,gCACZQ,KAAM0C,GAAa9F,EAAAA,EAAAA,GAACuH,EAAAA,IAAiB,KAAMvH,EAAAA,EAAAA,GAACwH,EAAAA,IAAkB,IAC9D7D,QAASA,KACPkC,GAAiBE,EAAgBF,GAAgBC,EAAW,EAC5DjK,UAEFmE,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qBAKrBZ,EAAAA,EAAAA,GAACoK,EAAAA,EAA2C,CAC1CpE,WAAYA,EACZuD,iBAAkBA,EAClBD,gBAAiBA,EACjBe,SAAUb,IAAajM,EAAkC+M,UAEvD,E,6JC5IH,MAAMC,EAAiCA,CAAC7E,EAAoB8E,IACjEA,EAAcvJ,KAAKC,UAAU,CAACsJ,EAAa9E,IAAuB,OAATA,QAAS,IAATA,EAAAA,EAAa,GCclE+E,GAAwB,SAACC,GAAoC,MACjE,6CAD6D1Q,UAAAC,OAAA,QAAAiB,IAAAlB,UAAA,GAAAA,UAAA,GAAG,KACR0Q,GAAkB,EAoFtEC,GAA2BA,KAAA,CAC/BC,sBAAkB1P,EAClB2P,wBAAoB3P,EACpB4P,oBAAoB,EACpBC,sBAAsB,EACtBC,mBAAoB,GACpBC,2BAAuB/P,EACvBgQ,SAAS,IAILC,GAAuBA,CAACxR,EAA0CyR,KAEtE,GAAoB,WAAhBA,EAAOhD,KACT,MAAO,IAAKgD,EAAOC,YAAY1R,GAAQuR,SAAS,GAGlD,GAAoB,oBAAhBE,EAAOhD,KAA4B,CACrC,MAAM,iBAAEwC,EAAgB,mBAAEC,GAjGmBS,KAC/C,MAAMV,EAA8CU,EAAkBzL,KACpElC,IAAA,IAAC,cAAE4N,EAAa,UAAE7F,EAAS,YAAE8E,GAAa7M,EAAA,MAAM,CAC9C6N,SAAS,EACTpD,KAAMqD,EAAAA,GAAcC,IACpBC,KAAM,WAAWJ,IACjBK,gBAAiBpB,EAAc,WAAWA,IAAgB,UAC1DqB,aAAa,EACbnG,YACA6F,gBACAf,cACAlN,YAAakN,EAAc,IAAIA,MAAgB9E,SAAcxK,EAC9D,IAGG2P,GAA2CiB,EAAAA,EAAAA,MAAKR,EAAkBzL,KAAIwB,IAAA,IAAC,YAAEmJ,GAAanJ,EAAA,OAAKmJ,CAAW,KAAG3K,KAC5G2K,IAAW,CACVzC,SAAS,EACT1K,KAAiB,OAAXmN,QAAW,IAAXA,EAAAA,EAAe,UACrBmB,KAAMnB,EAAc,WAAWA,IAAgB,UAC/CuB,aAAa,MAajB,OATIC,EAAAA,EAAAA,SAAQnB,IACVA,EAAmB5D,KAAK,CACtBc,SAAS,EACT1K,KAAM,UACNsO,KAAM,UACNI,aAAa,IAIV,CACLnB,mBACAC,qBACD,EA6DkDoB,CAAwCb,EAAOE,mBAEhG,MA3D+BY,EACjCC,EACAC,KAIyF,IAADC,EAAAC,EADxF,KAAKH,EAAavB,mBAAqBuB,EAAatB,qBAAuBsB,EAAajB,WAClFkB,EAAUxB,iBAAiB3Q,OAAS,GAAKmS,EAAUvB,mBAAmB5Q,OAAS,GACjF,MAAO,IACFkS,EACHvB,iBAA4C,QAA5ByB,EAAED,EAAUxB,wBAAgB,IAAAyB,EAAAA,EAAI,GAChDxB,mBAAgD,QAA9ByB,EAAEF,EAAUvB,0BAAkB,IAAAyB,EAAAA,EAAI,IAM1D,MAAMC,EAAiBH,EAAUxB,iBAAiBnL,QAC/C+M,IAAQ,IAAAC,EAAA,QAAmC,QAA9BA,EAACN,EAAavB,wBAAgB,IAAA6B,GAA7BA,EAA+BC,MAAMC,GAAUA,EAAMhB,OAASa,EAASb,OAAK,IAEvFiB,EAAmBR,EAAUvB,mBAAmBpL,QACnDoN,IAAU,IAAAC,EAAA,OACTP,EAAeG,MAAMF,GAAaA,EAASZ,kBAAoBiB,EAAWlB,UAC1C,QAAhCmB,EAACX,EAAatB,0BAAkB,IAAAiC,GAA/BA,EAAiCJ,MAAMK,GAAYA,EAAQpB,OAASkB,EAAWlB,OAAK,IAGzF,OAAIiB,EAAiB3S,OAAS,GAAKsS,EAAetS,OAAS,EAClD,IACFkS,EACHvB,iBAAkBuB,EAAavB,iBAC3B,IAAIuB,EAAavB,oBAAqB2B,GACtCH,EAAUxB,iBACdC,mBAAoBsB,EAAatB,mBAC7B,IAAIsB,EAAatB,sBAAuB+B,GACxCR,EAAUvB,oBAGXsB,CAAY,EAsBAD,CAA2BvS,EAAO,CAAEiR,mBAAkBC,sBAEzE,CACA,MAAoB,eAAhBO,EAAOhD,MACLgD,EAAO4B,cACF5B,EAAO4B,cAGXrT,CAAK,EAoBDsT,GAAyCA,CACpD3B,EACAZ,KAEA,MAAOwC,EAAcC,IAAwBC,EAAAA,EAAAA,YAAWjC,QAAsBjQ,EAAWyP,KAClF0C,EAASC,IAAcpH,EAAAA,EAAAA,WAAS,IAGvCqH,EAAAA,EAAAA,YAAU,KACRD,GAAW,GA1BsBE,WAEnC,MAAMC,EAAiBC,aAAaC,QAAQlD,GAAsBC,IAClE,GAAK+C,EAGL,IACE,OAAOxM,KAAK2M,MAAMH,EACpB,CAAE,MACA,MACF,GAiBEI,CAA6BnD,GAAiBoD,MAAMhP,IAClDqO,EAAqB,CAAE/E,KAAM,aAAc4E,cAAelO,IAC1DwO,GAAW,EAAM,GACjB,GACD,CAAC5C,KAGJ6C,EAAAA,EAAAA,YAAU,KACJF,GAGJF,EAAqB,CAAE/E,KAAM,kBAAmBkD,qBAAoB,GACnE,CAACA,EAAmB+B,KAGvBE,EAAAA,EAAAA,YAAU,KACJL,EAAahC,SA9BKsC,OAAO9C,EAAyBqD,KACxDL,aAAaM,QAAQvD,GAAsBC,GAAkBzJ,KAAKC,UAAU6M,GAAe,EA8BvFE,CAAkBvD,EAAiBwC,EACrC,GACC,CAACxC,EAAiBwC,IAGrB,MAAMgB,GAAgBC,EAAAA,EAAAA,cACnB9C,GACC8B,EAAqB,CACnB/E,KAAM,SACNiD,iBAEJ,IAGF,MAAO,CAAE6B,eAAcgB,gBAAeb,UAAS,E,gBC3Kf,IAAAzI,GAAA,CAAAvH,KAAA,UAAAiE,OAAA,iBAElC,MAAM8M,IAAsCC,EAAAA,EAAAA,OAC1C1Q,IAQO,IAAD2Q,EAAAC,EAAA,IARL,UACCC,EAAS,QACTC,EAAO,oBACPC,GAKD/Q,EACC,MAAM,MAAEgE,IAAUC,EAAAA,EAAAA,MACZ,cAAEkB,IAAkBhB,EAAAA,EAAAA,KAEpB6M,GAAsB3Q,EAAAA,EAAAA,UAAQ,KAAM8N,EAAAA,EAAAA,MAAK0C,EAAUI,SAASC,GAAQtT,OAAOuT,KAAKD,EAAI9P,aAAY,CAACyP,IACjGO,GAAqB/Q,EAAAA,EAAAA,UAAQ,KAAM8N,EAAAA,EAAAA,MAAK0C,EAAUI,SAASC,GAAQtT,OAAOuT,KAAKD,EAAIzP,YAAW,CAACoP,IAE/FQ,GAAsBC,EAAAA,EAAAA,MAEtBC,GAAYf,EAAAA,EAAAA,cACfgB,IACCH,GAAqBrV,IAAK,IAAWA,EAAOqR,mBAAoBmE,KAAU,GAE5E,CAACH,KAGII,EAAsBC,IAA2BnJ,EAAAA,EAAAA,UAAsC,MAExFoJ,GAAkBnB,EAAAA,EAAAA,cACrBvC,GAA6BxD,GAC5BiH,EAAwBE,EAAAA,GAAqBC,wBAAwBpH,GAAM,OAAOlN,EAAW0Q,KAC/F,IAGI6D,GAAgCC,EAAAA,EAAAA,MAChCC,GAAcC,EAAAA,EAAAA,OAEbC,EAAiBC,IAAsB5J,EAAAA,EAAAA,eAO5ChL,GAEI6U,GAAgC/R,EAAAA,EAAAA,UAAQ,MAASgS,KAAMxB,KAAc,CAACA,IAEtEyB,GAAsBjS,EAAAA,EAAAA,UAC1B,MAASgS,KAAMxB,EAAW0B,iBAAkBC,EAAAA,EAAOC,4CACnD,CAAC5B,IAGG6B,GACJrQ,EAAAA,EAAAA,GAAA,OAAKwD,KAAGO,EAAAA,EAAAA,IAAE,CAAEuM,UAAW3O,EAAM4C,QAAQsD,IAAI,IAAChM,UACxCmE,EAAAA,EAAAA,GAACuQ,EAAAA,IAAK,CACJC,aACExQ,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,0GAQzB,OACEZ,EAAAA,EAAAA,GAAA,OACEwD,KAAGO,EAAAA,EAAAA,IAAE,CACH0M,gBAAiB9O,EAAMsC,OAAOyM,oBAC9BC,YAAahP,EAAM4C,QAAQqM,GAC3BC,aAAclP,EAAM4C,QAAQqM,GAC5BE,cAAenP,EAAM4C,QAAQqM,GAE7BG,UAAW,aAAapP,EAAMsC,OAAO+M,SACrCC,WAAY,aAAatP,EAAMsC,OAAO+M,SAEtCE,KAAM,EACNC,SAAU,SACVpJ,QAAS,QACV,IAAClM,UAEF4E,EAAAA,EAAAA,IAAA,OACE+C,KAAGO,EAAAA,EAAAA,IAAE,CACHgE,QAAS,OACTqJ,cAAe,SACfpJ,IAAKrG,EAAM4C,QAAQqD,GACnByJ,WAAY1P,EAAM4C,QAAQqD,GAC1BuJ,SAAU,SACVD,KAAM,GACP,IAACrV,SAAA,EAEFmE,EAAAA,EAAAA,GAACiI,EAAAA,EAAK,CACJrF,YAAY,yCACZc,KAAK,YACLwE,QAAQlI,EAAAA,EAAAA,GAACmI,EAAAA,EAAU,IACnB7L,MAAiC,QAA5BgS,EAAEG,EAAQzD,0BAAkB,IAAAsD,EAAAA,EAAI,GACrC/F,YAAU,EACVtD,SAAU5D,IAAA,IAAC,OAAEgH,GAAQhH,EAAA,OAAK6N,EAAU7G,EAAO/L,MAAM,EACjDiE,YAAauC,EAAc,CAAAnC,GAAA,SACzBC,eAAe,4BAInBH,EAAAA,EAAAA,IAAA,OAAK+C,IAAGoB,GAAuB/I,SAAA,EAC7BmE,EAAAA,EAAAA,GAACsR,EAAAA,EAAwB,CAACC,YAAatB,EAAqBuB,UAAWC,EAAAA,EAAsB5V,UAC3FmE,EAAAA,EAAAA,GAAC0R,EAAAA,GAA2C,CAACC,kBAAmBlD,EAAQ7D,iBAAiB/O,UACvFmE,EAAAA,EAAAA,GAAC4R,EAAAA,EAA0B,CACzB/G,mBAAoB4D,EAAQ5D,mBAC5BD,iBAAkB6D,EAAQ7D,iBAC1BiH,cAAeC,EAAAA,KACfC,aAAcD,EAAAA,KACdtD,UAAWA,EACXwD,eAAgB3C,EAChBM,YAAaA,EACbL,gBAAiBA,EACjBH,OAAkC,QAA5BZ,EAAEE,EAAQzD,0BAAkB,IAAAuD,EAAAA,EAAI,GACtC0D,QAAS,KACTnC,mBAAoBA,EACpBhF,oBAAoB,EACpBoH,iBAAiB,EACjBjH,2BAAuB/P,EACvBiX,oBAAqB,CAAC1G,EAAAA,GAAcC,IAAKD,EAAAA,GAAc2G,SACvDC,yBAA0BhC,SAIhCrQ,EAAAA,EAAAA,GAACsS,EAAAA,EAAyB,CACxBzC,gBAAiBA,EACjB0C,SAAUA,IAAMzC,OAAmB5U,GACnCsT,UAAWA,EACXyD,QAAS,KACThC,oBAAqBF,EACrByC,iBAAkBf,EAAAA,EAClB3G,oBAAoB,EACpBG,2BAAuB/P,IAExBkU,IACCpP,EAAAA,EAAAA,GAACyS,GAAAA,EAAwB,CACvBC,aAAclE,EACdmE,cAAehE,EACfD,oBAAqBA,EACrBkE,aAAc7D,EACd8D,OAAQzD,EACR0D,SAAW1D,IACTK,EAA8B,IAAKL,EAAsB9R,iBAAapC,IACtEmU,EAAwB,KAAK,EAE/BkD,SAAUA,IAAMlD,EAAwB,MACxC4C,QAAS,KACTE,oBAAqB,CAAC1G,EAAAA,GAAcC,IAAKD,EAAAA,GAAc2G,kBAK3D,IAKCW,IAAkC1E,EAAAA,EAAAA,OAC7C1I,IAAiG,IAAhG,aAAEzH,EAAY,aAAE8U,GAA0ErN,EACzF,MAAM,MAAEhE,IAAUC,EAAAA,EAAAA,KAIZqR,ECxLiBC,EAAIC,EAAkBC,KAC/C,MAAMC,GAAMrR,EAAAA,EAAAA,UAMZ,OAJKqR,EAAI/Q,UAAYgR,EAAAA,EAAAA,SAAQF,EAAMC,EAAI/Q,QAAQ8Q,QAC7CC,EAAI/Q,QAAU,CAAE8Q,OAAM9W,MAAO6W,MAGxBE,EAAI/Q,QAAQhG,KAAK,EDiLK4W,EAAY,IAAMhV,GAAc,CAACA,IAEtDqV,EE3LkDrV,KAC1DF,EAAAA,EAAAA,UAAQ,KACN,MAAMuV,EAAqD,GAS3D,OARArV,EAAac,SAASmD,IAAW,IAADC,EAAAoR,EACpB,QAAVpR,EAAAD,EAAMrD,YAAI,IAAAsD,GAAS,QAAToR,EAAVpR,EAAYrD,eAAO,IAAAyU,GAAnBA,EAAqBxU,SAAQrB,IAAoD,IAAjDuB,IAAKwG,EAAWvE,aAAcqJ,GAAa7M,EACzE,GAAI+H,IAAc6N,EAAiB7G,MAAM9I,GAAMA,EAAE8B,YAAcA,GAAa9B,EAAE4G,cAAgBA,IAAc,CAC1G,MAAMe,EAAgBhB,EAA+B7E,EAAW8E,GAChE+I,EAAiBtM,KAAK,CAAEvB,YAAW8E,cAAae,iBAClD,IACA,KAEGkI,EAAAA,EAAAA,SAAQF,GAAkBlS,IAAA,IAAC,YAAEmJ,GAAanJ,EAAA,OAAMmJ,CAAW,GAAC,GAClE,CAACtM,IF+KuBwV,CAA4CT,IAE/D,aACJ/F,EAAY,cACZgB,EACAb,QAASsG,GACP1G,GAAuCsG,EAAkBP,GACvDxE,EF1L0CtQ,KAClD,MAAM,YAAE0V,IAAgBC,EAAAA,EAAAA,MACxB,OAAO7V,EAAAA,EAAAA,UACL,KACE8V,EAAAA,EAAAA,SACE5V,EAAa2B,KAA8B,CAACsC,EAAO7G,KAAK,IAAAyY,EAAApW,EAAAqW,EAAAC,EAAAC,EAAA9R,EAAAoR,EAAAW,EAAAC,EAAAC,EAAA,OAC5C,QAAVN,EAAA5R,EAAMzH,YAAI,IAAAqZ,GAAVA,EAAYO,SACR,CACEhX,YAAqD,QAA1CK,EAAkB,QAAlBqW,EAAY,QAAZC,EAAE9R,EAAMzH,YAAI,IAAAuZ,OAAA,EAAVA,EAAY5W,YAAI,IAAA2W,EAAAA,EAAc,QAAdE,EAAI/R,EAAMzH,YAAI,IAAAwZ,OAAA,EAAVA,EAAYI,gBAAQ,IAAA3W,EAAAA,EAAI,UACzD4W,OAAQ,CAAC,EAETxV,SAASyV,EAAAA,EAAAA,OACG,QADEpS,EACZD,EAAMrD,YAAI,IAAAsD,GAAS,QAAToR,EAAVpR,EAAYrD,eAAO,IAAAyU,OAAT,EAAVA,EAAqB3T,KAAIwB,IAAA,IAAC,aAAEF,EAAY,IAAEjC,EAAG,MAAE5C,EAAK,UAAEmY,EAAS,KAAEC,GAAMrT,EAAA,MAAM,CAE3EsT,QAASpK,EAA+BrL,EAAKiC,GAC7CjC,IAAQ,OAAHA,QAAG,IAAHA,EAAAA,EAAO,GACZ5C,MAAY,OAALA,QAAK,IAALA,EAAAA,EAAS,EAChBmY,UAAoB,OAATA,QAAS,IAATA,EAAAA,EAAa,EACxBC,KAAU,OAAJA,QAAI,IAAJA,EAAAA,EAAQ,EACf,IACD,WAEFtV,QAAQoV,EAAAA,EAAAA,OAGqB,QAHhBL,EACD,QADCC,EACXjS,EAAMrD,YAAI,IAAAsV,GAAQ,QAARC,EAAVD,EAAYhV,cAAM,IAAAiV,OAAR,EAAVA,EACIxU,KAAI+E,IAAA,IAAC,IAAE1F,EAAG,MAAE5C,GAAOsI,EAAA,MAAM,CAAE1F,IAAQ,OAAHA,QAAG,IAAHA,EAAAA,EAAO,GAAI5C,MAAY,OAALA,QAAK,IAALA,EAAAA,EAAS,GAAI,IAChEmD,QAAOkG,IAAA,IAAC,IAAEzG,GAAKyG,EAAA,OAAKzG,CAAG,WAAC,IAAAiV,EAAAA,EAAI,GAC/B,OAEF7U,KAAM,CAAC,EACPqM,KAAMxJ,EAAMzH,KAAK4Z,SACjBM,OAAQhB,EAAYzR,EAAMzH,KAAK4Z,SAAUhZ,GACzC0I,OAAO6Q,EAAAA,EAAAA,IAAqB1S,EAAMzH,KAAK4Z,WAEzC,IAAI,MAGd,CAACpW,EAAc0V,GAChB,EEqJmBkB,CAAoC7B,GAEtD,OAAIU,GAEA3T,EAAAA,EAAAA,GAAA,OACEwD,KAAGO,EAAAA,EAAAA,IAAE,CACH0M,gBAAiB9O,EAAMsC,OAAOyM,oBAC9BW,WAAY1P,EAAM4C,QAAQsD,GAC1BkJ,UAAW,aAAapP,EAAMsC,OAAO+M,SACrCC,WAAY,aAAatP,EAAMsC,OAAO+M,SACtCE,KAAM,EACN6D,eAAgB,SAChBC,WAAY,SACZjN,QAAS,QACV,IAAClM,UAEFmE,EAAAA,EAAAA,GAACiV,EAAAA,EAAO,OAKZjV,EAAAA,EAAAA,GAACkV,EAAAA,GAAwC,CAAClG,oBAAqBd,EAAcrS,UAC3EmE,EAAAA,EAAAA,GAACoO,GAAmC,CAClCI,UAAWA,EACXC,QAASvB,EACTwB,oBAAqB6E,KAEkB,I,4BGpMb,IAAA5V,GAAA,CAAAN,KAAA,SAAAiE,OAAA,yDAEpC,MAAM6T,GAAoCA,KACxC,MAAM,aAAEnC,IAAiBoC,EAAAA,EAAAA,MACnB,MAAEzT,IAAUC,EAAAA,EAAAA,KACZyT,GAAWC,EAAAA,EAAAA,MACXjM,GAA6BkM,EAAAA,EAAAA,OAGjC5b,OAAO,cAAEkM,EAAa,WAAEC,EAAU,iBAAEyD,EAAgB,iBAAEiM,EAAgB,kBAAEC,EAAiB,uBAAEjU,GAAwB,YACnH5D,EAAW,kBACX8X,EAAiB,WACjBC,EAAU,oBACVC,EAAmB,qBACnBC,EAAoB,oBACpBC,EAAmB,kBACnBC,EAAiB,cACjBC,EAAa,sBACbC,IACEC,EAAAA,EAAAA,KAEJC,IAAUnD,EAAc,kCAExBzF,EAAAA,EAAAA,YAAU,OACH6I,EAAAA,EAAAA,OAAuCpD,GAC1CqC,EAASlF,EAAAA,EAAOkG,uBAAuBrD,GAAe,CAAEsD,SAAS,GACnE,GACC,CAACtD,EAAcqC,IAElB,MAAM,SAAE7L,EAAQ,YAAEC,GVlDgC8M,MAClD,MAAOnX,EAAQoX,IAAaC,EAAAA,EAAAA,MAS5B,MAAO,CAAEjN,UARQI,EAAAA,EAAAA,IACfrM,EACA6B,EAAOsX,IAAIlZ,GACXD,EAAkCsM,OAKjBJ,YAHEkN,IACnBH,EAAU,CAAE,CAAChZ,GAAwBmZ,GAAO,EAEd,EUwCEJ,IAoBhCzX,KAAMZ,EACN0Y,WAAYC,EACZC,UAAWC,EACXvd,MAAOwd,EAAiB,cACxBC,EAAa,gBACbC,IACEC,EAAAA,EAAAA,GAA2B,CAC7BC,cAAe,CAACpE,GAChBlN,aACAlI,cACA4D,4BA1B4B6V,MAC5B,IAAKxR,EACH,MAAO,CAAEyR,kBAAcpc,GAEzB,MAAMqc,GAA4B9R,EAAAA,EAAAA,IAAsCI,GACxE,OAAI0R,EAA0BC,eAAiBD,EAA0B/M,YAChE,CACL8M,aAAc,WAAWC,EAA0B7R,YACnD+R,mBAAoBF,EAA0B/M,YAC9CkN,qBAAsBH,EAA0BC,eAG7C,CAAEF,aAAczR,EAAe,EAenCwR,KAICM,EAAkBX,aAA6BY,GAAAA,GAAkBZ,OAAoB9b,GAEnF4D,KAAM+Y,IAAoBC,EAAAA,EAAAA,GAAkC,CAAE5Z,kBAEhE,WAAE8H,EAAU,kBAAE+R,IAAsBC,EAAAA,EAAAA,IAA6C,CACrF9Z,eACAqL,mBACAuN,UAAWC,EACXlR,gBACAC,aACAmS,uBAAwB5O,KAGnB6O,EAAgBC,KAAqBjS,EAAAA,EAAAA,UA7Ed,MA8EvBkS,GAAaC,KAAkBnS,EAAAA,EAAAA,WAAS,GAEzCoS,GAAqB9O,IAAajM,EAAkCsM,MAEpE0O,GACJD,IAAsBF,IACpBpY,EAAAA,EAAAA,GAAA,OAAKwD,KAAGO,EAAAA,EAAAA,IAAE,CAAE+D,MAAOnG,EAAM4C,QAAQqM,IAAI,OAErC5Q,EAAAA,EAAAA,GAACwY,EAAAA,EAAkC,CACjCxS,WAAYsS,GAAqBP,EAAoB/R,EACrD9H,aAA0B,OAAZA,QAAY,IAAZA,EAAAA,EAAgB,GAC9B4Y,UAAWC,EACX0B,cAAe5B,EACfc,gBAAiBA,EACjBe,qBAAsBxP,QAAQ+N,GAC9B0B,WAAYzB,EACZ0B,gBAAiBjD,EACjB7P,WAAYA,EACZD,cAAeA,EACf0D,iBAAkBA,EAClBsO,gBAAiBA,EACjBnC,kBAAmBA,IAIzB,OACE1V,EAAAA,EAAAA,GAAC6Y,EAAAA,GAAsD,CAAAhd,UACrD4E,EAAAA,EAAAA,IAACqY,EAAAA,GAAyD,CACxDC,cAAevD,EACfwD,eAAgBvD,EAChBI,qBAAsBA,EACtBC,oBAAqBA,EAAoBja,SAAA,EAEzCmE,EAAAA,EAAAA,GAACoJ,EAAqC,CACpCpD,WAAYA,EACZuD,iBAAkBA,EAClBxD,gBAAiB4P,EACjBrM,gBAAiBsM,EACjB/P,cAAeA,EACfC,WAAYA,EACZ0D,SAAUA,EACVC,YAAaA,EACb7L,YAAaA,EACbC,oBAAqBkY,EACrBjY,iBAA8B,OAAZI,QAAY,IAAZA,EAAAA,EAAgB,GAClCmL,2BAA4BA,EAC5B7H,uBAAwBA,EACxBC,gBAAiBuU,EACjBtU,wBAAyBuU,KAE3BjW,EAAAA,EAAAA,GAACiZ,EAAAA,EAAM,CAACC,KAAK,KAAKC,SAAS,KAET,OAAjBnC,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmBoC,WAAYzB,IAC9BlX,EAAAA,EAAAA,IAAA6C,EAAAA,GAAA,CAAAzH,SAAA,EACEmE,EAAAA,EAAAA,GAACqZ,EAAAA,IAAK,CACJzW,YAAY,kCACZwW,QAASpC,EAAkBoC,QAC3BhR,KAAK,QACLkR,UAAU,KAEZtZ,EAAAA,EAAAA,GAACiZ,EAAAA,EAAM,CAACC,KAAK,KAAKC,SAAS,OAG9Bb,IACCtY,EAAAA,EAAAA,GAACuZ,GAAAA,GAAqC,CAAA1d,UACpC4E,EAAAA,EAAAA,IAAA,OAAK+C,IAAG7F,GAAyE9B,SAAA,EAC/EmE,EAAAA,EAAAA,GAACwZ,EAAAA,EAA8B,CAC7BC,SAAUtB,GACVuB,cAAetB,GACftQ,MAAOoQ,EACPyB,eAAgBtB,GAAexc,SAE9B0c,KAEF/O,IAAajM,EAAkC+M,QAC9CtK,EAAAA,EAAAA,GAAC+S,GAA+B,CAAC7U,aAA0B,OAAZA,QAAY,IAAZA,EAAAA,EAAgB,GAAI8U,aAAcA,SAKvFuF,OAGmD,EAU7D,OANsCqB,KACpC5Z,EAAAA,EAAAA,GAAC6Z,EAAAA,EAAgC,CAAAhe,UAC/BmE,EAAAA,EAAAA,GAACmV,GAAiC,K,iFC/L/B,MAAM2E,GACoBC,EAAAA,EAAAA,YAAWC,EAAAA,kBAA0BA,EAAAA,iBAAyBC,EAAAA,Q,kHCC/F,MAAMC,EAAeC,GAA+D,CAClF,0CACA,CAAEA,YAGEC,EAAU5M,UAEmF,IADjG6M,UAAW,EAAE,QAAEF,KACiDxc,EAChE,IACE,MAAMmB,QAAawb,EAAAA,EAAcC,OAAO,CAAEC,OAAQL,IAClD,OAAW,OAAJrb,QAAI,IAAJA,OAAI,EAAJA,EAAM+P,GACf,CAAE,MAAOjL,GACP,OAAO,IACT,GAMWkU,EAAoCzW,IAAmE,IAADoZ,EAAA,IAAjE,aAAEvc,EAAe,IAA2CmD,EAC5G,MAAMqZ,GAAW1c,EAAAA,EAAAA,UAAQ,KAEvB,MAAM2c,GAAoB7G,EAAAA,EAAAA,SACZ,OAAZ5V,QAAY,IAAZA,OAAY,EAAZA,EAAc0Q,SAASrQ,IAAW,IAAAC,EAAAC,EAAA,OAAgB,OAAXF,QAAW,IAAXA,GAAiB,QAANC,EAAXD,EAAaO,YAAI,IAAAN,GAAS,QAATC,EAAjBD,EAAmBO,eAAO,IAAAN,OAAf,EAAXA,EAA4BoB,KAAKZ,GAAWA,EAAOub,QAAO,KAE7FI,GAAoB9G,EAAAA,EAAAA,SAAoB,OAAZ5V,QAAY,IAAZA,OAAY,EAAZA,EAAc2B,KAAKtB,IAAW,IAAAK,EAAA,OAAgB,OAAXL,QAAW,IAAXA,GAAiB,QAANK,EAAXL,EAAa7D,YAAI,IAAAkE,OAAN,EAAXA,EAAmBic,aAAa,KAGrG,OAFyBpT,EAAAA,EAAAA,SAAOqE,EAAAA,EAAAA,MAAK,IAAI6O,KAAsBC,IAExC,GACtB,CAAC1c,IAEE4c,GAAeC,EAAAA,EAAAA,GAAW,CAC9BC,QAASN,EAAS7a,KAAKsa,IAAO,CAC5BE,SAAUH,EAAYC,GACtBC,UACAa,UAAWC,IACXC,UAAWD,IACXE,sBAAsB,EACtBC,OAAO,QAILhO,EAAUyN,EAAa1f,MAAKwJ,IAAA,IAAC,UAAEkS,GAAWlS,EAAA,OAAKkS,CAAS,IACxDtd,EAA+C,QAA1CihB,EAAGK,EAAapO,MAAK/G,IAAA,IAAC,MAAEnM,GAAOmM,EAAA,OAAKnM,CAAK,WAAC,IAAAihB,OAAA,EAAvCA,EAAyCjhB,MAEjD8hB,GAAuBC,EAAAA,EAAAA,GAAaT,EAAajb,KAAI+I,IAAA,IAAC,KAAE9J,GAAM8J,EAAA,OAAK9J,CAAI,KAO7E,MAAO,CACLA,MANWd,EAAAA,EAAAA,UACX,IAAMsd,EAAqBzb,KAAKf,GAASA,IAAMW,OAAOyJ,UACtD,CAACoS,IAKDjO,UACA7T,QACD,C,8HC7D2C,IAAA6H,EAAA,CAAAhE,KAAA,UAAAiE,OAAA,iEAE9C,MAAMka,EAAe7d,IAAmC,IAAD8d,EAAA,IAAjC,MAAEjiB,GAA0BmE,EAChD,OACEqC,EAAAA,EAAAA,GAAC0b,EAAAA,IAAW,CAAClY,IAAGnC,EAA+ExF,UAC7FmE,EAAAA,EAAAA,GAACuQ,EAAAA,IAAK,CACJ,cAAY,WACZoL,OACE3b,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,UAInB4P,YACgB,QADLiL,EACJ,OAALjiB,QAAK,IAALA,OAAK,EAALA,EAAO4f,eAAO,IAAAqC,EAAAA,GACZzb,EAAAA,EAAAA,GAACU,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sDAKrBgb,OAAO5b,EAAAA,EAAAA,GAAC6b,EAAAA,EAAU,OAER,EAQLhC,EAAmCjV,IAMzC,IAN0C,SAC/C/I,EAAQ,SACRigB,GAIDlX,EACC,OACE5E,EAAAA,EAAAA,GAAC+b,EAAAA,GAAa,CAAChgB,kBAAmByf,EAAcxgB,UAAW,CAAC8gB,GAAUjgB,UACpEmE,EAAAA,EAAAA,GAACgc,EAAAA,GAAsB,CAAAngB,SAAEA,KACX,C", "sources": ["../node_modules/react-error-boundary/dist/src/ErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/ErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/assertErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/useErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/withErrorBoundary.ts", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelListPageMode.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListPageAutoComplete.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListPageDatasetDropdown.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListPageOrderBySelector.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListPageControls.tsx", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelsChartsData.tsx", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelsChartsUIState.tsx", "experiment-tracking/components/experiment-logged-models/ExperimentLoggedModelListCharts.tsx", "common/hooks/useMemoDeep.ts", "experiment-tracking/components/experiment-logged-models/hooks/useExperimentLoggedModelAllMetricsByDataset.tsx", "experiment-tracking/pages/experiment-logged-models/ExperimentLoggedModelListPage.tsx", "common/hooks/useSafeDeferredValue.ts", "experiment-tracking/hooks/logged-models/useRelatedRunsDataForLoggedModels.tsx", "experiment-tracking/pages/experiment-logged-models/ExperimentLoggedModelPageWrapper.tsx"], "sourcesContent": ["import { createContext } from \"react\";\n\nexport type ErrorBoundaryContextType = {\n  didCatch: boolean;\n  error: any;\n  resetErrorBoundary: (...args: any[]) => void;\n};\n\nexport const ErrorBoundaryContext =\n  createContext<ErrorBoundaryContextType | null>(null);\n", "import {\n  Component,\n  createElement,\n  ErrorInfo,\n  isValidElement,\n  PropsWithChildren,\n  PropsWithRef,\n  ReactElement,\n} from \"react\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\nimport { ErrorBoundaryProps, FallbackProps } from \"./types\";\n\ntype ErrorBoundaryState = { didCatch: boolean; error: any };\n\nconst initialState: ErrorBoundaryState = {\n  didCatch: false,\n  error: null,\n};\n\nexport class ErrorBoundary extends Component<\n  PropsWithRef<PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  state = initialState;\n\n  static getDerivedStateFromError(error: Error) {\n    return { didCatch: true, error };\n  }\n\n  resetErrorBoundary = (...args: any[]) => {\n    const { error } = this.state;\n\n    if (error !== null) {\n      this.props.onReset?.({\n        args,\n        reason: \"imperative-api\",\n      });\n\n      this.setState(initialState);\n    }\n  };\n\n  componentDidCatch(error: Error, info: ErrorInfo) {\n    this.props.onError?.(error, info);\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState\n  ) {\n    const { didCatch } = this.state;\n    const { resetKeys } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (\n      didCatch &&\n      prevState.error !== null &&\n      hasArrayChanged(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onReset?.({\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\",\n      });\n\n      this.setState(initialState);\n    }\n  }\n\n  render() {\n    const { children, fallbackRender, FallbackComponent, fallback } =\n      this.props;\n    const { didCatch, error } = this.state;\n\n    let childToRender = children;\n\n    if (didCatch) {\n      const props: FallbackProps = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      };\n\n      if (isValidElement(fallback)) {\n        childToRender = fallback;\n      } else if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else {\n        throw new Error(\n          \"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\"\n        );\n      }\n    }\n\n    return createElement(\n      ErrorBoundaryContext.Provider,\n      {\n        value: {\n          didCatch,\n          error,\n          resetErrorBoundary: this.resetErrorBoundary,\n        },\n      },\n      childToRender\n    ) as ReactElement;\n  }\n}\n\nfunction hasArrayChanged(a: any[] = [], b: any[] = []) {\n  return (\n    a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n  );\n}\n", "import { ErrorBoundaryContextType } from \"./ErrorBoundaryContext\";\n\nexport function assertErrorBoundaryContext(\n  value: any\n): value is ErrorBoundaryContextType {\n  if (\n    value == null ||\n    typeof value.didCatch !== \"boolean\" ||\n    typeof value.resetErrorBoundary !== \"function\"\n  ) {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n\n  return true;\n}\n", "import { useContext, useMemo, useState } from \"react\";\nimport { assertErrorBoundaryContext } from \"./assertErrorBoundaryContext\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\n\nexport type UseErrorBoundaryApi<Error> = {\n  resetBoundary: () => void;\n  showBoundary: (error: Error) => void;\n};\n\nexport function useErrorBoundary<Error = any>(): UseErrorBoundaryApi<Error> {\n  const context = useContext(ErrorBoundaryContext);\n\n  assertErrorBoundaryContext(context);\n\n  const [state, setState] = useState<{\n    error: Error | null;\n    hasError: boolean;\n  }>({\n    error: null,\n    hasError: false,\n  });\n\n  const memoized = useMemo(\n    () => ({\n      resetBoundary: () => {\n        context?.resetErrorBoundary();\n        setState({ error: null, hasError: false });\n      },\n      showBoundary: (error: Error) =>\n        setState({\n          error,\n          hasError: true,\n        }),\n    }),\n    [context?.resetErrorBoundary]\n  );\n\n  if (state.hasError) {\n    throw state.error;\n  }\n\n  return memoized;\n}\n", "import { ComponentType, createElement } from \"react\";\nimport { ErrorBoundary } from \"./ErrorBoundary\";\nimport { ErrorBoundaryProps } from \"./types\";\n\nexport function withErrorBoundary<Props extends Object>(\n  Component: ComponentType<Props>,\n  errorBoundaryProps: ErrorBoundaryProps\n): ComponentType<Props> {\n  const Wrapped: ComponentType<Props> = (props: Props) => {\n    return createElement(\n      ErrorBoundary,\n      errorBoundaryProps,\n      createElement(Component, props)\n    );\n  };\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || \"Unknown\";\n  Wrapped.displayName = `withErrorBoundary(${name})`;\n\n  return Wrapped;\n}\n", "import { coerceToEnum } from '@databricks/web-shared/utils';\nimport { useSearchParams } from '../../../../common/utils/RoutingUtils';\n\nexport enum ExperimentLoggedModelListPageMode {\n  TABLE = 'TABLE',\n  CHART = 'CHART',\n}\n\nconst VIEW_MODE_QUERY_PARAM = 'viewMode';\n\nexport const useExperimentLoggedModelListPageMode = () => {\n  const [params, setParams] = useSearchParams();\n  const viewMode = coerceToEnum(\n    ExperimentLoggedModelListPageMode,\n    params.get(VIEW_MODE_QUERY_PARAM),\n    ExperimentLoggedModelListPageMode.TABLE,\n  );\n  const setViewMode = (mode: ExperimentLoggedModelListPageMode) => {\n    setParams({ [VIEW_MODE_QUERY_PARAM]: mode });\n  };\n  return { viewMode, setViewMode } as const;\n};\n", "import { FormattedMessage } from 'react-intl';\nimport { EntitySearchAutoComplete } from '../EntitySearchAutoComplete';\nimport { LoggedModelProto } from '../../types';\nimport { useMemo } from 'react';\nimport {\n  EntitySearchAutoCompleteEntityNameGroup,\n  getEntitySearchOptionsFromEntityNames,\n} from '../EntitySearchAutoComplete.utils';\nimport { isUserFacingTag } from '../../../common/utils/TagUtils';\n\nconst getEntityNamesFromLoggedModelsData = (\n  loggedModels: LoggedModelProto[],\n): EntitySearchAutoCompleteEntityNameGroup => {\n  const metricNames = new Set<string>();\n  const paramNames = new Set<string>();\n  const tagNames = new Set<string>();\n\n  for (const loggedModel of loggedModels) {\n    loggedModel.data?.metrics?.forEach((metric) => metric.key && metricNames.add(metric.key));\n    loggedModel.data?.params?.forEach((param) => param.key && paramNames.add(param.key));\n    loggedModel.info?.tags?.forEach((tag) => tag.key && tagNames.add(tag.key));\n  }\n\n  return {\n    metricNames: Array.from(metricNames),\n    paramNames: Array.from(paramNames),\n    tagNames: Array.from(tagNames).filter(isUserFacingTag),\n  };\n};\n\nconst VALID_FILTER_ATTRIBUTES = [\n  'model_id',\n  'model_name',\n  'status',\n  'artifact_uri',\n  'creation_time',\n  'last_updated_time',\n];\n\nexport const ExperimentLoggedModelListPageAutoComplete = ({\n  searchQuery,\n  onChangeSearchQuery,\n  loggedModelsData,\n}: {\n  searchQuery?: string;\n  onChangeSearchQuery: (searchFilter: string) => void;\n  loggedModelsData: LoggedModelProto[];\n}) => {\n  const options = useMemo(() => {\n    const entityNames = getEntityNamesFromLoggedModelsData(loggedModelsData);\n    const validAttributeOptions = VALID_FILTER_ATTRIBUTES.map((attribute) => ({\n      value: `attributes.${attribute}`,\n    }));\n    return getEntitySearchOptionsFromEntityNames(entityNames, validAttributeOptions);\n  }, [loggedModelsData]);\n\n  return (\n    <EntitySearchAutoComplete\n      searchFilter={searchQuery ?? ''}\n      onSearchFilterChange={onChangeSearchQuery}\n      defaultActiveFirstOption={false}\n      baseOptions={options}\n      onClear={() => onChangeSearchQuery('')}\n      placeholder=\"metrics.rmse >= 0.8\"\n      tooltipContent={\n        <div>\n          <FormattedMessage\n            defaultMessage=\"Search logged models using a simplified version of the SQL {whereBold} clause.\"\n            description=\"Tooltip string to explain how to search logged models from the listing page\"\n            values={{ whereBold: <b>WHERE</b> }}\n          />{' '}\n          <br />\n          <FormattedMessage\n            defaultMessage=\"Examples:\"\n            description=\"Text header for examples of logged models search syntax\"\n          />\n          <br />\n          {'• metrics.rmse >= 0.8'}\n          <br />\n          {'• metrics.`f1 score` < 1'}\n          <br />\n          • params.type = 'tree'\n          <br />\n          • tags.my_tag = 'foobar'\n          <br />\n          • attributes.name = 'elasticnet'\n          <br />\n        </div>\n      }\n    />\n  );\n};\n", "import {\n  Button,\n  ChevronDownIcon,\n  DialogCombobox,\n  DialogComboboxContent,\n  DialogComboboxCountBadge,\n  DialogComboboxCustomButtonTriggerWrapper,\n  DialogComboboxOptionList,\n  DialogComboboxOptionListCheckboxItem,\n  TableIcon,\n  useDesignSystemTheme,\n  XCircleFillIcon,\n} from '@databricks/design-system';\nimport { useMemo, useRef } from 'react';\nimport type { LoggedModelMetricDataset, LoggedModelProto } from '../../types';\nimport { useIntl } from 'react-intl';\n\nconst getDatasetHash = (dataset: LoggedModelMetricDataset) =>\n  JSON.stringify([dataset.dataset_name, dataset.dataset_digest]);\n\nexport const ExperimentLoggedModelListPageDatasetDropdown = ({\n  loggedModelsData,\n  selectedFilterDatasets,\n  onToggleDataset,\n  onClearSelectedDatasets,\n}: {\n  loggedModelsData: LoggedModelProto[];\n  selectedFilterDatasets?: LoggedModelMetricDataset[];\n  onToggleDataset?: (dataset: LoggedModelMetricDataset) => void;\n  onClearSelectedDatasets?: () => void;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const intl = useIntl();\n\n  const cachedDatasets = useRef<Map<string, { hash: string } & LoggedModelMetricDataset>>(new Map());\n\n  // Get all datasets with their hashes, also store them in an aggregated map.\n  // The hash is used as a unique key and also being fed to DialogCombobox since it exclusively uses string values.\n  // The map is used to aggregate all datasets encountered in the logged models data during the session.\n  const allDatasets = useMemo(() => {\n    for (const model of loggedModelsData) {\n      for (const metric of model.data?.metrics || []) {\n        if (!metric.dataset_name || !metric.dataset_digest) {\n          continue;\n        }\n        const datasetHash = getDatasetHash(metric);\n        if (!cachedDatasets.current.has(datasetHash)) {\n          // We're purposely using mutable hashmap in the memo hook\n          cachedDatasets.current.set(datasetHash, {\n            hash: datasetHash,\n            dataset_name: metric.dataset_name,\n            dataset_digest: metric.dataset_digest,\n          });\n        }\n      }\n    }\n    return Array.from(cachedDatasets.current.values());\n  }, [loggedModelsData]);\n\n  // Serialize the selected datasets to a string format for the DialogCombobox.\n  const serializedSelectedDatasets = useMemo(\n    () => selectedFilterDatasets?.map(getDatasetHash) || [],\n    [selectedFilterDatasets],\n  );\n\n  return (\n    <DialogCombobox\n      componentId=\"mlflow.logged_model.list_page.datasets_filter\"\n      id=\"mlflow.logged_model.list_page.datasets_filter\"\n      value={serializedSelectedDatasets}\n      label={intl.formatMessage({\n        defaultMessage: 'Datasets',\n        description: 'Label for the datasets filter dropdown in the logged model list page',\n      })}\n      stayOpenOnSelection\n    >\n      <DialogComboboxCustomButtonTriggerWrapper>\n        <Button\n          endIcon={<ChevronDownIcon />}\n          componentId=\"mlflow.logged_model.list_page.datasets_filter.toggle\"\n          icon={<TableIcon />}\n        >\n          Datasets\n          {serializedSelectedDatasets.length > 0 ? (\n            <>\n              <DialogComboboxCountBadge css={{ marginLeft: 4 }}>\n                {serializedSelectedDatasets.length}\n              </DialogComboboxCountBadge>\n              <XCircleFillIcon\n                aria-hidden=\"false\"\n                role=\"button\"\n                onClick={(e) => {\n                  e.stopPropagation();\n                  e.preventDefault();\n                  onClearSelectedDatasets?.();\n                }}\n                css={{\n                  color: theme.colors.textPlaceholder,\n                  fontSize: theme.typography.fontSizeSm,\n                  marginLeft: theme.spacing.xs,\n\n                  ':hover': {\n                    color: theme.colors.actionTertiaryTextHover,\n                  },\n                }}\n              />\n            </>\n          ) : null}\n        </Button>\n      </DialogComboboxCustomButtonTriggerWrapper>\n      <DialogComboboxContent>\n        <DialogComboboxOptionList>\n          {allDatasets.map(({ hash: serializedDataset, dataset_digest, dataset_name }) => (\n            <DialogComboboxOptionListCheckboxItem\n              value={serializedDataset}\n              checked={serializedSelectedDatasets.includes(serializedDataset)}\n              key={serializedDataset}\n              onChange={() => onToggleDataset?.({ dataset_digest, dataset_name })}\n            >\n              {dataset_name} (#{dataset_digest})\n            </DialogComboboxOptionListCheckboxItem>\n          ))}\n        </DialogComboboxOptionList>\n      </DialogComboboxContent>\n    </DialogCombobox>\n  );\n};\n", "import {\n  ArrowDownIcon,\n  ArrowUpIcon,\n  Button,\n  DropdownMenu,\n  Input,\n  SearchIcon,\n  SortAscendingIcon,\n  SortDescendingIcon,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { useMemo, useState } from 'react';\n\nimport { defineMessage, FormattedMessage, IntlShape, MessageDescriptor, useIntl } from 'react-intl';\nimport { ToggleIconButton } from '../../../common/components/ToggleIconButton';\nimport {\n  ExperimentLoggedModelListPageKnownColumns,\n  LOGGED_MODEL_LIST_METRIC_COLUMN_PREFIX,\n  parseLoggedModelMetricOrderByColumnId,\n} from './hooks/useExperimentLoggedModelListPageTableColumns';\n\ninterface BasicColumnDef {\n  colId?: string;\n  groupId?: string;\n  headerName?: string;\n  children?: BasicColumnDef[];\n}\n\nconst getSortableColumnLabel = (colId: string | ExperimentLoggedModelListPageKnownColumns, intl: IntlShape) => {\n  const labels: Partial<Record<ExperimentLoggedModelListPageKnownColumns | string, MessageDescriptor>> = {\n    [ExperimentLoggedModelListPageKnownColumns.CreationTime]: defineMessage({\n      defaultMessage: 'Creation time',\n      description: 'Label for the creation time column in the logged model list page',\n    }),\n  };\n\n  const descriptor = labels[colId];\n\n  if (descriptor) {\n    return intl.formatMessage(descriptor);\n  }\n\n  const parsedColumn = parseLoggedModelMetricOrderByColumnId(colId);\n\n  if (parsedColumn) {\n    return parsedColumn.metricKey;\n  }\n\n  return colId;\n};\n\nexport const ExperimentLoggedModelListPageOrderBySelector = ({\n  orderByColumn,\n  orderByAsc,\n  onChangeOrderBy,\n  columnDefs = [],\n}: {\n  orderByColumn: string;\n  orderByAsc?: boolean;\n  onChangeOrderBy: (orderByColumn: string, orderByAsc: boolean) => void;\n  columnDefs: BasicColumnDef[] | undefined;\n}) => {\n  const intl = useIntl();\n  const [filter, setFilter] = useState('');\n  const { theme } = useDesignSystemTheme();\n\n  const groupedOrderByOptions = useMemo<BasicColumnDef[]>(() => {\n    const lowerCaseFilter = filter.toLowerCase();\n    const attributeColumnGroup = {\n      groupId: 'attributes',\n      headerName: intl.formatMessage({\n        defaultMessage: 'Attributes',\n        description: 'Label for the attributes column group in the logged model column selector',\n      }),\n      children: [\n        {\n          colId: ExperimentLoggedModelListPageKnownColumns.CreationTime,\n          headerName: getSortableColumnLabel(ExperimentLoggedModelListPageKnownColumns.CreationTime, intl),\n        },\n      ].filter(({ headerName }) => headerName?.toLowerCase().includes(lowerCaseFilter)),\n    };\n\n    // Next, get all the dataset-grouped metric column groups\n    const metricColumnGroups = columnDefs\n      .filter((col) => col.groupId?.startsWith(LOGGED_MODEL_LIST_METRIC_COLUMN_PREFIX))\n      .map((col) => ({\n        ...col,\n        children: col.children?.filter(({ colId }) => colId?.includes(lowerCaseFilter)),\n        headerName: col.headerName\n          ? `Metrics (${col.headerName})`\n          : intl.formatMessage({\n              defaultMessage: 'Metrics',\n              description: 'Label for the ungrouped metrics column group in the logged model column selector',\n            }),\n      }));\n\n    const sortableColumnGroups = [attributeColumnGroup, ...metricColumnGroups].filter(\n      (col) => col.children && col.children.length > 0,\n    );\n\n    // If the currently used sorting field is not found, this probably means that\n    // user has filtered out results containing this column. Let's add it to the list\n    // of sortable columns so that user won't be confused.\n    if (\n      !sortableColumnGroups.some((group) => group.children && group.children.some((col) => col.colId === orderByColumn))\n    ) {\n      const { metricKey } = parseLoggedModelMetricOrderByColumnId(orderByColumn);\n\n      if (metricKey) {\n        sortableColumnGroups.push({\n          groupId: 'current',\n          headerName: intl.formatMessage({\n            defaultMessage: 'Currently sorted by',\n            description: 'Label for the custom column group in the logged model column selector',\n          }),\n          children: [{ colId: orderByColumn, headerName: metricKey }],\n        });\n      }\n    }\n    return sortableColumnGroups;\n  }, [columnDefs, intl, filter, orderByColumn]);\n\n  return (\n    <DropdownMenu.Root modal={false}>\n      <DropdownMenu.Trigger asChild>\n        <Button\n          componentId=\"mlflow.logged_model.list.order_by\"\n          icon={orderByAsc ? <SortAscendingIcon /> : <SortDescendingIcon />}\n        >\n          <FormattedMessage\n            defaultMessage=\"Sort: {sortBy}\"\n            description=\"Label for the filter button in the logged model list page. sortBy is the name of the column by which the table is currently sorted.\"\n            values={{ sortBy: getSortableColumnLabel(orderByColumn, intl) }}\n          />\n        </Button>\n      </DropdownMenu.Trigger>\n      <DropdownMenu.Content css={{ maxHeight: 300, overflow: 'auto' }}>\n        <div\n          css={{\n            padding: `${theme.spacing.sm}px ${theme.spacing.lg / 2}px ${theme.spacing.sm}px`,\n            width: '100%',\n            display: 'flex',\n            gap: theme.spacing.xs,\n          }}\n        >\n          <Input\n            componentId=\"mlflow.logged_model.list.order_by.filter\"\n            prefix={<SearchIcon />}\n            value={filter}\n            type=\"search\"\n            onChange={(e) => setFilter(e.target.value)}\n            placeholder={intl.formatMessage({\n              defaultMessage: 'Search',\n              description: 'Placeholder for the search input in the logged model list page sort column selector',\n            })}\n            autoFocus\n            allowClear\n          />\n          <div\n            css={{\n              display: 'flex',\n              gap: theme.spacing.xs,\n            }}\n          >\n            <ToggleIconButton\n              pressed={!orderByAsc}\n              icon={<ArrowDownIcon />}\n              componentId=\"mlflow.logged_model.list.order_by.button_desc\"\n              onClick={() => onChangeOrderBy(orderByColumn, false)}\n              aria-label={intl.formatMessage({\n                defaultMessage: 'Sort descending',\n                description: 'Label for the sort descending button in the logged model list page',\n              })}\n            />\n            <ToggleIconButton\n              pressed={orderByAsc}\n              icon={<ArrowUpIcon />}\n              componentId=\"mlflow.logged_model.list.order_by.button_asc\"\n              onClick={() => onChangeOrderBy(orderByColumn, true)}\n              aria-label={intl.formatMessage({\n                defaultMessage: 'Sort ascending',\n                description: 'Label for the sort ascending button in the logged model list page',\n              })}\n            />\n          </div>\n        </div>\n\n        {groupedOrderByOptions.map(({ headerName, children, groupId }) => (\n          <DropdownMenu.Group key={groupId} aria-label={headerName}>\n            <DropdownMenu.Label>{headerName}</DropdownMenu.Label>\n            {children?.map(({ headerName: columnHeaderName, colId }) => (\n              <DropdownMenu.CheckboxItem\n                key={colId}\n                componentId=\"mlflow.logged_model.list.order_by.column_toggle\"\n                checked={orderByColumn === colId}\n                onClick={() => {\n                  if (!colId) {\n                    return;\n                  }\n                  onChangeOrderBy(colId, Boolean(orderByAsc));\n                }}\n              >\n                <DropdownMenu.ItemIndicator />\n                {columnHeaderName}\n              </DropdownMenu.CheckboxItem>\n            ))}\n          </DropdownMenu.Group>\n        ))}\n      </DropdownMenu.Content>\n    </DropdownMenu.Root>\n  );\n};\n", "import {\n  Button,\n  ChartLineIcon,\n  ListIcon,\n  SegmentedControlButton,\n  SegmentedControlGroup,\n  SortAscendingIcon,\n  SortDescendingIcon,\n  Tooltip,\n  useDesignSystemTheme,\n  visuallyHidden,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { FormattedMessage } from 'react-intl';\nimport type { ColDef, ColGroupDef } from '@ag-grid-community/core';\nimport { ExperimentLoggedModelListPageColumnSelector } from './ExperimentLoggedModelListPageColumnSelector';\nimport { coerceToEnum } from '@databricks/web-shared/utils';\nimport { ExperimentLoggedModelListPageMode } from './hooks/useExperimentLoggedModelListPageMode';\nimport { ExperimentLoggedModelListPageAutoComplete } from './ExperimentLoggedModelListPageAutoComplete';\nimport { LoggedModelMetricDataset, LoggedModelProto } from '../../types';\nimport { ExperimentLoggedModelListPageDatasetDropdown } from './ExperimentLoggedModelListPageDatasetDropdown';\nimport { ExperimentLoggedModelListPageOrderBySelector } from './ExperimentLoggedModelListPageOrderBySelector';\n\nexport const ExperimentLoggedModelListPageControls = ({\n  orderByColumn,\n  orderByAsc,\n  sortingAndFilteringEnabled,\n  onChangeOrderBy,\n  onUpdateColumns,\n  columnDefs,\n  columnVisibility = {},\n  viewMode,\n  setViewMode,\n  searchQuery = '',\n  onChangeSearchQuery,\n  loggedModelsData,\n  selectedFilterDatasets,\n  onToggleDataset,\n  onClearSelectedDatasets,\n}: {\n  orderByColumn?: string;\n  orderByAsc?: boolean;\n  sortingAndFilteringEnabled?: boolean;\n  onChangeOrderBy: (orderByColumn: string, orderByAsc: boolean) => void;\n  onUpdateColumns: (columnVisibility: Record<string, boolean>) => void;\n  columnDefs?: (ColDef | ColGroupDef)[];\n  columnVisibility?: Record<string, boolean>;\n  viewMode: ExperimentLoggedModelListPageMode;\n  setViewMode: (mode: ExperimentLoggedModelListPageMode) => void;\n  searchQuery?: string;\n  onChangeSearchQuery: (searchFilter: string) => void;\n  loggedModelsData: LoggedModelProto[];\n  selectedFilterDatasets?: LoggedModelMetricDataset[];\n  onToggleDataset?: (dataset: LoggedModelMetricDataset) => void;\n  onClearSelectedDatasets?: () => void;\n}) => {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <div css={{ display: 'flex', flexWrap: 'wrap', gap: theme.spacing.sm }}>\n      <SegmentedControlGroup\n        componentId=\"mlflow.logged_model.list.view-mode\"\n        name=\"view-mode\"\n        value={viewMode}\n        onChange={(e) => {\n          setViewMode(\n            coerceToEnum(ExperimentLoggedModelListPageMode, e.target.value, ExperimentLoggedModelListPageMode.TABLE),\n          );\n        }}\n      >\n        <SegmentedControlButton value=\"TABLE\">\n          <Tooltip\n            componentId=\"mlflow.logged_model.list.view-mode-table-tooltip\"\n            content={intl.formatMessage({\n              defaultMessage: 'Table view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          >\n            <ListIcon />\n          </Tooltip>\n          <span css={visuallyHidden}>\n            {intl.formatMessage({\n              defaultMessage: 'Table view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          </span>\n        </SegmentedControlButton>\n        <SegmentedControlButton value=\"CHART\">\n          <Tooltip\n            componentId=\"mlflow.logged_model.list.view-mode-chart-tooltip\"\n            content={intl.formatMessage({\n              defaultMessage: 'Chart view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          >\n            <ChartLineIcon />\n          </Tooltip>\n          <span css={visuallyHidden}>\n            {intl.formatMessage({\n              defaultMessage: 'Chart view',\n              description: 'Label for the table view toggle button in the logged model list page',\n            })}\n          </span>\n        </SegmentedControlButton>\n      </SegmentedControlGroup>\n      {sortingAndFilteringEnabled ? (\n        <>\n          <ExperimentLoggedModelListPageAutoComplete\n            searchQuery={searchQuery}\n            onChangeSearchQuery={onChangeSearchQuery}\n            loggedModelsData={loggedModelsData}\n          />\n          <ExperimentLoggedModelListPageDatasetDropdown\n            loggedModelsData={loggedModelsData}\n            onToggleDataset={onToggleDataset}\n            onClearSelectedDatasets={onClearSelectedDatasets}\n            selectedFilterDatasets={selectedFilterDatasets}\n          />\n          <ExperimentLoggedModelListPageOrderBySelector\n            orderByColumn={orderByColumn ?? ''}\n            orderByAsc={orderByAsc}\n            onChangeOrderBy={onChangeOrderBy}\n            columnDefs={columnDefs}\n          />\n        </>\n      ) : (\n        <Button\n          componentId=\"mlflow.logged_model.list.sort\"\n          icon={orderByAsc ? <SortAscendingIcon /> : <SortDescendingIcon />}\n          onClick={() => {\n            orderByColumn && onChangeOrderBy(orderByColumn, !orderByAsc);\n          }}\n        >\n          <FormattedMessage\n            defaultMessage=\"Sort: Created\"\n            description=\"Label for the sort button in the logged model list page\"\n          />\n        </Button>\n      )}\n      <ExperimentLoggedModelListPageColumnSelector\n        columnDefs={columnDefs}\n        columnVisibility={columnVisibility}\n        onUpdateColumns={onUpdateColumns}\n        disabled={viewMode === ExperimentLoggedModelListPageMode.CHART}\n      />\n    </div>\n  );\n};\n", "import { compact, keyBy } from 'lodash';\nimport { useMemo } from 'react';\nimport { LoggedModelProto } from '../../../types';\nimport { getStableColorForRun } from '../../../utils/RunNameUtils';\nimport type { RunsChartsRunData } from '../../runs-charts/components/RunsCharts.common';\nimport { useExperimentLoggedModelListPageRowVisibilityContext } from './useExperimentLoggedModelListPageRowVisibility';\n\nexport const getMetricByDatasetChartDataKey = (metricKey?: string, datasetName?: string) =>\n  datasetName ? JSON.stringify([datasetName, metricKey]) : metricKey ?? '';\n\n/**\n * Creates chart-consumable data based on logged models, including metrics and parameters.\n * TODO: optimize, add unit tests\n */\nexport const useExperimentLoggedModelsChartsData = (loggedModels: LoggedModelProto[]) => {\n  const { isRowHidden } = useExperimentLoggedModelListPageRowVisibilityContext();\n  return useMemo<RunsChartsRunData[]>(\n    () =>\n      compact(\n        loggedModels.map<RunsChartsRunData | null>((model, index) =>\n          model.info?.model_id\n            ? {\n                displayName: model.info?.name ?? model.info?.model_id ?? 'Unknown',\n                images: {},\n\n                metrics: keyBy(\n                  model.data?.metrics?.map(({ dataset_name, key, value, timestamp, step }) => ({\n                    // Instead of using plain metric key, we will use specific data access key generated based on metric key and dataset\n                    dataKey: getMetricByDatasetChartDataKey(key, dataset_name),\n                    key: key ?? '',\n                    value: value ?? 0,\n                    timestamp: timestamp ?? 0,\n                    step: step ?? 0,\n                  })),\n                  'dataKey',\n                ),\n                params: keyBy(\n                  model.data?.params\n                    ?.map(({ key, value }) => ({ key: key ?? '', value: value ?? '' }))\n                    .filter(({ key }) => key) ?? [],\n                  'key',\n                ),\n                tags: {},\n                uuid: model.info.model_id,\n                hidden: isRowHidden(model.info.model_id, index),\n                color: getStableColorForRun(model.info.model_id),\n              }\n            : null,\n        ),\n      ),\n    [loggedModels, isRowHidden],\n  );\n};\n", "import { useCallback, useEffect, useReducer, useState } from 'react';\nimport type { ExperimentRunsChartsUIConfiguration } from '../../experiment-page/models/ExperimentPageUIState';\nimport { ChartSectionConfig } from '../../../types';\nimport {\n  RunsChartsBarCardConfig,\n  RunsChartsCardConfig,\n  RunsChartsMetricByDatasetEntry,\n  RunsChartType,\n} from '../../runs-charts/runs-charts.types';\nimport { isEmpty, uniq } from 'lodash';\nimport { RunsChartsUIConfigurationSetter } from '../../runs-charts/hooks/useRunsChartsUIConfiguration';\n\ntype UpdateChartStateAction = { type: 'UPDATE'; stateSetter: RunsChartsUIConfigurationSetter };\ntype InitializeChartStateAction = { type: 'INITIALIZE'; initialConfig?: LoggedModelsChartsUIConfiguration };\ntype NewLoggedModelsStateAction = { type: 'METRICS_UPDATED'; metricsByDatasets: RunsChartsMetricByDatasetEntry[] };\n\ntype ChartsReducerAction = UpdateChartStateAction | NewLoggedModelsStateAction | InitializeChartStateAction;\n\ninterface LoggedModelsChartsUIConfiguration extends ExperimentRunsChartsUIConfiguration {\n  isDirty: boolean;\n}\n\nconst createLocalStorageKey = (storeIdentifier: string, version = 1) =>\n  `experiment-logged-models-charts-ui-state-v${version}-${storeIdentifier}`;\n\n/**\n * Generates a list of chart tiles based on logged models metrics and datasets.\n */\nconst getExperimentLoggedModelsPageChartSetup = (metricsByDatasets: RunsChartsMetricByDatasetEntry[]) => {\n  const compareRunCharts: RunsChartsBarCardConfig[] = metricsByDatasets.map(\n    ({ dataAccessKey, metricKey, datasetName }) => ({\n      deleted: false,\n      type: RunsChartType.BAR,\n      uuid: `autogen-${dataAccessKey}`,\n      metricSectionId: datasetName ? `autogen-${datasetName}` : 'default',\n      isGenerated: true,\n      metricKey,\n      dataAccessKey,\n      datasetName,\n      displayName: datasetName ? `(${datasetName}) ${metricKey}` : undefined,\n    }),\n  );\n\n  const compareRunSections: ChartSectionConfig[] = uniq(metricsByDatasets.map(({ datasetName }) => datasetName)).map(\n    (datasetName) => ({\n      display: true,\n      name: datasetName ?? 'Metrics',\n      uuid: datasetName ? `autogen-${datasetName}` : 'default',\n      isReordered: false,\n    }),\n  );\n\n  if (isEmpty(compareRunSections)) {\n    compareRunSections.push({\n      display: true,\n      name: 'Metrics',\n      uuid: 'default',\n      isReordered: false,\n    });\n  }\n\n  return {\n    compareRunCharts,\n    compareRunSections,\n  };\n};\n\n// Internal utility function  used to merge the current charts state with potentially incoming new charts and sections\nconst reconcileChartsAndSections = (\n  currentState: LoggedModelsChartsUIConfiguration,\n  newCharts: { compareRunCharts: RunsChartsCardConfig[]; compareRunSections: ChartSectionConfig[] },\n) => {\n  // If there are no charts / sections, or if the state is in pristine state, just set the new charts if they're not empty\n  if (!currentState.compareRunCharts || !currentState.compareRunSections || !currentState.isDirty) {\n    if (newCharts.compareRunCharts.length > 0 || newCharts.compareRunSections.length > 0) {\n      return {\n        ...currentState,\n        compareRunCharts: newCharts.compareRunCharts ?? [],\n        compareRunSections: newCharts.compareRunSections ?? [],\n      };\n    }\n  }\n\n  // Otherwise, detect new sections and charts and add them to the list\n  const newChartsToAdd = newCharts.compareRunCharts.filter(\n    (newChart) => !currentState.compareRunCharts?.find((chart) => chart.uuid === newChart.uuid),\n  );\n  const newSectionsToAdd = newCharts.compareRunSections.filter(\n    (newSection) =>\n      newChartsToAdd.find((newChart) => newChart.metricSectionId === newSection.uuid) &&\n      !currentState.compareRunSections?.find((section) => section.uuid === newSection.uuid),\n  );\n\n  if (newSectionsToAdd.length > 0 || newChartsToAdd.length > 0) {\n    return {\n      ...currentState,\n      compareRunCharts: currentState.compareRunCharts\n        ? [...currentState.compareRunCharts, ...newChartsToAdd]\n        : newCharts.compareRunCharts,\n      compareRunSections: currentState.compareRunSections\n        ? [...currentState.compareRunSections, ...newSectionsToAdd]\n        : newCharts.compareRunSections,\n    };\n  }\n  return currentState;\n};\n\nconst chartsUIStateInitializer = (): LoggedModelsChartsUIConfiguration => ({\n  compareRunCharts: undefined,\n  compareRunSections: undefined,\n  autoRefreshEnabled: false,\n  isAccordionReordered: false,\n  chartsSearchFilter: '',\n  globalLineChartConfig: undefined,\n  isDirty: false,\n});\n\n// Reducer to manage the state of the charts UI\nconst chartsUIStateReducer = (state: LoggedModelsChartsUIConfiguration, action: ChartsReducerAction) => {\n  // 'UPDATE' is sent by controls that updates the UI state directly\n  if (action.type === 'UPDATE') {\n    return { ...action.stateSetter(state), isDirty: true };\n  }\n  // 'METRICS_UPDATED' is sent when new logged models data is available and potentially new charts need to be added\n  if (action.type === 'METRICS_UPDATED') {\n    const { compareRunCharts, compareRunSections } = getExperimentLoggedModelsPageChartSetup(action.metricsByDatasets);\n    const newState = reconcileChartsAndSections(state, { compareRunCharts, compareRunSections });\n    return newState;\n  }\n  if (action.type === 'INITIALIZE') {\n    if (action.initialConfig) {\n      return action.initialConfig;\n    }\n  }\n  return state;\n};\n\nconst loadPersistedDataFromStorage = async (storeIdentifier: string) => {\n  // This function is async on purpose to accommodate potential asynchoronous storage mechanisms (e.g. IndexedDB) in the future\n  const serializedData = localStorage.getItem(createLocalStorageKey(storeIdentifier));\n  if (!serializedData) {\n    return undefined;\n  }\n  try {\n    return JSON.parse(serializedData);\n  } catch {\n    return undefined;\n  }\n};\n\nconst saveDataToStorage = async (storeIdentifier: string, dataToPersist: LoggedModelsChartsUIConfiguration) => {\n  localStorage.setItem(createLocalStorageKey(storeIdentifier), JSON.stringify(dataToPersist));\n};\n\nexport const useExperimentLoggedModelsChartsUIState = (\n  metricsByDatasets: RunsChartsMetricByDatasetEntry[],\n  storeIdentifier: string,\n) => {\n  const [chartUIState, dispatchChartUIState] = useReducer(chartsUIStateReducer, undefined, chartsUIStateInitializer);\n  const [loading, setLoading] = useState(true);\n\n  // Attempt to load the persisted data when the component mounts\n  useEffect(() => {\n    setLoading(true);\n    loadPersistedDataFromStorage(storeIdentifier).then((data) => {\n      dispatchChartUIState({ type: 'INITIALIZE', initialConfig: data });\n      setLoading(false);\n    });\n  }, [storeIdentifier]);\n\n  // Attempt to update the charts state when the logged models change\n  useEffect(() => {\n    if (loading) {\n      return;\n    }\n    dispatchChartUIState({ type: 'METRICS_UPDATED', metricsByDatasets });\n  }, [metricsByDatasets, loading]);\n\n  // Attempt persist the data when the state changes\n  useEffect(() => {\n    if (chartUIState.isDirty) {\n      saveDataToStorage(storeIdentifier, chartUIState);\n    }\n  }, [storeIdentifier, chartUIState]);\n\n  // Create an updater function to pass it to chart controls\n  const updateUIState = useCallback(\n    (stateSetter: RunsChartsUIConfigurationSetter) =>\n      dispatchChartUIState({\n        type: 'UPDATE',\n        stateSetter,\n      }),\n    [],\n  );\n\n  return { chartUIState, updateUIState, loading };\n};\n", "import { Empty, Input, SearchIcon, Spinner, useDesignSystemTheme } from '@databricks/design-system';\nimport { noop, uniq } from 'lodash';\nimport { memo, ReactNode, useMemo, useCallback, useState } from 'react';\nimport { LoggedModelProto } from '../../types';\nimport { ExperimentRunsChartsUIConfiguration } from '../experiment-page/models/ExperimentPageUIState';\nimport { RunsChartsRunData } from '../runs-charts/components/RunsCharts.common';\nimport { RunsChartsDraggableCardsGridContextProvider } from '../runs-charts/components/RunsChartsDraggableCardsGridContext';\nimport { RunsChartsFullScreenModal } from '../runs-charts/components/RunsChartsFullScreenModal';\nimport { RunsChartsTooltipBody } from '../runs-charts/components/RunsChartsTooltipBody';\nimport { RunsChartsSectionAccordion } from '../runs-charts/components/sections/RunsChartsSectionAccordion';\nimport { RunsChartsTooltipWrapper } from '../runs-charts/hooks/useRunsChartsTooltip';\nimport {\n  RunsChartsUIConfigurationContextProvider,\n  useConfirmChartCardConfigurationFn,\n  useRemoveRunsChartFn,\n  useUpdateRunsChartsUIConfiguration,\n} from '../runs-charts/hooks/useRunsChartsUIConfiguration';\nimport { RunsChartsCardConfig, RunsChartsMetricByDatasetEntry, RunsChartType } from '../runs-charts/runs-charts.types';\nimport { useExperimentLoggedModelsChartsData } from './hooks/useExperimentLoggedModelsChartsData';\nimport { useExperimentLoggedModelsChartsUIState } from './hooks/useExperimentLoggedModelsChartsUIState';\nimport { useExperimentLoggedModelAllMetricsByDataset } from './hooks/useExperimentLoggedModelAllMetricsByDataset';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { useMemoDeep } from '../../../common/hooks/useMemoDeep';\nimport { RunsChartsConfigureModal } from '../runs-charts/components/RunsChartsConfigureModal';\nimport Routes from '../../routes';\n\nconst ExperimentLoggedModelListChartsImpl = memo(\n  ({\n    chartData,\n    uiState,\n    metricKeysByDataset,\n  }: {\n    chartData: RunsChartsRunData[];\n    uiState: ExperimentRunsChartsUIConfiguration;\n    metricKeysByDataset: RunsChartsMetricByDatasetEntry[];\n  }) => {\n    const { theme } = useDesignSystemTheme();\n    const { formatMessage } = useIntl();\n\n    const availableMetricKeys = useMemo(() => uniq(chartData.flatMap((run) => Object.keys(run.metrics))), [chartData]);\n    const availableParamKeys = useMemo(() => uniq(chartData.flatMap((run) => Object.keys(run.params))), [chartData]);\n\n    const updateChartsUIState = useUpdateRunsChartsUIConfiguration();\n\n    const setSearch = useCallback(\n      (search: string) => {\n        updateChartsUIState((state) => ({ ...state, chartsSearchFilter: search }));\n      },\n      [updateChartsUIState],\n    );\n\n    const [configuredCardConfig, setConfiguredCardConfig] = useState<RunsChartsCardConfig | null>(null);\n\n    const addNewChartCard = useCallback(\n      (metricSectionId: string) => (type: RunsChartType) =>\n        setConfiguredCardConfig(RunsChartsCardConfig.getEmptyChartCardByType(type, false, undefined, metricSectionId)),\n      [],\n    );\n\n    const confirmChartCardConfiguration = useConfirmChartCardConfigurationFn();\n    const removeChart = useRemoveRunsChartFn();\n\n    const [fullScreenChart, setFullScreenChart] = useState<\n      | {\n          config: RunsChartsCardConfig;\n          title: string | ReactNode;\n          subtitle: ReactNode;\n        }\n      | undefined\n    >(undefined);\n\n    const fullscreenTooltipContextValue = useMemo(() => ({ runs: chartData }), [chartData]);\n\n    const tooltipContextValue = useMemo(\n      () => ({ runs: chartData, getDataTraceLink: Routes.getExperimentLoggedModelDetailsPageRoute }),\n      [chartData],\n    );\n\n    const emptyState = (\n      <div css={{ marginTop: theme.spacing.lg }}>\n        <Empty\n          description={\n            <FormattedMessage\n              defaultMessage=\"No models found in experiment or all models are hidden. Select at least one model to view charts.\"\n              description=\"Label displayed in logged models chart view when no models are visible or selected\"\n            />\n          }\n        />\n      </div>\n    );\n\n    return (\n      <div\n        css={{\n          backgroundColor: theme.colors.backgroundSecondary,\n          paddingLeft: theme.spacing.md,\n          paddingRight: theme.spacing.md,\n          paddingBottom: theme.spacing.md,\n\n          borderTop: `1px solid ${theme.colors.border}`,\n          borderLeft: `1px solid ${theme.colors.border}`,\n\n          flex: 1,\n          overflow: 'hidden',\n          display: 'flex',\n        }}\n      >\n        <div\n          css={{\n            display: 'flex',\n            flexDirection: 'column',\n            gap: theme.spacing.sm,\n            paddingTop: theme.spacing.sm,\n            overflow: 'hidden',\n            flex: 1,\n          }}\n        >\n          <Input\n            componentId=\"mlflow.logged_model.list.charts.search\"\n            role=\"searchbox\"\n            prefix={<SearchIcon />}\n            value={uiState.chartsSearchFilter ?? ''}\n            allowClear\n            onChange={({ target }) => setSearch(target.value)}\n            placeholder={formatMessage({\n              defaultMessage: 'Search metric charts',\n              description: 'Placeholder for chart search input on the logged model chart view',\n            })}\n          />\n          <div css={{ overflow: 'auto' }}>\n            <RunsChartsTooltipWrapper contextData={tooltipContextValue} component={RunsChartsTooltipBody}>\n              <RunsChartsDraggableCardsGridContextProvider visibleChartCards={uiState.compareRunCharts}>\n                <RunsChartsSectionAccordion\n                  compareRunSections={uiState.compareRunSections}\n                  compareRunCharts={uiState.compareRunCharts}\n                  reorderCharts={noop}\n                  insertCharts={noop}\n                  chartData={chartData}\n                  startEditChart={setConfiguredCardConfig}\n                  removeChart={removeChart}\n                  addNewChartCard={addNewChartCard}\n                  search={uiState.chartsSearchFilter ?? ''}\n                  groupBy={null}\n                  setFullScreenChart={setFullScreenChart}\n                  autoRefreshEnabled={false}\n                  hideEmptyCharts={false}\n                  globalLineChartConfig={undefined}\n                  supportedChartTypes={[RunsChartType.BAR, RunsChartType.SCATTER]}\n                  noRunsSelectedEmptyState={emptyState}\n                />\n              </RunsChartsDraggableCardsGridContextProvider>\n            </RunsChartsTooltipWrapper>\n            <RunsChartsFullScreenModal\n              fullScreenChart={fullScreenChart}\n              onCancel={() => setFullScreenChart(undefined)}\n              chartData={chartData}\n              groupBy={null}\n              tooltipContextValue={fullscreenTooltipContextValue}\n              tooltipComponent={RunsChartsTooltipBody}\n              autoRefreshEnabled={false}\n              globalLineChartConfig={undefined}\n            />\n            {configuredCardConfig && (\n              <RunsChartsConfigureModal\n                chartRunData={chartData}\n                metricKeyList={availableMetricKeys}\n                metricKeysByDataset={metricKeysByDataset}\n                paramKeyList={availableParamKeys}\n                config={configuredCardConfig}\n                onSubmit={(configuredCardConfig) => {\n                  confirmChartCardConfiguration({ ...configuredCardConfig, displayName: undefined });\n                  setConfiguredCardConfig(null);\n                }}\n                onCancel={() => setConfiguredCardConfig(null)}\n                groupBy={null}\n                supportedChartTypes={[RunsChartType.BAR, RunsChartType.SCATTER]}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  },\n);\n\nexport const ExperimentLoggedModelListCharts = memo(\n  ({ loggedModels, experimentId }: { loggedModels: LoggedModelProto[]; experimentId: string }) => {\n    const { theme } = useDesignSystemTheme();\n\n    // Perform deep comparison on the logged models to avoid re-rendering the charts when the logged models change.\n    // Deep comparison should still be cheaper than rerendering all charts.\n    const cachedLoggedModels = useMemoDeep(() => loggedModels, [loggedModels]);\n\n    const metricsByDataset = useExperimentLoggedModelAllMetricsByDataset(cachedLoggedModels);\n\n    const {\n      chartUIState,\n      updateUIState,\n      loading: loadingState,\n    } = useExperimentLoggedModelsChartsUIState(metricsByDataset, experimentId);\n    const chartData = useExperimentLoggedModelsChartsData(cachedLoggedModels);\n\n    if (loadingState) {\n      return (\n        <div\n          css={{\n            backgroundColor: theme.colors.backgroundSecondary,\n            paddingTop: theme.spacing.lg,\n            borderTop: `1px solid ${theme.colors.border}`,\n            borderLeft: `1px solid ${theme.colors.border}`,\n            flex: 1,\n            justifyContent: 'center',\n            alignItems: 'center',\n            display: 'flex',\n          }}\n        >\n          <Spinner />\n        </div>\n      );\n    }\n    return (\n      <RunsChartsUIConfigurationContextProvider updateChartsUIState={updateUIState}>\n        <ExperimentLoggedModelListChartsImpl\n          chartData={chartData}\n          uiState={chartUIState}\n          metricKeysByDataset={metricsByDataset}\n        />\n      </RunsChartsUIConfigurationContextProvider>\n    );\n  },\n);\n", "import { isEqual } from 'lodash';\nimport { useRef } from 'react';\n\n/**\n * Utility hook that memoizes value based on deep comparison.\n * Dedicated to a few limited use cases where deep comparison is still cheaper than resulting re-renders.\n */\nexport const useMemoDeep = <T>(factory: () => T, deps: unknown[]): T => {\n  const ref = useRef<{ deps: unknown[]; value: T }>();\n\n  if (!ref.current || !isEqual(deps, ref.current.deps)) {\n    ref.current = { deps, value: factory() };\n  }\n\n  return ref.current.value;\n};\n", "import { orderBy } from 'lodash';\nimport type { LoggedModelProto } from '../../../types';\nimport { useMemo } from 'react';\nimport type { RunsChartsMetricByDatasetEntry } from '../../runs-charts/runs-charts.types';\nimport { getMetricByDatasetChartDataKey } from './useExperimentLoggedModelsChartsData';\n\nexport const useExperimentLoggedModelAllMetricsByDataset = (loggedModels: LoggedModelProto[]) =>\n  useMemo(() => {\n    const metricsByDataset: RunsChartsMetricByDatasetEntry[] = [];\n    loggedModels.forEach((model) => {\n      model.data?.metrics?.forEach(({ key: metricKey, dataset_name: datasetName }) => {\n        if (metricKey && !metricsByDataset.find((e) => e.metricKey === metricKey && e.datasetName === datasetName)) {\n          const dataAccessKey = getMetricByDatasetChartDataKey(metricKey, datasetName);\n          metricsByDataset.push({ metricKey, datasetName, dataAccessKey });\n        }\n      });\n    });\n    return orderBy(metricsByDataset, ({ datasetName }) => !datasetName);\n  }, [loggedModels]);\n", "import { Alert, Spacer, useDesignSystemTheme } from '@databricks/design-system';\nimport invariant from 'invariant';\nimport { useNavigate, useParams } from '../../../common/utils/RoutingUtils';\nimport Routes from '../../routes';\nimport { ExperimentLoggedModelPageWrapper } from './ExperimentLoggedModelPageWrapper';\n\nimport {\n  isExperimentLoggedModelsUIEnabled,\n  isLoggedModelsFilteringAndSortingEnabled,\n} from '../../../common/utils/FeatureUtils';\nimport { useEffect, useState } from 'react';\nimport { ExperimentLoggedModelListPageTable } from '../../components/experiment-logged-models/ExperimentLoggedModelListPageTable';\nimport { useSearchLoggedModelsQuery } from '../../hooks/logged-models/useSearchLoggedModelsQuery';\nimport { ExperimentLoggedModelListPageControls } from '../../components/experiment-logged-models/ExperimentLoggedModelListPageControls';\nimport {\n  parseLoggedModelMetricOrderByColumnId,\n  useExperimentLoggedModelListPageTableColumns,\n} from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelListPageTableColumns';\nimport { ExperimentLoggedModelOpenDatasetDetailsContextProvider } from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelOpenDatasetDetails';\nimport { useLoggedModelsListPageState } from '../../components/experiment-logged-models/hooks/useLoggedModelsListPagePageState';\nimport { useRelatedRunsDataForLoggedModels } from '../../hooks/logged-models/useRelatedRunsDataForLoggedModels';\nimport {\n  ExperimentLoggedModelListPageMode,\n  useExperimentLoggedModelListPageMode,\n} from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelListPageMode';\nimport { ExperimentViewRunsTableResizer } from '../../components/experiment-page/components/runs/ExperimentViewRunsTableResizer';\nimport { ExperimentLoggedModelListCharts } from '../../components/experiment-logged-models/ExperimentLoggedModelListCharts';\nimport { ExperimentLoggedModelListPageRowVisibilityContextProvider } from '../../components/experiment-logged-models/hooks/useExperimentLoggedModelListPageRowVisibility';\nimport { RunsChartsSetHighlightContextProvider } from '../../components/runs-charts/hooks/useRunsChartTraceHighlight';\nimport { BadRequestError } from '@databricks/web-shared/errors';\n\nconst INITIAL_RUN_COLUMN_SIZE = 295;\n\nconst ExperimentLoggedModelListPageImpl = () => {\n  const { experimentId } = useParams();\n  const { theme } = useDesignSystemTheme();\n  const navigate = useNavigate();\n  const sortingAndFilteringEnabled = isLoggedModelsFilteringAndSortingEnabled();\n\n  const {\n    state: { orderByColumn, orderByAsc, columnVisibility, rowVisibilityMap, rowVisibilityMode, selectedFilterDatasets },\n    searchQuery,\n    isFilteringActive,\n    setOrderBy,\n    setColumnVisibility,\n    setRowVisibilityMode,\n    toggleRowVisibility,\n    updateSearchQuery,\n    toggleDataset,\n    clearSelectedDatasets,\n  } = useLoggedModelsListPageState();\n\n  invariant(experimentId, 'Experiment ID must be defined');\n\n  useEffect(() => {\n    if (!isExperimentLoggedModelsUIEnabled() && experimentId) {\n      navigate(Routes.getExperimentPageRoute(experimentId), { replace: true });\n    }\n  }, [experimentId, navigate]);\n\n  const { viewMode, setViewMode } = useExperimentLoggedModelListPageMode();\n\n  // Translate currently sorting column to the format accepted by the API query.\n  // If the column is a metric, we need to parse and pass the dataset name and digest if found.\n  const getOrderByRequestData = () => {\n    if (!orderByColumn) {\n      return { orderByField: undefined };\n    }\n    const parsedMetricOrderByColumn = parseLoggedModelMetricOrderByColumnId(orderByColumn);\n    if (parsedMetricOrderByColumn.datasetDigest && parsedMetricOrderByColumn.datasetName) {\n      return {\n        orderByField: `metrics.${parsedMetricOrderByColumn.metricKey}`,\n        orderByDatasetName: parsedMetricOrderByColumn.datasetName,\n        orderByDatasetDigest: parsedMetricOrderByColumn.datasetDigest,\n      };\n    }\n    return { orderByField: orderByColumn };\n  };\n\n  const {\n    data: loggedModels,\n    isFetching: isFetchingLoggedModels,\n    isLoading: isLoadingLoggedModels,\n    error: loggedModelsError,\n    nextPageToken,\n    loadMoreResults,\n  } = useSearchLoggedModelsQuery({\n    experimentIds: [experimentId],\n    orderByAsc,\n    searchQuery,\n    selectedFilterDatasets,\n    ...getOrderByRequestData(),\n  });\n\n  // Find and extract 400 error from the logged models error\n  const badRequestError = loggedModelsError instanceof BadRequestError ? loggedModelsError : undefined;\n\n  const { data: relatedRunsData } = useRelatedRunsDataForLoggedModels({ loggedModels });\n\n  const { columnDefs, compactColumnDefs } = useExperimentLoggedModelListPageTableColumns({\n    loggedModels,\n    columnVisibility,\n    isLoading: isLoadingLoggedModels,\n    orderByColumn,\n    orderByAsc,\n    enableSortingByMetrics: sortingAndFilteringEnabled,\n  });\n\n  const [tableAreaWidth, setTableAreaWidth] = useState<number>(INITIAL_RUN_COLUMN_SIZE);\n  const [tableHidden, setTableHidden] = useState(false);\n\n  const isCompactTableMode = viewMode !== ExperimentLoggedModelListPageMode.TABLE;\n\n  const tableElement =\n    isCompactTableMode && tableHidden ? (\n      <div css={{ width: theme.spacing.md }} />\n    ) : (\n      <ExperimentLoggedModelListPageTable\n        columnDefs={isCompactTableMode ? compactColumnDefs : columnDefs}\n        loggedModels={loggedModels ?? []}\n        isLoading={isLoadingLoggedModels}\n        isLoadingMore={isFetchingLoggedModels}\n        badRequestError={badRequestError}\n        moreResultsAvailable={Boolean(nextPageToken)}\n        onLoadMore={loadMoreResults}\n        onOrderByChange={setOrderBy}\n        orderByAsc={orderByAsc}\n        orderByColumn={orderByColumn}\n        columnVisibility={columnVisibility}\n        relatedRunsData={relatedRunsData}\n        isFilteringActive={isFilteringActive}\n      />\n    );\n\n  return (\n    <ExperimentLoggedModelOpenDatasetDetailsContextProvider>\n      <ExperimentLoggedModelListPageRowVisibilityContextProvider\n        visibilityMap={rowVisibilityMap}\n        visibilityMode={rowVisibilityMode}\n        setRowVisibilityMode={setRowVisibilityMode}\n        toggleRowVisibility={toggleRowVisibility}\n      >\n        <ExperimentLoggedModelListPageControls\n          columnDefs={columnDefs}\n          columnVisibility={columnVisibility}\n          onChangeOrderBy={setOrderBy}\n          onUpdateColumns={setColumnVisibility}\n          orderByColumn={orderByColumn}\n          orderByAsc={orderByAsc}\n          viewMode={viewMode}\n          setViewMode={setViewMode}\n          searchQuery={searchQuery}\n          onChangeSearchQuery={updateSearchQuery}\n          loggedModelsData={loggedModels ?? []}\n          sortingAndFilteringEnabled={sortingAndFilteringEnabled}\n          selectedFilterDatasets={selectedFilterDatasets}\n          onToggleDataset={toggleDataset}\n          onClearSelectedDatasets={clearSelectedDatasets}\n        />\n        <Spacer size=\"sm\" shrinks={false} />\n        {/* Display error message, but not if it's 400 - in that case, the error message is displayed in the table */}\n        {loggedModelsError?.message && !badRequestError && (\n          <>\n            <Alert\n              componentId=\"mlflow.logged_models.list.error\"\n              message={loggedModelsError.message}\n              type=\"error\"\n              closable={false}\n            />\n            <Spacer size=\"sm\" shrinks={false} />\n          </>\n        )}\n        {isCompactTableMode ? (\n          <RunsChartsSetHighlightContextProvider>\n            <div css={{ display: 'flex', flex: 1, overflow: 'hidden', position: 'relative' }}>\n              <ExperimentViewRunsTableResizer\n                onResize={setTableAreaWidth}\n                runListHidden={tableHidden}\n                width={tableAreaWidth}\n                onHiddenChange={setTableHidden}\n              >\n                {tableElement}\n              </ExperimentViewRunsTableResizer>\n              {viewMode === ExperimentLoggedModelListPageMode.CHART && (\n                <ExperimentLoggedModelListCharts loggedModels={loggedModels ?? []} experimentId={experimentId} />\n              )}\n            </div>\n          </RunsChartsSetHighlightContextProvider>\n        ) : (\n          tableElement\n        )}\n      </ExperimentLoggedModelListPageRowVisibilityContextProvider>\n    </ExperimentLoggedModelOpenDatasetDetailsContextProvider>\n  );\n};\n\nconst ExperimentLoggedModelListPage = () => (\n  <ExperimentLoggedModelPageWrapper>\n    <ExperimentLoggedModelListPageImpl />\n  </ExperimentLoggedModelPageWrapper>\n);\n\nexport default ExperimentLoggedModelListPage;\n", "import { identity, isFunction } from 'lodash';\nimport React from 'react';\n\n/**\n * A safe version of `useDeferredValue` that falls back to identity (A->A) if `useDeferredValue` is not supported\n * by current React version.\n */\nexport const useSafeDeferredValue: <T>(value: T) => T =\n  'useDeferredValue' in React && isFunction(React.useDeferredValue) ? React.useDeferredValue : identity;\n", "import type { LoggedModelProto, RunEntity } from '../../types';\nimport { useEffect, useMemo } from 'react';\nimport { compact, sortBy, uniq } from 'lodash';\nimport { QueryFunctionContext, useQueries } from '@mlflow/mlflow/src/common/utils/reactQueryHooks';\nimport { MlflowService } from '../../sdk/MlflowService';\nimport { useArrayMemo } from '../../../common/hooks/useArrayMemo';\n\ntype UseRegisteredModelRelatedRunNamesQueryKey = ['USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS', { runUuid: string }];\n\nconst getQueryKey = (runUuid: string): UseRegisteredModelRelatedRunNamesQueryKey => [\n  'USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS',\n  { runUuid },\n];\n\nconst queryFn = async ({\n  queryKey: [, { runUuid }],\n}: QueryFunctionContext<UseRegisteredModelRelatedRunNamesQueryKey>): Promise<RunEntity | null> => {\n  try {\n    const data = await MlflowService.getRun({ run_id: runUuid });\n    return data?.run;\n  } catch (e) {\n    return null;\n  }\n};\n\n/**\n * Hook used to fetch necessary run data based on metadata found in logged models\n */\nexport const useRelatedRunsDataForLoggedModels = ({ loggedModels = [] }: { loggedModels?: LoggedModelProto[] }) => {\n  const runUuids = useMemo(() => {\n    // Extract all run ids found in metrics and source run ids\n    const allMetricRunUuids = compact(\n      loggedModels?.flatMap((loggedModel) => loggedModel?.data?.metrics?.map((metric) => metric.run_id)),\n    );\n    const allSourceRunUuids = compact(loggedModels?.map((loggedModel) => loggedModel?.info?.source_run_id));\n    const distinctRunUuids = sortBy(uniq([...allMetricRunUuids, ...allSourceRunUuids]));\n\n    return distinctRunUuids;\n  }, [loggedModels]);\n\n  const queryResults = useQueries({\n    queries: runUuids.map((runUuid) => ({\n      queryKey: getQueryKey(runUuid),\n      queryFn,\n      cacheTime: Infinity,\n      staleTime: Infinity,\n      refetchOnWindowFocus: false,\n      retry: false,\n    })),\n  });\n\n  const loading = queryResults.some(({ isLoading }) => isLoading);\n  const error = queryResults.find(({ error }) => error)?.error as Error | undefined;\n\n  const memoizedQueryResults = useArrayMemo(queryResults.map(({ data }) => data));\n\n  const data = useMemo(\n    () => memoizedQueryResults.map((data) => data).filter(Boolean) as RunEntity[],\n    [memoizedQueryResults],\n  );\n\n  return {\n    data,\n    loading,\n    error,\n  };\n};\n", "import { UserActionErrorHandler } from '@databricks/web-shared/metrics';\nimport { QueryClient, QueryClientProvider } from '@mlflow/mlflow/src/common/utils/reactQueryHooks';\nimport { ErrorBoundary } from 'react-error-boundary';\nimport { DangerIcon, Empty, PageWrapper } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nconst PageFallback = ({ error }: { error?: Error }) => {\n  return (\n    <PageWrapper css={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n      <Empty\n        data-testid=\"fallback\"\n        title={\n          <FormattedMessage\n            defaultMessage=\"Error\"\n            description=\"Title for error fallback component in prompts management UI\"\n          />\n        }\n        description={\n          error?.message ?? (\n            <FormattedMessage\n              defaultMessage=\"An error occurred while rendering this component.\"\n              description=\"Description for default error message in prompts management UI\"\n            />\n          )\n        }\n        image={<DangerIcon />}\n      />\n    </PageWrapper>\n  );\n};\n\n/**\n * Wrapper for all experiment logged model pages.\n * Provides error boundaries and user action error handling.\n */\nexport const ExperimentLoggedModelPageWrapper = ({\n  children,\n  resetKey,\n}: {\n  children: React.ReactNode;\n  resetKey?: unknown;\n}) => {\n  return (\n    <ErrorBoundary FallbackComponent={PageFallback} resetKeys={[resetKey]}>\n      <UserActionErrorHandler>{children}</UserActionErrorHandler>\n    </ErrorBoundary>\n  );\n};\n"], "names": ["$ebb31c7feaa4405e$export$b16d9fb1a22de840", "$hgUW1$createContext", "$44d7e150ebc754d2$var$initialState", "didCatch", "error", "$44d7e150ebc754d2$export$e926676385687eaf", "$hgUW1$Component", "state", "getDerivedStateFromError", "resetErrorBoundary", "_this", "_len", "arguments", "length", "args", "Array", "_key", "props", "onReset", "reason", "setState", "componentDidCatch", "info", "this", "onError", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "a", "undefined", "b", "some", "item", "index", "Object", "is", "$44d7e150ebc754d2$var$hasArrayChanged", "next", "prev", "render", "children", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$hgUW1$isValidElement", "Error", "$hgUW1$createElement", "Provider", "value", "$75c9d331f9c1ed1a$export$f20aa86254872370", "$7c3c25b3f398a9d6$export$c052f6604b7d51fe", "context", "$hgUW1$useContext", "$hgUW1$useState", "<PERSON><PERSON><PERSON><PERSON>", "memoized", "$hgUW1$useMemo", "resetBoundary", "showBoundary", "$62ff477d53f02a5b$export$f0c7a449e0cfaec7", "Component", "errorBoundaryProps", "Wrapped", "name", "displayName", "ExperimentLoggedModelListPageMode", "VIEW_MODE_QUERY_PARAM", "VALID_FILTER_ATTRIBUTES", "ExperimentLoggedModelListPageAutoComplete", "_ref", "searchQuery", "onChangeSearchQuery", "loggedModelsData", "options", "useMemo", "entityNames", "loggedModels", "metricNames", "Set", "paramNames", "tagNames", "loggedModel", "_loggedModel$data", "_loggedModel$data$met", "_loggedModel$data2", "_loggedModel$data2$pa", "_loggedModel$info", "_loggedModel$info$tag", "data", "metrics", "for<PERSON>ach", "metric", "key", "add", "params", "param", "tags", "tag", "from", "filter", "isUserFacingTag", "getEntityNamesFromLoggedModelsData", "validAttributeOptions", "map", "attribute", "getEntitySearchOptionsFromEntityNames", "_jsx", "EntitySearchAutoComplete", "searchFilter", "onSearchFilterChange", "defaultActiveFirstOption", "baseOptions", "onClear", "placeholder", "tooltipContent", "_jsxs", "FormattedMessage", "id", "defaultMessage", "values", "whereBold", "getDatasetHash", "dataset", "JSON", "stringify", "dataset_name", "dataset_digest", "_ref2", "styles", "ExperimentLoggedModelListPageDatasetDropdown", "selectedFilterDatasets", "onToggleDataset", "onClearSelectedDatasets", "theme", "useDesignSystemTheme", "intl", "useIntl", "cachedDatasets", "useRef", "Map", "allDatasets", "model", "_model$data", "datasetHash", "current", "has", "set", "hash", "serializedSelectedDatasets", "DialogCombobox", "componentId", "label", "formatMessage", "stayOpenOnSelection", "DialogComboboxCustomButtonTriggerWrapper", "<PERSON><PERSON>", "endIcon", "ChevronDownIcon", "icon", "TableIcon", "_Fragment", "DialogComboboxCountBadge", "css", "XCircleFillIcon", "role", "onClick", "e", "stopPropagation", "preventDefault", "_css", "color", "colors", "textPlaceholder", "fontSize", "typography", "fontSizeSm", "marginLeft", "spacing", "xs", "actionTertiaryTextHover", "DialogComboboxContent", "DialogComboboxOptionList", "_ref3", "serializedDataset", "DialogComboboxOptionListCheckboxItem", "checked", "includes", "onChange", "getSortableColumnLabel", "colId", "descriptor", "ExperimentLoggedModelListPageKnownColumns", "CreationTime", "defineMessage", "parsedColumn", "parseLoggedModelMetricOrderByColumnId", "metricKey", "_ref4", "ExperimentLoggedModelListPageOrderBySelector", "orderByColumn", "orderByAsc", "onChangeOrderBy", "columnDefs", "setFilter", "useState", "groupedOrderByOptions", "lowerCaseFilter", "toLowerCase", "attributeColumnGroup", "groupId", "headerName", "metricColumnGroups", "col", "_col$groupId", "startsWith", "LOGGED_MODEL_LIST_METRIC_COLUMN_PREFIX", "_col$children", "sortableColumnGroups", "group", "push", "DropdownMenu", "Root", "modal", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SortAscendingIcon", "SortDescendingIcon", "sortBy", "Content", "padding", "sm", "lg", "width", "display", "gap", "Input", "prefix", "SearchIcon", "type", "target", "autoFocus", "allowClear", "ToggleIconButton", "pressed", "ArrowDownIcon", "ArrowUpIcon", "_ref5", "Group", "Label", "_ref6", "columnHeaderName", "CheckboxItem", "Boolean", "ItemIndicator", "ExperimentLoggedModelListPageControls", "sortingAndFilteringEnabled", "onUpdateColumns", "columnVisibility", "viewMode", "setViewMode", "flexWrap", "SegmentedControlGroup", "coerceToEnum", "TABLE", "SegmentedControlButton", "<PERSON><PERSON><PERSON>", "content", "ListIcon", "visuallyHidden", "ChartLineIcon", "ExperimentLoggedModelListPageColumnSelector", "disabled", "CHART", "getMetricByDatasetChartDataKey", "datasetName", "createLocalStorageKey", "storeIdentifier", "chartsUIStateInitializer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compareRunSections", "autoRefreshEnabled", "isAccordionReordered", "chartsSearchFilter", "globalLineChartConfig", "isDirty", "chartsUIStateReducer", "action", "stateSetter", "metricsByDatasets", "dataAccessKey", "deleted", "RunsChartType", "BAR", "uuid", "metricSectionId", "isGenerated", "uniq", "isReordered", "isEmpty", "getExperimentLoggedModelsPageChartSetup", "reconcileChartsAndSections", "currentState", "<PERSON><PERSON><PERSON><PERSON>", "_newCharts$compareRun", "_newCharts$compareRun2", "newChartsToAdd", "new<PERSON>hart", "_currentState$compare", "find", "chart", "newSectionsToAdd", "newSection", "_currentState$compare2", "section", "initialConfig", "useExperimentLoggedModelsChartsUIState", "chartUIState", "dispatchChartUIState", "useReducer", "loading", "setLoading", "useEffect", "async", "serializedData", "localStorage", "getItem", "parse", "loadPersistedDataFromStorage", "then", "dataToPersist", "setItem", "saveDataToStorage", "updateUIState", "useCallback", "ExperimentLoggedModelListChartsImpl", "memo", "_uiState$chartsSearch", "_uiState$chartsSearch2", "chartData", "uiState", "metricKeysByDataset", "availableMetricKeys", "flatMap", "run", "keys", "availableParamKeys", "updateChartsUIState", "useUpdateRunsChartsUIConfiguration", "setSearch", "search", "configuredCardConfig", "setConfiguredCardConfig", "addNewChartCard", "RunsChartsCardConfig", "getEmptyChartCardByType", "confirmChartCardConfiguration", "useConfirmChartCardConfigurationFn", "<PERSON><PERSON><PERSON>", "useRemoveRunsChartFn", "fullScreenChart", "setFullScreenChart", "fullscreenTooltipContextValue", "runs", "tooltipContextValue", "getDataTraceLink", "Routes", "getExperimentLoggedModelDetailsPageRoute", "emptyState", "marginTop", "Empty", "description", "backgroundColor", "backgroundSecondary", "paddingLeft", "md", "paddingRight", "paddingBottom", "borderTop", "border", "borderLeft", "flex", "overflow", "flexDirection", "paddingTop", "RunsChartsTooltipWrapper", "contextData", "component", "RunsChartsTooltipBody", "RunsChartsDraggableCardsGridContextProvider", "visibleChartCards", "RunsChartsSectionAccordion", "reorder<PERSON><PERSON><PERSON>", "noop", "<PERSON><PERSON><PERSON><PERSON>", "startEditChart", "groupBy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supportedChartTypes", "SCATTER", "noRunsSelectedEmptyState", "RunsChartsFullScreenModal", "onCancel", "tooltipComponent", "RunsChartsConfigureModal", "chartRunData", "metricKeyList", "paramKeyList", "config", "onSubmit", "ExperimentLoggedModelListCharts", "experimentId", "cachedLoggedModels", "useMemoDeep", "factory", "deps", "ref", "isEqual", "metricsByDataset", "_model$data$metrics", "orderBy", "useExperimentLoggedModelAllMetricsByDataset", "loadingState", "isRowHidden", "useExperimentLoggedModelListPageRowVisibilityContext", "compact", "_model$info", "_model$info$name", "_model$info2", "_model$info3", "_model$data$params$ma", "_model$data2", "_model$data2$params", "model_id", "images", "keyBy", "timestamp", "step", "dataKey", "hidden", "getStableColorForRun", "useExperimentLoggedModelsChartsData", "justifyContent", "alignItems", "Spinner", "RunsChartsUIConfigurationContextProvider", "ExperimentLoggedModelListPageImpl", "useParams", "navigate", "useNavigate", "isLoggedModelsFilteringAndSortingEnabled", "rowVisibilityMap", "rowVisibilityMode", "isFilteringActive", "setOrderBy", "setColumnVisibility", "setRowVisibilityMode", "toggleRowVisibility", "updateSearchQuery", "toggleDataset", "clearSelectedDatasets", "useLoggedModelsListPageState", "invariant", "isExperimentLoggedModelsUIEnabled", "getExperimentPageRoute", "replace", "useExperimentLoggedModelListPageMode", "setParams", "useSearchParams", "get", "mode", "isFetching", "isFetchingLoggedModels", "isLoading", "isLoadingLoggedModels", "loggedModelsError", "nextPageToken", "loadMoreResults", "useSearchLoggedModelsQuery", "experimentIds", "getOrderByRequestData", "orderByField", "parsedMetricOrderByColumn", "datasetDigest", "orderByDatasetName", "orderByDatasetDigest", "badRequestError", "BadRequestError", "relatedRunsData", "useRelatedRunsDataForLoggedModels", "compactColumnDefs", "useExperimentLoggedModelListPageTableColumns", "enableSortingByMetrics", "tableAreaWidth", "setTableAreaWidth", "tableHidden", "setTableHidden", "isCompactTableMode", "tableElement", "ExperimentLoggedModelListPageTable", "isLoadingMore", "moreResultsAvailable", "onLoadMore", "onOrderByChange", "ExperimentLoggedModelOpenDatasetDetailsContextProvider", "ExperimentLoggedModelListPageRowVisibilityContextProvider", "visibilityMap", "visibilityMode", "Spacer", "size", "shrinks", "message", "<PERSON><PERSON>", "closable", "RunsChartsSetHighlightContextProvider", "ExperimentViewRunsTableResizer", "onResize", "runListHidden", "onHiddenChange", "ExperimentLoggedModelListPage", "ExperimentLoggedModelPageWrapper", "useSafeDeferredValue", "isFunction", "React", "identity", "get<PERSON><PERSON>y<PERSON>ey", "runUuid", "queryFn", "query<PERSON><PERSON>", "MlflowService", "getRun", "run_id", "_queryResults$find", "runUuids", "allMetricRunUuids", "allSourceRunUuids", "source_run_id", "queryResults", "useQueries", "queries", "cacheTime", "Infinity", "staleTime", "refetchOnWindowFocus", "retry", "memoizedQueryResults", "useArrayMemo", "<PERSON><PERSON><PERSON><PERSON>", "_error$message", "PageWrapper", "title", "image", "DangerIcon", "reset<PERSON>ey", "Error<PERSON>ou<PERSON><PERSON>", "UserActionErrorHandler"], "sourceRoot": ""}