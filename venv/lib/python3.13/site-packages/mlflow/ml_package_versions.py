# This file was auto-generated by update_ml_package_versions.py.
# Please do not edit it manually.

_ML_PACKAGE_VERSIONS = {
    "sklearn": {
        "package_info": {
            "pip_release": "scikit-learn"
        },
        "models": {
            "minimum": "1.3.0",
            "maximum": "1.7.0"
        },
        "autologging": {
            "minimum": "1.3.0",
            "maximum": "1.7.0"
        }
    },
    "pytorch": {
        "package_info": {
            "pip_release": "torch",
            "module_name": "torch"
        },
        "models": {
            "minimum": "2.1.0",
            "maximum": "2.7.1"
        },
        "autologging": {
            "minimum": "2.1.0",
            "maximum": "2.7.1"
        }
    },
    "pytorch-lightning": {
        "package_info": {
            "pip_release": "pytorch-lightning",
            "module_name": "lightning"
        },
        "autologging": {
            "minimum": "2.0.4",
            "maximum": "2.5.1.post0"
        }
    },
    "keras": {
        "package_info": {
            "pip_release": "keras"
        },
        "models": {
            "minimum": "3.0.2",
            "maximum": "3.10.0"
        },
        "autologging": {
            "minimum": "3.0.2",
            "maximum": "3.10.0"
        }
    },
    "tensorflow": {
        "package_info": {
            "pip_release": "tensorflow"
        },
        "models": {
            "minimum": "2.12.1",
            "maximum": "2.19.0"
        },
        "autologging": {
            "minimum": "2.12.1",
            "maximum": "2.19.0"
        }
    },
    "xgboost": {
        "package_info": {
            "pip_release": "xgboost"
        },
        "models": {
            "minimum": "1.7.6",
            "maximum": "3.0.2"
        },
        "autologging": {
            "minimum": "1.7.6",
            "maximum": "3.0.2"
        }
    },
    "lightgbm": {
        "package_info": {
            "pip_release": "lightgbm"
        },
        "models": {
            "minimum": "4.0.0",
            "maximum": "4.6.0"
        },
        "autologging": {
            "minimum": "4.0.0",
            "maximum": "4.6.0"
        }
    },
    "catboost": {
        "package_info": {
            "pip_release": "catboost"
        },
        "models": {
            "minimum": "1.2.1",
            "maximum": "1.2.8"
        }
    },
    "onnx": {
        "package_info": {
            "pip_release": "onnx"
        },
        "models": {
            "minimum": "1.14.1",
            "maximum": "1.18.0"
        }
    },
    "spacy": {
        "package_info": {
            "pip_release": "spacy"
        },
        "models": {
            "minimum": "3.5.4",
            "maximum": "3.8.7"
        }
    },
    "statsmodels": {
        "package_info": {
            "pip_release": "statsmodels"
        },
        "models": {
            "minimum": "0.14.1",
            "maximum": "0.14.4"
        },
        "autologging": {
            "minimum": "0.14.1",
            "maximum": "0.14.4"
        }
    },
    "spark": {
        "package_info": {
            "pip_release": "pyspark",
            "module_name": "pyspark"
        },
        "models": {
            "minimum": "3.2.1",
            "maximum": "4.0.0"
        },
        "autologging": {
            "minimum": "3.2.1",
            "maximum": "4.0.0"
        }
    },
    "prophet": {
        "package_info": {
            "pip_release": "prophet"
        },
        "models": {
            "minimum": "1.1.6",
            "maximum": "1.1.7"
        }
    },
    "pmdarima": {
        "package_info": {
            "pip_release": "pmdarima"
        },
        "models": {
            "minimum": "2.0.4",
            "maximum": "2.0.4"
        }
    },
    "diviner": {
        "package_info": {
            "pip_release": "diviner"
        },
        "models": {
            "minimum": "0.1.1",
            "maximum": "0.1.1"
        }
    },
    "h2o": {
        "package_info": {
            "pip_release": "h2o"
        },
        "models": {
            "minimum": "********",
            "maximum": "********"
        }
    },
    "shap": {
        "package_info": {
            "pip_release": "shap"
        },
        "models": {
            "minimum": "0.42.1",
            "maximum": "0.47.2"
        }
    },
    "paddle": {
        "package_info": {
            "pip_release": "paddlepaddle"
        },
        "models": {
            "minimum": "2.5.2",
            "maximum": "3.0.0"
        },
        "autologging": {
            "minimum": "2.5.2",
            "maximum": "3.0.0"
        }
    },
    "transformers": {
        "package_info": {
            "pip_release": "transformers"
        },
        "models": {
            "minimum": "4.35.2",
            "maximum": "4.52.4"
        },
        "autologging": {
            "minimum": "4.35.2",
            "maximum": "4.52.4"
        }
    },
    "openai": {
        "package_info": {
            "pip_release": "openai"
        },
        "models": {
            "minimum": "1.0.1",
            "maximum": "1.85.0"
        },
        "autologging": {
            "minimum": "1.17.0",
            "maximum": "1.85.0"
        }
    },
    "dspy": {
        "package_info": {
            "pip_release": "dspy"
        },
        "models": {
            "minimum": "2.5.17",
            "maximum": "2.6.27"
        },
        "autologging": {
            "minimum": "2.5.17",
            "maximum": "2.6.27"
        }
    },
    "langchain": {
        "package_info": {
            "pip_release": "langchain"
        },
        "models": {
            "minimum": "0.0.354",
            "maximum": "0.3.25"
        },
        "autologging": {
            "minimum": "0.1.0",
            "maximum": "0.3.25"
        }
    },
    "langgraph": {
        "package_info": {
            "pip_release": "langgraph"
        },
        "models": {
            "minimum": "0.2.0",
            "maximum": "0.4.8"
        },
        "autologging": {
            "minimum": "0.2.0",
            "maximum": "0.4.8"
        }
    },
    "llama_index": {
        "package_info": {
            "pip_release": "llama-index",
            "module_name": "llama_index.core"
        },
        "models": {
            "minimum": "0.10.44",
            "maximum": "0.12.41"
        },
        "autologging": {
            "minimum": "0.10.44",
            "maximum": "0.12.41"
        }
    },
    "ag2": {
        "package_info": {
            "pip_release": "ag2",
            "module_name": "autogen"
        },
        "autologging": {
            "minimum": "0.7.0",
            "maximum": "0.9.2"
        }
    },
    "autogen": {
        "package_info": {
            "pip_release": "autogen-agentchat",
            "module_name": "autogen_agentchat"
        },
        "autologging": {
            "minimum": "0.4.9",
            "maximum": "0.6.1"
        }
    },
    "gemini": {
        "package_info": {
            "pip_release": "google-genai",
            "module_name": "google.genai"
        },
        "autologging": {
            "minimum": "1.0.0",
            "maximum": "1.19.0"
        }
    },
    "anthropic": {
        "package_info": {
            "pip_release": "anthropic"
        },
        "autologging": {
            "minimum": "0.30.0",
            "maximum": "0.53.0"
        }
    },
    "crewai": {
        "package_info": {
            "pip_release": "crewai",
            "module_name": "crewai"
        },
        "autologging": {
            "minimum": "0.80.0",
            "maximum": "0.126.0"
        }
    },
    "pydantic_ai": {
        "package_info": {
            "pip_release": "pydantic-ai",
            "module_name": "pydantic_ai"
        },
        "autologging": {
            "minimum": "0.1.9",
            "maximum": "0.2.16"
        }
    },
    "smolagents": {
        "package_info": {
            "pip_release": "smolagents",
            "module_name": "smolagents"
        },
        "autologging": {
            "minimum": "1.14.0",
            "maximum": "1.17.0"
        }
    },
    "mistral": {
        "package_info": {
            "pip_release": "mistralai",
            "module_name": "mistralai"
        },
        "autologging": {
            "minimum": "1.0.0",
            "maximum": "1.8.1"
        }
    },
    "sentence_transformers": {
        "package_info": {
            "pip_release": "sentence-transformers"
        },
        "models": {
            "minimum": "2.3.1",
            "maximum": "4.1.0"
        }
    },
    "johnsnowlabs": {
        "package_info": {
            "pip_release": "johnsnowlabs"
        },
        "models": {
            "minimum": "4.4.8",
            "maximum": "6.0.2"
        }
    },
    "promptflow": {
        "package_info": {
            "pip_release": "promptflow"
        },
        "models": {
            "minimum": "1.3.0",
            "maximum": "1.18.0"
        }
    },
    "litellm": {
        "package_info": {
            "pip_release": "litellm"
        },
        "autologging": {
            "minimum": "1.52.9",
            "maximum": "1.58.4"
        }
    },
    "groq": {
        "package_info": {
            "pip_release": "groq"
        },
        "autologging": {
            "minimum": "0.13.0",
            "maximum": "0.26.0"
        }
    },
    "bedrock": {
        "package_info": {
            "pip_release": "boto3",
            "module_name": "boto3"
        },
        "autologging": {
            "minimum": "1.33.0",
            "maximum": "1.38.33"
        }
    }
}

# A mapping of flavor name to the module name to be imported for autologging.
# This is used for checking version compatibility in autologging.
# DO NOT EDIT MANUALLY
FLAVOR_TO_MODULE_NAME = {
    "sklearn": "sklearn",
    "pytorch": "torch",
    "pytorch-lightning": "lightning",
    "keras": "keras",
    "tensorflow": "tensorflow",
    "xgboost": "xgboost",
    "lightgbm": "lightgbm",
    "statsmodels": "statsmodels",
    "spark": "pyspark",
    "paddle": "paddle",
    "transformers": "transformers",
    "openai": "openai",
    "dspy": "dspy",
    "langchain": "langchain",
    "llama_index": "llama_index.core",
    "ag2": "autogen",
    "autogen": "autogen_agentchat",
    "gemini": "google.genai",
    "anthropic": "anthropic",
    "crewai": "crewai",
    "pydantic_ai": "pydantic_ai",
    "smolagents": "smolagents",
    "mistral": "mistralai",
    "litellm": "litellm",
    "groq": "groq",
    "bedrock": "boto3",
    "pyspark.ml": "pyspark"
}
