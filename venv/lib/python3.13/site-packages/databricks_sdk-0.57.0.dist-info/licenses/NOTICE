Copyright (2023) Databricks, Inc.

This Software includes software developed at Databricks (https://www.databricks.com/) and its use is subject to the included LICENSE file.

This Software contains code from the following open source projects, licensed under the Apache 2.0 license:

psf/requests - https://github.com/psf/requests
Copyright 2019 Kenneth <PERSON>
License - https://github.com/psf/requests/blob/main/LICENSE

googleapis/google-auth-library-python - https://github.com/googleapis/google-auth-library-python/tree/main
Copyright google-auth-library-python authors
License - https://github.com/googleapis/google-auth-library-python/blob/main/LICENSE

openai/openai-python - https://github.com/openai/openai-python
Copyright 2024 OpenAI
License - https://github.com/openai/openai-python/blob/main/LICENSE

This software contains code from the following open source projects, licensed under the BSD (3-clause) license.

x/oauth2 - https://cs.opensource.google/go/x/oauth2/+/master:oauth2.go
Copyright 2014 The Go Authors. All rights reserved.
License - https://cs.opensource.google/go/x/oauth2/+/master:LICENSE

encode/httpx - https://github.com/encode/httpx
Copyright 2019, Encode OSS Ltd
License - https://github.com/encode/httpx/blob/master/LICENSE.md

This software contains code from the following open source projects, licensed under the MIT license:

langchain-ai/langchain - https://github.com/langchain-ai/langchain/blob/master/libs/partners/openai
Copyright 2023 LangChain, Inc.
License - https://github.com/langchain-ai/langchain/blob/master/libs/partners/openai/LICENSE
