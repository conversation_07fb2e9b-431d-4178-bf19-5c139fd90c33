# Use Python 3.13 slim image as base
FROM python:3.13-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    MLFLOW_PORT=5000 \
    MLFLOW_HOST=0.0.0.0 \
    BACKEND_STORE_URI=file:./mlruns

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entrypoint script
COPY entrypoint.sh .
RUN chmod +x entrypoint.sh

# Create mlruns directory
RUN mkdir -p mlruns

# Copy existing MLflow data (if any)
COPY 755344388102216184/ ./mlruns/755344388102216184/
COPY models/ ./mlruns/models/

# Create a non-root user for security
RUN useradd -m -u 1000 mlflow && \
    chown -R mlflow:mlflow /app

USER mlflow

# Expose MLflow UI port
EXPOSE ${MLFLOW_PORT}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${MLFLOW_PORT}/ || exit 1

# Use entrypoint script
ENTRYPOINT ["./entrypoint.sh"]
