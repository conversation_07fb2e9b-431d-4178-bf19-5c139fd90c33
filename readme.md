# MLflow Tracking Server

This project provides an MLflow tracking server for experiment tracking and model management.

## Local Development

To run the project locally, install mlflow and run the following command:

```bash
mlflow ui --backend-store-uri file:./
```

## Docker Deployment

### Quick Start with Docker Compose (Recommended)

1. Build and run the MLflow server:

```bash
docker-compose up --build
```

2. Access the MLflow UI at: http://localhost:5000

3. To run in detached mode:

```bash
docker-compose up -d --build
```

4. To stop the service:

```bash
docker-compose down
```

### Manual Docker Build and Run

1. Build the Docker image:

```bash
docker build -t mlflow-server .
```

2. Run the container:

```bash
docker run -p 5000:5000 -v $(pwd):/app/mlruns mlflow-server
```

### Configuration

The following environment variables can be configured:

- `MLFLOW_PORT`: Port for MLflow UI (default: 5000)
- `MLFLOW_HOST`: Host to bind to (default: 0.0.0.0)
- `BACKEND_STORE_URI`: Backend store URI (default: file:./mlruns)

### Data Persistence

MLflow experiment data is stored in the `mlruns` directory and is mounted as a volume to ensure persistence across container restarts.
